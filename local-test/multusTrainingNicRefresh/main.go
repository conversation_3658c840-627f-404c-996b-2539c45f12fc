package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	nadv1 "github.com/k8snetworkplumbingwg/network-attachment-definition-client/pkg/apis/k8s.cni.cncf.io/v1"
	nadclientv1 "github.com/k8snetworkplumbingwg/network-attachment-definition-client/pkg/client/clientset/versioned/typed/k8s.cni.cncf.io/v1"
	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	kubeovnv1 "github.com/kubeovn/kube-ovn/pkg/client/clientset/versioned/typed/kubeovn/v1"
	"github.com/sirupsen/logrus"
	networkv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	v1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned/typed/network/v1"
	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

var (
	Engine         *db.EngineWrapper                 = nil
	KubeovnClient  *kubeovnv1.KubeovnV1Client        = nil
	BosonNetClient *netv1.NetworkV1Client            = nil
	NADClient      *nadclientv1.K8sCniCncfIoV1Client = nil
	RpConfig       *config.Config                    = nil
)

type vpcInfo struct {
	VpcDB    *db.Vpcs  `json:"vpcDB"`
	VpcCR    *ovn.Vpc  `json:"vpcCR"`
	RmIDInfo *rmIDInfo `json:"rmIDInfo"`
}

type rmIDInfo struct {
	Subscriptions  string `json:"Subscriptions"`
	ResourceGroups string `json:"ResourceGroups"`
	Zones          string `json:"Zones"`
	ResourceType   string `json:"ResourceType"`
	Vni            int    `json:"Vni"`
}

func init() {
	ch := utils.NewStopChannel()
	resourceprovider.BosonProvider = resourceprovider.NewResourceProvider()
	exporter.Exporter = exporter.NewExporter(resourceprovider.BosonProvider.RpConfig)
	exporter.Exporter.Serve(ch)

	RpConfig = resourceprovider.BosonProvider.RpConfig
	Engine = resourceprovider.BosonProvider.Engine
	KubeovnClient = resourceprovider.BosonProvider.KubeovnClient
	BosonNetClient = resourceprovider.BosonProvider.BosonNetClient
	NADClient = resourceprovider.BosonProvider.NADClient
}

func main() {
	opt := os.Args[1]
	if opt != "kubeovn" && opt != "bms" {
		logrus.Errorln("ope error:", opt)
		return
	}
	vpcNameSlice := strings.Split(os.Args[2], ",")
	logrus.Infoln(fmt.Sprintf("refresh vpcs:%s", objJson(vpcNameSlice)))

	excludeCidrs := []string{}
	if len(os.Args) == 4 {
		excludeCidrs = strings.Split(os.Args[3], ",")
	}
	logrus.Infoln(fmt.Sprintf("excludeCidrs :%s", objJson(excludeCidrs)))

	if opt == "kubeovn" {
		for _, vpcName := range vpcNameSlice {
			logrus.Infoln(fmt.Sprintf("refresh vpc:%s start", vpcName))
			if err := refreshOneKubeovn(vpcName, excludeCidrs); err != nil {
				logrus.Errorln(fmt.Sprintf("refresh vpc:%s fail, err:%s", vpcName, err.Error()))
				continue
			}
			logrus.Infoln(fmt.Sprintf("refresh vpc:%s success", vpcName))
		}
		return
	}

	if opt == "bms" {
		for _, vpcName := range vpcNameSlice {
			logrus.Infoln(fmt.Sprintf("refresh vpc:%s start", vpcName))
			if err := refreshOneBms(vpcName, excludeCidrs); err != nil {
				logrus.Errorln(fmt.Sprintf("refresh vpc:%s fail, err:%s", vpcName, err.Error()))
				continue
			}
			logrus.Infoln(fmt.Sprintf("refresh vpc:%s success", vpcName))
		}
		return
	}
}

func getVpcInfo(vpcName string) (*vpcInfo, error) {
	vpcData := &db.Vpcs{}
	has, err := Engine.Where("name = ? and deleted = false", vpcName).Get(vpcData)
	if err != nil {
		logrus.Errorln("get vpc db err", err)
		return nil, err
	}
	if !has {
		err := fmt.Errorf("vpc(%s) not exist", vpcName)
		logrus.Errorln("vpc db not exist", err)
		return nil, err
	}
	logrus.Infoln(objJson(objJson))

	vpc, err := KubeovnClient.Vpcs().Get(
		context.Background(), vpcData.Name, metav1.GetOptions{})
	if err != nil {
		logrus.Errorln("get vpc CR error", err)
		return nil, err
	}
	logrus.Infoln(objJson(vpc))

	rmIDInfo, err := generateRMIDInfo(vpcData.VPCID)
	if err != nil {
		logrus.Errorln("generate vpc RMID info error", err)
		return nil, err
	}
	logrus.Infoln(objJson(rmIDInfo))

	return &vpcInfo{
		VpcDB:    vpcData,
		VpcCR:    vpc,
		RmIDInfo: rmIDInfo,
	}, nil
}

func refreshOneKubeovn(vpcName string, excludeCidrs []string) error {
	vpcInfo, err := getVpcInfo(vpcName)
	if err != nil {
		logrus.Errorln("getVpcInfo error", err.Error())
		return err
	}
	tenantCode := utils.NameToTenantCode(vpcInfo.VpcCR.Name)
	trainingVni := vpcInfo.RmIDInfo.Vni
	logrus.Infoln("createOvnTrainingSubnet", objJson(vpcInfo), tenantCode, trainingVni)

	cidrPoolDatas, err := getAllTrainingCidrDatasWithVni(excludeCidrs, trainingVni)
	if err != nil {
		logrus.Errorln("getAllTrainingCidrDatasWithVni", err)
		return err
	}
	logrus.Infoln(objJson(cidrPoolDatas))

	// .3 create resource
	for _, cidrPoolData := range cidrPoolDatas {
		logrus.Infoln(objJson(cidrPoolData))
		// .3.1 update db allocated
		err = dao.SetCidrAllocatedByPoolID(Engine, cidrPoolData.Cidr.CIDRPoolID, true)
		if err != nil {
			logrus.Errorln(err)
			return err
		}

		// .3.2 generate
		rand8 := utils.Rand8()
		sntName := fmt.Sprintf("snt-%s-%s", utils.NameToTenantCode(vpcInfo.VpcCR.Name), rand8)
		nadName := fmt.Sprintf("nad-%s-%s", utils.NameToTenantCode(vpcInfo.VpcCR.Name), rand8)
		sntID := utils.UUIDGen()

		nad := generateNadCR(nadName, cidrPoolData.If_id, vpcInfo.VpcCR)
		snt := generateSubnet(sntID, sntName, nadName, cidrPoolData.If_id, vpcInfo.VpcCR, &cidrPoolData.Cidr, vpcInfo.VpcDB)
		sntData := generateSubnetData(sntID, sntName, vpcInfo.VpcCR, &cidrPoolData.Cidr, vpcInfo.VpcDB, vpcInfo.RmIDInfo, snt.Name)

		logrus.Infoln(nad)
		logrus.Infoln(snt)
		logrus.Infoln(sntData)

		// .3.3 create
		_, err = NADClient.NetworkAttachmentDefinitions(resourceprovider.NADNamespace).Create(context.Background(), nad, metav1.CreateOptions{})
		if err != nil {
			logrus.Errorln(err)
			return err
		}
		logrus.Infoln("nad create ok")

		_, err = KubeovnClient.Subnets().Create(context.Background(), snt, metav1.CreateOptions{})
		if err != nil {
			logrus.Errorln(err)
			return err
		}
		logrus.Infoln("subnet create ok")

		err = dao.AddSubnet(Engine, sntData)
		if err != nil {
			logrus.Errorln(err)
			return err
		}
		logrus.Infoln("subnet db create ok")
	}

	return nil
}

func needAlloc(cidr string, excludeCidrs []string) bool {
	for _, cidrTmp := range excludeCidrs {
		if cidrTmp == cidr {
			return false
		}
	}
	return true
}

func generateBmsCidrs(excludeCidrs []string) []string {
	cidrs := []string{}
	for _, cidr := range RpConfig.Boson.TrainingNetworks.Bms.Gws {
		if !needAlloc(cidr, excludeCidrs) {
			continue
		}
		cidrs = append(cidrs, cidr)
	}
	return cidrs
}

func generateOvnCidrs(excludeCidrs []string) []string {
	cidrs := []string{}
	for _, networkConfig := range RpConfig.Boson.TrainingNetworks.KubeOvn {
		if !needAlloc(networkConfig.Gw, excludeCidrs) {
			continue
		}
		cidrs = append(cidrs, networkConfig.Gw)
	}
	return cidrs
}

func refreshOneBms(vpcName string, excludeCidrs []string) error {
	vpcInfo, err := getVpcInfo(vpcName)
	if err != nil {
		logrus.Errorln("getVpcInfo error", err.Error())
		return err
	}
	tenantCode := utils.NameToTenantCode(vpcInfo.VpcCR.Name)
	trainingVni := vpcInfo.RmIDInfo.Vni
	logrus.Infoln("createBMSTrainingSubnet", objJson(vpcInfo), tenantCode, trainingVni)

	gws := generateBmsCidrs(excludeCidrs)
	cidrPools, err := dao.FetchCIDRPoolsWithGws(
		Engine,
		network.SubnetProperties_TRAINING.String(),
		network.SubnetProperties_VLAN.String(),
		gws,
		trainingVni)
	if err != nil {
		logrus.Errorf("fetch bms training error: %s", err.Error())
		return err
	}
	if len(cidrPools) < len(gws) {
		err := fmt.Errorf("fetch bms training fail, need:%d, get:%d",
			len(gws), len(cidrPools))
		return err
	}
	logrus.Infof("fetch bms training CIDR: %v", objJson(cidrPools))

	info := resourceprovider.GenerateBmsTrainingCidrInfo(cidrPools)
	for _, cidr := range RpConfig.Boson.TrainingNetworks.Bms.Gws {
		pool, ok := info[cidr]
		if !ok {
			err = fmt.Errorf("create bms training allocated pool fail, cidr:%s info:%v", cidr, info)
			return err
		}
		err := dao.SetCidrAllocatedByPoolID(Engine, pool.CIDRPoolID, true)
		if err != nil {
			logrus.Errorf("create bms training allocated pool error :%s", err.Error())
			return err
		}

		err = createBMSOneSubnet(
			&pool, vpcInfo.VpcDB, vpcInfo.RmIDInfo, vpcInfo.VpcCR, tenantCode,
			network.SubnetProperties_TRAINING.String(),
			network.SubnetProperties_CONTROLLER.String(),
			network.SubnetProperties_VLAN.String())
		if err != nil {
			logrus.Errorf("create bms training error :%s", err.Error())
			return err
		}
	}
	return nil
}

func createBMSOneSubnet(
	pool *db.CidrPools, vpcData *db.Vpcs, rmIDInfo *rmIDInfo,
	vpc *ovn.Vpc, tenantCode, scope, provider, networkType string,
) error {
	//miaozhanyong to do for prp, request.ResourceGroupName
	subnetID := utils.UUIDGen()
	subnetName := "sn-" + utils.Substring(tenantCode, 0, 20) + "-" + subnetID[:8]
	sn := db.Subnets{
		SubnetID:     subnetID,
		VPCID:        vpcData.VPCID,
		DisplayName:  subnetName,
		CIDR:         pool.CIDR,
		Description:  "",
		IsDefault:    true,
		GatewayIP:    pool.GatewayIP,
		Scope:        scope,
		Provider:     provider,
		NetworkType:  networkType,
		ReservedIPs:  pool.Reserved,
		VNI:          pool.VNI,
		CIDRPoolID:   pool.CIDRPoolID,
		ID:           utils.GenRMID(rmIDInfo.Subscriptions, rmIDInfo.ResourceGroups, rmIDInfo.Zones, "subnets", subnetName),
		Name:         subnetName,
		ResourceType: "network.vpc.v1.subnet",
		Zone:         RpConfig.Boson.VPCDefaultAZ,
		State:        network.Subnet_State_name[int32(network.Subnet_ACTIVE)],
		CreatorID:    vpcData.CreatorID,
		OwnerID:      vpcData.OwnerID,
		TenantID:     vpcData.TenantID,
		CreateTime:   vpcData.CreateTime,
		UpdateTime:   vpcData.UpdateTime,
		Deleted:      false,
	}

	if err := dao.AddSubnet(Engine, &sn); err != nil {
		logrus.Errorf("Add subnet %v error: %s", sn, err)
		return err
	}

	// TODO: Create subnet CR
	subnetCR := &v1.Subnet{
		ObjectMeta: metav1.ObjectMeta{
			Name: sn.Name,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    resourceprovider.VPCKind,
				}),
			},
		},
		Spec: v1.SubnetSpec{
			CIDR:        sn.CIDR,
			GwIP:        sn.GatewayIP,
			VPC:         vpc.Name,
			Scope:       sn.Scope,
			Provider:    sn.Provider,
			NetworkType: sn.NetworkType,
			VNI:         sn.VNI,
			IsDefault:   sn.IsDefault,
			PoolID:      sn.CIDRPoolID,
		},
	}

	if scope == network.SubnetProperties_TRAINING.String() && networkType == network.SubnetProperties_VLAN.String() {
		subnetCR.ObjectMeta.Annotations = map[string]string{
			"k8s.v1.cni.cncf.io/resourceName": "rdma-training/roce", // tag anno for multi-roce NIC subnets
		}
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("CreateBosonSubnet")

	if _, err := BosonNetClient.Subnets().Create(context.Background(), subnetCR, metav1.CreateOptions{}); err != nil {
		logrus.Errorf("Create CR subnet %v error: %s", subnetCR, err)
		k8sRTMetrics.ObserveDurationAndLog()
		return err
	}

	k8sRTMetrics.ObserveDurationAndLog()

	/* don't create ns for bms subnet
	// Add namespace for subnet
	nsName := "ns-" +  utils.Substring(tenantCode, 0, 20)  + "-" + subnetID[:8]
	ns := db.K8sNamespaces{
		NamespaceID: subnetID,
		VPCID:       request.Vpc.Uid,
		SubnetID:    subnetID,
		Name:        nsName,
	}

	if err := dao.AddNamespace(BosonProvider.Engine, &ns); err != nil {
		logrus.Errorf("Add namespace %v error: %s", ns, err)
		return err
	}

	err = createNS(nsName, vpc, request.Zone, request.Vpc.TenantId, request.Vpc.Uid, request.Vpc.Name, subnetID, subnetName)
	if err != nil {
		logrus.Errorf("Create namespace %s error: %s", ns, err)
		return err
	}
	*/
	return nil
}

func getAllTrainingCidrDatasWithVni(excludeCidrs []string, vni int) (
	[]resourceprovider.OvnTrainingCidrInfo, error,
) {
	gws := generateOvnCidrs(excludeCidrs)
	cidrPools, err := dao.FetchCIDRPoolsWithGws(
		Engine, "TRAINING", "VLAN", gws, vni)
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}
	if len(cidrPools) < len(gws) {
		err = fmt.Errorf(
			"allocate all training network fail, resources are not enough, vni:%d, needCnd:%d, getCnt:%d, allocate:%v, gws:%s",
			vni, len(gws), len(cidrPools), cidrPools, gws)
		logrus.Errorln(err)
		return nil, err
	}

	cidrPoolsMapTmp := map[string]db.CidrPools{}
	for _, cidr := range cidrPools {
		cidrPoolsMapTmp[cidr.GatewayIP] = *cidr
	}

	cidrPoolDatas := []resourceprovider.OvnTrainingCidrInfo{}
	for _, info := range RpConfig.Boson.TrainingNetworks.KubeOvn {
		if !needAlloc(info.Gw, excludeCidrs) {
			continue
		}

		cidr, ok := cidrPoolsMapTmp[info.Gw]
		if !ok {
			err := fmt.Errorf("no allocatable training roce cidr found, gw:%s vni:%d", info.Gw, vni)
			logrus.Errorln(err)
			return nil, err
		}

		cidrPoolDatas = append(cidrPoolDatas, resourceprovider.OvnTrainingCidrInfo{
			If_id: info.IfID,
			Cidr:  cidr,
		})
	}

	return cidrPoolDatas, nil
}

func generateNadCR(nadName string, if_id string, vpc *ovn.Vpc) *nadv1.NetworkAttachmentDefinition {
	return &nadv1.NetworkAttachmentDefinition{
		TypeMeta: metav1.TypeMeta{
			Kind:       "NetworkAttachmentDefinition",
			APIVersion: nadv1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      nadName,
			Namespace: resourceprovider.NADNamespace,
			Annotations: map[string]string{
				"k8s.v1.cni.cncf.io/resourceName": resourceprovider.ResourceName,
			},
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    resourceprovider.VPCKind,
				}),
			},
		},
		Spec: nadv1.NetworkAttachmentDefinitionSpec{
			Config: strings.ReplaceAll(strings.ReplaceAll(resourceprovider.NADConfig, "<nad_name>", nadName), "<roce_id>", if_id),
		},
	}
}

func generateSubnet(
	sntID string, sntName string,
	nadName string, if_id string, vpc *ovn.Vpc, cidrData *db.CidrPools, vpcData *db.Vpcs,
) *ovn.Subnet {
	return &ovn.Subnet{
		TypeMeta: metav1.TypeMeta{
			Kind:       resourceprovider.SubnetKind,
			APIVersion: ovn.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: sntName,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    resourceprovider.VPCKind,
				}),
			},
			Labels: map[string]string{
				"creator":                     "boson-provider",
				"kubernetes.io/metadata.name": sntName,
				"subnet_id":                   sntID,
				"subnet_scope":                cidrData.Scope,
				"subnet_type":                 cidrData.NetworkType,
				"tenant_id":                   vpcData.TenantID,
				"vni":                         fmt.Sprintf("%v", cidrData.VNI),
				"vpc_id":                      vpcData.VPCID,
				"vpc_name":                    vpc.Name,
			},
			Annotations: map[string]string{
				"k8s.v1.cni.cncf.io/networks":     fmt.Sprintf("%s/%s", resourceprovider.NADNamespace, nadName),
				"k8s.v1.cni.cncf.io/resourceName": resourceprovider.ResourceName,
				"roce_id":                         resourceprovider.GetAnnosRoceID(if_id),
			},
		},
		Spec: ovn.SubnetSpec{
			Vpc:         vpc.Name,
			CIDRBlock:   cidrData.CIDR,
			Gateway:     cidrData.GatewayIP,
			Protocol:    ovn.ProtocolIPv4,
			NatOutgoing: false,
			Default:     false,
			ExcludeIps:  strings.Split(cidrData.Reserved, ","),
			Provider:    fmt.Sprintf("%s.%s", nadName, resourceprovider.NADNamespace),
		},
	}
}

func generateSubnetData(
	sntID string, sntName string,
	vpc *ovn.Vpc, cidrData *db.CidrPools, vpcData *db.Vpcs, rmIDInfo *rmIDInfo, vpcSubnetName string,
) *db.Subnets {
	return &db.Subnets{
		SubnetID:     sntID,
		VPCID:        vpcData.VPCID,
		DisplayName:  sntName,
		CIDR:         cidrData.CIDR,
		IsDefault:    false,
		GatewayIP:    cidrData.GatewayIP,
		Scope:        cidrData.Scope,
		Provider:     "OVN",
		NetworkType:  cidrData.NetworkType,
		ID:           utils.GenRMID(rmIDInfo.Subscriptions, rmIDInfo.ResourceGroups, rmIDInfo.Zones, "subnets", vpcSubnetName),
		Name:         sntName,
		ResourceType: "network.vpc.v1.subnet",
		Zone:         vpcData.Zone,
		State:        network.Subnet_State_name[int32(network.Subnet_ACTIVE)],
		CreatorID:    vpcData.CreatorID,
		OwnerID:      vpcData.OwnerID,
		TenantID:     vpcData.TenantID,
		CreateTime:   vpcData.CreateTime,
		UpdateTime:   vpcData.UpdateTime,
		Deleted:      false,
		CIDRPoolID:   cidrData.CIDRPoolID,
		VNI:          cidrData.VNI,
	}
}

func addOneSubnet(rmIDInfo *rmIDInfo, vpc *ovn.Vpc, vpcData *db.Vpcs, tenantCode, scope, provider, networkType string) error {
	pool, err := dao.AllocateCidrPool(Engine, scope, networkType)
	if err != nil {
		logrus.Errorf("Add subnet failed, allocate cidr pool error: %s", err)
		return err
	}
	logrus.Infof("Allocate CIDR: %v", pool)

	//miaozhanyong to do for prp, request.ResourceGroupName
	subnetID := utils.UUIDGen()
	subnetName := "sn-" + utils.Substring(tenantCode, 0, 20) + "-" + subnetID[:8]
	sn := db.Subnets{
		SubnetID:     subnetID,
		VPCID:        vpcData.VPCID,
		DisplayName:  subnetName,
		CIDR:         pool.CIDR,
		IsDefault:    true,
		GatewayIP:    pool.GatewayIP,
		Scope:        scope,
		Provider:     provider,
		NetworkType:  networkType,
		ReservedIPs:  pool.Reserved,
		VNI:          pool.VNI,
		CIDRPoolID:   pool.CIDRPoolID,
		ID:           utils.GenRMID(rmIDInfo.Subscriptions, rmIDInfo.ResourceGroups, rmIDInfo.Zones, "subnets", subnetName),
		Name:         subnetName,
		ResourceType: "network.vpc.v1.subnet",
		Zone:         RpConfig.Boson.VPCDefaultAZ,
		State:        network.Subnet_State_name[int32(network.Subnet_ACTIVE)],
		CreatorID:    vpcData.CreatorID,
		OwnerID:      vpcData.OwnerID,
		TenantID:     vpcData.TenantID,
		CreateTime:   vpcData.CreateTime,
		UpdateTime:   vpcData.UpdateTime,
		Deleted:      false,
	}

	if err := dao.AddSubnet(Engine, &sn); err != nil {
		logrus.Errorf("Add subnet %v error: %s", sn, err)
		return err
	}

	// TODO: Create subnet CR
	subnetCR := &networkv1.Subnet{
		ObjectMeta: metav1.ObjectMeta{
			Name: sn.Name,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    resourceprovider.VPCKind,
				}),
			},
		},
		Spec: networkv1.SubnetSpec{
			CIDR:        sn.CIDR,
			GwIP:        sn.GatewayIP,
			VPC:         vpc.Name,
			Scope:       sn.Scope,
			Provider:    sn.Provider,
			NetworkType: sn.NetworkType,
			VNI:         sn.VNI,
			IsDefault:   sn.IsDefault,
			PoolID:      sn.CIDRPoolID,
		},
	}

	if scope == network.SubnetProperties_TRAINING.String() && networkType == network.SubnetProperties_VLAN.String() {
		subnetCR.ObjectMeta.Annotations = map[string]string{
			"k8s.v1.cni.cncf.io/resourceName": "rdma-training/roce", // tag anno for multi-roce NIC subnets
		}
	}

	if _, err := BosonNetClient.Subnets().Create(context.Background(), subnetCR, metav1.CreateOptions{}); err != nil {
		logrus.Errorf("Create CR subnet %v error: %s", subnetCR, err)
		return err
	}

	return nil
}

// fmt.Sprintf("/subscriptions/%v/resourceGroups/%v/zones/%v/%v/%v",
//
//	subscriptionName, resourceGroupName, zone, resourceType, resourceName)
func generateRMIDInfo(vpcID string) (*rmIDInfo, error) {
	vpcAnyoneSubnetData := db.Subnets{}
	has, err := Engine.Where(
		"vpc_id = ? and scope = ? and network_type = ? and deleted = ?",
		vpcID, "TRAINING", "VLAN", false).Get(&vpcAnyoneSubnetData)
	if err != nil {
		logrus.Errorln("get vni db error", err)
		return nil, err
	}
	if !has {
		err := fmt.Errorf("subnet not exist of vpc(%s)", vpcID)
		logrus.Errorln(err)
		return nil, err
	}

	infoSlice := strings.Split(vpcAnyoneSubnetData.ID, "/")
	logrus.Infoln(objJson(infoSlice))
	info := &rmIDInfo{
		Subscriptions:  infoSlice[2],
		ResourceGroups: infoSlice[4],
		Zones:          infoSlice[6],
		ResourceType:   infoSlice[7],
		Vni:            vpcAnyoneSubnetData.VNI,
	}
	return info, nil
}

func objJson(d interface{}) string {
	data, _ := json.Marshal(d)
	return string(data)
}
