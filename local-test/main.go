package main

import (
	"context"
	"github.com/sirupsen/logrus"
	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned/typed/network/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func test() {
	logrus.Infof("start")
	config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))
	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalf("K8s config error: %v", err.<PERSON><PERSON>r())
	}
	bosonNetClient, err := netv1.NewForConfig(cfg)
	if err != nil {
		logrus.Fatalf("Boson net connection failed: %v", err.Error())
	}
	name := "dnat-not-found"
	if drule, err := bosonNetClient.Dnats().Get(context.Background(), name, metav1.GetOptions{}); err == nil {
		logrus.Fatalf("dnat rule :%v", drule)
	} else {
		if errors.IsNotFound(err) {
			logrus.Fatalf("Boson get dnat failed: %v", err.Error())
		} else {
			panic(err)
		}
	}
}

func main() {
	test()
}
