package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"

	nadclientv1 "github.com/k8snetworkplumbingwg/network-attachment-definition-client/pkg/client/clientset/versioned/typed/k8s.cni.cncf.io/v1"
	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	kubeovnv1 "github.com/kubeovn/kube-ovn/pkg/client/clientset/versioned/typed/kubeovn/v1"
	"github.com/sirupsen/logrus"
	v1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned/typed/network/v1"
	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	k8s_errors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

var (
	Engine         *db.EngineWrapper                 = nil
	KubeovnClient  *kubeovnv1.KubeovnV1Client        = nil
	BosonNetClient *netv1.NetworkV1Client            = nil
	NADClient      *nadclientv1.K8sCniCncfIoV1Client = nil
	RpConfig       *config.Config                    = nil
)

type vpcInfo struct {
	VpcDB    *db.Vpcs  `json:"vpcDB"`
	VpcCR    *ovn.Vpc  `json:"vpcCR"`
	RmIDInfo *rmIDInfo `json:"rmIDInfo"`
}

type rmIDInfo struct {
	Subscriptions  string `json:"Subscriptions"`
	ResourceGroups string `json:"ResourceGroups"`
	Zones          string `json:"Zones"`
	ResourceType   string `json:"ResourceType"`
	Vni            int    `json:"Vni"`
}

func init() {
	ch := utils.NewStopChannel()
	resourceprovider.BosonProvider = resourceprovider.NewResourceProvider()
	exporter.Exporter = exporter.NewExporter(resourceprovider.BosonProvider.RpConfig)
	exporter.Exporter.Serve(ch)

	RpConfig = resourceprovider.BosonProvider.RpConfig
	Engine = resourceprovider.BosonProvider.Engine
	KubeovnClient = resourceprovider.BosonProvider.KubeovnClient
	BosonNetClient = resourceprovider.BosonProvider.BosonNetClient
}

func getsubnet() ([]string, error) {
	sns, err := Engine.Query("select name from subnets sb  where sb.deleted=false and sb.provider = 'CONTROLLER' and ((sb.zone like '%z'  and sb.cidr_pool_id not in (select cidr_pool_id from cidr_pools where zone_prp like '%a')) or (sb.zone not like '%z' and sb.cidr_pool_id not in (select cidr_pool_id from cidr_pools cp  where cp.zone_prp = sb.zone)))")
	if err != nil {
		logrus.Errorln("get subnets db err", err)
		return nil, err
	}

	var subnets []string

	for _, sn := range sns {
		for _, s := range sn {
			subnets = append(subnets, string(s[:]))
		}
	}

	return subnets, nil
}

func refreshOneBms(subnetName string, dryRun bool) error {
	logrus.Infof("recorrect subnet %s , dry-run %t start....", subnetName, dryRun)

	subnetData := db.Subnets{}
	has, err := Engine.Where("name = ?", subnetName).Get(&subnetData)
	if err != nil {
		return err
	}
	if !has {
		return fmt.Errorf("Subnet %v not exist", subnetName)
	}

	cidrPoolData := db.CidrPools{}
	has, err = Engine.Where("cidr_pool_id = ?", subnetData.CIDRPoolID).Get(&subnetData)
	if err != nil {
		return err
	}

	s := Engine.NewSession()
	defer s.Close()

	if has {
		cidrPoolData.Allocated = false
		if dryRun {
			logrus.Infof("dry run free cidr pool %s....", cidrPoolData.CIDRPoolID)
		} else {
			if err := dao.ReleaseCidrPool(s, cidrPoolData.CIDRPoolID); err != nil {

				return fmt.Errorf("update cidr pool %v error: %s", cidrPoolData.CIDRPoolID, err)
			}
		}
	}

	// allocate new cidr pool
	newCidrPoolData := &db.CidrPools{}
	if dryRun {
		logrus.Infof("recorrect subnet %s , dry-run %t allocate cidr ....", subnetName, dryRun)
	} else {
		newCidrPoolData, err = dao.AllocateCidrPool(Engine, subnetData.Scope, subnetData.NetworkType)
		if err != nil {
			logrus.Errorf("allocate cidr fail :%s for subnet: %s", err, objJson(subnetData))
			return err
		}
	}
	logrus.Infoln(objJson(cidrPoolData))

	// .3 update subnet resource

	logrus.Infoln(objJson(newCidrPoolData))
	// .3.1 update db
	subnetData.CIDR = newCidrPoolData.CIDR
	subnetData.GatewayIP = newCidrPoolData.GatewayIP
	subnetData.VNI = newCidrPoolData.VNI
	subnetData.CIDRPoolID = newCidrPoolData.CIDRPoolID
	subnetData.ReservedIPs = newCidrPoolData.Reserved

	if dryRun {
		logrus.Infof("dry run update subnet %s....", subnetData.Name)
	} else {
		if err := dao.UpdateSubnet(Engine, &subnetData); err != nil {
			logrus.Errorf("update subnet %s data fail: %s", subnetData.Name, err)
			return err
		}
	}

	// .3.2 update subnet CR, if not, create, else update
	// TODO: Create subnet CR
	if subnetCR, err := BosonNetClient.Subnets().Get(context.Background(), subnetName, metav1.GetOptions{}); err != nil {
		if k8s_errors.IsNotFound(err) {
			vpc, err := dao.GetVpc(Engine, subnetData.VPCID)
			if err != nil {
				logrus.Errorf("get vpc fail %s data fail :%s", subnetData.VPCID, err)
				return err
			}
			vpcCR, err := KubeovnClient.Vpcs().Get(
				context.Background(), vpc.Name, metav1.GetOptions{})
			if err != nil {
				logrus.Errorf("get vpc %s CR error: %v", vpc.Name, err)
				return err
			}
			subnetCR := &v1.Subnet{
				ObjectMeta: metav1.ObjectMeta{
					Name: subnetData.Name,
					OwnerReferences: []metav1.OwnerReference{
						*metav1.NewControllerRef(vpcCR, schema.GroupVersionKind{
							Group:   ovn.SchemeGroupVersion.Group,
							Version: ovn.SchemeGroupVersion.Version,
							Kind:    resourceprovider.VPCKind,
						}),
					},
				},
				Spec: v1.SubnetSpec{
					CIDR:        subnetData.CIDR,
					GwIP:        subnetData.GatewayIP,
					VPC:         vpc.Name,
					Scope:       subnetData.Scope,
					Provider:    subnetData.Provider,
					NetworkType: subnetData.NetworkType,
					VNI:         subnetData.VNI,
					IsDefault:   subnetData.IsDefault,
					PoolID:      subnetData.CIDRPoolID,
				},
			}

			if subnetData.Scope == network.SubnetProperties_TRAINING.String() && subnetData.NetworkType == network.SubnetProperties_VLAN.String() {
				subnetCR.ObjectMeta.Annotations = map[string]string{
					"k8s.v1.cni.cncf.io/resourceName": "rdma-training/roce", // tag anno for multi-roce NIC subnets
				}
			}
			options := metav1.CreateOptions{}
			if dryRun {
				options.DryRun = []string{metav1.DryRunAll}
			}
			if _, err := BosonNetClient.Subnets().Create(context.Background(), subnetCR, options); err != nil {
				logrus.Errorf("Create subnet CR subnet %v error: %s", subnetCR, err)
				return err
			}
		} else {
			logrus.Errorf("Get subnet CR subnet %v error: %s", subnetCR, err)
		}
	} else {
		subnetCR.Spec.CIDR = subnetData.CIDR
		subnetCR.Spec.GwIP = subnetData.GatewayIP
		subnetCR.Spec.PoolID = subnetData.CIDRPoolID
		subnetCR.Spec.VNI = subnetData.VNI

		options := metav1.UpdateOptions{}
		if dryRun {
			options.DryRun = []string{metav1.DryRunAll}
		}

		if _, err = BosonNetClient.Subnets().Update(context.Background(), subnetCR, options); err != nil {
			logrus.Errorf("Update subnet CR subnet %v error: %s", subnetCR, err)
			return err
		}
	}

	logrus.Infof("recorrect subnet %s , dry-run %t success", subnetName, dryRun)

	return nil
}

func objJson(d interface{}) string {
	data, _ := json.Marshal(d)
	return string(data)
}

/*
1. 查询裸金属的subnet（排除RoCE训练网），获取cidr_pool_id和name。 select name, cidr_pool_id, scope, network_type, vni from subnet_test where provider='CONTROLLER' and cidr not like '10.110%' and deleted=false;

2. 使用上述查找的cidr_pool_id, 根据 scope  | network_type | vni 三者相同，分配一个新的网段，allocated=false
    delete from cidr_pool_test where cidr_pool_id in (select cidr_pool_id from subnet_test where provider='CONTROLLER' and deleted=false and cidr not like '10.110%');
    delete 33 records
	select pool.cidr_pool_id from cidr_pool_test pool,subnet_test sb  where  pool.scope =sb.scope and pool.network_type = sb.network_type and pool.vni = sb.vni and pool.allocated=false and sb.provider='CONTROLLER' and sb.deleted= false and sb.cidr not like '10.110%' and pool.cidr not like '10.110.%';

	update cidr_pool_test set allocated=true where cidr_pool_id in (select pool.cidr_pool_id from cidr_pool_test pool,subnet_test sb  where  pool.scope =sb.scope and pool.network_type = sb.network_type and pool.vni = sb.vni and pool.allocated=false and sb.provider='CONTROLLER' and sb.deleted= false and sb.cidr not like '10.110%' and pool.cidr not like '10.110.%');
	update 33 records
3. 使用新的cidr 更新subnet
	update subnet_test set cidr =pool.cidr, gateway_ip=pool.gateway_ip, reserved_ips= pool.reserved, cidr_pool_id=pool.cidr_pool_id from cidr_pool_test pool where pool.vni = subnet_test.vni and pool.allocated=true and  pool.scope =subnet_test.scope and pool.network_type = subnet_test.network_type and provider='CONTROLLER' and subnet_test.cidr not like '10.110.%' and deleted=false ;
     update 33 records

4.  更新subnet cr
kubectl edit subnets.network sn-test0417-2fe035d7 -dry-run=server

spec:
  cidr: ***********/26
  gwIP: ***********
  poolID: b895fdd9-e607-4df7-8e1e-fc564860cf9f
*/

func main() {
	if len(os.Args) != 4 {
		logrus.Errorln("parameter error: ", os.Args[0], "subnet all/subnet-names dry-run")
		return
	}
	opt := os.Args[1]
	if opt != "subnet" {
		logrus.Errorln("operation object error:", opt)
		return
	}

	dryRun := os.Args[3] == "true"
	nameSlice := []string{}
	var err error
	if os.Args[2] == "all" {
		if nameSlice, err = getsubnet(); err != nil {
			logrus.Errorln("get subnets error: ", err)
			return
		}
	} else {
		nameSlice = strings.Split(os.Args[2], ",")
	}

	logrus.Infoln(fmt.Sprintf("recorrect %s:%s", opt, objJson(nameSlice)))

	for _, name := range nameSlice {
		//ipaDatas := db.Ipas{}
		has, err := Engine.Table("ipas").Join("INNER", "subnets", "ipas.subnet_id = subnets.subnet_id").
			Where("subnets.name = ?", name).Exist()

		if err != nil {
			logrus.Errorln(fmt.Sprintf("precheck subnet:%s fail, err:%s", name, err.Error()))
			return
		}
		if has {
			logrus.Errorf("Subnet %v exist ipas,exist .....", name)
			return
		}

	}

	/**
	* 1. allocate the cidr with scope, zone, vni, network_type from pool
	* 2. update subnet
	* 3. update sbunet cr
	 */
	count := 0
	for _, name := range nameSlice {
		logrus.Infof(fmt.Sprintf("recorrect subnet:%s start", name))
		if err := refreshOneBms(name, dryRun); err != nil {
			logrus.Errorln(fmt.Sprintf("recorrect subnet:%s fail, err:%s", name, err.Error()))
			continue
		}
		count++
		logrus.Infoln(fmt.Sprintf("recorrect subnet:%s success", name))
	}

	logrus.Infoln(fmt.Sprintf("recorrect all subnet:%d success, total:%d with dry-run: %t", count, len(nameSlice), dryRun))
	return

}
