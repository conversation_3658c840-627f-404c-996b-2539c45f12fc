package main

import (
	"bufio"
	"context"
	"flag"
	"fmt"
	"os"
	"strings"
	"sync"
	"time"

	ovnv1 "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	kubeovnv1 "github.com/kubeovn/kube-ovn/pkg/client/clientset/versioned/typed/kubeovn/v1"
	"github.com/sirupsen/logrus"
	v1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned/typed/network/v1"
	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	k8s_errors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/util/retry"
)

const (
	HaInstance     int    = 2
	HaHeartBeatNic string = "net1"
	HaMode         string = "master-standby"
	HaGwKind       string = "HaNatGw"
)

var (
	engine         *db.EngineWrapper          = nil
	KubeovnClient  *kubeovnv1.KubeovnV1Client = nil
	BosonNetClient *netv1.NetworkV1Client     = nil
)

type VpcInfo struct {
	vpc        *db.Vpcs
	vpcCR      *ovnv1.Vpc
	hagw       *db.HaNatGateways
	gw         *db.NatGateways
	gwCR       *ovnv1.VpcNatGateway
	gwEip      *db.NatGatewayExternalIpPools
	gwEipCidr  *db.CidrPools
	overlayIps []string
	skip       bool
	realEip    []*db.NatGatewayExternalIpPools
	updateVip  bool
	eips       []*db.Eips
	eipCRs     []*v1.EIP
	dnats      []*db.DnatRules
	dnatCRs    []*v1.Dnat
}

// readLines reads a whole file into memory
// and returns a slice of its lines.
func readLines(path string) ([]string, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" {
			lines = append(lines, strings.TrimSpace(scanner.Text()))
		}
	}
	return lines, scanner.Err()
}

func getAllVpcs(e *db.EngineWrapper) ([]string, error) {
	names := []string{}
	vpcs := []db.Vpcs{}
	if err := e.Where("deleted = false and state != DELETING ").Find(&vpcs); err != nil {
		return nil, err
	}

	for _, vpc := range vpcs {
		names = append(names, vpc.Name)
	}
	return names, nil
}

func init() {
	ch := utils.NewStopChannel()
	rp.BosonProvider = rp.NewResourceProvider()
	KubeovnClient = rp.BosonProvider.KubeovnClient
	BosonNetClient = rp.BosonProvider.BosonNetClient
	exporter.Exporter = exporter.NewExporter(rp.BosonProvider.RpConfig)
	exporter.Exporter.Serve(ch)
	// := rp.BosonProvider.KubeovnClient.VpcNatGateways()
	//haGwCli := rp.BosonProvider.BosonNetClient.HaNatGws()
}

func getVpcInfor(dbt *db.EngineWrapper, vpcName string) (*VpcInfo, error) {
	vpcInfo := &VpcInfo{}
	// get vpc
	vpc, err := dao.GetVpcByName(dbt, vpcName)

	if err != nil {
		logrus.Errorf("no vpc: %s skip", vpcName)
		vpcInfo.skip = true
		return vpcInfo, nil
	}

	if vpc.State == network.VPC_State_name[int32(network.VPC_DELETING)] {
		vpcInfo.skip = true
		return vpcInfo, nil
	}
	vpcInfo.vpc = vpc

	// check hanatgw exist
	if haNats, err := dao.ListHaNatGateways(dbt, vpc.VPCID); err != nil || len(haNats) > 0 {
		if err != nil {
			return vpcInfo, fmt.Errorf("get hanatgw error %v in the vpc %s", err, vpc.Name)
		}
		if len(haNats) > 0 && haNats[0].HaMde == HaMode {
			logrus.Infof("hanatgw %s exists in vpc %s, skip ", haNats[0].Name, vpcName)
			vpcInfo.skip = true
			vpcInfo.hagw = haNats[0]
			return vpcInfo, nil
		}
		if haNats[0].HaMde == "disable" {
			vpcInfo.hagw = haNats[0]
		}
	}

	// get nat gw
	gws, err := dao.ListNatGateways(dbt, vpc.VPCID)
	if err != nil {
		return vpcInfo, fmt.Errorf("get natgw error %v in the vpc %s", err, vpc.Name)
	}

	if len(gws) != 1 {
		return vpcInfo, fmt.Errorf("natgw capacity not 1 in the vpc %s", vpc.Name)
	}

	vpcInfo.gw = gws[0]
	if vpcInfo.hagw != nil {
		if vpcInfo.gw.NatGwExternalIPID != vpcInfo.hagw.NatGwExternalIPID {
			return vpcInfo, fmt.Errorf("natgw %s not consistant with hagw %s in the vpc %s", vpcInfo.gw.NatGwExternalIPID, vpcInfo.hagw.NatGwExternalIPID, vpc.Name)
		}
	}

	// get natgw cr
	gwCR, err := KubeovnClient.VpcNatGateways().Get(context.Background(), vpcInfo.gw.Name, metav1.GetOptions{})
	if err != nil {
		return vpcInfo, fmt.Errorf("get natgw %s cr error %v in the vpc %s", vpcInfo.gw.Name, err, vpc.Name)
	}
	vpcInfo.gwCR = gwCR.DeepCopy()

	// get overlay subnet
	sn, err := dao.GetSubnet(dbt, vpcInfo.gw.SubnetID)
	if err != nil {
		return vpcInfo, fmt.Errorf("get subnet error %v with id %s in the vpc %s", err, vpcInfo.gw.SubnetID, vpc.Name)
	}

	// prepare overlay ip
	for i := 0; i < HaInstance; i++ {
		ip, err := utils.CalcTheLastXIPFromCidr(sn.CIDR, byte(i+2))
		if err != nil {
			return vpcInfo, fmt.Errorf("get overlay ip fail: %s for natgw %s", err, vpcInfo.gw.Name)
		}
		vpcInfo.overlayIps = append(vpcInfo.overlayIps, ip)
	}

	// get current provider ip
	gweip := &db.NatGatewayExternalIpPools{}
	find, err := dbt.Where("nat_gw_external_ip_id = ? and nat_gateway_id = ?", vpcInfo.gw.NatGwExternalIPID, vpcInfo.gw.NATGatewayID).Get(gweip)
	if err != nil || !find {
		return vpcInfo, fmt.Errorf("get nat_gw_external_ip fail by id %s and gw id %s ", vpcInfo.gw.NatGwExternalIPID, vpcInfo.gw.NATGatewayID)

	}
	vpcInfo.gwEip = gweip

	eipCidr, err := dao.GetCidrPoolByCidrID(dbt, gweip.CIDRID)
	if err != nil {
		return vpcInfo, fmt.Errorf("get nat_gw_external_ip cidr fail by id %s ", gweip.CIDRID)

	}
	vpcInfo.gwEipCidr = eipCidr

	dnats := []*db.DnatRules{}
	if err := dbt.Where("nat_gateway_id = ? and deleted = ? ", vpcInfo.gw.NATGatewayID, false).Find(&dnats); err != nil {
		return vpcInfo, fmt.Errorf("get dnats fail by id %s ", vpcInfo.gw.NATGatewayID)
	}
	vpcInfo.dnats = dnats
	logrus.Infof("dnat rule count: %d", len(dnats))

	for _, dnat := range dnats {
		drule, err := BosonNetClient.Dnats().Get(context.Background(), dnat.Name, metav1.GetOptions{})

		if err != nil {
			logrus.Errorln(err)
			return vpcInfo, fmt.Errorf("get dnats CR fail by name %s ", dnat.Name)
		}

		vpcInfo.dnatCRs = append(vpcInfo.dnatCRs, drule)
	}
	logrus.Infof("dnat rule cr count: %d", len(vpcInfo.dnatCRs))

	return vpcInfo, nil
}

func mallocGwEips(dbt *db.EngineWrapper, session *db.SessionWrapper, vpcInfo *VpcInfo) (*VpcInfo, error) {
	vpcInfo.updateVip = false
	gweip := vpcInfo.gwEip
	moregweips := []*db.NatGatewayExternalIpPools{}
	err := dbt.Where("allocated = false and cidr_id = ?", gweip.CIDRID).OrderBy("inet(nat_gateway_external_ip_pools.nat_gw_external_ip)").Limit(HaInstance).Find(&moregweips)
	if err != nil {
		return vpcInfo, fmt.Errorf("get %v nat_gw_external_ip fail %v with cidr %v for vpc %s ", HaInstance, err, gweip.CIDRID, vpcInfo.vpc.Name)
	}

	if len(moregweips) != HaInstance {
		logrus.Infof("no enough free ips %v  with cidr %v for vpc %s, try other cidrs ", HaInstance, gweip.CIDRID, vpcInfo.vpc.Name)
		// malloc 3 eips from other subnet
		moregweips = []*db.NatGatewayExternalIpPools{}
		err = dbt.Table("nat_gateway_external_ip_pools").
			Where("allocated = false and cidr_id in (select cidr_id from nat_gateway_external_ip_pools where allocated = false group by cidr_id having count(cidr_id) > ?) and cidr_id in (select cidr_pool_id from cidr_pools where scope ='VPCGW' and zone_prp = ?)",
				HaInstance, rp.BosonProvider.RpConfig.Boson.VPCDefaultAZ).OrderBy("inet(nat_gateway_external_ip_pools.nat_gw_external_ip)").Limit(HaInstance + 1).Find(&moregweips)
		if err != nil {
			return vpcInfo, fmt.Errorf("get %v nat_gw_external_ip fail %v with cidr %v for vpc %s", HaInstance+1, err, gweip.CIDRID, vpcInfo.vpc.Name)
		}

		if len(moregweips) != (HaInstance + 1) {
			logrus.Errorf("no enough free nat_gw_external_ip %d for vpc %s ", len(moregweips), vpcInfo.vpc.Name)
			//for _, gwEip := range moregweips {
			//	logrus.Errorf("ip: %v \n", gwEip.NatGwExternalIP)
			//}
			return vpcInfo, fmt.Errorf("no enough nat_gw_external_ip %d for vpc %s ", len(moregweips), vpcInfo.vpc.Name)
		}

		vpcInfo.updateVip = true
		vpcInfo.gwEip = moregweips[HaInstance]
	}
	vpcInfo.realEip = moregweips

	gwEipIds := []string{}
	for _, gwEip := range moregweips {
		gwEipIds = append(gwEipIds, gwEip.NatGwExternalIPID)
	}
	logrus.Infof("handle vpc: %s vip %s eip: %v", vpcInfo.vpc.Name, gweip.NatGwExternalIP, gwEipIds)

	eipCidr, err := dao.GetCidrPoolByCidrID(dbt, moregweips[0].CIDRID)
	if err != nil {
		return vpcInfo, fmt.Errorf("get nat_gw_external_ip cidr fail by id %s for vpc %s", moregweips[0].CIDRID, vpcInfo.vpc.Name)
	}
	vpcInfo.gwEipCidr = eipCidr

	if err := dao.SetMultiNatGwEipPoolAllocated(session, vpcInfo.gw.NATGatewayID, gwEipIds); err != nil {
		return vpcInfo, fmt.Errorf("alloc nat_gw_external_ip fail for vpc %s gw %s ", vpcInfo.vpc.Name, vpcInfo.gw.NATGatewayID)
	}

	return vpcInfo, nil
}

func main() {
	file := flag.String("file", "", "需要处理的vpc列表文件名称")
	names := flag.String("vpcs", "", "需要处理的vpc 名称,使用,号分割")
	all := flag.Bool("all", false, "处理系统中所有的vpc")

	help := flag.Bool("help", false, "print help")
	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "用法: %s [选项]\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "选项:\n")
		flag.PrintDefaults()
	}
	logrus.SetFormatter(&utils.MyFormatter{})

	// 解析命令行参数
	flag.Parse()

	if len(os.Args) == 1 || *help {
		flag.Usage()
		return
	}

	logrus.Infof("\n\nstart...\n")
	dbt := rp.BosonProvider.Engine
	vpcNames := []string{}
	var err error
	if *file != "" {
		if vpcNames, err = readLines(*file); err != nil {
			logrus.Errorf("get vpc name from file %s error %v", *file, err)
			return
		}
	}

	if *names != "" {
		vpcNames = strings.Split(*names, ",")
	}

	if *all {
		if vpcNames, err = getAllVpcs(dbt); err != nil {
			logrus.Errorf("get all vpc names  error %v", err)
			return
		}
	}

	// get all vpc db
	// loop vpcs
	//      check valid: vpc exists, hanatgw not exists
	//      get natgateway
	//      allocate 2 provide IP
	//              fail: allocate 3 another cidrs
	//      make hanatgw db & cr

	skip := 0
	fail := make(map[string]error)
	wg := &sync.WaitGroup{}

	for _, vpcName := range vpcNames {
		logrus.Infof("handle vpc: %s", vpcName)
		// get vpc information
		vpcInfo, err := getVpcInfor(dbt, vpcName)
		if err != nil {
			logrus.Errorf("get vpc %s infor fail %v", vpcName, err)
			fail[vpcName] = err
			continue
		}

		if vpcInfo.skip {
			skip++
			continue
		}
		logrus.Infof("vpc: %s get infor success", vpcName)

		// allocate provider ip if fail: allocate 3 another cidrs and ready 2 overlay ip more
		haNatGwSession := dbt.NewSession()
		defer haNatGwSession.Close()
		if vpcInfo, err = mallocGwEips(dbt, haNatGwSession, vpcInfo); err != nil {
			logrus.Errorln(err)
			fail[vpcName] = err
			continue
		}

		// prepare hanatgw db & cr
		hagwName := "ha" + vpcInfo.gw.Name
		logrus.Infof("vpc %s malloc eip success, lanip %v update vip: %v ", vpcInfo.vpc.Name, vpcInfo.overlayIps, vpcInfo.updateVip)

		haNatGwData := db.HaNatGateways{
			HaNatGatewayId:    vpcInfo.gw.NATGatewayID,
			HaMde:             HaMode,
			DisplayName:       hagwName,
			Description:       vpcInfo.gw.Description,
			ID:                strings.ReplaceAll(vpcInfo.gw.ID, vpcInfo.gw.Name, hagwName),
			Name:              hagwName,
			NatGwExternalIPID: vpcInfo.gwEip.NatGwExternalIPID,
			InternalIP:        vpcInfo.gw.InternalIP,
			MonitorIp:         vpcInfo.gwEipCidr.GatewayIP,
			HeartbeatListen:   HaHeartBeatNic,
			ResourceType:      "network.natGateway.v1.natGateway",
			VrrpVip:           vpcInfo.gwEip.NatGwExternalIP,
			VpcID:             vpcInfo.vpc.VPCID,
			SubnetID:          vpcInfo.gw.SubnetID,
			Zone:              vpcInfo.vpc.Zone,
			State:             network.VPC_State_name[int32(network.VPC_CREATING)],
			CreatorID:         vpcInfo.vpc.CreatorID,
			OwnerID:           vpcInfo.vpc.OwnerID,
			TenantID:          vpcInfo.vpc.TenantID,
			CreateTime:        vpcInfo.vpc.CreateTime,
			UpdateTime:        vpcInfo.vpc.UpdateTime,
			Deleted:           false,
		}

		if vpcInfo.hagw != nil {
			if err := dao.UpdateHaNatGateway(haNatGwSession, &haNatGwData); err != nil {
				logrus.Errorln(err)
				fail[vpcName] = err
				continue
			}
		} else {
			if err := dao.AddHaNatGateway(haNatGwSession, &haNatGwData); err != nil {
				logrus.Errorln(err)
				fail[vpcName] = err
				continue
			}
		}

		vpcNatSpec := vpcInfo.gwCR.Spec.DeepCopy()
		if vpcInfo.updateVip {
			vpcNatSpec, err = transelateGwIP(&vpcInfo.gwCR.Spec, vpcInfo.gwEip.NatGwExternalIP, vpcInfo.gwEipCidr)
			if err != nil {
				fail[vpcName] = err
				continue
			}
		}
		vpcNatSpec.Selector = []string{
			"kubernetes.io/os: linux",
		}

		haNatGw := &v1.HaNatGw{
			TypeMeta: metav1.TypeMeta{
				Kind:       HaGwKind,
				APIVersion: v1.SchemeGroupVersion.Version,
			},
			ObjectMeta: metav1.ObjectMeta{
				Name:            haNatGwData.Name,
				OwnerReferences: vpcInfo.gwCR.ObjectMeta.OwnerReferences,
			},
			Spec: v1.HaNatGwSpec{
				HaMode:         haNatGwData.HaMde,
				HaHeartBeatNic: haNatGwData.HeartbeatListen,
				HaVip:          haNatGwData.VrrpVip,
				HaMonitor:      haNatGwData.MonitorIp,
				ExtendInfos:    assembleExtendInfo(haNatGwData.InternalIP, vpcInfo.overlayIps, vpcInfo.gwEip, vpcInfo.realEip),
				VpcNatSpec:     *vpcNatSpec,
			},
		}
		haNatGw.ObjectMeta.OwnerReferences = vpcInfo.gwCR.ObjectMeta.OwnerReferences

		if _, err = BosonNetClient.HaNatGws().Create(context.Background(), haNatGw, metav1.CreateOptions{}); err != nil {
			logrus.Errorln(err)
			fail[vpcName] = err
			continue
		}

		if vpcInfo.updateVip {
			if _, err := haNatGwSession.Exec("update nat_gateway_external_ip_pools set allocated = false, nat_gateway_id = ? where nat_gw_external_ip_id = ?", "", vpcInfo.gw.NatGwExternalIPID); err != nil {
				logrus.Errorf("release gw %s eip %s error %s, for vpc %s", vpcInfo.gwCR.Name, vpcInfo.gw.NatGwExternalIPID, err, vpcInfo.vpc.Name)
				fail[vpcName] = err
				continue
			}
		}

		if err := haNatGwSession.Commit(); err != nil {
			logrus.Errorf("session commit error %s, for vpc %s", err, vpcInfo.vpc.Name)
			fail[vpcName] = err
			continue
		}

		wg.Add(1)
		go updateCR(haNatGwData.Name, vpcInfo, &fail, wg)
		time.Sleep(1 * time.Second)
	}

	// wait all worker done
	wg.Wait()

	if len(fail) > 0 {
		logrus.Infof("\nvpc total: %d skip: %d fail: %d\nerror infor:	%v", len(vpcNames), skip, len(fail), fail)
	} else {
		logrus.Infof("\nvpc total: %d skip: %d fail: %d", len(vpcNames), skip, len(fail))
	}

}

func updateCR(haNatGwName string, vpcInfo *VpcInfo, fail *(map[string]error), wg *sync.WaitGroup) {
	defer wg.Done()

	// check hanatgw master & ACTIVE
	// eip & dnat
	logrus.Infof("waiting hanatgw %s ok... for vpc: %s", haNatGwName, vpcInfo.vpc.Name)
	for i := 0; i < 20; i++ {
		haNatGw, err := BosonNetClient.HaNatGws().Get(context.Background(), haNatGwName, metav1.GetOptions{})
		if err != nil {
			(*fail)[vpcInfo.vpc.Name] = fmt.Errorf("check hanatgw cr %s status fail: %v", haNatGwName, err)
			return
		}
		_, ok := haNatGw.Status.HaStatus["master"]
		if ok {
			break
		}
		time.Sleep(500 * time.Millisecond)
	}

	logrus.Infof("network service transfering... for vpc: %s", vpcInfo.vpc.Name)
	if vpcInfo.updateVip {
		// update eip.spec.sourceIP
		// update dnat.spec.gatewayName
		// update dnat rule fw rule, add the update procedure for this case
	}

	logrus.Infof("update dnats %d for vpc: %s", len(vpcInfo.dnatCRs), vpcInfo.vpc.Name)
	for _, dnat := range vpcInfo.dnatCRs {
		//logrus.Infof("update dnats %s for vpc: %s", dnat.Name, vpcInfo.vpc.Name)
		if err := retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
			drule, err := BosonNetClient.Dnats().Get(context.Background(), dnat.Name, metav1.GetOptions{})
			if err != nil {
				if k8s_errors.IsNotFound(err) {
					return nil
				}
				return err
			}
			copy := drule.DeepCopy()
			copy.Spec.GatewayName = haNatGwName
			if _, err = BosonNetClient.Dnats().Update(context.Background(), copy, metav1.UpdateOptions{}); err != nil {
				return err
			}
			return nil
		}); err != nil {
			logrus.Errorln(err)
			(*fail)[vpcInfo.vpc.Name] = fmt.Errorf("update dnat CR %s error %v", dnat.Name, err)
			return
		}

	}

	logrus.Infof("delete old natgw resource... for vpc: %s", vpcInfo.vpc.Name)

	// delete vpcnatgw
	if err := KubeovnClient.VpcNatGateways().Delete(context.Background(), vpcInfo.gwCR.Name, metav1.DeleteOptions{}); err != nil {
		if !k8s_errors.IsNotFound(err) {
			(*fail)[vpcInfo.vpc.Name] = fmt.Errorf("delete gw %s error %s, for vpc %s", vpcInfo.gwCR.Name, err, vpcInfo.vpc.Name)
			return
		}
	}

}

func transelateGwIP(gwSpec *ovnv1.VpcNatSpec, ip string, cidr *db.CidrPools) (*ovnv1.VpcNatSpec, error) {
	spec := gwSpec.DeepCopy()
	eipCIDR, err := utils.MergeIPToCIDR(ip, cidr.CIDR)
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}
	// 转化成hanatgw需要的内容
	for i, _ := range spec.DnatRules {
		spec.DnatRules[i].Eip = ip
	}

	for i, _ := range spec.Eips {
		spec.Eips[i].EipCIDR = eipCIDR
		spec.Eips[i].Gateway = cidr.GatewayIP
	}

	for i, _ := range spec.SnatRules {
		spec.SnatRules[i].Eip = ip
	}

	return spec, nil
}

func assembleExtendInfo(cvip string, crip []string, vip *db.NatGatewayExternalIpPools, rip []*db.NatGatewayExternalIpPools) []*v1.ExtendInfo {
	var extendInfos []*v1.ExtendInfo
	var containerRealIp []string
	var providerIp []string
	for i := 0; i < HaInstance; i++ {
		containerRealIp = append(containerRealIp, crip[i])
		providerIp = append(providerIp, rip[i].NatGwExternalIP)
	}

	// for container
	extendInfos = append(extendInfos, &v1.ExtendInfo{
		Vip:     cvip,
		RealIps: containerRealIp,
		Scope:   "c",
	}, &v1.ExtendInfo{
		Vip:     vip.NatGwExternalIP,
		RealIps: providerIp,
		Scope:   "p",
	})

	return extendInfos
}
