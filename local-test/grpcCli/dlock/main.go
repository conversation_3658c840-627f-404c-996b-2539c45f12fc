package main

import (
	"context"
	"database/sql"
	"flag"
	"fmt"
	"log"
	"math/rand"
	"os"
	"sync"
	"time"

	"github.com/allisson/go-pglock/v3"
	_ "github.com/lib/pq"
	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

type Lock struct {
	ID        int
	Name      string
	CreatedAt time.Time `pg:"default:now()"`
	UpdatedAt time.Time `pg:"default:now()"`
}

type DBInfo struct {
	user     *string
	password *string
	host     *string
	port     *int
	dbName   *string
	sslMode  *string
}

var dbInfo DBInfo

// func newDB(dbUrl string) (*sql.DB, error) {
// 	// export DATABASE_URL='postgres://user:pass@localhost:5432/pglock?sslmode=disable'
// 	//dsn := os.Getenv("DATABASE_URL")
// 	db, err := sql.Open("postgres", dbUrl)
// 	if err != nil {
// 		return nil, err
// 	}
// 	return db, db.Ping()
// }

func newDb(dbInfo *DBInfo) (*sql.DB, error) {
	// 创建连接字符串
	connInfo := fmt.Sprintf("host=%s port=%d user=%s password=%s dbname=%s sslmode=%s",
		*dbInfo.host, *dbInfo.port, *dbInfo.user, *dbInfo.password, *dbInfo.dbName, *dbInfo.sslMode)
	db, err := sql.Open("postgres", connInfo)
	if err != nil {
		return nil, err
	}
	return db, db.Ping()

}

func closeDB(db *sql.DB) {
	if err := db.Close(); err != nil {
		logrus.Fatal(err)
	}
}

func lockTest(context context.Context, db1 *sql.DB, dlock *pglock.Lock, wg *sync.WaitGroup) {
	defer wg.Done()

	startTime := time.Now()
	err := dlock.WaitAndLock(context)
	if err != nil {
		log.Fatalf("无法获取锁: %v", err)
	}
	//logrus.Infoln("just print")
	db1.Exec("select 1")

	err = dlock.Unlock(context)
	if err != nil {
		log.Fatalf("无法释放锁: %v", err)
	}
	elapsedTime := time.Since(startTime)
	logrus.Infof("成功释放锁，耗时: %s\n", elapsedTime)
}

func lockTestWithTImeout(ctx context.Context, db *sql.DB, wg *sync.WaitGroup, toP, i int) {
	defer wg.Done()

	timeOut := 10 * time.Second
	cctx, cancel := context.WithTimeout(ctx, timeOut)
	startTime := time.Now()
	var dlock pglock.Lock
	var err error
	func() {
		// 创建 pglock 实例
		dlock, err = pglock.NewLock(cctx, 11, db)
		if err != nil {
			logrus.WithField("workerID: ", i).Infof("new lock error %s", err.Error())
			return
		}
		err = dlock.WaitAndLock(cctx)
		if err != nil {
			logrus.WithField("workerID: ", i).Errorf("无法获取锁: %v", err)
			return
		}
		defer cancel()
	}()
	defer dlock.Close()

	if cctx.Err() == context.DeadlineExceeded {
		logrus.WithField("workerID: ", i).Error("已经超时，释放锁")
		if err := dlock.Unlock(ctx); err != nil {
			logrus.WithField("workerID: ", i).Error("释放锁失败，可以忽略")
		}
		return
	} else {
		elapsedTime := time.Since(startTime)
		logrus.WithField("workerID: ", i).Infof("成功获取锁，耗时: %s\n", elapsedTime)
	}

	//logrus.Infoln("just print")
	db.Exec("select 1")
	// sleep some time
	random := rand.Intn(100)
	logrus.WithField("workerID: ", i).Infof("get random %d", random)
	if random <= toP { // 如果小于我们定义的概率则多睡会
		time.Sleep(2 * timeOut)
		logrus.WithField("workerID: ", i).Infof("sleep %s", 2*timeOut)
	} else { // 如果大于我们定义的概率则少睡会，随机5s
		t := time.Duration(rand.Intn(5)) * time.Second
		time.Sleep(t)
		logrus.WithField("workerID: ", i).Infof("sleep %s", t)
	}

	err = dlock.Unlock(ctx)
	if err != nil {
		logrus.WithField("workerID: ", i).Fatalf("无法释放锁: %v", err)
	}
	elapsedTime := time.Since(startTime)
	logrus.WithField("workerID: ", i).Infof("成功释放锁，耗时: %s\n", elapsedTime)

}

func main() {
	//dbUrl := flag.String("dbUrl", "", "db url: postgres://user:pass@localhost:5432/pglock?sslmode=disable")
	dbInfo.dbName = flag.String("dbName", "", "db name")
	dbInfo.host = flag.String("host", "localhost", "db host")
	dbInfo.password = flag.String("password", "", "db password")
	dbInfo.port = flag.Int("port", 5432, "db port")
	dbInfo.sslMode = flag.String("sslMode", "disable", "db sslmode")
	dbInfo.user = flag.String("user", "boson", "db user")

	worker := flag.Int("worker", 10, "并发测试的worker个数")
	timeOutPercent := flag.Int("timeOutPercent", 0, "[0-99], 用来决定worker中超时的比例")
	help := flag.Bool("help", false, "print help")
	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "用法: %s [选项]\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "选项:\n")
		flag.PrintDefaults()
	}
	logrus.SetFormatter(&utils.MyFormatter{})

	// 解析命令行参数
	flag.Parse()

	if len(os.Args) == 1 || *help {
		flag.Usage()
		return
	}

	db, err := newDb(&dbInfo)
	if err != nil {
		logrus.Fatal(err)
	}
	defer closeDB(db)

	wg := &sync.WaitGroup{}
	wg.Add(*worker)

	ctx := context.Background()

	startTime := time.Now()
	for i := 0; i < *worker; i++ {
		//go lockTest(context, db1, &dlock, wg)
		go lockTestWithTImeout(ctx, db, wg, *timeOutPercent, i)
	}
	wg.Wait()
	elapsedTime := time.Since(startTime)
	logrus.Infof("all done 耗时：%s\n", elapsedTime)
}
