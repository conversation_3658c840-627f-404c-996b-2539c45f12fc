package main

import (
	"context"
	"crypto/tls"
	"fmt"
	"os"

	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
)

type GrpcClient struct {
	conn *grpc.ClientConn
	eip.EIPsClient
	eip.DNATRulesClient
}

func testEip(c *GrpcClient) {
	rsp, err := c.GetEIPStatus(context.TODO(), &eip.GetEIPStatusRequest{EipName: "eip-8624ed056aba44mzy-718b936f"})
	if err != nil {
		panic(err)
	}
	logrus.Infof("eip status: %v", rsp)
}

func testDnat(c *GrpcClient) {
	//create,deleted, update,get,list, bind,unbind
}

func test(serviceHost string) {
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
	}
	creds := credentials.NewTLS(tlsConfig)

	//conn, err := grpc.Dial("localhost:51090",
	conn, err := grpc.Dial(serviceHost,
		//grpc.WithTransportCredentials(creds))
		grpc.WithInsecure())
	grpc.WithTransportCredentials(creds)
	if err != nil {
		panic(err)
	}

	defer conn.Close()

	c := &GrpcClient{
		conn,
		eip.NewEIPsClient(conn),
		eip.NewDNATRulesClient(conn),
	}

	testEip(c)
	testDnat(c)
}

func main() {
	if os.Args == nil || len(os.Args) != 2 {
		fmt.Println("Illego args, do nothing")
		return
	}

	serviceHost := os.Args[1]
	test(serviceHost)
}
