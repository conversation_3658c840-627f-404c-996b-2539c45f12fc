package main

import (
	"context"
	"crypto/tls"
	"fmt"
	_ "github.com/lib/pq"
	"github.com/sirupsen/logrus"
	"os"

	"time"

	v1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned/typed/network/v1"
	ipav1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/ipa/v1"
	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	db2 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"xorm.io/xorm"
)

type GrpcClient struct {
	conn *grpc.ClientConn
	ipav1.IPAsClient
}

var rpConfig *config.Config
var bosonNetClient *netv1.NetworkV1Client
var engine *xorm.Engine
var subnetCR *v1.Subnet
var subnet db2.Subnets

func init() {
	rpConfig = config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))

	subnet = db2.Subnets{
		SubnetID:     utils.UUIDGen(),
		VPCID:        utils.UUIDGen(),
		DisplayName:  "auto-test",
		CIDR:         "*************/24",
		Description:  "auto-test",
		IsDefault:    true,
		GatewayIP:    "*************",
		Scope:        network.SubnetProperties_DATA.String(),
		Provider:     network.SubnetProperties_CONTROLLER.String(),
		NetworkType:  network.SubnetProperties_VLAN.String(),
		ReservedIPs:  "*************..***************",
		VNI:          4094,
		CIDRPoolID:   utils.UUIDGen(),
		ID:           "auto-test",
		Name:         "sn-auto-test-123456",
		ResourceType: "network.vpc.v1.subnet",
		Zone:         rpConfig.Boson.VPCDefaultAZ,
		State:        network.Subnet_State_name[int32(network.Subnet_ACTIVE)],
		CreatorID:    utils.UUIDGen(),
		OwnerID:      utils.UUIDGen(),
		TenantID:     utils.UUIDGen(),
		CreateTime:   time.Now(),
		UpdateTime:   time.Now(),
		Deleted:      false,
	}

	subnetCR = &v1.Subnet{
		ObjectMeta: metav1.ObjectMeta{
			Name: subnet.Name,
		},
		Spec: v1.SubnetSpec{
			CIDR:        subnet.CIDR,
			GwIP:        subnet.GatewayIP,
			VPC:         "test",
			Scope:       subnet.Scope,
			Provider:    subnet.Provider,
			NetworkType: subnet.NetworkType,
			VNI:         subnet.VNI,
			IsDefault:   subnet.IsDefault,
			PoolID:      subnet.CIDRPoolID,
		},
	}

}

func setupEnv() {
	// insert subnet
	pgInfo := fmt.Sprintf("host=%s port=%v user=%s "+
		"password=%s dbname=%s sslmode=disable",
		rpConfig.PG.Host, rpConfig.PG.Port, rpConfig.PG.User, rpConfig.PG.Password, rpConfig.PG.DB)
	fmt.Println(pgInfo)
	var err error
	engine, err = xorm.NewEngine("postgres", pgInfo)

	if err != nil {
		logrus.Fatal(err)
	}

	//engine.ShowSQL(true)
	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalf("K8s config error: %v", err.Error())
	}
	bosonNetClient, err = netv1.NewForConfig(cfg)
	if err != nil {
		logrus.Fatalf("Boson net connection failed: %v", err.Error())
	}

	_, err = engine.Insert(&subnet)
	if err != nil {
		logrus.Fatal(err)
	}

	logrus.Infof("create subnet %s ", subnet.SubnetID)

	if _, err := bosonNetClient.Subnets().Create(context.Background(), subnetCR, metav1.CreateOptions{}); err != nil {
		logrus.Errorf("Create CR subnet %v error: %s", subnetCR, err)
		engine.Delete(&subnet)
		logrus.Fatal(err)
	}
	logrus.Infof("create subnet CR %s ", subnet.Name)

}

func teardown() {
	// delete subnet

	defer engine.Close()
	if err := bosonNetClient.Subnets().Delete(context.Background(), subnetCR.Name, metav1.DeleteOptions{}); err != nil {
		logrus.Errorf("Delete CR subnet %v error: %s", subnetCR, err)
		logrus.Errorln(err)
	}
	logrus.Infof("delete subnet CR %s ", subnet.Name)
	if _, err := engine.Delete(&subnet); err != nil {
		logrus.Errorln(err)
	}
	logrus.Infof("delete subnet %s ", subnet.Name)

}

func deleteIpa(c *GrpcClient, id string) {
	if _, err := c.DeleteIPA(context.Background(), &ipav1.IPAUIDRequest{
		SubscriptionName:  "test",
		ResourceGroupName: "default",
		Zone:              rpConfig.Boson.VPCDefaultAZ,
		Id:                id,
	}); err != nil {
		logrus.Errorf("ipa delete error for ipa %s", id)
	}
}

func testIpa(c *GrpcClient) {
	// create
	ipa, err := c.CreateIPA(context.Background(), &ipav1.CreateIPARequest{
		SubscriptionName:  "test",
		ResourceGroupName: "default",
		Zone:              rpConfig.Boson.VPCDefaultAZ,
		SubnetId:          subnet.SubnetID,
		Protocol:          "IPv4",
	})
	if err != nil {
		logrus.Errorf("ipa create error for subnet %s, error %s", subnet.SubnetID, err.Error())
		return
	}
	logrus.Infof("create ipa %s, uuid %s", ipa.GetProperties().Ip, ipa.GetId())

	defer deleteIpa(c, ipa.GetId())

	// get
	ipa1, err := c.GetIPA(context.Background(), &ipav1.IPAUIDRequest{
		SubscriptionName:  "test",
		ResourceGroupName: "default",
		Zone:              rpConfig.Boson.VPCDefaultAZ,
		Id:                ipa.GetId(),
	})

	if err != nil {
		logrus.Errorf("ipa get error for subnet %s, error %s", subnet.SubnetID, err.Error())
	}

	if ipa1.GetProperties().Ip != ipa.GetProperties().Ip {
		logrus.Errorf("ipa values error for ipa %s %s", ipa.GetProperties().Ip, ipa1.GetProperties().Ip)
	}

	// create again, show be fail
	ipa2, err := c.CreateIPA(context.Background(), &ipav1.CreateIPARequest{
		SubscriptionName:  "test",
		ResourceGroupName: "default",
		Zone:              rpConfig.Boson.VPCDefaultAZ,
		SubnetId:          subnet.SubnetID,
		Protocol:          "IPv4",
	})

	if err == nil {
		logrus.Fatalf("ipa create should be fail for subnet %s, ip %s", subnet.SubnetID, ipa2.GetProperties().Ip)
		deleteIpa(c, ipa2.GetId())
		return
	}
	logrus.Infof("test case for creating fail ipa success")
}

func test(serviceHost string) {
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
	}
	creds := credentials.NewTLS(tlsConfig)

	//conn, err := grpc.Dial("localhost:51090",
	conn, err := grpc.Dial(serviceHost,
		//grpc.WithTransportCredentials(creds))
		grpc.WithInsecure())
	grpc.WithTransportCredentials(creds)
	if err != nil {
		panic(err)
	}

	defer conn.Close()

	c := &GrpcClient{
		conn,
		ipav1.NewIPAsClient(conn),
	}

	setupEnv()
	testIpa(c)
	teardown()
}

func main() {
	if os.Args == nil || len(os.Args) != 2 {
		fmt.Println("Illego args, do nothing")
		return
	}

	serviceHost := os.Args[1]
	test(serviceHost)
}
