package main

import (
	"context"
	"crypto/tls"
	"fmt"
	"os"

	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
)

func main() {

	if os.Args == nil || len(os.Args) == 0 {
		fmt.Println("No args, do nothing")
		return
	}

	if len(os.Args) != 3 {
		fmt.Println("Illego args, do nothing")
		return
	}

	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
	}
	creds := credentials.NewTLS(tlsConfig)

	serviceHost := os.Args[1]
	conn, err := grpc.Dial(serviceHost, grpc.WithTransportCredentials(creds))
	if err != nil {
		fmt.Println(err)
	}
	defer conn.Close()
	client := vpc.NewVPCsClient(conn)
	rsp, err := client.GetVPCStatus(context.TODO(), &vpc.GetVPCStatusRequest{VpcName: os.Args[2]})
	if err != nil {
		fmt.Println(err)
	}
	fmt.Println(rsp)
}
