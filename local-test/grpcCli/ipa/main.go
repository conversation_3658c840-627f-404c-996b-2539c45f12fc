package main

import (
	"bufio"
	"context"
	"flag"
	"fmt"
	"os"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/ipa/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"google.golang.org/grpc"
)

type GrpcClient struct {
	conn *grpc.ClientConn
	ipa.IPAsClient
	eip.EIPsClient
	eip.DNATRulesClient
}

func testEip(c *GrpcClient) {
	rsp, err := c.GetEIPStatus(context.TODO(), &eip.GetEIPStatusRequest{EipName: "eip-8624ed056aba44mzy-718b936f"})
	if err != nil {
		panic(err)
	}
	logrus.Infof("eip status: %v", rsp)
}

func testDnat(c *GrpcClient) {
	//create,deleted, update,get,list, bind,unbind
}

func testIpa(c *GrpcClient) {
	ipa, err := c.GetIPA(context.Background(), &ipa.IPAUIDRequest{
		SubscriptionName:  "e76e8641-c9c2-4be1-aba3-fb56fb90db60",
		ResourceGroupName: "default",
		Zone:              "sh-01",
		Id:                "dc50f504-aae6-454e-b255-c78eaa53ff11",
	})

	if err != nil {
		panic(err)
	}

	logrus.Infof("get ipa result: %v", ipa.Properties.Ip)
}

func ipaCreate(c *GrpcClient, subnetId string, ch chan int, wg *sync.WaitGroup) {
	defer wg.Done()

	for {
		val, ok := <-ch
		if !ok {
			logrus.Infof("all done, chan is closed")
			break
		}
		logrus.Infof("create ipa, index %d", val)
		uuid := utils.UUIDGen()
		ipa, err := c.CreateIPA(context.Background(), &ipa.CreateIPARequest{
			SubscriptionName:  uuid,
			ResourceGroupName: "default",
			Zone:              "sh-01",
			SubnetId:          subnetId,
			Protocol:          "IPv4",
		})
		if err != nil {
			logrus.Errorf("ipa create error for uuid %s, error %s", uuid, err.Error())
			continue
		}
		logrus.Infof("create ipa %s, uuid %s", ipa.GetProperties().Ip, ipa.GetId())
	}
}

func testIPACreate(c *GrpcClient, subnetId string, worker, ipCount int) {
	logrus.Infof("test create ipa count: %d, worker: %d", ipCount, worker)
	// init
	wg := &sync.WaitGroup{}
	ipChan := make(chan int, worker)

	beginTime := time.Now()
	logrus.Infof("ipa create begin %s", beginTime)
	wg.Add(worker)

	// create worker
	for w := 0; w < worker; w++ {
		go ipaCreate(c, subnetId, ipChan, wg)
	}

	// put data into chan
	for i := 0; i < ipCount; i++ {
		ipChan <- i
	}

	// close chan
	close(ipChan)

	// wait all worker done
	wg.Wait()
	endTime := time.Now()
	logrus.Infof("ipac create end %s", endTime)
	logrus.Infof("create %d ip, worker %d, time %s", ipCount, worker, endTime.Sub(beginTime))
}

// SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
// 	// Resource group
// 	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
// 	// Available zone
// 	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
// 	// The IPA uid.
// 	Id string `protobuf:"bytes,4,opt,name=id,proto3" json:"id,omitempty"`

func testIPADelete(c *GrpcClient, uuidsFile string) {
	// 打开文件
	file, err := os.Open(uuidsFile)
	if err != nil {
		fmt.Println("无法打开文件:", err)
		return
	}
	defer file.Close()

	// 创建切片用于存放 UUID
	ipaUUIDs := make([]string, 0)

	// 逐行读取文件
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		uuid := scanner.Text()
		ipaUUIDs = append(ipaUUIDs, uuid)
	}

	// 检查是否发生读取错误
	if err := scanner.Err(); err != nil {
		fmt.Println("读取文件出错:", err)
		return
	}

	for _, ipaUUID := range ipaUUIDs {
		_, err := c.DeleteIPA(context.Background(), &ipa.IPAUIDRequest{
			SubscriptionName:  utils.UUIDGen(),
			ResourceGroupName: "default",
			Zone:              "sh-01",
			Id:                ipaUUID,
		})
		if err != nil {
			logrus.Errorf("ipa %s deleted failed, error %s", ipaUUID, err.Error())
		}
	}
}

// func test(serviceHost string, subnetId string, worker, ipCount int) {
// 	//testIpa(c)
// 	// testIPACreate(c, subnetId, worker, ipCount)
// 	//testEip(c)
// 	//testDnat(c)
// }

func generateGrpcClient(serviceHost string) *GrpcClient {
	// tlsConfig := &tls.Config{
	// 	InsecureSkipVerify: true,
	// }
	// creds := credentials.NewTLS(tlsConfig)
	// conn, err := grpc.Dial(serviceHost, grpc.WithTransportCredentials(creds))
	conn, err := grpc.Dial(serviceHost, grpc.WithInsecure())
	//conn, err := grpc.Dial("localhost:51090",
	//conn, err := grpc.Dial(serviceHost,
	//grpc.WithTransportCredentials(creds))
	//	grpc.WithInsecure())
	//grpc.WithTransportCredentials(creds)
	if err != nil {
		panic(err)
	}

	return &GrpcClient{
		conn,
		ipa.NewIPAsClient(conn),
		eip.NewEIPsClient(conn),
		eip.NewDNATRulesClient(conn),
	}
}

// ipa batch test tool
func main() {
	serviceHost := flag.String("serviceHost", "localhost", "服务名称")
	worker := flag.Int("worker", 100, "并发测试的worker个数")
	ipCount := flag.Int("ipCount", 1000, "需要创建的")
	subnetId := flag.String("subnetId", "", "ipa所在的subnet")
	action := flag.String("action", "createIPA", "action to do; support createIPA, deleteIPA")
	uuidsFile := flag.String("uuidsFile", "", "file stores uuids that needs delete")
	help := flag.Bool("help", false, "print help")
	flag.Usage = func() {
		fmt.Fprintf(os.Stderr, "用法: %s [选项]\n", os.Args[0])
		fmt.Fprintf(os.Stderr, "选项:\n")
		flag.PrintDefaults()
	}
	logrus.SetFormatter(&utils.MyFormatter{})

	// 解析命令行参数
	flag.Parse()

	if len(os.Args) == 1 || *help {
		flag.Usage()
		return
	}

	c := generateGrpcClient(*serviceHost)
	defer c.conn.Close()

	if *action == "createIPA" {
		testIPACreate(c, *subnetId, *worker, *ipCount)
	} else if *action == "deleteIPA" {
		testIPADelete(c, *uuidsFile)
	}
}
