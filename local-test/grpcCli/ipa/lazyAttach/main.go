package main

import (
	"context"
	"crypto/tls"
	"fmt"
	"os"

	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/ipa/v1"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
)

type GrpcClient struct {
	conn *grpc.ClientConn
	ipa.IPAsClient
}

func testBindIPA(c *GrpcClient) {
	//test bind ipa
	logrus.Info("start to bind ipa")
	msg, err := c.BindIPA(context.TODO(), &ipa.BindingIPARequest{
		Id:   "5b2f9280-8cad-439e-969e-01f27e83d03b",
		Mac:  "0c:42:a1:a1:b8:78",
		Ipmi: "**************",
	})
	if err != nil {
		logrus.Errorf("bind ipa failed: %v", err)
		return
	}

	logrus.Infof("bind ipa successfully, response: %v", msg)
}

func testUnbindIPA(c *GrpcClient) {
	// test unbind ipa
	logrus.Info("start to unbind ipa")
	msg, err := c.UnbindIPA(context.TODO(), &ipa.BindingIPARequest{
		Id:   "5b2f9280-8cad-439e-969e-01f27e83d03b",
		Mac:  "0c:42:a1:a1:b8:78",
		Ipmi: "**************",
	})

	if err != nil {
		logrus.Errorf("unbind ipa failed: %v", err)
		return
	}

	logrus.Infof("unbind ipa successfully, response: %v", msg)

}

func test(serviceHost, action string) {
	tlsConfig := &tls.Config{
		InsecureSkipVerify: true,
	}
	creds := credentials.NewTLS(tlsConfig)

	//conn, err := grpc.Dial("localhost:51090",
	conn, err := grpc.Dial(serviceHost,
		//grpc.WithTransportCredentials(creds))
		grpc.WithInsecure())
	grpc.WithTransportCredentials(creds)
	if err != nil {
		panic(err)
	}

	defer conn.Close()

	c := &GrpcClient{
		conn,
		ipa.NewIPAsClient(conn),
	}

	if action == "bind" {
		testBindIPA(c)
	} else if action == "unbind" {
		testUnbindIPA(c)
	}
}

func main() {
	if os.Args == nil || len(os.Args) != 3 {
		fmt.Println("Illego args, do nothing.")
		fmt.Println("usage: ./main <service_host> <action> # action: bind or unbind")
		return
	}

	serviceHost := os.Args[1]
	action := os.Args[2]
	test(serviceHost, action)
}
