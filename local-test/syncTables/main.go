package main

import (
	"fmt"

	_ "github.com/lib/pq"
	"xorm.io/xorm"

	"github.com/sirupsen/logrus"
	dbNetController "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/controllers/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	db2 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
)

var rpConfig *config.Config

func init() {
	rpConfig = config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))
}

func main() {
	pgInfo := fmt.Sprintf("host=%s port=%v user=%s "+
		"password=%s dbname=%s sslmode=disable",
		rpConfig.PG.Host, rpConfig.PG.Port, rpConfig.PG.User, rpConfig.PG.Password, rpConfig.PG.DB)
	fmt.Println(pgInfo)
	engine, err := xorm.NewEngine("postgres", pgInfo)

	if err != nil {
		logrus.Fatal(err)
	}

	defer engine.Close()

	bosonTables := db2.GetDbTables()
	bosonNetController := dbNetController.GetDbTables()

	bosonTables = append(bosonTables, bosonNetController...)
	for _, v := range bosonTables {
		logrus.Info(fmt.Sprintf("sync start :%s", engine.TableName(v)))
		err = engine.Sync(v)
		if err != nil {
			logrus.Fatal(err)
		}
		logrus.Info(fmt.Sprintf("sync success :%s", engine.TableName(v)))
	}
}
