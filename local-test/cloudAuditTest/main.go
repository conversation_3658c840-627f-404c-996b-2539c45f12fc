package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"time"

	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	sd "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/sender"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"google.golang.org/grpc/metadata"
)

func format(obj interface{}) string {
	d, _ := json.Marshal(obj)
	return string(d)
}

func main() {
	rpConfig := config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))
	logrus.Info(fmt.Sprintf("%s", format(rpConfig)))
	rp.BosonProvider = &rp.ResourceProvider{}
	rp.BosonProvider.RpConfig = rpConfig

	rmSender, err := sd.NewRMSender(rpConfig)
	if err != nil {
		logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
	}

	bossSender, err := sd.NewBossSender(rpConfig)
	if err != nil {
		logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
	}

	noticeSender, err := sd.NewNoticeSender(rpConfig)
	if err != nil {
		logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
	}

	cloudAuditEipSender, err := sd.NewCloudAuditSenderEip(rpConfig)
	if err != nil {
		logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
	}
	logrus.Info(fmt.Sprintf("%s", format(cloudAuditEipSender)))

	cloudAuditVpcSender, err := sd.NewCloudAuditSenderVpc(rpConfig)
	if err != nil {
		logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
	}
	logrus.Info(fmt.Sprintf("%s", format(cloudAuditVpcSender)))

	cloudAuditDcSender, err := sd.NewCloudAuditSenderDc(rpConfig)
	if err != nil {
		logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
	}
	logrus.Info(fmt.Sprintf("%s", format(cloudAuditDcSender)))

	cloudAuditSlbSender, err := sd.NewCloudAuditSenderSlb(rpConfig)
	if err != nil {
		logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
	}
	logrus.Info(fmt.Sprintf("%s", format(cloudAuditSlbSender)))

	err = cloudAuditEipSender.Start()
	if err != nil {
		logrus.Fatalln(err)
	}
	logrus.Info("cloudAuditEipSender start")

	err = cloudAuditVpcSender.Start()
	if err != nil {
		logrus.Fatalln(err)
	}
	logrus.Info("cloudAuditVpcSender start")

	err = cloudAuditDcSender.Start()
	if err != nil {
		logrus.Fatalln(err)
	}
	logrus.Info("cloudAuditDcSender start")

	err = cloudAuditSlbSender.Start()
	if err != nil {
		logrus.Fatalln(err)
	}
	logrus.Info("cloudAuditSlbSender start")

	p := rp.Processor{
		RMSender:            rmSender,
		BossSender:          bossSender,
		NoticeSender:        noticeSender,
		CloudAuditEipSender: cloudAuditEipSender,
		CloudAuditVpcSender: cloudAuditVpcSender,
		CloudAuditDcSender:  cloudAuditDcSender,
		CloudAuditSlbSender: cloudAuditSlbSender,
	}

	md := make(metadata.MD)
	md["x-user-id"] = []string{utils.UUIDGen()}
	md["x-user-name"] = []string{utils.UUIDGen()}
	md["x-tenant-id"] = []string{utils.UUIDGen()}
	md["x-tenant-code"] = []string{utils.UUIDGen()}
	md["x-forwarded-for"] = []string{utils.UUIDGen()}
	ctx := metadata.NewIncomingContext(context.Background(), md)

	//testAll(p, ctx)
	//testOne1(p, ctx)
	//testOne2(p, ctx)
	//testOne3(p, ctx) // dc
	testOne4(p, ctx)
}

func testOne1(p rp.Processor, ctx context.Context) {
	userInfo := rp.GetUserInfoFromHeader(ctx)
	request := struct{}{}
	response := struct{}{}
	locationInfo := struct{}{}
	user := types.CloudAuditBodyUser{
		UserID:     userInfo.UserID,
		UserName:   userInfo.UserName,
		TenantID:   userInfo.TenantID,
		TenantCode: userInfo.TenantCode,
	}

	body := types.CloudAuditBody{
		Time:          time.Now().Format(time.RFC3339),
		User:          user,
		ServiceType:   string(types.CloudAuditServiceTypeEIP),
		OperationType: string(types.CloudAuditOperationTypeCreateDnatRules),
		ResourceType:  string(types.CloudAuditResourceTypeEipDnat),
		EventID:       utils.UUIDGen(),
		SourceIP:      userInfo.Source,
		EventRating:   string(types.CloudAuditEventRatingNormal),
		EventType:     string(types.CloudAuditEventTypeConsoleAction),
		Request:       request,
		Response:      response,
		ResourceName:  utils.UUIDGen(),
		ResourceID:    utils.UUIDGen(),
		APIVersion:    types.CloudAuditAPIVersion,
		Message:       "",
		Code:          int32(200),
		RequestID:     "",
		LocationInfo:  locationInfo,
		Endpoint:      "",
		ResourceURL:   "",
	}

	bodyMsg, err := json.Marshal(body)
	if err != nil {
		return
	}
	p.CloudAuditEipSender.Send(bodyMsg, body.EventID)
}

func testOne2(p rp.Processor, ctx context.Context) {
	p.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeCreateDnatRules, types.CloudAuditEventRatingNormal,
		struct{}{}, struct{}{}, http.StatusOK,
		utils.UUIDGen(), utils.UUIDGen(),
		p.CloudAuditSendMsg("create success"),
	)
}

func testOne3(p rp.Processor, ctx context.Context) {
	p.CloudAuditSendDCVC(
		ctx,
		types.CloudAuditOperationTypeCreateDcvcs, types.CloudAuditEventRatingNormal,
		struct{}{}, struct{}{}, http.StatusOK,
		utils.UUIDGen(), utils.UUIDGen(),
		p.CloudAuditSendMsg("create success"),
	)
}

func testOne4(p rp.Processor, ctx context.Context) {
	p.CloudAuditSendDCVC(
		ctx,
		types.CloudAuditOperationTypeCreateSlbListener, types.CloudAuditEventRatingNormal,
		struct{}{}, struct{}{}, http.StatusOK,
		utils.UUIDGen(), utils.UUIDGen(),
		p.CloudAuditSendMsg("create success"),
	)
}

func testAll(p rp.Processor, ctx context.Context) {
	p.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeCreateDnatRules, types.CloudAuditEventRatingNormal,
		struct{}{}, struct{}{}, http.StatusOK,
		utils.UUIDGen(), utils.UUIDGen(),
		p.CloudAuditSendMsg("create success"),
	)
	p.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeUpdateDnatRules, types.CloudAuditEventRatingNormal,
		struct{}{}, struct{}{}, http.StatusOK,
		utils.UUIDGen(), utils.UUIDGen(),
		p.CloudAuditSendMsg("create success"),
	)
	p.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeDeleteDnatRules, types.CloudAuditEventRatingNormal,
		struct{}{}, struct{}{}, http.StatusOK,
		utils.UUIDGen(), utils.UUIDGen(),
		p.CloudAuditSendMsg("create success"),
	)
	p.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeBindDnatRules, types.CloudAuditEventRatingNormal,
		struct{}{}, struct{}{}, http.StatusOK,
		utils.UUIDGen(), utils.UUIDGen(),
		p.CloudAuditSendMsg("create success"),
	)
	p.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeUnbindDnatRules, types.CloudAuditEventRatingNormal,
		struct{}{}, struct{}{}, http.StatusOK,
		utils.UUIDGen(), utils.UUIDGen(),
		p.CloudAuditSendMsg("create success"),
	)
	p.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeBatchUnbindDnatRules, types.CloudAuditEventRatingNormal,
		struct{}{}, struct{}{}, http.StatusOK,
		utils.UUIDGen(), utils.UUIDGen(),
		p.CloudAuditSendMsg("create success"),
	)

	p.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeCreateDnatRules, types.CloudAuditEventRatingWarning,
		struct{}{}, struct{}{}, http.StatusBadRequest,
		utils.UUIDGen(), utils.UUIDGen(),
		p.CloudAuditSendMsg("create fail"),
	)
	p.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeUpdateDnatRules, types.CloudAuditEventRatingWarning,
		struct{}{}, struct{}{}, http.StatusBadRequest,
		utils.UUIDGen(), utils.UUIDGen(),
		p.CloudAuditSendMsg("create fail"),
	)
	p.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeDeleteDnatRules, types.CloudAuditEventRatingWarning,
		struct{}{}, struct{}{}, http.StatusBadRequest,
		utils.UUIDGen(), utils.UUIDGen(),
		p.CloudAuditSendMsg("create fail"),
	)
	p.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeBindDnatRules, types.CloudAuditEventRatingWarning,
		struct{}{}, struct{}{}, http.StatusBadRequest,
		utils.UUIDGen(), utils.UUIDGen(),
		p.CloudAuditSendMsg("create fail"),
	)
	p.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeUnbindDnatRules, types.CloudAuditEventRatingWarning,
		struct{}{}, struct{}{}, http.StatusBadRequest,
		utils.UUIDGen(), utils.UUIDGen(),
		p.CloudAuditSendMsg("create fail"),
	)
	p.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeBatchUnbindDnatRules, types.CloudAuditEventRatingWarning,
		struct{}{}, struct{}{}, http.StatusBadRequest,
		utils.UUIDGen(), utils.UUIDGen(),
		p.CloudAuditSendMsg("create fail"),
	)
}
