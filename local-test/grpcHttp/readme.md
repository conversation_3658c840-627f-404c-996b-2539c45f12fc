# 编译
```
flynn@CN0214009254W:~/codes/boson-provider$ pwd
/home/<USER>/codes/boson-provider

flynn@CN0214009254W:~/codes/boson-provider$ docker run -ti --rm -v `pwd`:/src  registry.sensetime.com/sensecore-boson/pb-tools:latest sh
# pwd
/src
# ls
Makefile  README.md  api  build  cmd  docs  go.mod  go.sum  local-test  pb  pkg  sql  vendor

# cd local-test/grpcHttp
protoc -I ./ \
-I ./../../pb/third_party \
--go_out=.  \
--go-grpc_out=.  \
--grpc-gateway_out=.  \
--validate_out=.  --validate_opt=lang=go \
dnat.proto

go build -x -o main main.go

 ./main 8888 8889

```


```
./main 8888 8889


# ok
curl 'http://127.0.0.1:8888/network/eip/data/v1/subscriptions/224bae0c-a98f-41f5-b5d3-9f54aa98455f/resourceGroups/default/zones/cn-sh-01a/dnatRules?eip_names=eip-xuelionly-cd46578f&eip_names=eip-xuelionly-cd465' \
-H 'authority: network.cn-sh-01.sensecoreapi.tech' \
-H 'accept: application/json' \
-H 'accept-language: zh-CN' \
-H 'cache-control: no-cache' \
-H 'origin: https://console.sensecore.tech' \
-H 'pragma: no-cache' \
-H 'referer: https://console.sensecore.tech/' \
-H 'sec-ch-ua: ".Not/A)Brand";v="99", "Google Chrome";v="103", "Chromium";v="103"' \
-H 'sec-ch-ua-mobile: ?0' \
-H 'sec-ch-ua-platform: "macOS"' \
-H 'sec-fetch-dest: empty' \
-H 'sec-fetch-mode: cors' \
-H 'sec-fetch-site: cross-site' \
-H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
--compressed

# 输出：
subscription_name:"224bae0c-a98f-41f5-b5d3-9f54aa98455f"  resource_group_name:"default"  zone:"cn-sh-01a"  eip_names:"eip-xuelionly-cd46578f"  eip_names:"eip-xuelionly-cd465"


# error
curl 'http://127.0.0.1:8888/network/eip/data/v1/subscriptions/224bae0c-a98f-41f5-b5d3-9f54aa98455f/resourceGroups/default/zones/cn-sh-01a/dnatRules?eip_names\[\]=eip-xuelionly-cd46578f&eip_names\[\]=eip-xuelionly-cd465' \
-H 'authority: network.cn-sh-01.sensecoreapi.tech' \
-H 'accept: application/json' \
-H 'accept-language: zh-CN' \
-H 'cache-control: no-cache' \
-H 'origin: https://console.sensecore.tech' \
-H 'pragma: no-cache' \
-H 'referer: https://console.sensecore.tech/' \
-H 'sec-ch-ua: ".Not/A)Brand";v="99", "Google Chrome";v="103", "Chromium";v="103"' \
-H 'sec-ch-ua-mobile: ?0' \
-H 'sec-ch-ua-platform: "macOS"' \
-H 'sec-fetch-dest: empty' \
-H 'sec-fetch-mode: cors' \
-H 'sec-fetch-site: cross-site' \
-H 'user-agent: Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36' \
--compressed
subscription_name:"224bae0c-a98f-41f5-b5d3-9f54aa98455f"  resource_group_name:"default"  zone:"cn-sh-01a"  eip_names:""  eip_names:"eip-xuelionly-cd46578f"  eip_names:"eip-xuelionly-cd465"

# 输出:
subscription_name:"224bae0c-a98f-41f5-b5d3-9f54aa98455f"  resource_group_name:"default"  zone:"cn-sh-01a"  eip_names:""  eip_names:"eip-xuelionly-cd46578f"  eip_names:"eip-xuelionly-cd465"

```