syntax = "proto3";
package dnat;
option go_package = 'api.dnat;dnat';

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/api/annotations.proto";
import "higgs/common/v1/orderinfo.proto";


// DNATRule 服务，管理 AZ 内租户 DNATRule 相关网络资源.
// [EN] Service of DNATRule, for managing DNATRule related network resources for tenants in AZ.
service DNATRules {
  // 列举符合请求的所有EIP的 DNATRules.
  // [EN] List requested multi-eip DNATRules.
  rpc ListMDNATRules(ListMDNATRulesRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      get: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dnatRules"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}"
      permission: "eip.eip.get"
    };
  }
}

// 列举 DNATRules 的请求.
// [EN] Request to list multi-eip DNATRules.
message ListMDNATRulesRequest {

  // Subscription
  string subscription_name = 1;
  
  // Resource group
  string resource_group_name = 2;
  
  // Available zone
  string zone = 3;
  
  // List filter.
  string filter = 4;
  
  // Sort resoults.
  string order_by = 5;
  
  // The maximum number of items to return.
  int32 page_size = 6;
  
  // The next_page_token value returned from a previous List request, if any.
  string page_token = 7;
  
  // eip_name
  repeated string eip_names = 8;
  
}