// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: dnat.proto

package dnat

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DNATRulesClient is the client API for DNATRules service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DNATRulesClient interface {
	// 列举符合请求的所有EIP的 DNATRules.
	// [EN] List requested multi-eip DNATRules.
	ListMDNATRules(ctx context.Context, in *ListMDNATRulesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type dNATRulesClient struct {
	cc grpc.ClientConnInterface
}

func NewDNATRulesClient(cc grpc.ClientConnInterface) DNATRulesClient {
	return &dNATRulesClient{cc}
}

func (c *dNATRulesClient) ListMDNATRules(ctx context.Context, in *ListMDNATRulesRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/dnat.DNATRules/ListMDNATRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DNATRulesServer is the server API for DNATRules service.
// All implementations must embed UnimplementedDNATRulesServer
// for forward compatibility
type DNATRulesServer interface {
	// 列举符合请求的所有EIP的 DNATRules.
	// [EN] List requested multi-eip DNATRules.
	ListMDNATRules(context.Context, *ListMDNATRulesRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedDNATRulesServer()
}

// UnimplementedDNATRulesServer must be embedded to have forward compatible implementations.
type UnimplementedDNATRulesServer struct {
}

func (UnimplementedDNATRulesServer) ListMDNATRules(context.Context, *ListMDNATRulesRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMDNATRules not implemented")
}
func (UnimplementedDNATRulesServer) mustEmbedUnimplementedDNATRulesServer() {}

// UnsafeDNATRulesServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DNATRulesServer will
// result in compilation errors.
type UnsafeDNATRulesServer interface {
	mustEmbedUnimplementedDNATRulesServer()
}

func RegisterDNATRulesServer(s grpc.ServiceRegistrar, srv DNATRulesServer) {
	s.RegisterService(&DNATRules_ServiceDesc, srv)
}

func _DNATRules_ListMDNATRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMDNATRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DNATRulesServer).ListMDNATRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/dnat.DNATRules/ListMDNATRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DNATRulesServer).ListMDNATRules(ctx, req.(*ListMDNATRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DNATRules_ServiceDesc is the grpc.ServiceDesc for DNATRules service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DNATRules_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "dnat.DNATRules",
	HandlerType: (*DNATRulesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListMDNATRules",
			Handler:    _DNATRules_ListMDNATRules_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "dnat.proto",
}
