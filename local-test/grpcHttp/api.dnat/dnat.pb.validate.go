// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: dnat.proto

package dnat

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListMDNATRulesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMDNATRulesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMDNATRulesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMDNATRulesRequestMultiError, or nil if none found.
func (m *ListMDNATRulesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMDNATRulesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	if len(errors) > 0 {
		return ListMDNATRulesRequestMultiError(errors)
	}

	return nil
}

// ListMDNATRulesRequestMultiError is an error wrapping multiple validation
// errors returned by ListMDNATRulesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListMDNATRulesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMDNATRulesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMDNATRulesRequestMultiError) AllErrors() []error { return m }

// ListMDNATRulesRequestValidationError is the validation error returned by
// ListMDNATRulesRequest.Validate if the designated constraints aren't met.
type ListMDNATRulesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMDNATRulesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMDNATRulesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMDNATRulesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMDNATRulesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMDNATRulesRequestValidationError) ErrorName() string {
	return "ListMDNATRulesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListMDNATRulesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMDNATRulesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMDNATRulesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMDNATRulesRequestValidationError{}
