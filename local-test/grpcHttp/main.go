package main

import (
	"context"
	"fmt"
	"math"
	"net"
	"net/http"
	"os"
	"strconv"

	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/reflection"
	"google.golang.org/protobuf/encoding/protojson"
	"google.golang.org/protobuf/types/known/emptypb"

	dnat "gitlab.bj.sensetime.com/elementary/boson/boson-provider/local-test/grpcHttp/api.dnat"
)

func main() {
	httpPort, _ := strconv.Atoi(os.Args[1])
	rpcPort, _ := strconv.Atoi(os.Args[2])

	go func() {
		if err := grpcServer(rpcPort); err != nil {
			fmt.Printf("grpc server err:%s\n", err)
			return
		}
	}()

	go func() {
		if err := httpServer(httpPort, rpcPort); err != nil {
			fmt.Printf("grpc server err:%s\n", err)
			return
		}
	}()

	for {
	}
}

func grpcServer(port int) error {
	listen, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		return err
	}

	options := []grpc.ServerOption{
		grpc.MaxRecvMsgSize(math.MaxInt32),
		grpc.MaxSendMsgSize(**********),
	}

	server := grpc.NewServer(options...)
	dnat.RegisterDNATRulesServer(server, dnatServer{})

	reflection.Register(server)
	err = server.Serve(listen)
	if err != nil {
		fmt.Printf("rpc server start err:%s\n", err)
		return err
	}
	return nil
}

func httpServer(port int, grpcPort int) error {
	mux := runtime.NewServeMux(runtime.WithIncomingHeaderMatcher(CustomMatcher),
		runtime.WithMarshalerOption(runtime.MIMEWildcard, &runtime.JSONPb{
			MarshalOptions:   protojson.MarshalOptions{UseProtoNames: true, UseEnumNumbers: false, EmitUnpopulated: true},
			UnmarshalOptions: protojson.UnmarshalOptions{DiscardUnknown: true},
		}))

	opts := []grpc.DialOption{grpc.WithTransportCredentials(insecure.NewCredentials())}

	dnat.RegisterDNATRulesHandlerFromEndpoint(context.Background(), mux, fmt.Sprintf(":%d", grpcPort), opts)
	err := http.ListenAndServe(fmt.Sprintf(":%d", port), mux)
	if err != nil {
		fmt.Printf("http start err:%s\n", err)
		return err
	}

	return nil
}

type dnatServer struct {
	*dnat.UnimplementedDNATRulesServer
}

func (d dnatServer) ListMDNATRules(ctx context.Context, request *dnat.ListMDNATRulesRequest) (*emptypb.Empty, error) {
	fmt.Printf("%+v\n", request)
	return &emptypb.Empty{}, nil
}

func CustomMatcher(key string) (string, bool) {
	switch key {
	case "X-User-Id", "X-User-Name":
		return key, true
	case "X-Tenant-Id", "X-Tenant-Code":
		return key, true
	default:
		return runtime.DefaultHeaderMatcher(key)
	}
}
