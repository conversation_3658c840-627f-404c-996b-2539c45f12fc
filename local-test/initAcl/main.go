package main

import (
	"bufio"
	"context"
	"fmt"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"k8s.io/apimachinery/pkg/api/errors"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog"
	"os"
	"strings"
)

// readLines reads a whole file into memory
// and returns a slice of its lines.
func readLines(path string) ([]string, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" {
			lines = append(lines, strings.TrimSpace(scanner.Text()))
		}
	}
	return lines, scanner.Err()
}

func main() {
	rp.BosonProvider = rp.NewResourceProvider()

	vpcsCli := rp.BosonProvider.KubeovnClient.Vpcs()
	snCli := rp.BosonProvider.KubeovnClient.Subnets()

	vpcDefaulAcls := rp.BosonProvider.RpConfig.Boson.VPCDefaulAcls

	for _, acl := range vpcDefaulAcls {
		// custom acl priority
		if rp.CUSTOM_ACL_MID_PRIORITY_MIN <= acl.Priority && acl.Priority <= rp.CUSTOM_ACL_MID_PRIORITY_MAX {
			klog.Errorf("cannot assign requested custom acl priority, acl: %v", acl)
			return
		}
	}
	klog.Infof("patch vpc defaul acls: %+v", vpcDefaulAcls)

	vpcs, err := readLines(os.Args[1])
	if err != nil {
		panic(err)
	}

	count := 0
	for _, vpcName := range vpcs {
		// get vpc
		vpc, err := vpcsCli.Get(context.Background(), vpcName, v1.GetOptions{})

		if err != nil {
			if errors.IsNotFound(err) {
				continue
			}
			panic(err)
		}

		// get subnet
		snName, err := GetServiceSubnet(vpc.Status.Subnets)
		if err != nil {
			panic(fmt.Errorf("get service subnet error %v in the vpc %s", err, vpc.Name))
		}
		klog.Infof("handle vpc: %s subnet: %s", vpcName, snName)

		sn, err := snCli.Get(context.Background(), snName, v1.GetOptions{})

		if err != nil {
			if errors.IsNotFound(err) {
				continue
			}
			panic(err)
		}

		for _, acl := range sn.Spec.Acls {
			// custom acl priority
			if rp.CUSTOM_ACL_MID_PRIORITY_MIN <= acl.Priority && acl.Priority <= rp.CUSTOM_ACL_MID_PRIORITY_MAX {
				vpcDefaulAcls = append(vpcDefaulAcls, acl)
			}
		}
		sn.Spec.Acls = vpcDefaulAcls

		_, err = snCli.Update(context.Background(), sn, v1.UpdateOptions{})
		if err != nil {
			panic(err)
		}
		count++
	}

	klog.Infof(" vpc total: %d success: %d ", len(vpcs), count)

}

func GetServiceSubnet(subnets []string) (string, error) {
	for _, name := range subnets {
		if strings.HasPrefix(name, "sn-") {
			return name, nil
		}
	}

	return "", fmt.Errorf("No service subnet in the  %+v'", subnets)
}
