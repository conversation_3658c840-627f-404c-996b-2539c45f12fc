package main

import (
	"encoding/json"
	"fmt"
	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/producer"
	"io"
	"os"

	"context"

	"github.com/apache/rocketmq-client-go/v2/primitive"
)

type Config struct {
	GroupName  string   `json:"groupname"`
	NameServer []string `json:"nameserver"`
	AK         string   `json:"ak"`
	SK         string   `json:"sk"`
	Topic      string   `json:"topic"`
}

func main() {
	jsonFile, err := os.Open(os.Args[1])
	// if we os.Open returns an error then handle it
	if err != nil {
		panic(err)
	}

	defer jsonFile.Close()

	bytes, _ := io.ReadAll(jsonFile)

	var config Config

	json.Unmarshal(bytes, &config)

	fmt.Println(config)

	p, err := rocketmq.NewProducer(
		producer.WithGroupName(config.GroupName),
		producer.WithNsResolver(primitive.NewPassthroughResolver(config.NameServer)),
		producer.WithCredentials(primitive.Credentials{
			AccessKey: config.AK,
			SecretKey: config.SK,
		}),
	)

	if err != nil {
		panic(err)
	}

	err = p.Start()
	if err != nil {
		panic(err)
	}

	result, err := p.SendSync(context.Background(), &primitive.Message{
		Topic: config.Topic,
		Body:  []byte("Hello RocketMQ Go Client! "),
	})
	if err != nil {
		panic(err)
	}

	fmt.Println(result)
	err = p.Shutdown()
	if err != nil {
		panic(err)
	}

}
