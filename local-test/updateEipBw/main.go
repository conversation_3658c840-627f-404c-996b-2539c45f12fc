package main

import (
	"bufio"
	"context"
	"fmt"
	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"k8s.io/apimachinery/pkg/api/errors"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog"
	"os"
	"strings"
	"time"
)

// readLines reads a whole file into memory
// and returns a slice of its lines.
func readLines(path string) ([]string, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" {
			lines = append(lines, strings.TrimSpace(scanner.Text()))
		}
	}
	return lines, scanner.Err()
}

func main() {
	ch := utils.NewStopChannel()
	rp.BosonProvider = rp.NewResourceProvider()
	exporter.Exporter = exporter.NewExporter(rp.BosonProvider.RpConfig)
	exporter.Exporter.Serve(ch)

	eipsCli := rp.BosonProvider.BosonNetClient.EIPs()
	eips, err := readLines(os.Args[1])
	if err != nil {
		panic(err)
	}

	// check
	var eipsNeedUpdate []string
	for _, eipName := range eips {
		// get eip from cr
		eipCr, err := eipsCli.Get(context.Background(), eipName, v1.GetOptions{})
		if err != nil {
			if errors.IsNotFound(err) {
				continue
			}
			panic(fmt.Errorf("get eip from cr error %v in the eip name %s", err, eipName))
		}
		if eipCr.Spec.UpStreamMaxBandwidth != "" || eipCr.Spec.DownStreamMaxBandwidth != "" {
			continue
		}
		eipsNeedUpdate = append(eipsNeedUpdate, eipName)
	}
	klog.Infof("eip total: %d need update: %d %v ", len(eips), len(eipsNeedUpdate), eipsNeedUpdate)

	// update
	klog.Infof("update eip upstream_bw upstream_bw start...")
	count := 0
	for _, eipName := range eipsNeedUpdate {
		// get eip from db
		eipData, err := dao.GetEipByName(rp.BosonProvider.Engine, eipName)
		if err != nil {
			logrus.Errorln(err)
			panic(fmt.Errorf("get eip from db error %v in the eip name %s", err, eipName))
		}

		// get eip from cr
		eipCr, err := eipsCli.Get(context.Background(), eipName, v1.GetOptions{})
		if err != nil {
			if errors.IsNotFound(err) {
				continue
			}
			panic(fmt.Errorf("get eip from cr error %v in the eip name %s", err, eipName))
		}

		bw := eipData.BW
		klog.Infof("handle eip: %s bw: %d", eipName, bw)

		// eip limit rate
		upStreamBw := bw
		downStreamBw := bw
		eipCr.Spec.UpStreamMaxBandwidth = fmt.Sprintf("%d", upStreamBw)
		eipCr.Spec.DownStreamMaxBandwidth = fmt.Sprintf("%d", downStreamBw)

		_, err = eipsCli.Update(context.TODO(), eipCr, v1.UpdateOptions{})
		if err != nil {
			panic(err)
		}
		count++
		time.Sleep(2 * time.Minute)
	}

	klog.Infof("eip total: %d need update: %d success: %d ", len(eips), len(eipsNeedUpdate), count)

}
