#!/usr/bin/env python3

import subprocess
import json
from collections import defaultdict
import argparse
import copy
from datetime import datetime

user="boson"
passwd="a65W9NWc_TKM_0W"
host="************"
port="61831"
db_name="boson_service_v2"

def run_kubectl_get_json(resource, namespace=None):
    """
    运行 `kubectl get <resource> -o json` 并返回解析后的 JSON。
    如果执行失败则在 stderr 打印错误信息并返回 None。
    """
    cmd = ["kubectl", "get", resource, "-o", "json"]
    if namespace:
        cmd += ["-n", namespace]

    try:
        result = subprocess.run(cmd, stdout=subprocess.PIPE, stderr=subprocess.PIPE, text=True, check=True)
        return json.loads(result.stdout)
    except subprocess.CalledProcessError as e:
        print(f"Failed to get {resource}:\n{e.stderr.strip()}")
        return None

def run_kubectl_patch(resource_type, name, patch_data, namespace=None):
    cmd = [
        "kubectl", "patch", resource_type, name,
        "--type=merge",
        "-p", json.dumps(patch_data)
    ]
    if namespace:
        cmd += ["-n", namespace]
    try:
        subprocess.run(cmd, check=True, text=True)
        print(f"[PATCHED] {resource_type}/{name} has been updated.")
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] Failed to patch {resource_type}/{name}:\n{e.stderr.strip()}")

def sqlExec(sql):
    psqlCmdExec = f"psql postgresql://{user}:{passwd}@{host}:{port}/{db_name} -t -c \"{sql}\""
    try:
        result = subprocess.run(psqlCmdExec, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True, check=True)
        return result.stdout.strip()
    except subprocess.CalledProcessError as e:
        print(f"[ERROR] SQL Exec failed: {e.stderr.strip()}")
        return ""    

def extract_dnat_key_from_hanatgw(rule):
    """
    从 HaNatGw spec.dnatRules 中的单条规则，生成用于比较的元组 (eip, externalPort, internalIp, internalPort, protocol)。
    """
    return (
        rule.get("eip"),
        str(rule.get("externalPort")),
        rule.get("internalIp"),
        str(rule.get("internalPort")),
        rule.get("protocol")
    )

def extract_dnat_key_from_cr(rule):
    """
    从 Dnat CR 的 spec 中，生成用于比较的元组 (eip, outerPorts, internalIP, innerPorts, protocol)。
    """
    return (
        rule.get("eip"),
        str(rule.get("outerPorts")),
        rule.get("internalIP"),
        str(rule.get("innerPorts")),
        rule.get("protocol")
    )

def save_json(filename, data):
    with open(filename, 'w', encoding='utf-8') as f:
        print(f"{data}", file=f)

def is_internal_ip_in_cidrs(internal_ip, cidrs):
    """
    检查给定的 internal_ip 是否在指定的 CIDR 范围内。
    """

    # 使用 ipaddress 模块检查 IP 是否在 CIDR 范围内
    from ipaddress import ip_network, ip_address
    for cidr in cidrs.splitlines():
        cidr = cidr.strip()
        if ip_address(internal_ip) in ip_network(cidr):
            return True
    return False

def analyze_and_patch_hanatgw(dry_run=True):
    # 1. 获取所有 HaNatGw 资源
    hanatgws_data = run_kubectl_get_json("hanatgw.network.sensecore.cn")
    if not hanatgws_data:
        print("No HaNatGw resources found.")
        return
    all_hanatgw_fle = "all_hanatgw_{}.txt".format(datetime.now().strftime("%Y%m%d%H%M%S"))
    save_json(all_hanatgw_fle, json.dumps(hanatgws_data, indent=4, ensure_ascii=False))
    print(f"All HaNatGw resources saved to: {all_hanatgw_fle}")

    # 2. 获取所有 Dnat 资源
    dnats_data = run_kubectl_get_json("dnat.network.sensecore.cn")
    if not dnats_data:
        print("No Dnat resources found.")
        return

    # 3. 将 Dnat CR 按 gatewayName 分组, 过滤hanatgw中不需要存在的dnat
    dnats_by_gateway = defaultdict(list)
    for dnat_item in dnats_data.get("items", []):
        status = dnat_item.get("status", {})
        state = status.get("state")
        dnat_name = dnat_item.get("metadata", {}).get("name", "")
        if state == "CREATED":
            print(f"dnat {dnat_name} state is CREATED, skip it")
            continue
        
        spec = dnat_item.get("spec", {})

        annotations = dnat_item.get("metadata", {}).get("annotations", {})
        eip_internal_type = annotations.get("boson.sensetime.com/eip-internal-type", "")
        if eip_internal_type != "true":
            eip = annotations.get("boson.sensetime.com/gw-source-ip", "")
            if eip != "":
                spec["eip"] = eip
        
            out_port = annotations.get("boson.sensetime.com/nat_gw_port", "")
            if out_port != "":
                spec["outerPorts"] = out_port
        
        internalIP = spec.get("internalIP", "")
        if internalIP == "":
            internalIP = spec.get("status", {}).get("bindInfo", {}).get("instanceIP", "")
            spec["internalIP"] = internalIP
        
        gateway_name = spec.get("gatewayName")
        # 非 数据网 bms, 也不应该存在hanatgw, 所以这里也跳过
        if state == "ACTIVE":
            internalIP = spec.get("internalIP", "")
            if internalIP == "":
                print(f"dnat {dnat_name} internalIP is empty, skip it")
                continue
    
            # get bms cidr but except data
            cidr_cmd = '''
            select cidr from subnets where vpc_id = (select vpc_id from ha_nat_gateways where name='%s' and deleted=false)
            and scope !='DATA' and provider='CONTROLLER' and deleted=false''' % gateway_name
            cidrs_str = sqlExec(cidr_cmd)
            if is_internal_ip_in_cidrs(internalIP, cidrs_str):
                print(f"dnat {dnat_name} internalIP {internalIP} is in bms service cidrs {cidrs_str}, skip it")
                continue

        # inject dnat name to spec, for log
        spec["Name"] = dnat_name
        if gateway_name:
            dnats_by_gateway[gateway_name].append(spec)
    all_dnat_file = "all_dnat_{}.txt".format(datetime.now().strftime("%Y%m%d%H%M%S"))
    save_json(all_dnat_file, json.dumps(dnats_by_gateway, indent=4, ensure_ascii=False))
    print(f"All Dnat resources grouped by gatewayName saved to: {all_dnat_file}")


    # 4. 遍历每个 HaNatGw，进行比对
    for gw_item in hanatgws_data.get("items", []):
        gw_name = gw_item["metadata"]["name"]
        spec_rules = gw_item.get("spec", {}).get("dnatRules", [])
        original_rules = copy.deepcopy(spec_rules)

        # HaNatGw 中定义的规则集合
        gw_rules_set = set(extract_dnat_key_from_hanatgw(rule) for rule in spec_rules)

        # 与该 HaNatGw 关联的 Dnat CR 规则集合
        cr_rules = dnats_by_gateway.get(gw_name, [])
        cr_rules_set = set(extract_dnat_key_from_cr(rule) for rule in cr_rules)

        # 计算“多余”（出现在 HaNatGw spec 中但没有相关dnat CR 中）
        extra_in_cr = gw_rules_set - cr_rules_set
        if extra_in_cr:
            print(f"HaNatGw {gw_name} spec dnat: ")
            display_rules(gw_rules_set)

            print(f"HaNatGw {gw_name} releated dnat: ")
            display_rules(cr_rules)

            print(f"HaNatGw {gw_name} spec exist deleted dnat: ")
            display_rules(extra_in_cr)

            filtered_rules = [
                rule for rule in original_rules
                if extract_dnat_key_from_hanatgw(rule) not in extra_in_cr
            ]

            # 构造 patch 内容
            patch_body = {
                "spec": {
                    "dnatRules": filtered_rules
                }
            }

            if dry_run:
                print(f"[Dry Run] 会执行 patch: {gw_name}")
                print(json.dumps(patch_body, indent=2))
            else:
                run_kubectl_patch("hanatgw.network.sensecore.cn", gw_name, patch_body)

            print("--------------------------------------------------------------------------")
            continue


def display_rules(rules):
    for rule in rules:
        print(f"    {rule}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="校验并修复 HaNatGw 的 DNAT 配置")
    parser.add_argument("--no-dry-run", dest="dry_run", action="store_false", default=True, help="仅输出不执行（默认行为）")
    args = parser.parse_args()

    print(f"dry_run: {args.dry_run}")
    analyze_and_patch_hanatgw(dry_run=args.dry_run)