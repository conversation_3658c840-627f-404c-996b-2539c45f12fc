#!/usr/bin/env bash

# 修复企业EIP泄漏
# 详见：https://ones.ainewera.com/wiki/#/team/JNwe8qUX/space/F5zdhken/page/8ScP93ip

kubectl get hanatgws -oyaml >> hagw.yaml

export PGHOST="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  host:" | awk '{print $2}')"
export PGUSER="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  user:" | awk '{print $2}')"
export PGPASSWORD="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  password:" | awk '{print $2}')"
export PGPORT=$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  port:" | awk -F '"' '{print $2}')
export PGDATABASE="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  db:" | awk '{print $2}')"

Zone="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "env:" | awk '{print $2}' | xargs)"
ZoneSuffix=$(echo "${Zone: -1}" | tr '[:lower:]' '[:upper:]')
echo "zone suffix: ${ZoneSuffix}"

for eip in $(psql -t -c "select eip_ip from eip_pools where allocated=true and sku='ENTERPRISE_INTERNAL_${ZoneSuffix}'"); do
    if grep -q -w -E "elastic-nic-conf.*$eip" hagw.yaml; then
        # find in hagw, not leak
        echo "EIP $eip is used by HAGW, skipped..."
    else
        echo "EIP $eip is not used by HAGW, leak, releasing..."
        psql -c "update eip_pools set allocated=false where eip_ip='$eip'"
    fi
done