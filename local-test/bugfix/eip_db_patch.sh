#!/usr/bin/env bash

# 只在 2024 0530 版本使用,
#
# region all
#
# eip pools 增加line 列， 并且将sku 赋值到line
#

export PGHOST="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  host:" | awk '{print $2}')"
export PGUSER="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  user:" | awk '{print $2}')"
export PGPASSWORD="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  password:" | awk '{print $2}')"
export PGPORT=$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  port:" | awk -F '"' '{print $2}')
export PGDATABASE="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  db:" | awk '{print $2}')"

column_name="line"
psql -c "\d eip_pools" | grep -q $column_name
ret=$?; if [[ $ret != 0 ]]; then
    echo "not found column_name $column_name in table eip_pools, insert!"
    psql -c "alter table eip_pools add column $column_name varchar(64)"
    psql -c "update eip_pools set $column_name = sku"
else
    echo "found column_name $column_name in table eip_pools."
fi
