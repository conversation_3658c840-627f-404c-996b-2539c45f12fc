#!/usr/bin/env bash

# 只在 2024 0430 版本使用
#
# bug list
# 728800 【0430】创建slb失败，日志显示pq: duplicate key value violates unique constraint \"slb_envoy_resource_pkey\
# https://ones.ainewera.com/project/#/team/JNwe8qUX/task/NUr79ZLqw6qxovwJ
#
# 729037 【0430】【slb】释放slb后，deployment和pod均未释放
# https://ones.ainewera.com/project/#/team/JNwe8qUX/task/NUr79ZLqxG6Gy2it 
#

export PGHOST="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  host:" | awk '{print $2}')"
export PGUSER="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  user:" | awk '{print $2}')"
export PGPASSWORD="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  password:" | awk '{print $2}')"
export PGPORT=$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  port:" | awk -F '"' '{print $2}')
export PGDATABASE="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  db:" | awk '{print $2}')"

psql -c "alter table if exists slb_envoy_resource drop constraint if exists slb_envoy_resource_pkey;"
