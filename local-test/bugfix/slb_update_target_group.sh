#!/usr/bin/env bash

# 在930版本，SLB挂载BMS需求中，对于后端组的类型做了定义调整
# 需要调整存量的DB记录的值


export PGHOST="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  host:" | awk '{print $2}')"
export PGUSER="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  user:" | awk '{print $2}')"
export PGPASSWORD="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  password:" | awk '{print $2}')"
export PGPORT=$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  port:" | awk -F '"' '{print $2}')
export PGDATABASE="$(head -n 7 /boson-toolbox/boson-provider.yaml | grep "  db:" | awk '{print $2}')"

psql -c "update slb_target_groups set type='SERVER_INSTANCE' where type='ECS_INSTANCE';"
psql -c "update slb_resource_target_group set type='SERVER_INSTANCE' where type='ECS_INSTANCE';"
