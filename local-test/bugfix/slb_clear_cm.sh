#!/usr/bin/env bash

# 在1030版本，去掉了slb的cm资源，需要清理

if ! kubectl get crd slbs.network.sensecore.cn &> /dev/null; then
  echo "CRD slbs.network.sensecore.cn not exist, exit"
  exit 0
fi

for slb in $(kubectl get slbs.network.sensecore.cn -o custom-columns=":metadata.name" --no-headers); do
  echo "SLB: $slb"
  
  # 检查 ConfigMap 是否存在
  cm_name=$(kubectl get configmaps -n plat-boson-infra "$slb"-0-config -o jsonpath="{.metadata.name}" 2>/dev/null)
  
  if [[ -n "$cm_name" ]]; then
    echo "ConfigMap exists: ${cm_name}. Deleting..."
    kubectl delete configmap "$cm_name" -n plat-boson-infra --force
  else
    echo "ConfigMap not found for SLB: $slb"
  fi
done
