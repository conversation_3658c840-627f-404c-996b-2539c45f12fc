package main

import (
	"fmt"

	_ "github.com/lib/pq"
	"xorm.io/xorm"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
)

var rpConfig *config.Config

func init() {
	rpConfig = config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))
}

func main() {
	pgInfo := fmt.Sprintf("host=%s port=%v user=%s "+
		"password=%s dbname=%s sslmode=disable",
		rpConfig.PG.Host, rpConfig.PG.Port, rpConfig.PG.User, rpConfig.PG.Password, rpConfig.PG.DB)
	fmt.Println(pgInfo)
	engine, err := xorm.NewEngine("postgres", pgInfo)

	if err != nil {
		println(err.Error())
		return
	}

	defer engine.Close()

	tables, err := engine.DBMetas()
	if err != nil {
		fmt.Println(err)
	}

	for _, table := range tables {
		_, err = engine.Exec("truncate " + table.Name)
		if err != nil {
			fmt.Println(err)
		}
	}

	for _, table := range tables {
		fmt.Println("table: ", table.Name)
		results, _ := engine.Query("select * from " + table.Name)
		fmt.Println(results)
	}
}
