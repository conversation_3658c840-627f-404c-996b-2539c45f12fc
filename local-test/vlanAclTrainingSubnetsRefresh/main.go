package main

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"os"
	"strings"

	nadv1 "github.com/k8snetworkplumbingwg/network-attachment-definition-client/pkg/apis/k8s.cni.cncf.io/v1"
	nadclientv1 "github.com/k8snetworkplumbingwg/network-attachment-definition-client/pkg/client/clientset/versioned/typed/k8s.cni.cncf.io/v1"
	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	kubeovnv1 "github.com/kubeovn/kube-ovn/pkg/client/clientset/versioned/typed/kubeovn/v1"
	"github.com/sirupsen/logrus"
	v1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned/typed/network/v1"
	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	k8s_errors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

var (
	Engine         *db.EngineWrapper                 = nil
	KubeovnClient  *kubeovnv1.KubeovnV1Client        = nil
	BosonNetClient *netv1.NetworkV1Client            = nil
	NADClient      *nadclientv1.K8sCniCncfIoV1Client = nil
	RpConfig       *config.Config                    = nil
	Zone           string                            = ""
)

type vpcInfo struct {
	VpcDB    *db.Vpcs  `json:"vpcDB"`
	VpcCR    *ovn.Vpc  `json:"vpcCR"`
	RmIDInfo *rmIDInfo `json:"rmIDInfo"`
}

type rmIDInfo struct {
	Subscriptions  string `json:"Subscriptions"`
	ResourceGroups string `json:"ResourceGroups"`
	Zones          string `json:"Zones"`
	ResourceType   string `json:"ResourceType"`
	Vni            int    `json:"Vni"`
}

func init() {
	ch := utils.NewStopChannel()
	resourceprovider.BosonProvider = resourceprovider.NewResourceProvider()
	exporter.Exporter = exporter.NewExporter(resourceprovider.BosonProvider.RpConfig)
	exporter.Exporter.Serve(ch)

	RpConfig = resourceprovider.BosonProvider.RpConfig
	Engine = resourceprovider.BosonProvider.Engine
	KubeovnClient = resourceprovider.BosonProvider.KubeovnClient
	BosonNetClient = resourceprovider.BosonProvider.BosonNetClient
	NADClient = resourceprovider.BosonProvider.NADClient
	Zone = resourceprovider.BosonProvider.RpConfig.Boson.VPCDefaultAZ
}

/*
	cn-sh-01f 训练网需要从同cidr不同vlan模式切换到不同cidr，不同vlan的vlan+acl模式（RoCE 400G）
	当前01f没有使用训练网，所以可以切换
	1. check当前ovn（kubectl get ip |grep snt-xxxx），bms（ipa）是否被使用，如果使用直接返回
	2. 如果有旧的训练网，清理旧的subnet，创建新的subnet
	3.
*/
func checkTrainingSubnetUsed(vpcName string) (bool, error) {
	logrus.Infoln("check vpc:", vpcName)

	// 定义标签选择器
	labelSelector := fmt.Sprintf("vpc_name=%s,subnet_scope=TRAINING,subnet_type=VLAN", vpcName)

	// 获取自定义资源的列表
	listOptions := metav1.ListOptions{
		LabelSelector: labelSelector,
	}

	subnets, err := KubeovnClient.Subnets().List(context.Background(), listOptions)
	if err != nil {
		logrus.Errorln("get vpc subnet CR error", err)
		return true, err
	}
	for _, sn := range subnets.Items {
		if sn.Status.V4UsingIPs > 0 {
			return true, nil
		}
	}
	ipas, err := Engine.Query("select name from ipas where deleted=false and subnet_id in (select subnet_id from subnets where deleted=false and provider = 'CONTROLLER' and scope = 'TRAINING' and network_type = 'VLAN' and zone =?  and vpc_id in (select vpc_id from vpcs where name = ?))", Zone, vpcName)
	if err != nil {
		logrus.Errorln("get ipas db err", err)
		return true, err
	}
	logrus.Infoln("ipas in vpc :", ipas)

	return len(ipas) > 0, nil
}

func vlanAclTrainingSubnetEnable() bool {
	// 0 means: vlan maping vxlan
	return RpConfig.Boson.TrainingNetworks.Vlan_acl_nic_max_count > 0
}

func ovnTrainingSubnetEnable() bool {
	// 0 means: vlan maping vxlan
	return len(RpConfig.Boson.TrainingNetworks.KubeOvn) > 0
}

func bmsTrainingSubnetEnable() bool {
	// 0 means: vlan maping vxlan
	return len(RpConfig.Boson.TrainingNetworks.Bms.Gws) > 0
}

func getvpcs() ([]string, error) {
	vpcDatas, err := dao.GetAllVpcs(Engine)
	if err != nil {
		logrus.Errorln("get vpcs db err", err)
		return nil, err
	}

	var vpcs []string

	for _, vpc := range vpcDatas {
		vpcs = append(vpcs, vpc.Name)
	}

	return vpcs, nil
}

func main() {
	if len(os.Args) != 3 {
		logrus.Errorln("parameter error: ", os.Args[0], "vpc all/vpc-names")
		return
	}
	opt := os.Args[1]
	if opt != "vpc" {
		logrus.Errorln("operation object error:", opt)
		return
	}

	//dryRun := os.Args[3] == "true"
	vpcNameSlice := []string{}
	var err error
	if os.Args[2] == "all" {
		if vpcNameSlice, err = getvpcs(); err != nil {
			logrus.Errorln("get vpcs error: ", err)
			return
		}
	} else {
		vpcNameSlice = strings.Split(os.Args[2], ",")
	}

	logrus.Infoln(fmt.Sprintf("refresh vpcs:%s", objJson(vpcNameSlice)))
	if !ovnTrainingSubnetEnable() {
		logrus.Infoln("no ovn training network config,skip")
	}

	if !bmsTrainingSubnetEnable() {
		logrus.Infoln("no bms training network config,skip")
	}
	if !vlanAclTrainingSubnetEnable() {
		logrus.Infoln("no vlan acl training network config,skip")
		return
	}

	skip := []string{}
	success := []string{}
	fail := []string{}

	for _, vpcName := range vpcNameSlice {
		logrus.Infoln(fmt.Sprintf("refresh vpc:%s start", vpcName))

		vpcInfo, err := getVpcInfo(vpcName)
		if err != nil {
			fail = append(fail, vpcName)
			logrus.Errorln("getVpcInfo error", err.Error())
			continue
		}

		if vpcInfo.VpcDB.SubnetType == network.SubnetType_POD_SERVICE.String() || vpcInfo.VpcDB.SubnetType == network.SubnetType_NULL.String() {
			logrus.Infoln(fmt.Sprintf("skip refresh vpc:%s , no training subnets %s", vpcName, vpcInfo.VpcDB.SubnetType))
			skip = append(skip, vpcName)
			continue
		}

		used, err := checkTrainingSubnetUsed(vpcName)
		if err != nil {
			logrus.Errorln(fmt.Sprintf("refresh vpc:%s fail, err:%s", vpcName, err.Error()))
			fail = append(fail, vpcName)
			continue
		}
		if used {
			logrus.Errorln(fmt.Sprintf("vpc:%s training subnet is using, pelease release it and retry", vpcName))
			fail = append(fail, vpcName)
			continue
		}

		if err := refreshTrainingSubnets(vpcInfo); err != nil {
			logrus.Errorln(fmt.Sprintf("refresh vpc:%s fail, err:%s", vpcName, err.Error()))
			fail = append(fail, vpcName)
			continue
		}
		logrus.Infoln(fmt.Sprintf("refresh vpc:%s success", vpcName))
		success = append(success, vpcName)
	}

	if len(fail) > 0 {
		logrus.Errorln(fmt.Sprintf("refresh vpc total %d:\n	 success: %d %v \n	 skip: %d %v\n	fail: %d %v\n",
			len(vpcNameSlice), len(success), success, len(skip), skip, len(fail), fail))
	} else {
		logrus.Infoln(fmt.Sprintf("refresh vpc total %d:\n	 success: %d %v \n	 skip: %d %v\n	fail: %d %v\n",
			len(vpcNameSlice), len(success), success, len(skip), skip, len(fail), fail))
	}
	return

}

func getVpcInfo(vpcName string) (*vpcInfo, error) {
	vpcData := &db.Vpcs{}
	has, err := Engine.Where("name = ? and deleted = false", vpcName).Get(vpcData)
	if err != nil {
		logrus.Errorln("get vpc db err", err)
		return nil, err
	}
	if !has {
		err := fmt.Errorf("vpc(%s) not exist", vpcName)
		logrus.Errorln("vpc db not exist", err)
		return nil, err
	}
	logrus.Infoln(objJson(objJson))

	vpc, err := KubeovnClient.Vpcs().Get(
		context.Background(), vpcData.Name, metav1.GetOptions{})
	if err != nil {
		logrus.Errorln("get vpc CR error", err)
		return nil, err
	}
	logrus.Infoln(objJson(vpc))

	rmIDInfo, err := generateRMIDInfo(vpcData.VPCID)
	if err != nil {
		logrus.Errorln("generate vpc RMID info error", err)
		return nil, err
	}
	logrus.Infoln(objJson(rmIDInfo))

	return &vpcInfo{
		VpcDB:    vpcData,
		VpcCR:    vpc,
		RmIDInfo: rmIDInfo,
	}, nil
}

func removeTrainingSubnet(vpcInfo *vpcInfo) error {
	vpc := vpcInfo.VpcDB
	sns, err := dao.ListSubnets(Engine, vpc.VPCID)
	if err != nil {
		logrus.Errorf("failed to get subnets %s error: %s", vpc.Name, err)
		return err
	}

	// delete subnets cr, db,cdir_pool

	s := Engine.NewSession()
	defer s.Close()

	for _, sn := range sns {
		if sn.Scope != "TRAINING" || sn.NetworkType != "VLAN" {
			continue
		}

		logrus.Infof("Deleted vpc %s traning subnets: %s", vpc.Name, sn.Name)
		if sn.Provider == network.SubnetProperties_CONTROLLER.String() {
			if err := BosonNetClient.Subnets().Delete(context.Background(), sn.Name, metav1.DeleteOptions{}); err != nil && !k8s_errors.IsNotFound(err) {
				logrus.Errorf("Delete controller CR subnet %s error: %s", sn.Name, err)
				return err
			}
		}

		if sn.Provider == network.SubnetProperties_OVN.String() {
			// delete nad
			nadName := "nad-" + sn.Name[len("snt-"):]

			err := NADClient.NetworkAttachmentDefinitions(resourceprovider.NADNamespace).Delete(context.Background(), nadName, metav1.DeleteOptions{})
			if err != nil && !k8s_errors.IsNotFound(err) {
				logrus.Errorf("Delete NAD CR %s used by subnet %s error: %s", nadName, sn.Name, err)
				return err
			}

			if err := KubeovnClient.Subnets().Delete(context.Background(), sn.Name, metav1.DeleteOptions{}); err != nil && !k8s_errors.IsNotFound(err) {
				logrus.Errorf("Delete CR subnet %s error: %s", sn.Name, err)
				return err
			}

		}

		if err := dao.DeleteSubnet(s, sn.SubnetID); err != nil {
			logrus.Errorf("Delete subnets %s for vpc %s error: %s", sn.Name, vpc.Name, err)
			return err
		}

		// free cidr pool, if the cidr_pool's records not used, they can be deleted directly
		if len(sn.CIDRPoolID) > 0 {
			if err := dao.ReleaseCidrPool(s, sn.CIDRPoolID); err != nil {
				logrus.Errorf("Release cidr pool %s error: %s", sn.CIDRPoolID, err)
				return err
			}
		}

	}

	if err := s.Commit(); err != nil {
		logrus.Errorf("Delete vpc %s error: %s", vpc.Name, err)
		return err
	}

	logrus.Infof("Deleted vpc traning subnets: %s", vpc.Name)
	return nil
}

func createOneOvnTraningSubnet(
	session *db.SessionWrapper, cidrPoolData *db.CidrPools, ifId string, vpcCR *ovn.Vpc, vpcDB *db.Vpcs, rmIDInfo *rmIDInfo,
) error {
	logrus.Infoln("createOneOvnTraningSubnet", "vni", cidrPoolData.VNI, "if_id", ifId, "vpc", vpcCR)

	// 1 update db allocated
	if err := dao.SetCidrAllocatedByID(session, cidrPoolData.CIDRPoolID, true); err != nil {
		logrus.Errorf("failed to update cidr %s allocated error :%s", cidrPoolData.CIDRPoolID, err.Error())
		return err
	}

	// 2 generate
	rand8 := utils.Rand8()
	sntName := fmt.Sprintf("snt-%s-%s", utils.NameToTenantCode(vpcCR.Name), rand8)
	nadName := fmt.Sprintf("nad-%s-%s", utils.NameToTenantCode(vpcCR.Name), rand8)
	sntID := utils.UUIDGen()

	nad := generateNadCR(nadName, ifId, vpcCR)
	snt := generateSubnet(sntID, sntName, nadName, ifId, vpcCR, cidrPoolData, vpcDB)
	sntData := generateSubnetData(sntID, sntName, vpcCR, cidrPoolData, vpcDB, rmIDInfo, snt.Name)

	logrus.Infoln(nad)
	logrus.Infoln(snt)
	logrus.Infoln(sntData)

	if _, err := session.InsertOne(sntData); err != nil {
		logrus.Errorf("failed to create training subnet in vpc %s error :%s", vpcCR.Name, err.Error())
		return err
	}

	// 3 create
	if _, err := NADClient.NetworkAttachmentDefinitions(resourceprovider.NADNamespace).Create(
		context.Background(), nad, metav1.CreateOptions{}); err != nil {
		logrus.Errorf("failed to create training subnet nad %v in vpc %s error :%s", nad, vpcCR.Name, err.Error())
		return err
	}

	if _, err := KubeovnClient.Subnets().Create(context.Background(), snt, metav1.CreateOptions{}); err != nil {
		logrus.Errorf("failed to create training subnet cr %v in vpc %s error :%s", snt, vpcCR.Name, err.Error())
		return err
	}
	return nil
}

func createVlanAclTrainingSubnets(
	vpcInfo *vpcInfo, provider string,
) (err error) {
	// check
	trainingNetworkCnt := RpConfig.Boson.TrainingNetworks.Vlan_acl_nic_max_count
	if trainingNetworkCnt == 0 {
		err := errors.New("boson training network config error")
		logrus.Errorln(err)
		return err
	}
	vpcCR := vpcInfo.VpcCR
	vpcDB := vpcInfo.VpcDB
	rmIDInfo := vpcInfo.RmIDInfo
	tenantCode := utils.NameToTenantCode(vpcCR.Name)

	session := Engine.NewSession()
	defer session.Close()

	// 1. get all cidrs with free vni and order by  cidr
	var cidrPools []db.CidrPools
	// where clause need zone condition. i don't wyh the same sql doesn't need it in vpcs.go. maybe it si related with wrapper
	if err := session.Where("scope = ? AND network_type = ? AND allocated = false  and zone_prp=? and vni NOT IN (SELECT vni FROM cidr_pools WHERE allocated = true and zone_prp=?)", "TRAINING", "VLAN", RpConfig.Boson.VPCDefaultAZ, RpConfig.Boson.VPCDefaultAZ).
		OrderBy("vni").OrderBy("inet(cidr)").Limit(trainingNetworkCnt).Find(&cidrPools); err != nil {
		session.Rollback()
		logrus.Errorf("failed to create  training error :%s", err.Error())
		return err
	}

	logrus.Infoln("vpc allocate cidrs", "cidrpool", cidrPools, "vpc", vpcCR.Name, "count", trainingNetworkCnt)

	if len(cidrPools) < trainingNetworkCnt {
		session.Rollback()
		logrus.Errorf("failed to allocate  training subnets error :%s", err.Error())
		return err
	}

	// .3 create resource
	for index, cidrPoolData := range cidrPools {
		if provider == network.SubnetProperties_CONTROLLER.String() {
			// 3.1 update db allocated
			if err := dao.SetCidrAllocatedByID(session, cidrPoolData.CIDRPoolID, true); err != nil {
				session.Rollback()
				logrus.Errorf("failed to update cidr %s allocated error :%s", cidrPoolData.CIDRPoolID, err.Error())
				return err
			}
			// 3.2 create bms subnet
			err = createBMSOneSubnet(&cidrPoolData, vpcDB, rmIDInfo, vpcCR, tenantCode,
				network.SubnetProperties_TRAINING.String(),
				network.SubnetProperties_CONTROLLER.String(),
				network.SubnetProperties_VLAN.String())
		}

		if provider == network.SubnetProperties_OVN.String() {
			ifId := fmt.Sprintf("roce_%d", index)
			err = createOneOvnTraningSubnet(session, &cidrPoolData, ifId, vpcCR, vpcDB, rmIDInfo)
		}

		if err != nil {
			session.Rollback()
			logrus.Errorf("failed to create  training error :%s", err.Error())
			return err
		}
	}

	// 提交事务
	if err := session.Commit(); err != nil {
		session.Rollback()
		logrus.Errorln(err)
		return err
	}
	logrus.Infof("traning subnet %d success in vpc: %s", len(cidrPools), vpcCR.Name)

	return nil
}

func refreshTrainingSubnets(vpcInfo *vpcInfo) error {
	// get subnets training
	// delete subnet db&cr
	if err := removeTrainingSubnet(vpcInfo); err != nil {
		logrus.Errorln("failed to remove training subnets error", vpcInfo.VpcDB.Name, err.Error())
		return err
	}

	if vpcInfo.VpcDB.SubnetType == network.SubnetType_POD_SERVICE_TRAINING.String() ||
		vpcInfo.VpcDB.SubnetType == network.SubnetType_POD_BMS_SERVICE.String() ||
		vpcInfo.VpcDB.SubnetType == network.SubnetType_POD_BMS_SERVICE_TRAINING.String() {
		// create ovn training subnet
		if err := createVlanAclTrainingSubnets(vpcInfo, network.SubnetProperties_OVN.String()); err != nil {
			logrus.Errorln("failed to create ovn training subnets error", vpcInfo.VpcDB.Name, err.Error())
			return err
		}
	}
	if vpcInfo.VpcDB.SubnetType == network.SubnetType_POD_BMS_SERVICE_TRAINING.String() ||
		vpcInfo.VpcDB.SubnetType == network.SubnetType_POD_SERVICE_BMS.String() {
		// create bms training subnet
		if err := createVlanAclTrainingSubnets(vpcInfo, network.SubnetProperties_CONTROLLER.String()); err != nil {
			logrus.Errorln("failed to create bms training subnets error", vpcInfo.VpcDB.Name, err.Error())
			return err
		}
	}

	return nil
}

func needAlloc(cidr string, excludeCidrs []string) bool {
	for _, cidrTmp := range excludeCidrs {
		if cidrTmp == cidr {
			return false
		}
	}
	return true
}

func generateBmsCidrs(excludeCidrs []string) []string {
	cidrs := []string{}
	for _, cidr := range RpConfig.Boson.TrainingNetworks.Bms.Gws {
		if !needAlloc(cidr, excludeCidrs) {
			continue
		}
		cidrs = append(cidrs, cidr)
	}
	return cidrs
}

func generateOvnCidrs(excludeCidrs []string) []string {
	cidrs := []string{}
	for _, networkConfig := range RpConfig.Boson.TrainingNetworks.KubeOvn {
		if !needAlloc(networkConfig.Gw, excludeCidrs) {
			continue
		}
		cidrs = append(cidrs, networkConfig.Gw)
	}
	return cidrs
}

func createBMSOneSubnet(
	pool *db.CidrPools, vpcData *db.Vpcs, rmIDInfo *rmIDInfo,
	vpc *ovn.Vpc, tenantCode, scope, provider, networkType string,
) error {
	//miaozhanyong to do for prp, request.ResourceGroupName
	subnetID := utils.UUIDGen()
	subnetName := "sn-" + utils.Substring(tenantCode, 0, 20) + "-" + subnetID[:8]
	sn := db.Subnets{
		SubnetID:     subnetID,
		VPCID:        vpcData.VPCID,
		DisplayName:  subnetName,
		CIDR:         pool.CIDR,
		Description:  "",
		IsDefault:    true,
		GatewayIP:    pool.GatewayIP,
		Scope:        scope,
		Provider:     provider,
		NetworkType:  networkType,
		ReservedIPs:  pool.Reserved,
		VNI:          pool.VNI,
		CIDRPoolID:   pool.CIDRPoolID,
		ID:           utils.GenRMID(rmIDInfo.Subscriptions, rmIDInfo.ResourceGroups, rmIDInfo.Zones, "subnets", subnetName),
		Name:         subnetName,
		ResourceType: "network.vpc.v1.subnet",
		Zone:         RpConfig.Boson.VPCDefaultAZ,
		State:        network.Subnet_State_name[int32(network.Subnet_ACTIVE)],
		CreatorID:    vpcData.CreatorID,
		OwnerID:      vpcData.OwnerID,
		TenantID:     vpcData.TenantID,
		CreateTime:   vpcData.CreateTime,
		UpdateTime:   vpcData.UpdateTime,
		Deleted:      false,
	}

	if err := dao.AddSubnet(Engine, &sn); err != nil {
		logrus.Errorf("Add subnet %v error: %s", sn, err)
		return err
	}

	// TODO: Create subnet CR
	subnetCR := &v1.Subnet{
		ObjectMeta: metav1.ObjectMeta{
			Name: sn.Name,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    resourceprovider.VPCKind,
				}),
			},
		},
		Spec: v1.SubnetSpec{
			CIDR:        sn.CIDR,
			GwIP:        sn.GatewayIP,
			VPC:         vpc.Name,
			Scope:       sn.Scope,
			Provider:    sn.Provider,
			NetworkType: sn.NetworkType,
			VNI:         sn.VNI,
			IsDefault:   sn.IsDefault,
			PoolID:      sn.CIDRPoolID,
		},
	}

	if scope == network.SubnetProperties_TRAINING.String() && networkType == network.SubnetProperties_VLAN.String() {
		subnetCR.ObjectMeta.Annotations = map[string]string{
			"k8s.v1.cni.cncf.io/resourceName": "rdma-training/roce", // tag anno for multi-roce NIC subnets
		}
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("CreateBosonSubnet")

	if _, err := BosonNetClient.Subnets().Create(context.Background(), subnetCR, metav1.CreateOptions{}); err != nil {
		logrus.Errorf("Create CR subnet %v error: %s", subnetCR, err)
		k8sRTMetrics.ObserveDurationAndLog()
		return err
	}

	k8sRTMetrics.ObserveDurationAndLog()
	return nil
}

func getAllTrainingCidrDatasWithVni(excludeCidrs []string, vni int) (
	[]resourceprovider.OvnTrainingCidrInfo, error,
) {
	gws := generateOvnCidrs(excludeCidrs)
	cidrPools, err := dao.FetchCIDRPoolsWithGws(
		Engine, "TRAINING", "VLAN", gws, vni)
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}
	if len(cidrPools) < len(gws) {
		err = fmt.Errorf(
			"allocate all training network fail, resources are not enough, vni:%d, needCnd:%d, getCnt:%d, allocate:%v, gws:%s",
			vni, len(gws), len(cidrPools), cidrPools, gws)
		logrus.Errorln(err)
		return nil, err
	}

	cidrPoolsMapTmp := map[string]db.CidrPools{}
	for _, cidr := range cidrPools {
		cidrPoolsMapTmp[cidr.GatewayIP] = *cidr
	}

	cidrPoolDatas := []resourceprovider.OvnTrainingCidrInfo{}
	for _, info := range RpConfig.Boson.TrainingNetworks.KubeOvn {
		if !needAlloc(info.Gw, excludeCidrs) {
			continue
		}

		cidr, ok := cidrPoolsMapTmp[info.Gw]
		if !ok {
			err := fmt.Errorf("no allocatable training roce cidr found, gw:%s vni:%d", info.Gw, vni)
			logrus.Errorln(err)
			return nil, err
		}

		cidrPoolDatas = append(cidrPoolDatas, resourceprovider.OvnTrainingCidrInfo{
			If_id: info.IfID,
			Cidr:  cidr,
		})
	}

	return cidrPoolDatas, nil
}

func generateNadCR(nadName string, if_id string, vpc *ovn.Vpc) *nadv1.NetworkAttachmentDefinition {
	return &nadv1.NetworkAttachmentDefinition{
		TypeMeta: metav1.TypeMeta{
			Kind:       "NetworkAttachmentDefinition",
			APIVersion: nadv1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      nadName,
			Namespace: resourceprovider.NADNamespace,
			Annotations: map[string]string{
				"k8s.v1.cni.cncf.io/resourceName": resourceprovider.ResourceName,
			},
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    resourceprovider.VPCKind,
				}),
			},
		},
		Spec: nadv1.NetworkAttachmentDefinitionSpec{
			Config: strings.ReplaceAll(strings.ReplaceAll(resourceprovider.NADConfig, "<nad_name>", nadName), "<roce_id>", if_id),
		},
	}
}

func generateSubnet(
	sntID string, sntName string,
	nadName string, if_id string, vpc *ovn.Vpc, cidrData *db.CidrPools, vpcData *db.Vpcs,
) *ovn.Subnet {
	return &ovn.Subnet{
		TypeMeta: metav1.TypeMeta{
			Kind:       resourceprovider.SubnetKind,
			APIVersion: ovn.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: sntName,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    resourceprovider.VPCKind,
				}),
			},
			Labels: map[string]string{
				"creator":                     "boson-provider",
				"kubernetes.io/metadata.name": sntName,
				"subnet_id":                   sntID,
				"subnet_scope":                cidrData.Scope,
				"subnet_type":                 cidrData.NetworkType,
				"tenant_id":                   vpcData.TenantID,
				"vni":                         fmt.Sprintf("%v", cidrData.VNI),
				"vpc_id":                      vpcData.VPCID,
				"vpc_name":                    vpc.Name,
			},
			Annotations: map[string]string{
				"k8s.v1.cni.cncf.io/networks":     fmt.Sprintf("%s/%s", resourceprovider.NADNamespace, nadName),
				"k8s.v1.cni.cncf.io/resourceName": resourceprovider.ResourceName,
				"roce_id":                         resourceprovider.GetAnnosRoceID(if_id),
			},
		},
		Spec: ovn.SubnetSpec{
			Vpc:         vpc.Name,
			CIDRBlock:   cidrData.CIDR,
			Gateway:     cidrData.GatewayIP,
			Protocol:    ovn.ProtocolIPv4,
			NatOutgoing: false,
			Default:     false,
			ExcludeIps:  strings.Split(cidrData.Reserved, ","),
			Provider:    fmt.Sprintf("%s.%s", nadName, resourceprovider.NADNamespace),
		},
	}
}

func generateSubnetData(
	sntID string, sntName string,
	vpc *ovn.Vpc, cidrData *db.CidrPools, vpcData *db.Vpcs, rmIDInfo *rmIDInfo, vpcSubnetName string,
) *db.Subnets {
	return &db.Subnets{
		SubnetID:     sntID,
		VPCID:        vpcData.VPCID,
		DisplayName:  sntName,
		CIDR:         cidrData.CIDR,
		IsDefault:    false,
		GatewayIP:    cidrData.GatewayIP,
		Scope:        cidrData.Scope,
		Provider:     "OVN",
		NetworkType:  cidrData.NetworkType,
		ID:           utils.GenRMID(rmIDInfo.Subscriptions, rmIDInfo.ResourceGroups, rmIDInfo.Zones, "subnets", vpcSubnetName),
		Name:         sntName,
		ResourceType: "network.vpc.v1.subnet",
		Zone:         vpcData.Zone,
		State:        network.Subnet_State_name[int32(network.Subnet_ACTIVE)],
		CreatorID:    vpcData.CreatorID,
		OwnerID:      vpcData.OwnerID,
		TenantID:     vpcData.TenantID,
		CreateTime:   vpcData.CreateTime,
		UpdateTime:   vpcData.UpdateTime,
		Deleted:      false,
		CIDRPoolID:   cidrData.CIDRPoolID,
		VNI:          cidrData.VNI,
	}
}

// fmt.Sprintf("/subscriptions/%v/resourceGroups/%v/zones/%v/%v/%v",
//
//	subscriptionName, resourceGroupName, zone, resourceType, resourceName)
func generateRMIDInfo(vpcID string) (*rmIDInfo, error) {
	vpcAnyoneSubnetData := db.Subnets{}
	has, err := Engine.Where(
		"vpc_id = ? and scope = ? and network_type = ? and deleted = ?",
		vpcID, "TRAINING", "VLAN", false).Get(&vpcAnyoneSubnetData)
	if err != nil {
		logrus.Errorln("get vni db error", err)
		return nil, err
	}
	if !has {
		logrus.Infof("RoCE Training subnet not exist of vpc(%s)", vpcID)
		return nil, err
	}

	infoSlice := strings.Split(vpcAnyoneSubnetData.ID, "/")
	logrus.Infoln(objJson(infoSlice))
	info := &rmIDInfo{
		Subscriptions:  infoSlice[2],
		ResourceGroups: infoSlice[4],
		Zones:          infoSlice[6],
		ResourceType:   infoSlice[7],
		Vni:            vpcAnyoneSubnetData.VNI,
	}
	return info, nil
}

func objJson(d interface{}) string {
	data, _ := json.Marshal(d)
	return string(data)
}
