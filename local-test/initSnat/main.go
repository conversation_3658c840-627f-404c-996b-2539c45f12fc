package main

import (
	"fmt"
	"time"

	"github.com/sirupsen/logrus"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

func init() {
	rp.BosonProvider = rp.NewResourceProvider()
}

func initSnatRules() {
	snats := []db.SnatRules{}
	err := rp.BosonProvider.Engine.Find(&snats)
	if err != nil {
		logrus.Errorln(err)
		return
	}

	if len(snats) > 0 {
		logrus.Println("Already Inited.")
		return
	}

	eipdatas := []db.Eips{}

	err = rp.BosonProvider.Engine.Where("deleted = ?", false).Find(&eipdatas)
	if err != nil {
		logrus.Errorln(err)
		return
	}

	snats = []db.SnatRules{}

	for _, eipdata := range eipdatas {
		snats = append(snats, db.SnatRules{
			SnatRuleID:   utils.UUIDGen(),
			VPCID:        eipdata.VPCID,
			EIPID:        eipdata.EIPID,
			ID:           eipdata.ID,
			Name:         fmt.Sprintf("snat-%s-%s", utils.NameToTenantCode(eipdata.Name), utils.Rand8()),
			ResourceType: "network.eip.v1.snatRule",
			Zone:         eipdata.Zone,
			State:        types.StateActive,
			CreatorID:    eipdata.CreatorID,
			OwnerID:      eipdata.OwnerID,
			TenantID:     eipdata.TenantID,
			CreateTime:   time.Now(),
			UpdateTime:   time.Now(),
			Deleted:      false,
		})
	}
	logrus.Infoln("**", snats)

	_, err = rp.BosonProvider.Engine.Insert(&snats)
	if err != nil {
		logrus.Errorln(err)
		return
	}

	logrus.Println("Done.")

}

func main() {
	initSnatRules()
}
