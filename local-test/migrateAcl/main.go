package main

import (
	"context"
	"encoding/json"
	"fmt"
	"os"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"github.com/spf13/cobra"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/local-test/migrateAcl/parser"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"

	ovnv1 "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	kubeovnv1 "github.com/kubeovn/kube-ovn/pkg/client/clientset/versioned/typed/kubeovn/v1"
	v1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned/typed/network/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

const (
	SubnetSkipLabel           = "boson.sensetime.com/vpc-default-acl-skip"
	SubnetSkipValue           = "true"
	VPCACLKind                = "VpcAcl"
	VpcAclNamespace           = "plat-boson-infra"
	VpcNameLabel              = "boson.sensetime.com/vpc-name"
	VpcAclResourceLabel       = "boson.sensetime.com/vpc-acl-source"
	VpcAclResourceAPIValue    = "API"
	VpcAclResourceSubnetValue = "ovn-acl"
)

var (
	Engine         *db.EngineWrapper          = nil
	KubeovnClient  *kubeovnv1.KubeovnV1Client = nil
	BosonNetClient *netv1.NetworkV1Client     = nil
	DryRun                                    = true
	UpdateVpcLabel                            = false
)

var rootCmd = &cobra.Command{
	Use:   "migrateAcl",
	Short: "migrateAcl use for convert subnet acl to vpc acl and label vpc.",
	Long:  `migrateAcl use for convert subnet acl to vpc acl and label vpc.`,
	Run: func(cmd *cobra.Command, args []string) {
		fmt.Printf("Begin to migrateAcl, DryRun: %t, UpdateVpcLabel: %t\n", DryRun, UpdateVpcLabel)
		initClient()
		if UpdateVpcLabel {
			updateVpcSkipLabel()
		}
		processEverySubnet()
	},
}

func main() {
	rootCmd.PersistentFlags().BoolVarP(&UpdateVpcLabel, "update-vpc-label", "l", false, "Enable update vpc label with skip default vpc")
	rootCmd.PersistentFlags().BoolVarP(&DryRun, "dry-run", "d", true, "Enable to migrate vpc acl from subnet")
	if err := rootCmd.Execute(); err != nil {
		fmt.Println(err)
		os.Exit(1)
	}
}

func initClient() {
	resourceprovider.BosonProvider = resourceprovider.NewInitPoolProvider()
	ch := utils.NewStopChannel()
	exporter.Exporter = exporter.NewExporter(resourceprovider.BosonProvider.RpConfig)
	exporter.Exporter.Serve(ch)
	KubeovnClient = resourceprovider.BosonProvider.KubeovnClient
	BosonNetClient = resourceprovider.BosonProvider.BosonNetClient

	Engine = resourceprovider.BosonProvider.Engine

	if db.AZ == "cn-sh-01a" {
		db.SetAZ("cn-sh-01z")
	}
}

func updateVpcSkipLabel() {
	labelSelector := fmt.Sprintf("%s=%s", SubnetSkipLabel, SubnetSkipValue)
	sns, err := KubeovnClient.Subnets().List(context.Background(), metav1.ListOptions{LabelSelector: labelSelector})
	if err != nil {
		logrus.Fatalf("can not list subnet %v", err)
		return
	}
	for _, sn := range sns.Items {
		logrus.Infof("begin update vpc[%s] label", sn.Spec.Vpc)
		vpc, err := KubeovnClient.Vpcs().Get(context.Background(), sn.Spec.Vpc, metav1.GetOptions{})
		if err != nil {
			logrus.Errorf("can not get vpc %v", err)
			continue
		}
		if vpc.Labels != nil {
			vpc.Labels[SubnetSkipLabel] = SubnetSkipValue
		} else {
			vpc.Labels = make(map[string]string)
			vpc.Labels[SubnetSkipLabel] = SubnetSkipValue
		}
		_, err = KubeovnClient.Vpcs().Update(context.Background(), vpc, metav1.UpdateOptions{})
		if err != nil {
			logrus.Errorf("can not update vpc %v", err)
		}
	}

}

func processEverySubnet() {
	visitor := parser.NewVpcAclVistor()
	sns, err := KubeovnClient.Subnets().List(context.Background(), metav1.ListOptions{})
	if err != nil {
		logrus.Fatalf("can not list subnet %v", err)
		return
	}

	for _, sn := range sns.Items {
		// if sn.Labels != nil && sn.Labels[SubnetSkipLabel] == SubnetSkipValue {
		// 	logrus.Infof("begin skip subnet convert [%s]", sn.Name)
		// 	continue
		// }

		if !strings.HasPrefix(sn.Name, "sn-") {
			logrus.Infof("skip subnet convert [%s] with name", sn.Name)
			continue
		}

		logrus.Infof("begin process subnet convert [%s]", sn.Name)
		for si, sbAcl := range sn.Spec.Acls {
			if sbAcl.Priority < resourceprovider.SYSTEM_ACL_RESERVED_PRIORITY_MIN || sbAcl.Priority > resourceprovider.SYSTEM_ACL_RESERVED_PRIORITY_MAX {
				logrus.Debugf("cannot assign requested acl priority, acl: %v subnet: %s", sbAcl, sn.Name)
				continue
			}
			acls, err := visitor.GetVpcAcl(sbAcl.Match)
			if err != nil {
				logrus.Errorf("can not convert acl match in subnet[%s], match:%s", sn.Name, sbAcl.Match)
				continue
			}
			// validate acl destip length
			skipForValidate := false
			for _, acl := range acls {
				ips := strings.Split(acl.DstIp, ",")
				if len(ips) > 50 {
					logrus.Errorf("acl destip length is more than 50, skip this acl, sn name: %s, acl: %v", sn.Name, acl)
					skipForValidate = true
					break
				}
			}
			if skipForValidate {
				continue
			}
			fillVpcAclInfo(sn, si, acls)
		}
	}
}

func fillVpcAclInfo(sn ovnv1.Subnet, si int, acls []parser.VpcAcl) {
	for index, acl := range acls {

		dstPorts := strings.Split(acl.DstPort, ",")
		if len(dstPorts) > 15 {
			logrus.Errorf("dstPort count is more than 15, skip this acl, sn name: %s, acl: %v", sn.Name, acl)
			continue
		}

		priority := sn.Spec.Acls[si].Priority + index
		priType, pri, err := getVpcAclTypeAndPri(priority)
		if err != nil {
			logrus.Errorf("fail to get proirity info: %s with %v", sn.Spec.Vpc, err)
			continue
		}

		vpcInfo, err := dao.GetVpcByName(Engine, sn.Spec.Vpc)
		if err != nil {
			logrus.Errorf("fail to get vpc info: %s with %v", sn.Spec.Vpc, err)
			continue
		}

		isExisted, err := dao.ExistVPCACLByPriorityType(Engine, int32(pri), priType, vpcInfo.VPCID)
		if err != nil {
			logrus.Errorf("fail to get vpcacl: %s with %v", sn.Spec.Vpc, err)
			continue
		}
		if !isExisted {
			createData(sn, si, acl, index)
		} else {
			updateData(sn, si, acl, index)
		}

	}
}

func updateData(sn ovnv1.Subnet, si int, acl parser.VpcAcl, index int) {
	priority := sn.Spec.Acls[si].Priority + index
	priType, pri, err := getVpcAclTypeAndPri(priority)
	if err != nil {
		logrus.Errorf("fail to get proirity info: %s with %v", sn.Spec.Vpc, err)
		return
	}

	vpcInfo, err := dao.GetVpcByName(Engine, sn.Spec.Vpc)
	if err != nil {
		logrus.Errorf("fail to get vpc info: %s with %v", sn.Spec.Vpc, err)
		return
	}

	dbAcl, err := dao.GetVPCACLByPriorityType(Engine, int32(pri), priType, vpcInfo.VPCID)
	if err != nil && !strings.HasPrefix(err.Error(), "VPCACL not found") {
		logrus.Errorf("fail to get dbAcl info: %s with %v", sn.Spec.Vpc, err)
		return
	}

	crAcl := generateVpcAclCr(sn, si, acl, index, dbAcl.Name)
	jsonData, err := json.Marshal(crAcl)
	if err != nil {
		logrus.Errorf("Failed to marshal patch cr to JSON: %v", err)
	}
	logrus.Infof("intend to patch vpcacl name: %s spec: %s", dbAcl.Name, string(jsonData))
	if !DryRun {
		cr, err := BosonNetClient.VpcAcls(VpcAclNamespace).Get(context.Background(), dbAcl.Name, metav1.GetOptions{})
		if err != nil {
			logrus.Errorf("fail to get vpcacl cr: %s with %v", crAcl.Name, err)
			return
		}

		cr.Spec = crAcl.Spec
		_, err = BosonNetClient.VpcAcls(VpcAclNamespace).Update(context.Background(), cr, metav1.UpdateOptions{})
		if err != nil {
			logrus.Errorf("fail to patch vpcacl cr: %s with %v", crAcl.Name, err)
			return
		}
		logrus.Infof("succuss to update vpc acl in k8s[%s]", crAcl.Name)
	}

	newDbAcl, err := getUpdateVpcAclDb(sn, si, acl, index, dbAcl)
	if err != nil {
		logrus.Errorf("Failed to generate vpc updagte acl db to JSON: %v", err)
	}
	jsonData, err = json.Marshal(newDbAcl)
	if err != nil {
		logrus.Errorf("Failed to marshal vpc acl cr to JSON: %v", err)
	}
	logrus.Infof("intend to update vpcacl db: %s", string(jsonData))
	if !DryRun {
		err = dao.UpdateVPCACL(Engine, &newDbAcl)
		if err != nil {
			logrus.Errorf("fail to create vpcacl cr: %s with %v", newDbAcl.Name, err)
			return
		}
		logrus.Infof("succuss to update vpc acl in db [%s]", newDbAcl.Name)
	}

}

func createData(sn ovnv1.Subnet, si int, acl parser.VpcAcl, index int) {

	name := fmt.Sprintf("acl-%s-%s", strings.TrimPrefix(sn.Spec.Vpc, "vpc-"), utils.Rand8())

	crAcl := generateVpcAclCr(sn, si, acl, index, name)
	jsonData, err := json.Marshal(crAcl)
	if err != nil {
		logrus.Errorf("Failed to marshal vpc acl cr to JSON: %v", err)
	}
	logrus.Infof("intend to create vpcacl cr: %s", string(jsonData))
	if !DryRun {
		_, err := BosonNetClient.VpcAcls(VpcAclNamespace).Create(context.Background(), &crAcl, metav1.CreateOptions{})
		if err != nil {
			logrus.Errorf("fail to create vpcacl cr: %s with %v", crAcl.Name, err)
		}
		logrus.Infof("succuss to create vpc acl[%s]", crAcl.Name)
	}

	dbAcl, err := generateVpcAclDb(sn, si, acl, index, name)
	if err != nil {
		logrus.Errorf("Failed to generate vpc acl db to JSON: %v", err)
	}
	jsonData, err = json.Marshal(dbAcl)
	if err != nil {
		logrus.Errorf("Failed to marshal vpc acl db to JSON: %v", err)
	}
	logrus.Infof("intend to insert vpcacl db: %s", string(jsonData))
	if !DryRun {
		err = dao.AddVPCACL(Engine, &dbAcl)
		if err != nil {
			logrus.Errorf("fail to insert vpcacl db: %s with %v", dbAcl.Name, err)
		}
		logrus.Infof("succuss to insert vpc acl[%s]", dbAcl.Name)
	}
}

func generateVpcAclCr(sn ovnv1.Subnet, si int, acl parser.VpcAcl, index int, name string) v1.VpcAcl {

	priority := sn.Spec.Acls[si].Priority + index

	action := "deny"
	if sn.Spec.Acls[si].Action == "allow" {
		action = sn.Spec.Acls[si].Action
	}

	cr := v1.VpcAcl{
		TypeMeta: metav1.TypeMeta{
			Kind:       VPCACLKind,
			APIVersion: v1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      name,
			Namespace: VpcAclNamespace,
			Labels: map[string]string{
				VpcNameLabel:        sn.Spec.Vpc,
				VpcAclResourceLabel: VpcAclResourceAPIValue,
			},
			Annotations: map[string]string{
				VpcAclResourceLabel: VpcAclResourceSubnetValue,
			},
			OwnerReferences: sn.OwnerReferences,
		},

		Spec: v1.VpcAclSpec{
			Src:      "",
			SrcPort:  "",
			Dest:     acl.DstIp,
			DestPort: acl.DstPort,
			AclID:    name,
			Vpc:      sn.Spec.Vpc,
			Action:   action,
			Protocol: acl.Protocol,
			Priority: priority,
		},
	}

	if cr.Spec.Protocol == "" {
		cr.Spec.Protocol = "all"
	}

	return cr
}

func generateVpcAclDb(sn ovnv1.Subnet, si int, acl parser.VpcAcl, index int, name string) (db.VpcAcls, error) {
	priority := sn.Spec.Acls[si].Priority + index

	action := "deny"
	if sn.Spec.Acls[si].Action == "allow" {
		action = sn.Spec.Acls[si].Action
	}

	vpcInfo, err := dao.GetVpcByName(Engine, sn.Spec.Vpc)
	if err != nil {
		logrus.Errorf("fail to get vpc info: %s with %v", sn.Spec.Vpc, err)
		return db.VpcAcls{}, err
	}

	priType, pri, err := getVpcAclTypeAndPri(priority)
	if err != nil {
		logrus.Errorf("fail to get proirity info: %s with %v", sn.Spec.Vpc, err)
		return db.VpcAcls{}, err
	}

	dbAcl := db.VpcAcls{
		VpcAclID:     utils.UUIDGen(),
		DisplayName:  "",
		Description:  "covert-from-subnet",
		Action:       strings.ToUpper(action),
		Src:          "",
		SrcPort:      "",
		Dest:         acl.DstIp,
		DestPort:     acl.DstPort,
		Protocol:     acl.Protocol,
		Priority:     int32(pri),
		Type:         priType,
		VPCID:        vpcInfo.VPCID,
		ID:           vpcInfo.ID + "/vpcacls/" + name,
		Name:         name,
		ResourceType: "network.vpc.v1.acl",
		Zone:         vpcInfo.Zone,
		State:        vpc.ACL_CREATING.String(),
		CreatorID:    vpcInfo.CreatorID,
		OwnerID:      vpcInfo.OwnerID,
		TenantID:     vpcInfo.TenantID,
		CreateTime:   time.Now(),
		UpdateTime:   time.Now(),
		Deleted:      false,
	}

	if dbAcl.Protocol == "" {
		dbAcl.Protocol = "all"
	}
	return dbAcl, nil
}

func getUpdateVpcAclDb(sn ovnv1.Subnet, si int, acl parser.VpcAcl, index int, oldAcl *db.VpcAcls) (db.VpcAcls, error) {
	priority := sn.Spec.Acls[si].Priority + index

	action := "deny"
	if sn.Spec.Acls[si].Action == "allow" {
		action = sn.Spec.Acls[si].Action
	}

	vpcInfo, err := dao.GetVpcByName(Engine, sn.Spec.Vpc)
	if err != nil {
		logrus.Errorf("fail to get vpc info: %s with %v", sn.Spec.Vpc, err)
		return db.VpcAcls{}, err
	}

	priType, pri, err := getVpcAclTypeAndPri(priority)
	if err != nil {
		logrus.Errorf("fail to get proirity info: %s with %v", sn.Spec.Vpc, err)
		return db.VpcAcls{}, err
	}

	dbAcl := db.VpcAcls{
		VpcAclID:     oldAcl.VpcAclID,
		DisplayName:  oldAcl.DisplayName,
		Description:  oldAcl.Description,
		Action:       strings.ToUpper(action),
		Src:          oldAcl.Src,
		SrcPort:      oldAcl.SrcPort,
		Dest:         acl.DstIp,
		DestPort:     acl.DstPort,
		Protocol:     acl.Protocol,
		Priority:     int32(pri),
		Type:         priType,
		VPCID:        vpcInfo.VPCID,
		ID:           vpcInfo.ID + "/vpcacls/" + oldAcl.Name,
		Name:         oldAcl.Name,
		ResourceType: "network.vpc.v1.acl",
		Zone:         oldAcl.Zone,
		State:        oldAcl.State,
		CreatorID:    oldAcl.CreatorID,
		OwnerID:      oldAcl.OwnerID,
		TenantID:     oldAcl.TenantID,
		CreateTime:   oldAcl.CreateTime,
		UpdateTime:   time.Now(),
		Deleted:      false,
	}

	if dbAcl.Protocol == "" {
		dbAcl.Protocol = "all"
	}
	return dbAcl, nil
}

func getVpcAclTypeAndPri(prority int) (string, int, error) {
	if resourceprovider.CUSTOM_ACL_MID_PRIORITY_MIN <= prority && resourceprovider.CUSTOM_ACL_MID_PRIORITY_MAX >= prority {
		return vpc.ACLProperties_ACLType_name[3], prority - resourceprovider.CUSTOM_ACL_MID_PRIORITY_MIN, nil
	}
	if resourceprovider.SYSTEM_ACL_HIGH_PRIORITY_MIN <= prority && resourceprovider.SYSTEM_ACL_HIGH_PRIORITY_MAX >= prority {
		return vpc.ACLProperties_ACLType_name[2], prority - resourceprovider.SYSTEM_ACL_HIGH_PRIORITY_MIN, nil
	}
	if resourceprovider.SYSTEM_ACL_RESERVED_PRIORITY_MIN <= prority && resourceprovider.SYSTEM_ACL_RESERVED_PRIORITY_MAX >= prority {
		return vpc.ACLProperties_ACLType_name[1], prority - resourceprovider.SYSTEM_ACL_RESERVED_PRIORITY_MIN, nil
	}
	if resourceprovider.SYSTEM_ACL_GATEWAY_SERVICE_MIN <= prority && resourceprovider.SYSTEM_ACL_GATEWAY_SERVICE_MAX >= prority {
		return vpc.ACLProperties_ACLType_name[0], prority - resourceprovider.SYSTEM_ACL_GATEWAY_SERVICE_MIN, nil
	}

	return "", -1, fmt.Errorf("error in range with %d", prority)

}
