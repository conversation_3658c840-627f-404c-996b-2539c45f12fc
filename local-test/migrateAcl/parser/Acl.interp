token literal names:
null
'ip4.dst'
null
'ip.proto'
'&&'
'||'
'('
')'
null
null
null

token symbolic names:
null
IPADDRRULE
PORTRULE
PROTORULE
AND
OR
LEFT
RIGHT
RELATION
PREDICATE
WS

rule names:
acl


atn:
[4, 1, 10, 30, 2, 0, 7, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 3, 0, 17, 8, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 5, 0, 25, 8, 0, 10, 0, 12, 0, 28, 9, 0, 1, 0, 0, 1, 0, 1, 0, 0, 0, 33, 0, 16, 1, 0, 0, 0, 2, 3, 6, 0, -1, 0, 3, 4, 5, 1, 0, 0, 4, 5, 5, 8, 0, 0, 5, 17, 5, 9, 0, 0, 6, 7, 5, 2, 0, 0, 7, 8, 5, 8, 0, 0, 8, 17, 5, 9, 0, 0, 9, 10, 5, 3, 0, 0, 10, 11, 5, 8, 0, 0, 11, 17, 5, 9, 0, 0, 12, 13, 5, 6, 0, 0, 13, 14, 3, 0, 0, 0, 14, 15, 5, 7, 0, 0, 15, 17, 1, 0, 0, 0, 16, 2, 1, 0, 0, 0, 16, 6, 1, 0, 0, 0, 16, 9, 1, 0, 0, 0, 16, 12, 1, 0, 0, 0, 17, 26, 1, 0, 0, 0, 18, 19, 10, 6, 0, 0, 19, 20, 5, 4, 0, 0, 20, 25, 3, 0, 0, 7, 21, 22, 10, 5, 0, 0, 22, 23, 5, 5, 0, 0, 23, 25, 3, 0, 0, 6, 24, 18, 1, 0, 0, 0, 24, 21, 1, 0, 0, 0, 25, 28, 1, 0, 0, 0, 26, 24, 1, 0, 0, 0, 26, 27, 1, 0, 0, 0, 27, 1, 1, 0, 0, 0, 28, 26, 1, 0, 0, 0, 3, 16, 24, 26]