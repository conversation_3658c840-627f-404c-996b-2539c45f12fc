// Code generated from local-test/acl/parser/Acl.g4 by ANTLR 4.13.0. DO NOT EDIT.

package parser // Acl

import "github.com/antlr4-go/antlr/v4"

type BaseAclVisitor struct {
	*antlr.BaseParseTreeVisitor
}

func (v *BaseAclVisitor) VisitAndExpr(ctx *AndExprContext) interface{} {
	return v.VisitChildren(ctx)
}

func (v *BaseAclVisitor) VisitIpExpr(ctx *IpExprContext) interface{} {
	return v.VisitChildren(ctx)
}

func (v *BaseAclVisitor) VisitProtoExpr(ctx *ProtoExprContext) interface{} {
	return v.VisitChildren(ctx)
}

func (v *BaseAclVisitor) VisitPortExpr(ctx *PortExprContext) interface{} {
	return v.VisitChildren(ctx)
}

func (v *BaseAclVisitor) VisitParenExpr(ctx *ParenExprContext) interface{} {
	return v.VisitChildren(ctx)
}

func (v *BaseAclVisitor) VisitOrExpr(ctx *OrExprContext) interface{} {
	return v.VisitChildren(ctx)
}
