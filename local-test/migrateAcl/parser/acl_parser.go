// Code generated from local-test/acl/parser/Acl.g4 by ANTLR 4.13.0. DO NOT EDIT.

package parser // Acl

import (
	"fmt"
	"strconv"
	"sync"

	"github.com/antlr4-go/antlr/v4"
)

// Suppress unused import errors
var _ = fmt.Printf
var _ = strconv.Itoa
var _ = sync.Once{}

type AclParser struct {
	*antlr.BaseParser
}

var AclParserStaticData struct {
	once                   sync.Once
	serializedATN          []int32
	LiteralNames           []string
	SymbolicNames          []string
	RuleNames              []string
	PredictionContextCache *antlr.PredictionContextCache
	atn                    *antlr.ATN
	decisionToDFA          []*antlr.DFA
}

func aclParserInit() {
	staticData := &AclParserStaticData
	staticData.LiteralNames = []string{
		"", "'ip4.dst'", "", "'ip.proto'", "'&&'", "'||'", "'('", "')'",
	}
	staticData.SymbolicNames = []string{
		"", "IPADDRRULE", "PORTRULE", "PROTORULE", "AND", "OR", "LEFT", "RIGHT",
		"RELATION", "PREDICATE", "WS",
	}
	staticData.RuleNames = []string{
		"acl",
	}
	staticData.PredictionContextCache = antlr.NewPredictionContextCache()
	staticData.serializedATN = []int32{
		4, 1, 10, 30, 2, 0, 7, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1,
		0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 1, 0, 3, 0, 17, 8, 0, 1, 0, 1, 0, 1, 0,
		1, 0, 1, 0, 1, 0, 5, 0, 25, 8, 0, 10, 0, 12, 0, 28, 9, 0, 1, 0, 0, 1, 0,
		1, 0, 0, 0, 33, 0, 16, 1, 0, 0, 0, 2, 3, 6, 0, -1, 0, 3, 4, 5, 1, 0, 0,
		4, 5, 5, 8, 0, 0, 5, 17, 5, 9, 0, 0, 6, 7, 5, 2, 0, 0, 7, 8, 5, 8, 0, 0,
		8, 17, 5, 9, 0, 0, 9, 10, 5, 3, 0, 0, 10, 11, 5, 8, 0, 0, 11, 17, 5, 9,
		0, 0, 12, 13, 5, 6, 0, 0, 13, 14, 3, 0, 0, 0, 14, 15, 5, 7, 0, 0, 15, 17,
		1, 0, 0, 0, 16, 2, 1, 0, 0, 0, 16, 6, 1, 0, 0, 0, 16, 9, 1, 0, 0, 0, 16,
		12, 1, 0, 0, 0, 17, 26, 1, 0, 0, 0, 18, 19, 10, 6, 0, 0, 19, 20, 5, 4,
		0, 0, 20, 25, 3, 0, 0, 7, 21, 22, 10, 5, 0, 0, 22, 23, 5, 5, 0, 0, 23,
		25, 3, 0, 0, 6, 24, 18, 1, 0, 0, 0, 24, 21, 1, 0, 0, 0, 25, 28, 1, 0, 0,
		0, 26, 24, 1, 0, 0, 0, 26, 27, 1, 0, 0, 0, 27, 1, 1, 0, 0, 0, 28, 26, 1,
		0, 0, 0, 3, 16, 24, 26,
	}
	deserializer := antlr.NewATNDeserializer(nil)
	staticData.atn = deserializer.Deserialize(staticData.serializedATN)
	atn := staticData.atn
	staticData.decisionToDFA = make([]*antlr.DFA, len(atn.DecisionToState))
	decisionToDFA := staticData.decisionToDFA
	for index, state := range atn.DecisionToState {
		decisionToDFA[index] = antlr.NewDFA(state, index)
	}
}

// AclParserInit initializes any static state used to implement AclParser. By default the
// static state used to implement the parser is lazily initialized during the first call to
// NewAclParser(). You can call this function if you wish to initialize the static state ahead
// of time.
func AclParserInit() {
	staticData := &AclParserStaticData
	staticData.once.Do(aclParserInit)
}

// NewAclParser produces a new parser instance for the optional input antlr.TokenStream.
func NewAclParser(input antlr.TokenStream) *AclParser {
	AclParserInit()
	this := new(AclParser)
	this.BaseParser = antlr.NewBaseParser(input)
	staticData := &AclParserStaticData
	this.Interpreter = antlr.NewParserATNSimulator(this, staticData.atn, staticData.decisionToDFA, staticData.PredictionContextCache)
	this.RuleNames = staticData.RuleNames
	this.LiteralNames = staticData.LiteralNames
	this.SymbolicNames = staticData.SymbolicNames
	this.GrammarFileName = "Acl.g4"

	return this
}

// AclParser tokens.
const (
	AclParserEOF        = antlr.TokenEOF
	AclParserIPADDRRULE = 1
	AclParserPORTRULE   = 2
	AclParserPROTORULE  = 3
	AclParserAND        = 4
	AclParserOR         = 5
	AclParserLEFT       = 6
	AclParserRIGHT      = 7
	AclParserRELATION   = 8
	AclParserPREDICATE  = 9
	AclParserWS         = 10
)

// AclParserRULE_acl is the AclParser rule.
const AclParserRULE_acl = 0

// IAclContext is an interface to support dynamic dispatch.
type IAclContext interface {
	antlr.ParserRuleContext

	// GetParser returns the parser.
	GetParser() antlr.Parser
	// IsAclContext differentiates from other interfaces.
	IsAclContext()
}

type AclContext struct {
	antlr.BaseParserRuleContext
	parser antlr.Parser
}

func NewEmptyAclContext() *AclContext {
	var p = new(AclContext)
	antlr.InitBaseParserRuleContext(&p.BaseParserRuleContext, nil, -1)
	p.RuleIndex = AclParserRULE_acl
	return p
}

func InitEmptyAclContext(p *AclContext) {
	antlr.InitBaseParserRuleContext(&p.BaseParserRuleContext, nil, -1)
	p.RuleIndex = AclParserRULE_acl
}

func (*AclContext) IsAclContext() {}

func NewAclContext(parser antlr.Parser, parent antlr.ParserRuleContext, invokingState int) *AclContext {
	var p = new(AclContext)

	antlr.InitBaseParserRuleContext(&p.BaseParserRuleContext, parent, invokingState)

	p.parser = parser
	p.RuleIndex = AclParserRULE_acl

	return p
}

func (s *AclContext) GetParser() antlr.Parser { return s.parser }

func (s *AclContext) CopyAll(ctx *AclContext) {
	s.CopyFrom(&ctx.BaseParserRuleContext)
}

func (s *AclContext) GetRuleContext() antlr.RuleContext {
	return s
}

func (s *AclContext) ToStringTree(ruleNames []string, recog antlr.Recognizer) string {
	return antlr.TreesStringTree(s, ruleNames, recog)
}

type AndExprContext struct {
	AclContext
}

func NewAndExprContext(parser antlr.Parser, ctx antlr.ParserRuleContext) *AndExprContext {
	var p = new(AndExprContext)

	InitEmptyAclContext(&p.AclContext)
	p.parser = parser
	p.CopyAll(ctx.(*AclContext))

	return p
}

func (s *AndExprContext) GetRuleContext() antlr.RuleContext {
	return s
}

func (s *AndExprContext) AllAcl() []IAclContext {
	children := s.GetChildren()
	len := 0
	for _, ctx := range children {
		if _, ok := ctx.(IAclContext); ok {
			len++
		}
	}

	tst := make([]IAclContext, len)
	i := 0
	for _, ctx := range children {
		if t, ok := ctx.(IAclContext); ok {
			tst[i] = t.(IAclContext)
			i++
		}
	}

	return tst
}

func (s *AndExprContext) Acl(i int) IAclContext {
	var t antlr.RuleContext
	j := 0
	for _, ctx := range s.GetChildren() {
		if _, ok := ctx.(IAclContext); ok {
			if j == i {
				t = ctx.(antlr.RuleContext)
				break
			}
			j++
		}
	}

	if t == nil {
		return nil
	}

	return t.(IAclContext)
}

func (s *AndExprContext) AND() antlr.TerminalNode {
	return s.GetToken(AclParserAND, 0)
}

func (s *AndExprContext) EnterRule(listener antlr.ParseTreeListener) {
	if listenerT, ok := listener.(AclListener); ok {
		listenerT.EnterAndExpr(s)
	}
}

func (s *AndExprContext) ExitRule(listener antlr.ParseTreeListener) {
	if listenerT, ok := listener.(AclListener); ok {
		listenerT.ExitAndExpr(s)
	}
}

func (s *AndExprContext) Accept(visitor antlr.ParseTreeVisitor) interface{} {
	switch t := visitor.(type) {
	case AclVisitor:
		return t.VisitAndExpr(s)

	default:
		return t.VisitChildren(s)
	}
}

type IpExprContext struct {
	AclContext
}

func NewIpExprContext(parser antlr.Parser, ctx antlr.ParserRuleContext) *IpExprContext {
	var p = new(IpExprContext)

	InitEmptyAclContext(&p.AclContext)
	p.parser = parser
	p.CopyAll(ctx.(*AclContext))

	return p
}

func (s *IpExprContext) GetRuleContext() antlr.RuleContext {
	return s
}

func (s *IpExprContext) IPADDRRULE() antlr.TerminalNode {
	return s.GetToken(AclParserIPADDRRULE, 0)
}

func (s *IpExprContext) RELATION() antlr.TerminalNode {
	return s.GetToken(AclParserRELATION, 0)
}

func (s *IpExprContext) PREDICATE() antlr.TerminalNode {
	return s.GetToken(AclParserPREDICATE, 0)
}

func (s *IpExprContext) EnterRule(listener antlr.ParseTreeListener) {
	if listenerT, ok := listener.(AclListener); ok {
		listenerT.EnterIpExpr(s)
	}
}

func (s *IpExprContext) ExitRule(listener antlr.ParseTreeListener) {
	if listenerT, ok := listener.(AclListener); ok {
		listenerT.ExitIpExpr(s)
	}
}

func (s *IpExprContext) Accept(visitor antlr.ParseTreeVisitor) interface{} {
	switch t := visitor.(type) {
	case AclVisitor:
		return t.VisitIpExpr(s)

	default:
		return t.VisitChildren(s)
	}
}

type ProtoExprContext struct {
	AclContext
}

func NewProtoExprContext(parser antlr.Parser, ctx antlr.ParserRuleContext) *ProtoExprContext {
	var p = new(ProtoExprContext)

	InitEmptyAclContext(&p.AclContext)
	p.parser = parser
	p.CopyAll(ctx.(*AclContext))

	return p
}

func (s *ProtoExprContext) GetRuleContext() antlr.RuleContext {
	return s
}

func (s *ProtoExprContext) PROTORULE() antlr.TerminalNode {
	return s.GetToken(AclParserPROTORULE, 0)
}

func (s *ProtoExprContext) RELATION() antlr.TerminalNode {
	return s.GetToken(AclParserRELATION, 0)
}

func (s *ProtoExprContext) PREDICATE() antlr.TerminalNode {
	return s.GetToken(AclParserPREDICATE, 0)
}

func (s *ProtoExprContext) EnterRule(listener antlr.ParseTreeListener) {
	if listenerT, ok := listener.(AclListener); ok {
		listenerT.EnterProtoExpr(s)
	}
}

func (s *ProtoExprContext) ExitRule(listener antlr.ParseTreeListener) {
	if listenerT, ok := listener.(AclListener); ok {
		listenerT.ExitProtoExpr(s)
	}
}

func (s *ProtoExprContext) Accept(visitor antlr.ParseTreeVisitor) interface{} {
	switch t := visitor.(type) {
	case AclVisitor:
		return t.VisitProtoExpr(s)

	default:
		return t.VisitChildren(s)
	}
}

type PortExprContext struct {
	AclContext
}

func NewPortExprContext(parser antlr.Parser, ctx antlr.ParserRuleContext) *PortExprContext {
	var p = new(PortExprContext)

	InitEmptyAclContext(&p.AclContext)
	p.parser = parser
	p.CopyAll(ctx.(*AclContext))

	return p
}

func (s *PortExprContext) GetRuleContext() antlr.RuleContext {
	return s
}

func (s *PortExprContext) PORTRULE() antlr.TerminalNode {
	return s.GetToken(AclParserPORTRULE, 0)
}

func (s *PortExprContext) RELATION() antlr.TerminalNode {
	return s.GetToken(AclParserRELATION, 0)
}

func (s *PortExprContext) PREDICATE() antlr.TerminalNode {
	return s.GetToken(AclParserPREDICATE, 0)
}

func (s *PortExprContext) EnterRule(listener antlr.ParseTreeListener) {
	if listenerT, ok := listener.(AclListener); ok {
		listenerT.EnterPortExpr(s)
	}
}

func (s *PortExprContext) ExitRule(listener antlr.ParseTreeListener) {
	if listenerT, ok := listener.(AclListener); ok {
		listenerT.ExitPortExpr(s)
	}
}

func (s *PortExprContext) Accept(visitor antlr.ParseTreeVisitor) interface{} {
	switch t := visitor.(type) {
	case AclVisitor:
		return t.VisitPortExpr(s)

	default:
		return t.VisitChildren(s)
	}
}

type ParenExprContext struct {
	AclContext
}

func NewParenExprContext(parser antlr.Parser, ctx antlr.ParserRuleContext) *ParenExprContext {
	var p = new(ParenExprContext)

	InitEmptyAclContext(&p.AclContext)
	p.parser = parser
	p.CopyAll(ctx.(*AclContext))

	return p
}

func (s *ParenExprContext) GetRuleContext() antlr.RuleContext {
	return s
}

func (s *ParenExprContext) LEFT() antlr.TerminalNode {
	return s.GetToken(AclParserLEFT, 0)
}

func (s *ParenExprContext) Acl() IAclContext {
	var t antlr.RuleContext
	for _, ctx := range s.GetChildren() {
		if _, ok := ctx.(IAclContext); ok {
			t = ctx.(antlr.RuleContext)
			break
		}
	}

	if t == nil {
		return nil
	}

	return t.(IAclContext)
}

func (s *ParenExprContext) RIGHT() antlr.TerminalNode {
	return s.GetToken(AclParserRIGHT, 0)
}

func (s *ParenExprContext) EnterRule(listener antlr.ParseTreeListener) {
	if listenerT, ok := listener.(AclListener); ok {
		listenerT.EnterParenExpr(s)
	}
}

func (s *ParenExprContext) ExitRule(listener antlr.ParseTreeListener) {
	if listenerT, ok := listener.(AclListener); ok {
		listenerT.ExitParenExpr(s)
	}
}

func (s *ParenExprContext) Accept(visitor antlr.ParseTreeVisitor) interface{} {
	switch t := visitor.(type) {
	case AclVisitor:
		return t.VisitParenExpr(s)

	default:
		return t.VisitChildren(s)
	}
}

type OrExprContext struct {
	AclContext
}

func NewOrExprContext(parser antlr.Parser, ctx antlr.ParserRuleContext) *OrExprContext {
	var p = new(OrExprContext)

	InitEmptyAclContext(&p.AclContext)
	p.parser = parser
	p.CopyAll(ctx.(*AclContext))

	return p
}

func (s *OrExprContext) GetRuleContext() antlr.RuleContext {
	return s
}

func (s *OrExprContext) AllAcl() []IAclContext {
	children := s.GetChildren()
	len := 0
	for _, ctx := range children {
		if _, ok := ctx.(IAclContext); ok {
			len++
		}
	}

	tst := make([]IAclContext, len)
	i := 0
	for _, ctx := range children {
		if t, ok := ctx.(IAclContext); ok {
			tst[i] = t.(IAclContext)
			i++
		}
	}

	return tst
}

func (s *OrExprContext) Acl(i int) IAclContext {
	var t antlr.RuleContext
	j := 0
	for _, ctx := range s.GetChildren() {
		if _, ok := ctx.(IAclContext); ok {
			if j == i {
				t = ctx.(antlr.RuleContext)
				break
			}
			j++
		}
	}

	if t == nil {
		return nil
	}

	return t.(IAclContext)
}

func (s *OrExprContext) OR() antlr.TerminalNode {
	return s.GetToken(AclParserOR, 0)
}

func (s *OrExprContext) EnterRule(listener antlr.ParseTreeListener) {
	if listenerT, ok := listener.(AclListener); ok {
		listenerT.EnterOrExpr(s)
	}
}

func (s *OrExprContext) ExitRule(listener antlr.ParseTreeListener) {
	if listenerT, ok := listener.(AclListener); ok {
		listenerT.ExitOrExpr(s)
	}
}

func (s *OrExprContext) Accept(visitor antlr.ParseTreeVisitor) interface{} {
	switch t := visitor.(type) {
	case AclVisitor:
		return t.VisitOrExpr(s)

	default:
		return t.VisitChildren(s)
	}
}

func (p *AclParser) Acl() (localctx IAclContext) {
	return p.acl(0)
}

func (p *AclParser) acl(_p int) (localctx IAclContext) {
	var _parentctx antlr.ParserRuleContext = p.GetParserRuleContext()

	_parentState := p.GetState()
	localctx = NewAclContext(p, p.GetParserRuleContext(), _parentState)
	var _prevctx IAclContext = localctx
	var _ antlr.ParserRuleContext = _prevctx // TODO: To prevent unused variable warning.
	_startState := 0
	p.EnterRecursionRule(localctx, 0, AclParserRULE_acl, _p)
	var _alt int

	p.EnterOuterAlt(localctx, 1)
	p.SetState(16)
	p.GetErrorHandler().Sync(p)
	if p.HasError() {
		goto errorExit
	}

	switch p.GetTokenStream().LA(1) {
	case AclParserIPADDRRULE:
		localctx = NewIpExprContext(p, localctx)
		p.SetParserRuleContext(localctx)
		_prevctx = localctx

		{
			p.SetState(3)
			p.Match(AclParserIPADDRRULE)
			if p.HasError() {
				// Recognition error - abort rule
				goto errorExit
			}
		}
		{
			p.SetState(4)
			p.Match(AclParserRELATION)
			if p.HasError() {
				// Recognition error - abort rule
				goto errorExit
			}
		}
		{
			p.SetState(5)
			p.Match(AclParserPREDICATE)
			if p.HasError() {
				// Recognition error - abort rule
				goto errorExit
			}
		}

	case AclParserPORTRULE:
		localctx = NewPortExprContext(p, localctx)
		p.SetParserRuleContext(localctx)
		_prevctx = localctx
		{
			p.SetState(6)
			p.Match(AclParserPORTRULE)
			if p.HasError() {
				// Recognition error - abort rule
				goto errorExit
			}
		}
		{
			p.SetState(7)
			p.Match(AclParserRELATION)
			if p.HasError() {
				// Recognition error - abort rule
				goto errorExit
			}
		}
		{
			p.SetState(8)
			p.Match(AclParserPREDICATE)
			if p.HasError() {
				// Recognition error - abort rule
				goto errorExit
			}
		}

	case AclParserPROTORULE:
		localctx = NewProtoExprContext(p, localctx)
		p.SetParserRuleContext(localctx)
		_prevctx = localctx
		{
			p.SetState(9)
			p.Match(AclParserPROTORULE)
			if p.HasError() {
				// Recognition error - abort rule
				goto errorExit
			}
		}
		{
			p.SetState(10)
			p.Match(AclParserRELATION)
			if p.HasError() {
				// Recognition error - abort rule
				goto errorExit
			}
		}
		{
			p.SetState(11)
			p.Match(AclParserPREDICATE)
			if p.HasError() {
				// Recognition error - abort rule
				goto errorExit
			}
		}

	case AclParserLEFT:
		localctx = NewParenExprContext(p, localctx)
		p.SetParserRuleContext(localctx)
		_prevctx = localctx
		{
			p.SetState(12)
			p.Match(AclParserLEFT)
			if p.HasError() {
				// Recognition error - abort rule
				goto errorExit
			}
		}
		{
			p.SetState(13)
			p.acl(0)
		}
		{
			p.SetState(14)
			p.Match(AclParserRIGHT)
			if p.HasError() {
				// Recognition error - abort rule
				goto errorExit
			}
		}

	default:
		p.SetError(antlr.NewNoViableAltException(p, nil, nil, nil, nil, nil))
		goto errorExit
	}
	p.GetParserRuleContext().SetStop(p.GetTokenStream().LT(-1))
	p.SetState(26)
	p.GetErrorHandler().Sync(p)
	if p.HasError() {
		goto errorExit
	}
	_alt = p.GetInterpreter().AdaptivePredict(p.BaseParser, p.GetTokenStream(), 2, p.GetParserRuleContext())
	if p.HasError() {
		goto errorExit
	}
	for _alt != 2 && _alt != antlr.ATNInvalidAltNumber {
		if _alt == 1 {
			if p.GetParseListeners() != nil {
				p.TriggerExitRuleEvent()
			}
			_prevctx = localctx
			p.SetState(24)
			p.GetErrorHandler().Sync(p)
			if p.HasError() {
				goto errorExit
			}

			switch p.GetInterpreter().AdaptivePredict(p.BaseParser, p.GetTokenStream(), 1, p.GetParserRuleContext()) {
			case 1:
				localctx = NewAndExprContext(p, NewAclContext(p, _parentctx, _parentState))
				p.PushNewRecursionContext(localctx, _startState, AclParserRULE_acl)
				p.SetState(18)

				if !(p.Precpred(p.GetParserRuleContext(), 6)) {
					p.SetError(antlr.NewFailedPredicateException(p, "p.Precpred(p.GetParserRuleContext(), 6)", ""))
					goto errorExit
				}
				{
					p.SetState(19)
					p.Match(AclParserAND)
					if p.HasError() {
						// Recognition error - abort rule
						goto errorExit
					}
				}
				{
					p.SetState(20)
					p.acl(7)
				}

			case 2:
				localctx = NewOrExprContext(p, NewAclContext(p, _parentctx, _parentState))
				p.PushNewRecursionContext(localctx, _startState, AclParserRULE_acl)
				p.SetState(21)

				if !(p.Precpred(p.GetParserRuleContext(), 5)) {
					p.SetError(antlr.NewFailedPredicateException(p, "p.Precpred(p.GetParserRuleContext(), 5)", ""))
					goto errorExit
				}
				{
					p.SetState(22)
					p.Match(AclParserOR)
					if p.HasError() {
						// Recognition error - abort rule
						goto errorExit
					}
				}
				{
					p.SetState(23)
					p.acl(6)
				}

			case antlr.ATNInvalidAltNumber:
				goto errorExit
			}

		}
		p.SetState(28)
		p.GetErrorHandler().Sync(p)
		if p.HasError() {
			goto errorExit
		}
		_alt = p.GetInterpreter().AdaptivePredict(p.BaseParser, p.GetTokenStream(), 2, p.GetParserRuleContext())
		if p.HasError() {
			goto errorExit
		}
	}

errorExit:
	if p.HasError() {
		v := p.GetError()
		localctx.SetException(v)
		p.GetErrorHandler().ReportError(p, v)
		p.GetErrorHandler().Recover(p, v)
		p.SetError(nil)
	}
	p.UnrollRecursionContexts(_parentctx)
	return localctx
}

func (p *AclParser) Sempred(localctx antlr.RuleContext, ruleIndex, predIndex int) bool {
	switch ruleIndex {
	case 0:
		var t *AclContext = nil
		if localctx != nil {
			t = localctx.(*AclContext)
		}
		return p.Acl_Sempred(t, predIndex)

	default:
		panic("No predicate with index: " + fmt.Sprint(ruleIndex))
	}
}

func (p *AclParser) Acl_Sempred(localctx antlr.RuleContext, predIndex int) bool {
	switch predIndex {
	case 0:
		return p.Precpred(p.GetParserRuleContext(), 6)

	case 1:
		return p.Precpred(p.GetParserRuleContext(), 5)

	default:
		panic("No predicate with index: " + fmt.Sprint(predIndex))
	}
}
