package parser

import (
	"fmt"
	"reflect"
	"strconv"
	"strings"

	"github.com/antlr4-go/antlr/v4"
	"github.com/sirupsen/logrus"
)

type VpcAcl struct {
	// Todo
	// SrcIp    string
	// Todo
	// SrcPort  string
	DstIp    string
	DstPort  string
	Protocol string
}

type VpcAcls []VpcAcl

type VpcAclVisitor struct {
	BaseAclVisitor
}

func NewVpcAclVistor() *VpcAclVisitor {

	visitor := &VpcAclVisitor{}
	return visitor
}

func (v *VpcAclVisitor) GetVpcAcl(ovnMatch string) ([]VpcAcl, error) {
	input := antlr.NewInputStream(ovnMatch)
	// Create the lexer
	lexer := NewAclLexer(input)
	// Create the token stream from the lexer
	tokens := antlr.NewCommonTokenStream(lexer, antlr.TokenDefaultChannel)
	// Create the parser
	p := NewAclParser(tokens)
	// Parse the input
	tree := p.Acl()
	visitor := NewVpcAclVistor()
	return visitor.Visit(tree)
}

func (v *VpcAclVisitor) Visit(tree antlr.ParseTree) ([]VpcAcl, error) {
	switch val := tree.(type) {
	case *IpExprContext:
		return v.VisitIpExpr(val)
	case *ProtoExprContext:
		return v.VisitProtoExpr(val)
	case *ParenExprContext:
		return v.VisitParenExpr(val)
	case *OrExprContext:
		return v.VisitOrExpr(val)
	case *AndExprContext:
		return v.VisitAndExpr(val)
	case *PortExprContext:
		return v.VisitPortExpr(val)
	default:
		logrus.Errorf("can't support the expr %v", reflect.TypeOf(val))
		return nil, fmt.Errorf("can't support the expr %v", reflect.TypeOf(val))
	}
}

func (v *VpcAclVisitor) VisitIpExpr(ctx *IpExprContext) ([]VpcAcl, error) {
	logrus.Debugf("VisitIp: %s%s%s", ctx.IPADDRRULE(), ctx.RELATION(), ctx.PREDICATE())
	vpcAcl := VpcAcl{
		DstIp: ctx.PREDICATE().GetText(),
	}
	return []VpcAcl{vpcAcl}, nil
}

func (v *VpcAclVisitor) VisitProtoExpr(ctx *ProtoExprContext) ([]VpcAcl, error) {
	logrus.Debugf("VisitProto: %s%s%s", ctx.PROTORULE(), ctx.RELATION(), ctx.PREDICATE())
	var proto string
	switch ctx.PREDICATE().GetText() {
	case "6":
		proto = "tcp"
	case "17":
		proto = "udp"
	case "1":
		proto = "icmp"
	default:
		logrus.Errorf("unknown protocol %s", ctx.PREDICATE().GetText())
		return nil, fmt.Errorf("unknown protocol with %s", ctx.PREDICATE().GetText())
	}
	vpcAcl := VpcAcl{
		Protocol: proto,
	}

	return []VpcAcl{vpcAcl}, nil
}

func (v *VpcAclVisitor) VisitPortExpr(ctx *PortExprContext) ([]VpcAcl, error) {
	logrus.Debugf("VisitPort: %s%s%s", ctx.PORTRULE(), ctx.RELATION(), ctx.PREDICATE())
	vpcAcl := VpcAcl{}
	if ctx.RELATION().GetText() != "==" {
		vpcAcl.DstPort = ctx.RELATION().GetText() + ctx.PREDICATE().GetText()
	} else {
		vpcAcl.DstPort = ctx.PREDICATE().GetText()
	}

	return []VpcAcl{vpcAcl}, nil
}

func (v *VpcAclVisitor) VisitParenExpr(ctx *ParenExprContext) ([]VpcAcl, error) {
	logrus.Debugf("VisitParen")
	return v.Visit(ctx.Acl())
}

// TODO FIX DATA
func (v *VpcAclVisitor) VisitOrExpr(ctx *OrExprContext) ([]VpcAcl, error) {
	logrus.Debugf("VisitOr")
	lacls, err1 := v.Visit(ctx.Acl(0))
	racls, err2 := v.Visit(ctx.Acl(1))
	if err1 != nil || err2 != nil {
		return nil, fmt.Errorf("visit error: %v, %v", err1, err2)
	}
	acls := make([]VpcAcl, 0)
	for _, lacl := range lacls {
		for _, racl := range racls {
			// Protocol
			if lacl.Protocol != racl.Protocol {
				left := VpcAcl{
					DstIp:    lacl.DstIp,
					DstPort:  lacl.DstPort,
					Protocol: lacl.Protocol,
				}
				acls = append(acls, left)
				right := VpcAcl{
					DstIp:    racl.DstIp,
					DstPort:  racl.DstPort,
					Protocol: racl.Protocol,
				}
				acls = append(acls, right)
			} else {
				acl := VpcAcl{
					Protocol: lacl.Protocol,
				}
				// DstIp
				if lacl.DstIp == "" && racl.DstIp != "" {
					acl.DstIp = racl.DstIp
				}
				if lacl.DstIp != "" && racl.DstIp == "" {
					acl.DstIp = lacl.DstIp
				}
				if lacl.DstIp != "" && racl.DstIp != "" {
					acl.DstIp = lacl.DstIp + "," + racl.DstIp
				}
				// DstPort
				if lacl.DstPort == "" && racl.DstPort != "" {
					acl.DstPort = racl.DstPort
				}
				if lacl.DstPort != "" && racl.DstPort == "" {
					acl.DstPort = lacl.DstPort
				}
				if lacl.DstPort != "" && racl.DstPort != "" {
					acl.DstPort = lacl.DstPort + "," + racl.DstPort
				}
				acls = append(acls, acl)

			}
		}
	}

	return acls, nil
}
func (v *VpcAclVisitor) VisitAndExpr(ctx *AndExprContext) ([]VpcAcl, error) {
	logrus.Debugf("VisitAnd")
	lacls, err1 := v.Visit(ctx.Acl(0))
	racls, err2 := v.Visit(ctx.Acl(1))
	if err1 != nil || err2 != nil {
		return nil, fmt.Errorf("visit error: %v, %v", err1, err2)
	}
	acls := make([]VpcAcl, 0)
	for _, lacl := range lacls {
		for _, racl := range racls {
			acl := VpcAcl{}
			// DstIp
			if lacl.DstIp == "" && racl.DstIp != "" {
				acl.DstIp = racl.DstIp
			}
			if lacl.DstIp != "" && racl.DstIp == "" {
				acl.DstIp = lacl.DstIp
			}
			if lacl.DstIp != "" && racl.DstIp != "" {
				logrus.Errorf("Dst Ip addr: %s %s conflict", lacl.DstIp, racl.DstIp)
				return nil, fmt.Errorf("dst Ip addr: %s %s conflict", lacl.DstIp, racl.DstIp)
			}
			// DstPort
			if lacl.DstPort == "" && racl.DstPort != "" {
				acl.DstPort = racl.DstPort
			}
			if lacl.DstPort != "" && racl.DstPort == "" {
				acl.DstPort = lacl.DstPort
			}
			if lacl.DstPort != "" && racl.DstPort != "" {
				portRange, err := v.PortInRange(lacl.DstPort, racl.DstPort)
				if err != nil {
					logrus.Errorf("Dst Port : %s %s range err", lacl.DstPort, racl.DstPort)
					return nil, fmt.Errorf("dst Port : %s %s range err", lacl.DstPort, racl.DstPort)
				}
				if portRange != "" {
					acl.DstPort = portRange
				} else {
					logrus.Warnf("dst Port : %s %s conflict", lacl.DstPort, racl.DstPort)
					continue
				}
			}
			// Protocol
			if lacl.Protocol == "" && racl.Protocol != "" {
				acl.Protocol = racl.Protocol
			}
			if lacl.Protocol != "" && racl.Protocol == "" {
				acl.Protocol = lacl.Protocol
			}
			if lacl.Protocol != "" && racl.Protocol != "" {
				logrus.Warnf("Dst Port : %s %s conflict", lacl.Protocol, racl.Protocol)
				continue
			}
			acls = append(acls, acl)
		}
	}

	return acls, nil
}

func (v *VpcAclVisitor) PortInRange(lport, rport string) (string, error) {
	left, right := 0, 0
	var err error
	if strings.HasPrefix(lport, "<=") {
		right, err = strconv.Atoi(strings.TrimLeft(lport, "<="))
		if err != nil {
			logrus.Errorf("error parser port %s, err %v", lport, err)
			return "", err
		}
	} else if strings.HasPrefix(lport, ">=") {
		left, err = strconv.Atoi(strings.TrimLeft(lport, "=>"))
		if err != nil {
			logrus.Errorf("error parser port %s, err %v", lport, err)
			return "", err
		}
	}

	if strings.HasPrefix(rport, "<=") && right == 0 {
		right, err = strconv.Atoi(strings.TrimLeft(rport, "<="))
		if err != nil {
			logrus.Errorf("error parser port %s, err %v", rport, err)
			return "", err
		}
	} else if strings.HasPrefix(rport, ">=") && left == 0 {
		left, err = strconv.Atoi(strings.TrimLeft(rport, "=>"))
		if err != nil {
			logrus.Errorf("error parser port %s, err %v", rport, err)
			return "", err
		}
	}

	if left < right {
		return fmt.Sprintf("%d:%d", left, right), nil
	}

	return "", nil

}
