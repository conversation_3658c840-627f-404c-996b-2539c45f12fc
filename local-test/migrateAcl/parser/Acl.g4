grammar Acl;

acl                
 : acl AND acl     # AndExpr 
 | acl OR acl      # OrExpr 
 | IPADDRRULE RELATION PREDICATE          # IpExpr
 | PORTRULE RELATION PREDICATE            # PortExpr
 | PROTORULE RELATION PREDICATE           # ProtoExpr
 | LEFT acl RIGHT  # ParenExpr
 ;


IPADDRRULE:    'ip4.dst';
PORTRULE:      'udp.dst'|'tcp.dst';
PROTORULE:     'ip.proto';
AND:           '&&';
OR:            '||';
LEFT:           '(';
RIGHT:          ')';
RELATION:       '=='|'>='|'<=';
PREDICATE:      ([0-9]|'.'|'/')+  ;
WS:             [ \t\n\r\f] -> skip  ;