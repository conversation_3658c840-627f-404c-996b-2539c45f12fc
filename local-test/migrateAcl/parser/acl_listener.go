// Code generated from local-test/acl/parser/Acl.g4 by ANTLR 4.13.0. DO NOT EDIT.

package parser // Acl

import "github.com/antlr4-go/antlr/v4"

// AclListener is a complete listener for a parse tree produced by AclParser.
type AclListener interface {
	antlr.ParseTreeListener

	// EnterAndExpr is called when entering the AndExpr production.
	EnterAndExpr(c *AndExprContext)

	// EnterIpExpr is called when entering the IpExpr production.
	EnterIpExpr(c *IpExprContext)

	// EnterProtoExpr is called when entering the ProtoExpr production.
	EnterProtoExpr(c *ProtoExprContext)

	// EnterPortExpr is called when entering the PortExpr production.
	EnterPortExpr(c *PortExprContext)

	// EnterParenExpr is called when entering the ParenExpr production.
	EnterParenExpr(c *ParenExprContext)

	// EnterOrExpr is called when entering the OrExpr production.
	EnterOrExpr(c *OrExprContext)

	// ExitAndExpr is called when exiting the AndExpr production.
	ExitAndExpr(c *AndExprContext)

	// ExitIpExpr is called when exiting the IpExpr production.
	ExitIpExpr(c *IpExprContext)

	// ExitProtoExpr is called when exiting the ProtoExpr production.
	ExitProtoExpr(c *ProtoExprContext)

	// ExitPortExpr is called when exiting the PortExpr production.
	ExitPortExpr(c *PortExprContext)

	// ExitParenExpr is called when exiting the ParenExpr production.
	ExitParenExpr(c *ParenExprContext)

	// ExitOrExpr is called when exiting the OrExpr production.
	ExitOrExpr(c *OrExprContext)
}
