// Code generated from local-test/acl/parser/Acl.g4 by ANTLR 4.13.0. DO NOT EDIT.

package parser // Acl

import "github.com/antlr4-go/antlr/v4"

// BaseAclListener is a complete listener for a parse tree produced by AclParser.
type BaseAclListener struct{}

var _ AclListener = &BaseAclListener{}

// VisitTerminal is called when a terminal node is visited.
func (s *BaseAclListener) VisitTerminal(node antlr.TerminalNode) {}

// VisitErrorNode is called when an error node is visited.
func (s *BaseAclListener) VisitErrorNode(node antlr.ErrorNode) {}

// EnterEveryRule is called when any rule is entered.
func (s *BaseAclListener) EnterEveryRule(ctx antlr.ParserRuleContext) {}

// ExitEveryRule is called when any rule is exited.
func (s *BaseAclListener) ExitEveryRule(ctx antlr.ParserRuleContext) {}

// EnterAndExpr is called when production AndExpr is entered.
func (s *BaseAclListener) EnterAndExpr(ctx *AndExprContext) {}

// ExitAndExpr is called when production AndExpr is exited.
func (s *BaseAclListener) ExitAndExpr(ctx *AndExprContext) {}

// EnterIpExpr is called when production IpExpr is entered.
func (s *BaseAclListener) EnterIpExpr(ctx *IpExprContext) {}

// ExitIpExpr is called when production IpExpr is exited.
func (s *BaseAclListener) ExitIpExpr(ctx *IpExprContext) {}

// EnterProtoExpr is called when production ProtoExpr is entered.
func (s *BaseAclListener) EnterProtoExpr(ctx *ProtoExprContext) {}

// ExitProtoExpr is called when production ProtoExpr is exited.
func (s *BaseAclListener) ExitProtoExpr(ctx *ProtoExprContext) {}

// EnterPortExpr is called when production PortExpr is entered.
func (s *BaseAclListener) EnterPortExpr(ctx *PortExprContext) {}

// ExitPortExpr is called when production PortExpr is exited.
func (s *BaseAclListener) ExitPortExpr(ctx *PortExprContext) {}

// EnterParenExpr is called when production ParenExpr is entered.
func (s *BaseAclListener) EnterParenExpr(ctx *ParenExprContext) {}

// ExitParenExpr is called when production ParenExpr is exited.
func (s *BaseAclListener) ExitParenExpr(ctx *ParenExprContext) {}

// EnterOrExpr is called when production OrExpr is entered.
func (s *BaseAclListener) EnterOrExpr(ctx *OrExprContext) {}

// ExitOrExpr is called when production OrExpr is exited.
func (s *BaseAclListener) ExitOrExpr(ctx *OrExprContext) {}
