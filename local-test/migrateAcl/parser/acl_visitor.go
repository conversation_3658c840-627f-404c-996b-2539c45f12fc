// Code generated from local-test/acl/parser/Acl.g4 by ANTLR 4.13.0. DO NOT EDIT.

package parser // Acl

import "github.com/antlr4-go/antlr/v4"

// A complete Visitor for a parse tree produced by AclParser.
type AclVisitor interface {
	antlr.ParseTreeVisitor

	// Visit a parse tree produced by AclParser#AndExpr.
	VisitAndExpr(ctx *AndExprContext) interface{}

	// Visit a parse tree produced by AclParser#IpExpr.
	VisitIpExpr(ctx *IpExprContext) interface{}

	// Visit a parse tree produced by AclParser#ProtoExpr.
	VisitProtoExpr(ctx *ProtoExprContext) interface{}

	// Visit a parse tree produced by AclParser#PortExpr.
	VisitPortExpr(ctx *PortExprContext) interface{}

	// Visit a parse tree produced by AclParser#ParenExpr.
	VisitParenExpr(ctx *ParenExprContext) interface{}

	// Visit a parse tree produced by AclParser#OrExpr.
	VisitOrExpr(ctx *OrExprContext) interface{}
}
