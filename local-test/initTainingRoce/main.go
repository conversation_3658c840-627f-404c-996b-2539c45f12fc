package main

import (
	"context"
	"fmt"
	"strings"

	nadv1 "github.com/k8snetworkplumbingwg/network-attachment-definition-client/pkg/apis/k8s.cni.cncf.io/v1"
	ovnv1 "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"github.com/sirupsen/logrus"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"

	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

const NADNamespace = "plat-boson-service"
const NADConfig = `{
      "cniVersion": "0.3.1",
      "plugins": [
      {
        "type": "sriov",
        "ipam": {
          "type": "kube-ovn",
          "server_socket": "/run/openvswitch/kube-ovn-daemon.sock",
          "provider": "<nad_name>.plat-boson-service"
        }
      },
      {
        "type" : "route-override",
        "flushroutes" : true,
        "delroutes": [
        {
            "dst": "0.0.0.0/0",
            "gw": "<gw>"
        }]
      }
      ]
    }`

const ResourceName = "rdma-training/roce"
const ResourceName1 = "rdma-training/roce_1"

func init() {
	rp.BosonProvider = rp.NewResourceProvider()
}

// ************/19，网关：************，IP Range：************0 ~ **************
func initTrainingVlanCIDRs(cidr, gw, rev string) {

	cidrsData := []db.CidrPools{}

	err := rp.BosonProvider.Engine.Where("scope = ? and network_type = ? and gateway_ip = ?", "TRAINING", "VLAN", gw).Find(&cidrsData)
	if err != nil {
		logrus.Errorln(err)
		return
	}

	if len(cidrsData) > 0 {
		logrus.Println("Already Inited.")
		return
	}

	cidrsData = make([]db.CidrPools, 128)

	for i := 1500; i <= 1627; i++ {
		cidrsData[i-1500] = db.CidrPools{
			CIDRPoolID:  utils.UUIDGen(),
			CIDR:        cidr,
			GatewayIP:   gw,
			Scope:       "TRAINING",
			NetworkType: "VLAN",
			VNI:         i,
			Allocated:   false,
			Reserved:    rev,
			ZonePrp:     "cn-sh-01a",
		}
	}

	_, err = rp.BosonProvider.Engine.Insert(&cidrsData)
	if err != nil {
		logrus.Errorln(err)
		return
	}

	logrus.Println("Done.")

}

func recoverSNTAndNAD(gw, gw1 string) {

	subentsData := []db.Subnets{}

	err := rp.BosonProvider.Engine.Where("scope = ? and network_type = ? and gateway_ip = ?", "TRAINING", "VLAN", gw).Find(&subentsData)
	if err != nil {
		logrus.Errorln(err)
		return
	}

	if len(subentsData) > 0 {
		logrus.Println("Already Recovered.")
		return
	}

	vpcsData := []db.Vpcs{}
	err = rp.BosonProvider.Engine.Where("deleted = ?", false).Find(&vpcsData)
	if err != nil {
		logrus.Errorln(err)
		return
	}

	for _, vpcData := range vpcsData {
		vpc, err := rp.BosonProvider.KubeovnClient.Vpcs().Get(context.Background(), vpcData.Name, metav1.GetOptions{})
		if err != nil {
			if errors.IsNotFound(err) {
				logrus.Errorln(err)
				continue
			}
			logrus.Errorln(err)
			return
		}

		cidrData := db.CidrPools{}
		cidrData1 := db.CidrPools{}

		has, err := rp.BosonProvider.Engine.Where("scope = ? and network_type = ? and allocated = ? and gateway_ip = ?", "TRAINING", "VLAN", false, gw).Get(&cidrData)
		if err != nil {
			logrus.Errorln(err)
			return
		}
		if !has {
			reason := "no allocatable cidr found."
			logrus.Errorln(reason)
			return
		}

		has, err = rp.BosonProvider.Engine.Where("scope = ? and network_type = ? and allocated = ? and gateway_ip = ? and vni = ?", "TRAINING", "VLAN", false, gw1, cidrData.VNI).Get(&cidrData1)
		if err != nil {
			logrus.Errorln(err)
			return
		}
		if !has {
			reason := "no allocatable cidr1 found."
			logrus.Errorln(reason)
			return
		}

		rand8 := utils.Rand8()
		subnetName := fmt.Sprintf("snt-%s-%s", utils.NameToTenantCode(vpc.Name), rand8)
		nadName := fmt.Sprintf("nad-%s-%s", utils.NameToTenantCode(vpc.Name), rand8)
		subnetID := utils.UUIDGen()

		_, err = rp.BosonProvider.Engine.Exec("update cidr_pools set allocated = ? where cidr_pool_id = ?", true, cidrData.CIDRPoolID)
		if err != nil {
			logrus.Errorln(err)
			return
		}

		nad := &nadv1.NetworkAttachmentDefinition{
			TypeMeta: metav1.TypeMeta{
				Kind:       "NetworkAttachmentDefinition",
				APIVersion: nadv1.SchemeGroupVersion.String(),
			},
			ObjectMeta: metav1.ObjectMeta{
				Name:      nadName,
				Namespace: NADNamespace,
				Annotations: map[string]string{
					"k8s.v1.cni.cncf.io/resourceName": ResourceName,
				},
				OwnerReferences: []metav1.OwnerReference{
					*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
						Group:   ovnv1.SchemeGroupVersion.Group,
						Version: ovnv1.SchemeGroupVersion.Version,
						Kind:    rp.VPCKind,
					}),
				},
			},
			Spec: nadv1.NetworkAttachmentDefinitionSpec{
				Config: strings.ReplaceAll(strings.ReplaceAll(NADConfig, "<nad_name>", nadName), "<gw>", gw),
			},
		}

		subnet := &ovnv1.Subnet{
			TypeMeta: metav1.TypeMeta{
				Kind:       rp.SubnetKind,
				APIVersion: ovnv1.SchemeGroupVersion.String(),
			},
			ObjectMeta: metav1.ObjectMeta{
				Name: subnetName,
				OwnerReferences: []metav1.OwnerReference{
					*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
						Group:   ovnv1.SchemeGroupVersion.Group,
						Version: ovnv1.SchemeGroupVersion.Version,
						Kind:    rp.VPCKind,
					}),
				},
				Labels: map[string]string{
					"creator":                     "boson-provider",
					"kubernetes.io/metadata.name": subnetName,
					"subnet_id":                   subnetID,
					"subnet_scope":                cidrData.Scope,
					"subnet_type":                 cidrData.NetworkType,
					"tenant_id":                   vpcData.TenantID,
					"vni":                         fmt.Sprintf("%v", cidrData.VNI),
					"vpc_id":                      vpcData.VPCID,
					"vpc_name":                    vpc.Name,
				},
				Annotations: map[string]string{
					"k8s.v1.cni.cncf.io/networks":     fmt.Sprintf("%s/%s", NADNamespace, nadName),
					"k8s.v1.cni.cncf.io/resourceName": ResourceName,
				},
			},
			Spec: ovnv1.SubnetSpec{
				Vpc:         vpc.Name,
				CIDRBlock:   cidrData.CIDR,
				Gateway:     cidrData.GatewayIP,
				NatOutgoing: false,
				Default:     false,
				ExcludeIps:  strings.Split(cidrData.Reserved, ","),
				Provider:    fmt.Sprintf("%s.%s", nadName, NADNamespace),
			},
		}

		_, err = rp.BosonProvider.NADClient.NetworkAttachmentDefinitions(NADNamespace).Create(context.Background(), nad, metav1.CreateOptions{})
		if err != nil {
			logrus.Errorln(err)
			return
		}

		_, err = rp.BosonProvider.KubeovnClient.Subnets().Create(context.Background(), subnet, metav1.CreateOptions{})
		if err != nil {
			logrus.Errorln(err)
			return
		}

		subnetData := db.Subnets{
			SubnetID:     subnetID,
			VPCID:        vpcData.VPCID,
			DisplayName:  subnetName,
			CIDR:         cidrData.CIDR,
			IsDefault:    false,
			GatewayIP:    cidrData.GatewayIP,
			Scope:        cidrData.Scope,
			Provider:     "OVN",
			NetworkType:  cidrData.NetworkType,
			ID:           vpcData.ID,
			Name:         subnetName,
			ResourceType: "network.vpc.v1.subnet",
			Zone:         vpcData.Zone,
			State:        network.Subnet_State_name[int32(network.Subnet_ACTIVE)],
			CreatorID:    vpcData.CreatorID,
			OwnerID:      vpcData.OwnerID,
			TenantID:     vpcData.TenantID,
			CreateTime:   vpcData.CreateTime,
			UpdateTime:   vpcData.UpdateTime,
			Deleted:      false,
			CIDRPoolID:   cidrData.CIDRPoolID,
			VNI:          cidrData.VNI,
		}

		_, err = rp.BosonProvider.Engine.Insert(&subnetData)
		if err != nil {
			logrus.Errorln(err)
			return
		}

		rand8 = utils.Rand8()
		subnetName = fmt.Sprintf("snt-%s-%s", utils.NameToTenantCode(vpc.Name), rand8)
		nadName = fmt.Sprintf("nad-%s-%s", utils.NameToTenantCode(vpc.Name), rand8)
		subnetID = utils.UUIDGen()

		_, err = rp.BosonProvider.Engine.Exec("update cidr_pools set allocated = ? where cidr_pool_id = ?", true, cidrData1.CIDRPoolID)
		if err != nil {
			logrus.Errorln(err)
			return
		}

		nad = &nadv1.NetworkAttachmentDefinition{
			TypeMeta: metav1.TypeMeta{
				Kind:       "NetworkAttachmentDefinition",
				APIVersion: nadv1.SchemeGroupVersion.String(),
			},
			ObjectMeta: metav1.ObjectMeta{
				Name:      nadName,
				Namespace: NADNamespace,
				Annotations: map[string]string{
					"k8s.v1.cni.cncf.io/resourceName": ResourceName1,
				},
				OwnerReferences: []metav1.OwnerReference{
					*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
						Group:   ovnv1.SchemeGroupVersion.Group,
						Version: ovnv1.SchemeGroupVersion.Version,
						Kind:    rp.VPCKind,
					}),
				},
			},
			Spec: nadv1.NetworkAttachmentDefinitionSpec{
				Config: strings.ReplaceAll(strings.ReplaceAll(NADConfig, "<nad_name>", nadName), "<gw>", gw1),
			},
		}

		subnet = &ovnv1.Subnet{
			TypeMeta: metav1.TypeMeta{
				Kind:       rp.SubnetKind,
				APIVersion: ovnv1.SchemeGroupVersion.String(),
			},
			ObjectMeta: metav1.ObjectMeta{
				Name: subnetName,
				OwnerReferences: []metav1.OwnerReference{
					*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
						Group:   ovnv1.SchemeGroupVersion.Group,
						Version: ovnv1.SchemeGroupVersion.Version,
						Kind:    rp.VPCKind,
					}),
				},
				Labels: map[string]string{
					"creator":                     "boson-provider",
					"kubernetes.io/metadata.name": subnetName,
					"subnet_id":                   subnetID,
					"subnet_scope":                cidrData1.Scope,
					"subnet_type":                 cidrData1.NetworkType,
					"tenant_id":                   vpcData.TenantID,
					"vni":                         fmt.Sprintf("%v", cidrData1.VNI),
					"vpc_id":                      vpcData.VPCID,
					"vpc_name":                    vpc.Name,
				},
				Annotations: map[string]string{
					"k8s.v1.cni.cncf.io/networks":     fmt.Sprintf("%s/%s", NADNamespace, nadName),
					"k8s.v1.cni.cncf.io/resourceName": ResourceName1,
				},
			},
			Spec: ovnv1.SubnetSpec{
				Vpc:         vpc.Name,
				CIDRBlock:   cidrData1.CIDR,
				Gateway:     cidrData1.GatewayIP,
				NatOutgoing: false,
				Default:     false,
				ExcludeIps:  strings.Split(cidrData1.Reserved, ","),
				Provider:    fmt.Sprintf("%s.%s", nadName, NADNamespace),
			},
		}

		_, err = rp.BosonProvider.NADClient.NetworkAttachmentDefinitions(NADNamespace).Create(context.Background(), nad, metav1.CreateOptions{})
		if err != nil {
			logrus.Errorln(err)
			return
		}

		_, err = rp.BosonProvider.KubeovnClient.Subnets().Create(context.Background(), subnet, metav1.CreateOptions{})
		if err != nil {
			logrus.Errorln(err)
			return
		}

		subnetData = db.Subnets{
			SubnetID:     subnetID,
			VPCID:        vpcData.VPCID,
			DisplayName:  subnetName,
			CIDR:         cidrData1.CIDR,
			IsDefault:    false,
			GatewayIP:    cidrData1.GatewayIP,
			Scope:        cidrData1.Scope,
			Provider:     "OVN",
			NetworkType:  cidrData1.NetworkType,
			ID:           vpcData.ID,
			Name:         subnetName,
			ResourceType: "network.vpc.v1.subnet",
			Zone:         vpcData.Zone,
			State:        network.Subnet_State_name[int32(network.Subnet_ACTIVE)],
			CreatorID:    vpcData.CreatorID,
			OwnerID:      vpcData.OwnerID,
			TenantID:     vpcData.TenantID,
			CreateTime:   vpcData.CreateTime,
			UpdateTime:   vpcData.UpdateTime,
			Deleted:      false,
			CIDRPoolID:   cidrData1.CIDRPoolID,
			VNI:          cidrData1.VNI,
		}

		_, err = rp.BosonProvider.Engine.Insert(&subnetData)
		if err != nil {
			logrus.Errorln(err)
			return
		}
	}

	logrus.Println("Done.")
}

func main() {
	initTrainingVlanCIDRs("************/20", "************", "************..************,**************..**************")
	initTrainingVlanCIDRs("************/20", "************", "************..************,**************..**************")
	recoverSNTAndNAD("************", "************")
}
