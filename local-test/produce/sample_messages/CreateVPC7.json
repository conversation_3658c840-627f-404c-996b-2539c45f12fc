{"package": "sensetime.core.network.v1", "service": "VPCs", "method": "CreateVPC", "action": "Create", "tenant_code": "pgtest111", "callback_data": {"order_id": "e41ccf4d-df03-49c5-9f8b-f028cd8a0354"}, "data": {"subscription_name": "sub-b1", "resource_group_name": "rg-b1", "zone": "cn-sh-01e", "vpc_name": "vpc-pgtest111-718b0001", "vpc": {"id": "subscriptions/sub-b1/resourceGroups/rg-b1/zones/az01/vpcs/vpc-pgtest111-718b0001", "name": "vpc-pgtest111-718b0001", "uid": "6801400f-9055-4183-aa7b-4cef9ced50b8", "resource_type": "network.vpc.v1.vpc", "creator_id": "b1", "owner_id": "b1", "tenant_id": "b1", "zone": "cn-sh-01e", "display_name": "vpc-pgtest111-718b0001", "description": "The hanatgw test vpc", "properties": {"subnet_type": 1}, "create_time": {"seconds": 1659079046, "nanos": 523027000}, "update_time": {"seconds": 1659079046, "nanos": 523027000}}}}