{"package": "sensetime.core.network.v1", "service": "VPCs", "method": "CreateVPC", "action": "Create", "tenant_code": "hanatgw", "callback_data": {"order_id": "47499ea0-ad5c-4b5d-8266-2344ed056ab6"}, "data": {"subscription_name": "sub-b1", "resource_group_name": "rg-b1", "zone": "cn-sh-01z", "vpc_name": "vpc-hanatgw-718b0001", "vpc": {"id": "subscriptions/sub-b1/resourceGroups/rg-b1/zones/az01/vpcs/vpc-8624ed056aba44-718b0001", "name": "vpc-hanatgw-718b0001", "uid": "718b0001-dd32-4af6-a81b-9bb99e9f6d61", "resource_type": "network.vpc.v1.vpc", "creator_id": "b1", "owner_id": "b1", "tenant_id": "b1", "zone": "cn-sh-01z", "display_name": "vpc-hanatgw-test", "description": "The hanatgw test vpc", "properties": {}, "create_time": {"seconds": 1659079046, "nanos": 523027000}, "update_time": {"seconds": 1659079046, "nanos": 523027000}}}}