{"package": "sensetime.core.network.v1", "service": "VPCs", "method": "CreateVPC", "action": "Create", "tenant_code": "hanatgw", "callback_data": {"order_id": "47499ea0-ad5c-4b5d-8266-2344ed056ab6"}, "data": {"subscription_name": "sub-b1", "resource_group_name": "rg-b1", "zone": "cn-sh-01e", "vpc_name": "vpc-pgtest-718b0001", "vpc": {"id": "subscriptions/sub-b1/resourceGroups/rg-b1/zones/az01/vpcs/vpc-pgtest-718b0001", "name": "vpc-pgtest-718b0001", "uid": "718b0001-dd32-4af6-a81b-9bb99e9f6d61", "resource_type": "network.vpc.v1.vpc", "creator_id": "b1", "owner_id": "b1", "tenant_id": "b1", "zone": "cn-sh-01e", "display_name": "vpc-pgtest-718b0001", "description": "The hanatgw test vpc", "properties": {"subnet_type": 1}, "create_time": {"seconds": 1659079046, "nanos": 523027000}, "update_time": {"seconds": 1659079046, "nanos": 523027000}}}}