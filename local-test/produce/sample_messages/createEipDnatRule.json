{"package": "sensetime.core.network.v1", "service": "DNATRules", "method": "CreateDNATRule", "action": "Create", "tenant_code": "8624ed056aba44", "callback_data": {"order_id": "47499ea0-ad5c-4b5d-8266-2344ed056ab6"}, "data": {"subscription_name": "0b922adb-e224-43ea-8faf-908e5adf1519", "resource_group_name": "default", "zone": "sh-01", "dnat_rule_name": "dnat-8624ed056aba44-00000060", "eip_name": "eip-9994ed116aba22-a3059e99", "dnat_rule": {"id": "subscriptions/0b922adb-e224-43ea-8faf-908e5adf1519/resourceGroups/default/zones/sh-01/eips/eip-9994ed116aba22-a3059e99/dnatRules/dnat-8624ed056aba44-00000060", "name": "dnat-8624ed056aba44-00000060", "uid": "718b9360-dd32-4af6-a81b-00000060", "resource_type": "network.eip.v1.dnatrule", "creator_id": "b1", "owner_id": "b1", "tenant_id": "b1", "zone": "az01", "display_name": "eip-rule-b1-test60", "description": "The first test eip dnat rule", "properties": {"nat_gateway_id": "5e23a38d-e712-4812-81f6-21696628eabd", "eip_id": "a3059e99-1155-45b5-9392-05f3b2498299", "external_ip": "*************", "external_port": "80", "protocol": "tcp", "priority": 50}}}}