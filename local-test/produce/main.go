package main

import (
	"context"
	"encoding/json"
	"fmt"
	"io/ioutil"
	"os"
	"strconv"
	"sync"
	"sync/atomic"
	"time"

	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/apache/rocketmq-client-go/v2/producer"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/types/known/timestamppb"

	gwv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/natGateway/v1"
	vpcv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
)

var rpConfig *config.Config

func init() {
	rpConfig = config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))
}

// roundRobinQueueSelector choose the queue by roundRobin.
type roundRobinQueueSelector struct {
	sync.Locker
	indexer map[string]*int32
}

func NewRoundRobinQueueSelector() producer.QueueSelector {
	s := &roundRobinQueueSelector{
		Locker:  new(sync.Mutex),
		indexer: map[string]*int32{},
	}
	return s
}

func (r *roundRobinQueueSelector) Select(message *primitive.Message, queues []*primitive.MessageQueue, lastBrokerName string) *primitive.MessageQueue {
	t := message.Topic
	logrus.Infof("---- in selector message topic is %s", t)
	if _, exist := r.indexer[t]; !exist {
		r.Lock()
		if _, exist := r.indexer[t]; !exist {
			var v = int32(0)
			r.indexer[t] = &v
		}
		r.Unlock()
	}
	index := r.indexer[t]

	i := atomic.AddInt32(index, 1)
	if i < 0 {
		i = -i
		atomic.StoreInt32(index, 0)
	}
	qIndex := int(i) % len(queues)
	logrus.Infof("---- in selector qIndex is %d, queues length is %d", qIndex, len(queues))
	return queues[qIndex]
}

var createVPCMessage1 = &types.RMBody{
	Package: "sensetime.core.network.v1",
	Service: "VPCs",
	Method:  "CreateVPC",
	Action:  "Create",
	CallbackData: types.CallbackData{
		OrderID: "47499ea0-ad5c-4b5d-8266-2344ed056ab6",
	},
	Data: vpcv1.CreateVPCRequest{
		SubscriptionName:  "sub-b1",
		ResourceGroupName: "rg-b1",
		Zone:              "zone-b1",
		VpcName:           "vpc-b1-718b9360",
		Vpc: &vpcv1.VPC{
			Id:           "subscriptions/sub-b1/resourceGroups/rg-b1/zones/zone-b1/vpcs/vpc-b1-718b9360",
			Name:         "vpc-b1-718b9360",
			Uid:          "718b9360-dd32-4af6-a81b-9bb99e9f6d60",
			ResourceType: "network.v1.vpc",
			CreatorId:    "b1",
			OwnerId:      "b1",
			TenantId:     "b1",
			Zone:         "zone-b1",
			DisplayName:  "vpc-b1-test",
			Description:  "The first test vpc",
			Properties:   &vpcv1.VPCProperties{},
			CreateTime:   timestamppb.New(time.Now()),
			UpdateTime:   timestamppb.New(time.Now()),
		},
	},
}

var deleteVPCMessage1 = &types.RMBody{
	Package: "sensetime.core.network.v1",
	Service: "VPCs",
	Method:  "DeleteVPC",
	Action:  "Delete",
	CallbackData: types.CallbackData{
		OrderID: "ac37dc72-08a8-46b7-b8d5-5c8428ab515d",
	},
	Data: vpcv1.DeleteVPCRequest{
		SubscriptionName:  "sub-b1",
		ResourceGroupName: "rg-b1",
		Zone:              "zone-b1",
		VpcName:           "vpc-b1-718b9360",
	},
}

var createVPCMessage2 = &types.RMBody{
	Package: "sensetime.core.network.v1",
	Service: "VPCs",
	Method:  "CreateVPC",
	Action:  "Create",
	CallbackData: types.CallbackData{
		OrderID: "1354c6d9-4c83-4135-b17f-343f3e7e68ce",
	},
	Data: vpcv1.CreateVPCRequest{
		SubscriptionName:  "sub-b1",
		ResourceGroupName: "rg-b1",
		Zone:              "zone-b1",
		VpcName:           "vpc-b2",
		Vpc: &vpcv1.VPC{
			Id:           "subscriptions/sub-b1/resourceGroups/rg-b1/zones/zone-b1/vpcs/vpc-b2",
			Name:         "vpc-b2",
			Uid:          "5f547276-f4e9-4a10-9036-f45259845bfe",
			ResourceType: "network.v1.vpc",
			CreatorId:    "b2",
			OwnerId:      "b2",
			TenantId:     "b2",
			Zone:         "zone-b1",
			DisplayName:  "vpc-b2-test",
			Description:  "The second test vpc",
			Properties:   &vpcv1.VPCProperties{},
			CreateTime:   timestamppb.New(time.Now()),
			UpdateTime:   timestamppb.New(time.Now()),
		},
	},
}

var deleteVPCMessage2 = &types.RMBody{
	Package: "sensetime.core.network.v1",
	Service: "VPCs",
	Method:  "DeleteVPC",
	Action:  "Delete",
	CallbackData: types.CallbackData{
		OrderID: "7570e235-c6dd-40d0-a880-756c78879f68",
	},
	Data: vpcv1.DeleteVPCRequest{
		SubscriptionName:  "sub-b1",
		ResourceGroupName: "rg-b1",
		Zone:              "zone-b1",
		VpcName:           "vpc-b2",
	},
}

var createSubnetMessage1 = &types.RMBody{
	Package: "sensetime.core.network.v1",
	Service: "Subnets",
	Method:  "CreateSubnet",
	Action:  "Create",
	CallbackData: types.CallbackData{
		OrderID: "a3059ed1-1155-45b5-9392-05f3b2498258",
	},
	Data: vpcv1.CreateSubnetRequest{
		SubscriptionName:  "sub-b1",
		ResourceGroupName: "rg-b1",
		Zone:              "zone-b1",
		SubnetName:        "subnet-b1",
		Subnet: &vpcv1.Subnet{
			Id:           "subscriptions/sub-b1/resourceGroups/rg-b1/zones/zone-b1/subnets/subnet-b1",
			Name:         "subnet-b1",
			Uid:          "79a959e9-fa14-4dda-82bc-12fb8d727a9b",
			ResourceType: "network.v1.subnet",
			CreatorId:    "b1",
			OwnerId:      "b1",
			TenantId:     "b1",
			Zone:         "zone-b1",
			DisplayName:  "subnet-b1-test",
			Description:  "The first subnet",
			Properties: &vpcv1.SubnetProperties{
				Scope:       vpcv1.SubnetProperties_VPCGW,
				Provider:    vpcv1.SubnetProperties_OVN,
				NetworkType: vpcv1.SubnetProperties_FLAT,
				VpcId:       "718b9360-dd32-4af6-a81b-9bb99e9f6d60",
				Cidr:        "***********/20",
				GatewayIp:   "***********",
				IsDefault:   false,
			},
			CreateTime: timestamppb.New(time.Now()),
			UpdateTime: timestamppb.New(time.Now()),
		},
	},
}

var createSubnetMessage2 = &types.RMBody{
	Package: "sensetime.core.network.v1",
	Service: "Subnets",
	Method:  "CreateSubnet",
	Action:  "Create",
	CallbackData: types.CallbackData{
		OrderID: "a3059ed1-1155-45b5-9392-05f3b2498258",
	},
	Data: vpcv1.CreateSubnetRequest{
		SubscriptionName:  "sub-b1",
		ResourceGroupName: "rg-b1",
		Zone:              "zone-b1",
		SubnetName:        "sn-b1-79a959e9",
		Subnet: &vpcv1.Subnet{
			Id:           "subscriptions/sub-b1/resourceGroups/rg-b1/zones/zone-b1/subnets/sn-b1-79a959e9",
			Name:         "sn-b1-79a959e9",
			Uid:          "79a959e9-fa14-4dda-82bc-12fb8d727a9b",
			ResourceType: "network.v1.subnet",
			CreatorId:    "b1",
			OwnerId:      "b1",
			TenantId:     "b1",
			Zone:         "zone-b1",
			DisplayName:  "subnet-b1-test",
			Description:  "The first subnet",
			Properties: &vpcv1.SubnetProperties{
				Scope:       vpcv1.SubnetProperties_VPCGW,
				Provider:    vpcv1.SubnetProperties_OVN,
				NetworkType: vpcv1.SubnetProperties_FLAT,
				VpcId:       "718b9360-dd32-4af6-a81b-9bb99e9f6d60",
				Cidr:        "***********/20",
				GatewayIp:   "***********",
				IsDefault:   false,
			},
			CreateTime: timestamppb.New(time.Now()),
			UpdateTime: timestamppb.New(time.Now()),
		},
	},
}

var deleteSubnetMessage1 = &types.RMBody{
	Package: "sensetime.core.network.v1",
	Service: "Subnets",
	Method:  "DeleteSubnet",
	Action:  "Delete",
	CallbackData: types.CallbackData{
		OrderID: "21fe7811-be10-4aee-9ef9-2392527b2c6a",
	},
	Data: vpcv1.DeleteSubnetRequest{
		SubscriptionName:  "sub-b1",
		ResourceGroupName: "rg-b1",
		Zone:              "zone-b1",
		SubnetName:        "sn-b1-79a959e9",
	},
}

var createNATGatewayMessage1 = &types.RMBody{
	Package: "sensetime.core.network.v1",
	Service: "NATGateways",
	Method:  "CreateNATGateway",
	Action:  "Create",
	CallbackData: types.CallbackData{
		OrderID: "79a959e9-fa14-4dda-82bc-12fb8d727a9b",
	},
	Data: gwv1.CreateNATGatewayRequest{
		SubscriptionName:  "sub-b1",
		ResourceGroupName: "rg-b1",
		Zone:              "zone-b1",
		NatGatewayName:    "gw-b1-a3059ed1",
		NatGateway: &gwv1.NATGateway{
			Id:           "subscriptions/sub-b1/resourceGroups/rg-b1/zones/zone-b1/natGateways/gw-b1-a3059ed1",
			Name:         "gw-b1-a3059ed1",
			Uid:          "a3059ed1-1155-45b5-9392-05f3b2498250",
			ResourceType: "network.v1.subnet",
			CreatorId:    "b1",
			OwnerId:      "b1",
			TenantId:     "b1",
			Zone:         "zone-b1",
			DisplayName:  "gw-b1-test",
			Description:  "The first gw",
			Properties: &gwv1.NATGatewayProperties{
				SubnetId:   "1b7dbd83-9b57-418a-9149-043f83093e5e",
				VpcId:      "718b9360-dd32-4af6-a81b-9bb99e9f6d60",
				InternalIp: "************",
				NatGwExternalIp: &gwv1.NATGatewayExternalIP{
					Cidr:    "***********/22",
					Gateway: "***********",
				},
			},
			CreateTime: timestamppb.New(time.Now()),
			UpdateTime: timestamppb.New(time.Now()),
		},
	},
}

var deleteNATGatewayMessage1 = &types.RMBody{
	Package: "sensetime.core.network.v1",
	Service: "NATGateways",
	Method:  "DeleteNATGateway",
	Action:  "Delete",
	CallbackData: types.CallbackData{
		OrderID: "21fe7811-be10-4aee-9ef9-2392527b2c6c",
	},
	Data: gwv1.DeleteNATGatewayRequest{
		SubscriptionName:  "sub-b1",
		ResourceGroupName: "rg-b1",
		Zone:              "zone-b1",
		NatGatewayName:    "gw-b1-a3059ed1",
	},
}

func NewProducer() rocketmq.Producer {
	p, err := rocketmq.NewProducer(
		// 设置  nameSrvAddr
		// nameSrvAddr 是 Topic 路由注册中心
		producer.WithNameServer(rpConfig.MQ.Default.NameServer),
		// 指定发送失败时的重试时间
		producer.WithRetry(2),
		// 设置 Group
		producer.WithGroupName(rpConfig.MQ.Default.BrokerConsumerGroupName),
		producer.WithCredentials(primitive.Credentials{
			AccessKey: rpConfig.MQ.Default.AccessKey,
			SecretKey: rpConfig.MQ.Default.SecretKey,
		}),
		producer.WithQueueSelector(NewRoundRobinQueueSelector()),
	)
	if err != nil {
		fmt.Printf("new producer error: %s", err.Error())
		os.Exit(1)
	}
	return p
}

func produce(p rocketmq.Producer, topic string, message *types.RMBody) {

	// 开始连接
	err := p.Start()
	if err != nil {
		fmt.Printf("start producer error: %s", err.Error())
		os.Exit(1)
	}

	b, _ := json.Marshal(message)
	msg := &primitive.Message{
		Topic: topic,
		Body:  b,
	}

	// 发送信息
	res, err := p.SendSync(context.Background(), msg)
	if err != nil {
		fmt.Printf("send message error:%s\n", err)
	} else {

		fmt.Printf("send message success: result=%s\n", res.String())

	}

}

func toJson(message *types.RMBody) {
	res, err := json.MarshalIndent(message, "", "  ")
	if err != nil {
		fmt.Println(err)
	}

	err = ioutil.WriteFile("local-test/produce/sample_messages/"+message.Method+".json", res, os.ModePerm)
	if err != nil {
		fmt.Println(err)
	}
}

func main() {
	if len(os.Args) < 2 {
		fmt.Printf("Args error! %s [topic] [message-type:default|cloudEvent] [message.json] [sendTimes]", os.Args[0])
		return
	}

	topic := os.Args[1]
	messageType := os.Args[2]
	messageFlie := os.Args[3]
	sendTimes := "1"
	if len(os.Args) == 5 {
		sendTimes = os.Args[4]
	}
	count, err := strconv.Atoi(sendTimes)
	if err != nil {
		logrus.Fatal("count init failed")
	}

	messageByte, err := ioutil.ReadFile(messageFlie)
	if err != nil {
		fmt.Println(err)
		return
	}

	if messageType == "default" {
		message := types.RMBody{}
		err = json.Unmarshal(messageByte, &message)
		if err != nil {
			fmt.Println(err)
			return
		}

		p := NewProducer()
		for i := 0; i < count; i++ {
			produce(p, topic, &message)
		}
		p.Shutdown()
	} else if messageType == "cloudEvent" {
		message := types.CloudEventMsg{}
		err = json.Unmarshal(messageByte, &message)
		if err != nil {
			fmt.Println(err)
			return
		}

		// produceCloudEvent(topic, &message)
	}
}

func produceCloudEvent(topic string, message *types.CloudEventMsg) {
	fmt.Printf("%+v\n", message)

	p, err := rocketmq.NewProducer(
		// 设置  nameSrvAddr
		// nameSrvAddr 是 Topic 路由注册中心
		producer.WithNameServer(rpConfig.MQ.Default.NameServer),
		// 指定发送失败时的重试时间
		producer.WithRetry(2),
		// 设置 Group
		producer.WithGroupName(rpConfig.MQ.Default.SLBConsumerGroupName),
		producer.WithCredentials(primitive.Credentials{
			AccessKey: rpConfig.MQ.Default.AccessKey,
			SecretKey: rpConfig.MQ.Default.SecretKey,
		}),
	)
	if err != nil {
		fmt.Printf("new producer error: %s", err.Error())
		os.Exit(1)
	}

	// 开始连接
	err = p.Start()
	if err != nil {
		fmt.Printf("start producer error: %s", err.Error())
		os.Exit(1)
	}

	b, _ := json.Marshal(message)
	msg := &primitive.Message{
		Topic: topic,
		Body:  b,
	}
	// 发送信息
	res, err := p.SendSync(context.Background(), msg)
	if err != nil {
		fmt.Printf("send message error:%s\n", err)
	} else {

		fmt.Printf("send message success: result=%s\n", res.String())

	}

	err = p.Shutdown()
	if err != nil {
		fmt.Printf("shutdown producer error:%s", err.Error())
	}
}
