#!/bin/bash

function createEip() {
   echo "create"
  ./main sensetime-core-network-eip-v1-cn-sh-01e default sample_messages/CreateEIP-1.json  &
  sleep 0.1
  ./main sensetime-core-network-eip-v1-cn-sh-01e default sample_messages/CreateEIP-2.json  &
}

function deleteEip() {
   echo "delete"
  #./main sensetime-core-network-eip-v1-cn-sh-01e default sample_messages/DeleteEIP-1.json
  ./main sensetime-core-network-eip-v1-cn-sh-01e default sample_messages/DeleteEIP-2.json

}

function testEip(){
 createEip
# deleteEip
}

#testEip


function createVpc() {
  #echo "create"
  ./main sensetime-core-network-vpc-v1-cn-sh-01e default sample_messages/CreateVPC6.json 100
  #./main sensetime-core-network-vpc-v1-cn-sh-01e default sample_messages/DeleteVPC6.json 1
}


createVpc
