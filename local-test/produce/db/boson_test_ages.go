package main

import (
	"fmt"

	_ "github.com/lib/pq"
	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	"xorm.io/xorm"
)

var rpConfig *config.Config

// sync 完表之后，需要插入一条记录，作为race point
// insert into boson_test_ages values(1, 1);
type bosonTestAges struct {
	AgeID int `json:"age_id" xorm:"not null pk autoincr INT(10) 'age_id'"`
	Age   int `json:"age" xorm:"not null default 0 comment('年龄') INT(11) 'age'"`
}

func init() {
	rpConfig = config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))
}

func main() {
	pgInfo := fmt.Sprintf("host=%s port=%v user=%s "+
		"password=%s dbname=%s sslmode=disable",
		rpConfig.PG.Host, rpConfig.PG.Port, rpConfig.PG.User, rpConfig.PG.Password, rpConfig.PG.DB)
	fmt.Println(pgInfo)
	engine, err := xorm.NewEngine("postgres", pgInfo)

	if err != nil {
		logrus.Fatal(err)
	}

	defer engine.Close()

	engine.Sync2(new(bosonTestAges))

	tables, err := engine.DBMetas()
	if err != nil {
		logrus.Fatal(err)
	}

	for _, table := range tables {
		if table.Name == "boson_test_ages" {
			logrus.Infof("db has sync table %s", table.Name)
		}
	}
}
