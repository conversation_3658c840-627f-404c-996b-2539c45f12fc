package main

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/consumer"
	"github.com/apache/rocketmq-client-go/v2/primitive"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
)

var rpConfig *config.Config

func init() {
	rpConfig = config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))
}

func Consume(topic, group string) {

	fmt.Printf("Create consumer for Topic: %s\n", topic)

	// 设置推送消费者
	c, _ := rocketmq.NewPushConsumer(
		//消费组
		consumer.WithGroupName(group),
		// namesrv地址
		consumer.WithNameServer(rpConfig.MQ.Default.NameServer),
	)
	// 必须先在 开始前
	err := c.Subscribe(topic, consumer.MessageSelector{}, func(ctx context.Context, ext ...*primitive.MessageExt) (consumer.ConsumeResult, error) {
		for i := range ext {
			fmt.Printf("Get a message from Topic %s: %v \n", topic, ext[i])
		}
		return consumer.ConsumeSuccess, nil
	})
	if err != nil {
		fmt.Println(err.Error())
	}
	err = c.Start()
	if err != nil {
		fmt.Println(err.Error())
		os.Exit(-1)
	}
	time.Sleep(time.Hour)
	err = c.Shutdown()
	if err != nil {
		fmt.Printf("shutdown Consumer error:%s\n", err.Error())
	}

}

func main() {
	go Consume(rpConfig.MQ.Default.RMAckTopic, rpConfig.MQ.Default.BrokerRMProducerGroupName)
	Consume(rpConfig.MQ.Default.BossAckTopic, rpConfig.MQ.Default.BrokerBossProducerGroupName)
}
