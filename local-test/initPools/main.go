package main

import (
	"fmt"
	"os"
	"strings"

	_ "github.com/lib/pq"
	"github.com/sirupsen/logrus"
	"xorm.io/xorm"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	db2 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

var rpConfig *config.Config

func init() {
	rpConfig = config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))
}

func insertGwips(cidr, gw, ips string) {

	ipsList := strings.Split(ips, ",")

	pgInfo := fmt.Sprintf("host=%s port=%v user=%s "+
		"password=%s dbname=%s sslmode=disable",
		rpConfig.PG.Host, rpConfig.PG.Port, rpConfig.PG.User, rpConfig.PG.Password, rpConfig.PG.DB)
	fmt.Println(pgInfo)
	engine, err := xorm.NewEngine("postgres", pgInfo)
	engine.ShowSQL(true)

	if err != nil {
		logrus.Fatal(err)
	}

	defer engine.Close()

	cidrData := db2.CidrPools{}

	has, err := engine.Where("cidr = ? and gateway_ip = ? and scope = ?", cidr, gw, "VPCGW").Get(&cidrData)
	if err != nil {
		logrus.Fatal(err)
	}
	if !has {
		cidrID := utils.UUIDGen()

		cidrData = db2.CidrPools{
			CIDRPoolID:  cidrID,
			CIDR:        cidr,
			Description: "",
			GatewayIP:   gw,
			Scope:       "VPCGW",
			NetworkType: "FLAT",
			Allocated:   false,
			ZonePrp:     rpConfig.Boson.VPCDefaultAZ,
		}

		_, err = engine.Insert(&cidrData)
		if err != nil {
			logrus.Fatal(err)
		}
	} else {
		logrus.Info(fmt.Sprintf("gw %s cidr %s scope %s  exist, skip create", gw, cidr, "VPCGW"))
	}

	for _, ip := range ipsList {
		gwIp := db2.NatGatewayExternalIpPools{
			NatGwExternalIP: ip,
			CIDRID:          cidrData.CIDRPoolID,
		}
		has, err = engine.Get(&gwIp)
		if err != nil {
			logrus.Fatal(err)
		}

		if !has {
			gwIp = db2.NatGatewayExternalIpPools{
				NatGwExternalIPID: utils.UUIDGen(),
				NatGwExternalIP:   ip,
				Description:       "",
				Allocated:         false,
				NATGatewayID:      "",
				CIDRID:            cidrData.CIDRPoolID,
			}

			_, err = engine.Insert(&gwIp)
			if err != nil {
				logrus.Fatal(err)
			}
		} else {
			logrus.Info(fmt.Sprintf("gw external ip %s, cidr id %s exist, skip create", ip, cidrData.CIDRPoolID))
		}
	}
}

func insertSlbsIps(cidr, gw, ips string) {
	ipsList := strings.Split(ips, ",")
	pgInfo := fmt.Sprintf("host=%s port=%v user=%s "+
		"password=%s dbname=%s sslmode=disable",
		rpConfig.PG.Host, rpConfig.PG.Port, rpConfig.PG.User, rpConfig.PG.Password, rpConfig.PG.DB)
	fmt.Println(pgInfo)
	engine, err := xorm.NewEngine("postgres", pgInfo)
	engine.ShowSQL(true)

	if err != nil {
		logrus.Fatal(err)
	}

	defer engine.Close()
	cidrData := db2.CidrPools{}

	has, err := engine.Where("cidr = ? and gateway_ip = ? and scope = ?", cidr, gw, "SLB").Get(&cidrData)
	if err != nil {
		logrus.Fatal(err)
	}
	if !has {
		cidrID := utils.UUIDGen()

		cidrData = db2.CidrPools{
			CIDRPoolID:  cidrID,
			CIDR:        cidr,
			Description: "",
			GatewayIP:   gw,
			Scope:       "SLB",
			NetworkType: "FLAT",
			Allocated:   false,
			ZonePrp:     rpConfig.Boson.VPCDefaultAZ,
		}

		_, err = engine.Insert(&cidrData)
		if err != nil {
			logrus.Fatal(err)
		}
	} else {
		logrus.Info(fmt.Sprintf("cidr %s scope %s exist, skip create cidr pool", cidr, "SLB"))
	}

	for _, ip := range ipsList {
		slbIp := db2.SlbExternalIpPools{
			SlbExternalIp: ip,
			Description:   "",
			SlbID:         "",
			CidrID:        cidrData.CIDRPoolID,
			Type:          "",
		}

		exist, err := engine.Exist(&slbIp)
		if err != nil {
			logrus.Fatal(err)
		}
		if exist {
			logrus.Infoln("cidr exist, skip", slbIp)
			continue
		}

		slbIp.Allocated = false
		slbIp.SlbExternalIpID = utils.UUIDGen()
		_, err = engine.Insert(&slbIp)
		if err != nil {
			logrus.Fatal(err)
		}
	}
}

func insertEips(cidr, gw, ips, sku, line string) {

	ipsList := strings.Split(ips, ",")

	pgInfo := fmt.Sprintf("host=%s port=%v user=%s "+
		"password=%s dbname=%s sslmode=disable",
		rpConfig.PG.Host, rpConfig.PG.Port, rpConfig.PG.User, rpConfig.PG.Password, rpConfig.PG.DB)
	fmt.Println(pgInfo)
	engine, err := xorm.NewEngine("postgres", pgInfo)
	engine.ShowSQL(true)

	if err != nil {
		logrus.Fatal(err)
	}

	defer engine.Close()

	// if line empty, use sku as line
	if len(line) == 0 {
		line = sku
	}

	for _, ip := range ipsList {

		eip := db2.EipPools{
			EIPIP:   ip,
			Gateway: gw,
			CIDR:    cidr,
			Sku:     sku,
			Line:    line,
		}
		has, err := engine.Exist(&eip)
		if err != nil {
			logrus.Fatal(err)
		}

		if has {
			logrus.Info(fmt.Sprintf("has eip %s in db, skip", ip))
			continue
		}

		eip.Allocated = false
		eip.EIPPoolID = utils.UUIDGen()
		_, err = engine.Insert(&eip)
		if err != nil {
			logrus.Fatal(err)
		}
	}
}

func insertCIDRPool(pool *config.Pool, networkType string, scope string) {
	pgInfo := fmt.Sprintf("host=%s port=%v user=%s "+
		"password=%s dbname=%s sslmode=disable",
		rpConfig.PG.Host, rpConfig.PG.Port, rpConfig.PG.User, rpConfig.PG.Password, rpConfig.PG.DB)
	fmt.Println(pgInfo)
	engine, err := xorm.NewEngine("postgres", pgInfo)
	engine.ShowSQL(true)

	if err != nil {
		logrus.Fatal(err)
	}

	defer engine.Close()

	if networkType == "ib" && pool.IB == nil || networkType == "vlan" && pool.Vlan == nil {
		fmt.Println("Empty cidr pool", networkType)
		return
	}

	successCount := 0
	if networkType == "ib" {
		// no need to judge scope, cuz ib only used in training scope
		for _, ib := range pool.IB {
			cidrPool := db2.CidrPools{
				CIDR:        ib.CIDR,
				GatewayIP:   ib.Gateway,
				Scope:       ib.Scope,
				NetworkType: "IB",
				VNI:         ib.VNI,
				Reserved:    ib.Reserved,
				ZonePrp:     ib.ZonePRP,
			}

			exist, err := engine.Exist(&cidrPool)
			if err != nil {
				logrus.Fatal(err)
			}
			if exist {
				logrus.Infoln("cidr exist, skip")
				continue
			}
			cidrPool.Allocated = false
			cidrPool.CIDRPoolID = utils.UUIDGen()
			_, err = engine.Insert(&cidrPool)
			if err != nil {
				logrus.Fatal(err)
			}
			successCount++
		}
		logrus.Infoln(fmt.Sprintf("\nInit IB cidrPool total: %d success: %d skip: %d\n", len(pool.IB), successCount, len(pool.IB)-successCount))
	}

	if networkType == "vlan" {
		for _, vlan := range pool.Vlan {
			successCount = 0
			if scope != "all" && vlan.Scope != scope {
				continue
			}

			cidrPool := db2.CidrPools{
				CIDR:        vlan.CIDR,
				GatewayIP:   vlan.Gateway,
				Scope:       vlan.Scope,
				NetworkType: "VLAN",
				VNI:         vlan.VNI,
				Reserved:    vlan.Reserved,
				ZonePrp:     vlan.ZonePRP,
			}
			exist, err := engine.Exist(&cidrPool)
			if err != nil {
				logrus.Fatal(err)
			}
			if exist {
				logrus.Infoln("cidr exist, skip")
				continue
			}
			cidrPool.Allocated = false
			cidrPool.CIDRPoolID = utils.UUIDGen()
			_, err = engine.Insert(&cidrPool)
			if err != nil {
				logrus.Fatal(err)
			}
			successCount++
		}
		logrus.Infoln(fmt.Sprintf("\nInit %s VLAN cidrPool: total: %d  success: %d ingore: %d\n", scope, len(pool.Vlan), successCount, len(pool.Vlan)-successCount))
	}
}

func insertCIDRPoolForAll(pool *config.Pool) {
	insertCIDRPool(pool, "ib", "all")
	insertCIDRPool(pool, "vlan", "all")
}

func main() {

	var opt string
	if len(os.Args) < 2 {
		opt = "all"
	} else {
		opt = os.Args[1]
	}

	if rpConfig.InitPool == nil {
		fmt.Println("Empty InitPool")
		return
	}

	if opt == "nat_gateways" || opt == "all" {
		if rpConfig.InitPool["nat_gateways"] == nil || rpConfig.InitPool["nat_gateways"].IPs == nil || len(rpConfig.InitPool["nat_gateways"].IPs) == 0 {
			fmt.Println("Empty InitPool.nat_gateways")
		} else {
			insertGwips(rpConfig.InitPool["nat_gateways"].CIDR, rpConfig.InitPool["nat_gateways"].Gateway, strings.Join(rpConfig.InitPool["nat_gateways"].IPs, ","))
		}
	}

	if opt == "slbs" || opt == "all" {
		if rpConfig.InitPool["slbs"] == nil || rpConfig.InitPool["slbs"].IPs == nil || len(rpConfig.InitPool["slbs"].IPs) == 0 {
			fmt.Println("Empty InitPool.slbs")
		} else {
			insertSlbsIps(
				rpConfig.InitPool["slbs"].CIDR,
				rpConfig.InitPool["slbs"].Gateway,
				strings.Join(rpConfig.InitPool["slbs"].IPs, ","),
			)
		}
	}

	if opt == "eips" || opt == "all" {
		if rpConfig.InitPool["eips"] == nil || rpConfig.InitPool["eips"].IPs == nil || len(rpConfig.InitPool["eips"].IPs) == 0 {
			fmt.Println("Empty InitPool.eips")
		} else {
			insertEips(rpConfig.InitPool["eips"].CIDR, rpConfig.InitPool["eips"].Gateway, strings.Join(rpConfig.InitPool["eips"].IPs, ","), rpConfig.InitPool["eips"].Sku, rpConfig.InitPool["eips"].Line)
		}
	}

	if opt == "cidr_pools" || opt == "all" {
		if rpConfig.InitPool["cidr_pools"] == nil {
			fmt.Println("Empty InitPool.cidr_pools")
		} else if opt == "all" {
			fmt.Println("InitPool.cidr_pools all")
			insertCIDRPoolForAll(rpConfig.InitPool["cidr_pools"])
		} else {
			networkType := os.Args[2]
			scope := os.Args[3]
			insertCIDRPool(rpConfig.InitPool["cidr_pools"], networkType, scope)
		}
	}
}
