#!/usr/bin/python3
# -*- coding: utf-8 -*-

import argparse
import sys
import os
import json
import copy
import yaml
import logging
from datetime import datetime

def getAllVpc():
    getAllVpcCmd = "kubectl get vpc -ojson"
    fd = os.popen(getAllVpcCmd)
    vpcJsonStr = fd.read()
    vpc = json.loads(vpcJsonStr)

    vpcNames = []
    for vpc in vpc.get("items", []) :
        name = vpc.get("metadata", {}).get("name", "")
        if len(name) > 0 :
            vpcNames.append(name)

    return vpcNames

roceGw = "10.119.192.1"
roceGw1 = "10.119.208.1"
resourceName = "rdma-training/roce"
resourceName1 = "rdma-training/roce_1"

# key:vpcName, value:[]NAD
nadMap = {}

rollbackPath = ""
subnetYamlRollbackFd = ""
nadYamlRollbackFd = ""
tryRun = True
rollbackPathDefault = "./rollbackPath"

def execKubectlPatch(cmd):
    global tryRun
    if tryRun is True:
        logging.info(cmd)
        return 0

    fd = os.popen(cmd)
    logging.info("cmd: {}, return msg: {}".format(cmd, fd.read()))
    return 0

def oveSubnetOneSave(subnet):
    global subnetYamlRollbackFd
    data = bytes(yaml.dump(subnet), encoding="UTF-8")
    try:
        os.write(subnetYamlRollbackFd, data)
        os.write(subnetYamlRollbackFd, bytes("\n---\n", encoding="UTF-8"))
    except Exception as e:
        logging.ERROR("write subnetYamlRollbackFd fail, exception:{}, data:\n{}".format(e, data))
        return -1
    return 0


def nadNetworkAttachmentDefinitionOneSave(nad):
    global nadYamlRollbackFd
    data = bytes(yaml.dump(nad), encoding="UTF-8")
    try:
        os.write(nadYamlRollbackFd, data)
        os.write(nadYamlRollbackFd, bytes("\n---\n", encoding="UTF-8"))
    except Exception as e:
        logging.ERROR("write nadYamlRollbackFd fail, exception:{}, data:\n{}".format(e, data))
        return -1
    return 0


def ovnSubnetOne(subnet, annoRoceId):
    global resourceName
    # .1 save
    subnetOld = copy.deepcopy(subnet)

    # .2 modify
    # .2.1 annotations
    if subnet.get("metadata", {}).get("annotations", None) is None:
        logging.error("subnet:{} CR error, annotations are missing".format(subnet["metadata"]["name"]))
        return -1
    annos = subnet["metadata"]["annotations"]
    annos["k8s.v1.cni.cncf.io/resourceName"] = resourceName
    annos["roce_id"] = annoRoceId
    subnet["metadata"]["annotations"] = annos


    # .3 patch
    subnetName = subnet["metadata"]["name"]
    patch = {}
    # annotations
    patch["metadata"] = {}
    patch["metadata"]["annotations"] = subnet["metadata"]["annotations"]

    # check
    # 判定: annos 中是否已经存在 roce_id 字段，存在则说明已经更新过了，则不再更新
    if "roce_id" in subnetOld["metadata"]["annotations"]:
        logging.info("subnet:{} has been update, skip exec patch, \nnewPatch:{}\ncurrCR:{}".format(subnetName, patch, subnetOld))
        return 0

    patchCmd = "kubectl patch subnet {} --type merge --patch '{}' ".format(subnetName, json.dumps(patch))
    if execKubectlPatch(patchCmd) != 0 :
        return -1

    # .4 save
    if oveSubnetOneSave(subnetOld) != 0 :
        return -1

    return 0



def ovnSubnet(vpcCrName):
    global roceGw
    global roceGw1
    dumpCmd = "kubectl get subnet -l vpc_name={} -ojson".format(vpcCrName)
    fd = os.popen(dumpCmd)
    subnetsJsonStr = fd.read()
    try:
        subnets = json.loads(subnetsJsonStr)
    except Exception as e:
        logging.error("subnetsJsonStr:{} json.loads error. e:{}".format(subnetsJsonStr, e))
        return 0

    subnetsHit = []
    for subnet in subnets.get("items", []):
        gwIP = subnet.get("spec", {}).get("gateway", "")
        if len(gwIP) == 0:
            continue
        annoRoceId = ""
        if gwIP == roceGw:
            annoRoceId = "0"
        elif gwIP == roceGw1:
            annoRoceId = "1"
        else:
            continue
        if ovnSubnetOne(subnet, annoRoceId) != 0:
            return -1

    return 0



NADConfig = '''{
    "cniVersion": "0.3.1",
    "plugins": [
        {
            "type": "roce-host-device",
            "if_id": "",
            "server_socket": "/run/roce-cni/daemon.sock"
        }
    ]
}'''

def nadNetworkAttachmentDefinitionOne(vpcCrName, nad):
    global resourceName
    global resourceName1
    global NADConfig

    nadName = nad.get("metadata", {}).get("name", "")
    if nad.get("metadata", {}).get("annotations", None) is None :
        logging.error("vpc:{} nad:{} annotations is missed".format(vpcCrName, nadName))
        return -1

    annos = nad["metadata"]["annotations"]
    if annos.get("k8s.v1.cni.cncf.io/resourceName", None) is None:
        logging.error("vpc:{} nad:{} annotations.XXXresourceName is missed".format(vpcCrName, nadName))
        return -1

    # .1 save
    nadOld = copy.deepcopy(nad)

    # .2 modify
    # .2.1 init params
    specConfigIfId = ""
    annosResourceName = annos["k8s.v1.cni.cncf.io/resourceName"]
    if annosResourceName == resourceName:
        specConfigIfId = "roce_0"
    elif annosResourceName == resourceName1:
        specConfigIfId = "roce_1"
    else:
        logging.error("vpc:{} nad:{} annotations.XXXresourceName:{} is error".format(
            vpcCrName, nadName, annosResourceName))
        return -1

    # .2.2 annotations
    annos = nad.get("metadata", {}).get("annotations", {})
    annos["k8s.v1.cni.cncf.io/resourceName"] = resourceName
    nad["metadata"]["annotations"] = annos

    # .2.3 spec.config
    nadConfig = json.loads(NADConfig)
    nadConfig["plugins"][0]["if_id"] = specConfigIfId
    nad["spec"]["config"] = json.dumps(nadConfig, indent=4)

    # .3 patch
    patch = {}
    # annotations
    patch["metadata"] = {}
    patch["metadata"]["annotations"] = nad["metadata"]["annotations"]
    # spec.config
    patch["spec"] = {}
    patch["spec"]["config"] = nad["spec"]["config"]

    # check
    # 判定: config 中插件的配置是否存在 roce-host-device, 存在则说明已经更新过了，则不再更新
    nadConfigOrigin = json.loads(nadOld["spec"]["config"])
    for plug in nadConfigOrigin.get("plugins", []):
        if plug.get("type", "") == "roce-host-device":
            logging.info("nad:{} has been update, skip exec patch, \nnewPatch:{}\ncurrCR:{}".format(nadName, patch, nadOld))
            return 0

    patchCmd = "kubectl patch network-attachment-definitions.k8s.cni.cncf.io -n plat-boson-service {}  --type merge --patch  '{}' ".format(
        nadName, json.dumps(patch))
    if execKubectlPatch(patchCmd) != 0 :
        return -1

    # .4 save
    if nadNetworkAttachmentDefinitionOneSave(nadOld) != 0:
        return -1

    return 0

def nadNetworkAttachmentDefinition(vpcCrName):
    global nadMap

    if vpcCrName not in nadMap:
        logging.error("can not find vpc:{} associcate NAD".format(vpcCrName))
        return 0
    for nad in nadMap[vpcCrName]:
        if nadNetworkAttachmentDefinitionOne(vpcCrName, nad) != 0:
            return -1

    return 0

def nadMapInit():
    global nadMap
    dumpCmd = "kubectl get network-attachment-definitions.k8s.cni.cncf.io -ojson -n plat-boson-service"
    fd = os.popen(dumpCmd)
    nadAllJsonStr = fd.read()
    nads = json.loads(nadAllJsonStr)
    nadMap = {}
    for nad in nads.get("items", []):
        ownerRefs = nad.get("metadata", {}).get("ownerReferences", [])
        if len(ownerRefs) == 0:
            continue
        for ownerRef in ownerRefs:
            kind = ownerRef.get("kind", "")
            vpcName = ownerRef.get("name", "")
            if kind != "Vpc":
                continue
            if len(vpcName) == 0:
                continue
            if vpcName not in nadMap:
                nadMap[vpcName] = []
            nadMap[vpcName].append(nad)


"""
refreshVpcOne 刷新存量vpc的训练网CR
"""
def refreshVpcOne(vpcCrName):
    if nadNetworkAttachmentDefinition(vpcCrName) != 0:
        return -1

    if ovnSubnet(vpcCrName) != 0:
        return -1

    return 0

def getVpcs(args):
    vpcs = []
    if args.Model:
        if args.Model == "all":
            vpcs = getAllVpc()
        elif args.Model == "list":
            if args.VpcList:
                vpcs = args.VpcList.split(",")
            else:
                logging.error("List is null")
                return [], -1
        else:
            logging.error("Model error")
            return [], -1
    else:
        logging.error("Model is null")
        return [], -1
    return vpcs, 0

def initTryRun(args):
    # TryRun
    global tryRun
    if args.TryRun:
        if args.TryRun == "true":
            tryRun = True
        else:
            tryRun = False
    else:
        tryRun = True

def initRollbackPath(args):
    global rollbackPathDefault
    global rollbackPath
    global subnetYamlRollbackFd
    global nadYamlRollbackFd

    if args.RollbackPath:
        rollbackPath = args.RollbackPath
    else:
        rollbackPath = rollbackPathDefault

    if not os.path.exists(rollbackPath):
        os.makedirs(rollbackPath)
        logging.debug("{} not exist, create it".format(rollbackPath))

    timeSuf = datetime.now().strftime("%Y-%m-%d-%H-%M-%S")
    try:
        subnetYamlRollback = os.path.join(rollbackPath, "subnet.yaml.{}".format(timeSuf))
        subnetYamlRollbackFd = os.open(subnetYamlRollback, os.O_RDWR | os.O_CREAT)

        nadYamlRollback = os.path.join(rollbackPath, "nad.yaml.{}".format(timeSuf))
        nadYamlRollbackFd = os.open(nadYamlRollback, os.O_RDWR | os.O_CREAT)
    except Exception as e:
        logging.error("open files error, exception:{}".format(e))
        return -1
    return 0

def refreshVpc(args):
    global subnetYamlRollbackFd
    global nadYamlRollbackFd

    vpcs, ret = getVpcs(args)
    if ret != 0 :
        return -1
    if len(vpcs) == 0 :
        logging.error("vpc list is null")
        return -1

    try:
        initTryRun(args)

        # open FDs: subnetYamlRollbackFd, nadYamlRollbackFd
        if initRollbackPath(args) != 0:
            return -1

        # 因为 nad 没有label，所以手动做个全量map，给后面用
        nadMapInit()

        for vpcCrName in vpcs:
            if refreshVpcOne(vpcCrName) != 0:
                logging.error("refresh vpc {} fail, ending all refresh".format(vpcCrName))
                os.close(subnetYamlRollbackFd)
                os.close(nadYamlRollbackFd)
                return -1

    except Exception as e:
        logging.error("refresh VPCs exception:{}".format(e))
        os.close(subnetYamlRollbackFd)
        os.close(nadYamlRollbackFd)
        return -1

    os.close(subnetYamlRollbackFd)
    os.close(nadYamlRollbackFd)
    return 0

def getYamls(data):
    yamls = []
    yamlDatas = yaml.load_all(data)
    for y in yamlDatas:
        if y is None:
            break
        yamls.append(y)
    return yamls

def rollback(crFiles, crNames):
    crYamls = []
    for data in crFiles:
        crYamls.extend(getYamls(data))

    # .1 generate
    # key: cr.name, value:cr(dict)
    crMap = {}
    for y in crYamls:
        name = y.get("metadata", {}).get("name", None)
        if name is None:
            continue
        crMap[name] = y

    # .2 rollback
    for crName in crNames:
        info = crMap.get(crName, None)
        if info is None:
            logging.error("{} rollback fail, can not find rollback file info".format(crName))
            continue

        cr = "subnet"
        if info["kind"] == "Subnet":
            cr = "subnet"
        else:
            cr = "network-attachment-definitions.k8s.cni.cncf.io -n plat-boson-service -n plat-boson-service"

        # a. generate patch
        # 只保留 annotations、spec
        # 因为无论是ovn.subnet、nad.nadNetworkAttachmentDefinition 都只修改了这三个属性
        patch = {}
        patch["metadata"] = {}
        patch["metadata"]["annotations"] = info.get("metadata", {}).get("annotations", [])
        patch["spec"] = info.get("spec", {})
        if cr == "subnet":
            patch["metadata"]["annotations"]["roce_id"] = None

        # b. kube patch
        patchCmd = "kubectl patch {} {} --type merge --patch '{}' ".format(cr, crName, json.dumps(patch))
        execKubectlPatch(patchCmd)

    return 0

def rollbackFiles(args):
    crFileNames = []
    if args.RollbackFiles:
        crFileNames = args.RollbackFiles.split(",")
    else:
        logging.error("RollbackFiles is missed")
        return []
    if len(crFileNames) == 0:
        logging.error("RollbackFiles is null")
        return []

    try:
        crFiles = []
        for crFile in crFileNames:
            with open(crFile, mode="r") as f:
                crFiles.append(f.read())
    except Exception as e:
        logging.error("open files error, exception:{}".format(e))
        return []

    return crFiles


def rollbackVpc(args):
    initTryRun(args)

    crNames = []
    if args.RollbackCRNames:
        crNames = args.RollbackCRNames.split(",")
    else:
        logging.error("RollbackCRNames is missed")
        return -1
    if len(crNames) == 0:
        logging.error("RollbackCRNames is null")
        return -1

    crFiles = rollbackFiles(args)
    if len(crFiles) == 0:
        logging.error("open rollback files fail")
        return -1

    if rollback(crFiles, crNames) != 0:
        logging.error("rollback fail")
        return -1
    return 0

LOG_INFO = "info"
LOG_DEBUG = "debug"
LOG_ERROR = "error"
LOG_DEFAULT = LOG_INFO
def logInit(args):
    level = LOG_DEFAULT
    if args.LogLevel:
        level = args.LogLevel

    loggingMap = {
        LOG_INFO : logging.INFO,
        LOG_DEBUG : logging.DEBUG,
        LOG_ERROR : logging.ERROR
    }

    logging.basicConfig(level=loggingMap.get(level, logging.INFO))

def _main(args):
    logInit(args)
    if args.Model:
        if args.Model == "rollback":
            return rollbackVpc(args)

    return refreshVpc(args)

"""
支持 pf-vlan 方案，用以刷新租户 vpc 相关的 nad、subnet 资源，不涉及数据库的变更
"""
if __name__ == "__main__":
    """
    .1 试运行脚本, 刷新全量vpc的训练网ovn.subnet, nad.networkAttachmentDefinition, RollbackPath 存储的老版本的cr数据
    python3 refreshVPC.py --Model all --RollbackPath ./rollbackPath/
    .2 执行脚本, 刷新全量vpc的训练网ovn.subnet, nad.networkAttachmentDefinition, RollbackPath 存储的老版本的cr数据
    python3 refreshVPC.py --Model all --RollbackPath ./rollbackPath/ --TryRun false
    .3 试运行脚本, 刷新指定vpc的训练网ovn.subnet, nad.networkAttachmentDefinition, RollbackPath 存储的老版本的cr数据
    python3 refreshVPC.py --Model list \
        --VpcList vpc-8624ed056aba44-718b9360,vpc-8624ed056aba44-718b9360,vpc-8624ed056aba44-718b9360 \
        --RollbackPath ./rollbackPath/
    .4 执行脚本, 刷新指定vpc的训练网ovn.subnet, nad.networkAttachmentDefinition, RollbackPath 存储的老版本的cr数据
    python3 refreshVPC.py --Model list \
        --VpcList vpc-8624ed056aba44-718b9360,vpc-8624ed056aba44-718b9360,vpc-8624ed056aba44-718b9360 \
        --RollbackPath ./rollbackPath/ \
        --TryRun false
    .5 试运行脚本, 回滚指定vpc的cr, RollbackFiles 之前 RollbackPath 中备份的cr数据文件
    python3 refreshVPC.py --Model rollback \
        --RollbackFiles ./rollbackPath/subnet.yaml.2023-4-23-20-29-9,./RollbackPath/nad.yaml.2023-4-23-20-29-9 \
        --RollbackCRNames nad-bosontest2-63c5e4a3,snt-9624ed056aba44-012d00f0
    .6 执行脚本, 回滚指定vpc的cr, RollbackFiles 之前 RollbackPath 中备份的cr数据文件
    python3 refreshVPC.py --Model rollback \
        --RollbackFiles ./rollbackPath/subnet.yaml.2023-4-23-20-29-9,./RollbackPath/nad.yaml.2023-4-23-20-29-9 \
        --RollbackCRNames nad-bosontest2-63c5e4a3,snt-9624ed056aba44-012d00f0\
        --TryRun false
    """

    parser = argparse.ArgumentParser()
    parser.add_argument("--Model", help="refresh all vpc or some vpc", choices=["all", "list", "rollback"])
    parser.add_argument("--VpcList", help="vpc list, value: name1,name2 , name=cr.vpc.name")
    parser.add_argument("--RollbackPath", help="rollback path, default:{}".format(rollbackPathDefault), default=rollbackPathDefault)
    parser.add_argument("--RollbackFiles", help="rollback files")
    parser.add_argument("--RollbackCRNames", help="rollback CR names")
    parser.add_argument("--LogLevel", help="log level, default:{}".format(LOG_DEFAULT), choices=[LOG_ERROR, LOG_INFO, LOG_DEBUG], default=LOG_DEFAULT)
    parser.add_argument("--TryRun", help="try run, default:{}".format("true"),  choices=["true", "false"], default="true")

    args = parser.parse_args()
    sys.exit(_main(args))
