package main

import (
	"fmt"
	"os"
	"strings"

	_ "github.com/lib/pq"
	"xorm.io/xorm"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	db2 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

var rpConfig *config.Config

func init() {
	rpConfig = config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))
}

func insert(cidr, gw, ips string) {

	cidrID := utils.UUIDGen()

	cidrData := db2.CidrPools{
		CIDRPoolID:  cidrID,
		CIDR:        cidr,
		Description: "",
		GatewayIP:   gw,
		Scope:       "VPCGW",
		NetworkType: "FLAT",
		Allocated:   false,
		//miaozhanyong  todo
		ZonePrp: "",
	}

	ipsList := strings.Split(ips, ",")

	pgInfo := fmt.Sprintf("host=%s port=%v user=%s "+
		"password=%s dbname=%s sslmode=disable",
		rpConfig.PG.Host, rpConfig.PG.Port, rpConfig.PG.User, rpConfig.PG.Password, rpConfig.PG.DB)
	fmt.Println(pgInfo)
	engine, err := xorm.NewEngine("postgres", pgInfo)

	if err != nil {
		println(err.Error())
		return
	}

	defer engine.Close()

	_, err = engine.Insert(&cidrData)
	if err != nil {
		fmt.Println(err)
		return
	}

	for _, ip := range ipsList {

		gwIP := db2.NatGatewayExternalIpPools{
			NatGwExternalIPID: utils.UUIDGen(),
			NatGwExternalIP:   ip,
			Description:       "",
			Allocated:         false,
			NATGatewayID:      "",
			CIDRID:            cidrID,
		}

		_, err = engine.Insert(&gwIP)
		if err != nil {
			fmt.Println(err)
			return
		}
	}

	tables, err := engine.DBMetas()
	if err != nil {
		fmt.Println(err)
		return
	}

	for _, table := range tables {
		fmt.Println("table: ", table.Name)
		results, _ := engine.Query("select * from " + table.Name)
		fmt.Println(results)
	}
}

func main() {
	if os.Args == nil || len(os.Args) == 0 {
		fmt.Println("No args, do nothing")
		return
	}

	if len(os.Args) != 4 {
		fmt.Println("Illego args, do nothing")
		return
	}

	insert(os.Args[1], os.Args[2], os.Args[3])
}
