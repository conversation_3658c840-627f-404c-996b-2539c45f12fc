#!/usr/bin/env python3

import sys
import subprocess
import time
import os

# PGPASSWORD=b5bioOt5sPuJm2 psql  -h ************* -U boson -d boson_service_dev44 -p 34150
psqlCmd = ""
debug = False

def sqlExec(sql: str, header = True) -> str:
    global psqlCmd
    global debug
    psqlCmdExec = "{} -t -c {}".format(psqlCmd, sql)
    if header:
        psqlCmdExec = "{} -c {}".format(psqlCmd, sql)
    if debug:
        print("SQL Exec: {}".format(psqlCmdExec))
    result = subprocess.run(psqlCmdExec, shell=True, stdout=subprocess.PIPE, stderr=subprocess.PIPE, universal_newlines=True)
    time.sleep(0.5)
    return result.stdout

def kubectlExec(argStr: str) -> int:
    return 0

def slbInstance(slbName: str) -> str:
    print(sqlExec("\"select slb_id,name,zone,state,deleted from slbs where name = '{}'\"".format(slbName)))
    slbId = sqlExec("\"select slb_id from slbs where name = '{}'\"".format(slbName), header=False)
    return slbId.strip()

def slbTgs(slbId: str) -> list:
    print(sqlExec("\"select target_group_id,name,zone,state,deleted from slb_target_groups where slb_id = '{}'\"".format(slbId)))
    tgIds = sqlExec("\"select target_group_id from slb_target_groups where slb_id = '{}'\"".format(slbId), header=False)
    tgIds = tgIds.strip().split('\n')
    return [id.strip() for id in tgIds]

def slbTargets(tgId: str):
    print(sqlExec("\"select target_id,name,zone,state,deleted from slb_targets where target_group_id = '{}'\"".format(tgId)))

def slbListeners(slbId: str):
    print(sqlExec("\"select listener_id,name,zone,state,deleted from slb_listeners where slb_id = '{}'\"".format(slbId)))
    listenerIds = sqlExec("\"select listener_id from slb_listeners where slb_id = '{}'\"".format(slbId), header=False)
    listenerIds = listenerIds.strip().split('\n')
    for listenerId in listenerIds:
        print(sqlExec("\"select slb_id,forward_rule_id,target_group_id,deleted from slb_listeners where forward_rule_id = '{}'\"".format(listenerId)))

def slbIPs(slbId: str):
    print(sqlExec("\"select slb_id,name,eip_id,basic_network_ip_ids,basic_network_vip_id,intranet_vip from slbs where slb_id = '{}'\"".format(slbId)))

    basicNetworkIpIDs = sqlExec("\"select basic_network_ip_ids from slbs where slb_id = '{}'\"".format(slbId), header=False)
    basicNetworkIpIDs = basicNetworkIpIDs.strip().split(',')
    for basicNetworkIpID in basicNetworkIpIDs:
        print(sqlExec("\"select slb_external_ip,type from slb_external_ip_pools where slb_external_ip_id = '{}'\"".format(basicNetworkIpID), header=False))

    basicNetworkVipID = sqlExec("\"select basic_network_vip_id from slbs where slb_id = '{}'\"".format(slbId), header=False)
    basicNetworkVipID = basicNetworkVipID.strip()
    print(sqlExec("\"select slb_external_ip,type from slb_external_ip_pools where slb_external_ip_id = '{}'\"".format(basicNetworkVipID), header=False)) 

def main(argv: list) -> int:
    global psqlCmd
    global debug
    if os.environ.get("SLB_DEBUG") == "true":
        debug = True

    psqlCmd = argv[1]
    slbName = argv[2]
    # db
    # .1 slb instance
    slbId = slbInstance(slbName)
    
    tgIds = slbTgs(slbId)
    for tgId in tgIds:
        slbTargets(tgId)
    
    slbListeners(slbId)
    
    # .2 slb resource
    # a. ip
    slbIPs(slbId)
    # b. boson service

    
    # k8s resource
    # todo

    return 0

if __name__ == "__main__":
    sys.exit(main(sys.argv))
