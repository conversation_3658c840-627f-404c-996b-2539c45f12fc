package main

import (
	"fmt"
	"strings"

	_ "github.com/lib/pq"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"xorm.io/xorm"
)

var rpConfig *config.Config

func init() {
	rpConfig = config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))
}

func allocateIp() {
	pgInfo := fmt.Sprintf("host=%s port=%v user=%s "+
		"password=%s dbname=%s sslmode=disable",
		rpConfig.PG.Host, rpConfig.PG.Port, rpConfig.PG.User, rpConfig.PG.Password, rpConfig.PG.DB)
	fmt.Println(pgInfo)
	engineOrigin, err := xorm.NewEngine("postgres", pgInfo)
	if err != nil {
		logrus.Fatal(err)
	}
	engineOrigin.ShowSQL(true)

	engine := db.NewEngineWrapper(engineOrigin)
	exporter.Exporter = exporter.NewExporter(rpConfig)
	constLabel := map[string]string{"env": "dev", "region": "sh-regional", "az": "sh-01", "prp": "a"}
	dbRTLabelKeys := []string{"operation__name"}
	domain := "boson"
	app := "provider"
	exporter.DBRequestRT = prometheus.NewSummaryVec(prometheus.SummaryOpts{
		Name:        metricsNameGen(domain, app, "db_request_rt", "ms"),
		Help:        "db response time, unit ms",
		Objectives:  utils.Objectives,
		ConstLabels: constLabel,
	}, dbRTLabelKeys)

	db.SetAZ(rpConfig.Boson.VPCDefaultAZ)
	db.SetRegion(rpConfig.Boson.VPCDefaultRegion)

	vip, err := dao.AllocateSLBNetworkIP(engine, string(db.VirtualIP))
	if err != nil {
		logrus.Fatal(err)
	}
	logrus.Infof("vip:%+v", vip)

	ip, err := dao.AllocateSLBNetworkIP(engine, string(db.Communication))
	if err != nil {
		logrus.Fatal(err)
	}
	logrus.Infof("ip:%+v", ip)
}

func metricsNameGen(strs ...string) string {
	return strings.Join(strs, "__")
}

func main() {
	allocateIp()
}
