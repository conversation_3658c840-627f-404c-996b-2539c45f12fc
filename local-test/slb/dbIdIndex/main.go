package main

import (
	"fmt"
	"os"
	"strings"

	_ "github.com/lib/pq"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"xorm.io/xorm"
)

var rpConfig *config.Config

func init() {
	rpConfig = config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))
}

func index(dbTableName string) {
	pgInfo := fmt.Sprintf("host=%s port=%v user=%s "+
		"password=%s dbname=%s sslmode=disable",
		rpConfig.PG.Host, rpConfig.PG.Port, rpConfig.PG.User, rpConfig.PG.Password, rpConfig.PG.DB)
	fmt.Println(pgInfo)
	engineOrigin, err := xorm.NewEngine("postgres", pgInfo)
	if err != nil {
		logrus.Fatal(err)
	}
	engineOrigin.ShowSQL(true)

	engine := db.NewEngineWrapper(engineOrigin)
	exporter.Exporter = exporter.NewExporter(rpConfig)
	constLabel := map[string]string{"env": "dev", "region": "sh-regional", "az": "sh-01", "prp": "a"}
	dbRTLabelKeys := []string{"operation__name"}
	domain := "boson"
	app := "provider"
	exporter.DBRequestRT = prometheus.NewSummaryVec(prometheus.SummaryOpts{
		Name:        metricsNameGen(domain, app, "db_request_rt", "ms"),
		Help:        "db response time, unit ms",
		Objectives:  utils.Objectives,
		ConstLabels: constLabel,
	}, dbRTLabelKeys)

	db.SetAZ(rpConfig.Boson.VPCDefaultAZ)
	db.SetRegion(rpConfig.Boson.VPCDefaultRegion)

	dbTable, ok := db.BosonTables[dbTableName]
	if !ok {
		fmt.Printf("db:%s not exist", dbTableName)
		return
	}
	err = engine.Sync(dbTable)
	if err != nil {
		fmt.Printf("db:%s sync error:%s", dbTableName, err)
	}
}

func metricsNameGen(strs ...string) string {
	return strings.Join(strs, "__")
}

func main() {
	index(os.Args[1])
}
