测试与net-controller相关操作

# 工具准备
`https://ones.ainewera.com/wiki/#/team/JNwe8qUX/space/3gEw3p1k/page/6ThpyiZN`

# 环境搭建
```
dev11

kubectl label nodes host-10-111-41-11-************ diamond.sensetime.com/role-business-app=sensecore

kubectl label nodes host-10-111-41-13-************ diamond.sensetime.com/role-business-app=sensecore




# provider
helm pull boson/boson-provider --version 1.8.0-slb-20231123160824
helm install boson-provider boson-provider-1.8.0-slb-20231123160824.tgz -f provider.values.yaml

helm delete boson-provider


# net-controller
helm pull boson/boson-net-controller --version 1.8.0-slb-20231123160907

helm install boson-net-controller boson-net-controller-1.8.0-slb-20231123160907.tgz -f net-controller.values.yaml

helm upgrade boson-net-controller boson-net-controller-1.8.0-slb-20231123160907.tgz -f net-controller.values.yaml

helm delete boson-net-controller





```

SlbExternalIpPools
```
# 选择的 基础网络IP 信息
boson_service_dev44=> select * from nat_gateway_external_ip_pools where nat_gateway_id like '%flynn%';
        nat_gw_external_ip_id         | nat_gw_external_ip | description | allocated |   nat_gateway_id   |               cid
r_id
--------------------------------------+--------------------+-------------+-----------+--------------------+------------------
--------------------
 e0d1ba95-4666-4273-8404-f0dab60380e7 | *************      |             | t         | flynnzhang-slb-poc | 9d66b6f4-b464-4eaa-9ea6-e060a3c33b11
(1 row)

insert into slb_external_ip_pools (slb_external_ip_id,slb_external_ip,description,allocated,slb_id,cidr_id,type) values ('1b851646-1250-403a-9718-becf58c3eb01', '*************', '', false, '', '9d66b6f4-b464-4eaa-9ea6-e060a3c33b11', '');
insert into slb_external_ip_pools (slb_external_ip_id,slb_external_ip,description,allocated,slb_id,cidr_id,type) values ('1b851646-1250-403a-9718-becf58c3eb02', '*************', '', false, '', '9d66b6f4-b464-4eaa-9ea6-e060a3c33b11', '');
insert into slb_external_ip_pools (slb_external_ip_id,slb_external_ip,description,allocated,slb_id,cidr_id,type) values ('1b851646-1250-403a-9718-becf58c3eb03', '*************', '', false, '', '9d66b6f4-b464-4eaa-9ea6-e060a3c33b11', '');
insert into slb_external_ip_pools (slb_external_ip_id,slb_external_ip,description,allocated,slb_id,cidr_id,type) values ('1b851646-1250-403a-9718-becf58c3eb04', '*************', '', false, '', '9d66b6f4-b464-4eaa-9ea6-e060a3c33b11', '');
insert into slb_external_ip_pools (slb_external_ip_id,slb_external_ip,description,allocated,slb_id,cidr_id,type) values ('1b851646-1250-403a-9718-becf58c3eb05', '*************', '', false, '', '9d66b6f4-b464-4eaa-9ea6-e060a3c33b11', '');
insert into slb_external_ip_pools (slb_external_ip_id,slb_external_ip,description,allocated,slb_id,cidr_id,type) values ('1b851646-1250-403a-9718-becf58c3eb06', '*************', '', false, '', '9d66b6f4-b464-4eaa-9ea6-e060a3c33b11', '');
insert into slb_external_ip_pools (slb_external_ip_id,slb_external_ip,description,allocated,slb_id,cidr_id,type) values ('1b851646-1250-403a-9718-becf58c3eb07', '*************', '', false, '', '9d66b6f4-b464-4eaa-9ea6-e060a3c33b11', '');
insert into slb_external_ip_pools (slb_external_ip_id,slb_external_ip,description,allocated,slb_id,cidr_id,type) values ('1b851646-1250-403a-9718-becf58c3eb08', '*************', '', false, '', '9d66b6f4-b464-4eaa-9ea6-e060a3c33b11', '');
insert into slb_external_ip_pools (slb_external_ip_id,slb_external_ip,description,allocated,slb_id,cidr_id,type) values ('1b851646-1250-403a-9718-becf58c3eb09', '*************', '', false, '', '9d66b6f4-b464-4eaa-9ea6-e060a3c33b11', '');
insert into slb_external_ip_pools (slb_external_ip_id,slb_external_ip,description,allocated,slb_id,cidr_id,type) values ('1b851646-1250-403a-9718-becf58c3eb010', '*************', '', false, '', '9d66b6f4-b464-4eaa-9ea6-e060a3c33b11', '');

```


# 测试路由脚本
```
# boson.sensetime.com/slb-basic-network-ip: *************
# boson.sensetime.com/slb-basic-network-vip: *************
# boson.sensetime.com/slb-internet-vip: ""
# boson.sensetime.com/slb-intranet-vip: 127.0.0.1

# init(***********/21)
bash ip-tool.sh init ***********/21

# basic_add(*************/24,***********,24,***********)
bash ip-tool.sh basic-add *************/24,***********,24,***********
# bash ip-tool.sh basic-add *************/24,***********,24,***********


# basic_del(*************/24)
bash ip-tool.sh basic-del *************/24
# bash ip-tool.sh basic-del *************/24

```

# test
## 查看 rpc 服务符合预期，包含 slb
```
[root@HOST-10-111-38-29 zhangfeng5]#  kubectl -n plat-boson-service get service --selector=app-name=boson-provider-service --no-headers -o custom-columns=ServiceIP:.spec.clusterIP,GrpcPort:.spec.ports[1].port | awk '{print $1":"$2}'
*************:51090
[root@HOST-10-111-38-44 zhangfeng5]# grpcurl -plaintext *************:51090 list
grpc.reflection.v1alpha.ServerReflection
sensetime.core.network.eip.v1.ACLs
sensetime.core.network.eip.v1.DNATRules
sensetime.core.network.eip.v1.EIPs
sensetime.core.network.ipa.v1.IPAs
sensetime.core.network.slb.v1.SLBDataService
sensetime.core.network.vpc.v1.VPCs
```

## 创建 EIP

## 查看 EIP

## 列举 EIP
```
grpcurl -plaintext -d '{}' *************:51090 sensetime.core.network.eip.v1.EIPs.ListEIPs
```

## 修改 EIP

## 删除 EIP

## 创建 SLB
### without EIP
http://rocketmq-ui.sensecore.com/#/topic


### with EIP

## 查看 SLB
grpcurl -plaintext -d '{"zone":"cn-sh-01a","slbName":"slb-flynn-1-738a0005"}' *************:51090 sensetime.core.network.slb.v1.SLBDataService.GetSLBStatus
## 列举 SLB
### grpcurl
grpcurl -plaintext -d '{"zone":"cn-sh-01a"}' *************:51090 sensetime.core.network.slb.v1.SLBDataService.ListSLBsStatus
grpcurl -plaintext -d '{"zone":"cn-sh-01a","slb_names":["slb_names=slb-xuelionly-fbeaa8c4"]}' *************:51090 sensetime.core.network.slb.v1.SLBDataService.ListSLBsStatus

### all ok
```
grpcurl -plaintext -d '{"zone":"cn-sh-01a"}' *************:51090 sensetime.core.network.slb.v1.SLBDataService.ListSLBsStatus
```

### with tenantID ok
```
grpcurl -H 'x-tenant-id:8d2c81fc-717c-434e-898a-a572738a0002' -plaintext -d '{"zone":"cn-sh-01a"}' *************:51090 sensetime.core.network.slb.v1.SLBDataService.ListSLBsStatus
```

## 监听器
### 创建监听器
```
grpcurl -plaintext -d '{
    "subscription_name":"8d2c81fc-717c-434e-898a-a572738a0005",
    "resource_group_name":"default",
    "zone":"cn-sh-01a",
    "slbName":"slb-flynn-1-738a0005",
    "listener_name":"slb-l-flynn-001",
    "listener": {
        "id":"/subscriptions/8d2c81fc-717c-434e-898a-a572738a0005/resourceGroups/default/zones/cn-sh-01a/slbs/slb-flynn-1-738a0005/listeners/slb-l-flynn-001",
        "uid":"ff7d1450-8cf1-11ee-a356-4ec738a0005",
        "name":"slb-l-flynn-001",
        "display_name":"slb-l-flynn-001",
        "description":"test",
        "resource_type":"network.slb.v1.listener",
        "creator_id":"c0c6c1a8-409f-4e69-8fc9-769d1eebb467",
        "owner_id":"c0c6c1a8-409f-4e69-8fc9-769d1eebb467",
        "tenant_id":"8d2c81fc-717c-434e-898a-a572738a0005",
        "zone":"cn-sh-01a",
        "properties":{
            "slb_id":"ff7d1450-8cf1-11ee-a356-4ec738a0005",
            "protocol":"TCP",
            "port": 8888,
            "default_forward_action":{
                "type":"FORWARD_TARGET_GROUP",
                "forward_target_group_config":{}
            }
        }
    }
}' *************:51090 sensetime.core.network.slb.v1.SLBDataService.CreateListener
```

### 更新监听器
```
grpcurl -plaintext -d '{
    "subscription_name":"8d2c81fc-717c-434e-898a-a572738a0005",
    "resource_group_name":"default",
    "zone":"cn-sh-01a",
    "slbName":"slb-flynn-1-738a0005",
    "listener_name":"slb-l-flynn-001",
    "listener": {
        "uid":"ff7d1450-8cf1-11ee-a356-4ec738a0005",
        "description":"test",
        "zone":"cn-sh-01a",
        "properties":{
            "slb_id":"ff7d1450-8cf1-11ee-a356-4ec738a0005",
            "port": 9999
        }
    }
}' *************:51090 sensetime.core.network.slb.v1.SLBDataService.UpdateListener
```



# mq 测试
```
./mq *************:9876 ***********:9876 \
sensetime-core-network-v1-az1-consumer-slb-test \
sensetime-core-networkSlb-v1-cn-sh-01z \
rocketmq-ak-boson-provider \
e0dde8b7-ec05-07a4-c7bf-4fdab42cdbce \
sensetime-core-network-slb-v1-cn-sh-01a
```

# db 更新
```
select slb_count,slb_listener_count,slb_target_group_count,slb_target_count from vpc_quota ;

UPDATE vpc_quota
SET slb_count = 50,
    slb_listener_count = 50,
    slb_target_group_count = 50,
    slb_target_count = 50;


```
