package main

import (
	"fmt"
	"strings"

	_ "github.com/lib/pq"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"xorm.io/xorm"
)

var rpConfig *config.Config

func init() {
	rpConfig = config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))
}

func updateTarget() {
	pgInfo := fmt.Sprintf("host=%s port=%v user=%s "+
		"password=%s dbname=%s sslmode=disable",
		rpConfig.PG.Host, rpConfig.PG.Port, rpConfig.PG.User, rpConfig.PG.Password, rpConfig.PG.DB)
	fmt.Println(pgInfo)
	engineOrigin, err := xorm.NewEngine("postgres", pgInfo)
	if err != nil {
		logrus.Fatal(err)
	}
	engineOrigin.ShowSQL(true)

	engine := db.NewEngineWrapper(engineOrigin)
	exporter.Exporter = exporter.NewExporter(rpConfig)
	constLabel := map[string]string{"env": "dev", "region": "sh-regional", "az": "sh-01", "prp": "a"}
	dbRTLabelKeys := []string{"operation__name"}
	domain := "boson"
	app := "provider"
	exporter.DBRequestRT = prometheus.NewSummaryVec(prometheus.SummaryOpts{
		Name:        metricsNameGen(domain, app, "db_request_rt", "ms"),
		Help:        "db response time, unit ms",
		Objectives:  utils.Objectives,
		ConstLabels: constLabel,
	}, dbRTLabelKeys)

	db.SetAZ(rpConfig.Boson.VPCDefaultAZ)
	db.SetRegion(rpConfig.Boson.VPCDefaultRegion)

	target := &db.SlbTargets{
		TargetID:      "1013f8dc-29ba-40d1-bdaf-b435a71443ba",
		Name:          "app-0ejvz82l-84bd98796d-wvfg8",
		Zone:          "cn-sh-01a",
		State:         "REMOVED",
		DisplayName:   "",
		Description:   "",
		ResourceType:  "network.slb.v1.target",
		SlbID:         "35468dce-1e51-11ef-8d84-8e31ade4d6f2",
		TargetGroupID: "b3e8a9e9-6573-4b26-b14e-7e9f40d6e38b",
		IpAddress:     "***********",
		Port:          4455,
		Weight:        0,
		Type:          "CCI_DEPLOYMENT_INSTANCE",
		InstanceName:  "app-0ejvz82l-84bd98796d-wvfg8",
		ID:            "subscriptions/acp/resourceGroups/default/zones/cn-sh-01a/slbs/slb-acptest-de7f6e3a/targetGroups/slbtg-acptest-a6bfc20e/targets/app-0ejvz82l-84bd98796d-wvfg8",
		CreatorID:     "01bf84bf-92e0-4774-9e36-c31f852d28c8",
		OwnerID:       "01bf84bf-92e0-4774-9e36-c31f852d28c8",
		TenantID:      "d1f3a37a-995b-409d-8001-97a5f48a88da",
		Deleted:       false,
	}
	err = dao.UpdateTarget(engine, target)
	if err != nil {
		logrus.Fatal(err)
	}
}

func metricsNameGen(strs ...string) string {
	return strings.Join(strs, "__")
}

func main() {
	updateTarget()
}
