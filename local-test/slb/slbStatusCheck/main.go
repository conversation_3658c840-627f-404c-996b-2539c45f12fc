package main

import (
	"fmt"
	"io/ioutil"
	"os"

	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	"sigs.k8s.io/yaml"
)

func slbStatusValid(newSlb *netv1.SLB) bool {
	if newSlb == nil {
		return false
	}

	// listeners
	{
		specListenerMap := make(map[string]bool)
		statusListenerMap := make(map[string]bool)
		for _, specListener := range newSlb.Spec.Listeners {
			specListenerMap[specListener.ListenerID] = true
		}
		for _, statusListener := range newSlb.Status.ListenerStatus {
			statusListenerMap[statusListener.Spec.ListenerID] = true
		}
		if !mapKeyEqual(specListenerMap, statusListenerMap) {
			return false
		}
	}

	// targetGroups
	specTgMapForT := make(map[string]*netv1.TargetGroup)
	statusTgMapForT := make(map[string]*netv1.TargetGroupStatus)
	{
		specTgMap := make(map[string]bool)
		statusTgMap := make(map[string]bool)
		for i, specTg := range newSlb.Spec.TargetGroups {
			specTgMapForT[specTg.TargetGroupID] = &newSlb.Spec.TargetGroups[i]
			specTgMap[specTg.TargetGroupID] = true
		}
		for i, statusTg := range newSlb.Status.TargetGroupStatus {
			statusTgMapForT[statusTg.Spec.TargetGroupID] = &newSlb.Status.TargetGroupStatus[i]
			statusTgMap[statusTg.Spec.TargetGroupID] = true
		}
		if !mapKeyEqual(specTgMap, statusTgMap) {
			return false
		}
	}
	// targets
	{
		for _, specTg := range newSlb.Spec.TargetGroups {
			statusTg := statusTgMapForT[specTg.TargetGroupID]

			specTargetMap := make(map[string]bool)
			statusTargetTMap := make(map[string]bool)
			for _, specT := range specTg.Targets {
				if specT != nil {
					specTargetMap[specT.TargetID] = true
				}
			}
			for _, statusT := range statusTg.TargetStatus {
				statusTargetTMap[statusT.Spec.TargetID] = true
			}

			if !mapKeyEqual(specTargetMap, statusTargetTMap) {
				return false
			}
		}
	}

	return true
}

func mapKeyEqual(m1 map[string]bool, m2 map[string]bool) bool {
	fmt.Printf("\nm1:%+v\nm2:%+v\n", m1, m2)
	if len(m1) != len(m2) {
		return false
	}

	for k, _ := range m1 {
		if _, ok := m2[k]; !ok {
			return false
		}
	}

	return true
}

func main() {
	data, err := ioutil.ReadFile(os.Args[1])
	if err != nil {
		panic(err)
	}

	slb := &netv1.SLB{}
	err = yaml.Unmarshal(data, slb)
	if err != nil {
		panic(err)
	}

	if slbStatusValid(slb) {
		fmt.Printf("valid")
	} else {
		fmt.Printf("invalid")
	}
}
