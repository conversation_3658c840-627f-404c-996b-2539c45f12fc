package main

import (
	"context"
	"fmt"
	"os"

	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/consumer"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	// "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
)

/*
var rpconf *config.Config

func init() {
	rpconf = config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))
}

func test1() {
	groupName := os.Args[1]

	c, err := rocketmq.NewPushConsumer(
		consumer.WithNameServer(rpconf.MQ.Default.NameServer),
		consumer.WithGroupName(groupName),
		consumer.WithConsumerModel(consumer.Clustering),
		consumer.WithInstance(rpconf.MQ.Default.InstanceName),
		consumer.WithConsumerOrder(true),
		consumer.WithCredentials(primitive.Credentials{
			AccessKey: rpconf.MQ.Default.AccessKey,
			SecretKey: rpconf.MQ.Default.SecretKey,
		}),
	)
	if err != nil {
		fmt.Printf("NewPushConsumer err:%s\n", err)
		return
	}

	err = c.Subscribe(
		rpconf.MQ.Default.SLBTopic,
		consumer.MessageSelector{},
		func(ctx context.Context, ext ...*primitive.MessageExt) (consumer.ConsumeResult, error) {
			fmt.Printf("recv ...")
			return consumer.ConsumeSuccess, nil
		},
	)
	if err != nil {
		fmt.Printf("Subscribe err:%s\n", err)
		return
	}

	err = c.Start()
	if err != nil {
		fmt.Printf("Start err:%s\n", err)
		return
	}

	fmt.Printf("Start success\n")

	for {

	}
}
*/

func test2() {
	nameServer1 := os.Args[1]
	nameServer2 := os.Args[2]
	nameServers := []string{nameServer1, nameServer2}

	cusumerGroup := os.Args[3]

	instanceName := os.Args[4]

	ak := os.Args[5]
	sk := os.Args[6]

	c, err := rocketmq.NewPushConsumer(
		consumer.WithNameServer(nameServers),
		consumer.WithGroupName(cusumerGroup),
		consumer.WithConsumerModel(consumer.Clustering),
		consumer.WithInstance(instanceName),
		consumer.WithConsumerOrder(true),
		consumer.WithCredentials(primitive.Credentials{
			AccessKey: ak,
			SecretKey: sk,
		}),
	)
	if err != nil {
		println("cunsumer err", err.Error())
		return
	}

	topic := os.Args[7]
	err = c.Subscribe(topic, consumer.MessageSelector{}, cunsumerHandler)
	if err != nil {
		println("sub err", err.Error())
		return
	}

	err = c.Start()
	if err != nil {
		println("start err", err.Error())
		return
	}

	for {
	}
}

func cunsumerHandler(ctx context.Context, ext ...*primitive.MessageExt) (consumer.ConsumeResult, error) {
	fmt.Printf("Received a message: %+v\n", ext)
	return consumer.ConsumeSuccess, nil
}

func main() {
	// fmt.Printf("test1 :%p\n", test1)

	fmt.Printf("%+v", os.Args)
	test2()
}
