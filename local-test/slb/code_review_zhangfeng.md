# 业务逻辑
1、创建slb实例
.1 带eip
.2 不带eip

2、更新slb实例
.1 新增eip
.2 切换eip
.3 解绑eip

3、删除slb实例
.1 删除带eip
.2 删除不带eip

4、创建监听器
.1 不带后端组
.2 带后端组

5、更新监听器
.1 绑定后端组
.2 切换后端组
.3 解绑后端组

6、删除监听器
.1 带后端组
.2 不带后端组

7、创建后端组
.1 不带后端
.2 带后端

8、更新后端组
.1 新增后端
.2 更新后端 1,2,3 -> 1,2,4 ; 1,2,3 -> a,b,c
.3 清理后端

9、删除后端组
.1 带后端
.2 不带后端

10、更新后端
.1 ip
.2 端口

# net-controller
## 功能点
1、provider-controller 重启，同步状态
2、net-controller 重启
.1 slb_controller 的处理正常，不会因为创建事件出现异常
.2 lastSpec 逻辑需要关注，看会不会有问题
.3 xdsController 需要重建缓存
3、provider 的监控告警
4、db 里面 zone 字段的处理，避免出现多环境共用资源的时候，出错
.1 provider
.2 net-controller
5、provider 的资源更新逻辑，`判定是否更新` 这个问题的解决方案，记录到设计文档，注释在code里面
6、对接云管的消息结构体，callback_data 的逻辑有些问题，核心在于 租户code 等信息，在update的时候没有，看怎么处理
7、provider 分配 ip 的时候，都没有加锁，是否会有问题，需要分析
8、cr:slb 中记录了 slb实例、监听器、后端组、后端 四个资源的信息，相互之间的状态，不能影响
9、envoy_controller 的内部信息需要暴露出来
10、envoy 本身的健康检查

## 疑似 bug
1、EIP直接删除、SLB直接删除 可能有问题，主要是集中在EIP的fw配置
2、slb类型的EIP创建的时候，可能不需要配置snat规则
3、SLB 的 CR 直接删除，会卡住，需要看下
ok 4、额外的日志
```
2024-01-18T04:50:28.696 error   slbDp   /workspace/controllers/slb_envoy/envoy_controller.go:254        gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/controllers/slb_envoy.(*SLBEnvoyController).PodUpdate deploy controller get pod:slb-acptest-bed69be5-6f8475b5dd-wbkzd slb name fail, k8s client get error
        {"error": "SLB.network.sensecore.cn \"slb-acptest-bed69be5\" not found"}
gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/controllers/slb_envoy.(*SLBEnvoyController).PodUpdate
        /workspace/controllers/slb_envoy/envoy_controller.go:254
gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/controllers/network.(*EnqueueVpcPodHandler).Update
        /workspace/controllers/network/pod_controller.go:317
sigs.k8s.io/controller-runtime/pkg/source/internal.EventHandler.OnUpdate
        /go/pkg/mod/sigs.k8s.io/controller-runtime@v0.11.0/pkg/source/internal/eventsource.go:94
k8s.io/client-go/tools/cache.(*processorListener).run.func1
        /go/pkg/mod/github.com/lvxiafei/client-go@v0.23.2-0.20231117124200-6789a9711c64/tools/cache/shared_informer.go:785
k8s.io/apimachinery/pkg/util/wait.BackoffUntil.func1
        /go/pkg/mod/k8s.io/apimachinery@v0.23.1/pkg/util/wait/wait.go:155
k8s.io/apimachinery/pkg/util/wait.BackoffUntil
        /go/pkg/mod/k8s.io/apimachinery@v0.23.1/pkg/util/wait/wait.go:156
k8s.io/apimachinery/pkg/util/wait.JitterUntil
        /go/pkg/mod/k8s.io/apimachinery@v0.23.1/pkg/util/wait/wait.go:133
k8s.io/apimachinery/pkg/util/wait.Until
        /go/pkg/mod/k8s.io/apimachinery@v0.23.1/pkg/util/wait/wait.go:90
k8s.io/client-go/tools/cache.(*processorListener).run
        /go/pkg/mod/github.com/lvxiafei/client-go@v0.23.2-0.20231117124200-6789a9711c64/tools/cache/shared_informer.go:781
k8s.io/apimachinery/pkg/util/wait.(*Group).Start.func1
        /go/pkg/mod/k8s.io/apimachinery@v0.23.1/pkg/util/wait/wait.go:73
```

pass 放弃 5、dnat的日志
这个问题不太好改，需要增加入口检测，只在必要更新的时候，再输出日志
```
2024-01-18T04:50:12.251 info    /go/pkg/mod/github.com/lvxiafei/client-go@v0.23.2-0.20231117124200-6789a9711c64/tools/cache/shared_informer.go:785  k8s.io/client-go/tools/cache.(*processorListener).run.func1     Update service  {"controller": "dnat", "handler": "service", "service": "ns-acptest-22ecc49f/app-ams068af", "oldInstanceName": "app-ams068af", "newInstanceName": "app-ams068af"}
```

6、envoy ip-tool.sh init 的时候，增加一条路由；相关的 envoy 镜像，net-controller 都需要调整
```
ip route add *************/32 dev net1 via ***********

# 完整初始化
bash ip-tool.sh init ***********/21 ************* ***********
bash ip-tool.sh basic-add *************/24,***********,24,***********
```

7、记录是否初始化的标签不能打在pod上，需要直接打在 deployment 上，不然pod重启后，消失了

8、unittest 的代码框架，找一个整一下

ok 9、这一段代码有冗余 VIPs 和 InternetVip、BasicNetworkVip
```
		listenerConfig := &apis.SLBListenerConfig{
			SlbType:         slb.Spec.InstanceType,
			InternetVip:     slb.Spec.IPs.InternetVIP,
			BasicNetworkVip: slb.Spec.IPs.BasicNetworkVIP,
			VIPs:            vips,
			Listener:        listener,
		}
```

10、xxx
.1 更新 crd、DB
.2 更新 provider
.3 更新 net-controller, envoyImage: registry.sensetime.com/sensecore-boson/boson-envoy-dataplane:v1.27.0-2-g7443cdec1e-20240119161756


11、删除没有清理 deployment

12、createListenerCheck 这个函数的日志有问题

13、保存监听器后，net-controller 重建有问题

14、所有的配置，存一份到 db 吧，这里的处理太复杂了，回头再看怎么优化

15、pod 的更新 有问题，按照 slb 的需要重新写一次

16、删除还是有点问题


17、试一下看看不配置内网看会不会挂

18、 删除有点慢，看下为啥

19、删除没有删除干净，出现如下问题，怀疑是哪个地方没有删完
```
boson_service_dev44=> select * from slb_envoy_resource order by version desc limit 1;
 version |                               resource                                |     create_time     |     update_time

---------+-----------------------------------------------------------------------+---------------------+--------------------
-
     172 | configs:                                                             +| 2024-01-30 09:01:35 | 2024-01-30 09:01:35
         |   slb-acptest-00089999:                                              +|                     |
         |     nodeID: slb-acptest-00089999                                     +|                     |
         |     version: 28                                                      +|                     |
         |   slb-acptest-00099999:                                              +|                     |
         |     nodeID: slb-acptest-00099999                                     +|                     |
         |     version: 12                                                      +|                     |
         |   slb-acptest-10099999:                                              +|                     |
         |     nodeID: slb-acptest-10099999                                     +|                     |
         |     version: 34                                                      +|                     |
```

20、curl http://*************:19000/listeners?format=json 这个也能成功，有问题，需要处理
