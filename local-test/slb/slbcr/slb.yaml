apiVersion: network.sensecore.cn/v1
kind: SLB
metadata:
  annotations:
    boson.sensetime.com/az: cn-sh-01a
    boson.sensetime.com/region: cn-sh-01z
    orderid: c8192009631643157622784
    tenantid: d1f3a37a-995b-409d-8001-97a5f48a88da
    userid: 01bf84bf-92e0-4774-9e36-c31f852d28c8
  creationTimestamp: "2024-01-17T11:38:42Z"
  ownerReferences:
  - apiVersion: kubeovn.io/v1
    blockOwnerDeletion: true
    controller: true
    kind: Vpc
    name: vpc-acptest-22562dce
    uid: 8302ec88-e0d8-4da8-ad43-47bc2ebb183c
  name: slb-acptest-00005999
spec:
  name: slb-acptest-00005999
  capacityLimitation:
    tcpConns: 50000
    tcpCps: 5000
    udpConns: 50000
    udpCps: 5000
  instanceType: INTERNET
  ips:
    basicNetworkIPs:
    - *************/24
    basicNetworkIpGw: ***********
    basicNetworkVIP: *************/24
    basicNetworkVipGw: ***********
    internetVIP: *********
    intranetVIP: 127.0.0.1
  listeners:
  - listenerID: 12345678-1234-1234-1234-000000000001
    protocol: TCP
    port: 8888
    defaultForwardAction:
      actionType: FORWARD_TARGET_GROUP
      forwardTargetGroupConfig:
        targetGroupIDs:
        - 12345678-1234-1234-1234-000000000001
  - listenerID: 12345678-1234-1234-1234-000000000002
    protocol: TCP
    port: 9999
    defaultForwardAction:
      actionType: FORWARD_TARGET_GROUP
      forwardTargetGroupConfig:
        targetGroupIDs:
        - 12345678-1234-1234-1234-000000000001
  targetGroups:
  - targetGroupID: 12345678-1234-1234-1234-000000000001
    targetType: CCI_DEPLOYMENT_INSTANCE
    scheduler: RR
    protocol: TCP
    targets:
    - targetID: 12345678-1234-1234-1234-000000000001
      ipAddress: *******
      port: 1111
      instanceName: "t1"
  - targetGroupID: 12345678-1234-1234-1234-000000000002
    targetType: CCI_DEPLOYMENT_INSTANCE
    scheduler: RR
    protocol: TCP
  replica: 1
  subnet: sn-acptest-69d5cf95
  tenantID: d1f3a37a-995b-409d-8001-97a5f48a88da