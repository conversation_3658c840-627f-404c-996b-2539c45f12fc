package main

import (
	"context"
	"fmt"
	"os"

	"io/ioutil"

	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/apache/rocketmq-client-go/v2/producer"
)

func produce() {
	nameServer1 := os.Args[1]
	nameServer2 := os.Args[2]
	nameServers := []string{nameServer1, nameServer2}

	producerGroup := os.Args[3]

	instanceName := os.Args[4]

	ak := os.Args[5]
	sk := os.Args[6]

	data, err := parseData(os.Args[7])
	if err != nil {
		fmt.Printf("parse data err:%s\n", data)
		return
	}

	p, err := rocketmq.NewProducer(
		producer.WithNameServer(nameServers),
		producer.With<PERSON><PERSON><PERSON>(3),
		producer.WithGroupName(producerGroup),
		producer.With<PERSON>ueueSelector(producer.NewHash<PERSON><PERSON>ue<PERSON>elector()),
		producer.With<PERSON><PERSON><PERSON><PERSON><PERSON>(instanceName),
		producer.WithCredentials(primitive.Credentials{
			AccessKey: ak,
			SecretKey: sk,
		}),
	)

	if err != nil {
		fmt.Printf("new producer err:%s\n", err)
		return
	}
	err = p.Start()
	if err != nil {
		fmt.Printf("produce start err:%s\n", err)
		return
	}

	topic := os.Args[8]
	msg := &primitive.Message{
		Topic: topic,
		Body:  data,
	}

	key := os.Args[9]
	msg.WithKeys([]string{key})
	msg.WithShardingKey(key)

	res, err := p.SendSync(context.Background(), msg)
	if err != nil {
		fmt.Printf("sendSync err:%s\n", err)
		return
	}

	if res != nil && res.MessageQueue != nil {
		fmt.Printf("response: %s, %s\n%s\n", res.MessageQueue.Topic, res.MsgID, res.String())
	}
}

func parseData(path string) ([]byte, error) {
	b, err := ioutil.ReadFile(path)
	if err != nil {
		fmt.Printf("parse data err:%s\n", err)
		return nil, err
	}

	return b, nil
}

func main() {
	produce()
}

/*
    # regional的mq连接信息
    rocketmq:
      default:
        nameServer:
        - "*************:9876"
        - "***********:9876"
        instanceName: sensetime-core-networkDefault-v1-cn-sh-01z
        topic: sensetime-core-network-vpc-v1-cn-sh-01z
        eip_topic: sensetime-core-network-eip-v1-cn-sh-01a
        notice_topic: sensetime-core-message-engine-msg
        rmAckTopic: sensetime-core-rm-resource-state-sync
        bossAckTopic: sensetime-core-resource-operation-result
        brokerConsumerGroupName: sensetime-core-network-v1-az1-consumer
        eip_consumer_group_name: sensetime-core-network-v1-az1-consumer-eip
        brokerRMProducerGroupName: sensetime-core-network-v1-az1-producer-rm
        brokerBossProducerGroupName: sensetime-core-network-v1-az1-producer-boss
        brokerNoticeProducerGroupName: sensetime-core-network-v1-az1-producer-Notice
        accessKey: rocketmq-ak-boson-provider
        secretKey: "e0dde8b7-ec05-07a4-c7bf-4fdab42cdbce"
        slb_topic: "sensetime-core-network-slb-v1-cn-sh-01a"
        slb_consumer_group_name: "sensetime-core-network-v1-az1-consumer-slb"


./mqProduce \
*************:9876 ***********:9876 \
sensetime-core-network-v1-az1-producer-rm \
sensetime-core-networkTest-v1-cn-sh-01z \
rocketmq-ak-boson-provider e0dde8b7-ec05-07a4-c7bf-4fdab42cdbce \
test.json \
sensetime-core-rm-resource-state-sync \
testkeyzhangfeng

*/
