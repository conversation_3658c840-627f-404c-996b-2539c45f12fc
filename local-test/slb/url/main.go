package main

import (
	"fmt"
	"net/url"
)

func parse(queryString string) {
	// 解析查询字符串
	values, err := url.ParseQuery(queryString)
	if err != nil {
		fmt.Println("Error parsing query string:", err)
		return
	}

	// 获取所有的值
	slbNames := values["slb_names"]

	// 打印结果
	fmt.Println(queryString)
	for _, name := range slbNames {
		fmt.Printf("name:%s\n", name)
	}
}

func main() {
	parse("slb_names=slb-xuelionly-d2c6f5f6&slb_names=slb-xuelionly-d2c6f5f6")
	parse("slb_names=slb-xuelionly-d2c6f5f6")
	parse("")
}
