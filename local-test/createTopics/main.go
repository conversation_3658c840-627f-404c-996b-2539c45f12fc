package main

import (
	"context"
	"fmt"

	"github.com/apache/rocketmq-client-go/v2/admin"
	"github.com/apache/rocketmq-client-go/v2/primitive"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
)

var rpConfig *config.Config

func init() {
	rpConfig = config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))
}

type RMBody struct {
	Package string     `json:"package"`
	Service string     `json:"service"`
	Method  string     `json:"method"`
	Data    RMBodyData `json:"data"`
}

type RMBodyData struct {
	Parent  string `json:"parent"`
	Example string `json:"example"`
	Nume    int    `json:"nume"`
}

func createTopic() {

	nameservers := rpConfig.MQ.Default.NameServer
	broker := rpConfig.MQ.Default.Broker

	testAdmin, err := admin.NewAdmin(admin.WithResolver(primitive.NewPassthroughResolver(nameservers)))
	if err != nil {
		fmt.Println(err)
		return
	}
	defer testAdmin.Close()

	err = testAdmin.CreateTopic(
		context.Background(),
		admin.WithTopicCreate(rpConfig.MQ.Default.Topic),
		admin.WithOrder(true),
		admin.WithBrokerAddrCreate(broker),
	)

	if err != nil {
		fmt.Println(err)
		return
	}

	err = testAdmin.CreateTopic(
		context.Background(),
		admin.WithTopicCreate(rpConfig.MQ.Default.EIPTopic),
		admin.WithOrder(true),
		admin.WithBrokerAddrCreate(broker),
	)

	if err != nil {
		fmt.Println(err)
		return
	}

	err = testAdmin.CreateTopic(
		context.Background(),
		admin.WithTopicCreate(rpConfig.MQ.Default.RMAckTopic),
		admin.WithOrder(true),
		admin.WithBrokerAddrCreate(broker),
	)

	if err != nil {
		fmt.Println(err)
		return
	}

	err = testAdmin.CreateTopic(
		context.Background(),
		admin.WithTopicCreate(rpConfig.MQ.Default.BossAckTopic),
		admin.WithOrder(true),
		admin.WithBrokerAddrCreate(broker),
	)

	if err != nil {
		fmt.Println(err)
		return
	}

	err = testAdmin.CreateTopic(
		context.Background(),
		admin.WithTopicCreate(rpConfig.MQ.Default.NoticeTopic),
		admin.WithOrder(true),
		admin.WithBrokerAddrCreate(broker),
	)

	if err != nil {
		fmt.Println(err)
		return
	}

	fmt.Println("Created Topics: ", rpConfig.MQ.Default.Topic, rpConfig.MQ.Default.RMAckTopic, rpConfig.MQ.Default.BossAckTopic, rpConfig.MQ.Default.EIPTopic, rpConfig.MQ.Default.NoticeTopic)
}

func main() {
	createTopic()
}
