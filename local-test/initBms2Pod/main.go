package main

import (
	"bufio"
	"context"
	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"k8s.io/apimachinery/pkg/api/errors"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog"
	"os"
	"strings"
)

// readLines reads a whole file into memory
// and returns a slice of its lines.
func readLines(path string) ([]string, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" {
			lines = append(lines, strings.TrimSpace(scanner.Text()))
		}
	}
	return lines, scanner.Err()
}

func main() {
	ch := utils.NewStopChannel()
	rp.BosonProvider = rp.NewResourceProvider()
	rp.BosonProviderConfig = *rp.BosonProvider.RpConfig
	exporter.Exporter = exporter.NewExporter(rp.BosonProvider.RpConfig)
	exporter.Exporter.Serve(ch)

	haNatGwsCli := rp.BosonProvider.BosonNetClient.HaNatGws()
	vpcsCli := rp.BosonProvider.KubeovnClient.Vpcs()
	haNatGws, err := readLines(os.Args[1])
	if err != nil {
		panic(err)
	}

	count := 0
	for _, haNatGwName := range haNatGws {
		// get hanatgw
		haNatGw, err := haNatGwsCli.Get(context.Background(), haNatGwName, v1.GetOptions{})
		if err != nil {
			if errors.IsNotFound(err) {
				continue
			}
			panic(err)
		}
		vpcName := haNatGw.Spec.Vpc
		klog.Infof("handle vpc %s haNatGw: %s start...", vpcName, haNatGwName)

		// get vpc
		vpc, err := vpcsCli.Get(context.Background(), vpcName, v1.GetOptions{})
		if err != nil {
			if errors.IsNotFound(err) {
				continue
			}
			panic(err)
		}
		vpcData, err := dao.GetVpcByName(rp.BosonProvider.Engine, vpcName)
		if err != nil {
			logrus.Errorln(err)
			panic(err)
		}

		err = rp.AddBms2Pod(vpcData, vpc)
		if err != nil {
			logrus.Errorln(err)
			panic(err)
		}

		klog.Infof("handle vpc %s haNatGw: %s success...", vpcName, haNatGwName)
		count++
	}

	klog.Infof("haNatGws total: %d success: %d", len(haNatGws), count)

}
