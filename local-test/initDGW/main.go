package main

import (
	"bufio"
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"k8s.io/apimachinery/pkg/api/errors"
	v1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/klog"
)

// readLines reads a whole file into memory
// and returns a slice of its lines.
func readLines(path string) ([]string, error) {
	file, err := os.Open(path)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	var lines []string
	scanner := bufio.NewScanner(file)
	for scanner.Scan() {
		line := strings.TrimSpace(scanner.Text())
		if line != "" {
			lines = append(lines, strings.TrimSpace(scanner.Text()))
		}
	}
	return lines, scanner.Err()
}

func main() {
	rp.BosonProvider = rp.NewResourceProvider()

	vpcsCli := rp.BosonProvider.KubeovnClient.Vpcs()
	snCli := rp.BosonProvider.KubeovnClient.Subnets()
	dgwCli := rp.BosonProvider.BosonNetClient.DGWs(rp.BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs)

	vpcs, err := readLines(os.Args[1])
	if err != nil {
		panic(err)
	}

	count := 0
	for _, vpcName := range vpcs {
		// get vpc
		vpc, err := vpcsCli.Get(context.Background(), vpcName, v1.GetOptions{})

		if err != nil {
			if errors.IsNotFound(err) {
				continue
			}
			panic(err)
		}
		klog.Infof("handle vpc: %s", vpcName)

		// get subnet
		snName, err := GetServiceSubnet(vpc.Status.Subnets)
		if err != nil {
			panic(fmt.Errorf("get service subnet error %v in the vpc %s", err, vpc.Name))
		}

		sn, err := snCli.Get(context.Background(), snName, v1.GetOptions{})

		if err != nil {
			if errors.IsNotFound(err) {
				continue
			}
			panic(err)
		}

		// 1. Patch Annotion
		localIp, err := utils.CalcTheLastXIPFromCidr(sn.Spec.CIDRBlock, 5)
		if err != nil {
			panic(err)
		}

		if vpc.Annotations == nil {
			vpc.Annotations = map[string]string{
				rp.AnnoDgwLpStime:     time.Now().Format(time.RFC3339),
				rp.AnnoDgwLpIpAddress: localIp,
				rp.AnnoDgwLpCidr:      sn.Spec.CIDRBlock,
			}
		} else {
			vpc.Annotations[rp.AnnoDgwLpStime] = time.Now().Format(time.RFC3339)
			vpc.Annotations[rp.AnnoDgwLpIpAddress] = localIp
			vpc.Annotations[rp.AnnoDgwLpCidr] = sn.Spec.CIDRBlock
		}
		_, err = vpcsCli.Update(context.Background(), vpc, v1.UpdateOptions{})
		if err != nil {
			panic(err)
		}

		if sn.Annotations == nil {
			sn.Annotations = map[string]string{
				rp.AnnoDgwLpIpAddress: localIp,
			}
		} else {
			sn.Annotations[rp.AnnoDgwLpIpAddress] = localIp
		}
		_, err = snCli.Update(context.Background(), sn, v1.UpdateOptions{})
		if err != nil {
			panic(err)
		}

		// check dgw
		for i := 0; i <= 30; i++ {
			if dgw, err := dgwCli.Get(context.Background(), strings.Replace(vpc.Name, "vpc-", "dgw-", 1), v1.GetOptions{}); err != nil {
				if i == 30 {
					klog.Error(err)
					panic(err)
				}

				klog.Infof("create vpc: %s dgw: %s checking...", vpcName, dgw.Name)
				time.Sleep(1 * time.Second)
			} else {
				count++
				klog.Infof("create vpc: %s dgw: %s success", vpcName, dgw.Name)
				break
			}
		}
	}
	klog.Infof(" vpc total: %d success: %d ", len(vpcs), count)

}

func GetServiceSubnet(subnets []string) (string, error) {
	for _, name := range subnets {
		if strings.HasPrefix(name, "sn-") {
			return name, nil
		}
	}

	return "", fmt.Errorf("No service subnet in the  %+v'", subnets)
}
