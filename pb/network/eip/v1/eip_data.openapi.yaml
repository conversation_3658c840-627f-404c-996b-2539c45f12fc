# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: EIPDataService API
    description: "EIP 服务，管理 AZ 内租户 EIP 相关网络资源.\r\n [EN] Service of EIP, for managing EIP related network resources for tenants in AZ."
    version: 0.0.1
paths:
    ? /network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/updateEipDstCidr
    :   patch:
            tags:
                - EIPDataService
            description: "[zh] 企业内网EIP更新dst cidr\r\n [en] Internal EIP update dst cidr"
            operationId: EIPDataService_UpdateEIPDstCIDR
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: 'The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/EIPDstCidrInfo'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/EIPDstCIDR'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbAvailableEips:
        get:
            tags:
                - EIPDataService
            description: "列举符合请求的所有 SLB 类型的 EIPs.\r\n [EN] List requested slb-typed EIPs."
            operationId: EIPDataService_ListSlbTypedAvailableEIPs
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: The maximum number of items to return.
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: The next_page_token value returned from a previous List request, if any.
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListSlbTypedAvailableEIPsResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        ACLProperties:
            type: object
            properties:
                action:
                    type: integer
                    description: '[zh] ACL动作. [en] ACL action.'
                    format: enum
                src:
                    type: string
                    description: '[zh] ACL源地址. [en] ACL source addresses, support multi IPs,IP range or Subnet: ********,********,*********-*********,********/27.'
                dest:
                    type: string
                    description: '[zh] ACL目的地址. [en] ACL destination addresses, reserved now.'
                dest_port:
                    type: string
                    description: '[zh] ACL目的端口，目前保留. [en] ACL destination port, reserved now.'
                protocol:
                    type: string
                    description: '[zh] 对外暴露的协议，目前保留. [en] External protocol, reserved now.'
                priority:
                    type: integer
                    description: '[zh] ACL规则优先级，目前保留. [en] ACL rule priority.'
                    format: int32
            description: '[zh] 资源实际属性. [en] Real resource properties.'
        BillingItems:
            type: object
            properties:
                bw:
                    type: integer
                    description: '[zh] EIP 带宽限制，单位: M/s. [en] eip bandwidth, unit: M/s'
                    format: int32
            description: '[zh] 计费项 [en] billing items'
        EIP:
            type: object
            properties:
                id:
                    type: string
                    description: '[zh] EIP id [en] The EIP resource id using the form:     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}`.'
                name:
                    type: string
                    description: '[zh] EIP名 [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                display_name:
                    type: string
                    description: '[zh] EIP前端展示名 [en] eip resource display name'
                description:
                    type: string
                    description: '[zh] EIP说明 [en] eip resource description'
                uid:
                    type: string
                    description: '[zh] EIP uuid [en] The EIP resource uuid.'
                resource_type:
                    type: string
                    description: '[zh] EIP资源类型 [en] The EIP resource type.'
                creator_id:
                    type: string
                    description: '[zh] 创建该EIP的用户id [en] The id of the user who created the EIP resource.'
                owner_id:
                    type: string
                    description: '[zh] 拥有该EIP资源的用户id [en] The id of the user who owns the EIP resource.'
                tenant_id:
                    type: string
                    description: '[zh] 租户id [en] Tenant id.'
                zone:
                    type: string
                    description: '[zh] 可用区 [en] Available zone.'
                state:
                    type: integer
                    description: '[zh] 当前EIP资源的状态 [en] The current state of the EIP resource.'
                    format: enum
                sku_id:
                    type: string
                    description: '[zh] 最小库存单元id [en] Sku id.'
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: '[zh] EIP资源标签 [en] Tags attached to the EIP resource.'
                properties:
                    $ref: '#/components/schemas/EIPProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
                deleted:
                    type: boolean
                    description: '[zh] EIP是否已删除 [en] Indicates whether the EIP resource is deleted or not.'
                create_time:
                    type: string
                    description: '[zh] EIP资源的创建时间 [en] The time when the EIP resource was created.'
                    format: date-time
                update_time:
                    type: string
                    description: '[zh] EIP资源的更新时间 [en] The time when the EIP resource was last updated.'
                    format: date-time
            description: '[zh] EIP 实例结构体. [en] EIP entity.'
        EIPACL:
            type: object
            properties:
                acl_name:
                    type: string
                    description: '[zh] ACL名称. [en] The name of the ACL.'
                acl_properties:
                    $ref: '#/components/schemas/ACLProperties'
                acl_state:
                    type: string
                    description: '[zh] ACL状态. [en] The state of the ACL.'
            description: '[zh] EIP ACL规则 [en] EIP ACL'
        EIPDstCIDR:
            type: object
            properties:
                eip_name:
                    type: string
                    description: "[zh] EIP 名\r [en] EIP name"
                dst_cidrs:
                    type: string
                    description: "[zh] 企业内网 EIP snat dst cidr\r [en] Internal EIP snat dst cidr"
            description: "[zh] 更新内网EIP dst cidr响应\r [en] update internal EIP dst cidr response"
        EIPDstCidrInfo:
            type: object
            properties:
                dst_cidrs:
                    type: string
                    description: "[zh] EIP snat dst cidr\r [en] EIP snat dst cidr"
        EIPProperties:
            type: object
            properties:
                vpc_id:
                    type: string
                    description: '[zh] VPC的uuid. [en] VPC uid.'
                association_id:
                    type: string
                    description: '[zh] 关联的设备或组件 name, like vpc nat gateway id. [en] Related device or item id, like vpc nat gateway id.'
                association_type:
                    type: integer
                    description: '[zh] 关联的设备或组件类型，NATGW,POD,SLB,BM. [en] Related device type，NATGW,POD,SLB,BM.'
                    format: enum
                resources:
                    $ref: '#/components/schemas/Resources'
                sku:
                    type: string
                    description: '[zh] EIP 类型. [en] EIP sku.'
                default_snat:
                    type: boolean
                    description: '[zh] 是否作为默认 vpc 流量出口. [en] The vpc default snat.'
                acl_enabled:
                    type: boolean
                    description: '[zh] 是否启用黑白名单. [en] EIP acl enabled.'
                acls:
                    type: array
                    items:
                        $ref: '#/components/schemas/EIPACL'
                    description: '[zh] EIP黑白名单规则列表. [en] EIP acl rule list.'
                internal_eip:
                    type: boolean
                    description: '[zh] 是否是企业网EIP. [en] EIP internal flag.'
                snat_dst_cidr_enabled:
                    type: boolean
                    description: '[zh] 是否启用基于目的网段的snat规则. [en] EIP snat enable while access dst cidr.'
            description: '[zh] 资源实际属性. [en] Real resource properties.'
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        LimitRateItems:
            type: object
            properties:
                up_stream_bw:
                    type: integer
                    description: '[zh] EIP 上行带宽限制，单位: M/s. [en] eip upstream bandwidth limit, unit: M/s'
                    format: int32
                down_stream_bw:
                    type: integer
                    description: '[zh] EIP 下行带宽限制，单位: M/s [en] EIP downstream bandwidth limit, unit: M/s'
                    format: int32
            description: '[zh] 限速项 [en] rate limit items'
        ListSlbTypedAvailableEIPsResponse:
            type: object
            properties:
                eips:
                    type: array
                    items:
                        $ref: '#/components/schemas/EIP'
                    description: "EIP 列表.\r [EN] EIP list."
                next_page_token:
                    type: string
                    description: "下一个页面的 token，如果没有更多数据则为空.\r [EN] Token to retrieve the next page of results, or empty if there are no more results in the list."
                total_size:
                    type: integer
                    description: total size
                    format: int32
            description: "列举符合请求的所有 SLB 类型的 EIPs.\r [EN] List requested slb-typed EIPs."
        OrderInfo:
            type: object
            properties:
                billing_cycle_number:
                    type: integer
                    description: '[zh] 购买时长. [en] length of purchase.'
                    format: int32
                auto_renew:
                    type: boolean
                    description: '[zh] 自动续费. [en] Automatic renewal.'
                currency_code:
                    type: string
                    description: '[zh] 货币代码. [en] currency code.'
                payment_channel:
                    type: integer
                    description: '[zh] 支付方式. [en] payment method.'
                    format: enum
                note:
                    type: string
                    description: '[zh] 订单备注. [en] order notes'
                order_type:
                    type: integer
                    description: '[zh] 订单类型. [en] Order Type.'
                    format: enum
                order_id:
                    type: string
                    description: '[zh] 订单id. [en] order id.'
                start_time:
                    type: string
                    description: '[zh] 订单生效日期. [en] Order effective date.'
                    format: date-time
                payment_model:
                    type: integer
                    description: '[zh] 付费类型. [en] payment type.'
                    format: enum
                billing_model:
                    type: integer
                    description: '[zh] 计费类型. [en] billing type.'
                    format: enum
                original_id:
                    type: string
                    description: '[zh] 合同包ID. [en] Contract package ID. original id-- OT_ORIGINAL: original spu_id/package_id; OT_RENEW/OT_UPGRADED/OT_DOWNGRADED: original/ order_id; OT_CONTRACT: original contract_id'
                end_time:
                    type: string
                    description: '[zh] 订单结束时间. [en] Order end time.'
                    format: date-time
            description: '[zh] 订单信息. [en] Order Infomation.'
        Resources:
            type: object
            properties:
                billing_items:
                    $ref: '#/components/schemas/BillingItems'
                limit_rate_items:
                    $ref: '#/components/schemas/LimitRateItems'
            description: '[zh] 资源规格属性 [en] EIP Resources'
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
tags:
    - name: EIPDataService
