syntax = "proto3";
package sensetime.core.network.eip.v1;
option go_package = 'gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1;eip';

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/api/annotations.proto";
import "higgs/common/v1/orderinfo.proto";


// DNATRule 服务，管理 AZ 内租户 DNATRule 相关网络资源.
// [EN] Service of DNATRule, for managing DNATRule related network resources for tenants in AZ.
service DNATRules {

  // 列举符合请求的所有 DNATRules.
  // [EN] List requested DNATRules.
  rpc ListDNATRules (ListDNATRulesRequest) returns (ListDNATRulesResponse) {
    option (google.api.http) = {
      get: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/dnatRules"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.get"
    };
  }

  // 列举符合请求的所有EIP的 DNATRules.
  // [EN] List requested multi-eip DNATRules.
  rpc ListMDNATRules(ListMDNATRulesRequest) returns (ListDNATRulesResponse) {
    option (google.api.http) = {
      get: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dnatRules"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}"
      permission: "eip.eip.get"
    };
  }

  // 获取符合请求的一个 DNATRule.
  // [EN] Get a requested DNATRule.
  rpc GetDNATRule (GetDNATRuleRequest) returns (DNATRule) {
    option (google.api.http) = {
      get: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/dnatRules/{dnat_rule_name}"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.get"
    };
  }

  // 创建一个 DNATRule.
  // [EN] Create a DNATRule.
  rpc CreateDNATRule (CreateDNATRuleRequest) returns (DNATRule) {
    option (google.api.http) = {
      post: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/dnatRules/{dnat_rule_name}"
      body: "dnat_rule"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.update"
    };
  }

  // 绑定一个 DNATRule和计算实例.
  // [EN] bind a DNATRule with a instance.
  rpc BindDNATRule (BindDNATRuleRequest) returns (DNATRule) {
    option (google.api.http) = {
      post: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/dnatRules/{dnat_rule_name}/bind"
      body: "dnat_rule"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.update"
    };
  }

  // 解绑一个 DNATRule.
  // [EN] unbind the DNATRule.
  rpc UnbindDNATRule (UnbindDNATRuleRequest) returns (DNATRule) {
    option (google.api.http) = {
      post: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/dnatRules/{dnat_rule_name}/unbind"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.update"
    };
  }

  // 批量解绑DNATRule.
  // [EN] unbind DNATRules.
  rpc UnbindDNATRules (UnbindDNATRulesRequest) returns (ListDNATRulesResponse) {
    option (google.api.http) = {
      post: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/dnatRules/unbind"
      body: "dnat_rules"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.update"
    };
  }

   // 更新一个 DNATRule.
  // [EN] update the DNATRule.
  rpc UpdateDNATRule (UpdateDNATRuleRequest) returns (DNATRule) {
    option (google.api.http) = {
      patch: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/dnatRules/{dnat_rule_name}"
      body: "dnat_rule"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.update"
    };
  }

  // 删除一个 DNATRule.
  // [EN] Delete a DNATRule.
  rpc DeleteDNATRule (DeleteDNATRuleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/dnatRules/{dnat_rule_name}"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.update"
    };
  }
}

// 列举 DNATRules 的请求.
// [EN] Request to list DNATRules.
message ListDNATRulesRequest {

  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // List filter.
  string filter = 4;

  // Sort resoults.
  string order_by = 5;

  // The maximum number of items to return.
  int32 page_size = 6;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 7;

  // eip_name
  string eip_name = 8;

 }

// 列举 DNATRules 的请求.
// [EN] Request to list multi-eip DNATRules.
message ListMDNATRulesRequest {

  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // List filter.
  string filter = 4;

  // Sort resoults.
  string order_by = 5;

  // The maximum number of items to return.
  int32 page_size = 6;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 7;

  // eip_name
  repeated string eip_names = 8;

 }

// 获取某一个 DNATRule 的请求.
// [EN] Request to get a DNATRule.
message GetDNATRuleRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string dnat_rule_name = 4;

  // eip_name
  string eip_name = 5;
}

// 创建一个 DNATRule 的请求.
// [EN] Request to create a DNATRule.
message CreateDNATRuleRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone, todo: add validation
  string zone = 3;

  // The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string dnat_rule_name = 4 [(validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // The DNATRule resource to create.
  DNATRule dnat_rule = 5;

  // eip_name
  string eip_name = 6;
}

// 绑定一个 DNATRule 的请求.
// [EN] Request to bind a DNATRule with instance.
message BindDNATRuleRequest {
    // Subscription
    string subscription_name = 1;

    // Resource group
    string resource_group_name = 2;

    // Available zone, todo: add validation
    string zone = 3;

    // The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    string dnat_rule_name = 4 [(validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

    // The DNATRule resource to create.
    DNATRule dnat_rule = 5;

    // eip_name
    string eip_name = 6;
}

// 解绑一个 DNATRule 的请求.
// [EN] Request to unbind a DNATRule from instance.
message UnbindDNATRuleRequest {
    // Subscription
    string subscription_name = 1;

    // Resource group
    string resource_group_name = 2;

    // Available zone, todo: add validation
    string zone = 3;

    // The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    string dnat_rule_name = 4 [(validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

    // eip_name
    string eip_name = 5;
  }

// DNATRules 实体结构.
// [EN] the entity of DNATRules.
message DnatRules {
    enum FilterType {
        EIP_IP = 0;
        INTERNAL_IP = 1;
        INTERNAL_INSTANCE_NAME = 2;
        RULE_NAME = 3;
    };

    // Resource filter type
    FilterType filterType = 1;

    // Resource filter parameter
    repeated string filterValues = 2;

}

// 解绑 DNATRules 的请求.
// [EN] Request to unbind all the DNATRules with parameters.
message UnbindDNATRulesRequest {
    // Subscription
    string subscription_name = 1;

    // Resource group
    string resource_group_name = 2;

    // Available zone, todo: add validation
    string zone = 3;

    // eip_name
    string eip_name = 4;

    // The DNATRules resource
    DnatRules dnat_rules = 5;
  }

  // 更新一个 DNATRule 的请求.
  // [EN] Request to update a DNATRule.
  message UpdateDNATRuleRequest {
    // Subscription
    string subscription_name = 1;

    // Resource group
    string resource_group_name = 2;

    // Available zone, todo: add validation
    string zone = 3;

    // The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    string dnat_rule_name = 4 [(validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

    // The DNATRule resource to create.
    DNATRule dnat_rule = 5;

    // eip_name
    string eip_name = 6;
  }

// 删除某一个 DNATRule 的请求.
// [EN] Request to delete a DNATRule.
message DeleteDNATRuleRequest {

  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string dnat_rule_name = 4;

  // eip_name
  string eip_name = 5;

}

// 列举 DNATRules 的响应.
// [EN] Response to list DNATRules.
message ListDNATRulesResponse {

  // DNATRule 列表.
  // [EN] DNATRule list.
  repeated DNATRule dnat_rules = 1;

  // 下一个页面的 token，如果没有更多数据则为空.
  // [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
  string next_page_token = 2;

  // total size
  int32 total_size = 3;

}

// DNATRule 实例结构体.
// [EN] DNATRule entity.
message DNATRule {
  // The DNATRule resource id using the form:
  //     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/dnatRules/{dnat_rule_name}`.
  string id = 1;

  // The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 2;

  // ContainerInstance resource display name
  string display_name = 3 [(validate.rules).string = {ignore_empty: true, pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // ContainerInstance resource description
  string description = 4;

  // The DNATRule resource uuid.
  string uid = 5;

  // The DNATRule resource type.
  string resource_type = 6;

  // The id of the user who created the DNATRule resource.
  string creator_id = 7;

  // The id of the user who owns the DNATRule resource.
  string owner_id = 8;

  // Tenant id.
  string tenant_id = 9;

  // Available zone.
  string zone = 10;

  // Represents the different states of a DNATRule.
  enum State {
    // The DNATRule resource is being created.
    CREATING = 0;

    // The DNATRule resource has been created.
    CREATED = 1;

    // The DNATRule resource is being bound.
    BINDING = 2;

    // The DNATRule resource is being unbound.
    UNBINDING = 3;

    // The DNATRule resource is being updated.
    UPDATING = 4;

    // The DNATRule resource has been active.
    ACTIVE = 5;

    // The DNATRule resource is being deleted.
    DELETING = 6;

    // The DNATRule resource has been deleted.
    DELETED = 7;

    // The DNATRule resource has been failed.
    FAILED = 8;
  };

  // The current state of the DNATRule resource.
  State state = 11;

  // Sku id.
  string sku_id = 12;

  // Tags attached to the DNATRule resource.
  map<string, string> tags = 13;

  // Properties of the DNATRule resource.
  DNATRuleProperties properties = 14;

  // Payment information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 15;

  // Indicates whether the DNATRule resource is deleted or not.
  bool deleted = 16;

  // The time when the DNATRule resource was created.
  google.protobuf.Timestamp create_time = 17;

  // The time when the DNATRule resource was last updated.
  google.protobuf.Timestamp update_time = 18;

}

// 资源实际属性.
// [EN] Real resource properties.
message DNATRuleProperties {

  // 所属的 NATGateway uid.
  // [EN] NATGateway uid.
  string nat_gateway_id = 1;

  // 所属的 EIP uid.
  // [EN] EIP uid.
  string eip_id = 2;

  // VPC 对外暴露的 DNATRule external IP.
  // [EN] DNATRule external IP in VPC.
  string external_ip = 3;

  // 对外暴露的端口,（10000，10000-20000，(10000,10001,20000,20001),*）.
  // [EN] External port,（10000，10000-20000，(10000,10001,20000,20001),*）.
  string external_port = 4;

  // 对外暴露的协议.
  // [EN] External protocol.
  string protocol = 5;

  // 对外暴露的 VPC 内服务的 IP.
  // [EN] Exposed service IP in the VPC.
  string internal_ip = 6;

  // 对外暴露的 VPC 内服务的端口,（10000，10000-20000，(10000,10001,20000,20001),*）.
  // [EN] Exposed service port in the VPC,（10000，10000-20000，(10000,10001,20000,20001),*）.
  string internal_port = 7;

  // 规则优先级.
  // [EN] NAT rule priority.
  int32 priority = 8;

  enum  InstanceType{
    UNSPECIFIED = 0;
    //裸金属实例
    BMS = 1;
    //云开发机
    AILIB =2;
    //云容器实例
    CCI = 3;
    //算力池内嵌的云容器实例
    ACP_CCI = 4;
    //CCI SERVICE实例
    CCI_DEPLOYMENT_SERVICE = 5;
    //云主机
    ECS = 6;
  };

  // 对外暴露的 VPC内服务的实例类型
  // [EN] Exposed service instance type in the VPC
  InstanceType internal_instance_type = 9;

  // 对外暴露的 VPC内服务的实例唯一标识,目前使用实例name作为唯一标识
  // [EN] Exposed service instance name in the VPC
  string internal_instance_name = 10;

}
