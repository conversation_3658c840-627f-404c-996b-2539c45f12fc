syntax = "proto3";
package sensetime.core.network.eip.v1;
option go_package = 'gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1;eip';

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/api/annotations.proto";
import "higgs/common/v1/orderinfo.proto";
import "google/api/field_behavior.proto";
import "network/eip/v1/acl.proto";


// [zh] EIP 服务，管理 AZ 内租户 EIP 相关网络资源.
// [en] Service of EIP, for managing EIP related network resources for tenants in AZ.
service EIPs {

  // [zh] 获取 EIP 实例列表.
  // [en] List requested EIPs.
  rpc ListEIPs (ListEIPsRequest) returns (ListEIPsResponse) {
    option (google.api.http) = {
      get: "/network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}"
      permission: "eip.eip.list"
    };
  }

  // [zh] 查看 EIP 实例详情.
  // [en] Get a requested EIP.
  rpc GetEIP (GetEIPRequest) returns (EIP) {
    option (google.api.http) = {
      get: "/network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.get"
    };
  }

  // [zh] 查看 EIP 状态信息.
  // [en] Get a requested EIP status.
  rpc GetEIPStatus (GetEIPStatusRequest) returns (EIPStatus) {
    option (google.api.http) = {
      get: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/status"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.get"
    };
  }

  // [zh] 创建一个 EIP.
  // [en] Create a EIP.
  rpc CreateEIP (CreateEIPRequest) returns (EIP) {
    option (google.api.http) = {
      post: "/network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      body: "eip"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.create"
    };
  }

  // [zh] 更新 EIP 可编辑字段.
  // [en] Update EIP editable properties.
  rpc UpdateEIP (UpdateEIPRequest) returns (EIP) {
    option (google.api.http) = {
      patch: "/network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      body: "eip"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.update"
    };
  }

  // [zh] 过期停服一个 EIP.
  // [en] ExpireStop a EIP.
  rpc ExpireStopEIP (ExpireStopEIPRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}:expireStop"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.update"
    };
  }

  // [zh] 恢复一个 EIP.
  // [en] Resume a EIP.
  rpc RenewStartEIP (RenewStartEIPRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}:renewStart"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.update"
    };
  }

  // [zh] 在保留期释放一个 EIP.
  // [en] release a EIP.
  rpc ReleaseEIP (ReleaseEIPRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}:release"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.delete"
    };
  }


  // [zh] 强制删除一个 EIP.
  // [en] Force Delete a EIP.
  rpc ForceDeleteEIP (ForceDeleteEIPRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}:forceDelete"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.delete"
    };
  }

 // [zh] 删除一个 EIP.
  // [en] Delete a EIP.
  rpc DeleteEIP (DeleteEIPRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.delete"
    };
  }

  // [zh] EIP 扩缩容.
  // [en] EIP resize.
  rpc ResizeEIP(ResizeEIPRequest) returns (EIP) {
      option (google.api.http) = {
        post : "/network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}:resize"
        body : "eip_resize"
      };
      option (sensetime.core.higgs.api.is_asynchronous) = true;
      option (sensetime.core.higgs.api.is_control_plane) = true;
      option (sensetime.core.higgs.api.policy) = {
        scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
        permission: "eip.eip.resize"
      };
    }

  // [zh] EIP metrics.
  // [en] EIP metrics.
  rpc GetEIPMetrics (GetEIPMetricsRequest) returns (EIPMetrics) {
    option (google.api.http) = {
      get: "/network/eip/data/v1/eips/metrics"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}"
      permission: "eip.eip.list"
    };
  }
}

// [zh] 列举 EIPs 的请求.
// [en] Request to list EIPs.
message ListEIPsRequest {

  // [zh] 订阅
  // [en] Subscription
  string subscription_name = 1;

  // [zh] 资源组
  // [en] Resource group
  string resource_group_name = 2;

  // [zh] 可用区
  // [en] Available zone
  string zone = 3;

  // [zh] eip过滤条件
  // [en] List filter.
  string filter = 4;

  // [zh] eip资源排序规则
  // [en] Sort resoults.
  string order_by = 5;

  // [zh] 分页大小
  // [en] The maximum number of items to return.
  int32 page_size = 6;

  // [zh] 从上一个List请求返回的next_page_token值(如果有的话)
  // [en] The next_page_token value returned from a previous List request, if any.
  string page_token = 7;

  // [zh] 租户id
  // [en] tenant_id.
  string tenant_id = 8;
}

// [zh] 获取某一个 EIP 的请求.
// [en] Request to get a EIP.
message GetEIPRequest {

  // [zh] 订阅
  // [en] Subscription
  string subscription_name = 1;

  // [zh] 资源组
  // [en] Resource group
  string resource_group_name = 2;

  // [zh] 可用区
  // [en] Available zone
  string zone = 3;

  // [zh] eip名
  // [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string eip_name = 4;

}

// [zh] 获取 EIP 状态信息.
// [en] Request to get a EIP status.
message GetEIPStatusRequest {

  // [zh] 订阅
  // [en] Subscription
  string subscription_name = 1;
  // [zh] 资源组
  // [en] Resource group
  string resource_group_name = 2;
  // [zh] 可用区
  // [en] Available zone
  string zone = 3;

  // [zh] eip名
  // [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string eip_name = 4;

}

// [en] CreateEIPRequest.
message CreateEIPRequest {
  // [zh] 订阅
  // [en] Subscription
  string subscription_name = 1;

  // [zh] 资源组
  // [en] Resource group
  string resource_group_name = 2;

  // [zh] 可用区
  // [en] Available zone, todo: add validation
  string zone = 3;

  // [zh] eip名
  // [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string eip_name = 4 [(validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] eip实例
  // [en] The EIP resource to create.
  EIP eip = 5;
}


// [zh] EIP 可编辑字段.
// [en] EIP editable properties.
message UpdateEIPRequest {

  // [zh] 订阅
  // [en] Subscription
  string subscription_name = 1;

  // [zh] 资源组
  // [en] Resource group
  string resource_group_name = 2;

  // [zh] 可用区
  // [en] Available zone
  string zone = 3;

  // [zh] eip名
  // [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string eip_name = 4;

  // [zh] eip实例
  // [en] The EIP resource to update.
  EIP eip = 5;

  // [zh] 要更新的eip字段掩码
  // [en] update_mask
  google.protobuf.FieldMask update_mask = 6;
}

// [zh] 过期停服EIP请求
// [en] ExpireStopEIPRequest
message ExpireStopEIPRequest {

  // [zh] 订阅
  // [en] Subscription
  string subscription_name = 1;

  // [zh] 资源组
  // [en] Resource group
  string resource_group_name = 2;

  // [zh] 可用区
  // [en] Available zone
  string zone = 3;

  // [zh] eip名
  // [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string eip_name = 4;
}

// [en] RenewStartEIPRequest
message RenewStartEIPRequest {

  // [zh] 订阅
  // [en] Subscription
  string subscription_name = 1;

  // [zh] 资源组
  // [en] Resource group
  string resource_group_name = 2;

  // [zh] 可用区
  // [en] Available zone
  string zone = 3;

  // [zh] eip名
  // [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string eip_name = 4;
}

// [zh] 释放EIP请求
// [en] ReleaseEIPRequest
message ReleaseEIPRequest {

  // [zh] 订阅
  // [en] Subscription
  string subscription_name = 1;

  // [zh] 资源组
  // [en] Resource group
  string resource_group_name = 2;

  // [zh] 可用区
  // [en] Available zone
  string zone = 3;

  // [zh] eip名
  // [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string eip_name = 4;

}

// [en] ForceDeleteEIPRequest
message ForceDeleteEIPRequest {

  // [en] Subscription
  string subscription_name = 1;

  // [en] Resource group
  string resource_group_name = 2;

  // [en] Available zone
  string zone = 3;

  // [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string eip_name = 4;

}

// [zh] 删除某一个 EIP 的请求.
// [en] Request to delete a EIP.
message DeleteEIPRequest {

  // [zh] 订阅
  // [en] Subscription
  string subscription_name = 1;

  // [zh] 资源组
  // [en] Resource group
  string resource_group_name = 2;

  // [zh] 可用区
  // [en] Available zone
  string zone = 3;

  // [zh] eip名
  // [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string eip_name = 4;
}

// [zh] EIP 扩容请求.
// [en] Resize EIP request.
message ResizeEIPRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] EIP 名称，需要严格满足正则 `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`
  // [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string eip_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 变更 EIP 资源.
  // [en] Resize EIP resource.
  EIPResize eip_resize = 5 [(google.api.field_behavior) = REQUIRED];
}

// [zh] 变更 EIP 资源.
// [en] Resize EIP body.
message EIPResize {
    // [zh] EIP 资源uuid.
    // [en] EIP resource uuid.
    string resource_id = 1 [(google.api.field_behavior) = REQUIRED];

    // [zh] 最小库存单元id.
    // [en] EIP resource sku_id.
    string sku_id = 2 [(google.api.field_behavior) = REQUIRED];

    // [zh] 变更操作者id.
    // [en] Operator id.
    string operator_id = 3 [(google.api.field_behavior) = REQUIRED];

    // [zh] EIP 属性.
    // [en] EIP properties.
    EIPProperties properties = 4; // [(validate.rules).message.required = true];

    // [zh] 订单信息.
    // [en] Order information.
    sensetime.core.higgs.common.v1.OrderInfo order_info = 5; // [(validate.rules).message.required = true];
}

// [zh] snat status 信息
// [en] snat status info
message SnatStatus {
  // [zh] snat 名字
  // [en] snat name
  string name = 1;

  // [zh] snat 源地址cidr
  // [en] snat inner cidr
  string inner_ip = 2;

  // [zh] snat 目的cidr
  // [en] snat outer cidr
  string outer_ip = 3;

  // [zh] snat policy策略, src/dst/all
  // [en] snat policy
  string policy = 4;

  // [zh] snat policy值
  // [en] snat policy value
  string policy_value = 5;
}

// 获取 EIP 属性的响应.
// [en] Response to get EIP status.
message EIPStatus {

  // EIP 信息.
  // [en] EIP info.
  EIP eip_info = 1;

  // Resource ip
  string eip_ip = 2;

  // 加载的vpc信息.
  // [en] vpc info.
  VPCStatus vpc_info = 3;

  // snat 规则数量
  // [en] snat count.
  int32 snat_count = 4;

  // dnat 规则数量
  // [en] dnat count.
  int32 dnat_count = 5;

  // [zh] snat规则信息
  // [en] snat status info
  repeated SnatStatus snat_info = 6;
}

// EIP 相关 metrics.
// [en] EIP metrics.
message EIPMetrics {
  // resource_metrics.
  repeated ResourceMetrics resource_metrics = 1;
}

// ResourceMetrics
message ResourceMetrics {
  // name.
  string name = 1;

  // value.
  int32 value = 2;

  // unit.
  string unit = 3;

  // color.
  string color = 4;
}

// vpc info.
message VPCStatus {
    // vpc name.
    string name = 1;

    // vpc display name
    string display_name = 2;

    // vpc uid.
    string id = 3;
}

// GetEIPMetricsRequest
message GetEIPMetricsRequest {
}

// [zh] 列举 EIPs 的响应.
// [en] Response to list EIPs.
message ListEIPsResponse {

  // [zh] EIP 列表.
  // [en] EIP list.
  repeated EIP eips = 1;

  // [zh] 下一个页面的 token，如果没有更多数据则为空.
  // [en] Token to retrieve the next page of results, or empty if there are no more results in the list.
  string next_page_token = 2;

  // [zh] 返回的eip实例总数
  // [en] total size
  int32 total_size = 3;

}

// [zh] EIP 实例结构体.
// [en] EIP entity.
message EIP {
  // [zh] EIP id
  // [en] The EIP resource id using the form:
  //     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}`.
  string id = 1;

  // [zh] EIP名
  // [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 2;

  // [zh] EIP前端展示名
  // [en] eip resource display name
  string display_name = 3 [(validate.rules).string = {max_len: 25}];

  // [zh] EIP说明
  // [en] eip resource description
  string description = 4;

  // [zh] EIP uuid
  // [en] The EIP resource uuid.
  string uid = 5;

  // [zh] EIP资源类型
  // [en] The EIP resource type.
  string resource_type = 6;

  // [zh] 创建该EIP的用户id
  // [en] The id of the user who created the EIP resource.
  string creator_id = 7;

  // [zh] 拥有该EIP资源的用户id
  // [en] The id of the user who owns the EIP resource.
  string owner_id = 8;

  // [zh] 租户id
  // [en] Tenant id.
  string tenant_id = 9;

  // [zh] 可用区
  // [en] Available zone.
  string zone = 10;

  // [zh] EIP状态枚举
  // [en] Represents the different states of a EIP.
  enum State {
    // [zh] EIP创建中
    // [en] The EIP resource is being created.
    CREATING = 0;

    // [zh] EIP更新中
    // [en] The EIP resource is being updated.
    UPDATING = 1;

    // [zh] EIP active
    // [en] The EIP resource has been active.
    ACTIVE = 2;

    // [zh] EIP删除中
    // [en] The EIP resource is being deleting.
    DELETING = 3;

    // [zh] EIP已删除
    // [en] The EIP resource has been deleted.
    DELETED = 4;

    // [zh] EIP相关操作失败
    // [en] The EIP resource has been failed.
    FAILED = 5;

    // [zh] EIP挂起中
    // [en] The EIP resource is being suspending.
    SUSPENDING = 6;

    // [zh] EIP已挂起
    // [en] The EIP resource has been suspended.
    SUSPENDED = 7;

    // [zh] EIP恢复中
    // [en] The EIP resource is being resuming.
    RESUMING = 8;

    // [zh] EIP已过期，停止中
    // [en] The EIP resource is being expireStopping.
    EXPIRESTOPPING = 9;

    // [zh] EIP已过期，停止服务
    // [en] The EIP resource has been expireStopped.
    EXPIRESTOPPED = 10;

    // [zh] EIP重新恢复服务
    // [en] The EIP resource is being renewStarting.
    RENEWSTARTING = 11;
  };

  // [zh] 当前EIP资源的状态
  // [en] The current state of the EIP resource.
  State state = 11;

  // [zh] 最小库存单元id
  // [en] Sku id.
  string sku_id = 12;

  // [zh] EIP资源标签
  // [en] Tags attached to the EIP resource.
  map<string, string> tags = 13;

  // [zh] EIP资源属性
  // [en] Properties of the EIP resource.
  EIPProperties properties = 14;

  // [zh] EIP订单信息
  // [en] Payment information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 15;

  // [zh] EIP是否已删除
  // [en] Indicates whether the EIP resource is deleted or not.
  bool deleted = 16;

  // [zh] EIP资源的创建时间
  // [en] The time when the EIP resource was created.
  google.protobuf.Timestamp create_time = 17;

  // [zh] EIP资源的更新时间
  // [en] The time when the EIP resource was last updated.
  google.protobuf.Timestamp update_time = 18;

}

// [zh] 资源实际属性.
// [en] Real resource properties.
message EIPProperties {

  // [zh] VPC的uuid.
  // [en] VPC uid.
  string vpc_id = 1;

  // [zh] 关联的设备或组件 name, like vpc nat gateway id.
  // [en] Related device or item id, like vpc nat gateway id.
  string association_id = 2;

  // [zh] EIP关联类型
  // [en] EIP association type.
  enum AType {
    // [zh] EIP支持网关
    // [en] The EIP resource associate NAT GateWay
    NATGW = 0;

    // [zh] EIP支持POD
    // [en] The EIP resource associate POD.
    POD = 1;

    // [zh] EIP支持SLB
    // [en] The EIP resource associate SLB.
    SLB = 2;

    // [zh] EIP支持裸金属
    // [en] The EIP resource associate SLB.
    BM = 3;

    // [zh] EIP支持网关和裸金属
    // [en] The EIP support NATGW and BM at same time, 1030 default.
    NATGW_AND_BM = 4;
  };

  // [zh] 关联的设备或组件类型，NATGW,POD,SLB,BM.
  // [en] Related device type，NATGW,POD,SLB,BM.
  AType association_type = 3;

  // [zh] EIP资源规格属性
  // [en] Resources
  Resources resources = 4;

  // [zh] EIP 类型.
  // [en] EIP sku.
  string sku = 5;

  // [zh] 是否作为默认 vpc 流量出口.
  // [en] The vpc default snat.
  bool default_snat = 6;

  // [zh] 是否启用黑白名单.
  // [en] EIP acl enabled.
  bool acl_enabled = 7;

  // [zh] EIP黑白名单规则列表.
  // [en] EIP acl rule list.
  repeated EIPACL acls = 8 ;

  // [zh] 是否是企业网EIP.
  // [en] EIP internal flag.
  bool internal_eip = 9;

  // [zh] 是否启用基于目的网段的snat规则.
  // [en] EIP snat enable while access dst cidr.
  bool snat_dst_cidr_enabled = 10;
}

// [zh] EIP ACL规则
// [en] EIP ACL
message EIPACL {
  // [zh] ACL名称.
  // [en] The name of the ACL.
  string acl_name = 1;
  // [zh] ACL属性.
  // [en] The properties of the ACL.
  ACLProperties acl_properties = 2;
  // [zh] ACL状态.
  // [en] The state of the ACL.
  string acl_state = 3;
}

// [zh] 资源规格属性
// [en] EIP Resources
message Resources {
  // [zh] 单独购买的计费项
  // [en] eip billing itmes
  BillingItems billing_items = 1;
  // [zh] 限速项
  // [en] eip limit rate items
  LimitRateItems limit_rate_items = 2;
}

// [zh] 计费项
// [en] billing items
message BillingItems {
  // [zh] EIP 带宽限制，单位: M/s.
  // [en] eip bandwidth, unit: M/s
  int32 bw = 1;
}

// [zh] 限速项
// [en] rate limit items
message LimitRateItems {
  // [zh] EIP 上行带宽限制，单位: M/s.
  // [en] eip upstream bandwidth limit, unit: M/s
  int32 up_stream_bw = 1;
  // [zh] EIP 下行带宽限制，单位: M/s
  // [en] EIP downstream bandwidth limit, unit: M/s
  int32 down_stream_bw = 2;
}
