syntax = "proto3";
package sensetime.core.network.eip.v1;
option go_package = 'gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1;eip';

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/api/annotations.proto";
import "higgs/common/v1/orderinfo.proto";
import "google/api/field_behavior.proto";
import "network/eip/v1/eip.proto";

// EIP 服务，管理 AZ 内租户 EIP 相关网络资源.
// [EN] Service of EIP, for managing EIP related network resources for tenants in AZ.
service EIPDataService {
  // 列举符合请求的所有 SLB 类型的 EIPs.
  // [EN] List requested slb-typed EIPs.
  rpc ListSlbTypedAvailableEIPs (ListSlbTypedAvailableEIPsRequest) returns (ListSlbTypedAvailableEIPsResponse) {
    option (google.api.http) = {
      get: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbAvailableEips"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
  }

  // [zh] 企业内网EIP更新dst cidr
  // [en] Internal EIP update dst cidr
  rpc UpdateEIPDstCIDR(UpdateEIPDstCIDRRequest) returns (EIPDstCIDR) {
    option (google.api.http) = {
      patch: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/updateEipDstCidr"
      body: "eip_dst_cidr"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.update"
    };
  }
}

// 列举符合请求的所有 SLB 类型的 EIPs.
// [EN] List requested slb-typed EIPs.
message ListSlbTypedAvailableEIPsRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The maximum number of items to return.
  int32 page_size = 4;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 5;
}

// 列举符合请求的所有 SLB 类型的 EIPs.
// [EN] List requested slb-typed EIPs.
message ListSlbTypedAvailableEIPsResponse {
  // EIP 列表.
  // [EN] EIP list.
  repeated EIP eips = 1;

  // 下一个页面的 token，如果没有更多数据则为空.
  // [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
  string next_page_token = 2;

  // total size
  int32 total_size = 3;
}

// [zh] 更新内网EIP dst cidr 请求
// [en] update internal EIP dst cidr request
message UpdateEIPDstCIDRRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string eip_name = 4;

  // [zh] 企业内网 EIP snat dst cidr
  // [en] Internal EIP snat dst cidr
  EIPDstCidrInfo eip_dst_cidr = 5 [(google.api.field_behavior) = REQUIRED];
}

message EIPDstCidrInfo {
  // [zh] EIP snat dst cidr
  // [en] EIP snat dst cidr
  string dst_cidrs = 1 [(validate.rules).string = { max_len: 1024 }];
}

// [zh] 更新内网EIP dst cidr响应
// [en] update internal EIP dst cidr response
message EIPDstCIDR {
  // [zh] EIP 名
  // [en] EIP name
  string eip_name = 1;

  // [zh] 企业内网 EIP snat dst cidr
  // [en] Internal EIP snat dst cidr
  string dst_cidrs = 2;
}
