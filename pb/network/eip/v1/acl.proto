syntax = "proto3";
package sensetime.core.network.eip.v1;
option go_package = 'gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1;eip';

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/api/annotations.proto";
import "higgs/common/v1/orderinfo.proto";


// [zh] ACL 服务，管理 AZ 内租户 ACL 相关网络资源.
// [en] Service of ACLs, for managing ACLs related network resources for tenants in AZ.
service ACLs {

  // [zh]列举EIP下所有 ACLs.
  // [en] List EIP ACLs.
  rpc ListACLs (ListACLsRequest) returns (ListACLsResponse) {
    option (google.api.http) = {
      get: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/acls"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.get"
    };
  }

  // [zh]获取符合请求的一个 ACL.
  // [en] Get an ACL.
  rpc GetACL (GetACLRequest) returns (ACL) {
    option (google.api.http) = {
      get: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/acls/{acl_name}"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.get"
    };
  }

  // [zh]创建一个 ACL.
  // [en] Create an ACL.
  rpc CreateACL (CreateACLRequest) returns (ACL) {
    option (google.api.http) = {
      post: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/acls/{acl_name}"
      body: "acl"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.update"
    };
  }
  
  // [zh]更新一个 ACL.
  // [en] update the ACL.
  rpc UpdateACL (UpdateACLRequest) returns (ACL) {
    option (google.api.http) = {
      patch: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/acls/{acl_name}"
      body: "acl"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.update"
    };
  }
  
  // [zh]删除一个 ACL.
  // [en] Delete a ACL.
  rpc DeleteACL (DeleteACLRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/acls/{acl_name}"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}"
      permission: "eip.eip.update"
    };
  }
}

// [zh] 列举 ACLs 的请求.
// [en] Request to list ACLs.
message ListACLsRequest {

  // [zh] 订购
  // [en] Subscription
  string subscription_name = 1;

  // [zh] 资源组
  // [en] Resource group
  string resource_group_name = 2;

  // [zh] 可用区
  // [en] Available zone
  string zone = 3;

  // [zh] 过滤条件
  // [en] List filter.
  string filter = 4;

  // [zh] 排序字段
  // [en] Sort results.
  string order_by = 5;

  // [zh] 分页大小
  // [en] The maximum number of items to return.
  int32 page_size = 6;

  // [zh] 分页标记
  // [en] The next_page_token value returned from a previous List request, if any.
  string page_token = 7;

  // [zh] eip名称
  // [en] eip name
  string eip_name = 8;

 }

// [zh] 获取某一个 ACL 的请求.
// [en] Request to get an ACL.
message GetACLRequest {
  // [zh] 订购
  // [en] Subscription
  string subscription_name = 1;

  // [zh] 资源组
  // [en] Resource group
  string resource_group_name = 2;

  // [zh] 可用区
  // [en] Available zone
  string zone = 3;

  // [zh] ACL资源名称
  // [en] The ACL resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string acl_name = 4;

  // [zh] eip名称
  // [en] eip name
  string eip_name = 5;
}

// [zh] 创建一个 ACL 的请求.
// [en] Request to create a ACL.
message CreateACLRequest {
  // [zh] 订购
  // [en] Subscription
  string subscription_name = 1;

  // [zh] 资源组
  // [en] Resource group
  string resource_group_name = 2;

  // [zh] 可用区
  // [en] Available zone
  string zone = 3;

  // [zh] ACL资源名称
  // [en] The ACL resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string acl_name = 4 [(validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] ACL资源
  // [en] The ACL resource to create.
  ACL acl = 5;

  // [zh] eip名称
  // [en] eip name
  string eip_name = 6;
}

// 更新一个 ACL 的请求.
// [EN] Request to update an ACL.
message UpdateACLRequest {
  // [zh] 订购
  // [en] Subscription
  string subscription_name = 1;

  // [zh] 资源组
  // [en] Resource group
  string resource_group_name = 2;

  // [zh] 可用区
  // [en] Available zone
  string zone = 3;

  // [zh] ACL资源名称
  // [en] The ACL resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string acl_name = 4 [(validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] ACL资源
  // [en] The ACL resource to create.
  ACL acl = 5;

  // [zh] eip名称
  // [en] eip name
  string eip_name = 6;
}
   
// 删除某一个 ACL 的请求.
// [EN] Request to delete an ACL.
message DeleteACLRequest {

  // [zh] 订购
  // [en] Subscription
  string subscription_name = 1;

  // [zh] 资源组
  // [en] Resource group
  string resource_group_name = 2;

  // [zh] 可用区
  // [en] Available zone
  string zone = 3;

  // [zh] ACL资源名称
  // [en] The ACL resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string acl_name = 4 [(validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] eip名称
  // [en] eip name
  string eip_name = 6;

}

// [zh] 列举 ACLs 的响应.
// [en] Response to list ACLs.
message ListACLsResponse {

  // [zh] ACL 列表.
  // [en] ACL list.
  repeated ACL acls = 1;

  // [zh] 下一个页面的 token，如果没有更多数据则为空.
  // [en] Token to retrieve the next page of results, or empty if there are no more results in the list.
  string next_page_token = 2;

  // [zh] 分页大小
  // [en] total size
  int32 total_size = 3;

}

// [zh] ACL 实例结构体.
// [en] ACL entity.
message ACL {
  // [zh] ACL资源ID
  // [en] The ACL resource id using the form: `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/acls/{acl_name}`.
  string id = 1;

  // [zh] ACL资源名称
  // [en] The ACL resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 2;

  // [zh] ACL资源显示名称
  // [en] ContainerInstance resource display name
  optional string display_name = 3 [(validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // [zh] 资源描述信息
  // [en] ContainerInstance resource description
  string description = 4;

  // [zh] ACL资源唯一标识
  // [en] The ACL resource uuid.
  string uid = 5;

  // [zh] ACL资源类型
  // [en] The ACL resource type.
  string resource_type = 6;

  // [zh] 创建者ID
  // [en] The id of the user who created the ACL resource.
  string creator_id = 7;

  // [zh] 拥有者ID
  // [en] The id of the user who owns the ACL resource.
  string owner_id = 8;

  // [zh] 租户ID
  // [en] Tenant id.
  string tenant_id = 9;

  // [zh] 可用区
  // [en] Available zone.
  string zone = 10;

  // [zh] ACL状态类型
  // [en] Represents the different states of an ACL.
  enum State {
    // [zh] 创建中
    // [en] The ACL resource is being created.
    CREATING = 0;

    // [zh] 更新中
    // [en] The ACL resource is being updated.
    UPDATING = 1;

    // [zh] 已激活
    // [en] The ACL resource has been active.
    ACTIVE = 2;

    // [zh] 删除中
    // [en] The ACL resource is being deleted.
    DELETING = 3;

    // [zh] 已删除
    // [en] The ACL resource has been deleted.
    DELETED = 4;

    // [zh] 失败
    // [en] The ACL resource has been failed.
    FAILED = 5;
  };

  // [zh] ACL资源当前状态
  // [en] The current state of the ACL resource.
  State state = 11;

  // [zh] sku标识
  // [en] Sku id.
  string sku_id = 12;

  // [zh] 标签
  // [en] Tags attached to the ACL resource.
  map<string, string> tags = 13;

  // [zh] ACL资源属性
  // [en] Properties of the ACL resource.
  ACLProperties properties = 14;

  // [zh] 订单信息
  // [en] Payment information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 15;

  // [zh] ACL资源是否已删除
  // [en] Indicates whether the ACL resource is deleted or not.
  bool deleted = 16;

  // [zh] 创建时间
  // [en] The time when the ACL resource was created.
  google.protobuf.Timestamp create_time = 17;

  // [zh] 更新时间
  // [en] The time when the ACL resource was last updated.
  google.protobuf.Timestamp update_time = 18;

}

// [zh] 资源实际属性.
// [en] Real resource properties.
message ACLProperties {
  // [zh] ACL动作类型.
  // [en] ACL action type.
  enum  ACLAction{
    // [zh] 拒绝规则
    // [en] Deny acl rule
    DENY = 0;
    // [zh] 允许规则
    // [en] Allow acl rule
    ALLOW = 1;
  };

  // [zh] ACL动作.
  // [en] ACL action.
  ACLAction action = 1;

  // [zh] ACL源地址.
  // [en] ACL source addresses, support multi IPs,IP range or Subnet: ********,********,*********-*********,********/27.
  string src = 2;

  // [zh] ACL目的地址.
  // [en] ACL destination addresses, reserved now.
  string dest = 3;

  // [zh] ACL目的端口，目前保留.
  // [en] ACL destination port, reserved now.
  string dest_port = 4;

  // [zh] 对外暴露的协议，目前保留.
  // [en] External protocol, reserved now.
  string protocol = 5;

  // [zh] ACL规则优先级，目前保留.
  // [en] ACL rule priority.
  int32 priority = 6;

}
