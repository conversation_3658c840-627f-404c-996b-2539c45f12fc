# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: DNATRules API
    description: |-
        DNATRule 服务，管理 AZ 内租户 DNATRule 相关网络资源.
         [EN] Service of DNATRule, for managing DNATRule related network resources for tenants in AZ.
    version: 0.0.1
paths:
    /network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dnatRules:
        get:
            tags:
                - DNATRules
            description: |-
                列举符合请求的所有EIP的 DNATRules.
                 [EN] List requested multi-eip DNATRules.
            operationId: DNATRules_ListMDNATRules
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: filter
                  in: query
                  description: List filter.
                  schema:
                    type: string
                - name: order_by
                  in: query
                  description: Sort resoults.
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: The maximum number of items to return.
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: The next_page_token value returned from a previous List request, if any.
                  schema:
                    type: string
                - name: eip_names
                  in: query
                  description: eip_name
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListDNATRulesResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/dnatRules
    :   get:
            tags:
                - DNATRules
            description: |-
                列举符合请求的所有 DNATRules.
                 [EN] List requested DNATRules.
            operationId: DNATRules_ListDNATRules
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: eip_name
                  required: true
                  schema:
                    type: string
                - name: filter
                  in: query
                  description: List filter.
                  schema:
                    type: string
                - name: order_by
                  in: query
                  description: Sort resoults.
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: The maximum number of items to return.
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: The next_page_token value returned from a previous List request, if any.
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListDNATRulesResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/dnatRules/unbind
    :   post:
            tags:
                - DNATRules
            description: |-
                批量解绑DNATRule.
                 [EN] unbind DNATRules.
            operationId: DNATRules_UnbindDNATRules
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: 'Available zone, todo: add validation'
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: eip_name
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DnatRules'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListDNATRulesResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/dnatRules/{dnat_rule_name}
    :   get:
            tags:
                - DNATRules
            description: |-
                获取符合请求的一个 DNATRule.
                 [EN] Get a requested DNATRule.
            operationId: DNATRules_GetDNATRule
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: eip_name
                  required: true
                  schema:
                    type: string
                - name: dnat_rule_name
                  in: path
                  description: 'The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DNATRule'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - DNATRules
            description: |-
                创建一个 DNATRule.
                 [EN] Create a DNATRule.
            operationId: DNATRules_CreateDNATRule
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: 'Available zone, todo: add validation'
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: eip_name
                  required: true
                  schema:
                    type: string
                - name: dnat_rule_name
                  in: path
                  description: 'The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DNATRule'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DNATRule'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - DNATRules
            description: |-
                删除一个 DNATRule.
                 [EN] Delete a DNATRule.
            operationId: DNATRules_DeleteDNATRule
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: eip_name
                  required: true
                  schema:
                    type: string
                - name: dnat_rule_name
                  in: path
                  description: 'The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - DNATRules
            description: |-
                更新一个 DNATRule.
                 [EN] update the DNATRule.
            operationId: DNATRules_UpdateDNATRule
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: 'Available zone, todo: add validation'
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: eip_name
                  required: true
                  schema:
                    type: string
                - name: dnat_rule_name
                  in: path
                  description: 'The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DNATRule'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DNATRule'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/dnatRules/{dnat_rule_name}/bind
    :   post:
            tags:
                - DNATRules
            description: |-
                绑定一个 DNATRule和计算实例.
                 [EN] bind a DNATRule with a instance.
            operationId: DNATRules_BindDNATRule
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: 'Available zone, todo: add validation'
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: eip_name
                  required: true
                  schema:
                    type: string
                - name: dnat_rule_name
                  in: path
                  description: 'The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DNATRule'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DNATRule'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/dnatRules/{dnat_rule_name}/unbind
    :   post:
            tags:
                - DNATRules
            description: |-
                解绑一个 DNATRule.
                 [EN] unbind the DNATRule.
            operationId: DNATRules_UnbindDNATRule
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: 'Available zone, todo: add validation'
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: eip_name
                  required: true
                  schema:
                    type: string
                - name: dnat_rule_name
                  in: path
                  description: 'The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DNATRule'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        DNATRule:
            type: object
            properties:
                id:
                    type: string
                    description: 'The DNATRule resource id using the form:     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/dnatRules/{dnat_rule_name}`.'
                name:
                    type: string
                    description: 'The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                display_name:
                    type: string
                    description: ContainerInstance resource display name
                description:
                    type: string
                    description: ContainerInstance resource description
                uid:
                    type: string
                    description: The DNATRule resource uuid.
                resource_type:
                    type: string
                    description: The DNATRule resource type.
                creator_id:
                    type: string
                    description: The id of the user who created the DNATRule resource.
                owner_id:
                    type: string
                    description: The id of the user who owns the DNATRule resource.
                tenant_id:
                    type: string
                    description: Tenant id.
                zone:
                    type: string
                    description: Available zone.
                state:
                    type: integer
                    description: The current state of the DNATRule resource.
                    format: enum
                sku_id:
                    type: string
                    description: Sku id.
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: Tags attached to the DNATRule resource.
                properties:
                    $ref: '#/components/schemas/DNATRuleProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
                deleted:
                    type: boolean
                    description: Indicates whether the DNATRule resource is deleted or not.
                create_time:
                    type: string
                    description: The time when the DNATRule resource was created.
                    format: date-time
                update_time:
                    type: string
                    description: The time when the DNATRule resource was last updated.
                    format: date-time
            description: DNATRule 实例结构体. [EN] DNATRule entity.
        DNATRuleProperties:
            type: object
            properties:
                nat_gateway_id:
                    type: string
                    description: 所属的 NATGateway uid. [EN] NATGateway uid.
                eip_id:
                    type: string
                    description: 所属的 EIP uid. [EN] EIP uid.
                external_ip:
                    type: string
                    description: VPC 对外暴露的 DNATRule external IP. [EN] DNATRule external IP in VPC.
                external_port:
                    type: string
                    description: 对外暴露的端口,（10000，10000-20000，(10000,10001,20000,20001),*）. [EN] External port,（10000，10000-20000，(10000,10001,20000,20001),*）.
                protocol:
                    type: string
                    description: 对外暴露的协议. [EN] External protocol.
                internal_ip:
                    type: string
                    description: 对外暴露的 VPC 内服务的 IP. [EN] Exposed service IP in the VPC.
                internal_port:
                    type: string
                    description: 对外暴露的 VPC 内服务的端口,（10000，10000-20000，(10000,10001,20000,20001),*）. [EN] Exposed service port in the VPC,（10000，10000-20000，(10000,10001,20000,20001),*）.
                priority:
                    type: integer
                    description: 规则优先级. [EN] NAT rule priority.
                    format: int32
                internal_instance_type:
                    type: integer
                    description: 对外暴露的 VPC内服务的实例类型 [EN] Exposed service instance type in the VPC
                    format: enum
                internal_instance_name:
                    type: string
                    description: 对外暴露的 VPC内服务的实例唯一标识,目前使用实例name作为唯一标识 [EN] Exposed service instance name in the VPC
            description: 资源实际属性. [EN] Real resource properties.
        DnatRules:
            type: object
            properties:
                filterType:
                    type: integer
                    description: Resource filter type
                    format: enum
                filterValues:
                    type: array
                    items:
                        type: string
                    description: Resource filter parameter
            description: DNATRules 实体结构. [EN] the entity of DNATRules.
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        ListDNATRulesResponse:
            type: object
            properties:
                dnat_rules:
                    type: array
                    items:
                        $ref: '#/components/schemas/DNATRule'
                    description: DNATRule 列表. [EN] DNATRule list.
                next_page_token:
                    type: string
                    description: 下一个页面的 token，如果没有更多数据则为空. [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
                total_size:
                    type: integer
                    description: total size
                    format: int32
            description: 列举 DNATRules 的响应. [EN] Response to list DNATRules.
        OrderInfo:
            type: object
            properties:
                billing_cycle_number:
                    type: integer
                    description: '[zh] 购买时长. [en] length of purchase.'
                    format: int32
                auto_renew:
                    type: boolean
                    description: '[zh] 自动续费. [en] Automatic renewal.'
                currency_code:
                    type: string
                    description: '[zh] 货币代码. [en] currency code.'
                payment_channel:
                    type: integer
                    description: '[zh] 支付方式. [en] payment method.'
                    format: enum
                note:
                    type: string
                    description: '[zh] 订单备注. [en] order notes'
                order_type:
                    type: integer
                    description: '[zh] 订单类型. [en] Order Type.'
                    format: enum
                order_id:
                    type: string
                    description: '[zh] 订单id. [en] order id.'
                start_time:
                    type: string
                    description: '[zh] 订单生效日期. [en] Order effective date.'
                    format: date-time
                payment_model:
                    type: integer
                    description: '[zh] 付费类型. [en] payment type.'
                    format: enum
                billing_model:
                    type: integer
                    description: '[zh] 计费类型. [en] billing type.'
                    format: enum
                original_id:
                    type: string
                    description: '[zh] 合同包ID. [en] Contract package ID. original id-- OT_ORIGINAL: original spu_id/package_id; OT_RENEW/OT_UPGRADED/OT_DOWNGRADED: original/ order_id; OT_CONTRACT: original contract_id'
                end_time:
                    type: string
                    description: '[zh] 订单结束时间. [en] Order end time.'
                    format: date-time
            description: '[zh] 订单信息. [en] Order Infomation.'
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
tags:
    - name: DNATRules
