syntax = "proto3";
package sensetime.core.network.natGateway.v1;
option go_package = 'gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/natGateway/v1;natGateway';

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/api/annotations.proto";
import "higgs/common/v1/orderinfo.proto";


// SNATRule 服务，管理 AZ 内租户 SNATRule 相关网络资源.
// [EN] Service of SNATRule, for managing SNATRule related network resources for tenants in AZ.
service SNATRules {

  // 列举符合请求的所有 SNATRules.
  // [EN] List requested SNATRules.
  rpc ListSNATRules (ListSNATRulesRequest) returns (ListSNATRulesResponse) {
    option (google.api.http) = {
      get: "/network/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/snatRules"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
  }

  // 获取符合请求的一个 SNATRule.
  // [EN] Get a requested SNATRule.
  rpc GetSNATRule (GetSNATRuleRequest) returns (SNATRule) {
    option (google.api.http) = {
      get: "/network/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/snatRules/{snat_rule_name}"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
  }

  // 创建一个 SNATRule.
  // [EN] Create a SNATRule.
  rpc CreateSNATRule (CreateSNATRuleRequest) returns (SNATRule) {
    option (google.api.http) = {
      post: "/network/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/snatRules/{snat_rule_name}"
      body: "snat_rule"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
  }

  // 删除一个 SNATRule.
  // [EN] Delete a SNATRule.
  rpc DeleteSNATRule (DeleteSNATRuleRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/network/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/snatRules/{snat_rule_name}"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
  }

}

// 列举 SNATRules 的请求.
// [EN] Request to list SNATRules.
message ListSNATRulesRequest {

  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // List filter.
  string filter = 4;

  // Sort resoults.
  string order_by = 5;

  // The maximum number of items to return.
  int32 page_size = 6;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 7;

 }

// 获取某一个 SNATRule 的请求.
// [EN] Request to get a SNATRule.
message GetSNATRuleRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The SNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string snat_rule_name = 4;

}

// CreateSNATRuleRequest.
message CreateSNATRuleRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone, todo: add validation
  string zone = 3;

  // The SNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string snat_rule_name = 4 [(validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // The SNATRule resource to create.
  SNATRule snat_rule = 5;
}


// 删除某一个 SNATRule 的请求.
// [EN] Request to delete a SNATRule.
message DeleteSNATRuleRequest {

  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The SNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string snat_rule_name = 4;

}

// 列举 SNATRules 的响应.
// [EN] Response to list SNATRules.
message ListSNATRulesResponse {

  // SNATRule 列表.
  // [EN] SNATRule list.
  repeated SNATRule snat_rules = 1;

  // 下一个页面的 token，如果没有更多数据则为空.
   // [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
  string next_page_token = 2;

  // total size
  int32 total_size = 3;

}

// SNATRule 实例结构体.
// [EN] SNATRule entity.
message SNATRule {
  // The SNATRule resource id using the form:
  //     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/snatRules/{snat_rule_name}`.
  string id = 1;

  // The SNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 2;

  // ContainerInstance resource display name
  string display_name = 3 [(validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // ContainerInstance resource description
  string description = 4;

  // The SNATRule resource uuid.
  string uid = 5;

  // The SNATRule resource type.
  string resource_type = 6;

  // The id of the user who created the SNATRule resource.
  string creator_id = 7;

  // The id of the user who owns the SNATRule resource.
  string owner_id = 8;

  // Tenant id.
  string tenant_id = 9;

  // Available zone.
  string zone = 10;

  // Represents the different states of a SNATRule.
  enum State {
    // The SNATRule resource is being created.
    CREATING = 0;

    // The SNATRule resource is being updated.
    UPDATING = 1;

    // The SNATRule resource has been active.
    ACTIVE = 2;

    // The SNATRule resource is being deleted.
    DELETING = 3;

    // The SNATRule resource has been deleted.
    DELETED = 4;
  };

  // The current state of the SNATRule resource.
  State state = 11;

  // Sku id.
  string sku_id = 12;

  // Tags attached to the SNATRule resource.
  map<string, string> tags = 13;

  // Properties of the SNATRule resource.
  SNATRuleProperties properties = 14;

  // Payment information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 15;

  // Indicates whether the SNATRule resource is deleted or not.
  bool deleted = 16;

  // The time when the SNATRule resource was created.
  google.protobuf.Timestamp create_time = 17;

  // The time when the SNATRule resource was last updated.
  google.protobuf.Timestamp update_time = 18;

}

// 资源实际属性.
// [EN] Real resource properties.
message SNATRuleProperties {

  // 所属的 NATGateway uid.
  // [EN] NATGateway uid.
  string nat_gateway_id = 1;

  // VPC 对外暴露的 SNATRule Gateway IP.
  // [EN] VPC external SNATRule Gateway IP.
  string external_ip = 2;

  // VPC 内子网的 cidr.
  // [EN] Subnet cidr in VPC.
  string internal_cidr = 3;

  // 规则优先级.
  // [EN] NAT rule priority.
  int32 priority = 4;
}
