syntax = "proto3";
package sensetime.core.network.natGateway.v1;
option go_package = 'gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/natGateway/v1;natGateway';

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/api/annotations.proto";
import "higgs/common/v1/orderinfo.proto";


// NATGateway 服务，管理 AZ 内租户 NATGateway 相关网络资源.
// [EN] Service of NATGateway, for managing NATGateway related network resources for tenants in AZ.
service NATGateways {

  // 列举符合请求的所有 NATGateways.
  // [EN] List requested NATGateways.
  rpc ListNATGateways (ListNATGatewaysRequest) returns (ListNATGatewaysResponse) {
    option (google.api.http) = {
      get: "/network/natGateway/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/natGateways"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;

  }

  // 获取符合请求的一个 NATGateway.
  // [EN] Get a requested NATGateway.
  rpc GetNATGateway (GetNATGatewayRequest) returns (NATGateway) {
    option (google.api.http) = {
      get: "/network/natGateway/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/natGateways/{nat_gateway_name}"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
  }

  // 创建一个 NATGateway.
  // [EN] Create a NATGateway.
  rpc CreateNATGateway (CreateNATGatewayRequest) returns (NATGateway) {
    option (google.api.http) = {
      post: "/network/natGateway/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/natGateways/{nat_gateway_name}"
      body: "nat_gateway"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
  }

  // 更新 NATGateway 可编辑字段.
  // [EN] Update NATGateway editable properties.
  rpc UpdateNATGateway (UpdateNATGatewayRequest) returns (NATGateway) {
    option (google.api.http) = {
      patch: "/network/natGateway/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/natGateways/{nat_gateway_name}"
      body: "nat_gateway"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
  }

  // 删除一个 NATGateway.
  // [EN] Delete a NATGateway.
  rpc DeleteNATGateway (DeleteNATGatewayRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/network/natGateway/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/natGateways/{nat_gateway_name}"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
  }

}

// 列举 NATGateways 的请求.
// [EN] Request to list NATGateways.
message ListNATGatewaysRequest {

  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // List filter.
  string filter = 4;

  // Sort resoults.
  string order_by = 5;

  // The maximum number of items to return.
  int32 page_size = 6;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 7;

 }

// 获取某一个 NATGateway 的请求.
// [EN] Request to get a NATGateway.
message GetNATGatewayRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The NATGateway resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string nat_gateway_name = 4;

}

// CreateNATGatewayRequest.
message CreateNATGatewayRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone, todo: add validation
  string zone = 3;

  // The NATGateway resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string nat_gateway_name = 4 [(validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // The NATGateway resource to create.
  NATGateway nat_gateway = 5;
}

// NATGateway 可编辑字段.
// [EN] NATGateway editable properties.
message UpdateNATGatewayRequest {

  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The NATGateway resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string nat_gateway_name = 4;

  // The NATGateway resource to update.
  NATGateway nat_gateway = 5;
}

// 删除某一个 NATGateway 的请求.
// [EN] Request to delete a NATGateway.
message DeleteNATGatewayRequest {

  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The NATGateway resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string nat_gateway_name = 4;

}

// 列举 NATGateways 的响应.
// [EN] Response to list NATGateways.
message ListNATGatewaysResponse {

  // NATGateway 列表.
  // [EN] NATGateway list.
  repeated NATGateway nat_gateways = 1;

  // 下一个页面的 token，如果没有更多数据则为空.
   // [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
  string next_page_token = 2;

  // total size
  int32 total_size = 3;

}

// NATGateway 实例结构体.
// [EN] NATGateway entity.
message NATGateway {
  // The NATGateway resource id using the form:
  //     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/natGateways/{nat_gateway_name}`.
  string id = 1;

  // The NATGateway resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 2;

  // ContainerInstance resource display name
  string display_name = 3 [(validate.rules).string = {ignore_empty: true, pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // ContainerInstance resource description
  string description = 4;

  // The NATGateway resource uuid.
  string uid = 5;

  // The NATGateway resource type.
  string resource_type = 6;

  // The id of the user who created the NATGateway resource.
  string creator_id = 7;

  // The id of the user who owns the NATGateway resource.
  string owner_id = 8;

  // Tenant id.
  string tenant_id = 9;

  // Available zone.
  string zone = 10;

  // Represents the different states of a NATGateway.
  enum State {
    // The NATGateway resource is being created.
    CREATING = 0;

    // The NATGateway resource is being updated.
    UPDATING = 1;

    // The NATGateway resource has been active.
    ACTIVE = 2;

    // The NATGateway resource is being deleting.
    DELETING = 3;

    // The NATGateway resource has been deleted.
    DELETED = 4;

    // The NATGateway resource has been failed.
    FAILED = 5;
  };

  // The current state of the NATGateway resource.
  State state = 11;

  // Sku id.
  string sku_id = 12;

  // Tags attached to the NATGateway resource.
  map<string, string> tags = 13;

  // Properties of the NATGateway resource.
  NATGatewayProperties properties = 14;

  // Payment information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 15;

  // Indicates whether the NATGateway resource is deleted or not.
  bool deleted = 16;

  // The time when the NATGateway resource was created.
  google.protobuf.Timestamp create_time = 17;

  // The time when the NATGateway resource was last updated.
  google.protobuf.Timestamp update_time = 18;

}

// 资源实际属性.
// [EN] Real resource properties.
message NATGatewayProperties {

  // 所属的子网 uid.
  // [EN] Subnet uid.
  string subnet_id = 1;

  // 关联的 VPC uid.
  // [EN] Related VPC uid.
  string vpc_id = 2;

  // VPC 内的 NATGateway Gateway IP.
  // [EN] NATGateway Gateway IP in the VPC.
  string internal_ip = 3;

  // VPC 对外暴露的 NATGateway Gateway IP.
  // [EN] VPC external NATGateway Gateway IP.
  NATGatewayExternalIP nat_gw_external_ip = 4;

}

// VPC 对外暴露的 NATGateway Gateway IP 属性.
// [EN] VPC external NATGateway Gateway IP properties.
message NATGatewayExternalIP {

  // VPCExternalGateway cidr.
  // [EN] VPCExternalGateway cidr.
  string cidr = 1;

  // 当前 VPCExternalGateway 对应 IP 段的网关地址.
  // [EN] The gateway ip for the VPCExternalGateway cidr.
  string gateway = 2;
}
