syntax = "proto3";
package sensetime.core.network.slb.v1;
option go_package = "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/slb/v1;slb";


import "network/slb/v1/slb.proto";
import "higgs/common/v1/orderinfo.proto";

// [zh] 创建负载均衡资源消息, 消息类型："sensetime.core.network.slb.create.v1" .
// [en] CreateSLBMessage, message type: "sensetime.core.network.slb.create.v1" .
message CreateSLBMessage {

  // [zh] 租户编码.
  // [en] tenant code.
  string tenant_code = 1;

  // [zh] 租户物理资源池信息.
  // [en] tenant prp.
  repeated  string tenant_prp = 2;

  // [zh] 操作者ID.
  // [en] operator id.
  string operator_id = 3;

  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 4;

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 5;

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 6;

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name.
  string slb_name = 7;

  // [zh] 负载均衡资源.
  // [en] The SLB resource struct.
  SLB slb = 8;

  // [zh] 专有云资源.
  // [en] The dedicated resource struct.
  DedicatedResource dedicated_resource = 9;

  // [zh] Boss回传信息.
  // [en] Boss returns information.
  CallbackData callback_data = 10;
}

// [zh] 更新负载均衡资源消息, 消息类型："sensetime.core.network.slb.update.v1" .
// [en] UpdateSLBMessage, message type: "sensetime.core.network.slb.update.v1" .
message UpdateSLBMessage {

  // [zh] 租户编码.
  // [en] tenant code.
  string tenant_code = 1;

  // [zh] 租户物理资源池信息.
  // [en] tenant prp.
  repeated  string tenant_prp = 2;

  // [zh] 操作者ID.
  // [en] operator id.
  string operator_id = 3;

  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 4;

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 5;

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 6;

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name.
  string slb_name = 7;

  // [zh] 负载均衡资源.
  // [en] The SLB resource struct.
  SlbUpdate slb = 8;

  // [zh] 专有云资源.
  // [en] The dedicated resource struct.
  DedicatedResource dedicated_resource = 9;
}

// [zh] 退订负载均衡资源消息, 消息类型："sensetime.core.network.slb.delete.v1" .
// [en] CreateSLBMessage, message type: "sensetime.core.network.slb.delete.v1" .
message DeleteSLBMessage {

  // [zh] 租户编码.
  // [en] tenant code.
  string tenant_code = 1;

  // [zh] 租户物理资源池信息.
  // [en] tenant prp.
  repeated  string tenant_prp = 2;

  // [zh] 操作者ID.
  // [en] operator id.
  string operator_id = 3;

  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 4;

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 5;

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 6;

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name.
  string slb_name = 7;
}

// [zh] 【暂不使用】扩缩容负载均衡资源消息, 消息类型："sensetime.core.network.slb.resize.v1" .
// [en] 【Not in use yet】ResizeSLBMessage, message type: "sensetime.core.network.slb.resize.v1" .
message ResizeSLBMessage {

  // [zh] 租户编码.
  // [en] tenant code.
  string tenant_code = 1;

  // [zh] 租户物理资源池信息.
  // [en] tenant prp.
  repeated  string tenant_prp = 2;

  // [zh] 操作者ID.
  // [en] operator id.
  string operator_id = 3;

  // [zh] 资源ID.
  // [en] resource id.
  string resource_id = 4;

  // [zh] 最小库存单元id.
  // [en] SKU id.
  string sku_id = 5;

  // [zh] 资源的属性
  // [en] Properties of the resource.
  SLBProperties properties = 6;

  // [zh] 订单信息.
  // [en] Order information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 7;

  // [zh] 专有云资源.
  // [en] The dedicated resource struct.
  DedicatedResource dedicated_resource = 9;

  // [zh] Boss回传信息.
  // [en] Boss returns information.
  CallbackData callback_data = 10;
}

// [zh] 专有云资源信息.
// [en] The dedicated resource.
message DedicatedResource {
  // [zh] 是否是专有云租户.
  // [en] The dedicated tenant.
  bool is_dedicated_tenant = 1;
  // [zh] 是否是专有云资源.
  // [en] The dedicated resource.
  bool is_dedicated_resource = 2;
  // [zh] 专有云资源包ID.
  // [en] The dedicated resource package id.
  string resource_package_id = 3;
}

// [zh] Boss回传订单信息.
// [en] Boss returns order information.
message CallbackData {
  // [zh] 租户ID.
  // [en] The tenant id.
  string tenant_id = 1;
  // [zh] 用户ID.
  // [en] The user id.
  string user_id = 2;
  // [zh] 订单ID.
  // [en] The order id.
  string order_id = 3;
}