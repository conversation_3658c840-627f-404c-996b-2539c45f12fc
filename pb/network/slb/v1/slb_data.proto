
syntax = "proto3";
package sensetime.core.network.slb.v1;
option go_package = "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/slb/v1;slb";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/api/annotations.proto";
import "google/protobuf/field_mask.proto";
import "higgs/common/v1/orderinfo.proto";
import "network/slb/v1/slb.proto";

// [zh] 负载均衡服务
// [en] SLB Data Service.
service SLBDataService {
  // [zh] 查看负载均衡资源后端状态.
  // [en] Gets details of a SLB resources status.
  rpc GetSLBStatus(GetSLBStatusRequest) returns (SLBStatus) {
    option (google.api.http) = {
      get : "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/status"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.get"
    };
  }

  // [zh] 列举符合条件的负载均衡状态列表
  // [en] List  details of SLBs resources status.
  rpc ListSLBsStatus(ListSLBsStatusRequest) returns (ListSLBsStatusResponse) {
    option (google.api.http) = {
      get : "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
  }

  // [zh] 创建一个监听器.
  // [en] Create a Listener.
  rpc CreateListener (CreateListenerRequest) returns (Listener) {
    option (google.api.http) = {
      post: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/listeners/{listener_name}"
      body: "listener"
    };
    option (sensetime.core.higgs.api.is_asynchronous)  = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.update"
    };
  }

  // [zh] 更新监听器的可编辑字段.
  // [en] Update listener editable properties.
  rpc UpdateListener (UpdateListenerRequest) returns (Listener) {
    option (google.api.http) = {
      patch: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/listeners/{listener_name}"
      body: "listener"
    };
    option (sensetime.core.higgs.api.is_asynchronous)  = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.update"
    };
  }
  
  // [zh] 删除一个监听器
  // [en] Delete a listener.
  rpc DeleteListener (DeleteListenerRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/listeners/{listener_name}"
    };
    option (sensetime.core.higgs.api.is_asynchronous)  = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.update"
    };
  }

  // [zh] 获取符合请求的所有监听器.
  // [en] List requested listener.
  rpc ListListeners (ListListenersRequest) returns (ListListenersResponse) {
    option (google.api.http) = {
      get: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/listeners"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.get"
    };
  }

  // [zh] 获取符合请求的一个监听器.
  // [en] Get a requested listener.
  rpc GetListener (GetListenerRequest) returns (Listener) {
    option (google.api.http) = {
      get: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/listeners/{listener_name}"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.get"
    };
  }

  // [zh] 获取符合请求的一个监听器的相关属性.
  // [en] Get a requested listener status.
  rpc GetListenerStatus (GetListenerStatusRequest) returns (ListenerStatus) {
    option (google.api.http) = {
      get: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/listeners/{listener_name}/status"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.get"
    };
  }

  // [zh] 启动一个监听器.
  // [en] Start a Listener.
  rpc StartListener (StartListenerRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/listeners/{listener_name}:start"
      body: "*"
    };
    option (sensetime.core.higgs.api.is_asynchronous)  = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.update"
    };
  }

  // [zh] 停止一个监听器.
  // [en] Stop a Listener.
  rpc StopListener (StopListenerRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/listeners/{listener_name}:stop"
      body: "*"
    };
    option (sensetime.core.higgs.api.is_asynchronous)  = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.update"
    };
  }

  // [zh] 创建一个后端组
  // [en] Create one TargetGroup
  rpc CreateTargetGroup (CreateTargetGroupRequest) returns (TargetGroup) {
    option (google.api.http) = {
      post: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}"
      body: "target_group"
    };
    option (sensetime.core.higgs.api.is_asynchronous)  = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.update"
    };
  }

  // [zh] 更新后端组的可编辑字段
  // [en] Update TargetGroup editable properties.
  rpc UpdateTargetGroup (UpdateTargetGroupRequest) returns (TargetGroup) {
    option (google.api.http) = {
      patch: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}"
      body: "target_group"
    };
    option (sensetime.core.higgs.api.is_asynchronous)  = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.update"
    };
  }

  // [zh] 删除一个后端组
  // [en] Delete one TargetGroup
  rpc DeleteTargetGroup (DeleteTargetGroupRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}"
    };
    option (sensetime.core.higgs.api.is_asynchronous)  = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.update"
    };
  }

  // [zh] 列举符合请求的后端组
  // [en] List requested TargetGroup
  rpc ListTargetGroups (ListTargetGroupsRequest) returns (ListTargetGroupsResponse) {
    option (google.api.http) = {
      get: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.get"
    };
  }

  // [zh] 获取符合请求的后端组
  // [en] Get requested TargetGroup
  rpc GetTargetGroup (GetTargetGroupRequest) returns (TargetGroup) {
    option (google.api.http) = {
      get: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.get"
    };
  }

  // [zh] 获取符合请求的后端组的状态
  rpc GetTargetGroupStatus (GetTargetGroupStatusRequest) returns (TargetGroupStatus) {
    option (google.api.http) = {
      get: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}/status"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.get"
    };
  }

  // [zh] 添加一个或多个后端到某个后端组.
  // [en] Add one or more backend targets to a TargetGroup.
  rpc TargetGroupAddTargets (TargetGroupAddTargetsRequest) returns (TargetGroupAddTargetsResponse) {
    option (google.api.http) = {
      post: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}:targetsAdd"
      body: "*"
    };
    option (sensetime.core.higgs.api.is_asynchronous)  = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.update"
    };
  }

  // [zh] 从某个后端组删除一个或多个后端.
  // [en] Remove one or more backend targets from a TargetGroup.
  rpc TargetGroupRemoveTargets (TargetGroupRemoveTargetsRequest) returns (TargetGroupRemoveTargetsResponse) {
    option (google.api.http) = {
      post: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}:targetsRemove"
      body: "*"
    };
    option (sensetime.core.higgs.api.is_asynchronous)  = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.update"
    };
  }

  // [zh] 修改某个后端组的一台或者多台后端的属性.
  // [en] Update the editable properties of one or more backends of a TargetGroup.
  rpc TargetsUpdate (TargetsUpdateRequest) returns (TargetGroupTargets) {
    option (google.api.http) = {
      post: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}/targets"
      body: "*"
    };
    option (sensetime.core.higgs.api.is_asynchronous)  = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.update"
    };
  }

  // [zh] 查询某个后端组绑定的后端列表
  // [en] Get requested TargetGroup targets
  rpc ListTargets (ListTargetsRequest) returns (ListTargetsResponse) {
    option (google.api.http) = {
      get: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}/targets"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.get"
    };
  }

  // [zh] 查询某个后端组绑定的某个后端
  // [en] Get requested TargetGroup targets
  rpc GetTarget (GetTargetRequest) returns (Target) {
    option (google.api.http) = {
      get: "/network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}/targets/{target_name}"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.get"
    };
  }
}

// [zh] 获取某一个 SLB 状态的请求
// [en] Request to get a SLB status
message GetSLBStatusRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 列举符合条件的负载均衡状态列表
// [en] List  details of SLBs resources status.
message ListSLBsStatusRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] slb过滤条件【暂不支持】
  // [en] List filter.
  string filter = 4;

  // [zh] slb资源排序规则【暂不支持】
  // [en] Sort resoults.
  string order_by = 5;

  // [zh] 分页大小【暂不支持】
  // [en] The maximum number of items to return.
  int32 page_size = 6;

  // [zh] 从上一个List请求返回的next_page_token值(如果有的话)【暂不支持】
  // [en] The next_page_token value returned from a previous List request, if any.
  string page_token = 7;

  // [zh] slb的name列表
  // [en] The slb name list
  repeated string slb_names = 8;
}

// [zh] 列举符合条件的负载均衡状态列表
// [en] List  details of SLBs resources status.
message ListSLBsStatusResponse {
  // [zh] SLB状态列表
  // [en] SLB status list
  repeated SLBStatus slbs = 1;

  // [zh] 下一个页面的 token，如果没有更多数据则为空.
  // [en] Token to retrieve the next page of results, or empty if there are no more results in the list.
  string next_page_token = 2;

  // [zh] 返回的slb实例总数
  // [en] total size
  int32 total_size = 3;
}

// [zh] Listener 创建字段
// [en] Listener creation properties
message CreateListenerRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1;

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2;

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3;

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 监听器名称
  // [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string listener_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 监听器资源
  // [en] The Listener resource to create
  Listener listener = 6;
}

// [zh] Listener 可编辑字段
// [en] Listener editable properties
message UpdateListenerRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1;

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2;

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3;

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 监听器名称
  // [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string listener_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 监听器资源
  // [en] The Listener resource to update
  ListenerUpdate listener = 6;

  // [zh] 更新标记.
  // [en] The resource update mask.
  google.protobuf.FieldMask update_mask = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// [zh] 删除某一个 Listener 的请求
// [en] Request to delete a Listener
message DeleteListenerRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1;

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2;

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3;

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 监听器名称
  // [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string listener_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 列举 Listeners 的请求
// [en] Request to list Listener
message ListListenersRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 过滤字段
  // [en] List filter.
  string filter = 4;

  // [zh] 排序字段
  // [en] Sort resoults.
  string order_by = 5;

  // [zh] 分页大小
  // [en] The maximum number of items to return.
  int32 page_size = 6;

  // [zh] 分页token
  // [en] The next_page_token value returned from a previous List request, if any.
  string page_token = 7;

  // [zh] 租户ID
  // [en] tenant_id.
  string tenant_id = 8;

  // [zh] 负载均衡名称
  // [en] SLB name
  string slb_name = 9 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 列举 Listeners 的响应
// [en] Response to list Listeners
message ListListenersResponse {
  // [zh] Listener 列表
  // [en] Listener list
  repeated ListenerStatusInfo listener_status_infos = 1;

  // [zh] 下一个页面的 token，如果没有更多数据则为空.
  // [en] Token to retrieve the next page of results, or empty if there are no more results in the list.
  string next_page_token = 2;

  // [zh] 总数量
  // [en] total size
  int32 total_size = 3;
}

// [zh] 获取某一个 Listener 的请求
// [en] Request to get a Listener
message GetListenerRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 监听器名称
  // [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string listener_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 获取某一个 Listener 状态的请求
// [en] Request to get a Listener status
message GetListenerStatusRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 监听器名称
  // [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string listener_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 启动一个监听器的请求
// [en] Request to start a Listener
message StartListenerRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 监听器名称
  // [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string listener_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 停止一个监听器的请求
// [en] Request to stop a Listener
message StopListenerRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 监听器名称
  // [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string listener_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 后端组创建字段
// [en] TargetGroup creation properties
message CreateTargetGroupRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1;

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2;

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3;

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 后端组名称
  // [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string target_group_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 创建的后端组
  // [en] The Target resource to create
  TargetGroup target_group = 6;
}

// [zh] 更新后端组的可编辑字段
// [en] Update TargetGroup editable properties.
message UpdateTargetGroupRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1;

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2;

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3;

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 后端组名称
  // [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string target_group_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 更新的后端组
  // [en] The Target resource to create
  TargetGroupUpdate target_group = 6;

  // [zh] 更新标记.
  // [en] The resource update mask.
  google.protobuf.FieldMask update_mask = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// [zh] 删除一个后端组
// [en] Delete one TargetGroup
message DeleteTargetGroupRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1;

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2;

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3;

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 后端组名称
  // [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string target_group_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}


// [zh] 列举符合请求的后端组
// [en] List requested TargetGroup
message ListTargetGroupsRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 过滤字段
  // [en] List filter.
  string filter = 4;

  // [zh] 排序字段
  // [en] Sort resoults.
  string order_by = 5;

  // [zh] 分页大小
  // [en] The maximum number of items to return.
  int32 page_size = 6;

  // [zh] 分页token
  // [en] The next_page_token value returned from a previous List request, if any.
  string page_token = 7;

  // [zh] 租户ID
  // [en] tenant_id.
  string tenant_id = 8;

  // [zh] 负载均衡名称
  // [en] SLB name
  string slb_name = 9 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 列举符合请求的后端组
// [en] Response to list TargetGroup
message ListTargetGroupsResponse {
  // [zh] TargetGroup 列表
  // [en] TargetGroup list
  repeated TargetGroupStatus target_groups = 1;

  // [zh] 下一个页面的 token，如果没有更多数据则为空.
  // [en] Token to retrieve the next page of results, or empty if there are no more results in the list.
  string next_page_token = 2;

  // [zh] 总数量
  // [en] total size
  int32 total_size = 3;
}

// [zh] 获取符合请求的后端组
// [en] Get requested TargetGroup
message GetTargetGroupRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 后端组名称
  // [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string target_group_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 获取符合请求的后端组
// [en] Get requested TargetGroup
message GetTargetGroupStatusRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 后端组名称
  // [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string target_group_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 添加一个或多个后端到某个后端组.
// [en] Add one or more backend targets to a TargetGroup.
message TargetGroupAddTargetsRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 后端组名称
  // [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string target_group_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 后端
  // [en] The TargetGroup resource to create
  repeated Target targets = 6;
}

// [zh] 添加一个或多个后端到某个后端组
// [en] Response to add targets
message TargetGroupAddTargetsResponse {
  // [zh] 当前后端组包含的后端列表
  // [en] List of backends contained in the current backend group
  repeated Target curr_targets = 1;
}

// [zh] 从某个后端组删除一个或多个后端.
// [en] Remove one or more backend targets from a TargetGroup.
message TargetGroupRemoveTargetsRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 后端组名称
  // [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string target_group_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 后端ID列表
  // [en] The Target uids
  repeated string target_ids = 6;
}

// [zh] 从某个后端组删除一个或多个后端.
// [en] Response to remove targets
message TargetGroupRemoveTargetsResponse {
  // [zh] 后端组包含的后端列表
  // [en] List of backends contained in the current backend group
  repeated Target curr_targets = 1;
}

// [zh] 修改某个后端组的一台或者多台后端的属性.
// [en] Update the editable properties of one or more backends of a TargetGroup.
message TargetsUpdateRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 后端组名称
  // [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string target_group_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [en] The TargetGroup resource to update
  repeated TargetUpdate target_group_targets = 6;

  // [zh] 更新标记.
  // [en] update_mask
  google.protobuf.FieldMask update_mask = 7 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// [zh] 修改某个后端组的后端属性的响应
// [en] Response to modifying the backend properties of a backend group
message TargetGroupTargets {
  // [zh] 后端组包含的后端列表
  // [en] List of backends contained in the current backend group
  repeated Target curr_targets = 1;
}

// [zh] 查询某个后端组绑定的后端列表
// [en] List requested TargetGroup targets
message ListTargetsRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 过滤字段
  // [en] List filter.
  string filter = 4;

  // [zh] 排序字段
  // [en] Sort resoults.
  string order_by = 5;

  // [zh] 分页大小
  // [en] The maximum number of items to return.
  int32 page_size = 6;

  // [zh] 分页token
  // [en] The next_page_token value returned from a previous List request, if any.
  string page_token = 7;

  // [zh] 租户ID
  // [en] tenant_id.
  string tenant_id = 8;

  // [zh] 负载均衡名称
  // [en] SLB name
  string slb_name = 9 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
 
  // [zh] 后端组名称
  // [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string target_group_name = 10 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 查询某个后端组绑定的后端列表
// [en] Response to list TargetGroup targets
message ListTargetsResponse {
  // [zh] Target 列表
  // [en] Target list
  repeated TargetInfo target_infos = 1;

  // [zh] 下一个页面的 token，如果没有更多数据则为空.
  // [en] Token to retrieve the next page of results, or empty if there are no more results in the list.
  string next_page_token = 2;

  // [zh] 总数量
  // [en] total size
  int32 total_size = 3;
}

// [zh] 查询某个后端组绑定的后端
// [en] Get requested TargetGroup target
message GetTargetRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 后端组名称
  // [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string target_group_name = 5 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 后端名称
  // [en] The Target resource name
  string target_name = 6 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] SLB 的后端状态
// [en] SLB status
message SLBStatus {
  // [zh] SLB 配置
  // [en] SLB info
  SLB slb = 1;

  // [zh] 公网VIP
  // [en] internet vip
  string internet_vip = 2;

  // [zh] 内网VIP
  // [en] intranet vip
  string intranet_vip = 3;

  // [zh] 基础网络VIP
  // [en] basic network vip
  string basic_network_vip = 4;

  // [zh] 是否暴露基础网络VIP
  // [en] expose basic network vip
  bool expose_basic_network_vip = 5;

  // [zh] vpc 信息
  // [en] vpc info
  VPCInfo vpc = 6;

  // [zh] eip 信息
  // [en] eip info
  EIPInfo eip = 7;

  // [zh] 监听器信息
  // [en] listener info
  repeated ListenerInfo listeners = 8;
}

// [zh] 监听器资源.
// [en] The Listener resource.
message Listener {
  // [zh] 资源id.
  // [en] The Listener resource id using the form:
  //     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/slbs/{slb_name}/listeners/{name}`.
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源的uuid.
  // [en] The Listener resource uuid.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源标识.
  // [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 3;

  // [zh] 资源名称.
  // [en] The Listener resource display name
  string display_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // [zh] 资源描述.
  // [en] The Listener resource description.
  string description = 5;

  // [zh] 资源类型.
  // [en] The Listener resource type.
  string resource_type = 6;

  // [zh] 创建者id.
  // [en] The id of the user who created the Listener resource.
  string creator_id = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 拥有者id.
  // [en] The id of the user who owns the Listener resource.
  string owner_id = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 租户id
  // [en] Tenant id.
  string tenant_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 监听器状态
  // [en] Represents the different states of a Listener.
  enum State {
    // [zh] 创建中
    // [en] The Listener resource is being created.
    CREATING = 0;

    // [zh] 更新中
    // [en] The Listener resource is being updated.
    UPDATING = 1;

    // [zh] 已激活
    // [en] The Listener resource has been active.
    ACTIVE = 2;

    // [zh] 删除中
    // [en] The Listener resource is being deleting.
    DELETING = 3;

    // [zh] 已删除
    // [en] The Listener resource has been deleted.
    DELETED = 4;

    // [zh] 操作失败
    // [en] The Listener resource has been failed.
    FAILED = 5;

    // [zh] 停止中
    // [en] The Listener resource has been stopping
    STOPPING = 6;

    // [zh] 已停止
    // [en] The Listener resource has been stopped
    STOPPED = 7;

    // [zh] 启动中
    // [en] The Listener resource has been starting
    STARTING = 8;
  };

  // [zh] 资源状态.
  // [en] The current state of the Listener resource.
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 监听器资源的标签.
  // [en] Tags attached to the VirtualMachine resource.
  map<string, string> tags = 12;

  // [zh] 监听器资源的属性
  // [en] Properties of the VirtualMachine resource.
  ListenerProperties properties = 13; // [(validate.rules).message.required = true];

  // [zh] 资源是否已删除.
  // [en] Indicates whether the Listener resource is deleted or not.
  bool deleted = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源创建时间.
  // [en] The time when the Listener resource was created.
  google.protobuf.Timestamp create_time = 15 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源更新时间.
  // [en] The time when the Listener resource was last updated.
  google.protobuf.Timestamp update_time = 16 [(google.api.field_behavior) = OUTPUT_ONLY];
}


// [zh] Listener 资源实际属性
// [en] Real resource properties of Listener
message ListenerProperties {
  // [zh] 监听的协议枚举
  // [en] Listener protocol enum
  enum ListenProtocol {
    // [zh] 无效值
    // [en] Invalid protocol
    INVALID = 0;

    // [zh] TCP
    // [en] TCP
    TCP = 1;

    // [zh] UDP
    // [en] UDP
    UDP = 2;
  };

  // [zh] 监听的协议
  // [en] Listener protocol
  ListenProtocol protocol = 1;

  // [zh] 监听的端口
  // [en] Listener port
  int32 port = 2;

  // [zh] 默认转发动作
  // [en] Default forward action
  ForwardAction default_forward_action = 3;

  // [zh] 源地址保持配置
  // [en] Config of retaine original client IP address 
  OriginalClientIPAddress original_client_ip_address = 4;
}

// [zh] Listener 的信息
// [en] Listener info
message ListenerStatusInfo {
  // [zh] 监听器资源配置
  // [en] Listener info.
  Listener listener = 1;

  // [zh] 监听器资源状态
  // [en] Listener status.
  ListenerStatus status = 2;
}

// [zh] Listener 的状态
// [en] Listener status
message ListenerStatus {
  // [zh] 健康检查结果
  // [en] Health check results
  ListenerHealthCheckStatus health_check_status = 2;
}

// [zh] 后端组
// [en] TargetGroup
message TargetGroup {
  // [zh] 资源ID
  // [en] The Listener resouce id using the form:
  //     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{name}`.
  string id = 1;

  // [zh] 资源UUID
  // [en] The TargetGroup uuid
  string uid = 2;

  // [zh] 资源标识.
  // [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 3 [(validate.rules).string = { pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63 }];

  // [zh] 资源名称.
  // [en] The TargetGroup resource display name
  string display_name = 4 [(validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // [zh] 资源描述.
  // [en] The TargetGroup resource description.
  string description = 5;

  // [zh] 资源类型.
  // [en] The TargetGroup resource type.
  string resource_type = 6;

  // [zh] 创建者id.
  // [en] The id of the user who created the TargetGroup resource.
  string creator_id = 7;

  // [zh] 拥有者id.
  // [en] The id of the user who owns the TargetGroup resource.
  string owner_id = 8;

  // [zh] 租户id
  // [en] Tenant id.
  string tenant_id = 9;

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 10;

  // [zh] 后端组状态
  // [en] Represents the different states of a TargetGroup.
  enum State {
    // [zh] 创建中
    // [en] The TargetGroup resource is being created.
    CREATING = 0;

    // [zh] 更新中
    // [en] The TargetGroup resource is being updated.
    UPDATING = 1;

    // [zh] 已激活
    // [en] The TargetGroup resource has been active.
    ACTIVE = 2;

    // [zh] 删除中
    // [en] The TargetGroup resource is being deleting.
    DELETING = 3;

    // [zh] 已删除
    // [en] The TargetGroup resource has been deleted.
    DELETED = 4;

    // [zh] 操作失败
    // [en] The TargetGroup resource has been failed.
    FAILED = 5;
  };

  // [zh] 资源状态.
  // [en] The current state of the TargetGroup resource.
  State state = 11;

  // [zh] 资源的标签.
  // [en] Tags attached to the TargetGroup resource.
  map<string, string> tags = 12;

  // [zh] 后端组属性
  // [en] Properties of the TargetGroup resource.
  TargetGroupProperties properties = 13;

  // [zh] 是否删除
  // [en] Indicates whether the TargetGroup resource is deleted or not.
  bool deleted = 14;

  // [zh] 创建时间
  // [en] The time when the TargetGroup resource was created.
  google.protobuf.Timestamp create_time = 15;

  // [zh] 更新时间
  // [en] The time when the TargetGroup resource was last updated.
  google.protobuf.Timestamp update_time = 16;
}

// [zh] 后端组资源实际属性
// [en] Real resource properties of TargetGroup.
message TargetGroupProperties {
  // [zh] SLB 后端支持的类型
  // [en] SLB target type
  enum SLBTargetType {
    // [zh] 无效值
    // [en] Invalid type
    INVALID = 0;

    // [zh] CCI部署创建的实例
    // [en] CCI instance of CCI Deployment
    CCI_DEPLOYMENT_INSTANCE = 1;

    // [zh] 服务器实例，包含 ECS、BMS 等
    // [en] Server instance
    SERVER_INSTANCE = 2;
  }

  // [zh] 后端组的类型
  // [en] TargetGroup type
  SLBTargetType type = 1;

  // [zh] 调度算法取值
  // [en] Scheduler enum
  enum TargetGroupSchedulerType {
    // [zh] 轮询, 默认值
    // [en] RR, round-robin, default
    RR = 0;

    // [zh] 加权轮询
    // [en] WRR, wight round-robin
    WRR = 1;
  };

  // [zh] 负载算法
  // [en] The scheduling algorithm
  TargetGroupSchedulerType scheduler = 2;

  // [zh] 后端使用的协议类型枚举
  // [en] The protocol used by the Target
  enum TargetProtocol {
    // [zh] 默认值，默认与监听器监听协议保持一致
    // [en] Default value, which is consistent with the listener listening protocol.
    DEFAULT = 0;

    // [zh] HTTP, 支持 HTTP、HTTPS QUIC 监听器与之建立链接
    // [en] HTTP, supports HTTP and HTTPS QUIC listeners to establish links with it
    HTTP = 1;

    // [zh] HTTPS, 支持 HTTPS 监听器与之建立链接
    // [en] HTTPS, supports HTTPS listeners to establish connections with it
    HTTPS = 2;

    // [zh] TCP, 支持 TCP 监听器与之建立链接
    // [en] TCP, supports TCP listener to establish a connection with it
    TCP = 3;

    // [zh] UDP, 支持 UDP 监听器与之建立链接
    // [en] UDP, supports UDP listener to establish a connection with it
    UDP = 4;

    // [zh] TCPSSL, 支持 TCPSSL 监听器与之建立链接
    // [en] TCPSSL, supports TCPSSL listener to establish a connection with it
    TCPSSL = 5;
  };

  // [zh] 后端使用的协议，默认与监听协议保持一致
  // [en] The protocol used by the Target
  TargetProtocol protocol = 3;

  // [zh] 健康检查配置
  // [en] Health check config
  HealthCheck health_check = 4;

  // [zh] 后端组权重，范围 0~100，默认100，如果为0，则不转发请求到这个后端组
  // [en] The weight of the TargetGroup. Valid values: 0 to 100. Default value: 100. If the weight of a TargetGroup is set to 0, no requests are forwarded to the TargetGroup.
  int32 weight = 5;

  // [zh] CCI应用信息，类型为： CCI_DEPLOYMENT_INSTANCE 生效
  // [en] The CCI application info takes effect when the type is CCI_DEPLOYMENT_INSTANCE.
  CciDeploymentInstanceInfo cci_info = 6;
}

// [zh] TargetGroup 的状态
// [en] TargetGroup status`
message TargetGroupStatus {
  // [zh] 后端组配置
  // [en] Target group info
  TargetGroup target_group = 1;

  // [zh] 关联的监听器信息
  // [en] Associated Listener Information
  repeated ListenerInfo listeners = 2;

  // [zh] 所属VPC信息
  // [en] VPC info
  VPCInfo vpc = 3;
}

// [zh] Target 实体结构
// [en] Target entity
message Target {
  // [zh] 资源id
  // [en] The Listener resouce id using the form:
  //     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}/targets/{name}`.
  string id = 1;

  // [zh] 资源uuid
  // [en] The Target uuid
  string uid = 2;

  // [zh] 资源标识
  // [en] The Target resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 3 [(validate.rules).string = { pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63 }];

  // [zh] 资源名称
  // [en] The Target resource display name
  string display_name = 4 [(validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // [zh] 资源描述
  // [en] The Target resource description.
  string description = 5;

  // [zh] 资源类型
  // [en] The Target resource type.
  string resource_type = 6;

  // [zh] 创建者id
  // [en] The id of the user who created the Target resource.
  string creator_id = 7;

  // [zh] 拥有者id
  // [en] The id of the user who owns the Target resource.
  string owner_id = 8;

  // [zh] 租户id
  // [en] Tenant id.
  string tenant_id = 9;

  // [zh] 可用区
  // [en] Available zone.
  string zone = 10;

  // [zh] 后端资源状态
  // [en] Represents the different states of a Target.
  enum State {
    // [zh] 增加中
    // [en] The Target resource is being adding.
    ADDING = 0;

    // [zh] 更新中
    // [en] The Target resource is being updated.
    UPDATING = 1;

    // [zh] 已激活
    // [en] The Target resource has been active.
    ACTIVE = 2;

    // [zh] 移除中
    // [en] The Target resource is being removing.
    REMOVING = 3;

    // [zh] 已移除
    // [en] The Target resource has been removed.
    REMOVED = 4;

    // [zh] 操作失败
    // [en] The Target resource has been failed.
    FAILED = 5;
  };

  // [zh] 资源状态
  // [en] The current state of the Target resource.
  State state = 11;

  // [zh] 资源标签
  // [en] Tags attached to the Target resource.
  map<string, string> tags = 12;

  // [zh] 资源属性
  // [en] Properties of the Target resource.
  TargetProperties properties = 13;

  // [zh] 是否删除
  // [en] Indicates whether the Target resource is deleted or not.
  bool deleted = 14;

  // [zh] 创建时间
  // [en] The time when the Target resource was created.
  google.protobuf.Timestamp create_time = 15;

  // [zh] 更新时间
  // [en] The time when the Target resource was last updated.
  google.protobuf.Timestamp update_time = 16;
}

// [zh] Target 资源实际属性
// [en] Real resource properties of Target
message TargetProperties {
  // [zh] IPv4地址
  // [en] ipv4 address
  string ipv4_address = 1;

  // [zh] 后端使用的端口
  // [en] The port used by the Target.
  int32 port = 2;

  // [zh] 后端权重，范围 0~100，默认100，如果为0，则不转发请求到这个后端
  // [en] The weight of the Target. Valid values: 0 to 100. Default value: 100. If the weight of a target is set to 0, no requests are forwarded to the target.
  int32 weight = 3;

  // [zh] SLB 后端支持的类型
  // [en] SLB target type
  enum SLBTargetType {
    // [zh] 无效值
    // [en] Invalid type
    INVALID = 0;

    // [zh] CCI部署创建的实例
    // [en] CCI instance of CCI Deployment
    CCI_DEPLOYMENT_INSTANCE = 1;

    // [zh] ECS云服务器实例
    // [en] ECS instance_name
    ECS_INSTANCE = 2;

    // [zh] 裸金属实例
    // [en] BMS instance
    BMS_INSTANCE = 3;
  }

  // [zh] 后端类型
  // [en] Target type
  SLBTargetType type = 4;

  // [zh] 实例名称
  // [en] instanceName
  string instance_name = 5;
}

// [zh] 健康检查配置
// [en] Health check config
message HealthCheck {
  // [zh] 是否开启健康检查
  // [en] Health check switch
  bool enable = 1;

  // [zh] 健康检查类型枚举
  // [en] Health check type enum
  enum HealthCheckType {
    // [zh] 无效值
    // [en] Invalid value
    INVALID = 0;

    // [zh] TCP
    // [en] TCP
    TCP = 1;

    // [zh] HTTP
    // [en] HTTP
    HTTP = 2;
  };

  // [zh] 健康检查类型
  // [en] Healcheck type
  HealthCheckType type = 2;

  // [zh] 等待健康检查的超时时间，单位: 毫秒
  // [en] The time to wait for a health check response, unit: ms.
  int32 timeout = 3;

  // [zh] 检查间隔，单位: 秒
  // [en] The interval between health checks.
  int32 interval = 4;

  // [zh] 健康状态阈值，主机被标记为健康之前所需的健康健康检查次数。启动的时候，只需要一次，即可立即进入健康状态。
  // [en] The number of healthy health checks required before a host is marked healthy
  int32 health_threshold = 5;

  // [zh] 不健康状态阈值，主机被标记为不健康之前所需的不健康健康检查次数。
  // [en] The number of unhealthy health checks required before a host is marked unhealthy
  int32 unhealth_threshold = 6;

  // [zh] TCP 主动健康检查配置，当健康检查类型为 TCP 的时候，配置生效
  // [en] TCP health check config
  TCPHealthCheckConfig tcp_health_check_config = 7;

  // [zh] HTTP 主动健康检查配置，当健康检查类型为 HTTP 的时候，配置生效
  // [en] HTTP health check config
  HTTPHealthCheckConfig http_health_check_config = 8;
}

// [zh] 健康检查结果
// [en] Health check status
message ListenerHealthCheckStatus {
  // [zh] 后端健康检查状态类型
  // [en] health check status enum
  enum HealthCheckState {
    // [zh] 初始化
    // [en] The Target health check status is initial
    INITIAL = 0;

    // [zh] 健康
    // [en] The Target health check status is health
    HEALTH = 1;

    // [zh] 部分健康
    // [en] The Target health check status is health partly
    PARTLY_HEALTH = 2;
    
    // [zh] 异常
    // [en] The Target health check status is unhealth
    UNHEALTH = 3;
    
    // [zh] 未开启
    // [en] The Target health check status is unhealth
    DISABLE = 4;
  };

  // [zh] 后端健康检查状态
  // [en] health check status
  HealthCheckState state = 1;
}

// [zh] Target 实体结构
// [en] Target entity
message TargetInfo {
  // [zh] Target 实体结构
  // [en] Target entity
  Target target = 1;

  // [zh] Target 状态
  // [en] Target status
  TargetStatus status = 2;
}

// [zh] Target 实体结构
// [en] Target entity
message TargetStatus {
  // [zh] Target 健康检查状态
  // [en] Target health check status
  TargetHealthCheckStatus health_check = 1;
}

// [zh] 后端的健康检查结果
// [en] Target health check status
message TargetHealthCheckStatus {
  // [zh] 后端健康检查状态类型
  // [en] health check status enum
  enum State {
    // [zh] 初始化
    // [en] The Target health check status is initial
    INITIAL = 0;

    // [zh] 健康
    // [en] The Target health check status is health
    HEALTH = 1;

    // [zh] 不健康
    // [en] The Target health check status is unhealth
    UNHEALTH = 2;

    // [zh] 未开启
    // [en] The Target health check status is disable
    DISABLE = 3;
  };

  // [zh] 健康检查状态
  // [en] Health check status
  State state = 1;
}

// [zh] 转发动作
// [en] Forward action
message ForwardAction {
  // [zh] 转发动作类型枚举
  // [en] forward action type
  enum ForwardActionType {
    // [zh] 无效值
    // [en] Invalid value
    INVALID = 0;

    // [zh] 转发到后端组
    // [en] Forward TargetGroup
    FORWARD_TARGET_GROUP = 1;
  };

  // [zh] 转发动作类型
  // [en] Forward action type
  ForwardActionType type = 1;

  // [zh] 转发动作类型为 ForwardTarget 的配置, 当 type 为 ForwardTargetGroup，此字段生效
  // [en] ForwardTarget config
  ForwardTargetGroupConfig forward_target_group_config = 2;
}

// [zh] 转发到目的后端的配置
// [en] ForwardTarget config
message ForwardTargetGroupConfig {
  // [zh] 后端组列表
  // [en] TargetGroup list
  repeated TargetGroupInfo target_groups = 1;
}

// [zh] 源地址保持配置
// [en] Config of retaine original client IP address
message OriginalClientIPAddress {
  // [zh] 是否开启源地址保持，默认关闭
  // [en] Original client IP address retaine enable
  bool original_client_ip_address_enable = 1;

  // [zh] 四层 Proxy Protocol 配置，当 源地址保持 开启，且监听协议为四层协议的时候，此字段生效
  // [en] The configuration of Proxy Protocol
  bool proxy_protocol_enable = 2;
}

// [zh] TCP 检查检查配置
// [en] TCP health check config
message TCPHealthCheckConfig {
  // [zh] 健康检查端口，发送SYN握手报文来检测服务器端口是否存活
  // [en] Health check port, send SYN handshake packets to detect if the server port is alive.
  int32 port = 1;
}

// [zh] HTTP 检查检查配置
// [en] HTTP health check config
message HTTPHealthCheckConfig {
  // [zh] HTTP 检查的host
  // [en] HTTP health check host
  string host = 1;

  // [zh] HTTP 检查的host的类型
  // [en] HTTP health check host type
  enum HttpHostType {
    // [zh] ip
    // [en] ip
    IP = 0;

    // [zh] name
    // [en] name
    NAME = 1;
  };

  // [zh] host类型
  // [en] host type
  HttpHostType host_type = 2;


  // [zh] HTTP 检查的path
  // [en] HTTP health check path
  string path = 3;

  // [zh] http 方式
  // [en] http method
  enum HTTPMethod {
    // [zh] HEAD
    // [en] HEAD
    HEAD = 0;
 
    // [zh] GET
    // [en] GET
    GET = 1;
  };

  // [zh] HTTP 检查的方式
  // [en] HTTP health check host
  HTTPMethod method = 4;

  // [zh] HTTP 版本
  // [en] HTTP version
  enum HTTPVersion {
    // [zh] HTTP1
    // [en] HTTP1
    HTTP1 = 0;

    // [zh] HTTP2
    // [en] HTTP2
    HTTP2 = 1;
  };

  // [zh] HTTP 检查的版本
  // [en] HTTP health check version
  HTTPVersion version = 5;

  enum HTTPReturnCode {
    // [zh] 1XX
    code_1XX = 0;

    // [zh] 2XX
    code_2XX = 1;

    // [zh] 3XX
    code_3XX = 2;

    // [zh] 4XX
    code_4XX = 3;

    // [zh] 5XX
    code_5XX = 4;
  };

  // [zh] HTTP返回码
  // [ch] HTTP return code
  repeated HTTPReturnCode return_code = 6;
}

// [zh] VPC 信息
// [en] VPC info
message VPCInfo {
  // [zh] ID
  // [en] ID
  string id = 1;

  // [zh] UID
  // [en] UID
  string uid = 2;

  // [zh] name
  // [en] name
  string name = 3;

  // [zh] display_name
  // [en] display_name
  string display_name = 4;
}

// [zh] EIP 信息
// [en] EIP info
message EIPInfo {
  // [zh] ID
  // [en] ID
  string id = 1;

  // [zh] UID
  // [en] UID
  string uid = 2;

  // [zh] name
  // [en] name
  string name = 3;

  // [zh] display_name
  // [en] display_name
  string display_name = 4;
}

// [zh] 监听器信息
// [en] Listener info
message ListenerInfo {
  // [zh] ID
  // [en] EIP ID
  string id = 1;

  // [zh] UID
  // [en] UID
  string uid = 2;

  // [zh] name
  // [en] name
  string name = 3;

  // [zh] display_name
  // [en] display_name
  string display_name = 4;
  
  // [zh] Listener 资源实际属性
  // [en] Real resource properties of Listener
  ListenerProperties properties = 5;
}

// [zh] 后端组信息
// [en] target group info
message TargetGroupInfo {
  // [zh] ID
  // [en] EIP ID
  string id = 1;

  // [zh] UID
  // [en] UID
  string uid = 2;

  // [zh] name
  // [en] name
  string name = 3;

  // [zh] display_name
  // [en] display_name
  string display_name = 4;
}

// [zh] 监听器可更新资源.
// [en] The Listener update-resource.
message ListenerUpdate {
  // [zh] 资源名称.
  // [en] The Listener resource display name
  optional string display_name = 1 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // [zh] 资源描述.
  // [en] The Listener resource description.
  string description = 2;

  // [zh] 监听器资源的属性
  // [en] Properties of the VirtualMachine resource.
  ListenerProperties properties = 3;
}

// [zh] 后端组可更新资源.
// [en] The TargetGroup update-resource.
message TargetGroupUpdate {
  // [zh] 资源名称.
  // [en] The TargetGroup resource display name
  optional string display_name = 1 [(validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // [zh] 资源描述.
  // [en] The TargetGroup resource description.
  string description = 2;

  // [zh] 后端组属性
  // [en] Properties of the TargetGroup resource.
  TargetGroupProperties properties = 3;
}

// [zh] 后端可更新资源.
// [en] The Target update-resource.
message TargetUpdate {
  // [zh] 资源uuid
  // [en] The Target uuid
  string uid = 1;
 
  // [zh] 资源名称
  // [en] The Target resource display name
  optional string display_name = 2 [(validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // [zh] 资源描述
  // [en] The Target resource description.
  string description = 3;

  // [zh] 资源属性
  // [en] Properties of the Target resource.
  TargetProperties properties = 4;
}

// [zh] 容器应用部署信息
// [en] CCI deployment instance info
message CciDeploymentInstanceInfo {
  // [zh] instance name
  // instance name
  string name = 1;

  // [zh] instance display name
  // [en] instance display name
  string display_name = 2;

  // [zh] instance id
  // [en] instance id
  string id = 3;

  // [zh] instance uid
  // [en] instance uid
  string uid = 4;

  // [zh] instance port
  // [en] instance port
  int32 port = 5;
}
