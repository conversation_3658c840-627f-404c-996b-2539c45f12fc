syntax = "proto3";
package sensetime.core.network.slb.v1;
option go_package = "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/slb/v1;slb";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/api/annotations.proto";
import "google/protobuf/field_mask.proto";
import "higgs/common/v1/orderinfo.proto";

// [zh] 负载均衡服务
// [en] SLBs Service.
service SLBs {
  // [zh] 创建负载均衡资源.
  // [en] Creates SLB resources.
  rpc CreateSLB(CreateSLBRequest) returns (SLB) {
    option (google.api.http) = {
      post : "/network/slb/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      body : "slb"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.create"
    };
  }

  // [zh] 删除负载均衡资源.
  // [en] Delete the SLB resources.
  rpc DeleteSLB(DeleteSLBRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete : "/network/slb/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.delete"
    };
  }

  // [zh] 更新负载均衡资源.
  // [en] Update the SLB resources.
  rpc UpdateSLB(UpdateSLBRequest) returns (SLB) {
    option (google.api.http) = {
      patch: "/network/slb/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      body: "slb"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.is_asynchronous) = false;
     option (sensetime.core.higgs.api.policy) = {
       scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
       permission: "slb.slb.update"
     };
  }

  // [zh] 查看负载均衡资源详情.
  // [en] Gets details of a SLB resources.
  rpc GetSLB(GetSLBRequest) returns (SLB) {
    option (google.api.http) = {
      get : "/network/slb/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.get"
    };
  }

  // [zh] 释放 (保留期释放).
  // [en] The Release of a SLB resources.
  rpc ReleaseSLB(ReleaseSLBRequest) returns (google.protobuf.Empty) {
    option(google.api.http) = {
      post: "/network/slb/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}:release"
      body: "*"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}"
      permission: "slb.slb.delete"
    };
  }
}

// [zh] 创建负载均衡资源请求.
// [en] CreateSLBRequest.
message CreateSLBRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1;

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2;

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3;

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 创建负载均衡资源.
  // [en] The SLB resource to create.
  SLB slb = 5; // [(validate.rules).message.required = true];
}

// [zh] 删除负载均衡资源请求.
// [en] DeleteSLBRequest.
message DeleteSLBRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 更新负载均衡请求.
// [en] Update SLB request.
message UpdateSLBRequest{
  // [zh] 订阅.
  // [en] The resource subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] The resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] The resource available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源名称.
  // [en] The resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 更新资源.
  // [en] The resource to update.
  SlbUpdate slb = 5;

  // [zh] 更新标记.
  // [en] The resource update mask.
  google.protobuf.FieldMask update_mask = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// [zh] 查看负载均衡资源详情请求.
// [en] GetSLBRequest.
message GetSLBRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 负载均衡资源名称.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 释放负载均衡资源.
// [en] Release SLB resource.
message ReleaseSLBRequest {
    // [zh] 订阅名.
    // [en] Subscription.
    string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

    // [zh] 资源组.
    // [en] Resource group.
    string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

    // [zh] 可用区.
    // [en] Available zone.
    string zone = 3 [(google.api.field_behavior) = REQUIRED];

    // [zh] 负载均衡资源名称.
    // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    string slb_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 负载均衡资源.
// [en] The SLB resource.
message SLB {
  // [zh] 资源id.
  // [en] The SLB resource id using the form:
  //     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/slbs/{name}`.
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源的uuid.
  // [en] The SLB resource uuid.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源标识.
  // [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 3;

  // [zh] 资源名称.
  // [en] The SLB resource display name
  string display_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // [zh] 资源描述.
  // [en] The SLB resource description.
  string description = 5;

  // [zh] 资源类型.
  // [en] The SLB resource type.
  string resource_type = 6;

  // [zh] 创建者id.
  // [en] The id of the user who created the SLB resource.
  string creator_id = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 拥有者id.
  // [en] The id of the user who owns the SLB resource.
  string owner_id = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 租户id
  // [en] Tenant id.
  string tenant_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源状态.
  // [en] Represents the different states of a SLB.
  enum State {
    // [zh] 创建中.
    // [en] The SLB resource is being created.
    CREATING = 0;

    // [zh] 更新中.
    // [en] The SLB resource is being updated.
    UPDATING = 1;

    // [zh] 已激活.
    // [en] The SLB resource has been active.
    ACTIVE = 2;

    // [zh] 删除中.
    // [en] TThe SLB resource is being deleted.
    DELETING = 3;

    // [zh] 已删除.
    // [en] The SLB resource has been deleted.
    DELETED = 4;

    // [zh] 操作失败.
    // [en] The SLB resource has failed.
    FAILED = 5;

    // [zh] 到期降级中【未使用】
    // [en]  The SLB resource is being expireDowngrading.
    EXPIREDOWNGRADING = 6;

    // [zh] 到期已降级【未使用】
    // [en]   The SLB resource has been expireDowngrade.
    EXPIREDOWNGRADED = 7;

    // [zh] 续订升级中【未使用】
    // [en]   The SLB resource is being renewUpgrading.
    RENEWUPGRADING = 8;

    // [zh] 到期停服中.
    // [en] The SLB resource is being disabled.
    EXPIRESTOPPING = 9;

    // [zh] 到期已停服.
    // [en] The SLB resource has been disabled.
    EXPIRESTOPPED = 10;

    // [zh] 续订恢复中.
    // [en] The SLB resource is being enabled.
    RENEWSTARTING = 11;

    // [zh] 服务降级中【瞬时】【未使用】.
    // [en] Default, the SLB is being downgrading.
    DOWNGRADING = 12 ;

    // [zh] 服务降级【未使用】.
    // [en] Default, the SLB is being downgraded.
    DOWNGRADE = 13 ;

    // [zh] 服务降级恢复中【瞬时】【未使用】.
    // [en] Default, the SLB is being restoring.
    RESTORING=14;

    // [zh] 欠费停服中【瞬时】【未使用】.
    // [en] Default, the SLB is being arrearStopping.
    ARREARSTOPPING = 15;

    // [zh] 欠费停服【未使用】
    // [en] Default, the SLB is being arrearStopped.
    ARREARSTOPPED = 16;

    // [zh] 充值恢复中【瞬时】【未使用】.
    // [en] Default, the SLB is being rechargeStarting.
    RECHARGESTARTING = 17;

    // [zh] 资源变更中【瞬时】【未使用】.
    // [en] Default, the SLB is being resizing.
    RESIZING =18;

    // [zh] 取消资源变更【瞬时】【未使用】.
    // [en] Default, the aoss pack is being resizeCanceling.
    RESIZECANCELING = 19;
  };

  // [zh] 资源状态.
  // [en] The current state of the SLB resource.
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 最小库存单元id.
  // [en] SKU id.
  string sku_id = 12 [(google.api.field_behavior) = REQUIRED];

  // [zh] 负载均衡资源的标签.
  // [en] Tags attached to the VirtualMachine resource.
  map<string, string> tags = 13;

  // [zh] 负载均衡资源的属性
  // [en] Properties of the VirtualMachine resource.
  SLBProperties properties = 14; // [(validate.rules).message.required = true];

  // [zh] 订单信息.
  // [en] Order information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 15; // [(validate.rules).message.required = true];

  // [zh] 资源是否已删除.
  // [en] Indicates whether the SLB resource is deleted or not.
  bool deleted = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源创建时间.
  // [en] The time when the SLB resource was created.
  google.protobuf.Timestamp create_time = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源更新时间.
  // [en] The time when the SLB resource was last updated.
  google.protobuf.Timestamp update_time = 18 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// [zh] 资源属性.
// [en] The SLBProperties.
message SLBProperties {
  // [zh] 资源规格属性.
  // [en] Resource Specification Properties.
  Resources resources = 1;

  // [zh] SLB 类型
  // [en] SLB loadbalancer type
  LoadBalancerType type = 2;

  // [zh] 所属 VPC uid
  // [en] VPC uid
  string vpc_id = 3;

  // [zh] IP 协议栈版本
  // [en] IP protocol version
  SLBIPVersion ip_version = 4;

  // [zh] 所关联的 EIP uid.
  // [en] EIP uid.
  string eip_id = 5;
}

// [zh] 资源规格属性
// [en] Resource Specification Properties.
message Resources {
  // [zh] 资源的规格限制，对应运营系统SLB的描述属性
  // [en] Capacity limitations
  CapacityLimitations capacity_limitations = 1;
}

// [zh] 资源的描述属性
// [en] Description attribute
message CapacityLimitations {
  // [zh] TCP 新建连接数：每秒处理的新建TCP连接的数量，单位:秒
  // [en] CPS: the number of new TCP connections processed per second; Unit:Second; 
  int32 tcp_cps = 1;

  // [zh] TCP 并发连接数：每分钟内并发TCP连接的数量，单位：分钟
  // [en] CONNS: the number of concurrent TCP connections per minute; Unit:Minute
  int32 tcp_conns = 2;

  // [zh] UDP 新建连接数：每秒处理的新建UDP连接的数量，单位：秒
  // [en] The number of new UDP connections processed per second; Unit: Second
  int32 udp_cps = 3;

  // [zh] UDP 并发连接数：每分钟内并发UDP连接的数量，单位：分钟
  // [en] The number of concurrent UDP connections per minute; Unit: Second
  int32 udp_conns = 4;

  // [zh] TCPSSL 新建连接数：每秒处理的新建TCPSSL连接的数量，单位：秒
  // [en] The number of new TCPSSL connections processed per second; Unit: Second
  int32 tcpssl_cps = 5 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] TCPSSL 并发连接数：每分钟内并发TCPSSL连接的数量，单位：分钟
  // [en] The number of concurrent TCPSSL connections per minute; Unit: Second
  int32 tcpssl_conns = 6 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] HTTP/ 新建连接数：每秒处理的新建HTTP/连接的数量，单位：秒
  // [en] The number of new HTTP/ connections processed per second; Unit: Second
  int32 http_cps = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] HTTP/ 并发连接数：每分钟内并发HTTP/连接的数量，单位：分钟
  // [en] The number of concurrent HTTP/ connections per minute; Unit: Second
  int32 http_conns = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] HTTP/ 每秒查询数：每秒可以完成的HTTP或的查询（请求）的数量，单位：秒
  // [en] QPS: The number of HTTP/ queries (requests) that can be processed per second; Unit: Second
  int32 http_qps = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] HTTPS 新建连接数：每秒处理的新建HTTPS连接的数量，单位：秒
  // [en] The number of new HTTPS connections processed per second; Unit: Second
  int32 https_cps = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] HTTPS 并发连接数：每分钟内并发HTTPS连接的数量，单位：分钟
  // [en] The number of concurrent HTTPS connections per minute; Unit: Second
  int32 https_conns = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] HTTPS 每秒查询数：每秒可以完成的HTTPS的查询（请求）的数量，单位：秒
  // [en] QPS: The number of HTTPS queries (requests) that can be processed per second; Unit: Second
  int32 https_qps = 12 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// [zh] 负载均衡资源.
// [en] The SLB resource.
message SlbUpdate {
  // [zh] 资源名称.
  // [en] The SLB resource display name
  optional string display_name = 1 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // [zh] 资源描述.
  // [en] The SLB resource description.
  string description = 2;

  // [zh] 资源规格属性.
  // [en] Resource Specification Properties.
  Resources resources = 3;

  // [zh] SLB 类型
  // [en] SLB loadbalancer type
  LoadBalancerType type = 4;

  // [zh] IP 协议栈版本
  // [en] IP protocol version
  SLBIPVersion ip_version = 5;

  // [zh] 所属的 EIP uid.
  // [en] EIP uid.
  string eip_id = 6;
}

// [zh] SLB 类型
// [en] SLB loadbalancer type
enum LoadBalancerType {
  // [zh] 外网，默认值
  // [en] internet, default
  INTERNET = 0;

  // [zh] 内网
  // [en] intranet
  INTRANET = 1;
};

// [zh] IP 版本枚举
// [en] Represents the different states of IP version
enum SLBIPVersion {
  // [zh] IPv4, 默认值
  // [en] IPv4, default
  IPV4 = 0;

  // [zh] 双栈
  // [en] Dual Stack
  DUAL_STACK = 1;
};
