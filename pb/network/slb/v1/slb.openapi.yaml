# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: SLBs API
    description: "[zh] 负载均衡服务\r\n [en] SLBs Service."
    version: 0.0.1
paths:
    /network/slb/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}:
        get:
            tags:
                - SLBs
            description: "[zh] 查看负载均衡资源详情.\r\n [en] Gets details of a SLB resources."
            operationId: SLBs_GetSLB
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/SLB'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - SLBs
            description: "[zh] 创建负载均衡资源.\r\n [en] Creates SLB resources."
            operationId: SLBs_CreateSLB
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SLB'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/SLB'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - SLBs
            description: "[zh] 删除负载均衡资源.\r\n [en] Delete the SLB resources."
            operationId: SLBs_DeleteSLB
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - SLBs
            description: "[zh] 更新负载均衡资源.\r\n [en] Update the SLB resources."
            operationId: SLBs_UpdateSLB
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] The resource subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] The resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] The resource available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 资源名称.\r [en] The resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: update_mask
                  in: query
                  description: "[zh] 更新标记.\r [en] The resource update mask."
                  schema:
                    type: string
                    format: field-mask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/SlbUpdate'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/SLB'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/slb/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}:release:
        post:
            tags:
                - SLBs
            description: "[zh] 释放 (保留期释放).\r\n [en] The Release of a SLB resources."
            operationId: SLBs_ReleaseSLB
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅名.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ReleaseSLBRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        CapacityLimitations:
            type: object
            properties:
                tcp_cps:
                    type: integer
                    description: "[zh] TCP 新建连接数：每秒处理的新建TCP连接的数量，单位:秒\r [en] CPS: the number of new TCP connections processed per second; Unit:Second;"
                    format: int32
                tcp_conns:
                    type: integer
                    description: "[zh] TCP 并发连接数：每分钟内并发TCP连接的数量，单位：分钟\r [en] CONNS: the number of concurrent TCP connections per minute; Unit:Minute"
                    format: int32
                udp_cps:
                    type: integer
                    description: "[zh] UDP 新建连接数：每秒处理的新建UDP连接的数量，单位：秒\r [en] The number of new UDP connections processed per second; Unit: Second"
                    format: int32
                udp_conns:
                    type: integer
                    description: "[zh] UDP 并发连接数：每分钟内并发UDP连接的数量，单位：分钟\r [en] The number of concurrent UDP connections per minute; Unit: Second"
                    format: int32
                tcpssl_cps:
                    readOnly: true
                    type: integer
                    description: "[zh] TCPSSL 新建连接数：每秒处理的新建TCPSSL连接的数量，单位：秒\r [en] The number of new TCPSSL connections processed per second; Unit: Second"
                    format: int32
                tcpssl_conns:
                    readOnly: true
                    type: integer
                    description: "[zh] TCPSSL 并发连接数：每分钟内并发TCPSSL连接的数量，单位：分钟\r [en] The number of concurrent TCPSSL connections per minute; Unit: Second"
                    format: int32
                http_cps:
                    readOnly: true
                    type: integer
                    description: "[zh] HTTP/ 新建连接数：每秒处理的新建HTTP/连接的数量，单位：秒\r [en] The number of new HTTP/ connections processed per second; Unit: Second"
                    format: int32
                http_conns:
                    readOnly: true
                    type: integer
                    description: "[zh] HTTP/ 并发连接数：每分钟内并发HTTP/连接的数量，单位：分钟\r [en] The number of concurrent HTTP/ connections per minute; Unit: Second"
                    format: int32
                http_qps:
                    readOnly: true
                    type: integer
                    description: "[zh] HTTP/ 每秒查询数：每秒可以完成的HTTP或的查询（请求）的数量，单位：秒\r [en] QPS: The number of HTTP/ queries (requests) that can be processed per second; Unit: Second"
                    format: int32
                https_cps:
                    readOnly: true
                    type: integer
                    description: "[zh] HTTPS 新建连接数：每秒处理的新建HTTPS连接的数量，单位：秒\r [en] The number of new HTTPS connections processed per second; Unit: Second"
                    format: int32
                https_conns:
                    readOnly: true
                    type: integer
                    description: "[zh] HTTPS 并发连接数：每分钟内并发HTTPS连接的数量，单位：分钟\r [en] The number of concurrent HTTPS connections per minute; Unit: Second"
                    format: int32
                https_qps:
                    readOnly: true
                    type: integer
                    description: "[zh] HTTPS 每秒查询数：每秒可以完成的HTTPS的查询（请求）的数量，单位：秒\r [en] QPS: The number of HTTPS queries (requests) that can be processed per second; Unit: Second"
                    format: int32
            description: "[zh] 资源的描述属性\r [en] Description attribute"
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        OrderInfo:
            type: object
            properties:
                billing_cycle_number:
                    type: integer
                    description: '[zh] 购买时长. [en] length of purchase.'
                    format: int32
                auto_renew:
                    type: boolean
                    description: '[zh] 自动续费. [en] Automatic renewal.'
                currency_code:
                    type: string
                    description: '[zh] 货币代码. [en] currency code.'
                payment_channel:
                    type: integer
                    description: '[zh] 支付方式. [en] payment method.'
                    format: enum
                note:
                    type: string
                    description: '[zh] 订单备注. [en] order notes'
                order_type:
                    type: integer
                    description: '[zh] 订单类型. [en] Order Type.'
                    format: enum
                order_id:
                    type: string
                    description: '[zh] 订单id. [en] order id.'
                start_time:
                    type: string
                    description: '[zh] 订单生效日期. [en] Order effective date.'
                    format: date-time
                payment_model:
                    type: integer
                    description: '[zh] 付费类型. [en] payment type.'
                    format: enum
                billing_model:
                    type: integer
                    description: '[zh] 计费类型. [en] billing type.'
                    format: enum
                original_id:
                    type: string
                    description: '[zh] 合同包ID. [en] Contract package ID. original id-- OT_ORIGINAL: original spu_id/package_id; OT_RENEW/OT_UPGRADED/OT_DOWNGRADED: original/ order_id; OT_CONTRACT: original contract_id'
                end_time:
                    type: string
                    description: '[zh] 订单结束时间. [en] Order end time.'
                    format: date-time
            description: '[zh] 订单信息. [en] Order Infomation.'
        ReleaseSLBRequest:
            required:
                - subscription_name
                - resource_group_name
                - zone
                - slb_name
            type: object
            properties:
                subscription_name:
                    type: string
                    description: "[zh] 订阅名.\r [en] Subscription."
                resource_group_name:
                    type: string
                    description: "[zh] 资源组.\r [en] Resource group."
                zone:
                    type: string
                    description: "[zh] 可用区.\r [en] Available zone."
                slb_name:
                    type: string
                    description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
            description: "[zh] 释放负载均衡资源.\r [en] Release SLB resource."
        Resources:
            type: object
            properties:
                capacity_limitations:
                    $ref: '#/components/schemas/CapacityLimitations'
            description: "[zh] 资源规格属性\r [en] Resource Specification Properties."
        SLB:
            required:
                - display_name
                - sku_id
            type: object
            properties:
                id:
                    readOnly: true
                    type: string
                    description: "[zh] 资源id.\r [en] The SLB resource id using the form:\r     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/slbs/{name}`."
                uid:
                    readOnly: true
                    type: string
                    description: "[zh] 资源的uuid.\r [en] The SLB resource uuid."
                name:
                    type: string
                    description: "[zh] 资源标识.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                display_name:
                    type: string
                    description: "[zh] 资源名称.\r [en] The SLB resource display name"
                description:
                    type: string
                    description: "[zh] 资源描述.\r [en] The SLB resource description."
                resource_type:
                    type: string
                    description: "[zh] 资源类型.\r [en] The SLB resource type."
                creator_id:
                    readOnly: true
                    type: string
                    description: "[zh] 创建者id.\r [en] The id of the user who created the SLB resource."
                owner_id:
                    readOnly: true
                    type: string
                    description: "[zh] 拥有者id.\r [en] The id of the user who owns the SLB resource."
                tenant_id:
                    readOnly: true
                    type: string
                    description: "[zh] 租户id\r [en] Tenant id."
                zone:
                    readOnly: true
                    type: string
                    description: "[zh] 可用区.\r [en] Available zone."
                state:
                    readOnly: true
                    type: integer
                    description: "[zh] 资源状态.\r [en] The current state of the SLB resource."
                    format: enum
                sku_id:
                    type: string
                    description: "[zh] 最小库存单元id.\r [en] SKU id."
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: "[zh] 负载均衡资源的标签.\r [en] Tags attached to the VirtualMachine resource."
                properties:
                    $ref: '#/components/schemas/SLBProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
                deleted:
                    readOnly: true
                    type: boolean
                    description: "[zh] 资源是否已删除.\r [en] Indicates whether the SLB resource is deleted or not."
                create_time:
                    readOnly: true
                    type: string
                    description: "[zh] 资源创建时间.\r [en] The time when the SLB resource was created."
                    format: date-time
                update_time:
                    readOnly: true
                    type: string
                    description: "[zh] 资源更新时间.\r [en] The time when the SLB resource was last updated."
                    format: date-time
            description: "[zh] 负载均衡资源.\r [en] The SLB resource."
        SLBProperties:
            type: object
            properties:
                resources:
                    $ref: '#/components/schemas/Resources'
                type:
                    type: integer
                    description: "[zh] SLB 类型\r [en] SLB loadbalancer type"
                    format: enum
                vpc_id:
                    type: string
                    description: "[zh] 所属 VPC uid\r [en] VPC uid"
                ip_version:
                    type: integer
                    description: "[zh] IP 协议栈版本\r [en] IP protocol version"
                    format: enum
                eip_id:
                    type: string
                    description: "[zh] 所关联的 EIP uid.\r [en] EIP uid."
            description: "[zh] 资源属性.\r [en] The SLBProperties."
        SlbUpdate:
            required:
                - display_name
            type: object
            properties:
                display_name:
                    type: string
                    description: "[zh] 资源名称.\r [en] The SLB resource display name"
                description:
                    type: string
                    description: "[zh] 资源描述.\r [en] The SLB resource description."
                resources:
                    $ref: '#/components/schemas/Resources'
                type:
                    type: integer
                    description: "[zh] SLB 类型\r [en] SLB loadbalancer type"
                    format: enum
                ip_version:
                    type: integer
                    description: "[zh] IP 协议栈版本\r [en] IP protocol version"
                    format: enum
                eip_id:
                    type: string
                    description: "[zh] 所属的 EIP uid.\r [en] EIP uid."
            description: "[zh] 负载均衡资源.\r [en] The SLB resource."
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
tags:
    - name: SLBs
