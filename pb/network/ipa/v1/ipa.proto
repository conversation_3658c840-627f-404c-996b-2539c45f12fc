syntax = "proto3";
package sensetime.core.network.ipa.v1;
option go_package = 'gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/ipa/v1;ipa';

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/common/v1/orderinfo.proto";


// IPA 服务，管理子网内IP地址.
// [EN] Service of IPA, for managing IPA in the subnet.
service IPAs {

  // 列举符合请求的所有 IPAs.
  // [EN] List requested IPAs.
  rpc ListIPAs (ListIPAsRequest) returns (ListIPAsResponse) {
    option (google.api.http) = {
      get: "/network/ipa/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/ipas"
    };
  }

  // 获取符合请求的一个 IPA.
  // [EN] Get a requested IPA.
  rpc GetIPA (IPAUIDRequest) returns (IPA) {
    option (google.api.http) = {
      get: "/network/ipa/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/ipas/{id}"
    };
  }

  // 分配一个 IPA.
  // [EN] Create a IPA.
  rpc CreateIPA (CreateIPARequest) returns (IPA) {
    option (google.api.http) = {
      post: "/network/ipa/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/ipas"
      body: "*"
    };
  }

  // 更新一个 IPA.
  // [EN] Update a IPA.
  rpc UpdateIPA (UpdateIPARequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      patch: "/network/ipa/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/ipas"
      body: "*"
    };
  }


  // 释放一个 IPA.
  // [EN] Delete a IPA.
  rpc DeleteIPA (IPAUIDRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/network/ipa/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/ipas/{id}"
    };
  }

  // 绑定一个 IPA.
  // [EN] Bind a IPA.
  rpc BindIPA (BindingIPARequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/network/ipa/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/ipas/{id}:active"
      body: "*"
    };
  }

  // 解绑一个 IPA.
  // [EN] Unbind a IPA.
  rpc UnbindIPA (BindingIPARequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/network/ipa/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/ipas/{id}:deactive"
      body: "*"
    };
  }

}

// 列举 IPAs 的请求.
// [EN] Request to list IPAs.
message ListIPAsRequest {

  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // List filter.
  string filter = 4;

  // Sort resoults.
  string order_by = 5;

  // The maximum number of items to return.
  int32 page_size = 6;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 7;

}

// IPA UID的请求.
// [EN] IPA UID Request.
message IPAUIDRequest {

  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The IPA uid.
  string id = 4;

}

// CreateIPARequest.
message CreateIPARequest {
  // 订购名称
  // [EN] Subscription
  string subscription_name = 1;

  // 资源组
  // [EN] Resource group
  string resource_group_name = 2;

  // 可用区
  // [EN] Available zone.
  string zone = 6;

  // IPA分配的子网ID
  // [EN] The Subnet uid.
  string subnet_id = 7;

  // IPA协议类型：IPv4，IPv6 或者 Dual
  // [EN] The protocol of the IPA: IPv4, IPv6 or Dual.
  string protocol = 8;

  // 指定IPA分配的IP，如果为空则自动分配
  // [EN] Specifies the IP address assigned by the IPA. If the IP address is empty, the IP address is assigned automatically.
  string ip = 9;
}


// 绑定 IPA 的请求.
// [EN] Binding IPA Request.
message BindingIPARequest {

  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The IPA uid.
  string id = 4;

  // The binding MAC address
  string mac = 5;

  // The binding IPMI address
  string ipmi = 6;

  // The binding mode: disable, enable
  string mode = 7;
}

// 更新 IPA 的请求.
// [EN] Update IPA Request.
message UpdateIPARequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The IPA uid.
  string id = 4;

  // Original binding MAC address
  string original_mac = 5;

  // New binding MAC address
  string new_mac = 6;

  // The binding IPMI address
  string ipmi = 7;
}

// 列举 IPAs 的响应.
// [EN] Response to list IPAs.
message ListIPAsResponse {

  // IPA 列表.
  // [EN] IPA list.
  repeated IPA ipas = 1;

  // 下一个页面的 token，如果没有更多数据则为空.
  // [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
  string next_page_token = 2;

  // total size
  int32 total_size = 3;

}

// IPA 实例结构体.
// [EN] IPA entity.
message IPA {
  // The IPA resource id using the form:
  //     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/ipas/{ipa}`.
  string id = 1;

  // The IPA resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 2;

  // ContainerInstance resource display name.
  string display_name = 3 [(validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // ContainerInstance resource description.
  string description = 4;

  // The Subnet resource uuid.
  string uid = 5;

  // The Subnet resource type.
  string resource_type = 6;

  // The id of the user who created the Subnet resource.
  string creator_id = 7;

  // The id of the user who owns the Subnet resource.
  string owner_id = 8;

  // Tenant id.
  string tenant_id = 9;

  // Available zone.
  string zone = 10;

  // Represents the different states of a IPA.
  enum State {
    // The IPA resource is being created.
    CREATED = 0;

    // The IPA resource is being binding.
    BINDING = 1;

    // The IPA resource has been actived.
    ACTIVE = 2;

    // The IPA resource is being unbinding.
    UNBINDING = 3;

    // The IPA resource has been deleted.
    DELETED = 4;

    // The IPA resource has been failed.
    FAILED = 5;

    // The IPA resource is being updating.
    UPDATING = 6;

    // The IPA resource is being provision.
    PROVISION = 7;
  };

  // The current state of the IPA resource.
  State state = 11;

  // Sku id.
  string sku_id = 12;

  // Tags attached to the IPA resource.
  map<string, string> tags = 13;

  // Properties of the IPA resource.
  IPAProperties properties = 14;

  // Payment information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 15;

  // Indicates whether the IPA resource is deleted or not.
  bool deleted = 16;

  // The time when the IPA resource was created.
  google.protobuf.Timestamp create_time = 17;

  // The time when the IPA resource was last updated.
  google.protobuf.Timestamp update_time = 18;

}

// 资源实际属性.
// [EN] Real resource properties.
message IPAProperties {
  // IPA分配的子网ID
  // [EN] The Subnet uid.
  string subnet_id = 1;

  // IPA协议类型：IPv4，IPv6 或者 Dual
  // [EN] The protocol of the IPA: IPv4, IPv6 or Dual.
  string protocol = 2;

  // IPA分配的IP地址
  // [EN] The IP address of the IPA.
  string ip = 3;

  // 绑定IPA的MAC地址
  // [EN] The MAC address of the IPA.
  string mac = 4;

  // 网关IP
  // [EN] The gateway IP.
  string gw_ip = 5;

  // 子网CIDR
  // [EN] The cidr.
  string cidr = 6;

  // IPA分配的MTU
  // [EN] The IPA MTU.
  int32 mtu = 7;

  // 绑定IPA的IPMI地址
  // [EN] The IPMI address
  string ipmi = 8;

  // NAT网关IP
  // [EN] The nat gateway IP.
  string nat_gw_ip = 9;
}
