# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: IPAs API
    description: |-
        IPA 服务，管理子网内IP地址.
         [EN] Service of IPA, for managing IPA in the subnet.
    version: 0.0.1
paths:
    /network/ipa/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/ipas:
        get:
            tags:
                - IPAs
            description: |-
                列举符合请求的所有 IPAs.
                 [EN] List requested IPAs.
            operationId: IPAs_ListIPAs
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: filter
                  in: query
                  description: List filter.
                  schema:
                    type: string
                - name: order_by
                  in: query
                  description: Sort resoults.
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: The maximum number of items to return.
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: The next_page_token value returned from a previous List request, if any.
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListIPAsResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - IPAs
            description: |-
                分配一个 IPA.
                 [EN] Create a IPA.
            operationId: IPAs_CreateIPA
            parameters:
                - name: subscription_name
                  in: path
                  description: 订购名称 [EN] Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: 资源组 [EN] Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: 可用区 [EN] Available zone.
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/CreateIPARequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/IPA'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - IPAs
            description: |-
                更新一个 IPA.
                 [EN] Update a IPA.
            operationId: IPAs_UpdateIPA
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/UpdateIPARequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/ipa/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/ipas/{id}:
        get:
            tags:
                - IPAs
            description: |-
                获取符合请求的一个 IPA.
                 [EN] Get a requested IPA.
            operationId: IPAs_GetIPA
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: id
                  in: path
                  description: The IPA uid.
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/IPA'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - IPAs
            description: |-
                释放一个 IPA.
                 [EN] Delete a IPA.
            operationId: IPAs_DeleteIPA
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: id
                  in: path
                  description: The IPA uid.
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/ipa/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/ipas/{id}:active:
        post:
            tags:
                - IPAs
            description: |-
                绑定一个 IPA.
                 [EN] Bind a IPA.
            operationId: IPAs_BindIPA
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: id
                  in: path
                  description: The IPA uid.
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/BindingIPARequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/ipa/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/ipas/{id}:deactive:
        post:
            tags:
                - IPAs
            description: |-
                解绑一个 IPA.
                 [EN] Unbind a IPA.
            operationId: IPAs_UnbindIPA
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: id
                  in: path
                  description: The IPA uid.
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/BindingIPARequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        BindingIPARequest:
            type: object
            properties:
                subscription_name:
                    type: string
                    description: Subscription
                resource_group_name:
                    type: string
                    description: Resource group
                zone:
                    type: string
                    description: Available zone
                id:
                    type: string
                    description: The IPA uid.
                mac:
                    type: string
                    description: The binding MAC address
                ipmi:
                    type: string
                    description: The binding IPMI address
                mode:
                    type: string
                    description: 'The binding mode: disable, enable'
            description: 绑定 IPA 的请求. [EN] Binding IPA Request.
        CreateIPARequest:
            type: object
            properties:
                subscription_name:
                    type: string
                    description: 订购名称 [EN] Subscription
                resource_group_name:
                    type: string
                    description: 资源组 [EN] Resource group
                zone:
                    type: string
                    description: 可用区 [EN] Available zone.
                subnet_id:
                    type: string
                    description: IPA分配的子网ID [EN] The Subnet uid.
                protocol:
                    type: string
                    description: 'IPA协议类型：IPv4，IPv6 或者 Dual [EN] The protocol of the IPA: IPv4, IPv6 or Dual.'
                ip:
                    type: string
                    description: 指定IPA分配的IP，如果为空则自动分配 [EN] Specifies the IP address assigned by the IPA. If the IP address is empty, the IP address is assigned automatically.
            description: CreateIPARequest.
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        IPA:
            type: object
            properties:
                id:
                    type: string
                    description: 'The IPA resource id using the form:     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/ipas/{ipa}`.'
                name:
                    type: string
                    description: 'The IPA resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                display_name:
                    type: string
                    description: ContainerInstance resource display name.
                description:
                    type: string
                    description: ContainerInstance resource description.
                uid:
                    type: string
                    description: The Subnet resource uuid.
                resource_type:
                    type: string
                    description: The Subnet resource type.
                creator_id:
                    type: string
                    description: The id of the user who created the Subnet resource.
                owner_id:
                    type: string
                    description: The id of the user who owns the Subnet resource.
                tenant_id:
                    type: string
                    description: Tenant id.
                zone:
                    type: string
                    description: Available zone.
                state:
                    type: integer
                    description: The current state of the IPA resource.
                    format: enum
                sku_id:
                    type: string
                    description: Sku id.
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: Tags attached to the IPA resource.
                properties:
                    $ref: '#/components/schemas/IPAProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
                deleted:
                    type: boolean
                    description: Indicates whether the IPA resource is deleted or not.
                create_time:
                    type: string
                    description: The time when the IPA resource was created.
                    format: date-time
                update_time:
                    type: string
                    description: The time when the IPA resource was last updated.
                    format: date-time
            description: IPA 实例结构体. [EN] IPA entity.
        IPAProperties:
            type: object
            properties:
                subnet_id:
                    type: string
                    description: IPA分配的子网ID [EN] The Subnet uid.
                protocol:
                    type: string
                    description: 'IPA协议类型：IPv4，IPv6 或者 Dual [EN] The protocol of the IPA: IPv4, IPv6 or Dual.'
                ip:
                    type: string
                    description: IPA分配的IP地址 [EN] The IP address of the IPA.
                mac:
                    type: string
                    description: 绑定IPA的MAC地址 [EN] The MAC address of the IPA.
                gw_ip:
                    type: string
                    description: 网关IP [EN] The gateway IP.
                cidr:
                    type: string
                    description: 子网CIDR [EN] The cidr.
                mtu:
                    type: integer
                    description: IPA分配的MTU [EN] The IPA MTU.
                    format: int32
                ipmi:
                    type: string
                    description: 绑定IPA的IPMI地址 [EN] The IPMI address
                nat_gw_ip:
                    type: string
                    description: NAT网关IP [EN] The nat gateway IP.
            description: 资源实际属性. [EN] Real resource properties.
        ListIPAsResponse:
            type: object
            properties:
                ipas:
                    type: array
                    items:
                        $ref: '#/components/schemas/IPA'
                    description: IPA 列表. [EN] IPA list.
                next_page_token:
                    type: string
                    description: 下一个页面的 token，如果没有更多数据则为空. [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
                total_size:
                    type: integer
                    description: total size
                    format: int32
            description: 列举 IPAs 的响应. [EN] Response to list IPAs.
        OrderInfo:
            type: object
            properties:
                billing_cycle_number:
                    type: integer
                    description: '[zh] 购买时长. [en] length of purchase.'
                    format: int32
                auto_renew:
                    type: boolean
                    description: '[zh] 自动续费. [en] Automatic renewal.'
                currency_code:
                    type: string
                    description: '[zh] 货币代码. [en] currency code.'
                payment_channel:
                    type: integer
                    description: '[zh] 支付方式. [en] payment method.'
                    format: enum
                note:
                    type: string
                    description: '[zh] 订单备注. [en] order notes'
                order_type:
                    type: integer
                    description: '[zh] 订单类型. [en] Order Type.'
                    format: enum
                order_id:
                    type: string
                    description: '[zh] 订单id. [en] order id.'
                start_time:
                    type: string
                    description: '[zh] 订单生效日期. [en] Order effective date.'
                    format: date-time
                payment_model:
                    type: integer
                    description: '[zh] 付费类型. [en] payment type.'
                    format: enum
                billing_model:
                    type: integer
                    description: '[zh] 计费类型. [en] billing type.'
                    format: enum
                original_id:
                    type: string
                    description: '[zh] 合同包ID. [en] Contract package ID. original id-- OT_ORIGINAL: original spu_id/package_id; OT_RENEW/OT_UPGRADED/OT_DOWNGRADED: original/ order_id; OT_CONTRACT: original contract_id'
                end_time:
                    type: string
                    description: '[zh] 订单结束时间. [en] Order end time.'
                    format: date-time
            description: '[zh] 订单信息. [en] Order Infomation.'
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        UpdateIPARequest:
            type: object
            properties:
                subscription_name:
                    type: string
                    description: Subscription
                resource_group_name:
                    type: string
                    description: Resource group
                zone:
                    type: string
                    description: Available zone
                id:
                    type: string
                    description: The IPA uid.
                original_mac:
                    type: string
                    description: Original binding MAC address
                new_mac:
                    type: string
                    description: New binding MAC address
                ipmi:
                    type: string
                    description: The binding IPMI address
            description: 更新 IPA 的请求. [EN] Update IPA Request.
tags:
    - name: IPAs
