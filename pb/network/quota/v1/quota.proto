syntax = "proto3";
package sensetime.core.network.quota.data.v1;
option go_package = "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/quota/v1;quota";

import "google/api/annotations.proto";

// [zh] Quota 服务
// [en] Service of Quota
service QuotaService {
  // [zh] 列举产品的所有 Quotas.
  // [en] List requested Quotas.
  rpc ListQuotas(ListQuotasRequest) returns (ListQuotasResponse) {
    option (google.api.http) = {
      get: "/network/quota/data/v1/quotas"
    };
  }
}

// [zh] 列举 Quotas 的请求.
// [en] Request to list Quotas.
message ListQuotasRequest {
  // [zh] 过滤条件
  // [en] List filter.
  string filter = 1;

  // [zh] 排序规则
  // [en] Sort rules.
  string order_by = 2;

  // [zh] 分页大小
  // [en] The maximum number of items to return.
  int32 page_size = 3;

  // [zh] 从上一个List请求返回的next_page_token值(如果有的话)
  // [en] The next_page_token value returned from a previous List request, if
  // any.
  string page_token = 4;
}

// [zh] 列举 Quotas 的响应.
// [en] Response to list Quotas.
message ListQuotasResponse {
  message QuotaValue {
    map<string, int64> values = 1;
  }

  // [zh] Quotas 列表.
  // [en] Quotas list.
  map<string, QuotaValue> Quotas = 1;

  // [zh] 下一个页面的 token，如果没有更多数据则为空.
  // [en] Token to retrieve the next page of results, or empty if there are no
  // more results in the list.
  string next_page_token = 2;

  // [zh] Quotas 总数
  // [en] total size
  int32 total_size = 3;
}
