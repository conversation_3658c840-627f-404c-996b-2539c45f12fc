syntax = "proto3";
package sensetime.core.network.dc.v1;
option go_package = "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/dc/v1;dc";

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/api/annotations.proto";
import "higgs/common/v1/orderinfo.proto";
import "google/api/field_behavior.proto";

// DCVC 服务，管理 AZ 内租户 DCVC 相关网络资源.
// [EN] Service of DCVC, for managing DCVC related network resources for tenants in AZ.
service DCVCs {
  // 创建一个 DCVC.
  // [EN] Create a DCVC.a
  rpc CreateDCVC(CreateDCVCRequest) returns (DCVC) {
    option (google.api.http) = {
      post: "/network/dc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcvcs/{dcvc_name}"
      body: "dcvc"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}"
      permission: "dc.dcpl.create"
    };
  }

  // 删除一个 DCVC.
  // [EN] Delete a DCVC.
  rpc DeleteDCVC(DeleteDCVCRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/network/dc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcvcs/{dcvc_name}"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcvcs/{dcvc_name}"
      permission: "dc.dcpl.update"
    };
  }

  // 更新 DCVC 可编辑字段.
  // [EN] Update DCVC editable properties.
  rpc UpdateDCVC(UpdateDCVCRequest) returns (DCVC) {
    option (google.api.http) = {
      patch: "/network/dc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcvcs/{dcvc_name}"
      body: "dcvc"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcvcs/{dcvc_name}"
      permission: "dc.dcpl.update"
    };
  }

  // 获取符合请求的一个 DCVC.
  // [EN] Get a requested DCVC.
  rpc GetDCVC(GetDCVCRequest) returns (DCVC) {
    option (google.api.http) = {
      get: "/network/dc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcvcs/{dcvc_name}"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}"
      permission: "dc.dcpl.get"
    };
  }

  // 列举符合请求的所有 DCVCs.
  // [EN] List requested DCVCs.
  rpc ListDCVCs(ListDCVCsRequest) returns (ListDCVCsResponse) {
    option (google.api.http) = {
      get: "/network/dc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcvcs"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}"
      permission: "dc.dcpl.list"
    };
  }
}

// DCVC 创建字段.
// [EN] DCVC creation properties.
message CreateDCVCRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Zone, todo: add validation
  string zone = 3;

  // The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string dcvc_name = 4 [(validate.rules).string = { pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63 }];

  // The DCVC resource to create.
  DCVC dcvc = 5;
}

// 删除某一个 DCVC 的请求.
// [EN] Request to delete a DCVC.
message DeleteDCVCRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Zone
  string zone = 3;

  // The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string dcvc_name = 4;
}

// DCVC 可编辑字段.
// [EN] DCVC editable properties.
message UpdateDCVCRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Zone
  string zone = 3;

  // The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string dcvc_name = 4;

  // The DCVC resource to update.
  DCVCUpdateProperties dcvc = 5;

  // update_mask
  google.protobuf.FieldMask update_mask = 6;
}

// [zh] 更新DCVC资源.
// [en] Update DCVC properties.
message DCVCUpdateProperties {
  // resource display name
  optional string display_name = 1 [(validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // ContainerInstance resource description
  string description = 2;

  // Properties of the DCVC resource.
  DCVCProperties properties = 3;
}

// 获取某一个 DCVC 的请求.
// [EN] Request to get a DCVC.
message GetDCVCRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Zone
  string zone = 3;

  // The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string dcvc_name = 4;
}

// 列举 DCVCs 的请求.
// [EN] Request to list DCVCs.
message ListDCVCsRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Zone
  string zone = 3;

  // List filter.
  string filter = 4;

  // Sort resoults.
  string order_by = 5;

  // The maximum number of items to return.
  int32 page_size = 6;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 7;

  // tenant_id.
  string tenant_id = 8;
}

// 列举 DCVCs 的响应.
// [EN] Response to list DCVCs.
message ListDCVCsResponse {
  // DCVC 列表.
  // [EN] DCVC list.
  repeated DCVC dcvcs = 1;

  // 下一个页面的 token，如果没有更多数据则为空.
  // [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
  string next_page_token = 2;

  // total size
  int32 total_size = 3;
}

// DCVC 实例结构体.
// [EN] DCVC entity.
message DCVC {
  // The DCVC resource id
  string id = 1;

  // The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 2;

  // resource display name
  string display_name = 3 [(validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // ContainerInstance resource description
  string description = 4;

  // The DCVC resource uuid.
  string uid = 5;

  // The DCVC resource type.
  string resource_type = 6;

  // The id of the user who created the DCVC resource.
  string creator_id = 7;

  // The id of the user who owns the DCVC resource.
  string owner_id = 8;

  // Tenant id.
  string tenant_id = 9;

  // Zone.
  string zone = 10;

  // Represents the different states of a DCVC.
  enum State {
    // The DCVC resource is being created.
    CREATING = 0;

    // The DCVC resource is being updated.
    UPDATING = 1;

    // The DCVC resource has been active.
    ACTIVE = 2;

    // The DCVC resource is being deleting.
    DELETING = 3;

    // The DCVC resource has been deleted.
    DELETED = 4;

    // The DCVC resource has been failed.
    FAILED = 5;
  };

  // The current state of the DCVC resource.
  State state = 11;

  // Sku id.
  string sku_id = 12;

  // Tags attached to the DCVC resource.
  map<string, string> tags = 13;

  // Properties of the DCVC resource.
  DCVCProperties properties = 14;

  // Payment information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 15;

  // Indicates whether the DCVC resource is deleted or not.
  bool deleted = 16;

  // The time when the DCVC resource was created.
  google.protobuf.Timestamp create_time = 17;

  // The time when the DCVC resource was last updated.
  google.protobuf.Timestamp update_time = 18;
}

// 资源实际属性.
// [EN] Real resource properties.
message DCVCProperties {

  // [zh] 路由类型.
  // [en] route type.
  string  route_type = 1;

  // LimitRateItems
  LimitRateItems limit_rate_items = 2;

  // [zh] 物理线路 id.
  // [en] dcpl id.
  string  dcpl_id = 3;

  // [zh] 专线网关 name.
  // [en] dcgw name.
  string  dcgw_name = 4;

  // [zh] 虚拟通道 vlan id.
  // [en] vlan id
  string  vlan_id = 5;

  // [zh]  客户IDC侧互联ip.
  // [en]  connect ip of customer.
  string customer_connect_ip = 6;

  // [zh]  大装置侧互联ip.
  // [en]  connect ip of sensecore.
  string sensecore_connect_ip = 7;

  // [zh] 专线网关的 vxlan name.
  // [en] vxlan name of dcgw
  string vxlan_name = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 专线网关的 vxlan id.
  // [en] vxlan id of dcgw
  string vxlan_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 专线网关本端的 vtep ip.
  // [en] local vtep ip of dcgw.
  string local_vtep_ip = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 专线网关远端的 vtep ip.
  // [en] remote vtep ip of dcgw.
  string  remote_vtep_ip = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 专线网关本端的 vxlan ip.
  // [en] local vxlan ip of dcgw
  string local_vxlan_ip = 12 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 专线网关远端的 vxlan ip.
  // [en] local dc asw vxlan ip.
  string remote_vxlan_ip = 13 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh]  专线网关本端的 CIDR.
  // [en] local cidr of dcgw.
  repeated string local_cidr = 14;

  // [zh] 专线网关远端的 CIDR.
  // [en] remote cidr of dcgw.
  repeated string remote_cidr = 15;

  // [zh] 专线网关的 subscription name.
  // [en] subscription name of dcgw
  string subscription_name = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 专线网关的 resource group name.
  // [en] resource group name of dcgw
  string resource_group_name = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // HealtCheck.
  HealtCheck health_check = 18;
}

// 限速项
message LimitRateItems {
  // DCGW 上行带宽限制，单位: M/s.
  int32 up_stream_bw = 1;
  // DCGW 下行带宽限制，单位: M/s
  int32 down_stream_bw = 2;
}

// HealtCheck
message HealtCheck {
  // [zh] 开启/关闭健康检查标志。默认为：关闭。
  // [en] enable, default false
  bool enable = 1;

  // [zh] 健康检查的目标IP地址。默认值为空字符串，空字符串表示使用此虚拟通道的互联IP（IDC侧），即：CustomerIDCConnectIP。
  // [en] target ip, default customer_connect_ip
  string target_ip = 2;

  // [zh] 健康检查的间隔时间，单位：秒。默认值为2秒。支持[2,5]内整数.
  // [en] health check interval, default 2s, effective value 2 ~ 5
  optional int32 interval = 3 [(validate.rules).int32 = {gte:2, lte: 5}];

  // [zh] 健康检查成功/失败判定次数。单位：次。默认值为5次。有效值为3 ~ 8次。
  // [en] health check counter, default 5, effective value 3 ~ 8
  optional int32 counter = 4 [(validate.rules).int32 = {gte:3, lte: 8}];

  // [zh] 健康检查的结果，"SUCCESS" --成功，"FAIL"--失败，"CHECKING" --- 检测中 （虚拟通道尚未完成完整的1轮检测，无法给出成功/失败结果。默认情况下2x5=10秒）
  // [en] run status
  string run_status = 5;

  //[zh]  健康检查结果最后变化时间。表示最近从“成功”到“失败”，或者“失败”到“成功”，或者“检测中”到“成功”/“失败”的切换时刻。从这个时间至今，都是没有状态变化。
  // [en] run time
  google.protobuf.Timestamp  run_time = 6;
}