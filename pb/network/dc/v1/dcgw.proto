syntax = "proto3";
package sensetime.core.network.dc.v1;
option go_package = "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/dc/v1;dc";

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/api/annotations.proto";
import "higgs/common/v1/orderinfo.proto";
import "google/api/field_behavior.proto";

// DCGW 服务，管理 AZ 内租户 DCGW 相关网络资源.
// [EN] Service of DCGW, for managing DCGW related network resources for tenants in AZ.
service DCGWs {
  // 创建一个 DCGW.
  // [EN] Create a DCGW.a
  rpc CreateDCGW(CreateDCGWRequest) returns (DCGW) {
    option (google.api.http) = {
      post: "/network/dc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcgws/{dcgw_name}"
      body: "dcgw"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}"
      permission: "vpc.vpc.update"
    };
  }

  // 删除一个 DCGW.
  // [EN] Delete a DCGW.
  rpc DeleteDCGW(DeleteDCGWRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/network/dc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcgws/{dcgw_name}"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcgws/{dcgw_name}"
      permission: "vpc.vpc.update"
    };
  }

  // 更新 DCGW 可编辑字段.
  // [EN] Update DCGW editable properties.
  rpc UpdateDCGW(UpdateDCGWRequest) returns (DCGW) {
    option (google.api.http) = {
      patch: "/network/dc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcgws/{dcgw_name}"
      body: "dcgw"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcgws/{dcgw_name}"
      permission: "vpc.vpc.update"
    };
  }

  // 获取符合请求的一个 DCGW.
  // [EN] Get a requested DCGW.
  rpc GetDCGW(GetDCGWRequest) returns (DCGW) {
    option (google.api.http) = {
      get: "/network/dc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcgws/{dcgw_name}"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}"
      permission: "vpc.vpc.get"
    };
  }

  // 列举符合请求的所有 DCGWs.
  // [EN] List requested DCGWs.
  rpc ListDCGWs(ListDCGWsRequest) returns (ListDCGWsResponse) {
    option (google.api.http) = {
      get: "/network/dc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcgws"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}"
      permission: "vpc.vpc.list"
    };
  }
}

// DCGW 创建字段.
// [EN] DCGW creation properties.
message CreateDCGWRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Zone, todo: add validation
  string zone = 3;

  // The DCGW resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string dcgw_name = 4 [(validate.rules).string = { pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63 }];

  // The DCGW resource to create.
  DCGW dcgw = 5;
}

// 删除某一个 DCGW 的请求.
// [EN] Request to delete a DCGW.
message DeleteDCGWRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Zone
  string zone = 3;

  // The DCGW resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string dcgw_name = 4;
}

// DCGW 可编辑字段.
// [EN] DCGW editable properties.
message UpdateDCGWRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Zone
  string zone = 3;

  // The DCGW resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string dcgw_name = 4;

  // The DCGW resource to update.
  DCGWUpdateProperties dcgw = 5;

  // update_mask
  google.protobuf.FieldMask update_mask = 6;
}

// [zh] 更新DCGW资源.
// [en] Update DCGW properties.
message DCGWUpdateProperties {
  // resource display name
  optional string display_name = 1 [(validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // ContainerInstance resource description
  string description = 2;

  // Properties of the DCGW resource.
  DCGWProperties properties = 3;
}

// 获取某一个 DCGW 的请求.
// [EN] Request to get a DCGW.
message GetDCGWRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Zone
  string zone = 3;

  // The DCGW resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string dcgw_name = 4;
}

// 列举 DCGWs 的请求.
// [EN] Request to list DCGWs.
message ListDCGWsRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Zone
  string zone = 3;

  // List filter.
  string filter = 4;

  // Sort resoults.
  string order_by = 5;

  // The maximum number of items to return.
  int32 page_size = 6;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 7;

  // tenant_id.
  string tenant_id = 8;
}

// 列举 DCGWs 的响应.
// [EN] Response to list DCGWs.
message ListDCGWsResponse {
  // DCGW 列表.
  // [EN] DCGW list.
  repeated DCGW dcgws = 1;

  // 下一个页面的 token，如果没有更多数据则为空.
  // [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
  string next_page_token = 2;

  // total size
  int32 total_size = 3;
}

// DCGW 实例结构体.
// [EN] DCGW entity.
message DCGW {
  // The DCGW resource id
  string id = 1;

  // The DCGW resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 2;

  // resource display name
  string display_name = 3 [(validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // ContainerInstance resource description
  string description = 4;

  // The DCGW resource uuid.
  string uid = 5;

  // The DCGW resource type.
  string resource_type = 6;

  // The id of the user who created the DCGW resource.
  string creator_id = 7;

  // The id of the user who owns the DCGW resource.
  string owner_id = 8;

  // Tenant id.
  string tenant_id = 9;

  // Zone.
  string zone = 10;

  // Represents the different states of a DCGW.
  enum State {
    // The DCGW resource is being created.
    CREATING = 0;

    // The DCGW resource is being updated.
    UPDATING = 1;

    // The DCGW resource has been active.
    ACTIVE = 2;

    // The DCGW resource is being deleting.
    DELETING = 3;

    // The DCGW resource has been deleted.
    DELETED = 4;

    // The DCGW resource has been failed.
    FAILED = 5;
  };

  // The current state of the DCGW resource.
  State state = 11;

  // Sku id.
  string sku_id = 12;

  // Tags attached to the DCGW resource.
  map<string, string> tags = 13;

  // Properties of the DCGW resource.
  DCGWProperties properties = 14;

  // Payment information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 15;

  // Indicates whether the DCGW resource is deleted or not.
  bool deleted = 16;

  // The time when the DCGW resource was created.
  google.protobuf.Timestamp create_time = 17;

  // The time when the DCGW resource was last updated.
  google.protobuf.Timestamp update_time = 18;
}

// 资源实际属性.
// [EN] Real resource properties.
message DCGWProperties {

  // VPC uid.
  // [EN] VPC uid.
  string vpc_id = 1;

  // [zh] 专线网关高可用模式.
  // [en] ha mode of dcgw
  string ha_mode = 2;

  // [zh] 专线网关部署模式.
  // [en] deploy mode of dcgw.
  string deploy_mode = 3;

  // [zh] 专线网关内部的 vip.
  // [en] internal vip of dcgw.
  string internal_vip = 4;

  // [zh] monitor 监听地址
  // [en] monitor ip of dcgw.
  string monitor_ip = 5;

  // [zh] heartbeat 监听在哪个网卡
  // [en] heartbeat nic ip of dcgw.
  string  heartbeat_nic = 6;

  // [zh] 专线网关外部的 vip.
  // [en] external vip of dcgw
  string external_vip = 7;


  // [zh]  专线网关本端的 CIDR.
  // [en] local cidr of dcgw.
  repeated string local_cidr = 8;

  // [zh] 专线网关远端的 CIDR.
  // [en] remote cidr of dcgw.
  repeated string remote_cidr = 9;

  // [zh] 专线网关关联的虚拟通道.
  // [en] dcvcs of dcgw.
  repeated DCVCElement dcvcs = 13;

  // [zh] 专线网关的 subscription name.
  // [en] subscription name of dcgw
  string subscription_name = 14 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 专线网关的 resource group name.
  // [en] resource group name of dcgw
  string resource_group_name = 15 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// 虚拟通道.
// [EN] DCVC Element.
message DCVCElement {
  // The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 1;

  // resource display name
  string display_name = 2 [(validate.rules).string = {ignore_empty: true, pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];
}