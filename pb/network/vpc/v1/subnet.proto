syntax = "proto3";
package sensetime.core.network.vpc.v1;
option go_package = 'gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1;vpc';

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/api/annotations.proto";
import "higgs/common/v1/orderinfo.proto";


// Subnet 服务，管理 AZ 内租户 Subnet 相关网络资源.
// [EN] Service of Subnet, for managing Subnet related network resources for tenants in AZ.
service Subnets {

  // 列举符合请求的所有 Subnets.
  // [EN] List requested Subnets.
  rpc ListSubnets (ListSubnetsRequest) returns (ListSubnetsResponse) {
    option (google.api.http) = {
      get: "/network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/subnets"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
  }

  // 获取符合请求的一个 Subnet.
  // [EN] Get a requested Subnet.
  rpc GetSubnet (GetSubnetRequest) returns (Subnet) {
    option (google.api.http) = {
      get: "/network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/subnets/{subnet_name}"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
  }

  // 创建一个 Subnet.
  // [EN] Create a Subnet.
  rpc CreateSubnet (CreateSubnetRequest) returns (Subnet) {
    option (google.api.http) = {
      post: "/network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/subnets/{subnet_name}"
      body: "subnet"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
  }

  // 更新 Subnet 可编辑字段.
  // [EN] Update Subnet editable properties.
  rpc UpdateSubnet (UpdateSubnetRequest) returns (Subnet) {
    option (google.api.http) = {
      patch: "/network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/subnets/{subnet_name}"
      body: "subnet"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
  }

  // 删除一个 Subnet.
  // [EN] Delete a Subnet.
  rpc DeleteSubnet (DeleteSubnetRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/subnets/{subnet_name}"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
  }
}

// 列举 Subnets 的请求.
// [EN] Request to list Subnets.
message ListSubnetsRequest {

  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // List filter.
  string filter = 4;

  // Sort resoults.
  string order_by = 5;

  // The maximum number of items to return.
  int32 page_size = 6;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 7;

}

// 获取某一个 Subnet 的请求.
// [EN] Request to get a Subnet.
message GetSubnetRequest {

  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The Subnet resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string subnet_name = 4;

}

// CreateSubnetRequest.
message CreateSubnetRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone, todo: add validation
  string zone = 3;

  // The Subnet resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string subnet_name = 4 [(validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // The Subnet resource to create.
  Subnet subnet = 5;
}


// Subnet 可编辑字段.
// [EN] Subnet editable properties.
message UpdateSubnetRequest {

  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The Subnet resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string subnet_name = 4;

  // The Subnet resource to update.
  Subnet subnet = 5;
}

// 删除某一个 Subnet 的请求.
// [EN] Request to delete a Subnet.
message DeleteSubnetRequest {

  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The Subnet resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string subnet_name = 4;
}

// 列举 Subnets 的响应.
// [EN] Response to list Subnets.
message ListSubnetsResponse {

  // Subnet 列表.
  // [EN] Subnet list.
  repeated Subnet Subnets = 1;

  // 下一个页面的 token，如果没有更多数据则为空.
  // [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
  string next_page_token = 2;

  // total size
  int32 total_size = 3;

}

// Subnet 实例结构体.
// [EN] Subnet entity.
message Subnet {
  // The Subnet resource id using the form:
  //     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/subnets/{subnet_name}`.
  string id = 1;

  // The Subnet resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 2;

  // ContainerInstance resource display name.
  string display_name = 3 [(validate.rules).string = {ignore_empty: true, pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // ContainerInstance resource description.
  string description = 4;

  // The Subnet resource uuid.
  string uid = 5;

  // The Subnet resource type.
  string resource_type = 6;

  // The id of the user who created the Subnet resource.
  string creator_id = 7;

  // The id of the user who owns the Subnet resource.
  string owner_id = 8;

  // Tenant id.
  string tenant_id = 9;

  // Available zone.
  string zone = 10;

  // Represents the different states of a Subnet.
  enum State {
    // The Subnet resource is being created.
    CREATING = 0;

    // The Subnet resource is being updated.
    UPDATING = 1;

    // The Subnet resource has been active.
    ACTIVE = 2;

    // The Subnet resource is being deleting.
    DELETING = 3;

    // The Subnet resource has been deleted.
    DELETED = 4;

    // The Subnet resource has been failed.
    FAILED = 5;
  };

  // The current state of the Subnet resource.
  State state = 11;

  // Sku id.
  string sku_id = 12;

  // Tags attached to the Subnet resource.
  map<string, string> tags = 13;

  // Properties of the Subnet resource.
  SubnetProperties properties = 14;

  // Payment information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 15;

  // Indicates whether the Subnet resource is deleted or not.
  bool deleted = 16;

  // The time when the Subnet resource was created.
  google.protobuf.Timestamp create_time = 17;

  // The time when the Subnet resource was last updated.
  google.protobuf.Timestamp update_time = 18;

}

// 资源实际属性.
// [EN] Real resource properties.
message SubnetProperties {

  // Subnet network scope.
  enum Scope {
    // Service network.
    SERVICE = 0;
    // Training network.
    TRAINING = 1;
    // Data network.
    DATA = 2;
    // VPC Gateway network
    VPCGW = 3;
    // Manage network.
    MANAGE = 4;
    // Storage network.
    STORAGE = 5;
  };

  // Subnet provider.
  enum Provider {
    // Ovn support subnet.
    OVN = 0;
    // Boson controller support subnet.
    CONTROLLER = 1;
  };

  // Subnet network type.
  enum NetworkType {
    // Geneve network.
    GENEVE = 0;
    // Vxlan network.
    VXLAN = 1;
    // Flat network.
    FLAT = 2;
    // Vlan network.
    VLAN = 3;
    // IB network.
    IB = 4;
  };

  // 当前子网范围
  // [EN] Subnet scope.
  Scope scope = 1;

  // 当前子网提供者
  // [EN] Subnet provider.
  Provider provider = 2;

  // 当前子网的网络类型
  // [EN] Subnet network type.
  NetworkType network_type = 3;

  // 关联的 VPC uid
  // [EN] Subnet VPC uid.
  string vpc_id = 4;

  // 当前子网的CIDR
  // [EN] Subnet CIDR.
  string cidr = 5;

  // 当前子网预留不能使用的IP地址，x.x.x.1..x.x.x.9, x.x.x.250..x.x.x.255 地址默认不做分配
  // [EN] Subnet reserved IPs.
  repeated string reserved_ips = 6;

  // 是否默认子网
  // [EN] Is default subnet.
  bool is_default = 7;

  // 关联的网关IP地址
  // [EN] Subnet gateway IP.
  string gateway_ip = 8;

  // 关联的CIDR资源池
  // [EN] CIDR pool related to the subnet.
  string cidr_pool_id = 9;
}
