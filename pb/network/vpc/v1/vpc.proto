syntax = "proto3";
package sensetime.core.network.vpc.v1;
option go_package = "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1;vpc";

import "google/api/annotations.proto";
import "google/protobuf/empty.proto";
import "google/protobuf/field_mask.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/api/annotations.proto";
import "higgs/common/v1/orderinfo.proto";

// VPC 服务，管理 AZ 内租户 VPC 相关网络资源.
// [EN] Service of VPC, for managing VPC related network resources for tenants in AZ.
service VPCs {
  // 列举符合请求的所有 VPCs.
  // [EN] List requested VPCs.
  rpc ListVPCs(ListVPCsRequest) returns (ListVPCsResponse) {
    option (google.api.http) = {
      get: "/network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}"
      permission: "vpc.vpc.list"
    };
  }

  // 获取符合请求的一个 VPC.
  // [EN] Get a requested VPC.
  rpc GetVPC(GetVPCRequest) returns (VPC) {
    option (google.api.http) = {
      get: "/network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}"
      permission: "vpc.vpc.get"
    };
  }

  // 获取符合请求的一个 VPC 相关属性.
  // [EN] Get a requested VPC status.
  rpc GetVPCStatus(GetVPCStatusRequest) returns (VPCStatus) {
    option (google.api.http) = {
      get: "/network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}/status"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}"
      permission: "vpc.vpc.get"
    };
  }

  // 获取符合请求的一个 VPC 相关属性.
  // [EN] Get a requested VPC status.
  rpc GetVPCDataStatus(GetVPCStatusRequest) returns (VPCStatus) {
    option (google.api.http) = {
      get: "/network/vpc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}/status"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}"
      permission: "vpc.vpc.get"
    };
  }

  // 创建一个 VPC.
  // [EN] Create a VPC.
  rpc CreateVPC(CreateVPCRequest) returns (VPC) {
    option (google.api.http) = {
      post: "/network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}"
      body: "vpc"
    };
    option (sensetime.core.higgs.api.is_asynchronous)  = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}"
      permission: "vpc.vpc.create"
    };
  }

  // 更新 VPC 可编辑字段.
  // [EN] Update VPC editable properties.
  rpc UpdateVPC(UpdateVPCRequest) returns (VPC) {
    option (google.api.http) = {
      patch: "/network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}"
      body: "vpc"
    };
    option (sensetime.core.higgs.api.is_asynchronous)  = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}"
      permission: "vpc.vpc.update"
    };
  }

  // 更新 VPCDGW 可编辑字段.
  // [EN] Update VPCDGW editable properties.
  rpc UpdateVPCDgw(UpdateVPCDgwRequest) returns (DGWGateway) {
    option (google.api.http) = {
      patch: "/network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}/dgws/{dgw_name}"
      body: "dgw"
    };
    option (sensetime.core.higgs.api.is_asynchronous)  = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}"
      permission: "vpc.vpc.update"
    };
  }

  // 删除一个 VPC.
  // [EN] Delete a VPC.
  rpc DeleteVPC(DeleteVPCRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete: "/network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}"
    };
    option (sensetime.core.higgs.api.is_asynchronous)  = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}"
      permission: "vpc.vpc.delete"
    };
  }

  // VPC metrics.
  // [EN] VPC metrics.
  rpc GetVPCMetrics(GetVPCMetricsRequest) returns (VPCMetrics) {
    option (google.api.http) = {
      get: "/network/vpc/data/v1/vpcs/metrics"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy)           = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}"
      permission: "vpc.vpc.list"
    };
  }

  // [zh] 获取VPC内RDMA集群信息.
  // [en] get rdma cluster infor in vpc.
  rpc GetVPCRDMAStatus(GetVPCRDMARequest) returns (VPCRDMAClustersStatus) {
    option (google.api.http) = {
      get: "/network/vpc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}/rdma_clusters"
    };
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}"
      permission: "vpc.vpc.get"
    };
  }
}

// 列举 VPCs 的请求.
// [EN] Request to list VPCs.
message ListVPCsRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // List filter.
  string filter = 4;

  // Sort resoults.
  string order_by = 5;

  // The maximum number of items to return.
  int32 page_size = 6;

  // The next_page_token value returned from a previous List request, if any.
  string page_token = 7;

  // tenant_id.
  string tenant_id = 8;
}

// 获取某一个 VPC 的请求.
// [EN] Request to get a VPC.
message GetVPCRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vpc_name = 4;
}

// 获取符合 VPC 相关属性的请求.
// [EN] Get requested VPC status.
message GetVPCStatusRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vpc_name = 4;
}

// VPC 创建字段.
// [EN] VPC creation properties.
message CreateVPCRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone, todo: add validation
  string zone = 3;

  // The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vpc_name = 4 [(validate.rules).string = { pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63 }];

  // The VPC resource to create.
  VPC vpc = 5;
}

// VPC 可编辑字段.
// [EN] VPC editable properties.
message UpdateVPCRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vpc_name = 4;

  // The VPC resource to update.
  VPCUpdateProperties vpc = 5;

  // update_mask
  google.protobuf.FieldMask update_mask = 6;
}

message VPCUpdateProperties {
  // ContainerInstance resource display name
  optional string display_name = 1 [(validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // ContainerInstance resource description
  string description = 2;

  // the VPC resource subnets' type
  SubnetType subnet_type = 3;
}

// VPC DGW 可编辑字段.
// [EN] VPC DGW editable properties.
message UpdateVPCDgwRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vpc_name = 4;

  // The dgw resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string dgw_name = 5;

  // The DGW resource to update.
  DGWGateway dgw = 6;

  // update_mask
  google.protobuf.FieldMask update_mask = 7;
}

// DGWGateway 实例结构体.
// [EN] DGWGateway entity.
message DGWGateway {
  // The dgw resource id using the form:
  //     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}/dgws/{name}`.
  string id = 1;

  // The dgw resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 2 [(validate.rules).string = { pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63 }];

  // ContainerInstance resource display name
  string display_name = 3 [(validate.rules).string = {ignore_empty: true, pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // ContainerInstance resource description
  string description = 4;

  // The dgw resource uuid.
  string uid = 5;

  // The dgw resource type.
  string resource_type = 6;

  // The id of the user who created the VPC dgw resource.
  string creator_id = 7;

  // The id of the user who owns the VPC dgw resource.
  string owner_id = 8;

  // Tenant id.
  string tenant_id = 9;

  // Available zone.
  string zone = 10;

  // Represents the different states of a VPC dgw.
  enum State {
    // The VPC dgw resource is being created.
    CREATING = 0;

    // The VPC dgw resource is being updated.
    UPDATING = 1;

    // The VPC dgw resource has been active.
    ACTIVE = 2;

    // The VPC dgw resource is being deleting.
    DELETING = 3;

    // The VPC dgw resource has been deleted.
    DELETED = 4;

    // The VPC dgw resource has been failed.
    FAILED = 5;

    // the VPC dgw resource has been inactive
    INACTIVE = 6;

    // the VPC dgw resource has been error
    ERROR = 7;
  };

  // The current state of the VPC dgw resource.
  State state = 11;

  // Represents the different admin states of a dgw.
  enum AdminState {
    // The dgw resource is invalid.
    INVALID = 0;

    // The dgw resource is enable.
    ENABLE = 1;

    // The dgw resource is disable.
    DISABLE = 2;
  };

  // the current admin status
  AdminState admin_state = 12;

  // cidr/prefix模式,200.64.xx.xx
  string gw_external_ip = 13;

  // 以，为连接的目标网段列表
  string gw_policy = 14;

  // vpc overlay ip, 10.119.xxx.xxx
  string internal_ip = 15;

  // Indicates whether the VPC resource is deleted or not.
  bool deleted = 16;

  // The time when the VPC resource was created.
  google.protobuf.Timestamp create_time = 17;

  // The time when the VPC resource was last updated.
  google.protobuf.Timestamp update_time = 18;
}

// 删除某一个 VPC 的请求.
// [EN] Request to delete a VPC.
message DeleteVPCRequest {
  // Subscription
  string subscription_name = 1;

  // Resource group
  string resource_group_name = 2;

  // Available zone
  string zone = 3;

  // The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vpc_name = 4;
}

// 列举 VPCs 的响应.
// [EN] Response to list VPCs.
message ListVPCsResponse {
  // VPC 列表.
  // [EN] VPC list.
  repeated VPC vpcs = 1;

  // 下一个页面的 token，如果没有更多数据则为空.
  // [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
  string next_page_token = 2;

  // total size
  int32 total_size = 3;
}

// 返回 VPC 相关属性的响应.
// [EN] Response to vpc status.
message VPCStatus {
  // vpc cidr.
  string cidr = 1;

  // namespaces info
  repeated NamespaceStatus namespaces = 2;

  // subnets info
  repeated SubnetStatus subnets = 3;

  // natGateway info
  repeated NATGatewayStatus nat_gateways = 4;

  // eip info
  repeated EIPStatus eips = 5;

  // distribute Gateway info
  repeated DGWGatewayStatus dgw_gateways = 6;
}

// VPC 相关 metrics.
// [EN] VPC metrics.
message VPCMetrics {
  // resource_metrics.
  repeated ResourceMetrics resource_metrics = 1;
}

// ResourceMetrics
message ResourceMetrics {
  // name.
  string name = 1;

  // value.
  int32 value = 2;

  // unit.
  string unit = 3;

  // color.
  string color = 4;
}

// vpc status namespace info.
message NamespaceStatus {
  // namespace name.
  string name = 1;

  // subnet name
  string subnet = 2;
}

// vpc status subnet info.
message SubnetStatus {
  // subnet name.
  string name = 1;

  // subnet cidr.
  string cidr = 2;

  // subnet uid.
  string id = 3;

  // subnet scope.
  string scope = 4;

  // subnet provider.
  string provider = 5;

  // subnet network type.
  string network_type = 6;
}

// vpc status distribute Gateway info.
message DGWGatewayStatus {
  // Gateway name.
  string name = 1;

  // Represents the different admin states of a dgw.
  enum AdminState {
    // The dgw resource is invalid.
    INVALID = 0;

    // The dgw resource is enable.
    ENABLE = 1;

    // The dgw resource is disable.
    DISABLE = 2;
  };

  // admin enable
  AdminState admin_state = 2;

  // Gateway ip.
  string ip = 3;

  // dest subnet
  string policy = 4;

  // Gateway uid.
  string id = 5;

  // Represents the different states of a dgw.
  enum State {
    // The dgw resource is being created.
    CREATING = 0;

    // The dgw resource is being updated.
    UPDATING = 1;

    // The dgw resource has been active.
    ACTIVE = 2;

    // The dgw resource is being deleting.
    DELETING = 3;

    // The dgw resource has been deleted.
    DELETED = 4;

    // The dgw resource has been failed.
    FAILED = 5;

    // the VPC dgw resource has been inactive
    INACTIVE = 6;

    // the VPC dgw resource has been error
    ERROR = 7;
  };

  // Gateway state
  State state = 6;
}

// vpc status natGateway info.
message NATGatewayStatus {
  // natGateway name.
  string name = 1;

  // natGateway ip.
  string ip = 2;

  // natGateway uid.
  string id = 3;
}

// vpc status eip info.
message EIPStatus {
  // eip name.
  string name = 1;

  // eip ip.
  string ip = 2;

  // eip uid.
  string id = 3;

  // The default snat.
  bool default_snat = 4;

  // EIP association type.
  enum AType {
    // The EIP resource type is NATGW.
    NATGW = 0;

    // The EIP resource type is POD.
    POD = 1;

    // The EIP resource type is SLB.
    SLB = 2;

    // The EIP resource type is BM.
    BM = 3;

    // The EIP support NATGW and BM at same time, 1030 default.
    NATGW_AND_BM = 4;
  };

  // Related device type，NATGW,POD,SLB,BM.
  AType association_type = 5;

  // Related device or item id, like vpc nat gateway id.
  string association_id = 6;
}

// GetVPCMetricsRequest
message GetVPCMetricsRequest {
}

// VPC 实例结构体.
// [EN] VPC entity.
message VPC {
  // The VPC resource id using the form:
  //     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}`.
  string id = 1;

  // The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 2;

  // ContainerInstance resource display name
  string display_name = 3 [(validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // ContainerInstance resource description
  string description = 4;

  // The VPC resource uuid.
  string uid = 5;

  // The VPC resource type.
  string resource_type = 6;

  // The id of the user who created the VPC resource.
  string creator_id = 7;

  // The id of the user who owns the VPC resource.
  string owner_id = 8;

  // Tenant id.
  string tenant_id = 9;

  // Available zone.
  string zone = 10;

  // Represents the different states of a VPC.
  enum State {
    // The VPC resource is being created.
    CREATING = 0;

    // The VPC resource is being updated.
    UPDATING = 1;

    // The VPC resource has been active.
    ACTIVE = 2;

    // The VPC resource is being deleting.
    DELETING = 3;

    // The VPC resource has been deleted.
    DELETED = 4;

    // The VPC resource has been failed.
    FAILED = 5;
  };

  // The current state of the VPC resource.
  State state = 11;

  // Sku id.
  string sku_id = 12;

  // Tags attached to the VPC resource.
  map<string, string> tags = 13;

  // Properties of the VPC resource.
  VPCProperties properties = 14;

  // Payment information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 15;

  // Indicates whether the VPC resource is deleted or not.
  bool deleted = 16;

  // The time when the VPC resource was created.
  google.protobuf.Timestamp create_time = 17;

  // The time when the VPC resource was last updated.
  google.protobuf.Timestamp update_time = 18;
}

// 资源实际属性.
// [EN] Real resource properties.
message VPCProperties {
  // VPC 级别最大的 cidr, subnet 的 cidr 应该为 VPC cidr 的子集.
  // [EN] VPC level cidr, subnet cidrs should be the subset of the VPC cidr.
  string cidr = 1;

  // 默认 VPC.
  // [EN] Default VPC or not.
  bool is_default = 2;

  // the VPC resource subnets' type
  SubnetType subnet_type = 3;
}

// Represents the different states of a VPC.
enum SubnetType {
  // only create vpc resource
  NULL = 0;
  // create vpc resource & default pod service subnet & nat gw
  POD_SERVICE = 1;
  // create vpc resource & default pod service subnet & nat gw  & training subnets
  POD_SERVICE_TRAINING = 2;
  // create vpc resource & default pod service subnet & nat gw  & training subnets & bms service service subnet
  POD_BMS_SERVICE = 3;
  // create vpc resource & default pod service subnet & nat gw  & training subnets & bms service service subnet & bms training sbunet
  POD_BMS_SERVICE_TRAINING = 4;
  // create vpc resource & default pod service subnet & nat gw  & bms service service subnet & bms training sbunet
  POD_SERVICE_BMS = 5;
}

// [zh] 获取符合 VPC RDMA 相关属性的请求.
// [en] Get requested VPC RDMA status.
message GetVPCRDMARequest {
  // [zh] 订阅
  // [en] Subscription
  string subscription_name = 1;

  // [zh] 资源组
  // [en] Resource group
  string resource_group_name = 2;

  // [zh] 可用区
  // [en] Available zone
  string zone = 3;

  // [zh] vpc名
  // [en] The VPC resource name with the restriction:
  // `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vpc_name = 4;
}

message VPCRDMAClustersStatus {
  // cluster_status
  repeated ClusterStatus clusters_status = 1;
}

// cluster_status
message ClusterStatus {
  // name.
  string name = 1;

  // protocol of cluster
  enum Protocol {
    // Default value. This value is unused.
    PROTOCOL_UNSPECIFIED = 0;

    // IB
    IB = 1;

    // RoCE
    RoCE = 2;

    // None
    None = 3;
  }
  // protocol. IB，RoCE，None
  Protocol protocol = 2;

  // network detail information
  repeated NetworkInfo networkInfos = 3;

  // extend config, such as "sharp_enable":"true"
  map<string, string> properties = 4;
}

message NetworkInfo {
  // nic_count. such as 1,2,4,8,16
  int32 nic_count = 1;

  // bandwidth.
  string bandwidth = 2;

  // nodes count
  int32 node_count = 3;
}