# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: VpcPeerings API
    description: |-
        [zh] 对等连接服务
         [en] VpcPeerings Service.
    version: 0.0.1
paths:
    /network/vpc_peering/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings:
        get:
            tags:
                - VpcPeerings
            description: |-
                [zh] 查看对等连接资源详情.
                 [en] Gets details of a VpcPeering resources.
            operationId: VpcPeerings_ListVpcPeering
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅. [en] Subscription.'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组. [en] Resource group.'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区. [en] Available zone.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListVpcPeeringResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/vpc_peering/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}
    :   get:
            tags:
                - VpcPeerings
            description: |-
                [zh] 查看对等连接资源详情.
                 [en] Gets details of a VpcPeering resources.
            operationId: VpcPeerings_GetVpcPeering
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅. [en] Subscription.'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组. [en] Resource group.'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区. [en] Available zone.'
                  required: true
                  schema:
                    type: string
                - name: vpc_peering_name
                  in: path
                  description: '[zh] 对等连接资源名称. [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VpcPeering'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - VpcPeerings
            description: |-
                [zh] 创建对等连接资源.
                 [en] Creates VpcPeering resources.
            operationId: VpcPeerings_CreateVpcPeering
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅. [en] Subscription.'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组. [en] Resource group.'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区. [en] Available zone.'
                  required: true
                  schema:
                    type: string
                - name: vpc_peering_name
                  in: path
                  description: '[zh] 对等连接资源名称. [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/VpcPeeringCreate'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VpcPeering'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - VpcPeerings
            description: |-
                [zh] 删除对等连接资源.
                 [en] Delete the VpcPeering resources.
            operationId: VpcPeerings_DeleteVpcPeering
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅. [en] Subscription.'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组. [en] Resource group.'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区. [en] Available zone.'
                  required: true
                  schema:
                    type: string
                - name: vpc_peering_name
                  in: path
                  description: '[zh] 对等连接资源名称. [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - VpcPeerings
            description: |-
                [zh] 更新对等连接资源.
                 [en] Update the VpcPeering resources.
            operationId: VpcPeerings_UpdateVpcPeering
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅. [en] The resource subscription.'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组. [en] The resource group.'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区. [en] The resource available zone.'
                  required: true
                  schema:
                    type: string
                - name: vpc_peering_name
                  in: path
                  description: '[zh] 资源名称. [en] The resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
                - name: update_mask
                  in: query
                  description: '[zh] 更新标记. [en] The resource update mask.'
                  schema:
                    type: string
                    format: field-mask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/VpcPeeringUpdate'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VpcPeering'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/vpc_peering/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}:release
    :   post:
            tags:
                - VpcPeerings
            description: |-
                [zh] 释放 (保留期释放).
                 [en] The Release of a VpcPeering resources.
            operationId: VpcPeerings_ReleaseVpcPeering
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅名. [en] Subscription.'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组. [en] Resource group.'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区. [en] Available zone.'
                  required: true
                  schema:
                    type: string
                - name: vpc_peering_name
                  in: path
                  description: '[zh] 对等连接资源名称. [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ReleaseVpcPeeringRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/vpc_peering/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}:resize
    :   post:
            tags:
                - VpcPeerings
            description: |-
                [zh] 扩缩容对等连接资源.
                 [en] The Resize of a VpcPeering resources.
            operationId: VpcPeerings_ResizeVpcPeering
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅名. [en] Subscription.'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组. [en] Resource group.'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区. [en] Available zone.'
                  required: true
                  schema:
                    type: string
                - name: vpc_peering_name
                  in: path
                  description: '[zh] 对等连接资源名称. [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/VpcPeeringResize'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VpcPeering'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        BillingItems:
            type: object
            properties: {}
            description: '[zh] 计费项 [en] The BillingItems in VpcPeeringProperties Resources.'
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        ListVpcPeeringResponse:
            type: object
            properties:
                vpc_peerings:
                    type: array
                    items:
                        $ref: '#/components/schemas/VpcPeering'
                    description: VPC Peering 列表. [EN] VPC peering list.
                total_size:
                    type: integer
                    description: total size
                    format: int32
            description: 列举 VPC Peering 的响应. [EN] Response to list VPC Peerings.
        OrderInfo:
            type: object
            properties:
                billing_cycle_number:
                    type: integer
                    description: '[zh] 购买时长. [en] length of purchase.'
                    format: int32
                auto_renew:
                    type: boolean
                    description: '[zh] 自动续费. [en] Automatic renewal.'
                currency_code:
                    type: string
                    description: '[zh] 货币代码. [en] currency code.'
                payment_channel:
                    type: integer
                    description: '[zh] 支付方式. [en] payment method.'
                    format: enum
                note:
                    type: string
                    description: '[zh] 订单备注. [en] order notes'
                order_type:
                    type: integer
                    description: '[zh] 订单类型. [en] Order Type.'
                    format: enum
                order_id:
                    type: string
                    description: '[zh] 订单id. [en] order id.'
                start_time:
                    type: string
                    description: '[zh] 订单生效日期. [en] Order effective date.'
                    format: date-time
                payment_model:
                    type: integer
                    description: '[zh] 付费类型. [en] payment type.'
                    format: enum
                billing_model:
                    type: integer
                    description: '[zh] 计费类型. [en] billing type.'
                    format: enum
                original_id:
                    type: string
                    description: '[zh] 合同包ID. [en] Contract package ID. original id-- OT_ORIGINAL: original spu_id/package_id; OT_RENEW/OT_UPGRADED/OT_DOWNGRADED: original/ order_id; OT_CONTRACT: original contract_id'
                end_time:
                    type: string
                    description: '[zh] 订单结束时间. [en] Order end time.'
                    format: date-time
            description: '[zh] 订单信息. [en] Order Infomation.'
        ReleaseVpcPeeringRequest:
            required:
                - subscription_name
                - resource_group_name
                - zone
                - vpc_peering_name
            type: object
            properties:
                subscription_name:
                    type: string
                    description: '[zh] 订阅名. [en] Subscription.'
                resource_group_name:
                    type: string
                    description: '[zh] 资源组. [en] Resource group.'
                zone:
                    type: string
                    description: '[zh] 可用区. [en] Available zone.'
                vpc_peering_name:
                    type: string
                    description: '[zh] 对等连接资源名称. [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
            description: '[zh] 释放对等连接资源. [en] Release VpcPeering resource.'
        Resources:
            type: object
            properties:
                billing_items:
                    $ref: '#/components/schemas/BillingItems'
            description: '[zh] 资源规格属性 [en] Resource Specification Properties.'
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        VpcPeering:
            required:
                - display_name
                - sku_id
            type: object
            properties:
                id:
                    readOnly: true
                    type: string
                    description: '[zh] 资源id. [en] The VpcPeering resource id using the form:     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/vpc_peerings/{name}`.'
                uid:
                    readOnly: true
                    type: string
                    description: '[zh] 资源的uuid. [en] The VpcPeering resource uuid.'
                name:
                    readOnly: true
                    type: string
                    description: '[zh] 资源标识. [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                display_name:
                    type: string
                    description: '[zh] 资源名称. [en] The VpcPeering resource display name'
                description:
                    type: string
                    description: '[zh] 资源描述. [en] The VpcPeering resource description.'
                resource_type:
                    type: string
                    description: '[zh] 资源类型. [en] The VpcPeering resource type.'
                creator_id:
                    readOnly: true
                    type: string
                    description: '[zh] 创建者id. [en] The id of the user who created the VpcPeering resource.'
                owner_id:
                    readOnly: true
                    type: string
                    description: '[zh] 拥有者id. [en] The id of the user who owns the VpcPeering resource.'
                tenant_id:
                    readOnly: true
                    type: string
                    description: '[zh] 租户id [en] Tenant id.'
                zone:
                    readOnly: true
                    type: string
                    description: '[zh] 可用区. [en] Available zone.'
                state:
                    readOnly: true
                    type: integer
                    description: '[zh] 资源状态. [en] The current state of the VpcPeering resource.'
                    format: enum
                sku_id:
                    type: string
                    description: '[zh] 最小库存单元id. [en] SKU id.'
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: '[zh] 对等连接资源的标签. [en] Tags attached to the VirtualMachine resource.'
                properties:
                    $ref: '#/components/schemas/VpcPeeringProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
                deleted:
                    readOnly: true
                    type: boolean
                    description: '[zh] 资源是否已删除. [en] Indicates whether the VpcPeering resource is deleted or not.'
                create_time:
                    readOnly: true
                    type: string
                    description: '[zh] 资源创建时间. [en] The time when the VpcPeering resource was created.'
                    format: date-time
                update_time:
                    readOnly: true
                    type: string
                    description: '[zh] 资源更新时间. [en] The time when the VpcPeering resource was last updated.'
                    format: date-time
            description: '[zh] 对等连接资源. [en] The VpcPeering resource.'
        VpcPeeringCreate:
            required:
                - display_name
                - sku_id
            type: object
            properties:
                id:
                    readOnly: true
                    type: string
                    description: '[zh] 资源id. [en] The VpcPeering resource id using the form:     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/vpc_peerings/{name}`.'
                uid:
                    readOnly: true
                    type: string
                    description: '[zh] 资源的uuid. [en] The VpcPeering resource uuid.'
                name:
                    readOnly: true
                    type: string
                    description: '[zh] 资源标识. [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                display_name:
                    type: string
                    description: '[zh] 资源名称. [en] The VpcPeering resource display name'
                description:
                    type: string
                    description: '[zh] 资源描述. [en] The VpcPeering resource description.'
                resource_type:
                    type: string
                    description: '[zh] 资源类型. [en] The VpcPeering resource type.'
                creator_id:
                    readOnly: true
                    type: string
                    description: '[zh] 创建者id. [en] The id of the user who created the VpcPeering resource.'
                owner_id:
                    readOnly: true
                    type: string
                    description: '[zh] 拥有者id. [en] The id of the user who owns the VpcPeering resource.'
                tenant_id:
                    readOnly: true
                    type: string
                    description: '[zh] 租户id [en] Tenant id.'
                zone:
                    readOnly: true
                    type: string
                    description: '[zh] 可用区. [en] Available zone.'
                state:
                    readOnly: true
                    type: integer
                    description: '[zh] 资源状态. [en] The current state of the VpcPeering resource.'
                    format: enum
                sku_id:
                    type: string
                    description: '[zh] 最小库存单元id. [en] SKU id.'
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: '[zh] 对等连接资源的标签. [en] Tags attached to the VpcPeering resource.'
                properties:
                    $ref: '#/components/schemas/VpcPeeringProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
                deleted:
                    readOnly: true
                    type: boolean
                    description: '[zh] 资源是否已删除. [en] Indicates whether the VpcPeering resource is deleted or not.'
                create_time:
                    readOnly: true
                    type: string
                    description: '[zh] 资源创建时间. [en] The time when the VpcPeering resource was created.'
                    format: date-time
                update_time:
                    readOnly: true
                    type: string
                    description: '[zh] 资源更新时间. [en] The time when the VpcPeering resource was last updated.'
                    format: date-time
            description: '[zh] 对等连接资源. [en] The VpcPeering resource.'
        VpcPeeringProperties:
            type: object
            properties:
                resources:
                    $ref: '#/components/schemas/Resources'
                vpc:
                    type: string
                    description: '[zh] 本地的 vpc 名. [en] name of local vpc.'
                type:
                    type: string
                    description: '[zh] 对等连接的类型. [en] type of vpc peering.'
                transit_switch:
                    type: string
                    description: '[zh] 对等连接使用的 trainsit switch 名. [en] transit switch of this vpc peering.'
                local_gateway_ip:
                    type: string
                    description: '[zh] 本地对等连接在 transit switch 上的 ip. [en] local gateway ip in transit switch.'
                local_gateway_nodes:
                    type: array
                    items:
                        type: string
                    description: '[zh] 本地对等连接路由的网关节点. [en] node name of gateway.'
                remote_cidr:
                    type: array
                    items:
                        type: string
                    description: '[zh] 对等连接远端的 CIDR. [en] remote CIDR of this vpc peering.'
                remote_gateway_ip:
                    type: string
                    description: '[zh] 对等连接远端的网关ip. [en] local gateway ip in transit switch.'
                remote_vpc:
                    type: string
                    description: '[zh] 对等连接远端的 vpc 名称. [en] name of remote vpc in this peering.'
                local_cidr:
                    type: array
                    items:
                        type: string
                    description: '[zh] 对等连接本端的 CIDR. [en] local CIDR of this vpc peering.'
                remote_zone:
                    type: string
                    description: '[zh] 对等连接远端 zone. [en] available zone of remote vpc in this peering.'
            description: '[zh] 资源属性. [en] The VpcPeeringProperties.'
        VpcPeeringResize:
            required:
                - resource_id
                - sku_id
                - operator_id
            type: object
            properties:
                resource_id:
                    type: string
                    description: '[zh] 对等连接资源uuid. [en] VpcPeering resource uuid.'
                sku_id:
                    type: string
                    description: '[zh] 对等连接资源sku_id. [en] VpcPeering resource sku_id.'
                operator_id:
                    type: string
                    description: '[zh] 变更操作者id. [en] operator id.'
                properties:
                    $ref: '#/components/schemas/VpcPeeringProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
            description: '[zh] 变更对等连接资源. [en] Resize VpcPeering resource.'
        VpcPeeringUpdate:
            required:
                - display_name
            type: object
            properties:
                display_name:
                    type: string
                    description: '[zh] 资源名称. [en] The resource display name with the restriction: `^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]?([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_]{0,62})$`.'
                description:
                    type: string
                    description: '[zh] 资源描述. [en] The resource Description.'
            description: '[zh] 更新对等连接资源. [en] Update VpcPeering body.'
tags:
    - name: VpcPeerings
