syntax = "proto3";
package sensetime.core.network.vpc.v1;
option go_package = "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1;vpc";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/api/annotations.proto";
import "google/protobuf/field_mask.proto";
import "higgs/common/v1/orderinfo.proto";

// [zh] 对等连接服务
// [en] VpcPeerings Service.
service VpcPeerings {
  // [zh] 创建对等连接资源.
  // [en] Creates VpcPeering resources.
  rpc CreateVpcPeering(CreateVpcPeeringRequest) returns (VpcPeering) {
    option (google.api.http) = {
      post : "/network/vpc_peering/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}"
      body : "vpc_peering"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}"
      permission: "vpc_peering.instance.create"
    };
  }
  // [zh] 删除对等连接资源.
  // [en] Delete the VpcPeering resources.
  rpc DeleteVpcPeering(DeleteVpcPeeringRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete : "/network/vpc_peering/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}"
      permission: "vpc_peering.instance.delete"
    };
  }
  // [zh] 更新对等连接资源.
  // [en] Update the VpcPeering resources.
  rpc UpdateVpcPeering(UpdateVpcPeeringRequest) returns (VpcPeering) {
    option (google.api.http) = {
      patch: "/network/vpc_peering/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}"
      body: "vpc_peering"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.is_asynchronous) = false;
     option (sensetime.core.higgs.api.policy) = {
       scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}"
       permission: "vpc_peering.instance.update"
     };
  }
  // [zh] 查看对等连接资源详情.
  // [en] Gets details of a VpcPeering resources.
  rpc GetVpcPeering(GetVpcPeeringRequest) returns (VpcPeering) {
    option (google.api.http) = {
      get : "/network/vpc_peering/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}"
      permission: "vpc_peering.instance.get"
    };
  }

  // [zh] 查看对等连接资源详情.
  // [en] Gets details of a VpcPeering resources.
  rpc ListVpcPeering(ListVpcPeeringRequest) returns (ListVpcPeeringResponse) {
    option (google.api.http) = {
      get : "/network/vpc_peering/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}"
      permission: "vpc_peering.instance.get"
    };
  }


  // [zh] 扩缩容对等连接资源.
  // [en] The Resize of a VpcPeering resources.
  rpc ResizeVpcPeering(ResizeVpcPeeringRequest) returns (VpcPeering) {
    option (google.api.http) = {
      post : "/network/vpc_peering/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}:resize"
      body : "vpc_peering_resize"
    };
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}"
      permission: "vpc_peering.instance.resize"
    };
  }

  // [zh] 释放 (保留期释放).
  // [en] The Release of a VpcPeering resources.
  rpc ReleaseVpcPeering(ReleaseVpcPeeringRequest) returns (google.protobuf.Empty) {
    option(google.api.http) = {
      post: "/network/vpc_peering/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}:release"
      body: "*"
    };
    option (sensetime.core.higgs.api.is_control_plane) = true;
    option (sensetime.core.higgs.api.is_asynchronous) = true;
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpc_peerings/{vpc_peering_name}"
      permission: "vpc_peering.instance.delete"
    };
  }
}

// [zh] 创建对等连接资源请求.
// [en] CreateVpcPeeringRequest.
message CreateVpcPeeringRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1;

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2;

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3;

  // [zh] 对等连接资源名称.
  // [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vpc_peering_name = 4 [(validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 创建对等连接资源.
  // [en] The VpcPeering resource to create.
  VpcPeeringCreate vpc_peering = 5; // [(validate.rules).message.required = true];
}

// [zh] 对等连接资源.
// [en] The VpcPeering resource.
message VpcPeeringCreate {
  // [zh] 资源id.
  // [en] The VpcPeering resource id using the form:
  //     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/vpc_peerings/{name}`.
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源的uuid.
  // [en] The VpcPeering resource uuid.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源标识.
  // [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源名称.
  // [en] The VpcPeering resource display name
  string display_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // [zh] 资源描述.
  // [en] The VpcPeering resource description.
  string description = 5;

  // [zh] 资源类型.
  // [en] The VpcPeering resource type.
  string resource_type = 6;

  // [zh] 创建者id.
  // [en] The id of the user who created the VpcPeering resource.
  string creator_id = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 拥有者id.
  // [en] The id of the user who owns the VpcPeering resource.
  string owner_id = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 租户id
  // [en] Tenant id.
  string tenant_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源状态.
  // [en] The current state of the VpcPeering resource.
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 最小库存单元id.
  // [en] SKU id.
  string sku_id = 12 [(google.api.field_behavior) = REQUIRED];

  // [zh] 对等连接资源的标签.
  // [en] Tags attached to the VpcPeering resource.
  map<string, string> tags = 13;

  // [zh] 对等连接资源的属性
  // [en] Properties of the VpcPeering resource.
  VpcPeeringProperties properties = 14; // [(validate.rules).message.required = true];

  // [zh] 订单信息.
  // [en] Order information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 15; // [(validate.rules).message.required = true];

  // [zh] 资源是否已删除.
  // [en] Indicates whether the VpcPeering resource is deleted or not.
  bool deleted = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源创建时间.
  // [en] The time when the VpcPeering resource was created.
  google.protobuf.Timestamp create_time = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源更新时间.
  // [en] The time when the VpcPeering resource was last updated.
  google.protobuf.Timestamp update_time = 18 [(google.api.field_behavior) = OUTPUT_ONLY];
}

message VpcPeeringProperty {
  }

// [zh] 删除对等连接资源请求.
// [en] DeleteVpcPeeringRequest.
message DeleteVpcPeeringRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 对等连接资源名称.
  // [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vpc_peering_name = 4 [(google.api.field_behavior) = REQUIRED];
}

// [zh] 更新对等连接请求.
// [en] Update VpcPeering request.
message UpdateVpcPeeringRequest{
  // [zh] 订阅.
  // [en] The resource subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];
  // [zh] 资源组.
  // [en] The resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];
  // [zh] 可用区.
  // [en] The resource available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];
  // [zh] 资源名称.
  // [en] The resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vpc_peering_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
  // [zh] 更新资源.
  // [en] The resource to update.
  VpcPeeringUpdate vpc_peering = 5;
  // [zh] 更新标记.
  // [en] The resource update mask.
  google.protobuf.FieldMask update_mask = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// [zh] 更新对等连接资源.
// [en] Update VpcPeering body.
message VpcPeeringUpdate {
  // [zh] 资源名称.
  // [en] The resource display name with the restriction: `^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]?([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_]{0,62})$`.
  optional string display_name = 1 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];
  // [zh] 资源描述.
  // [en] The resource Description.
  string description = 2;
}

// [zh] 查看对等连接资源详情请求.
// [en] GetVpcPeeringRequest.
message GetVpcPeeringRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 对等连接资源名称.
  // [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vpc_peering_name = 4 [(google.api.field_behavior) = REQUIRED];
}

// 列举 VPC Peering 的请求.
// [EN] Request to list VPC Peerings.
message ListVpcPeeringRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];
}

// 列举 VPC Peering 的响应.
// [EN] Response to list VPC Peerings.
message ListVpcPeeringResponse {
  // VPC Peering 列表.
  // [EN] VPC peering list.
  repeated VpcPeering vpc_peerings = 1;

  // total size
  int32 total_size = 2;
}

// [zh] ResizeVpcPeeringRequest.
// [en] 对等连接资源变更请求.
message ResizeVpcPeeringRequest {
  // [zh] 订阅名.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];
  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];
  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];
  // [zh] 对等连接资源名称.
  // [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vpc_peering_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
  // [zh] 变更资源包body参数.
  // [en] Resize VpcPeering resource.
  VpcPeeringResize vpc_peering_resize = 5 [(google.api.field_behavior) = REQUIRED];
}

// [zh] 变更对等连接资源.
// [en] Resize VpcPeering resource.
message VpcPeeringResize {
    // [zh] 对等连接资源uuid.
    // [en] VpcPeering resource uuid.
    string resource_id = 1 [(google.api.field_behavior) = REQUIRED];
    // [zh] 对等连接资源sku_id.
    // [en] VpcPeering resource sku_id.
    string sku_id = 2 [(google.api.field_behavior) = REQUIRED];
    // [zh] 变更操作者id.
    // [en] operator id.
    string operator_id = 3 [(google.api.field_behavior) = REQUIRED];
    // [zh] 对等连接资源的属性
    // [en] Properties of the VpcPeering resource.
    VpcPeeringProperties properties = 4; // [(validate.rules).message.required = true];
    // [zh] 订单信息.
    // [en] Order information.
    sensetime.core.higgs.common.v1.OrderInfo order_info = 5; // [(validate.rules).message.required = true];
}

// [zh] 释放对等连接资源.
// [en] Release VpcPeering resource.
message ReleaseVpcPeeringRequest {
    // [zh] 订阅名.
    // [en] Subscription.
    string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];
    // [zh] 资源组.
    // [en] Resource group.
    string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];
    // [zh] 可用区.
    // [en] Available zone.
    string zone = 3 [(google.api.field_behavior) = REQUIRED];
    // [zh] 对等连接资源名称.
    // [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    string vpc_peering_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 对等连接资源.
// [en] The VpcPeering resource.
message VpcPeering {
  // [zh] 资源id.
  // [en] The VpcPeering resource id using the form:
  //     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/vpc_peerings/{name}`.
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源的uuid.
  // [en] The VpcPeering resource uuid.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源标识.
  // [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源名称.
  // [en] The VpcPeering resource display name
  string display_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // [zh] 资源描述.
  // [en] The VpcPeering resource description.
  string description = 5;

  // [zh] 资源类型.
  // [en] The VpcPeering resource type.
  string resource_type = 6;

  // [zh] 创建者id.
  // [en] The id of the user who created the VpcPeering resource.
  string creator_id = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 拥有者id.
  // [en] The id of the user who owns the VpcPeering resource.
  string owner_id = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 租户id
  // [en] Tenant id.
  string tenant_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源状态.
  // [en] The current state of the VpcPeering resource.
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 最小库存单元id.
  // [en] SKU id.
  string sku_id = 12 [(google.api.field_behavior) = REQUIRED];

  // [zh] 对等连接资源的标签.
  // [en] Tags attached to the VirtualMachine resource.
  map<string, string> tags = 13;

  // [zh] 对等连接资源的属性
  // [en] Properties of the VirtualMachine resource.
  VpcPeeringProperties properties = 14; // [(validate.rules).message.required = true];

  // [zh] 订单信息.
  // [en] Order information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 15; // [(validate.rules).message.required = true];

  // [zh] 资源是否已删除.
  // [en] Indicates whether the VpcPeering resource is deleted or not.
  bool deleted = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源创建时间.
  // [en] The time when the VpcPeering resource was created.
  google.protobuf.Timestamp create_time = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源更新时间.
  // [en] The time when the VpcPeering resource was last updated.
  google.protobuf.Timestamp update_time = 18 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// [zh] 资源属性.
// [en] The VpcPeeringProperties.
message VpcPeeringProperties {
  // [zh] 资源规格属性.
  // [en] Resource Specification Properties.
  Resources resources = 1;

  // [zh] 本地的 vpc 名.
  // [en] name of local vpc.
  string vpc = 2;

  // [zh] 对等连接的类型.
  // [en] type of vpc peering.
  string type = 3;

  // [zh] 对等连接使用的 trainsit switch 名.
  // [en] transit switch of this vpc peering.
  string transit_switch = 4;

  // [zh] 本地对等连接在 transit switch 上的 ip.
  // [en] local gateway ip in transit switch.
  string local_gateway_ip = 5;

  // [zh] 本地对等连接路由的网关节点.
  // [en] node name of gateway.
  repeated string local_gateway_nodes = 6;

  // [zh] 对等连接远端的 CIDR.
  // [en] remote CIDR of this vpc peering.
  repeated string remote_cidr = 7;

  // [zh] 对等连接远端的网关ip.
  // [en] local gateway ip in transit switch.
  string remote_gateway_ip = 8;

  // [zh] 对等连接远端的 vpc 名称.
  // [en] name of remote vpc in this peering.
  string remote_vpc = 9;

  // [zh] 对等连接本端的 CIDR.
  // [en] local CIDR of this vpc peering.
  repeated string local_cidr = 10;

  // [zh] 对等连接远端 zone.
  // [en] available zone of remote vpc in this peering.
  string remote_zone = 11;
}

// [zh] 资源规格属性
// [en] Resource Specification Properties.
message Resources {
  // [zh] 单独购买的计费项
  // [en] Billing Items Purchased Separately.
  BillingItems billing_items = 4;
}

// [zh] 计费项
// [en] The BillingItems in VpcPeeringProperties Resources.
message BillingItems {

}

// [zh] 资源状态.
// [en] Represents the different states of a VpcPeering.
enum State {
  // [zh] 创建中.
  // [en] The VpcPeering resource is being created.
  CREATING = 0;

  // [zh] 更新中.
  // [en] The VpcPeering resource is being updated.
  UPDATING = 1;

  // [zh] 已激活.
  // [en] The VpcPeering resource has been active.
  ACTIVE = 2;

  // [zh] 删除中.
  // [en] TThe VpcPeering resource is being deleted.
  DELETING = 3;

  // [zh] 已删除.
  // [en] The VpcPeering resource has been deleted.
  DELETED = 4;

  // [zh] 操作失败.
  // [en] The VpcPeering resource has failed.
  FAILED = 5;

  // [zh] 到期降级中
  // [en]  The VpcPeering resource is being expireDowngrading.
  EXPIREDOWNGRADING = 6;

  // [zh] 到期已降级
  // [en]   The VpcPeering resource has been expireDowngrade.
  EXPIREDOWNGRADED = 7;

  // [zh] 续订升级中
  // [en]   The VpcPeering resource is being renewUpgrading.
  RENEWUPGRADING = 8;

  // [zh] 到期停服中.
  // [en] The VpcPeering resource is being disabled.
  EXPIRESTOPPING = 9;

  // [zh] 到期已停服.
  // [en] The VpcPeering resource has been disabled.
  EXPIRESTOPPED = 10;

  // [zh] 续订恢复中.
  // [en] The VpcPeering resource is being enabled.
  RENEWSTARTING = 11;

  // [zh] 服务降级中【瞬时】.
  // [en] Default, the VpcPeering is being downgrading.
  DOWNGRADING = 12 ;

  // [zh] 服务降级.
  // [en] Default, the VpcPeering is being downgraded.
  DOWNGRADE = 13 ;

  // [zh] 服务降级恢复中【瞬时】.
  // [en] Default, the VpcPeering is being restoring.
  RESTORING=14;

  // [zh] 欠费停服中【瞬时】.
  // [en] Default, the VpcPeering is being arrearStopping.
  ARREARSTOPPING = 15;

  // [zh] 欠费停服
  // [en] Default, the VpcPeering is being arrearStopped.
  ARREARSTOPPED = 16;

  // [zh] 充值恢复中【瞬时】.
  // [en] Default, the VpcPeering is being rechargeStarting.
  RECHARGESTARTING = 17;

  // [zh] 资源变更中【瞬时】.
  // [en] Default, the VpcPeering is being resizing.
  RESIZING =18;

  // [zh] 取消资源变更【瞬时】.
  // [en] Default, the aoss pack is being resizeCanceling.
  RESIZECANCELING = 19;
};
