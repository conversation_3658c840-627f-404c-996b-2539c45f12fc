syntax = "proto3";
package sensetime.core.network.vnic.v1;
option go_package = "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vnic/v1;vnic";

import "google/api/annotations.proto";
import "google/api/field_behavior.proto";
import "google/protobuf/empty.proto";
import "validate/validate.proto";
import "google/protobuf/timestamp.proto";
import "higgs/api/annotations.proto";
import "google/protobuf/field_mask.proto";
import "higgs/common/v1/orderinfo.proto";

// [zh] 虚拟网卡服务
// [en] VNics Service.
service VNics {
  // [zh] 创建虚拟网卡资源.
  // [en] Creates VNic resources.
  rpc CreateVNic(CreateVNicRequest) returns (VNic) {
    option (google.api.http) = {
      post : "/network/vnic/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vnics/{vnic_name}"
      body : "vnic"
    };
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vnics/{vnic_name}"
      permission: "vnic.instance.create"
    };
  }

  // [zh] 删除虚拟网卡资源.
  // [en] Delete the VNic resources.
  rpc DeleteVNic(DeleteVNicRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      delete : "/network/vnic/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vnics/{vnic_name}"
    };
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vnics/{vnic_name}"
      permission: "vnic.instance.delete"
    };
  }

  // [zh] 更新虚拟网卡资源.
  // [en] Update the VNic resources.
  rpc UpdateVNic(UpdateVNicRequest) returns (VNic) {
    option (google.api.http) = {
      patch: "/network/vnic/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vnics/{vnic_name}"
      body: "vnic"
    };
     option (sensetime.core.higgs.api.policy) = {
       scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vnics/{vnic_name}"
       permission: "vnic.instance.update"
     };
  }

  // [zh] 查看虚拟网卡资源详情.
  // [en] Gets details of a VNic resource.
  rpc GetVNic(GetVNicRequest) returns (VNic) {
    option (google.api.http) = {
      get : "/network/vnic/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vnics/{vnic_name}"
    };
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vnics/{vnic_name}"
      permission: "vnic.instance.get"
    };
  }

  // [zh] 列举虚拟网卡资源详情.
  // [en] List details of VNic resources.
  rpc ListVNics(ListVNicRequest) returns (ListVNicsResponse) {
    option (google.api.http) = {
      get : "/network/vnic/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/subnets/{subnet_name}"
    };
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/subnets/{subnet_name}"
      permission: "vnic.instance.list"
    };
  }

  // [zh] 扩缩容虚拟网卡资源.
  // [en] The Resize of a VNic resources.
  rpc ResizeVNic(ResizeVNicRequest) returns (VNic) {
    option (google.api.http) = {
      post : "/network/vnic/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vnics/{vnic_name}:resize"
      body : "vnic_resize"
    };
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vnics/{vnic_name}"
      permission: "vnic.instance.update"
    };
  }

  // [zh] 释放 (保留期释放).
  // [en] The Release of a VNic resources.
  rpc ReleaseVNic(ReleaseVNicRequest) returns (google.protobuf.Empty) {
    option (google.api.http) = {
      post: "/network/vnic/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vnics/{vnic_name}:release"
      body: "*"
    };
    option (sensetime.core.higgs.api.policy) = {
      scope: "/rm/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vnics/{vnic_name}"
      permission: "vnic.instance.delete"
    };
  }
}

// [zh] 创建虚拟网卡资源请求.
// [en] CreateVNicRequest.
message CreateVNicRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1;

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2;

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3;

  // [zh] 虚拟网卡资源名称.
  // [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vnic_name = 4 [(validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];

  // [zh] 创建虚拟网卡资源.
  // [en] The VNic resource to create.
  VNicCreate vnic = 5; // [(validate.rules).message.required = true];
}

// [zh] 虚拟网卡资源.
// [en] The VNic resource.
message VNicCreate {
  // [zh] 资源id.
  // [en] The VNic resource id using the form:
  //     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/vnics/{name}`.
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] vnic 的 subnet 名.
  // [en] The name of the subnet.
  string subnet = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源标识.
  // [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源描述.
  // [en] The VNic resource description.
  string description = 4;

  // [zh] 资源类型.
  // [en] The VNic resource type.
  string resource_type = 5;

  // [zh] 最小库存单元id.
  // [en] SKU id.
  string sku_id = 12;

  // [zh] 虚拟网卡资源的标签.
  // [en] Tags attached to the VNic resource.
  map<string, string> tags = 13;

  // [zh] 虚拟网卡资源的属性
  // [en] Properties of the VNic resource.
  VNicProperties properties = 14; // [(validate.rules).message.required = true]

  // [zh] 订单信息.
  // [en] Order information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 15; // [(validate.rules).message.required = true]
}

// [zh] 删除虚拟网卡资源请求.
// [en] DeleteVNicRequest.
message DeleteVNicRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] vnic 的 subnet 名.
  // [en] The name of the subnet.
  string subnet = 4 [(google.api.field_behavior) = REQUIRED];

  // [zh] 虚拟网卡资源名称.
  // [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vnic_name = 5 [(google.api.field_behavior) = REQUIRED];
}

// [zh] 更新虚拟网卡请求.
// [en] Update VNic request.
message UpdateVNicRequest{
  // [zh] 订阅.
  // [en] The resource subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];
  // [zh] 资源组.
  // [en] The resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];
  // [zh] 可用区.
  // [en] The resource available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];
  // [zh] 资源名称.
  // [en] The resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vnic_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
  // [zh] 更新资源.
  // [en] The resource to update.
  VNicUpdate vnic = 5;
  // [zh] 更新标记.
  // [en] The resource update mask.
  google.protobuf.FieldMask update_mask = 6 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// [zh] 更新虚拟网卡资源.
// [en] Update VNic body.
message VNicUpdate {
  // [zh] 资源名称.
  // [en] The resource display name with the restriction: `^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]?([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_]{0,62})$`.
  string display_name = 1 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {ignore_empty: true, pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];
  // [zh] 资源描述.
  // [en] The resource Description.
  string description = 2;
}

// [zh] 查看虚拟网卡资源详情请求.
// [en] GetVNicRequest.
message GetVNicRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 子网名.
  // [en] The name of the subnet.
  string subnet = 4 [(google.api.field_behavior) = REQUIRED];

  // [zh] 虚拟网卡资源名称.
  // [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vnic_name = 5 [(google.api.field_behavior) = REQUIRED];
}

// 列举 VNIC 的请求.
// [EN] Request to list VNICs.
message ListVNicRequest {
  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];

  // [zh] 子网名.
  // [en] The name of the subnet.
  string subnet_name = 4 [(google.api.field_behavior) = REQUIRED];
}

// 列举 VNIC 的响应.
// [EN] Response to list VNICs.
message ListVNicsResponse {
  // VPC 列表.
  // [EN] VPC list.
  repeated VNic vnics = 1;

  // total size
  int32 total_size = 3;
}


// [zh] ResizeVNicRequest.
// [en] 虚拟网卡资源变更请求.
message ResizeVNicRequest {
  // [zh] 订阅名.
  // [en] Subscription.
  string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];
  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];
  // [zh] 可用区.
  // [en] Available zone.
  string zone = 3 [(google.api.field_behavior) = REQUIRED];
  // [zh] 虚拟网卡资源名称.
  // [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string vnic_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
  // [zh] 变更资源包body参数.
  // [en] Resize VNic resource.
  VNicResize vnic_resize = 5 [(google.api.field_behavior) = REQUIRED];
}

// [zh] 变更虚拟网卡资源.
// [en] Resize VNic resource.
message VNicResize {
    // [zh] 虚拟网卡资源uuid.
    // [en] VNic resource uuid.
    string resource_id = 1 [(google.api.field_behavior) = REQUIRED];
    // [zh] 虚拟网卡资源sku_id.
    // [en] VNic resource sku_id.
    string sku_id = 2 [(google.api.field_behavior) = REQUIRED];
    // [zh] 变更操作者id.
    // [en] operator id.
    string operator_id = 3 [(google.api.field_behavior) = REQUIRED];
    // [zh] 虚拟网卡资源的属性
    // [en] Properties of the VNic resource.
    VNicProperties properties = 14; // [(validate.rules).message.required = true];
    // [zh] 订单信息.
    // [en] Order information.
    sensetime.core.higgs.common.v1.OrderInfo order_info = 5; // [(validate.rules).message.required = true];
}

// [zh] 释放虚拟网卡资源.
// [en] Release VNic resource.
message ReleaseVNicRequest {
    // [zh] 订阅名.
    // [en] Subscription.
    string subscription_name = 1 [(google.api.field_behavior) = REQUIRED];
    // [zh] 资源组.
    // [en] Resource group.
    string resource_group_name = 2 [(google.api.field_behavior) = REQUIRED];
    // [zh] 可用区.
    // [en] Available zone.
    string zone = 3 [(google.api.field_behavior) = REQUIRED];
    // [zh] 虚拟网卡资源名称.
    // [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
    string vnic_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$", max_len: 63}];
}

// [zh] 虚拟网卡资源.
// [en] The VNic resource.
message VNic {
  // [zh] 资源id.
  // [en] The VNic resource id using the form:
  //     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/vnics/{name}`.
  string id = 1 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源的uuid.
  // [en] The VNic resource uuid.
  string uid = 2 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源标识.
  // [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 3 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源名称.
  // [en] The VNic resource display name
  string display_name = 4 [(google.api.field_behavior) = REQUIRED, (validate.rules).string = {pattern: "^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$", max_len: 63}];

  // [zh] 资源描述.
  // [en] The VNic resource description.
  string description = 5;

  // [zh] 资源类型.
  // [en] The VNic resource type.
  string resource_type = 6;

  // [zh] 创建者id.
  // [en] The id of the user who created the VNic resource.
  string creator_id = 7 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 拥有者id.
  // [en] The id of the user who owns the VNic resource.
  string owner_id = 8 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 租户id
  // [en] Tenant id.
  string tenant_id = 9 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 10 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源状态.
  // [en] The current state of the VNic resource.
  State state = 11 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 最小库存单元id.
  // [en] SKU id.
  string sku_id = 12 [(google.api.field_behavior) = REQUIRED];

  // [zh] 虚拟网卡资源的标签.
  // [en] Tags attached to the VirtualMachine resource.
  map<string, string> tags = 13;

  // [zh] 虚拟网卡资源的属性
  // [en] Properties of the VirtualMachine resource.
  VNicProperties properties = 14; // [(validate.rules).message.required = true];

  // [zh] 订单信息.
  // [en] Order information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 15; // [(validate.rules).message.required = true];

  // [zh] 资源是否已删除.
  // [en] Indicates whether the VNic resource is deleted or not.
  bool deleted = 16 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源创建时间.
  // [en] The time when the VNic resource was created.
  google.protobuf.Timestamp create_time = 17 [(google.api.field_behavior) = OUTPUT_ONLY];

  // [zh] 资源更新时间.
  // [en] The time when the VNic resource was last updated.
  google.protobuf.Timestamp update_time = 18 [(google.api.field_behavior) = OUTPUT_ONLY];
}

// [zh] 资源属性.
// [en] The VNicProperties.
message VNicProperties {
  // [zh] 资源规格属性.
  // [en] Resource Specification Properties.
  Resources resources = 1;

  // ID of this VNic
  string vnic_id = 2;

  // The VIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
  string name = 3 [(validate.rules).string = { pattern: "^[a-z]([a-z0-9-]{0,30}[a-z0-9])?$", max_len: 32 }];

  // The IPv4 address
  string ip_addr_v4 = 4;
  
  // The Mac address
  string mac_addr = 5;

  // The name of vnic subnet
  string subnet = 6;

  // The name of vip vpc
  string vpc = 7;
}

// [zh] 资源规格属性
// [en] Resource Specification Properties.
message Resources {
  // [zh] 单独购买的计费项
  // [en] Billing Items Purchased Separately.
  BillingItems billing_items = 4;
}

// [zh] 计费项
// [en] The BillingItems in VNicProperties Resources.
message BillingItems {

}

// [zh] 资源状态.
// [en] Represents the different states of a VNic.
enum State {
  // [zh] 未知状态
  // [en] Unknown status
  UNKNOWN = 0;

  // [zh] 已激活, 未分配.
  // [en] The VNic resource has been active but not allocated.
  ACTIVE = 1;

  // [zh] 已被使用.
  // [en] TThe VNic resource is being deleted.
  RESERVED = 2;
};
