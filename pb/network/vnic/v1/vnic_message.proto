syntax = "proto3";
package sensetime.core.network.vnic.v1;
option go_package = "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vnic/v1;vnic";


import "network/vnic/v1/vnic.proto";
import "higgs/common/v1/orderinfo.proto";

// [zh] 创建虚拟网卡资源消息.
// [en] CreateVNicMessage.
message CreateVNicMessage {

  // [zh] 租户编码.
  // [en] tenant code.
  string tenant_code = 1;

  // [zh] 租户物理资源池信息.
  // [en] tenant prp.
  repeated  string tenant_prp = 2;

  // [zh] 操作者ID.
  // [en] operator id.
  string operator_id = 3;

  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 4;

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 5;

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 6;

  // [zh] 虚拟网卡资源名称.
  // [en] The VNic resource name.
  string vnic_name = 7;

  // [zh] 虚拟网卡资源.
  // [en] The VNic resource struct.
  VNic vnic = 8;

  // [zh] 专有云资源.
  // [en] The dedicated resource struct.
  DedicatedResource dedicated_resource = 9;

  // [zh] Boss回传信息.
  // [en] Boss returns information.
  CallbackData callback_data = 10;
}

// [zh] 退订虚拟网卡资源消息.
// [en] CreateVNicMessage.
message DeleteVNicMessage {

  // [zh] 租户编码.
  // [en] tenant code.
  string tenant_code = 1;

  // [zh] 租户物理资源池信息.
  // [en] tenant prp.
  repeated  string tenant_prp = 2;

  // [zh] 操作者ID.
  // [en] operator id.
  string operator_id = 3;

  // [zh] 订阅.
  // [en] Subscription.
  string subscription_name = 4;

  // [zh] 资源组.
  // [en] Resource group.
  string resource_group_name = 5;

  // [zh] 可用区.
  // [en] Available zone.
  string zone = 6;

  // [zh] 虚拟网卡资源名称.
  // [en] The VNic resource name.
  string vnic_name = 7;
}

// [zh] 扩缩容虚拟网卡资源消息.
// [en] ResizeVNicMessage.
message ResizeVNicMessage {

  // [zh] 租户编码.
  // [en] tenant code.
  string tenant_code = 1;

  // [zh] 租户物理资源池信息.
  // [en] tenant prp.
  repeated  string tenant_prp = 2;

  // [zh] 操作者ID.
  // [en] operator id.
  string operator_id = 3;

  // [zh] 资源ID.
  // [en] resource id.
  string resource_id = 4;

  // [zh] 最小库存单元id.
  // [en] SKU id.
  string sku_id = 5;

  // [zh] 资源的属性
  // [en] Properties of the resource.
  ModelSpaceProperties properties = 6;

  // [zh] 订单信息.
  // [en] Order information.
  sensetime.core.higgs.common.v1.OrderInfo order_info = 7;

  // [zh] 专有云资源.
  // [en] The dedicated resource struct.
  DedicatedResource dedicated_resource = 9;

  // [zh] Boss回传信息.
  // [en] Boss returns information.
  CallbackData callback_data = 10;
}

message ModelSpaceProperties {
}

// [zh] 专有云资源信息.
// [en] The dedicated resource.
message DedicatedResource {
  // [zh] 是否是专有云租户.
  // [en] The dedicated tenant.
  bool is_dedicated_tenant = 1;
  // [zh] 是否是专有云资源.
  // [en] The dedicated resource.
  bool is_dedicated_resource = 2;
  // [zh] 专有云资源包ID.
  // [en] The dedicated resource package id.
  string resource_package_id = 3;
}

// [zh] Boss回传订单信息.
// [en] Boss returns order information.
message CallbackData {
  // [zh] 租户ID.
  // [en] The tenant id.
  string tenant_id = 1;
  // [zh] 用户ID.
  // [en] The user id.
  string user_id = 2;
  // [zh] 订单ID.
  // [en] The order id.
  string order_id = 3;
}
