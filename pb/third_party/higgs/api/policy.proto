syntax = "proto3";
// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
package sensetime.core.higgs.api;

option go_package = "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/api/annotations;annotations";
// policy info
message PolicyInfo {
  // scope
  string scope = 1;
  //   permission
  string permission = 2;
};