syntax = "proto3";

// (-- api-linter: core::0215::versioned-packages=disabled
//     aip.dev/not-precedent: We need to do this because reasons. --)
package sensetime.core.higgs.api;

import "google/protobuf/descriptor.proto";

import "higgs/api/policy.proto";

option go_package = "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/api/annotations;annotations";

extend google.protobuf.MethodOptions {
  bool is_control_plane  = 50000;
}

extend google.protobuf.MethodOptions {
  bool is_asynchronous  = 50001;
}

extend google.protobuf.MethodOptions {

  PolicyInfo policy = 50003;
}
