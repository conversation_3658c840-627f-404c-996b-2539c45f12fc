syntax = "proto3";

package sensetime.core.higgs.common.v1;
option go_package = "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1;common";

import "google/protobuf/timestamp.proto";

// [zh] 订单信息.
// [en] Order Infomation.
message OrderInfo {

    // [zh] 购买时长.
    // [en] length of purchase.
    int32 billing_cycle_number = 1;

    // [zh] 自动续费.
    // [en] Automatic renewal.
    bool auto_renew = 2;

    // [zh] 货币代码.
    // [en] currency code.
    string currency_code = 3;

    // [zh] 支付方式.
    // [en] payment method.
    enum PaymentChannel {
        // [zh] 未指定.
        // [en] unspecified.
        PC_UNSPECIFIED = 0;

        // [zh] 余额支付.
        // [en] balance payment.
        PC_BALANCE = 1;

        // [zh] 信用支付.
        // [en] credit payment.
        PC_CREDIT = 2;

        // [zh] 冻结款支付.
        // [en] Block payments.
        PC_FROZENACCOUNT =3;

        // [zh] 线下汇款.
        // [en] Online remittance.
        PC_REMIT_OFFLINE = 4;

        // [zh] 支付宝支付.
        // [en] pay by AliPay.
        PC_ALIPAY = 5;

        // [zh] 银联支付.
        // [en] UnionPay payment.
        PC_UNIONPAY = 6;

        // [zh] 在线转账.
        // [en] Transfer money online.
        PC_REMIT_ONLINE = 7;

        // [zh] 合同包支付.
        // [en] Contract package payment.
        PC_CONTRACT = 8;
    };

    // [zh] 支付方式.
    // [en] payment method.
    PaymentChannel payment_channel = 4;

    // [zh] 订单备注.
    // [en] order notes
    string note = 5;

    // [zh] 订单类型.
    // [en] Order Type
    enum OrderType {
        // [zh] 未指定.
        // [en] unspecified.
        OT_UNSPECIFIED = 0;

        // [zh] 原始订单.
        // [en] original order.
        OT_ORIGINAL = 1;

        // [zh] 续费订单.
        // [en] Renew order.
        OT_RENEW = 2;

        // [zh] 升配订单.
        // [en] Upgrade order.
        OT_UPGRADED = 3;

        // [zh] 降配订单.
        // [en] downgrade order.
        OT_DOWNGRADED = 4;

        // [zh] 合同包订单.
        // [en] Contract package order.
        OT_CONTRACT = 5;
    }

    // [zh] 订单类型.
    // [en] Order Type.
    OrderType order_type = 6;

    // [zh] 订单id.
    // [en] order id.
    string order_id = 7;

    // [zh] 订单生效日期.
    // [en] Order effective date.
    google.protobuf.Timestamp start_time = 8;

    // [zh] 付费类型.
    // [en] payment type.
    enum PaymentModel {
        // [zh] 未指定.
        // [en] unspecified.
        PM_UNSPECIFIED = 0;
        // [zh] 预付.
        // [en] prepaid.
        PM_PREPAID = 1;
        // [zh] 后付费.
        // [en] postpaid
        PM_POSTPAID = 2;
    }

    // [zh] 付费类型.
    // [en] payment type.
    PaymentModel payment_model = 9;

    // [zh] 计费类型.
    // [en] billing type.
    enum BillingModel {
        // [zh] 未指定.
        // [en] unspecified
        BM_UNSPECIFIED =0;
        // [zh] 按量计费.
        // [en] Pay as you go.
        BM_USAGE = 1;
        // [zh] 包月.
        // [en] monthly subscription.
        BM_MONTHLY = 2;
        // [zh] 包年.
        // [en] annually.
        BM_YEARLY = 3;
    }

    // [zh] 计费类型.
    // [en] billing type.
    BillingModel billing_model = 10;

    // [zh] 合同包ID.
    // [en] Contract package ID. original id-- OT_ORIGINAL: original spu_id/package_id; OT_RENEW/OT_UPGRADED/OT_DOWNGRADED: original/ order_id; OT_CONTRACT: original contract_id
    string original_id = 11;

    // [zh] 订单结束时间.
    // [en] Order end time.
    google.protobuf.Timestamp end_time = 12;
}