package aip

import (
	"strconv"
	"strings"

	"go.einride.tech/aip/filtering"
	"go.einride.tech/spanner-aip/spanfiltering"
)

func ParseFilter(request filtering.Request, declarationOptions []filtering.DeclarationOption, fieldMap map[string]string) (c string, p map[string]interface{}, err error) {
	declarations, err := filtering.NewDeclarations(declarationOptions...)
	if err != nil {
		return "", nil, err
	}
	filter, err := filtering.ParseFilter(request, declarations)
	if err != nil {
		return "", nil, err
	}
	spanSQL, params, err := spanfiltering.TranspileFilter(filter)
	if err != nil {
		return "", nil, err
	}
	condition := strings.ReplaceAll(spanSQL.SQL(), "@", ":")
	for k, v := range fieldMap {
		index1 := strings.Index(condition, " "+k+" ")
		index2 := strings.Index(condition, " "+k+")")
		if index1 >= 0 || index2 >= 0 {
			condition = strings.ReplaceAll(condition, " "+k, " "+v)
			continue
		}
		index1 = strings.Index(condition, "("+k+" ")
		index2 = strings.Index(condition, "("+k+")")
		if index1 >= 0 || index2 >= 0 {
			condition = strings.ReplaceAll(condition, "("+k, "("+v)
			continue
		}
		// 在开头
		index1 = strings.Index(condition, k+" ")
		if index1 == 0 || len(condition) == len(k) {
			condition = strings.Replace(condition, k, v, 1)
			continue
		}
	}
	// fuzzy search
	for k, v := range params {
		if val, ok := v.(string); ok {
			if strings.HasPrefix(val, "*") || strings.HasSuffix(val, "*") {
				condition = strings.ReplaceAll(condition, "= :"+k, "like :"+k)
				params[k] = strings.ReplaceAll(val, "*", "%")
			}
		}
	}
	return condition, params, nil
}

func ParseFilterPlaceholder(request filtering.Request, declarationOptions []filtering.DeclarationOption, fieldMap map[string]string) (c string, paramsSlice []interface{}, err error) {
	condition, params, err := ParseFilter(request, declarationOptions, fieldMap)
	if err != nil {
		return "", nil, err
	}

	paramsCount := len(params)

	paramsSlice = make([]interface{}, paramsCount)

	for i := 0; i < paramsCount; i++ {
		key := "param_" + strconv.Itoa(i)
		condition = strings.ReplaceAll(condition, ":"+key, "?")
		paramsSlice[i] = params[key]
	}

	return condition, paramsSlice, nil
}
