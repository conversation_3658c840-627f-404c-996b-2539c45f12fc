package aip

import (
	"strings"

	"go.einride.tech/aip/ordering"
)

func ParseOrderBy(request ordering.Request, fieldMap map[string]string) (string, error) {
	if request.GetOrderBy() != "" {
		actual, err := ordering.ParseOrderBy(request)
		if err != nil {
			return "", err
		}
		orderBy := " order by "
		for i := range actual.Fields {
			path := actual.Fields[i].Path
			if val, ok := fieldMap[path]; ok {
				path = val
			}
			field := ""
			if strings.Contains(path, ".") {
				field = path
			} else {
				field = "\"" + path + "\""
			}
			desc := actual.Fields[i].Desc
			if desc {
				if i == len(actual.Fields)-1 {
					orderBy = orderBy + field + " desc"
				} else {
					orderBy = orderBy + field + " desc, "
				}
			} else {
				if i == len(actual.Fields)-1 {
					orderBy += field
				} else {
					orderBy = orderBy + field + ", "
				}
			}
		}
		return orderBy, nil
	}
	return "", nil
}
