package aip

import (
	"errors"
	"fmt"
	"strconv"

	"go.einride.tech/aip/pagination"
	"go.uber.org/zap"
)

const (
	maxPageSize     = 1000
	defaultPageSize = 50
)

func ParsePageTokenAndPageSize(request pagination.Request) (pageToken int, pageSize int32, err error) {
	pageSize = request.GetPageSize()
	switch {
	case pageSize < 0:
		return 0, 0, errors.New("page size is negative")
	case pageSize == 0:
		pageSize = defaultPageSize
	case pageSize > maxPageSize:
		pageSize = maxPageSize
	}
	if request.GetPageToken() == "" {
		return 1, pageSize, nil
	}
	pageToken, err = strconv.Atoi(request.GetPageToken())
	if err != nil {
		return 0, 0, err
	}
	if pageToken <= 0 {
		return 0, 0, errors.New("page token must start with 1")
	}
	return pageToken, pageSize, nil
}

func CalcNextPageTokenNumber(totalSize int, localSize int, request pagination.Request) (int, error) {
	pageToken, pageSize, err := ParsePageTokenAndPageSize(request)
	if err != nil {
		reason := fmt.Sprintf("invalid parameters to failed to ParsePageTokenAndPageSize %v , err: %v", zap.Any("request", request), zap.Error(err))
		return 0, errors.New(reason)
	}

	currSize := pageToken * int(pageSize)
	if currSize >= totalSize {
		return 0, nil
	}

	return pageToken + 1, nil
}

func CalcNextPageToken(totalSize int, localSize int, request pagination.Request) (string, error) {
	number, err := CalcNextPageTokenNumber(totalSize, localSize, request)
	if err != nil {
		return "", err
	}
	return strconv.Itoa(number), nil
}
