package utils

import (
	"bytes"
	"fmt"
	"os"
	"os/signal"
	"path/filepath"
	"syscall"
	"time"

	"github.com/sirupsen/logrus"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
)

// MyFormatter 日志格式化
type MyFormatter struct{}

// Format 日志格式化规范
func (m *MyFormatter) Format(entry *logrus.Entry) ([]byte, error) {
	var b *bytes.Buffer
	if entry.Buffer != nil {
		b = entry.Buffer
	} else {
		b = &bytes.Buffer{}
	}

	// 设置日志时间输出格式，并精确到纳秒
	// 2006-01-02T15:04:05.999999999Z07:00
	timestamp := entry.Time.Format(time.RFC3339Nano)
	var newLog string

	//HasCaller()为true才会有调用信息
	if entry.HasCaller() {
		fName := filepath.Base(filepath.Dir(entry.Caller.File)) + "/" + filepath.Base(entry.Caller.File)
		newLog = fmt.Sprintf("[%s] [%s] [%s:%d] ",
			timestamp, entry.Level, fName, entry.Caller.Line)
	} else {
		newLog = fmt.Sprintf("[%s] [%s] ", timestamp, entry.Level)
	}

	b.WriteString(newLog)
	//support the Fields
	for k, v := range entry.Data {
		stringVal, ok := v.(string)
		if !ok {
			stringVal = fmt.Sprint(v)
		}
		b.WriteString(fmt.Sprintf("[%s=%s] ", k, stringVal))
	}

	b.WriteString(entry.Message)
	b.WriteByte('\n')
	return b.Bytes(), nil
}

// LogrusInit 日志初始化
func LogrusInit() {
	logrus.SetReportCaller(true)
	logrus.SetFormatter(&MyFormatter{})
}

// InitAll 服务初始化
func InitAll() {

	LogrusInit()
	errors.NewBundle()
	logrus.Infof("Initialize Resource Provider")
}

// NewStopChannel 关闭 channel
func NewStopChannel() <-chan struct{} {
	stopCh := make(chan struct{})

	// Stop on shutdown signal:
	// Must be a buffered channel, otherwise the shutdown signal may be lost.
	shutdownCh := make(chan os.Signal, 1)
	signal.Notify(shutdownCh, syscall.SIGINT, syscall.SIGTERM)
	go func() {
		s := <-shutdownCh
		logrus.Warningf("Received shutdown signal: %v", s)
		close(stopCh)
	}()

	return stopCh
}
