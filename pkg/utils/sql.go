package utils

import (
	"fmt"
	"strings"
)

// FormatCondition format google aip sql condition & params to xorm condition
// condition: ((internal_instance_type = :param_0) AND (internal_instance_name = :param_1)) params: map[param_0:AILIB param_1:instance1]
// result: ((internal_instance_type = 'AILIB') AND (internal_instance_name = 'instance1'))
func FormatCondition(condition string, params map[string]interface{}) string {
	if len(condition) == 0 {
		return ""
	}
	for k, v := range params {
		pk := fmt.Sprintf(":%s", k)
		pv := ""
		switch v.(type) {
		case string:
			pv = fmt.Sprintf("'%s'", v)
		default:
			pv = fmt.Sprintf("%v", v)
		}
		condition = strings.ReplaceAll(condition, pk, pv)
	}

	return condition
}
