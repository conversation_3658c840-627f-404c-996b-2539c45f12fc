package utils

import (
	kubeovnclientset "github.com/kubeovn/kube-ovn/pkg/client/clientset/versioned"
	networkclientset "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/flowcontrol"
)

var (
	// MasterURL k8s master 地址
	MasterURL string
	// Kubeconfig kubeconfig 路径
	Kubeconfig string
)

// GetRestConfig 根据配置返回集群内外的 RestConfig
func GetRestConfig() (*rest.Config, error) {
	return clientcmd.BuildConfigFromFlags(MasterURL, Kubeconfig)
}

func AddRateLimit(c *rest.Config) {
	c.RateLimiter = flowcontrol.NewTokenBucketRateLimiter(20, 100)
}

// GetClientSet 返回 k8s client
func GetClientSet() (*kubernetes.Clientset, error) {
	cfg, err := GetRestConfig()
	if err != nil {
		return nil, err
	}

	return kubernetes.NewForConfig(cfg)
}

func GetKubeovnClientSet() (*kubeovnclientset.Clientset, error) {
	cfg, err := GetRestConfig()
	if err != nil {
		return nil, err
	}

	return kubeovnclientset.NewForConfig(cfg)
}

func GetNetworkClientSet() (*networkclientset.Clientset, error) {
	cfg, err := GetRestConfig()
	if err != nil {
		return nil, err
	}

	return networkclientset.NewForConfig(cfg)
}
