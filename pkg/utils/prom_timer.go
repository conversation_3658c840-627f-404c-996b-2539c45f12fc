package utils

import (
	"time"

	"github.com/prometheus/client_golang/prometheus"
	log "github.com/sirupsen/logrus"
)

var Objectives = map[float64]float64{0.5: 0.05, 0.9: 0.01, 0.99: 0.001, 1.0: 0.0}

// Timer is a helper type to time functions. Use NewTimer to create new
// instances.
type Timer struct {
	begin    time.Time
	observer prometheus.Observer
}

// NewTimer creates a new Timer. The provided Observer is used to observe a
// duration in seconds. Timer is usually used to time a function call in the
// following way:
//
//	func TimeMe() {
//	    timer := NewTimer(myHistogram)
//	    defer timer.ObserveDuration()
//	    // Do actual work.
//	}
func NewTimer(o prometheus.Observer) *Timer {
	return &Timer{
		begin:    time.Now(),
		observer: o,
	}
}

// ObserveDuration records the duration passed since the Timer was created with
// NewTimer. It calls the Observe method of the Observer provided during
// construction with theclean duration in seconds as an argument. ObserveDuration is
// usually called with a defer statement.
//
// Note that this method is only guaranteed to never observe negative durations
// if used with Go1.9+.
// Added yft: this func will return duration
func (t *Timer) ObserveDuration() time.Duration {
	duration := time.Since(t.begin)
	if t.observer != nil {
		t.observer.Observe(duration.Seconds())
	}
	return duration
}

// ObserveDurationAndLog will do prometheus observe and when actual time larger than expected,
// log a warning message.
// expectedTime is in milliseconds, int64
func (t *Timer) ObserveDurationAndLog(expectedTime time.Duration, stage string, extraInfo ...string) {
	actualTime := t.ObserveDuration()
	metric, ok := t.observer.(prometheus.Metric)
	if !ok {
		log.Fatalf("observer can not transfer to Metric, type of observer: %T", t.observer)
	}
	// if expectedTime=0, disable slow query log.
	if expectedTime != 0 {
		if actualTime > 2*expectedTime {
			log.Errorf("[EXTREMELY SLOW OPS] metric: %v, in stage: %v, expected time: %v, actual time: %v, extra infos: %q",
				metric.Desc().String(), stage, expectedTime, actualTime, extraInfo)
		} else if actualTime > expectedTime {
			log.Warnf("[SLOW OPS] metric: %v, in stage: %v, expected time: %v, actual time: %v, extra infos: %q",
				metric.Desc().String(), stage, expectedTime, actualTime, extraInfo)
		}
	}
}

func SlowOpsLog(expectedTime int64, actualTime int64, metricsDesc string) {
	// if expectedTime=0, disable slow query log.
	if expectedTime != 0 {
		if actualTime > 2*expectedTime {
			log.Errorf("[EXTREMELY SLOW OPS] metric: %v, expected time: %v, actual time: %v",
				metricsDesc, expectedTime, actualTime)
		} else if actualTime > expectedTime {
			log.Warnf("[SLOW OPS] metric: %v, expected time: %v, actual time: %v",
				metricsDesc, expectedTime, actualTime)
		}
	}
}
