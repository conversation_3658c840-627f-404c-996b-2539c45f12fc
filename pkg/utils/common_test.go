package utils

import (
	"testing"
)

func TestCalcTheLastIPFromCidr(t *testing.T) {
	type args struct {
		cidr string
	}
	tests := []struct {
		name    string
		args    args
		want    string
		wantErr bool
	}{
		// 掩码控制的IP地址bit为0.
		{"10.0.0.0/8", args{"10.0.0.0/8"}, "**************", false},
		{"10.0.0.0/9", args{"10.0.0.0/9"}, "**************", false},
		{"10.0.0.0/10", args{"10.0.0.0/10"}, "*************", false},
		{"10.0.0.0/11", args{"10.0.0.0/11"}, "*************", false},
		{"10.0.0.0/12", args{"10.0.0.0/12"}, "*************", false},
		{"10.0.0.0/13", args{"10.0.0.0/13"}, "************", false},
		{"10.0.0.0/14", args{"10.0.0.0/14"}, "************", false},
		{"10.0.0.0/15", args{"10.0.0.0/15"}, "************", false},
		{"10.0.0.0/16", args{"10.0.0.0/16"}, "************", false},
		{"10.0.0.0/17", args{"10.0.0.0/17"}, "************", false},
		{"10.0.0.0/18", args{"10.0.0.0/18"}, "***********", false},
		{"10.0.0.0/19", args{"10.0.0.0/19"}, "***********", false},
		{"10.0.0.0/20", args{"10.0.0.0/20"}, "***********", false},
		{"10.0.0.0/21", args{"10.0.0.0/21"}, "**********", false},
		{"10.0.0.0/22", args{"10.0.0.0/22"}, "**********", false},
		{"10.0.0.0/23", args{"10.0.0.0/23"}, "10.0.1.254", false},
		{"10.0.0.0/24", args{"10.0.0.0/24"}, "**********", false},
		{"10.0.0.0/25", args{"10.0.0.0/25"}, "**********", false},
		{"10.0.0.0/26", args{"10.0.0.0/26"}, "*********", false},
		{"10.0.0.0/27", args{"10.0.0.0/27"}, "*********", false},
		{"10.0.0.0/28", args{"10.0.0.0/28"}, "*********", false},
		{"10.0.0.0/29", args{"10.0.0.0/29"}, "********", false},
		{"10.0.0.0/30", args{"10.0.0.0/30"}, "********", false},
		{"10.0.0.0/31", args{"10.0.0.0/31"}, "", true},
		{"10.0.0.0/32", args{"10.0.0.0/32"}, "", true},
		// 掩码控制的IP地址bit为1.
		{"**************/8", args{"**************/8"}, "**************", false},
		{"**************/9", args{"**************/9"}, "**************", false},
		{"**************/10", args{"**************/10"}, "**************", false},
		{"**************/11", args{"**************/11"}, "**************", false},
		{"**************/12", args{"**************/12"}, "**************", false},
		{"**************/13", args{"**************/13"}, "**************", false},
		{"**************/14", args{"**************/14"}, "**************", false},
		{"**************/15", args{"**************/15"}, "**************", false},
		{"**************/16", args{"**************/16"}, "**************", false},
		{"**************/17", args{"**************/17"}, "**************", false},
		{"**************/18", args{"**************/18"}, "**************", false},
		{"**************/19", args{"**************/19"}, "**************", false},
		{"**************/20", args{"**************/20"}, "**************", false},
		{"**************/21", args{"**************/21"}, "**************", false},
		{"**************/22", args{"**************/22"}, "**************", false},
		{"**************/23", args{"**************/23"}, "**************", false},
		{"**************/24", args{"**************/24"}, "**************", false},
		{"**************/25", args{"**************/25"}, "**************", false},
		{"**************/26", args{"**************/26"}, "**************", false},
		{"**************/27", args{"**************/27"}, "**************", false},
		{"**************/28", args{"**************/28"}, "**************", false},
		{"**************/29", args{"**************/29"}, "**************", false},
		{"**************/30", args{"**************/30"}, "**************", false},
		{"**************/31", args{"**************/31"}, "", true},
		{"**************/32", args{"**************/32"}, "", true},
	}
	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			got, err := CalcTheLastXIPFromCidr(tt.args.cidr, 1)
			if (err != nil) != tt.wantErr {
				t.Errorf("GetTheLastAddrFromCIDR() error = %v, wantErr %v", err, tt.wantErr)
				return
			}
			if got != tt.want {
				t.Errorf("GetTheLastAddrFromCIDR() = %v, want %v", got, tt.want)
			}
		})
	}
}

func TestGenVpcDGWResourceID(t *testing.T) {
	sub := "8b6dce5e-8f53-4c6e-8e29-5092c4f5a51a"
	resourceGroup := "default"
	zone := "cn-sh-01z"
	vpcName := "vpc-test"
	dgwName := "dgw-test"
	dgwID := GenVpcDGWResourceID(sub, resourceGroup, zone, vpcName, dgwName)
	expireDGWID := "/subscriptions/8b6dce5e-8f53-4c6e-8e29-5092c4f5a51a/resourceGroups/default/zones/cn-sh-01z/vpcs/vpc-test/dgw/dgw-test"
	if dgwID != expireDGWID {
		t.Errorf("expired: %s, real: %s, not valid", expireDGWID, dgwID)
	}
}
