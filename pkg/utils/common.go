package utils

import (
	"hash/fnv"
	"net"
	"os"
	"reflect"

	uuid "github.com/satori/go.uuid"
	"github.com/sirupsen/logrus"

	"fmt"
	"strings"
	"time"
)

const (
	EXTERNAL_IP_PER_HANATGW = 3 // 目前一个hanatgw消耗3个external ip，1vip，2rip。
)

// GetEnv 获取环境变量
func GetEnv(key, fallback string) string {
	if value, ok := os.LookupEnv(key); ok {
		return value
	}
	return fallback
}

// UUIDGen 生产 UUID
func UUIDGen() string {
	u, _ := uuid.NewV4()
	return u.String()
}

// Rand8 生产8位随机串
func Rand8() string {
	return UUIDGen()[:8]
}

// EmptyToBackup 空值替换
func EmptyToBackup(src, backup string) string {
	if src == "" {
		return backup
	}
	return src
}

func EmptyToBackupInt32(src, backup int32) int32 {
	if src == 0 {
		return backup
	}
	return src
}

func GetInitRunStatus(enable bool, enableRunStatus string) string {
	if enable {
		return enableRunStatus
	}
	return ""
}

// GetFirstxAddrsFromCIDR 返回前 x 个 cidr 段的 ips
func GetFirstxAddrsFromCIDR(x int, cidr string) (string, error) {
	_, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return "", err
	}

	ip := ipnet.IP

	ip[len(ip)-1] = 1

	res := ip.String()

	ip[len(ip)-1] = byte(x)

	return res + ".." + ip.String(), nil
}

// GetLastxAddrsFromCIDR 返回最后 x 个 cidr 段的 ips
func GetLastxAddrsFromCIDR(x int, cidr string) (string, error) {
	_, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return "", err
	}

	ip := net.IP{}
	ip = append(ip, ipnet.IP...)

	ip[len(ip)-1] = 1

	i := ip[len(ip)-2]
	for ; ; i++ {
		ip[len(ip)-2] = i + 1
		if !ipnet.Contains(ip) {
			break
		}
	}
	ip[len(ip)-2] = i

	i = ip[len(ip)-1]
	for ; ; i++ {
		ip[len(ip)-1] = i + 1
		if !ipnet.Contains(ip) || i == 254 {
			break
		}
	}

	ip[len(ip)-1] = i

	res := ip.String()

	ip[len(ip)-1] = byte(int(i) - x + 1)

	return ip.String() + ".." + res, nil
}

// GetTheFirstAddrFromCIDR 返回第一个可用 ip
func GetTheFirstAddrFromCIDR(cidr string) (string, error) {
	_, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return "", err
	}

	ip := ipnet.IP

	ip[len(ip)-1] = 1

	return ip.String(), nil
}

// GetTheLastAddrFromCIDR 返回最后一个可用 ip
func GetTheLastAddrFromCIDR(cidr string) (string, error) {
	return CalcTheLastXIPFromCidr(cidr, 1)
}

// CalcTheLastIPFromCidr 返回倒数第x个可用 ip
func CalcTheLastXIPFromCidr(cidr string, x byte) (string, error) {
	_, ipNet, err := net.ParseCIDR(cidr)
	if err != nil {
		return "", err
	}

	byteIp := []byte(ipNet.IP)                // []byte representation of IP
	byteMask := []byte(ipNet.Mask)            // []byte representation of mask
	byteTargetIp := make([]byte, len(byteIp)) // []byte holding target IP
	for k := range byteIp {
		// mask will give us all fixed bits of the subnet (for the given byte)
		// inverted mask will give us all moving bits of the subnet (for the given byte)
		invertedMask := byteMask[k] ^ 0xff // inverted mask byte
		if k == 3 && invertedMask <= 2 {
			// 而当广播地址为.1或.0的时候，说明子网太小，没有空闲IP可用
			return "", fmt.Errorf("No enough IP address for cidr: %v", cidr)
		}

		// broadcastIP = networkIP added to the inverted mask
		byteTargetIp[k] = byteIp[k]&byteMask[k] | invertedMask
	}

	// 最后一个可用IP是，广播地址前的一个IP地址
	byteTargetIp[3] -= x
	return net.IP(byteTargetIp).String(), nil
}

// CheckSubCIDR 判断 subcidr 是否为 cidr 的子集
func CheckSubCIDR(cidr, subcidr string) bool {
	_, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return false
	}

	firstIP, err := GetTheFirstAddrFromCIDR(subcidr)
	if err != nil || !ipnet.Contains(net.ParseIP(firstIP)) {
		return false
	}

	lastIP, err := GetTheLastAddrFromCIDR(subcidr)
	if err != nil || !ipnet.Contains(net.ParseIP(lastIP)) {
		return false
	}

	return true
}

// MergeIPToCIDR 返回 ip 对应的 cidr
func MergeIPToCIDR(ip, cidr string) (string, error) {
	_, ipnet, err := net.ParseCIDR(cidr)
	if err != nil {
		return "", err
	}

	ones, _ := ipnet.Mask.Size()
	return fmt.Sprintf("%v/%v", ip, ones), nil
}

func GenRMID(subscriptionName, resourceGroupName, zone, resourceType, resourceName string) string {
	return fmt.Sprintf("/subscriptions/%v/resourceGroups/%v/zones/%v/%v/%v", subscriptionName, resourceGroupName, zone, resourceType, resourceName)
}

func Substring(s string, startIndex int, count int) string {
	maxCount := len(s) - startIndex
	if count > maxCount {
		count = maxCount
	}
	return s[startIndex:count]
}

func NameToTenantCode(name string) string {
	index := strings.Index(name, "-")
	lastIndex := strings.LastIndex(name, "-")
	if lastIndex <= 0 || index == lastIndex {
		return name
	}
	return name[index+1 : lastIndex]
}

func TimeFormat(myTime time.Time) string {
	return myTime.Format("2006-01-02T15:04:05Z")
}

func In(target string, str_array []string) bool {
	for _, element := range str_array {
		if target == element {
			return true
		}
	}
	return false
}

func HashStringToInt32(s string) int32 {
	h := fnv.New32a()
	h.Write([]byte(s))
	return int32(h.Sum32())
}

func TimeNow(msgs ...string) {
	currentTime := time.Now()
	logrus.Infof("current Time is %s:%s", currentTime.Format("2006-01-02 15:04:05.000000000"), strings.Join(msgs, " "))
}

func ContainString(ss []string, target string) bool {
	for _, s := range ss {
		if s == target {
			return true
		}
	}
	return false
}

func ToMapSetE(i interface{}) (map[interface{}]struct{}, error) {
	// judge the validation of the input
	if i == nil {
		return nil, fmt.Errorf("unable to converts %#v of type %T to map[interface{}]struct{}", i, i)
	}
	kind := reflect.TypeOf(i).Kind()
	if kind != reflect.Slice && kind != reflect.Array {
		return nil, fmt.Errorf("the input %#v of type %T isn't a slice or array", i, i)
	}

	// execute the convert
	v := reflect.ValueOf(i)
	m := make(map[interface{}]struct{}, v.Len())
	for j := 0; j < v.Len(); j++ {
		m[v.Index(j).Interface()] = struct{}{}
	}
	return m, nil
}

func GenResourcePrefix(subscription, resourceGroup, zone string) string {
	return fmt.Sprintf("/subscriptions/%s/resourceGroups/%s/zones/%s", subscription, resourceGroup, zone)
}

func GenVpcResourceID(subscription, resourceGroup, zone string, resourceName string) string {
	return fmt.Sprintf("%s/vpcs/%s", GenResourcePrefix(subscription, resourceGroup, zone), resourceName)
}

func GenEipResourceID(subscription, resourceGroup, zone string, resourceName string) string {
	return fmt.Sprintf("%s/eips/%s", GenResourcePrefix(subscription, resourceGroup, zone), resourceName)
}

func GenVpcAclREsourceID(subscription, resourceGroup, zone string, vpcName, aclName string) string {
	return fmt.Sprintf("%s/vpcs/%s/acls/%s", GenResourcePrefix(subscription, resourceGroup, zone), vpcName, aclName)
}

func GenVpcDGWResourceID(subscription, resourceGroup, zone string, vpcName, dgwName string) string {
	return fmt.Sprintf("%s/dgw/%s", GenVpcResourceID(subscription, resourceGroup, zone, vpcName), dgwName)
}

func GenEipNicName(eipName string) (string, error) {
	lastDashIndex := strings.LastIndex(eipName, "-")

	if lastDashIndex != -1 && lastDashIndex+1 < len(eipName) {
		eipNicSuffix := eipName[lastDashIndex+1:]
		return fmt.Sprintf("eip%s", eipNicSuffix), nil
	}

	return "", fmt.Errorf("eip name=%s, last index of '-' not valid", eipName)
}

func IsIn[T comparable](e T, s []T) bool {
	for _, v := range s {
		if v == e {
			return true
		}
	}

	return false
}

func GenAclNameForInternalEip(eipName string) string {
	return strings.Replace(eipName, "eip", "acl", 1)
}

func ConvertIPWithCIDR(ip, cidr string) (string, error) {
	_, ipNet, err := net.ParseCIDR(cidr)
	if err != nil {
		return "", fmt.Errorf("net parse cidr=%s failed, err: %v", cidr, err)
	}

	maskSize, _ := ipNet.Mask.Size()
	return fmt.Sprintf("%s/%d", ip, maskSize), nil
}
