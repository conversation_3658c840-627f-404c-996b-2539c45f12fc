package db

import "time"

// Dchcs 表
type Dchcs struct {
	DchcID   string `json:"dchc_id" xorm:"varchar(64) pk 'dchc_id'"`
	Name     string `json:"name" xorm:"varchar(64) index notnull 'name'"`
	DcvcName string `json:"dcvc_name" xorm:"varchar(64) notnull 'dcvc_name'"`

	Enable   bool   `json:"enable" xorm:"bool default false 'enable'"`
	TargetIp string `json:"remote_target_ip" xorm:"varchar(64) 'remote_target_ip'"` // remote IDC target ip
	Interval int32  `json:"interval" xorm:"int default 2 'interval'"`
	Counter  int32  `json:"counter" xorm:"int default 5 'counter'"`
	HcType   string `json:"hc_type" xorm:"varchar(64) default 'icmp' 'hc_type'"` // default icmp
	HcInfo   string `json:"hc_info" xorm:"varchar(128)  'hc_info'"`

	RunStatus string    `json:"run_status" xorm:"varchar(64) 'run_status'"`
	RunTime   time.Time `json:"run_time" xorm:"timestamp 'run_time'"`

	Zone       string    `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	CreateTime time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted    bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags       string    `json:"tags" xorm:"text 'tags'"`
}
