package db

import "time"

// Ipas ipas表
type Ipas struct {
	IpaID        string    `json:"ipa_id" xorm:"varchar(64) pk 'ipa_id'"`
	DisplayName  string    `json:"display_name" xorm:"varchar(64) notnull 'display_name'"`
	SubnetID     string    `json:"subnet_id" xorm:"varchar(64) notnull 'subnet_id'"`
	Protocol     string    `json:"protocol" xorm:"varchar(10) notnull 'protocol'"`
	IP           string    `json:"ip" xorm:"varchar(64) notnull 'ip'"`
	MAC          string    `json:"mac" xorm:"varchar(256) notnull 'mac'"`
	GatewayIP    string    `json:"gateway_ip" xorm:"varchar(64) notnull 'gateway_ip'"`
	CIDR         string    `json:"cidr" xorm:"varchar(32) notnull 'cidr'"`
	MTU          int       `json:"mtu" xorm:"varchar(64) notnull 'mtu'"`
	IPMI         string    `json:"ipmi" xorm:"varchar(64) 'ipmi'"`
	ID           string    `json:"id" xorm:"varchar(1024) notnull 'id'"`
	Description  string    `json:"description" xorm:"varchar(256) default '' 'description'"`
	Name         string    `json:"name" xorm:"varchar(64) notnull 'name'"`
	ResourceType string    `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`
	Zone         string    `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State        string    `json:"state" xorm:"varchar(64) notnull 'state'"`
	CreatorID    string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID      string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID     string    `json:"tenant_id" xorm:"varchar(64) notnull 'tenant_id'"`
	CreateTime   time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime   time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted      bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags         string    `json:"tags" xorm:"text 'tags'"`
	Mode         string    `json:"mode" xorm:"varchar(32) default 'enable' 'mode'"`
}
