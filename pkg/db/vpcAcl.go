package db

import (
	"time"
)

// VPC ACL 表
type VpcAcls struct {
	VpcAclID     string    `json:"vpc_acl_id" xorm:"varchar(64) pk 'vpc_acl_id'"`
	DisplayName  string    `json:"display_name" xorm:"varchar(64) 'display_name'"`
	Description  string    `json:"description" xorm:"varchar(256) 'description'"`
	Action       string    `json:"action" xorm:"varchar(6) 'action'"`
	Src          string    `json:"src" xorm:"varchar(1024) 'src'"`
	Dest         string    `json:"dest" xorm:"varchar(1024) 'dest'"`
	SrcPort      string    `json:"src_port" xorm:"varchar(128) 'src_port'"`
	DestPort     string    `json:"dest_port" xorm:"varchar(128) 'dest_port'"`
	Protocol     string    `json:"protocol" xorm:"varchar(16) 'protocol'"`
	Priority     int32     `json:"priority" xorm:"int notnull unique(priority) 'priority'"`
	Type         string    `json:"type" xorm:"varchar(64) notnull unique(priority) 'type'"`
	VPCID        string    `json:"vpc_id" xorm:"varchar(64) notnull unique(priority) 'vpc_id'"`
	ID           string    `json:"id" xorm:"varchar(1024) notnull 'id'"`
	Name         string    `json:"name" xorm:"varchar(64) notnull unique 'name'"`
	ResourceType string    `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`
	Zone         string    `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State        string    `json:"state" xorm:"varchar(64) notnull 'state'"`
	CreatorID    string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID      string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID     string    `json:"tenant_id" xorm:"varchar(64) notnull 'tenant_id'"`
	CreateTime   time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime   time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted      bool      `json:"deleted" xorm:"bool notnull unique(priority) 'deleted'"`
	Tags         string    `json:"tags" xorm:"text 'tags'"`
	ExtData      string    `json:"ext_data" xorm:"varchar(64) default null 'ext_data'"`
}
