package db

import "time"

// Dcvcs 表
type Dcvcs struct {
	DcvcID       string `json:"dcvc_id" xorm:"varchar(64) pk 'dcvc_id'"`
	DisplayName  string `json:"display_name" xorm:"varchar(64) notnull 'display_name'"`
	Description  string `json:"description" xorm:"varchar(256) 'description'"`
	ID           string `json:"id" xorm:"varchar(1024) notnull 'id'"`
	Name         string `json:"name" xorm:"varchar(64) index notnull 'name'"`
	ResourceType string `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`

	SubscriptionName  string `json:"subscription_name" xorm:"varchar(64) 'subscription_name'"`
	ResourceGroupName string `json:"resource_group_name" xorm:"varchar(64) 'resource_group_name'"`

	VPCID              string `json:"vpc_id" xorm:"varchar(64) index notnull 'vpc_id'"`
	DcplId             string `json:"dcpl_id" xorm:"varchar(64) notnull 'dcpl_id'"`
	DcgwName           string `json:"dcgw_name" xorm:"varchar(64) notnull 'dcgw_name'"`
	RouteType          string `json:"route_type" xorm:"varchar(64) notnull 'route_type'"`
	UpStreamBW         int32  `json:"upstream_bw" xorm:"int 'upstream_bw'"`
	DownStreamBW       int32  `json:"downstream_bw" xorm:"int 'downstream_bw'"`
	VlanId             string `json:"vlan_id" xorm:"varchar(64) notnull 'vlan_id'"`
	CustomerConnectIp  string `json:"customer_connect_ip" xorm:"varchar(64) notnull 'customer_connect_ip'"`
	SenseCoreConnectIp string `json:"sensecore_connect_ip" xorm:"varchar(64) notnull 'sensecore_connect_ip'"`

	VxlanName     string `json:"vxlan_name" xorm:"varchar(64) 'vxlan_name'"`
	VxlanId       string `json:"vxlan_id" xorm:"varchar(64) 'vxlan_id'"`
	LocalVtepIp   string `json:"local_vtep_ip" xorm:"varchar(64) 'local_vtep_ip'"` // internal allocate
	RemoteVtepIp  string `json:"remote_vtep_ip" xorm:"varchar(64) 'remote_vtep_ip'"`
	LocalVxlanIp  string `json:"local_vxlan_ip" xorm:"varchar(64) 'local_vxlan_ip'"`
	RemoteVxlanIp string `json:"remote_vxlan_ip" xorm:"varchar(64) 'remote_vxlan_ip'"` // internal allocate
	LocalCidr     string `json:"local_cidr" xorm:"varchar(64) 'local_cidr'"`
	RemoteCidr    string `json:"remote_cidr" xorm:"varchar(64) 'remote_cidr'"`

	Zone       string    `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State      string    `json:"state" xorm:"varchar(64) notnull 'state'"`
	CreatorID  string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID    string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID   string    `json:"tenant_id" xorm:"varchar(64) index notnull 'tenant_id'"`
	CreateTime time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted    bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags       string    `json:"tags" xorm:"text 'tags'"`
}
