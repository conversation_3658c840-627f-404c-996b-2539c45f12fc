package db

import (
	"time"
)

// SnatRules 表
type SnatRules struct {
	SnatRuleID   string    `json:"snat_rule_id" xorm:"varchar(64) pk 'snat_rule_id'"`
	DisplayName  string    `json:"display_name" xorm:"varchar(64) 'display_name'"`
	Description  string    `json:"description" xorm:"varchar(256) 'description'"`
	NatGatewayID string    `json:"nat_gateway_id" xorm:"varchar(64) 'nat_gateway_id'"`
	Protocol     string    `json:"protocol" xorm:"varchar(64) 'protocol'"`
	OuterIP      string    `json:"outer_ip" xorm:"varchar(64) 'outer_ip'"`
	OuterPortMin string    `json:"outer_port_min" xorm:"int 'outer_port_min'"`
	OuterPortMax string    `json:"outer_port_max" xorm:"int 'outer_port_max'"`
	InnerPortMin string    `json:"inner_port_min" xorm:"int 'inner_port_min'"`
	InnerPortMax string    `json:"inner_port_max" xorm:"int 'inner_port_max'"`
	InnerIP      string    `json:"inner_ip" xorm:"varchar(64) 'inner_ip'"`
	VPCID        string    `json:"vpc_id" xorm:"varchar(64) notnull 'vpc_id'"`
	EIPID        string    `json:"eip_id" xorm:"varchar(64) notnull 'eip_id'"`
	ID           string    `json:"id" xorm:"varchar(1024) notnull 'id'"`
	Name         string    `json:"name" xorm:"varchar(64) notnull 'name'"`
	ResourceType string    `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`
	Zone         string    `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State        string    `json:"state" xorm:"varchar(64) notnull 'state'"`
	CreatorID    string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID      string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID     string    `json:"tenant_id" xorm:"varchar(64) notnull 'tenant_id'"`
	CreateTime   time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime   time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted      bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags         string    `json:"tags" xorm:"text 'tags'"`
	DstCidrs     string    `json:"dst_cidrs" xorm:"varchar(1024) null 'dst_cidrs'"`
}
