package db

import (
	"time"
)

// Subnets 表
type Subnets struct {
	SubnetID     string    `json:"subnet_id" xorm:"varchar(64) pk 'subnet_id'"`
	DisplayName  string    `json:"display_name" xorm:"varchar(64) notnull 'display_name'"`
	CIDR         string    `json:"cidr" xorm:"varchar(64) notnull 'cidr'"`
	Description  string    `json:"description" xorm:"varchar(256) 'description'"`
	IsDefault    bool      `json:"is_default" xorm:"bool notnull 'is_default'"`
	GatewayIP    string    `json:"gateway_ip" xorm:"varchar(64) notnull 'gateway_ip'"`
	VPCID        string    `json:"vpc_id" xorm:"varchar(64) notnull 'vpc_id'"`
	Scope        string    `json:"scope" xorm:"varchar(32) notnull default 'SERVICE' 'scope'"`              // SERVICE|TRAINING|DATA|VPCGW|MANAGE|STORAGE
	Provider     string    `json:"provider" xorm:"varchar(32) notnull default 'OVN' 'provider'"`            // OVN|CONTROLLER
	NetworkType  string    `json:"network_type" xorm:"varchar(32) notnull default 'GENEVE' 'network_type'"` // GENEVE|VXLAN|FLAT|VLAN|IB
	ReservedIPs  string    `json:"reserved_ips" xorm:"text notnull default '' 'reserved_ips'"`
	VNI          int       `json:"vni" xorm:"int 'vni'"`
	CIDRPoolID   string    `json:"cidr_pool_id" xorm:"varchar(64) default '' 'cidr_pool_id'"`
	ID           string    `json:"id" xorm:"varchar(1024) notnull 'id'"`
	Name         string    `json:"name" xorm:"varchar(64) notnull 'name'"`
	ResourceType string    `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`
	Zone         string    `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State        string    `json:"state" xorm:"varchar(64) notnull 'state'"`
	CreatorID    string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID      string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID     string    `json:"tenant_id" xorm:"varchar(64) notnull 'tenant_id'"`
	CreateTime   time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime   time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted      bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags         string    `json:"tags" xorm:"text 'tags'"`
}

// k8s_namespaces 表
type K8sNamespaces struct {
	NamespaceID string `json:"namespace_id" xorm:"varchar(64) pk 'namespace_id'"`
	Name        string `json:"name" xorm:"varchar(64) notnull 'name'"`
	SubnetID    string `json:"subnet_id" xorm:"varchar(64) notnull 'subnet_id'"`
	VPCID       string `json:"vpc_id" xorm:"varchar(64) notnull 'vpc_id'"`
}

// CidrPools cidr_pools表
type CidrPools struct {
	CIDRPoolID  string `json:"cidr_pool_id" xorm:"varchar(64) pk 'cidr_pool_id'"`
	CIDR        string `json:"cidr" xorm:"varchar(64) notnull 'cidr'"`
	GatewayIP   string `json:"gateway_ip" xorm:"varchar(64) notnull 'gateway_ip'"`
	Scope       string `json:"scope" xorm:"varchar(32) index(idx_scope_networktype) 'scope'"`               // SERVICE|TRAINING|DATA|VPCGW|MANAGE|STORAGE|SLB
	NetworkType string `json:"network_type" xorm:"varchar(32) index(idx_scope_networktype) 'network_type'"` // GENEVE|VXLAN|FLAT|VLAN|IB
	VNI         int    `json:"vni" xorm:"int default 0 'vni'"`                                              // vlanID(Vlan), vni(Vxlan) or partitionKey(IB)
	Allocated   bool   `json:"allocated" xorm:"bool 'allocated'"`
	Reserved    string `json:"reserved" xorm:"varchar(256) 'reserved'"`
	Description string `json:"description" xorm:"varchar(256) 'description'"`
	ZonePrp     string `json:"zone_prp" xorm:"varchar(64) notnull 'zone_prp'"`
}
