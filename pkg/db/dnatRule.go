package db

import (
	"time"
)

// DnatRules 表
type DnatRules struct {
	DnatRuleID     string `json:"dnat_rule_id" xorm:"varchar(64) pk 'dnat_rule_id'"`
	DisplayName    string `json:"display_name" xorm:"varchar(64) notnull 'display_name'"`
	Description    string `json:"description" xorm:"varchar(256) 'description'"`
	Protocol       string `json:"protocol" xorm:"varchar(64) notnull 'protocol'"`
	OuterIP        string `json:"outer_ip" xorm:"varchar(64) notnull 'outer_ip'"`
	OuterPortMin   int32  `json:"outer_port_min" xorm:"int 'outer_port_min'"`
	OuterPortMax   int32  `json:"outer_port_max" xorm:"int 'outer_port_max'"`
	InnerPortMin   int32  `json:"inner_port_min" xorm:"int 'inner_port_min'"`
	InnerPortMax   int32  `json:"inner_port_max" xorm:"int 'inner_port_max'"`
	NatGatewayPort int32  `json:"nat_gw_port" xorm:"int 'nat_gw_port'"`
	InternalIP     string `json:"inner_ip" xorm:"varchar(64) 'inner_ip'"`
	NatGatewayID   string `json:"nat_gateway_id" xorm:"varchar(64) notnull 'nat_gateway_id'"`
	EIPID          string `json:"eip_id" xorm:"varchar(64) index notnull 'eip_id'"`
	Priority       int32  `json:"priority" xorm:"int 'priority'"`
	ID             string `json:"id" xorm:"varchar(1024) notnull 'id'"`
	Name           string `json:"name" xorm:"varchar(64) index notnull 'name'"`

	ResourceType string    `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`
	Zone         string    `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State        string    `json:"state" xorm:"varchar(64) notnull 'state'"`
	CreatorID    string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID      string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID     string    `json:"tenant_id" xorm:"varchar(64) notnull 'tenant_id'"`
	CreateTime   time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime   time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted      bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags         string    `json:"tags" xorm:"text 'tags'"`

	// v0.8.0 add items
	InternalInstanceType string `json:"internal_instance_type" xorm:"varchar(64) default 'UNSPECIFIED' 'internal_instance_type'"`
	InternalInstanceName string `json:"internal_instance_name" xorm:"varchar(64) default '' 'internal_instance_name'"`
}
