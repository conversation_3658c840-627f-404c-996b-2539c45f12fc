package db

import (
	"time"
)

// ACL 表
type Acls struct {
	AclID        string    `json:"acl_id" xorm:"varchar(64) pk 'acl_id'"`
	DisplayName  string    `json:"display_name" xorm:"varchar(64) 'display_name'"`
	Description  string    `json:"description" xorm:"varchar(256) 'description'"`
	Action       string    `json:"action" xorm:"varchar(6) 'action'"`
	Src          string    `json:"src" xorm:"text 'src'"`
	Dest         string    `json:"dest" xorm:"varchar(64) 'dest'"`
	DestPort     string    `json:"dest_port" xorm:"varchar(64) 'dest_port'"`
	Protocol     string    `json:"protocol" xorm:"varchar(64) 'protocol'"`
	Priority     int32     `json:"priority" xorm:"int 'priority'"`
	VPCID        string    `json:"vpc_id" xorm:"varchar(64) notnull 'vpc_id'"`
	EIPID        string    `json:"eip_id" xorm:"varchar(64) notnull 'eip_id'"`
	EIPIP        string    `json:"eip_ip" xorm:"varchar(64) notnull 'eip_ip'"`
	ID           string    `json:"id" xorm:"varchar(1024) notnull 'id'"`
	Name         string    `json:"name" xorm:"varchar(64) notnull 'name'"`
	ResourceType string    `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`
	Zone         string    `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State        string    `json:"state" xorm:"varchar(64) notnull 'state'"`
	CreatorID    string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID      string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID     string    `json:"tenant_id" xorm:"varchar(64) notnull 'tenant_id'"`
	CreateTime   time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime   time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted      bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags         string    `json:"tags" xorm:"text 'tags'"`
}
