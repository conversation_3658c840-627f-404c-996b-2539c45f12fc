package db

import "time"

// Dcgws 表
type Dcgws struct {
	DcgwID       string `json:"dcgw_id" xorm:"varchar(64) pk 'dcgw_id'"`
	VPCID        string `json:"vpc_id" xorm:"varchar(64) index notnull 'vpc_id'"`
	DisplayName  string `json:"display_name" xorm:"varchar(64) notnull 'display_name'"`
	Description  string `json:"description" xorm:"varchar(256) 'description'"`
	ID           string `json:"id" xorm:"varchar(1024) notnull 'id'"`
	Name         string `json:"name" xorm:"varchar(64) index notnull 'name'"`
	ResourceType string `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`

	SubscriptionName  string `json:"subscription_name" xorm:"varchar(64) 'subscription_name'"`
	ResourceGroupName string `json:"resource_group_name" xorm:"varchar(64) 'resource_group_name'"`

	HaMode     string `json:"ha_mode" xorm:"varchar(64) default 'master-standby' 'ha_mode'"`
	DeployMode string `json:"deploy_mode" xorm:"varchar(32) default 'nsa' 'deploy_mode'"` // 非独立 Non-Standalone，NSA 和 独立 Standalone，SA

	InternalVip  string `json:"internal_vip" xorm:"varchar(64) 'internal_vip'"`
	MonitorIp    string `json:"monitor_ip" xorm:"varchar(64) 'monitor_ip'"`
	HeartbeatNic string `json:"heartbeat_nic" xorm:"varchar(64) 'heartbeat_nic'"`
	ExternalVip  string `json:"external_vip" xorm:"varchar(64) 'external_vip'"`
	LocalCidr    string `json:"local_cidr" xorm:"varchar(64) 'local_cidr'"`
	RemoteCidr   string `json:"remote_cidr" xorm:"varchar(64) 'remote_cidr'"`

	Zone       string    `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State      string    `json:"state" xorm:"varchar(64) notnull 'state'"`
	CreatorID  string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID    string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID   string    `json:"tenant_id" xorm:"varchar(64) index notnull 'tenant_id'"`
	CreateTime time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted    bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags       string    `json:"tags" xorm:"text 'tags'"`
}
