package db

import (
	"time"
)

// distribute_gateways 表
type DistributeGateways struct {
	DistributeGatewayID string    `json:"distribute_gateway_id" xorm:"varchar(64) pk 'distribute_gateway_id'"`
	DisplayName         string    `json:"display_name" xorm:"varchar(64) notnull 'display_name'"`
	Description         string    `json:"description" xorm:"varchar(256) 'description'"`
	GwExternalIP        string    `json:"gw_external_ip" xorm:"varchar(64) notnull 'gw_external_ip'"`
	InternalIP          string    `json:"internal_ip" xorm:"varchar(64) notnull 'internal_ip'"`
	GwPolicy            string    `json:"gw_policy" xorm:"varchar(1024) notnull 'gw_policy'"`
	VPCID               string    `json:"vpc_id" xorm:"varchar(64) notnull 'vpc_id'"`
	GwExternalCIDR      string    `json:"gw_external_cidr" xorm:"varchar(64) notnull 'gw_external_cidr'"`
	ID                  string    `json:"id" xorm:"varchar(1024) notnull 'id'"`
	Name                string    `json:"name" xorm:"varchar(64) notnull 'name'"`
	ResourceType        string    `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`
	Zone                string    `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State               string    `json:"state" xorm:"varchar(64) notnull 'state'"`
	AdminState          string    `json:"admin_state" xorm:"varchar(64) notnull 'admin_state'"`
	CreatorID           string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID             string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID            string    `json:"tenant_id" xorm:"varchar(64) notnull 'tenant_id'"`
	CreateTime          time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime          time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted             bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags                string    `json:"tags" xorm:"text 'tags'"`
}
