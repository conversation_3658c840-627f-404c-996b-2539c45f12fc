package db

import (
	"time"
)

// NAT_gateways 表
type NatGateways struct {
	NATGatewayID      string    `json:"nat_gateway_id" xorm:"varchar(64) pk 'nat_gateway_id'"`
	DisplayName       string    `json:"display_name" xorm:"varchar(64) notnull 'display_name'"`
	Description       string    `json:"description" xorm:"varchar(256) 'description'"`
	NatGwExternalIPID string    `json:"nat_gw_external_ip_id" xorm:"varchar(64) notnull 'nat_gw_external_ip_id'"`
	InternalIP        string    `json:"internal_ip" xorm:"varchar(64) notnull 'internal_ip'"`
	VPCID             string    `json:"vpc_id" xorm:"varchar(64) notnull 'vpc_id'"`
	SubnetID          string    `json:"subnet_id" xorm:"varchar(64) notnull 'subnet_id'"`
	Type              string    `json:"type" xorm:"varchar(64) notnull 'type'"`
	ID                string    `json:"id" xorm:"varchar(1024) notnull 'id'"`
	Name              string    `json:"name" xorm:"varchar(64) notnull 'name'"`
	ResourceType      string    `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`
	Zone              string    `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State             string    `json:"state" xorm:"varchar(64) notnull 'state'"`
	CreatorID         string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID           string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID          string    `json:"tenant_id" xorm:"varchar(64) notnull 'tenant_id'"`
	CreateTime        time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime        time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted           bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags              string    `json:"tags" xorm:"text 'tags'"`
}

// NAT_Gateway_External_IP_Pools 表
type NatGatewayExternalIpPools struct {
	NatGwExternalIPID string `json:"nat_gw_external_ip_id" xorm:"varchar(64) pk 'nat_gw_external_ip_id'"`
	NatGwExternalIP   string `json:"nat_gw_external_ip" xorm:"varchar(64) notnull 'nat_gw_external_ip'"`
	Description       string `json:"description" xorm:"varchar(256) 'description'"`
	Allocated         bool   `json:"allocated" xorm:"bool notnull 'allocated'"`
	// 如果支持ha nat gw的化，这个字段会存ha nat gw的id，为了兼容和回滚，这里没有更新这里的字段
	NATGatewayID string `json:"nat_gateway_id" xorm:"varchar(64) 'nat_gateway_id'"`
	CIDRID       string `json:"cidr_id" xorm:"varchar(64) notnull 'cidr_id'"`
}

// Cidr_Pools 表
// type CidrPools struct {
// 	CIDRID      string `json:"cidr_id" xorm:"varchar(64) pk 'cidr_id'"`
// 	CIDR        string `json:"cidr" xorm:"varchar(64) notnull 'cidr'"`
// 	Description string `json:"description" xorm:"varchar(256) 'description'"`
// 	GatewayIP   string `json:"gateway_ip" xorm:"varchar(64) notnull 'gateway_ip'"`
// }
