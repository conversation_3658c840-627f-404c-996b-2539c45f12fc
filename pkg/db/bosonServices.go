package db

// BosonServices boson_services 管理 boson-provider 创建的 BosonService 数据
// RefResourceID+ServiceName 作为业务 key
type BosonServices struct {
	ID                int64  `json:"id" xorm:"pk autoincr 'id'"`
	RefResourceID     string `json:"ref_resource_id" xorm:"varchar(128) 'ref_resource_id'"` // 关联此 service 的资源，例如服务器组ID
	ServiceName       string `json:"service_name" xorm:"varchar(256) 'service_name'"`       // CR:BosonService.Name
	EndpointNamespace string `json:"endpoint_namespace" xorm:"varchar(64) 'endpoint_namespace'"`
	EndpointName      string `json:"endpoint_name" xorm:"varchar(64) 'endpoint_name'"`
	EndpointKind      string `json:"endpoint_kind" xorm:"varchar(64) 'endpoint_kind'"`
	EndpointZone      string `json:"endpoint_zone" xorm:"varchar(64) 'endpoint_zone'"`
	Deleted           bool   `json:"deleted" xorm:"bool notnull 'deleted'"`
}

// BosonServiceEndpoints boson_service_endpoints
// ServiceName+Name 作为业务key
type BosonServiceEndpoints struct {
	ID          int64  `json:"id" xorm:"pk autoincr 'id'"`
	ServiceName string `json:"service_name" xorm:"varchar(256) 'service_name'"`
	Name        string `json:"name" xorm:"varchar(64) 'name'"`
	Ip          string `json:"ip" xorm:"varchar(64) 'ip'"`
	Zone        string `json:"zone" xorm:"varchar(64) 'zone'"`
	Deleted     bool   `json:"deleted" xorm:"bool notnull 'deleted'"`
}
