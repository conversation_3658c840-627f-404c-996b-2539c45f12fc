package db

import (
	"reflect"

	"xorm.io/xorm"
)

var (
	AZ     string
	Region string
)

func SetAZ(zone string) {
	AZ = zone
}

func SetRegion(region string) {
	Region = region
}

func (s *SessionWrapper) injectAZ(bean interface{}) {
	switch v := bean.(type) {
	case *Eips:
		v.Zone = AZ
	case *Ipas:
		v.Zone = AZ
	case *DnatRules:
		v.Zone = AZ
	case *SnatRules:
		v.Zone = AZ
	case *Acls:
		v.Zone = AZ
	case *VpcAcls:
		v.Zone = AZ
	case *NatGateways:
		if s.<PERSON>lax {
			_ = s.In("zone", AZ, Region)
		} else {
			v.Zone = Region
		}
	case *HaNatGateways:
		if s.<PERSON>lax {
			_ = s.In("zone", AZ, Region)
		} else {
			v.Zone = Region
		}
	case *DistributeGateways:
		if s.Relax {
			_ = s.In("zone", AZ, Region)
		} else {
			v.Zone = Region
		}
	case *CidrPools:
		v.ZonePrp = AZ
	case *Vpcs:
		if s.<PERSON>lax {
			_ = s.In("zone", AZ, Region)
		} else {
			v.Zone = Region
		}
	case *Subnets:
		if s.Relax {
			_ = s.In("zone", AZ, Region)
		} else {
			v.Zone = Region
		}
	case *Slbs:
		if s.Relax {
			_ = s.In("zone", AZ)
		} else {
			v.Zone = AZ
		}
	case *SlbListeners:
		if s.Relax {
			_ = s.In("zone", AZ)
		} else {
			v.Zone = AZ
		}
	case *SlbTargetGroups:
		if s.Relax {
			_ = s.In("zone", AZ)
		} else {
			v.Zone = AZ
		}
	case *SlbTargets:
		if s.Relax {
			_ = s.In("zone", AZ)
		} else {
			v.Zone = AZ
		}
	case *BosonServices:
		if s.Relax {
			_ = s.In("endpoint_zone", AZ)
		} else {
			v.EndpointZone = AZ
		}
	case *BosonServiceEndpoints:
		if s.Relax {
			_ = s.In("zone", AZ)
		} else {
			v.Zone = AZ
		}
	case *Dcgws:
		v.Zone = AZ
	case *Dcvcs:
		v.Zone = AZ
	case *Dchcs:
		v.Zone = AZ
	}
}

func (s *SessionWrapper) injectCondiAz(rowsSlicePtr interface{}, condiBean ...interface{}) []interface{} {
	if len(condiBean) > 0 {
		s.injectAZ(condiBean[0])
	} else {
		sliceValue := reflect.Indirect(reflect.ValueOf(rowsSlicePtr))
		sliceElementType := sliceValue.Type().Elem()
		var pv reflect.Value
		if sliceElementType.Kind() == reflect.Ptr {
			pv = reflect.New(sliceElementType.Elem())
		} else {
			pv = reflect.New(sliceElementType)
		}
		condi := pv.Interface()
		s.injectAZ(condi)
		condiBean = []interface{}{condi}
	}
	return condiBean
}

func (s *SessionWrapper) insertMulInjectAZ(rowsSlicePtr interface{}) {
	slice := reflect.ValueOf(rowsSlicePtr).Elem()
	for i := 0; i < slice.Len(); i++ {
		elem := slice.Index(i)
		actualElem := elem.Interface()
		s.injectAZ(actualElem)
	}
}

/// SessionWrapper ///

type SessionWrapper struct {
	*xorm.Session
	Relax bool
}

func (s *SessionWrapper) Get(beans ...interface{}) (bool, error) {
	s.Relax = true
	if len(beans) > 0 {
		s.injectAZ(beans[0])
	}
	return s.Session.Get(beans...)
}

func (s *SessionWrapper) Exist(beans ...interface{}) (bool, error) {
	s.Relax = true
	if len(beans) > 0 {
		s.injectAZ(beans[0])
	}
	return s.Session.Exist(beans...)
}

func (s *SessionWrapper) Find(rowsSlicePtr interface{}, condiBean ...interface{}) error {
	s.Relax = true
	condiBean = s.injectCondiAz(rowsSlicePtr, condiBean...)
	return s.Session.Find(rowsSlicePtr, condiBean...)
}

func (s *SessionWrapper) FindAndCount(rowsSlicePtr interface{}, condiBean ...interface{}) (int64, error) {
	s.Relax = true
	condiBean = s.injectCondiAz(rowsSlicePtr, condiBean...)
	return s.Session.FindAndCount(rowsSlicePtr, condiBean...)
}

func (s *SessionWrapper) Count(beans ...interface{}) (int64, error) {
	s.Relax = true
	if len(beans) > 0 {
		s.injectAZ(beans[0])
	}
	return s.Session.Count(beans...)
}

func (s *SessionWrapper) And(query interface{}, args ...interface{}) *SessionWrapper {
	_ = s.Session.And(query, args...)
	return s
}

func (s *SessionWrapper) In(column string, args ...interface{}) *SessionWrapper {
	_ = s.Session.In(column, args...)
	return s
}

func (s *SessionWrapper) OrderBy(column string, args ...interface{}) *SessionWrapper {
	_ = s.Session.OrderBy(column, args...)
	return s
}

func (s *SessionWrapper) Insert(beans ...interface{}) (int64, error) {
	s.Relax = false
	if len(beans) > 0 {
		for i := range beans {
			s.injectAZ(beans[i])
		}
	}
	return s.Session.Insert(beans...)
}

func (s *SessionWrapper) InsertMulti(rowsSlicePtr interface{}) (int64, error) {
	s.Relax = false
	if rowsSlicePtr != nil {
		s.insertMulInjectAZ(rowsSlicePtr)
	}
	return s.Session.InsertMulti(rowsSlicePtr)
}

func (s *SessionWrapper) InsertOne(bean interface{}) (int64, error) {
	s.Relax = false
	if bean != nil {
		s.injectAZ(bean)
	}
	return s.Session.InsertOne(bean)
}

func (s *SessionWrapper) Update(bean interface{}, condiBean ...interface{}) (int64, error) {
	s.Relax = false
	if bean != nil {
		s.injectAZ(bean)
	}
	return s.Session.Update(bean, condiBean...)
}

/// EngineWrapper ///

type EngineWrapper struct {
	*xorm.Engine
}

func NewEngineWrapper(e *xorm.Engine) *EngineWrapper {
	return &EngineWrapper{e}
}

func (engine *EngineWrapper) Where(query interface{}, args ...interface{}) *SessionWrapper {
	session := engine.Engine.Where(query, args...)
	return &SessionWrapper{session, false}
}

func (engine *EngineWrapper) Get(beans ...interface{}) (bool, error) {
	session := engine.NewSession()
	defer session.Close()
	return session.Get(beans...)
}

func (engine *EngineWrapper) Find(beans interface{}, condiBeans ...interface{}) error {
	session := engine.NewSession()
	defer session.Close()
	return session.Find(beans, condiBeans...)
}

func (engine *EngineWrapper) FindAndCount(beans interface{}, condiBeans ...interface{}) (int64, error) {
	session := engine.NewSession()
	defer session.Close()
	return session.FindAndCount(beans, condiBeans...)
}

func (engine *EngineWrapper) Exist(beans ...interface{}) (bool, error) {
	session := engine.NewSession()
	defer session.Close()
	return session.Exist(beans...)
}

func (engine *EngineWrapper) Count(beans ...interface{}) (int64, error) {
	session := engine.NewSession()
	defer session.Close()
	return session.Count(beans...)
}

func (engine *EngineWrapper) Insert(beans ...interface{}) (int64, error) {
	session := engine.NewSession()
	defer session.Close()
	return session.Insert(beans...)
}

func (engine *EngineWrapper) InsertOne(bean interface{}) (int64, error) {
	session := engine.NewSession()
	defer session.Close()
	return session.InsertOne(bean)
}

func (engine *EngineWrapper) InsertMulti(rowsSlicePtr interface{}) (int64, error) {
	session := engine.NewSession()
	defer session.Close()
	return session.InsertMulti(rowsSlicePtr)
}

func (engine *EngineWrapper) Update(bean interface{}, condiBeans ...interface{}) (int64, error) {
	session := engine.NewSession()
	defer session.Close()
	return session.Update(bean, condiBeans...)
}

func (engine *EngineWrapper) OrderBy(order interface{}, args ...interface{}) *SessionWrapper {
	session := engine.Engine.OrderBy(order, args...)
	return &SessionWrapper{session, false}
}

func (engine *EngineWrapper) In(column string, args ...interface{}) *SessionWrapper {
	session := engine.Engine.In(column, args...)
	return &SessionWrapper{session, false}
}

func (engine *EngineWrapper) ID(id interface{}) *SessionWrapper {
	session := engine.NewSession()
	// should not Close at this moment
	return &SessionWrapper{session.ID(id), false}
}

func (engine *EngineWrapper) NewSession() *SessionWrapper {
	session := engine.Engine.NewSession()
	return &SessionWrapper{session, false}
}
