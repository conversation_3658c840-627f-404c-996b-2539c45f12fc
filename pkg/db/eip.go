package db

import (
	"time"
)

// Eips 表
type Eips struct {
	EIPID              string    `json:"eip_id" xorm:"varchar(64) pk 'eip_id'"`
	DisplayName        string    `json:"display_name" xorm:"varchar(64) notnull 'display_name'"`
	Description        string    `json:"description" xorm:"varchar(256) 'description'"`
	AssociationID      string    `json:"association_id" xorm:"varchar(64) notnull 'association_id'"`
	AssociationType    string    `json:"association_type" xorm:"varchar(64) notnull 'association_type'"`
	EIPIP              string    `json:"eip_ip" xorm:"varchar(64) notnull 'eip_ip'"`
	VPCID              string    `json:"vpc_id" xorm:"varchar(64) index notnull 'vpc_id'"`
	EIPPoolID          string    `json:"eip_pool_id" xorm:"varchar(64) notnull 'eip_pool_id'"`
	BW                 int32     `json:"bw" xorm:"int 'bw'"`
	UpStreamBW         int32     `json:"upstream_bw" xorm:"int 'upstream_bw'"`
	DownStreamBW       int32     `json:"downstream_bw" xorm:"int 'downstream_bw'"`
	MaxConn            int32     `json:"max_conn" xorm:"int 'max_conn'"`
	ID                 string    `json:"id" xorm:"varchar(1024) notnull 'id'"`
	Name               string    `json:"name" xorm:"varchar(64) notnull 'name'"`
	ResourceType       string    `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`
	Zone               string    `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State              string    `json:"state" xorm:"varchar(64) notnull 'state'"`
	CreatorID          string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID            string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID           string    `json:"tenant_id" xorm:"varchar(64) index notnull 'tenant_id'"`
	CreateTime         time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime         time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted            bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags               string    `json:"tags" xorm:"text 'tags'"`
	InternalEIP        bool      `json:"internal_eip" xorm:"bool notnull default false 'internal_eip'"`
	SnatDstCidrEnabled bool      `json:"snat_dst_cidr_enabled" xorm:"bool notnull default false 'snat_dst_cidr_enabled'"`
}

// Eip_Pools 表
type EipPools struct {
	EIPPoolID string `json:"eip_pool_id" xorm:"varchar(64) pk 'eip_pool_id'"`
	EIPIP     string `json:"eip_ip" xorm:"varchar(64) notnull 'eip_ip'"`
	Allocated bool   `json:"allocated" xorm:"bool notnull 'allocated'"`
	Gateway   string `json:"gateway" xorm:"varchar(64) 'gateway'"`
	CIDR      string `json:"cidr" xorm:"varchar(64) 'cidr'"`
	Sku       string `json:"sku" xorm:"varchar(64) notnull default 'BGP' 'sku'"`
	Line      string `json:"line" xorm:"varchar(64) notnull 'line'"`
}

// Eip sku line 拓扑表
type SkuLineMeta struct {
	UUID            string    `json:"uuid" xorm:"varchar(36) notnull 'uuid'"`
	Sku             string    `json:"sku" xorm:"varchar(32) notnull 'sku'"`
	SkuDisplayName  string    `json:"sku_display_name" xorm:"varchar(256) notnull 'sku_display_name'"`
	Line            string    `json:"line" xorm:"varchar(32) notnull 'line'"`
	LineDisplayName string    `json:"line_display_name" xorm:"varchar(256) notnull 'line_display_name'"`
	Weight          uint32    `json:"weight" xorm:"uint32 notnull 'weight'"`
	CreateTime      time.Time `json:"create_time" xorm:"timestamp created 'create_time'"`
	UpdateTime      time.Time `json:"update_time" xorm:"timestamp updated 'update_time'"`
	DeleteTime      time.Time `json:"delete_time" xorm:"timestamp 'delete_time'"`
	Deleted         bool      `json:"deleted" xorm:"bool default false notnull 'deleted'"`
}

// tenant绑定eip线路表
type TenantSkuLine struct {
	TenantID        string    `json:"tenant_id" xorm:"varchar(36) notnull 'tenant_id'"`
	LineUUID        string    `json:"line_uuid" xorm:"varchar(36) notnull 'line_uuid'"`
	Sku             string    `json:"sku" xorm:"varchar(32) notnull 'sku'"`
	SkuDisplayName  string    `json:"sku_display_name" xorm:"varchar(256) notnull 'sku_display_name'"`
	Line            string    `json:"line" xorm:"varchar(32) notnull 'line'"`
	LineDisplayName string    `json:"line_display_name" xorm:"varchar(256) notnull 'line_display_name'"`
	CreateTime      time.Time `json:"create_time" xorm:"timestamp created 'create_time'"`
	DeleteTime      time.Time `json:"delete_time" xorm:"timestamp 'delete_time'"`
	Deleted         bool      `json:"deleted" xorm:"bool default false notnull 'deleted'"`
}
