package db

var BosonTables = map[string]interface{}{
	"vpcs":                          Vpcs{},
	"vpc_quota":                     VpcQuota{},
	"subnets":                       Subnets{},
	"k8s_namespaces":                K8sNamespaces{},
	"nat_gateways":                  NatGateways{},
	"nat_gateway_external_ip_pools": NatGatewayExternalIpPools{},
	"cidr_pools":                    CidrPools{},
	"eips":                          Eips{},
	"eip_pools":                     EipPools{},
	"dnat_rules":                    DnatRules{},
	"ipas":                          Ipas{},
	"snat_rules":                    SnatRules{},
	"acls":                          Acls{},
	"ha_nat_gateways":               HaNatGateways{},
	"dcgws":                         Dcgws{},
	"dcvcs":                         Dcvcs{},
	"dchcs":                         Dchcs{},
	"slbs":                          Slbs{},
	"slb_listeners":                 SlbListeners{},
	"slb_target_groups":             SlbTargetGroups{},
	"slb_targets":                   SlbTargets{},
	"slb_external_ip_pools":         SlbExternalIpPools{},
	"slb_health_checks":             SlbHealthChecks{},
	"boson_services":                BosonServices{},
	"boson_service_endpoints":       BosonServiceEndpoints{},
	"vpc_acls":                      VpcAcls{},
	"slb_forward_rule_associate_target_groups": SlbForwardRuleAssociateTargetGroups{},
	"distribute_gateways":                      DistributeGateways{},
	"tenant_sku_line":                          TenantSkuLine{},
}

func GetDbTables() []interface{} {
	tables := []interface{}{}
	for k := range BosonTables {
		tables = append(tables, BosonTables[k])
	}
	return tables
}
