package db

import "time"

type HaNatGateways struct {
	HaNatGatewayId    string    `json:"ha_nat_gateway_id" xorm:"varchar(64) pk 'ha_nat_gateway_id'"`
	HaMde             string    `json:"ha_mode" xorm:"varchar(64) notnull 'ha_mode'"`
	DisplayName       string    `json:"display_name" xorm:"varchar(64) notnull 'display_name'"`
	Description       string    `json:"description" xorm:"varchar(256) notnull 'description'"`
	ID                string    `json:"id" xorm:"varchar(1024) notnull 'id'"`
	Name              string    `json:"name" xorm:"varchar(64) notnull 'name'"`
	NatGwExternalIPID string    `json:"nat_gw_external_ip_id" xorm:"varchar(64) notnull 'nat_gw_external_ip_id'"`
	InternalIP        string    `json:"internal_ip" xorm:"varchar(64) notnull 'internal_ip'"`
	BmsGwVip          string    `json:"bms_gw_vip" xorm:"varchar(64) 'bms_gw_vip'"`
	MonitorIp         string    `json:"monitor_ip" xorm:"varchar(64) notnull 'monitor_ip'"`
	HeartbeatListen   string    `json:"heartbeat_listen" xorm:"varchar(64) notnull 'heartbeat_listen'"`
	ResourceType      string    `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`
	VrrpVip           string    `json:"vrrp_vip" xorm:"varchar(64) notnull 'vrrp_vip'"`
	VpcID             string    `json:"vpc_id" xorm:"varchar(64) notnull 'vpc_id'"`
	SubnetID          string    `json:"subnet_id" xorm:"varchar(64) notnull 'subnet_id'"`
	Zone              string    `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State             string    `json:"state" xorm:"varchar(64) notnull 'state'"`
	CreatorID         string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID           string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID          string    `json:"tenant_id" xorm:"varchar(64) notnull 'tenant_id'"`
	CreateTime        time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime        time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted           bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags              string    `json:"tags" xorm:"text 'tags'"`
}
