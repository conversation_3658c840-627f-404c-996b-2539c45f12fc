package migrate

import (
	"fmt"

	"github.com/sirupsen/logrus"
	"xorm.io/xorm"
)

func AlterVpcAclColumnLength(engine *xorm.Engine) {
	var columns []map[string]string
	err := engine.SQL(`
		SELECT COLUMN_NAME, CHARACTER_MAXIMUM_LENGTH
		FROM INFORMATION_SCHEMA.COLUMNS
		WHERE TABLE_NAME = 'vpc_acls' AND COLUMN_NAME IN ('src', 'dest')
	`).Find(&columns)
	if err != nil {
		logrus.Errorf("Failed to query column info: %v", err)
	} else {
		logrus.Infof("intend to altercolumns: %v", columns)
		for _, column := range columns {
			columnName := column["column_name"]
			length := column["character_maximum_length"]
			if length != "1024" {
				_, err := engine.Exec(fmt.Sprintf("ALTER TABLE vpc_acls ALTER COLUMN %s TYPE VARCHAR(1024)", columnName))
				if err != nil {
					logrus.E<PERSON><PERSON>("Failed to alter column %s: %v", columnName, err)
				} else {
					logrus.Infof("Successfully altered column %s to VARCHAR(1024)", columnName)
				}
			}
		}
	}
}
