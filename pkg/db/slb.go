package db

import (
	"time"
)

// SLB 表; slb 设计为 Region 级别的资源
type Slbs struct {
	// -- 基础信息
	SlbID        string `json:"slb_id" xorm:"varchar(64) pk 'slb_id'"`
	Name         string `json:"name" xorm:"varchar(64) notnull 'name'"`
	Zone         string `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State        string `json:"state" xorm:"varchar(64) notnull 'state'"`
	DisplayName  string `json:"display_name" xorm:"varchar(64) notnull 'display_name'"`
	Description  string `json:"description" xorm:"varchar(256) 'description'"`
	ResourceType string `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`
	// --
	LoadBalancerType string `json:"load_balancer_type" xorm:"varchar(64) notnull 'load_balancer_type'"`
	VpcID            string `json:"vpc_id" xorm:"varchar(64) notnull 'vpc_id'"`
	IPVersion        string `json:"ip_version" xorm:"varchar(64) notnull default 'IPV4' 'ip_version'"`
	EipID            string `json:"eip_id" xorm:"varchar(64) 'eip_id'"`
	// -- IP 信息
	BasicNetworkIpIDs string `json:"basic_network_ip_ids" xorm:"text notnull 'basic_network_ip_ids'"`
	BasicNetworkVipID string `json:"basic_network_vip_id" xorm:"varchar(64) notnull 'basic_network_vip_id'"`
	IntranetVIP       string `json:"intranet_vip" xorm:"varchar(64) notnull 'intranet_vip'"` // 【废弃】仅保留
	// -- 资源规格属性
	TcpCps      int64 `json:"tcp_cps" xorm:"int(10) notnull default 0 'tcp_cps'"`
	TcpConns    int64 `json:"tcp_conns" xorm:"int(10) notnull default 0 'tcp_conns'"`
	UdpCps      int64 `json:"udp_cps" xorm:"int(10) notnull default 0 'udp_cps'"`
	UdpConns    int64 `json:"udp_conns" xorm:"int(10) notnull default 0 'udp_conns'"`
	TcpsslCps   int64 `json:"tcpssl_cps" xorm:"int(10) notnull default 0 'tcpssl_cps'"`
	TcpsslConns int64 `json:"tcpssl_conns" xorm:"int(10) notnull default 0 'tcpssl_conns'"`
	HttpCps     int64 `json:"http_cps" xorm:"int(10) notnull default 0 'http_cps'"`
	HttpConns   int64 `json:"http_conns" xorm:"int(10) notnull default 0 'http_conns'"`
	HttpQps     int64 `json:"http_qps" xorm:"int(10) notnull default 0 'http_qps'"`
	HttpsCps    int64 `json:"https_cps" xorm:"int(10) notnull default 0 'https_cps'"`
	HttpsConns  int64 `json:"https_conns" xorm:"int(10) notnull default 0 'https_conns'"`
	HttpsQps    int64 `json:"https_qps" xorm:"int(10) notnull default 0 'https_qps'"`
	// -- 其他公共字段
	ID         string    `json:"id" xorm:"varchar(1024) index notnull 'id'"`
	CreatorID  string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID    string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID   string    `json:"tenant_id" xorm:"varchar(64) index notnull 'tenant_id'"`
	CreateTime time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted    bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags       string    `json:"tags" xorm:"text 'tags'"`
	Sku        string    `json:"sku" xorm:"varchar(64) 'sku'"`
}

// Listener 表
type SlbListeners struct {
	// -- 基础信息
	ListenerID   string `json:"listener_id" xorm:"varchar(64) pk 'listener_id'"`
	Name         string `json:"name" xorm:"varchar(64) notnull 'name'"`
	Zone         string `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State        string `json:"state" xorm:"varchar(64) notnull 'state'"`
	DisplayName  string `json:"display_name" xorm:"varchar(64) notnull 'display_name'"`
	Description  string `json:"description" xorm:"varchar(256) 'description'"`
	ResourceType string `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`
	// --
	SlbID                         string `json:"slb_id" xorm:"varchar(64) 'slb_id'"`
	ForwardActionType             string `json:"forward_action_type" xorm:"varchar(64) 'forward_action_type'"`
	Protocol                      string `json:"protocol" xorm:"varchar(64) 'protocol'"`
	Port                          int32  `json:"port" xorm:"int 'port'"`
	OriginalClientIpAddressEnable bool   `json:"original_client_ip_address_enable" xorm:"bool 'original_client_ip_address_enable'"`
	ProxyProtocolEnable           bool   `json:"proxy_protocol_enable" xorm:"bool 'proxy_protocol_enable'"`
	// -- 其他公共字段
	ID         string    `json:"id" xorm:"varchar(1024) index notnull 'id'"`
	CreatorID  string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID    string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID   string    `json:"tenant_id" xorm:"varchar(64) index notnull 'tenant_id'"`
	CreateTime time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted    bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags       string    `json:"tags" xorm:"text 'tags'"`
}

type SlbForwardRuleAssociateTargetGroups struct {
	ID int64 `json:"id" xorm:"pk autoincr 'id'"` // 改成联合主键 TODO
	// ListenerID or RuleID
	ForwardRuleID   string `json:"forward_rule_id" xorm:"varchar(64) notnull 'forward_rule_id'"`
	TargetGroupID   string `json:"target_group_id" xorm:"varchar(64) 'target_group_id'"`
	ForwardRuleType string `json:"forward_rule_type" xorm:"varchar(64) 'forward_rule_type'"`
	SlbID           string `json:"slb_id" xorm:"varchar(64) 'slb_id'"`
	Deleted         bool   `json:"deleted" xorm:"bool notnull 'deleted'"`
}

// TargetGroup 表
type SlbTargetGroups struct {
	// -- 基础信息
	TargetGroupID string `json:"target_group_id" xorm:"varchar(64) pk 'target_group_id'"`
	Name          string `json:"name" xorm:"varchar(64) notnull 'name'"`
	Zone          string `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State         string `json:"state" xorm:"varchar(64) notnull 'state'"`
	DisplayName   string `json:"display_name" xorm:"varchar(64) notnull 'display_name'"`
	Description   string `json:"description" xorm:"varchar(256) 'description'"`
	ResourceType  string `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`

	// --
	SlbID                            string `json:"slb_id" xorm:"varchar(64) 'slb_id'"`
	Type                             string `json:"type" xorm:"varchar(64) 'type'"`
	Scheduler                        string `json:"scheduler" xorm:"varchar(64) 'scheduler'"`
	Protocol                         string `json:"protocol" xorm:"varchar(64) 'protocol'"`
	Weight                           int32  `json:"weight" xorm:"int 'weight'"`
	CciDeploymentInstanceName        string `json:"cci_deployment_instance_name" xorm:"varchar(64) 'cci_deployment_instance_name'"`
	CciDeploymentInstanceDisplayName string `json:"cci_deployment_instance_display_name" xorm:"varchar(64) 'cci_deployment_instance_display_name'"`
	CciDeploymentInstanceId          string `json:"cci_deployment_instance_id" xorm:"varchar(64) 'cci_deployment_instance_id'"`   // 暂未使用
	CciDeploymentInstanceUid         string `json:"cci_deployment_instance_uid" xorm:"varchar(64) 'cci_deployment_instance_uid'"` // 暂未使用
	CciDeploymentInstancePort        int32  `json:"cci_deployment_instance_port" xorm:"int 'cci_deployment_instance_port'"`
	// TODO
	// 缺少健康检查的配置

	// 其他公共字段
	ID         string    `json:"id" xorm:"varchar(1024) index notnull 'id'"`
	CreatorID  string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID    string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID   string    `json:"tenant_id" xorm:"varchar(64) index notnull 'tenant_id'"`
	CreateTime time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted    bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags       string    `json:"tags" xorm:"text 'tags'"`
}

// Target 表
type SlbTargets struct {
	// -- 基础信息
	TargetID     string `json:"target_id" xorm:"varchar(64) pk 'target_id'"`
	Name         string `json:"name" xorm:"varchar(64) notnull 'name'"`
	Zone         string `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State        string `json:"state" xorm:"varchar(64) notnull 'state'"`
	DisplayName  string `json:"display_name" xorm:"varchar(64) notnull 'display_name'"`
	Description  string `json:"description" xorm:"varchar(256) 'description'"`
	ResourceType string `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`

	// --
	SlbID         string `json:"slb_id" xorm:"varchar(64) 'slb_id'"`
	TargetGroupID string `json:"target_group_id" xorm:"varchar(64) 'target_group_id'"`
	IpAddress     string `json:"ip_address" xorm:"varchar(64) 'ip_address'"`
	Port          int32  `json:"port" xorm:"int 'port'"`
	Weight        int32  `json:"weight" xorm:"int 'weight'"`
	Type          string `json:"type" xorm:"varchar(64) 'type'"`
	InstanceName  string `json:"instance_name" xorm:"varchar(64) 'instance_name'"`

	// 其他公共字段
	ID         string    `json:"id" xorm:"varchar(1024) index notnull 'id'"`
	CreatorID  string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID    string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID   string    `json:"tenant_id" xorm:"varchar(64) index notnull 'tenant_id'"`
	CreateTime time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted    bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags       string    `json:"tags" xorm:"text 'tags'"`
}

// SLB_External_IP_Pools 表
type SlbExternalIpPools struct {
	SlbExternalIpID string `json:"slb_external_ip_id" xorm:"varchar(64) pk 'slb_external_ip_id'"`
	SlbExternalIp   string `json:"slb_external_ip" xorm:"varchar(64) notnull 'slb_external_ip'"`
	Description     string `json:"description" xorm:"varchar(256) 'description'"`
	Allocated       bool   `json:"allocated" xorm:"bool notnull 'allocated'"`
	SlbID           string `json:"slb_id" xorm:"varchar(64) 'slb_id'"` // 字段无效？TODO
	CidrID          string `json:"cidr_id" xorm:"varchar(64) notnull 'cidr_id'"`
	Type            string `json:"type" xorm:"varchar(64) 'type'"`
}

type SlbHealthChecks struct {
	TargetGroupID     string `json:"target_group_id" xorm:"varchar(64) pk 'target_group_id'"`
	Enable            bool   `json:"enable" xorm:"bool notnull 'enable'"`
	Type              string `json:"type" xorm:"varchar(32) notnull 'type'"`
	Timeout           int32  `json:"timeout" xorm:"int notnull 'timeout'"`
	Interval          int32  `json:"interval" xorm:"int notnull 'interval'"`
	HealthThreshold   int32  `json:"health_threshold" xorm:"int notnull 'health_threshold'"`
	UnhealthThreshold int32  `json:"unhealth_threshold" xorm:"int notnull 'unhealth_threshold'"`
	HealthCheckConfig string `json:"health_check_config" xorm:"varchar(1024) notnull 'health_check_config'"`
	Deleted           bool   `json:"deleted" xorm:"bool notnull 'deleted'"`
}

type SlbExternalIpPoolType string

const (
	Communication SlbExternalIpPoolType = "communication"
	VirtualIP     SlbExternalIpPoolType = "vip"
)
