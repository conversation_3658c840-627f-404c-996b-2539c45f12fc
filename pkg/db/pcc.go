package db

import (
	"time"
)

// Pccs 表
type Pccs struct {
	PCCID             string    `json:"pcc_id" xorm:"varchar(64) pk 'pcc_id'"`
	DisplayName       string    `json:"display_name" xorm:"varchar(64) notnull 'display_name'"`
	Description       string    `json:"description" xorm:"varchar(256) 'description'"`
	ID                string    `json:"id" xorm:"varchar(1024) notnull 'id'"`
	Name              string    `json:"name" xorm:"varchar(64) index notnull 'name'"`
	ResourceType      string    `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`
	UpStreamBW        int32     `json:"upstream_bw" xorm:"int 'upstream_bw'"`
	DownStreamBW      int32     `json:"downstream_bw" xorm:"int 'downstream_bw'"`
	TransitSwitch     string    `json:"transit_switch" xorm:"varchar(64) notnull 'transit_switch'"`
	TransitSwitchType string    `json:"transit_switch_type" xorm:"varchar(64) notnull 'transit_switch_type'"`
	LocalVpc          string    `json:"local_vpc" xorm:"varchar(64) notnull 'local_vpc'"`
	RemoteVpc         string    `json:"remote_vpc" xorm:"varchar(64) notnull 'remote_vpc'"`
	LocalDisplayName  string    `json:"local_display_name" xorm:"varchar(64) 'local_display_name'"`
	RemoteDisplayName string    `json:"remote_display_name" xorm:"varchar(64) 'remote_display_name'"`
	LocalCidr         string    `json:"local_cidr" xorm:"varchar(64) notnull 'local_cidr'"`
	RemoteCidr        string    `json:"remote_cidr" xorm:"varchar(64) notnull 'remote_cidr'"`
	LocalZone         string    `json:"local_zone" xorm:"varchar(64) notnull 'local_zone'"`
	RemoteZone        string    `json:"remote_zone" xorm:"varchar(64) notnull 'remote_zone'"`
	LocalGatewayIp    string    `json:"local_gateway_ip" xorm:"varchar(64) notnull 'local_gateway_ip'"`
	RemoteGatewayIp   string    `json:"remote_gateway_ip" xorm:"varchar(64) notnull 'remote_gateway_ip'"`
	LocalState        string    `json:"local_state" xorm:"varchar(64) notnull 'local_state'"`
	RemoteState       string    `json:"remote_state" xorm:"varchar(64) notnull 'remote_state'"`
	Zone              string    `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	CreatorID         string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID           string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID          string    `json:"tenant_id" xorm:"varchar(64) index notnull 'tenant_id'"`
	CreateTime        time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime        time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Tags              string    `json:"tags" xorm:"text 'tags'"`
}
