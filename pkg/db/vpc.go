package db

import (
	"time"
)

// Vpcs 表
type Vpcs struct {
	VPCID        string    `json:"vpc_id" xorm:"varchar(64) pk 'vpc_id'"`
	DisplayName  string    `json:"display_name" xorm:"varchar(64) notnull 'display_name'"`
	CIDR         string    `json:"cidr" xorm:"varchar(64) notnull 'cidr'"`
	Description  string    `json:"description" xorm:"varchar(256) 'description'"`
	IsDefault    bool      `json:"is_default" xorm:"bool notnull 'is_default'"`
	ID           string    `json:"id" xorm:"varchar(1024) notnull 'id'"`
	Name         string    `json:"name" xorm:"varchar(64) index notnull 'name'"`
	ResourceType string    `json:"resource_type" xorm:"varchar(64) notnull 'resource_type'"`
	Zone         string    `json:"zone" xorm:"varchar(64) notnull 'zone'"`
	State        string    `json:"state" xorm:"varchar(64) notnull 'state'"`
	CreatorID    string    `json:"creator_id" xorm:"varchar(64) notnull 'creator_id'"`
	OwnerID      string    `json:"owner_id" xorm:"varchar(64) notnull 'owner_id'"`
	TenantID     string    `json:"tenant_id" xorm:"varchar(64) index notnull 'tenant_id'"`
	SubnetType   string    `json:"subnet_type" xorm:"varchar(64) default 'POD_BMS_SERVICE_TRAINING' 'subnet_type'"`
	CreateTime   time.Time `json:"create_time" xorm:"timestamp created notnull 'create_time'"`
	UpdateTime   time.Time `json:"update_time" xorm:"timestamp updated notnull 'update_time'"`
	Deleted      bool      `json:"deleted" xorm:"bool notnull 'deleted'"`
	Tags         string    `json:"tags" xorm:"text 'tags'"`
}

// VPC_Quota 表
type VpcQuota struct {
	VPCID                string `json:"vpc_id" xorm:"varchar(64) pk 'vpc_id'"`
	GeneveSubnetCount    int64  `json:"geneve_subnet_count" xorm:"int(10) notnull 'geneve_subnet_count'"`
	VlanSubnetCount      int64  `json:"vlan_subnet_count" xorm:"int(10) notnull 'vlan_subnet_count'"`
	IBSubnetCount        int64  `json:"ib_subnet_count" xorm:"int(10) notnull 'ib_subnet_count'"`
	RoceSubnetCount      int64  `json:"roce_subnet_count" xorm:"int(10) notnull 'roce_subnet_count'"`
	EipCount             int64  `json:"eip_count" xorm:"int(10) notnull 'eip_count'"`
	EipDnatRuleCount     int64  `json:"eip_dnat_rule_count" xorm:"int(10) notnull default 50 'eip_dnat_rule_count'"`
	NatGatewayCount      int64  `json:"nat_gateway_count" xorm:"int(10) notnull 'nat_gateway_count'"`
	SlbPerVpc            int64  `json:"slb_per_vpc" xorm:"int(10) notnull default 10 'slb_per_vpc'"`
	ListenerPerSlb       int64  `json:"listener_per_slb" xorm:"int(10) notnull default 50 'listener_per_slb'"`
	RulePerSlb           int64  `json:"rule_per_slb" xorm:"int(10) notnull default 50 'rule_per_slb'"`
	TargetGroupPerSlb    int64  `json:"target_group_per_slb" xorm:"int(10) notnull default 50 'target_group_per_slb'"`
	TargetPerTargetGroup int64  `json:"target_per_target_group" xorm:"int(10) notnull default 50 'target_per_target_group'"`
	SlbPreVpc            int64  `json:"slb_pre_vpc" xorm:"int(10) notnull default 0 'slb_pre_vpc'"`
	ListenerPreSlb       int64  `json:"listener_pre_slb" xorm:"int(10) notnull default 0 'listener_pre_slb'"`
	RulePreSlb           int64  `json:"rule_pre_slb" xorm:"int(10) notnull default 0 'rule_pre_slb'"`
	TargetGroupPreSlb    int64  `json:"target_group_pre_slb" xorm:"int(10) notnull default 0 'target_group_pre_slb'"`
	TargetPreTargetGroup int64  `json:"target_pre_target_group" xorm:"int(10) notnull default 0 'target_pre_target_group'"`
	PeerCount            int64  `json:"peer_count" xorm:"int(10) notnull default 10 'peer_count'"`
	PublicEIPAclCount    int64  `json:"public_eip_acl_count" xorm:"int(10) notnull default 10 'public_eip_acl_count'"`

	InternalEIPAclCount     int64 `json:"internal_eip_acl_count" xorm:"int(10) notnull default 50 'internal_eip_acl_count'"`
	InternalEIPDstCidrCount int64 `json:"internal_eip_dst_cidr_count" xorm:"int(10) notnull default 50 'internal_eip_dst_cidr_count'"`
}
