package dao

import (
	"fmt"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

func GetDcvcByName(e *db.EngineWrapper, dcvcName string) (*db.Dcvcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetDCVC")
	defer dbRTMetrics.ObserveDurationAndLog()

	dcvc := &db.Dcvcs{}
	var err error
	find := false
	if find, err = e.Where("name = ? and deleted = ?", dcvcName, false).Get(dcvc); err != nil {
		return dcvc, err
	}

	if !find {
		return nil, fmt.Errorf("DCVC not found, name = %s", dcvcName)
	}

	return dcvc, nil
}

func GetDcvcByDcgwName(e *db.EngineWrapper, dcgwName string) ([]*db.Dcvcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetDcvcByDcgwName")
	defer dbRTMetrics.ObserveDurationAndLog()

	dcvcDatas := []*db.Dcvcs{}
	err := e.Where("dcgw_name = ? and deleted = ?", dcgwName, false).Find(&dcvcDatas)
	return dcvcDatas, err
}

func GetDcvcByDcgwNameWithAllZone(e *db.EngineWrapper, dcgwName string) ([]*db.Dcvcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetDcvcByDcgwName")
	defer dbRTMetrics.ObserveDurationAndLog()

	dcvcDatas := []*db.Dcvcs{}
	err := e.Engine.Where("dcgw_name = ? and deleted = ?", dcgwName, false).Find(&dcvcDatas)
	return dcvcDatas, err
}

func GetDcvc(e *db.EngineWrapper, id string) (*db.Dcvcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetDCVC")
	defer dbRTMetrics.ObserveDurationAndLog()

	dcvcData := db.Dcvcs{}
	has, err := e.ID(id).And("deleted = false").Get(&dcvcData)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, fmt.Errorf("DCVC not found, id = %s", id)
	}
	return &dcvcData, nil
}

func GetAllDcvcs(e *db.EngineWrapper, includeDeleted bool) ([]*db.Dcvcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListDCVCs")
	defer dbRTMetrics.ObserveDurationAndLog()

	dcvcDatas := []*db.Dcvcs{}
	var err error
	if includeDeleted {
		err = e.Find(&dcvcDatas)
	} else {
		err = e.Where("deleted = false").Find(&dcvcDatas)
	}
	return dcvcDatas, err
}

func SetDcvcState(e *db.EngineWrapper, name, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetStateDCVC")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update dcvcs set state = ?, deleted = ? where name = ? ", state, state == "DELETED", name)
	return err
}

func ExistDcvc(e *db.EngineWrapper, name string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDCVC")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Where("deleted = false").Exist(&db.Dcvcs{
		Name: name,
	})
}
func ExistDcvcResource(e *db.EngineWrapper, name, subscriptionName, resourceGroupName string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDCVC")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Where("deleted = false").Exist(&db.Dcvcs{
		Name:              name,
		SubscriptionName:  subscriptionName,
		ResourceGroupName: resourceGroupName,
	})
}

func ExistDcvcByDcgwName(e *db.EngineWrapper, dcgwName string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDCVCByDcgwName")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Where("deleted = false").Exist(&db.Dcvcs{
		DcgwName: dcgwName,
	})
}

func ExistDcvcByVxlanId(e *db.EngineWrapper, vxlanId string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDCVCByDcgwName")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Engine.Where("deleted = false").Exist(&db.Dcvcs{
		VxlanId: vxlanId,
	})
}

func ExistDcvcByDcplId(e *db.EngineWrapper, dcplId string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDCVCByDcplId")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Where("deleted = false").Exist(&db.Dcvcs{
		DcplId: dcplId,
	})
}

func CheckDcvcExists(e *db.EngineWrapper, dcvcName string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDCVC")
	defer dbRTMetrics.ObserveDurationAndLog()

	checkDcvcData := db.Dcvcs{}
	return e.Where("name = ? and deleted = ?", dcvcName, false).Exist(&checkDcvcData)
}

func DeleteDcvc(e *db.SessionWrapper, dcvcID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteDCVC")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update dcvcs set state = 'DELETED', deleted = true where dcvc_id = ?", dcvcID)
	return err
}

func AddDCVC(e *db.EngineWrapper, dcvc *db.Dcvcs) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddDCVC")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Insert(dcvc)
	return err
}

func UpdateDCVC(e *db.EngineWrapper, dcvc *db.Dcvcs) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateDCVC")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.ID(dcvc.DcvcID).Update(dcvc)
	return err
}

func ListDcvcsByTenant(e *db.EngineWrapper, tenantID string) ([]*db.Dcvcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListDCVCs")
	defer dbRTMetrics.ObserveDurationAndLog()

	dcvcDatas := []*db.Dcvcs{}
	err := e.Engine.Where("tenant_id = ? and deleted = ?", tenantID, false).Find(&dcvcDatas)

	return dcvcDatas, err
}

func ListDcvcsByTenantWithAllZone(e *db.EngineWrapper, tenantID string) ([]*db.Dcvcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListDCVCs")
	defer dbRTMetrics.ObserveDurationAndLog()

	dcvcDatas := []*db.Dcvcs{}
	err := e.Engine.Where("tenant_id = ? and deleted = ?", tenantID, false).Find(&dcvcDatas)

	return dcvcDatas, err
}

func GetDcvcsCountByDcgwId(e *db.EngineWrapper, dcgwName string) (int64, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("FindAndCountDNATRules")
	defer dbRTMetrics.ObserveDurationAndLog()

	dcvcDatas := []db.Dcvcs{}
	return e.Where("dcgw_name = ? and deleted = ?", dcgwName, false).FindAndCount(&dcvcDatas)
}

func ListAllocateRemoteVxlanIPs(e *db.EngineWrapper, dcgwName string) ([]string, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListAllocateRemoteVxlanIPs")
	defer dbRTMetrics.ObserveDurationAndLog()

	ips := []string{}
	if err := e.SQL("SELECT remote_vxlan_ip  FROM dcvcs WHERE  dcgw_name = ? and deleted = false", dcgwName).Find(&ips); err != nil {
		return ips, err
	}

	return ips, nil
}
