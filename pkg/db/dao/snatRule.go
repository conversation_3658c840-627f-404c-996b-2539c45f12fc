package dao

import (
	"fmt"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

func AddSnatRule(e *db.EngineWrapper, rule *db.SnatRules) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddSNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.InsertOne(rule)
	return err
}

func GetSnatRule(e *db.EngineWrapper, ruleID string) (*db.SnatRules, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	rule := &db.SnatRules{}
	find, err := e.Where("snat_rule_id = ? and deleted = ?", ruleID, false).Get(rule)

	if err != nil {
		return rule, err
	}

	if !find {
		return rule, fmt.Errorf("SnatRule not found, snat_rule_id = %s", ruleID)
	}

	return rule, nil
}

func GetSnatRuleByName(e *db.EngineWrapper, ruleName string) (*db.SnatRules, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	rule := &db.SnatRules{}
	find, err := e.Where("name = ? and deleted = ?", ruleName, false).Get(rule)

	if err != nil {
		return rule, err
	}

	if !find {
		return rule, fmt.Errorf("SnatRule not found, name = %s", ruleName)
	}

	return rule, nil
}

func ExistSnatRule(e *db.EngineWrapper, eipID, name string) (bool, error) {
	return e.Where("deleted = false").Exist(&db.SnatRules{
		EIPID: eipID,
		Name:  name,
	})
}

func ListDefaultSnatRules(e *db.EngineWrapper, vpcID string) ([]*db.SnatRules, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListSNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	rules := []*db.SnatRules{}
	// only public eip has default SNAT rules
	err := e.SQL(`select * from snat_rules
		where deleted = false and inner_ip = '0.0.0.0/0' and protocol = 'ANY' and inner_port_max = 0 and eip_id in (
			select eip_id from eips where vpc_id = ? and internal_eip = false and deleted = false
		)`, vpcID).Find(&rules)

	if err != nil {
		return rules, err
	}

	return rules, nil
}

func ExistDefaultSnatRule(e *db.EngineWrapper, eipID, vpcID string) (bool, error) {
	return e.Where("deleted = false").Exist(&db.SnatRules{
		EIPID:        eipID,
		VPCID:        vpcID,
		InnerIP:      "0.0.0.0/0",
		Protocol:     "ANY",
		InnerPortMax: "0",
	})
}

func GetDefaultSnatRuleByName(e *db.EngineWrapper, eipID string) (*db.SnatRules, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	rule := &db.SnatRules{}
	find, err := e.Where("eip_id = ? and deleted = ? and inner_ip = '0.0.0.0/0' and protocol = 'ANY' and inner_port_max = 0", eipID, false).Get(rule)

	if err != nil {
		return rule, err
	}

	// if the record not exist, should return nil record, not error
	if !find {
		return nil, nil
	}

	return rule, nil
}

func ListSnatRules(e *db.EngineWrapper, eipID string) ([]*db.SnatRules, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListSNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	rules := []*db.SnatRules{}
	err := e.Where("eip_id = ? and deleted = ? ", eipID, false).Find(&rules)

	if err != nil {
		return rules, err
	}

	return rules, nil
}

func ListSnatRulesByVPC(e *db.EngineWrapper, vpcID string) ([]*db.SnatRules, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListSNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	rules := []*db.SnatRules{}
	err := e.Where("vpc_id = ? and deleted = ? ", vpcID, false).Find(&rules)

	if err != nil {
		return rules, err
	}

	return rules, nil
}

func UpdateSnatRule(e *db.EngineWrapper, rule *db.SnatRules) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateSNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.ID(rule.SnatRuleID).Update(rule)
	return err
}

func DeleteSnatRule(e *db.EngineWrapper, ruleID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteSNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update snat_rules set deleted = ?, state = ? where snat_rule_id = ? ", true, "DELETED", ruleID)
	return err
}

func SetSnatRuleState(e *db.EngineWrapper, ruleID, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetStateSNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update snat_rules set state = ? , deleted = ? where snat_rule_id = ? ", state, state == "DELETED", ruleID)
	return err
}

func SetSnatRulesState(e *db.EngineWrapper, eipID, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetStateSNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update snat_rules set state = ? , deleted = ? where eip_id = ? ", state, state == "DELETED", eipID)
	return err
}

// only used for internal EIP snat rules; for internal eip, only support one snat rules
func UpdateSnatRuleDstCidrs(e *db.EngineWrapper, eipID, dstCidrs string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateSNATRuleDstCidrs")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update snat_rules set dst_cidrs = ? where eip_id = ? and deleted = false", dstCidrs, eipID)
	return err
}

// for internal EIP, only has one undeleted snat rules
func GetSnatRuleByInternalEipID(e *db.EngineWrapper, eipID string) (*db.SnatRules, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetInternalEIPSNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	rule := &db.SnatRules{}
	find, err := e.Where("eip_id = ? and deleted = ? ", eipID, false).Get(rule)

	if err != nil {
		return rule, err
	}

	// if the record not exist, should return nil record, not error
	if !find {
		return nil, fmt.Errorf("not found undeleted snat rules for internal eip id=%s", eipID)
	}

	return rule, nil
}
