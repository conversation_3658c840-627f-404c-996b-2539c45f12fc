package dao

import (
	"fmt"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

func AddACL(e *db.EngineWrapper, acl *db.Acls) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.InsertOne(acl)
	return err
}

func GetACL(e *db.EngineWrapper, aclID string) (*db.Acls, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	acl := &db.Acls{}
	find, err := e.Where("acl_id = ? and deleted = ?", aclID, false).Get(acl)

	if err != nil {
		return acl, err
	}

	if !find {
		return acl, fmt.Errorf("ACL not found, acl_id = %s", aclID)
	}

	return acl, nil
}

func GetAllACLs(e *db.EngineWrapper, includeDeleted bool) ([]*db.Acls, error) {
	acls := []*db.Acls{}
	var err error
	if includeDeleted {
		err = e.Find(&acls)
	} else {
		err = e.Where("deleted = false").Find(&acls)
	}

	return acls, err
}

func GetACLByName(e *db.EngineWrapper, name string) (*db.Acls, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	acl := &db.Acls{}
	find, err := e.Where("name = ? and deleted = false", name).Get(acl)

	if err != nil {
		return acl, err
	}

	if !find {
		return acl, fmt.Errorf("ACL not found, name = %s", name)
	}

	return acl, nil
}

func ExistACL(e *db.EngineWrapper, name string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Where("deleted = false").Exist(&db.Acls{
		Name: name,
	})
}

func ListACLs(e *db.EngineWrapper, eipID string) ([]*db.Acls, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	acls := []*db.Acls{}
	err := e.Where("eip_id = ? and deleted = ? ", eipID, false).Find(&acls)

	if err != nil {
		return acls, err
	}

	return acls, nil
}

func ListACLsByVPCID(e *db.EngineWrapper, vpcID string) ([]*db.Acls, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	acls := []*db.Acls{}
	err := e.Where("vpc_id = ? and deleted = ? ", vpcID, false).Find(&acls)

	if err != nil {
		return acls, err
	}

	return acls, nil
}

func UpdateACL(e *db.EngineWrapper, acl *db.Acls) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.ID(acl.AclID).Update(acl)
	return err
}

func DeleteACL(e *db.EngineWrapper, aclID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update acls set deleted = true, state = ? where acl_id = ? ", "DELETED", aclID)
	return err
}

func SetACLState(e *db.EngineWrapper, aclID, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetACLState")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update acls set state = ?, deleted = ? where acl_id = ? ", state, state == "DELETED", aclID)
	return err
}

func SetACLStateByName(e *db.EngineWrapper, aclName, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetACLStateByName")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update acls set state = ?, deleted = ? where name = ? ", state, state == "DELETED", aclName)
	return err
}
