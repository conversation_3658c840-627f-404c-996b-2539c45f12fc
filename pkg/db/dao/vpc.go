package dao

import (
	"fmt"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

func GetVpcByName(e *db.EngineWrapper, vpcName string) (*db.Vpcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetVPC")
	defer dbRTMetrics.ObserveDurationAndLog()

	vpc := &db.Vpcs{}
	var err error
	find := false
	if find, err = e.Where("name = ? and deleted = ?", vpcName, false).Get(vpc); err != nil {
		return vpc, err
	}

	if !find {
		return nil, fmt.Errorf("VPC not found, name = %s", vpcName)
	}

	return vpc, nil
}

func GetVpc(e *db.EngineWrapper, id string) (*db.Vpcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetVPC")
	defer dbRTMetrics.ObserveDurationAndLog()

	vpcData := db.Vpcs{}
	has, err := e.ID(id).And("deleted = false").Get(&vpcData)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, fmt.Errorf("VPC not found, id = %s", id)
	}
	return &vpcData, nil
}

func GetAllVpcs(e *db.EngineWrapper) ([]*db.Vpcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListVPCs")
	defer dbRTMetrics.ObserveDurationAndLog()

	vpcDatas := []*db.Vpcs{}
	err := e.Where("deleted = false").Find(&vpcDatas)
	return vpcDatas, err
}

func CheckVpcExists(e *db.EngineWrapper, vpcName string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckVPC")
	defer dbRTMetrics.ObserveDurationAndLog()

	checkVpcData := db.Vpcs{}
	return e.Where("name = ? and deleted = ?", vpcName, false).Exist(&checkVpcData)
}

func DeleteVpc(e *db.SessionWrapper, vpcID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteVPC")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update vpcs set state = 'DELETED', deleted = true where vpc_id = ?", vpcID)
	return err
}

func AddVPC(e *db.EngineWrapper, vpc *db.Vpcs) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddVPC")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Insert(vpc)
	return err
}

func UpdateVPC(e *db.EngineWrapper, vpc *db.Vpcs) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateVPC")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.ID(vpc.VPCID).Update(vpc)
	return err
}

func GetVpcQuotaByVpcID(e *db.EngineWrapper, id string) (*db.VpcQuota, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetVPCQuota")
	defer dbRTMetrics.ObserveDurationAndLog()

	vpcQuota := db.VpcQuota{}
	has, err := e.Where("vpc_id = ?", id).Get(&vpcQuota)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, fmt.Errorf("VpcQuota not found, vpc_id = %s", id)
	}
	return &vpcQuota, nil
}

func AddVPCQuota(e *db.EngineWrapper, vpcQuota *db.VpcQuota) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddVPCQuota")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Insert(vpcQuota)
	return err
}

func DeleteVPCQuota(e *db.SessionWrapper, vpcID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddVPCQuota")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("delete from vpc_quota where vpc_id = ? ", vpcID)
	return err
}

func ListVpcsByTenant(e *db.EngineWrapper, tenantID string) ([]*db.Vpcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListVPCs")
	defer dbRTMetrics.ObserveDurationAndLog()

	vpcDatas := []*db.Vpcs{}
	err := e.Where("tenant_id = ? and deleted = ?", tenantID, false).Find(&vpcDatas)

	return vpcDatas, err
}

func ListVpcsByTenantWithAllZone(e *db.EngineWrapper, tenantID string) ([]*db.Vpcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListVPCs")
	defer dbRTMetrics.ObserveDurationAndLog()

	vpcDatas := []*db.Vpcs{}
	err := e.Engine.Where("tenant_id = ? and deleted = ?", tenantID, false).Find(&vpcDatas)

	return vpcDatas, err
}

func GetAllVpcsWithIDPrefix(e *db.EngineWrapper, idPrefix string) ([]*db.Vpcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListVPCs")
	defer dbRTMetrics.ObserveDurationAndLog()

	vpcDatas := []*db.Vpcs{}
	err := e.Where("deleted = false and id like ?", "%"+idPrefix+"%").Find(&vpcDatas)
	return vpcDatas, err
}

func GetVpcByID(e *db.EngineWrapper, vpcID string) (*db.Vpcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetVPC")
	defer dbRTMetrics.ObserveDurationAndLog()

	vpc := &db.Vpcs{}
	var err error
	find := false
	if find, err = e.Where("id = ? and deleted = ?", vpcID, false).Get(vpc); err != nil {
		return vpc, err
	}

	if !find {
		return nil, fmt.Errorf("VPC not found, id=%s", vpcID)
	}

	return vpc, nil
}
