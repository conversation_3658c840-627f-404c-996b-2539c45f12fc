package dao

import (
	"fmt"
	"sync"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

/*
TODO
在操作 db 的时候，全部都是没有加锁的
1. 在当下，几乎可以认为是慢速单用户操作，所以问题不大
2. 在后面，需要调整下，针对数据库的资源分配，需要统计设计加锁机制
*/

var slbMtx sync.Mutex

func GetSLBByResourceID(e *db.EngineWrapper, slbResourceID string, includeDeleted bool) (*db.Slbs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSLBByResourceID")
	defer dbRTMetrics.ObserveDurationAndLog()

	slb := &db.Slbs{}
	q := e.Where("id = ?", slbResourceID)
	if !includeDeleted {
		q.And("deleted = ?", false)
	}

	has, err := q.Get(slb)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, fmt.Errorf("slb %s not exist", slbResourceID)
	}

	return slb, nil
}

func CheckSLBByResourceId(e *db.EngineWrapper, resourceID string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckSLBByID")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Table("slbs").Where("id = ? and deleted = false", resourceID).Exist()
}

func GetSLBByName(e *db.EngineWrapper, slbName string, includeDeleted bool) (*db.Slbs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSLBByName")
	defer dbRTMetrics.ObserveDurationAndLog()

	slb := &db.Slbs{}
	q := e.Where("name = ?", slbName)
	if !includeDeleted {
		q.And("deleted = ?", false)
	}
	has, err := q.Get(slb)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, fmt.Errorf("slb %s not exist", slbName)
	}

	return slb, nil
}

func InsertSLB(e *db.EngineWrapper, slb db.Slbs) error {
	dbRTMetrics := exporter.InitDBRTMetrics("InsertSLB")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.InsertOne(&slb)
	return err
}

func UpdateSLB(e *db.EngineWrapper, slb db.Slbs) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateSLB")
	defer dbRTMetrics.ObserveDurationAndLog()

	slb.UpdateTime = timestamppb.Now().AsTime()
	_, err := e.ID(slb.SlbID).AllCols().Update(&slb)
	return err
}

func DeleteSLB(e *db.EngineWrapper, slbID string, deleteState string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteSLB")
	defer dbRTMetrics.ObserveDurationAndLog()
	_, err := e.Exec(
		"update slbs set state = ?, deleted = true, update_time = ? where slb_id = ?",
		deleteState, timestamppb.Now().AsTime(), slbID,
	)
	return err
}

func SetSLBState(e *db.EngineWrapper, slbID string, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetSLBState")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec(
		"update slbs set state = ?, update_time = ? where slb_id = ? ",
		state, timestamppb.Now().AsTime(), slbID,
	)
	return err
}

func AllocateSLBNetworkIP(e *db.EngineWrapper, ipType string) (*db.SlbExternalIpPools, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("AllocateSLBNetworkIP")
	defer dbRTMetrics.ObserveDurationAndLog()

	slbMtx.Lock()
	defer slbMtx.Unlock()

	ip := &db.SlbExternalIpPools{}
	has, err := e.Table("slb_external_ip_pools").
		Join(
			"INNER", "cidr_pools",
			"slb_external_ip_pools.cidr_id = cidr_pools.cidr_pool_id",
		).
		Where("slb_external_ip_pools.allocated = false and cidr_pools.zone_prp = ?", db.AZ).
		Get(ip)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, fmt.Errorf("Allocate SLB basic network ip fail. can not find available IP")
	}

	_, err = e.Exec(
		"update slb_external_ip_pools set allocated = true, type = ? where slb_external_ip_id = ?",
		ipType, ip.SlbExternalIpID,
	)
	// ip.Allocated = true
	// ip.Type = ipType
	// _, err = e.ID(ip.SlbExternalIpID).Update(ip) // 这种更新，allocated 总是不能更新
	if err != nil {
		return nil, err
	}

	return ip, nil
}

func ReleaseSLBNetworkIP(e *db.EngineWrapper, externalIpID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("ReleaseSLBNetworkIP")
	defer dbRTMetrics.ObserveDurationAndLog()

	ip := &db.SlbExternalIpPools{}
	has, err := e.Where("slb_external_ip_id = ? and allocated = ?", externalIpID, true).Get(ip)
	if err != nil {
		return err
	}
	if !has {
		return fmt.Errorf("Release SLB basic network ip fail. can not find IP:%s\n", externalIpID)
	}

	_, err = e.Exec(
		"update slb_external_ip_pools set allocated = false, type = ? where slb_external_ip_id = ?",
		"", ip.SlbExternalIpID,
	)
	// ip.Allocated = false
	// ip.Type = ""
	// _, err = e.ID(ip.SlbExternalIpID).Update(ip)
	if err != nil {
		return err
	}

	return nil
}

func GetSLBNetworkIPByID(e *db.EngineWrapper, externalIpID string) (*db.SlbExternalIpPools, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ReleaseSLBNetworkIP")
	defer dbRTMetrics.ObserveDurationAndLog()
	ip := &db.SlbExternalIpPools{}
	has, err := e.Where("slb_external_ip_id = ?", externalIpID).Get(ip)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, fmt.Errorf("Get SLB basic network ip fail. can not find record: %s\n", externalIpID)
	}

	return ip, nil
}

func GetSLBListenerByResourceId(e *db.EngineWrapper, listenerResourceId string, includeDeleted bool) (*db.SlbListeners, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSLBListenerByResourceId")
	defer dbRTMetrics.ObserveDurationAndLog()

	slbListener := &db.SlbListeners{}
	q := e.Where("id = ?", listenerResourceId)
	if !includeDeleted {
		q.And("deleted = ?", false)
	}
	has, err := q.Get(slbListener)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, fmt.Errorf("slb listener %s not exist", listenerResourceId)
	}

	return slbListener, nil
}

func CheckSLBListenerByResourceID(e *db.EngineWrapper, listenerResourceId string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckSLBListenerByResourceID")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Table("slb_listeners").Where(
		"id = ? and deleted = false", listenerResourceId,
	).Exist()
}

func InsertListener(e *db.EngineWrapper, listener db.SlbListeners) error {
	dbRTMetrics := exporter.InitDBRTMetrics("InsertListener")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.InsertOne(&listener)
	return err
}

func DeleteListener(e *db.EngineWrapper, slbID string, listenerID string, deleteState string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteListener")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec(
		"update slb_listeners set state = ?, deleted = true, update_time = ? where slb_id = ? and listener_id = ?",
		deleteState, timestamppb.Now().AsTime(), slbID, listenerID,
	)
	return err
}

func InsertSlbAssociations(e *db.EngineWrapper, assos []*db.SlbForwardRuleAssociateTargetGroups) error {
	dbRTMetrics := exporter.InitDBRTMetrics("InsertSlbAssociations")
	defer dbRTMetrics.ObserveDurationAndLog()

	if len(assos) == 0 {
		return nil
	}

	_, err := e.InsertMulti(&assos)
	return err
}

func DeleteSlbAssociations(e *db.EngineWrapper, asso db.SlbForwardRuleAssociateTargetGroups) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteSlbAssociations")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec(
		"update slb_forward_rule_associate_target_groups set deleted = true where forward_rule_id = ? and target_group_id = ?",
		asso.ForwardRuleID, asso.TargetGroupID,
	)

	return err
}

func ClearSlbAssociationsByForwardRuleIDs(e *db.EngineWrapper, ruleIDs []string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("ClearSlbAssociations")
	defer dbRTMetrics.ObserveDurationAndLog()

	if len(ruleIDs) == 0 {
		return nil
	}

	// fixme: 为啥这样不行？
	// condRuleIDs := []string{}
	// for _, ruleID := range ruleIDs {
	// 	condRuleIDs = append(condRuleIDs, fmt.Sprintf("'%s'", ruleID))
	// }

	// condKey := "forward_rule_id"
	// condValue := strings.Join(condRuleIDs, ",")
	// _, err := e.Exec(
	// 	"update slb_forward_rule_associate_target_groups set deleted = true where ? IN (?)",
	// 	condKey, condValue,
	// )

	condKey := "forward_rule_id"
	_, err := e.Table("slb_forward_rule_associate_target_groups").
		In(condKey, ruleIDs).
		Update(map[string]interface{}{"deleted": true})

	return err
}

func ListSlbAssociationsByForwardRule(
	e *db.EngineWrapper, ruleIDs []string, ruleType string,
) ([]*db.SlbForwardRuleAssociateTargetGroups, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListSlbAssociationsByForwardRule")
	defer dbRTMetrics.ObserveDurationAndLog()
	if len(ruleIDs) == 0 {
		return []*db.SlbForwardRuleAssociateTargetGroups{}, nil
	}

	assos := []*db.SlbForwardRuleAssociateTargetGroups{}
	err := e.Where("deleted = false and forward_rule_type = ?", ruleType).In("forward_rule_id", ruleIDs).Find(&assos)
	if err != nil {
		return nil, err
	}

	return assos, nil
}

func CheckTargetGroupIDs(e *db.EngineWrapper, slbID string, ids []string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckTargetGroupIDs")
	defer dbRTMetrics.ObserveDurationAndLog()
	if len(ids) == 0 {
		err := fmt.Errorf("CheckTargetGroupIDs slb:%s ids is nil", slbID)
		return false, err
	}

	datas := []*db.SlbTargetGroups{}
	err := e.Where(
		"slb_id = ? and deleted = false", slbID,
	).In("target_group_id", ids).Find(&datas)
	if err != nil {
		return false, err
	}

	if len(datas) != 0 {
		return true, nil
	}

	return false, nil
}

func ListTargetGroupsByID(e *db.EngineWrapper, ids []string) ([]*db.SlbTargetGroups, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckTargetGroupIDs")
	defer dbRTMetrics.ObserveDurationAndLog()

	if len(ids) == 0 {
		return []*db.SlbTargetGroups{}, nil
	}

	targetGroups := []*db.SlbTargetGroups{}
	err := e.Where("deleted = false").In("target_group_id", ids).Find(&targetGroups)
	return targetGroups, err
}

func ListSLBs(e *db.EngineWrapper, tenantID string) ([]*db.Slbs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListSLBs")
	defer dbRTMetrics.ObserveDurationAndLog()
	slbs := []*db.Slbs{}
	var err error
	if len(tenantID) != 0 {
		err = e.Where("deleted = false and tenant_id = ?", tenantID).Find(&slbs)
	} else {
		err = e.Where("deleted = false").Find(&slbs)
	}

	return slbs, err
}

func GetSLBListenersBySlbID(e *db.EngineWrapper, slbID string, includeDeleted bool) ([]*db.SlbListeners, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSLBListenersBySlbID")
	defer dbRTMetrics.ObserveDurationAndLog()

	slbListeners := []*db.SlbListeners{}
	q := e.Where("slb_id = ?", slbID)
	if !includeDeleted {
		q.And("deleted = ?", false)
	}
	err := q.Find(&slbListeners)
	if err != nil {
		return nil, err
	}

	return slbListeners, nil
}

func InsertTargetGroup(e *db.EngineWrapper, targetGroups db.SlbTargetGroups) error {
	dbRTMetrics := exporter.InitDBRTMetrics("InsertTargetGroup")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.InsertOne(&targetGroups)
	return err
}

func GetSLBTargetGroupByID(e *db.EngineWrapper, slbID string, targetGroupID string) (*db.SlbTargetGroups, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSLBTargetGroupByID")
	defer dbRTMetrics.ObserveDurationAndLog()

	targetGroup := &db.SlbTargetGroups{}
	has, err := e.Where("target_group_id = ? and slb_id = ?", targetGroupID, slbID).Get(targetGroup)
	if err != nil {
		return nil, err
	}

	if !has {
		err = fmt.Errorf("slb:%s targetGroup %s not exist", slbID, targetGroupID)
		return nil, err
	}

	return targetGroup, nil
}

func GetSLBTargetGroupByName(e *db.EngineWrapper, slbID string, targetGroupName string) (*db.SlbTargetGroups, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSLBTargetGroupByName")
	defer dbRTMetrics.ObserveDurationAndLog()

	targetGroup := &db.SlbTargetGroups{}
	has, err := e.Where("name = ? and slb_id = ?", targetGroupName, slbID).Get(targetGroup)
	if err != nil {
		return nil, err
	}

	if !has {
		err = fmt.Errorf("slb:%s targetGroup %s not exist", slbID, targetGroupName)
		return nil, err
	}

	return targetGroup, nil
}

func UpdateSlbListener(e *db.EngineWrapper, listener db.SlbListeners) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateSlbListener")
	defer dbRTMetrics.ObserveDurationAndLog()

	listener.UpdateTime = timestamppb.Now().AsTime()
	_, err := e.ID(listener.ListenerID).AllCols().Update(&listener)
	return err
}

func UpdateSlbTargetGroup(e *db.EngineWrapper, targetGroup db.SlbTargetGroups) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateSlbTargetGroup")
	defer dbRTMetrics.ObserveDurationAndLog()

	targetGroup.UpdateTime = timestamppb.Now().AsTime()
	_, err := e.ID(targetGroup.TargetGroupID).AllCols().Update(&targetGroup)
	return err
}

func ListSlbAssociationsByTargetGroupIDWithType(
	e *db.EngineWrapper, targetGroupIDs []string, forwardRuleType string,
) ([]*db.SlbForwardRuleAssociateTargetGroups, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListSlbAssociationsByTargetGroupIDWithType")
	defer dbRTMetrics.ObserveDurationAndLog()
	if len(targetGroupIDs) == 0 {
		return []*db.SlbForwardRuleAssociateTargetGroups{}, nil
	}

	assos := []*db.SlbForwardRuleAssociateTargetGroups{}
	err := e.Where(
		"deleted = false and forward_rule_type = ?", forwardRuleType,
	).In("target_group_id", targetGroupIDs).Find(&assos)
	if err != nil {
		return nil, err
	}

	return assos, nil
}

func ListSlbAssociationsByTargetGroupID(e *db.EngineWrapper, targetGroupIDs []string) ([]*db.SlbForwardRuleAssociateTargetGroups, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListSlbAssociationsByTargetGroupID")
	defer dbRTMetrics.ObserveDurationAndLog()
	if len(targetGroupIDs) == 0 {
		return []*db.SlbForwardRuleAssociateTargetGroups{}, nil
	}

	assos := []*db.SlbForwardRuleAssociateTargetGroups{}
	err := e.Where("deleted = false").In("target_group_id", targetGroupIDs).Find(&assos)
	if err != nil {
		return nil, err
	}

	return assos, nil
}

func ClearSlbAssociationsByTargetGroupIDs(e *db.EngineWrapper, targetGroupIDs []string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("ClearSlbAssociationsByTargetGroupIDs")
	defer dbRTMetrics.ObserveDurationAndLog()

	if len(targetGroupIDs) == 0 {
		return nil
	}

	condKey := "target_group_id"
	_, err := e.Table("slb_forward_rule_associate_target_groups").
		In(condKey, targetGroupIDs).
		Update(map[string]interface{}{"deleted": true})

	return err
}

func DeleteTargetGroup(e *db.EngineWrapper, slbID string, targetGroupID string, deleteState string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteTargetGroup")
	defer dbRTMetrics.ObserveDurationAndLog()
	_, err := e.Exec(
		"update slb_target_groups set state = ?, deleted = true, update_time = ? where slb_id = ? and target_group_id = ?",
		deleteState, timestamppb.Now().AsTime(), slbID, targetGroupID,
	)
	return err
}

func GetSLBTargetGroupsBySlbID(
	e *db.EngineWrapper, slbID string, includeDeleted bool,
) ([]*db.SlbTargetGroups, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSLBTargetGroupsBySlbID")
	defer dbRTMetrics.ObserveDurationAndLog()

	targetGroups := []*db.SlbTargetGroups{}
	q := e.Where("slb_id = ?", slbID)
	if !includeDeleted {
		q.And("deleted = ?", false)
	}
	err := q.Find(&targetGroups)
	if err != nil {
		return nil, err
	}

	return targetGroups, nil
}

func GetSLBTargetsByTargetGroupID(
	e *db.EngineWrapper, slbID string, targetGroupID string,
) ([]*db.SlbTargets, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSLBTargetsBySlbID")
	defer dbRTMetrics.ObserveDurationAndLog()

	targets := []*db.SlbTargets{}
	err := e.Where(
		"deleted = false and slb_id = ? and target_group_id = ?", slbID, targetGroupID,
	).Find(&targets)

	if err != nil {
		return nil, err
	}

	return targets, nil
}

func GetSLBTargetByResourceID(
	e *db.EngineWrapper, targetResourceID string,
) (*db.SlbTargets, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSLBTargetByResourceID")
	defer dbRTMetrics.ObserveDurationAndLog()

	target := &db.SlbTargets{}
	has, err := e.Where(
		"deleted = false and id = ? ", targetResourceID,
	).Get(target)
	if err != nil {
		return nil, err
	}

	if !has {
		err := fmt.Errorf("target:%s not exist", targetResourceID)
		return nil, err
	}

	return target, nil
}

func GetSLBTargetByNameHas(
	e *db.EngineWrapper, tgUid string, targetName string,
) (*db.SlbTargets, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSLBTargetByResourceID")
	defer dbRTMetrics.ObserveDurationAndLog()

	target := &db.SlbTargets{}
	has, err := e.Where(
		"deleted = false and target_group_id = ? and name = ?", tgUid, targetName,
	).Get(target)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, nil
	}

	return target, nil
}

func ListSLBListeners(e *db.EngineWrapper, slbID string, listenerIDs []string) ([]*db.SlbListeners, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListSLBListeners")
	defer dbRTMetrics.ObserveDurationAndLog()

	slbListeners := []*db.SlbListeners{}
	q := e.Where("slb_id = ? and deleted = false", slbID).In("listener_id", listenerIDs)
	err := q.Find(&slbListeners)
	if err != nil {
		return nil, err
	}

	return slbListeners, nil
}

func InsertTargets(e *db.EngineWrapper, targets []*db.SlbTargets) error {
	dbRTMetrics := exporter.InitDBRTMetrics("InsertTargets")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.InsertMulti(&targets)
	return err
}

func UpdateTarget(e *db.EngineWrapper, target *db.SlbTargets) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateTarget")
	defer dbRTMetrics.ObserveDurationAndLog()

	target.UpdateTime = timestamppb.Now().AsTime()
	_, err := e.ID(target.TargetID).AllCols().Update(target)
	return err
}

func DeleteTargets(
	e *db.EngineWrapper, slbID string, targetGroupID string, targetIDs []string, deleteState string,
) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteTargets")
	defer dbRTMetrics.ObserveDurationAndLog()

	condKey := "target_id"
	_, err := e.Table("slb_targets").
		In(condKey, targetIDs).
		Update(map[string]interface{}{
			"state":       deleteState,
			"deleted":     true,
			"update_time": timestamppb.Now().AsTime(),
		})

	return err
}

func ListSLBsByVpcId(e *db.EngineWrapper, vpcID string) ([]*db.Slbs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListSLBsByVpcId")
	defer dbRTMetrics.ObserveDurationAndLog()
	slbs := []*db.Slbs{}
	err := e.Where("deleted = false and vpc_id = ?", vpcID).Find(&slbs)
	return slbs, err
}

func ClearSlbAssociationsBySlbId(e *db.EngineWrapper, slbID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("ClearSlbAssociationsBySlbId")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec(
		"update slb_forward_rule_associate_target_groups set deleted = true where slb_id = ?", slbID,
	)

	return err
}

func ClearSlbListenerBySlbId(e *db.EngineWrapper, slbID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("ClearSlbListenerBySlbId")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec(
		"update slb_listeners set deleted = true where slb_id = ?", slbID,
	)

	return err
}

func ClearSlbTargetGroupBySlbId(e *db.EngineWrapper, slbID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("ClearSlbTargetGroupBySlbId")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec(
		"update slb_target_groups set deleted = true where slb_id = ?", slbID,
	)

	return err
}

func ClearSlbTargetBySlbId(e *db.EngineWrapper, slbID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("ClearSlbTargetBySlbId")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec(
		"update slb_targets set deleted = true where slb_id = ?", slbID,
	)

	return err
}

/*
TODO 优化
UPDATE slbs
SET

	description = CASE
	    WHEN name='slb-flynn-1-738a0004' THEN 'slb-flynn-1-738a0004'
	    WHEN name='slb-flynn-1-738a0003' THEN 'slb-flynn-1-738a0003'
	END,
	display_name = CASE
	    WHEN name='slb-flynn-1-738a0004' THEN 'slb-flynn-4'
	    WHEN name='slb-flynn-1-738a0003' THEN 'slb-flynn-3'
	END

WHERE name IN ('slb-flynn-1-738a0004', 'slb-flynn-1-738a0003');
*/
func SetListenerState(e *db.EngineWrapper, slbID string, listenerID string, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetListenerState")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec(
		"update slb_listeners set state = ?, update_time = ? where slb_id = ? and listener_id = ?",
		state, timestamppb.Now().AsTime(), slbID, listenerID,
	)
	return err
}

func SetTargetGroupState(e *db.EngineWrapper, slbID string, targetGroupID string, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetTargetGroupState")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec(
		"update slb_target_groups set state = ?, update_time = ? where slb_id = ? and target_group_id = ?",
		state, timestamppb.Now().AsTime(), slbID, targetGroupID,
	)
	return err
}

func SetTargetState(e *db.EngineWrapper, slbID string, targetGroupID string, targetID string, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetTargetState")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec(
		"update slb_targets set state = ?, update_time = ? where slb_id = ? and target_group_id = ? and target_id = ?",
		state, timestamppb.Now().AsTime(), slbID, targetGroupID, targetID,
	)
	return err
}

func GetSlbsAll(e *db.EngineWrapper, includeDeleted bool) ([]*db.Slbs, error) {
	datas := []*db.Slbs{}
	var err error
	if includeDeleted {
		err = e.Find(&datas)
	} else {
		err = e.Where("deleted = false").Find(&datas)
	}
	return datas, err
}

func GetSlbWithEip(e *db.EngineWrapper, eipID string) (*db.Slbs, error) {
	slb := &db.Slbs{}
	has, err := e.Where("eip_id = ? and deleted = false", eipID).Get(slb)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, nil
	}

	return slb, nil
}

func GetInternetSlbsRunningWithEip(e *db.EngineWrapper) ([]*db.Slbs, error) {
	datas := []*db.Slbs{}
	err := e.Where("load_balancer_type = 'INTERNET' AND deleted = false AND eip_id IS NOT NULL").Find(&datas)
	if err != nil {
		return nil, err
	}

	return datas, nil
}

func GetSLBTargetGroupByNameExist(e *db.EngineWrapper, slbID string, targetGroupName string) (*db.SlbTargetGroups, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSLBTargetGroupByNameExist")
	defer dbRTMetrics.ObserveDurationAndLog()

	targetGroup := &db.SlbTargetGroups{}
	has, err := e.Where("name = ? and slb_id = ?", targetGroupName, slbID).Get(targetGroup)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, nil
	}

	return targetGroup, nil
}

func SlbResourceId(subscription, resourceGroup, zone, slbName string) string {
	return fmt.Sprintf(
		"/subscriptions/%s/resourceGroups/%s/zones/%s/slbs/%s",
		subscription, resourceGroup, zone, slbName,
	)
}

func SlbListenerResourceId(subscription, resourceGroup, zone, slbName, listenerName string) string {
	return fmt.Sprintf(
		"subscriptions/%s/resourceGroups/%s/zones/%s/slbs/%s/listeners/%s",
		subscription, resourceGroup, zone, slbName, listenerName,
	)
}

func TargetGroupResourceID(subscription, resourceGroup, zone, slbName, targetGroupName string) string {
	return fmt.Sprintf(
		"subscriptions/%s/resourceGroups/%s/zones/%s/slbs/%s/targetGroups/%s",
		subscription, resourceGroup, zone, slbName, targetGroupName,
	)
}

func TargetResourceID(subscription, resourceGroup, zone, slbName, targetGroupName, targetName string) string {
	return fmt.Sprintf(
		"subscriptions/%s/resourceGroups/%s/zones/%s/slbs/%s/targetGroups/%s/targets/%s",
		subscription, resourceGroup, zone, slbName, targetGroupName, targetName,
	)
}

func TargetResourceIdByTargetGroupResourceID(targetGroupResourceId, targetName string) string {
	return fmt.Sprintf(
		"%s/targets/%s",
		targetGroupResourceId, targetName,
	)
}

func CheckTargetGroupResourceID(e *db.EngineWrapper, targetGroupResourceID string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckTargetGroupResourceID")
	defer dbRTMetrics.ObserveDurationAndLog()

	datas := []*db.SlbTargetGroups{}
	err := e.Where(
		"id = ? and deleted = false", targetGroupResourceID,
	).Find(&datas)
	if err != nil {
		return false, err
	}

	if len(datas) != 0 {
		return true, nil
	}

	return false, nil
}

func GetSLBTargetGroupByResourceId(e *db.EngineWrapper, targetGroupResourceId string) (*db.SlbTargetGroups, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSLBTargetGroupByID")
	defer dbRTMetrics.ObserveDurationAndLog()

	targetGroup := &db.SlbTargetGroups{}
	has, err := e.Where("id = ?", targetGroupResourceId).Get(targetGroup)
	if err != nil {
		return nil, err
	}

	if !has {
		err = fmt.Errorf("targetGroup %s not exist", targetGroupResourceId)
		return nil, err
	}

	return targetGroup, nil
}

func InsertHealthCheck(e *db.EngineWrapper, healthCheck *db.SlbHealthChecks) error {
	dbRTMetrics := exporter.InitDBRTMetrics("InsertHealthCheck")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.InsertOne(healthCheck)

	return err
}

func UpdateHealthCheck(e *db.EngineWrapper, healthCheck *db.SlbHealthChecks) error {
	dbRTMetrics := exporter.InitDBRTMetrics("InsertHealthCheck")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec(
		"update slb_health_checks set enable = ?, type = ?, timeout = ?, interval = ?, health_threshold = ?, unhealth_threshold = ?, health_check_config = ? where target_group_id = ?",
		healthCheck.Enable, healthCheck.Type, healthCheck.Timeout,
		healthCheck.Interval, healthCheck.HealthThreshold, healthCheck.UnhealthThreshold,
		healthCheck.HealthCheckConfig, healthCheck.TargetGroupID,
	)

	return err
}

func GetHealthCheckByTargetGroupId(e *db.EngineWrapper, tgId string) (*db.SlbHealthChecks, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetHealthCheckByTargetGroupId")
	defer dbRTMetrics.ObserveDurationAndLog()

	healthCheck := &db.SlbHealthChecks{}
	has, err := e.Where("target_group_id = ?", tgId).Get(healthCheck)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, fmt.Errorf("targetGroup %s not exist", tgId)
	}

	return healthCheck, nil
}

func DeleteHealthCheck(e *db.EngineWrapper, tgID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteSLB")
	defer dbRTMetrics.ObserveDurationAndLog()
	_, err := e.Exec(
		"update slb_health_checks set deleted = true where target_group_id = ?", tgID,
	)

	return err
}
