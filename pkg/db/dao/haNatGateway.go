package dao

import (
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

func ListHaNatGateways(e *db.EngineWrapper, vpcID string) ([]*db.HaNatGateways, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListHaNatGateways")
	defer dbRTMetrics.ObserveDurationAndLog()

	haNatGws := []*db.HaNatGateways{}
	err := e.Where("vpc_id = ? and deleted = false", vpcID).Find(&haNatGws)
	if err != nil {
		return haNatGws, err
	}

	return haNatGws, nil
}

func ListHaNatGatewaysBySubnetID(e *db.EngineWrapper, subnetID string) ([]*db.HaNatGateways, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListHaNatGatewaysBySubnetID")
	defer dbRTMetrics.ObserveDurationAndLog()

	natGws := []*db.HaNatGateways{}
	err := e.Where("subnet_id = ? and deleted = false", subnetID).Find(&natGws)
	if err != nil {
		return natGws, err
	}

	return natGws, nil
}

func GetHaNatGateway(e *db.EngineWrapper, natgwID string) (*db.HaNatGateways, bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetHaNatGateway")
	defer dbRTMetrics.ObserveDurationAndLog()

	natGW := db.HaNatGateways{}
	has, err := e.ID(natgwID).Where("deleted = false").Get(&natGW)
	if err != nil {
		return nil, false, err
	}

	return &natGW, has, nil
}

func GetHaNatGatewayByName(e *db.EngineWrapper, name string) (*db.HaNatGateways, bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetHaNatGatewayByName")
	defer dbRTMetrics.ObserveDurationAndLog()

	natGW := db.HaNatGateways{
		Name:    name,
		Deleted: false,
	}

	has, err := e.Get(&natGW)
	if err != nil {
		return nil, false, err
	}

	return &natGW, has, nil
}

func GetHaNatGatewayByVpcId(e *db.EngineWrapper, vpcId string) (*db.HaNatGateways, bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetHaNatGatewayByVpcId")
	defer dbRTMetrics.ObserveDurationAndLog()

	natGW := db.HaNatGateways{
		VpcID:   vpcId,
		Deleted: false,
	}

	has, err := e.Get(&natGW)
	if err != nil {
		return nil, false, err
	}

	return &natGW, has, nil
}

func GetNatGatewayExternalIpPoolByHaGwID(e *db.EngineWrapper, haGwId string) ([]db.NatGatewayExternalIpPools, int64, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetNatGatewayExternalIpPoolByHaGwID")
	defer dbRTMetrics.ObserveDurationAndLog()

	natGwIPData := []db.NatGatewayExternalIpPools{}
	count, err := e.Where("nat_gateway_id = ? ", haGwId).OrderBy("inet(nat_gw_external_ip)").FindAndCount(natGwIPData)
	if err != nil {
		return natGwIPData, 0, err
	}
	return natGwIPData, count, err
}

func DeleteHaNatGateway(e *db.EngineWrapper, ID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteHaNatGateway")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update ha_nat_gateways set state = 'DELETED', deleted = true where ha_nat_gateway_id = ?", ID)
	return err
}

func DeleteHaNatGateways(e *db.SessionWrapper, vpcID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteHaNatGateways")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update ha_nat_gateways set state = 'DELETED', deleted = true where vpc_id = ?", vpcID)
	return err
}

func AddHaNatGateway(e *db.SessionWrapper, haNatgw *db.HaNatGateways) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddHaNatGateway")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Insert(haNatgw)
	return err
}

func UpdateHaNatGateway(e *db.SessionWrapper, haNatgw *db.HaNatGateways) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateHaNatGateway")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.ID(haNatgw.HaNatGatewayId).Update(haNatgw)
	return err
}

func CheckHaNatGateway(e *db.SessionWrapper, name string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckHaNatGateway")
	defer dbRTMetrics.ObserveDurationAndLog()

	checkData := db.HaNatGateways{}
	return e.Where("name = ?", name).Exist(&checkData)
}

func DeleteHaNatGatewayByName(e *db.EngineWrapper, name string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteHaNatGatewayByName")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update ha_nat_gateways set state = 'DELETED', deleted = true where name = ?", name)
	return err
}

func SetHaNatGatewayState(e *db.EngineWrapper, haNatGwId, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetHaNatGatewayState")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update ha_nat_gateways set state = ?, deleted = ? where ha_nat_gateway_id = ? ", state, state == "DELETED", haNatGwId)
	return err
}

func SetHaNatGatewayBmsGwVip(e *db.EngineWrapper, haNatGwId string, bmsLanIp string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetHaNatGatewayBmsGwVip")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update ha_nat_gateways set bms_gw_vip = ?, deleted = false where ha_nat_gateway_id = ? ", bmsLanIp, haNatGwId)
	return err
}
