package dao

import (
	"fmt"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"time"
)

func GetDchcByName(e *db.EngineWrapper, dchcName string) (*db.Dchcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetDCHC")
	defer dbRTMetrics.ObserveDurationAndLog()

	dchc := &db.Dchcs{}
	var err error
	find := false
	if find, err = e.Where("name = ? and deleted = ?", dchcName, false).Get(dchc); err != nil {
		return dchc, err
	}

	if !find {
		return nil, fmt.Errorf("DCHC not found, name = %s", dchcName)
	}

	return dchc, nil
}

func GetDchcByDcvcName(e *db.EngineWrapper, dcvcName string) (*db.Dchcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetDchcByDcvcName")
	defer dbRTMetrics.ObserveDurationAndLog()

	dchcData := &db.Dchcs{}
	var err error
	find := false
	if find, err = e.Where("dcvc_name = ? and deleted = ?", dcvcName, false).Get(dchcData); err != nil {
		return dchcData, err
	}

	if !find {
		return nil, fmt.Errorf("DCHC not found, dcvc_name = %s", dcvcName)
	}

	return dchcData, nil
}

func ListDCVCGetDchcByDcvcName(e *db.EngineWrapper, dcvcName string) (*db.Dchcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetDchcByDcvcName")
	defer dbRTMetrics.ObserveDurationAndLog()

	dchcData := &db.Dchcs{}
	var err error
	find := false
	if find, err = e.Engine.Where("dcvc_name = ?", dcvcName).Get(dchcData); err != nil {
		return dchcData, err
	}

	if !find {
		return nil, fmt.Errorf("DCHC not found, dcvc_name = %s", dcvcName)
	}

	return dchcData, nil
}

func GetDchc(e *db.EngineWrapper, id string) (*db.Dchcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetDCHC")
	defer dbRTMetrics.ObserveDurationAndLog()

	dchcData := db.Dchcs{}
	has, err := e.ID(id).And("deleted = false").Get(&dchcData)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, fmt.Errorf("DCHC not found, id = %s", id)
	}
	return &dchcData, nil
}

func GetAllDchcs(e *db.EngineWrapper, includeDeleted bool) ([]*db.Dchcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListDCHCs")
	defer dbRTMetrics.ObserveDurationAndLog()

	dchcDatas := []*db.Dchcs{}
	var err error
	if includeDeleted {
		err = e.Find(&dchcDatas)
	} else {
		err = e.Where("deleted = false").Find(&dchcDatas)
	}
	return dchcDatas, err
}

func SetDchcDeletedByDcvcName(e *db.EngineWrapper, dcvcName string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetDeletedDCHCByDcvcName")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update dchcs set deleted = ? where dcvc_name = ? ", true, dcvcName)
	return err
}

func SetDchcRunResult(e *db.EngineWrapper, dcvcName, runStatus string, runTime time.Time) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetRunStatusRunTimeDCHC")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update dchcs set run_status = ?, run_time = ? where dcvc_name = ?", runStatus, runTime, dcvcName)
	return err
}

func ExistDchc(e *db.EngineWrapper, name string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDCHC")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Where("deleted = false").Exist(&db.Dchcs{
		Name: name,
	})
}

func ExistDchcByDcvcName(e *db.EngineWrapper, dcvcName string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDCHCByDcvcName")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Where("deleted = false").Exist(&db.Dchcs{
		DcvcName: dcvcName,
	})
}

func CheckDchcExists(e *db.EngineWrapper, dchcName string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDCHC")
	defer dbRTMetrics.ObserveDurationAndLog()

	checkDchcData := db.Dchcs{}
	return e.Where("name = ? and deleted = ?", dchcName, false).Exist(&checkDchcData)
}

func AddDCHC(e *db.EngineWrapper, dchc *db.Dchcs) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddDCHC")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Insert(dchc)
	return err
}

func UpdateDCHC(e *db.EngineWrapper, dchc *db.Dchcs) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateDCHC")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.ID(dchc.DchcID).AllCols().Update(dchc)
	return err
}

func ListDchcsByTenant(e *db.EngineWrapper, tenantID string) ([]*db.Dchcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListDCHCs")
	defer dbRTMetrics.ObserveDurationAndLog()

	dchcDatas := []*db.Dchcs{}
	err := e.Where("tenant_id = ? and deleted = ?", tenantID, false).Find(&dchcDatas)

	return dchcDatas, err
}

func ListDchcsByTenantWithAllZone(e *db.EngineWrapper, tenantID string) ([]*db.Dchcs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListDCHCs")
	defer dbRTMetrics.ObserveDurationAndLog()

	dchcDatas := []*db.Dchcs{}
	err := e.Where("tenant_id = ? and deleted = ?", tenantID, false).Find(&dchcDatas)

	return dchcDatas, err
}
