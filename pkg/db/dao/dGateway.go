package dao

import (
	"fmt"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

func AddDGateway(e *db.EngineWrapper, gw *db.DistributeGateways) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddDGW")
	_, err := e.InsertOne(gw)
	dbRTMetrics.ObserveDurationAndLog()
	return err
}

func GetDGateway(e *db.EngineWrapper, gwID string) (*db.DistributeGateways, error) {
	gw := &db.DistributeGateways{}
	var err error
	find := false
	dbRTMetrics := exporter.InitDBRTMetrics("GetDGW")
	if find, err = e.Where("distribute_gateway_id = ? and deleted = ?", gwID, false).Get(gw); err != nil {
		dbRTMetrics.ObserveDurationAndLog()
		return gw, err
	}

	dbRTMetrics.ObserveDurationAndLog()
	if !find {
		return gw, fmt.Errorf("DistributeGateway not found, distribute_gateway_id = %s", gwID)
	}

	return gw, nil
}

func GetAllDGateway(e *db.EngineWrapper, includeDeleted bool) ([]*db.DistributeGateways, error) {
	dgwDatas := []*db.DistributeGateways{}
	var err error
	if includeDeleted {
		err = e.Find(&dgwDatas)
	} else {
		err = e.Where("deleted = false").Find(&dgwDatas)
	}
	return dgwDatas, err
}

func GetDGatewayByName(e *db.EngineWrapper, gwName string) (*db.DistributeGateways, bool, error) {
	gw := &db.DistributeGateways{}
	var err error
	find := false
	dbRTMetrics := exporter.InitDBRTMetrics("AddDGW")
	if find, err = e.Where("name = ? and deleted = ?", gwName, false).Get(gw); err != nil {
		dbRTMetrics.ObserveDurationAndLog()
		return gw, false, err
	}

	dbRTMetrics.ObserveDurationAndLog()

	return gw, find, nil
}

func Exists(e *db.EngineWrapper, IP string) (bool, error) {
	return e.Exist(&db.DistributeGateways{
		GwExternalIP: IP,
		Deleted:      false,
	})
}

func UpdateDGateway(e *db.EngineWrapper, gw *db.DistributeGateways) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateDGW")
	_, err := e.ID(gw.DistributeGatewayID).Update(gw)
	dbRTMetrics.ObserveDurationAndLog()
	return err
}

func ListDGateways(e *db.EngineWrapper, vpcID string) ([]*db.DistributeGateways, error) {
	natGws := []*db.DistributeGateways{}
	dbRTMetrics := exporter.InitDBRTMetrics("ListDGW")
	err := e.Where("vpc_id = ? and deleted = false", vpcID).Find(&natGws)
	dbRTMetrics.ObserveDurationAndLog()
	if err != nil {
		return natGws, err
	}

	return natGws, nil
}

func DeleteDGateways(e *db.EngineWrapper, vpcID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteDGW")
	_, err := e.Exec("update distribute_gateways set state = 'DELETED', deleted = true where vpc_id = ?", vpcID)
	dbRTMetrics.ObserveDurationAndLog()
	return err
}

func SetDGatewayState(e *db.EngineWrapper, gwID, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetStateDGW")
	_, err := e.Exec("update distribute_gateways set state = ?, deleted = ? where distribute_gateway_id = ? ", state, state == "DELETED", gwID)
	dbRTMetrics.ObserveDurationAndLog()
	return err
}

func SetDGatewayStateByName(e *db.EngineWrapper, name, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetStateDGW")
	_, err := e.Exec("update distribute_gateways set state = ?,  deleted = ?  where name = ?", state, state == "DELETED", name)
	dbRTMetrics.ObserveDurationAndLog()
	return err
}

func ListGatewayIPs(e *db.EngineWrapper) ([]string, error) {
	ips := []string{}
	dbRTMetrics := exporter.InitDBRTMetrics("ListDGW")
	if err := e.SQL("select gw_external_ip from distribute_gateways where deleted = ?", false).Find(&ips); err != nil {
		dbRTMetrics.ObserveDurationAndLog()
		return ips, err
	}
	dbRTMetrics.ObserveDurationAndLog()
	return ips, nil
}

func GetDGatewayByID(e *db.EngineWrapper, gwID string) (*db.DistributeGateways, bool, error) {
	gw := &db.DistributeGateways{}
	var err error
	find := false
	dbRTMetrics := exporter.InitDBRTMetrics("AddDGW")
	if find, err = e.Where("id = ? and deleted = ?", gwID, false).Get(gw); err != nil {
		dbRTMetrics.ObserveDurationAndLog()
		return gw, false, err
	}

	dbRTMetrics.ObserveDurationAndLog()

	return gw, find, nil
}
