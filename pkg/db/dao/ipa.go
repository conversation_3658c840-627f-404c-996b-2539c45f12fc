package dao

import (
	"fmt"

	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/ipa/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

func AddIPA(e *db.EngineWrapper, ipa *db.Ipas) error {
	_, err := e.InsertOne(ipa)
	return err
}

func GetIPA(e *db.EngineWrapper, ipaID string) (*db.Ipas, error) {
	ipa := &db.Ipas{}
	var err error
	find := false
	if find, err = e.Where("ipa_id = ? and deleted = ?", ipaID, false).Get(ipa); err != nil {
		return ipa, err
	}

	if !find {
		return ipa, fmt.Errorf("IPA not found, ipa_id = %s", ipaID)
	}

	return ipa, nil
}

func GetIPAByMac(e *db.EngineWrapper, mac string) (*db.Ipas, bool, error) {
	ipa := &db.Ipas{}
	var err error
	find := false
	if find, err = e.Where("mac = ? and deleted = ?", mac, false).Get(ipa); err != nil {
		return ipa, find, err
	}

	return ipa, find, nil
}

func Exist(e *db.EngineWrapper, subnetID, IP string) (bool, error) {
	return e.Where("deleted = false").Exist(&db.Ipas{
		SubnetID: subnetID,
		IP:       IP,
	})
}

func GetAllIPAs(e *db.EngineWrapper, includeDeleted bool) ([]*db.Ipas, error) {
	ipaDatas := []*db.Ipas{}
	var err error
	if includeDeleted {
		err = e.Find(&ipaDatas)
	} else {

		err = e.Where("deleted = false").Find(&ipaDatas)
	}
	return ipaDatas, err
}

func ListIPAs(e *db.EngineWrapper, subnetID string) ([]*db.Ipas, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListIPAs")
	defer dbRTMetrics.ObserveDurationAndLog()

	ipas := []*db.Ipas{}
	if err := e.Where("subnet_id = ? and deleted = ? ", subnetID, false).Find(&ipas); err != nil {
		return ipas, err
	}

	return ipas, nil
}

func UpdateIPA(e *db.EngineWrapper, ipa *db.Ipas) error {
	_, err := e.ID(ipa.IpaID).Update(ipa)
	return err
}

func DeleteIPA(e *db.EngineWrapper, ipaID string) error {
	_, err := e.Exec("update ipas set deleted = ?, state = DELETED where ipa_id = ? ", true, ipaID)
	return err
}

func SetIPAState(e *db.EngineWrapper, ipaID, state string) error {
	_, err := e.Exec("update ipas set state = ?, deleted = ? where ipa_id = ? ", state, state == network.IPA_DELETED.String(), ipaID)
	return err
}

func ListSubnetIPs(e *db.EngineWrapper, subnetID string) ([]string, error) {
	ips := []string{}
	if err := e.SQL("select ip from ipas where subnet_id = ? and deleted = ?", subnetID, false).Find(&ips); err != nil {
		return ips, err
	}

	return ips, nil
}
