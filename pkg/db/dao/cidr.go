package dao

import (
	"errors"
	"fmt"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

func GetCidrPoolByID(e *db.EngineWrapper, id string) (db.CidrPools, bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetCIDRPool")
	defer dbRTMetrics.ObserveDurationAndLog()

	cidr := db.CidrPools{}
	has, err := e.Where("cidr_pool_id = ?", id).Get(&cidr)
	return cidr, has, err
}

func GetCidrPoolByIDInSession(e *db.SessionWrapper, id string) (db.CidrPools, bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetCIDRPool")
	defer dbRTMetrics.ObserveDurationAndLog()

	cidr := db.CidrPools{}
	has, err := e.Where("cidr_pool_id = ?", id).Get(&cidr)
	return cidr, has, err
}

func GetCidrPoolByCidrID(e *db.EngineWrapper, id string) (*db.CidrPools, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetCIDRPool")
	defer dbRTMetrics.ObserveDurationAndLog()

	cidr := db.CidrPools{}
	has, err := e.Where("cidr_pool_id = ?", id).Get(&cidr)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, fmt.Errorf("CIDR pool not found, cidr_id = %s", id)
	}
	return &cidr, err
}

func FetchCidrPool(e *db.EngineWrapper, scope, networkType, gw string, vni int) (db.CidrPools, error) {
	poolMutex.Lock()
	defer poolMutex.Unlock()

	dbRTMetrics := exporter.InitDBRTMetrics("FetchCIDRPool")
	defer dbRTMetrics.ObserveDurationAndLog()

	cidrData := db.CidrPools{}
	if vni > 0 {
		cidrData.VNI = vni
	}

	has, err := e.Where("scope = ? and network_type = ? and allocated = ? and gateway_ip = ?", scope, networkType, false, gw).Get(&cidrData)
	if err != nil {
		return cidrData, err
	}
	if !has {
		err = errors.New("no allocatable cidr found.")
	}

	return cidrData, err
}

func SetCidrAllocatedByPoolID(e *db.EngineWrapper, id string, allocated bool) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetCIDRAllocated")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update cidr_pools set allocated = ? where cidr_pool_id = ?", allocated, id)
	return err
}

func SetCidrAllocatedByID(s *db.SessionWrapper, id string, allocated bool) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetCIDRAllocated")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := s.Exec("update cidr_pools set allocated = ? where cidr_pool_id = ?", allocated, id)
	return err
}

func AddCidrPool(e *db.EngineWrapper, pool *db.CidrPools) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddCIDRPool")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.InsertOne(pool)
	return err
}

func DeleteCidrPool(s *db.SessionWrapper, cidrPoolID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteCIDRPool")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := s.Exec("delete from cidr_pools where cidr_pool_id = ? ", cidrPoolID)
	return err
}

func AllocateCidrPool(e *db.EngineWrapper, scope, networkType string) (*db.CidrPools, error) {
	poolMutex.Lock()
	defer poolMutex.Unlock()

	pool := &db.CidrPools{}
	find, err := e.Where("scope = ? and network_type = ? and allocated = false", scope, networkType).OrderBy("vni").Get(pool)
	if err != nil {
		return pool, err
	}

	if !find {
		return pool, fmt.Errorf("No proper cidr pool, scope = %s and network_type = %s", scope, networkType)
	}

	if _, err := e.Exec("update cidr_pools set allocated = true where cidr_pool_id = ?", pool.CIDRPoolID); err != nil {
		return pool, err
	}

	pool.Allocated = true
	return pool, nil
}

func ReleaseCidrPool(e *db.SessionWrapper, cidrPoolID string) error {
	poolMutex.Lock()
	defer poolMutex.Unlock()

	if _, err := e.Exec("update cidr_pools set allocated = false where cidr_pool_id = ?", cidrPoolID); err != nil {
		return err
	}
	return nil
}

func FetchCIDRPoolsWithGws(e *db.EngineWrapper, scope, networkType string, gws []string, vni int) ([]*db.CidrPools, error) {
	poolMutex.Lock()
	defer poolMutex.Unlock()

	dbRTMetrics := exporter.InitDBRTMetrics("FetchCIDRPoolsWithGws")
	defer dbRTMetrics.ObserveDurationAndLog()

	cidrPools := []*db.CidrPools{}
	err := e.Where(
		"scope = ? and network_type = ? and allocated = ? and vni = ?",
		scope, networkType, false, vni,
	).In("gateway_ip", gws).Find(&cidrPools)
	if err != nil {
		return nil, err
	}

	return cidrPools, nil
}
