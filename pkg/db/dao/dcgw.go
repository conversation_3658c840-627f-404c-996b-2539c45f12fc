package dao

import (
	"fmt"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

func GetDcgwByName(e *db.EngineWrapper, dcgwName string) (*db.Dcgws, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetDCGW")
	defer dbRTMetrics.ObserveDurationAndLog()

	dcgw := &db.Dcgws{}
	var err error
	find := false
	if find, err = e.Where("name = ? and deleted = ?", dcgwName, false).Get(dcgw); err != nil {
		return dcgw, err
	}

	if !find {
		return nil, fmt.Errorf("DCGW not found, name = %s", dcgwName)
	}

	return dcgw, nil
}

func GetDcgw(e *db.EngineWrapper, id string) (*db.Dcgws, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetDCGW")
	defer dbRTMetrics.ObserveDurationAndLog()

	dcgwData := db.Dcgws{}
	has, err := e.ID(id).And("deleted = false").Get(&dcgwData)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, fmt.Errorf("DCGW not found, id = %s", id)
	}
	return &dcgwData, nil
}

func GetAllDcgws(e *db.EngineWrapper) ([]*db.Dcgws, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListDCGWs")
	defer dbRTMetrics.ObserveDurationAndLog()

	dcgwDatas := []*db.Dcgws{}
	err := e.Where("deleted = false").Find(&dcgwDatas)
	return dcgwDatas, err
}

func SetDcgwState(e *db.EngineWrapper, name, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetPCCState")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update dcgws set state = ?, deleted = ? where name = ? ", state, state == "DELETED", name)
	return err
}

func ExistDcgw(e *db.EngineWrapper, name string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDCGW")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Where("deleted = false").Exist(&db.Dcgws{
		Name: name,
	})
}
func ExistDcgwResource(e *db.EngineWrapper, name, subscriptionName, resourceGroupName string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDCGW")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Where("deleted = false").Exist(&db.Dcgws{
		Name:              name,
		SubscriptionName:  subscriptionName,
		ResourceGroupName: resourceGroupName,
	})
}

func CheckDcgwExistsByVpcId(e *db.EngineWrapper, vpcId string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDcgwExistsByVpcId")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Where("deleted = false").Exist(&db.Dcgws{
		VPCID: vpcId,
	})
}

func DeleteDcgw(e *db.SessionWrapper, dcgwID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteDCGW")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update dcgws set state = 'DELETED', deleted = true where dcgw_id = ?", dcgwID)
	return err
}

func AddDCGW(e *db.EngineWrapper, dcgw *db.Dcgws) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddDCGW")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Insert(dcgw)
	return err
}

func UpdateDCGW(e *db.EngineWrapper, dcgw *db.Dcgws) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateDCGW")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.ID(dcgw.DcgwID).Update(dcgw)
	return err
}

func ListDcgwsByTenant(e *db.EngineWrapper, tenantID string) ([]*db.Dcgws, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListDCGWs")
	defer dbRTMetrics.ObserveDurationAndLog()

	dcgwDatas := []*db.Dcgws{}
	err := e.Engine.Where("tenant_id = ? and deleted = ?", tenantID, false).Find(&dcgwDatas)

	return dcgwDatas, err
}

func ListDcgwsByTenantWithAllZone(e *db.EngineWrapper, tenantID string) ([]*db.Dcgws, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListDCGWs")
	defer dbRTMetrics.ObserveDurationAndLog()

	dcgwDatas := []*db.Dcgws{}
	err := e.Engine.Where("tenant_id = ? and deleted = ?", tenantID, false).Find(&dcgwDatas)

	return dcgwDatas, err
}
