package test

import (
	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
)

func Add(e *db.EngineWrapper, n int) error {
	age := bosonTestAges{}
	has, err := e.Where("age_id=1").Get(&age)
	if err != nil {
		logrus.Error("Query error:", err)
		return err
	}
	if has {
		age.Age++
	}
	_, err = e.Update(age)
	if err != nil {
		logrus.Error("Update error:", err)
		return err
	}

	has, err = e.Where("age_id=1").Get(&age)
	if err != nil {
		logrus.Error("Query error:", err)
		return err
	}

	if has {
		logrus.Infof("age=%d", age.Age)
	} else {
		logrus.Info("empty record")
	}
	return nil
}
