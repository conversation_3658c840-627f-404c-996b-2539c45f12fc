package dao

import (
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

func AddNamespace(e *db.EngineWrapper, ns *db.K8sNamespaces) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddNamespace")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.InsertOne(ns)
	return err
}

func ListNamespaces(e *db.EngineWrapper, vpcID string) ([]*db.K8sNamespaces, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListNamespaces")
	defer dbRTMetrics.ObserveDurationAndLog()

	nss := []*db.K8sNamespaces{}
	if err := e.Where("vpc_id = ? ", vpcID).Find(&nss); err != nil {
		return nss, err
	}

	return nss, nil
}

func ListNamespacesBySubnet(e *db.EngineWrapper, subnetID string) ([]*db.K8sNamespaces, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListNamespaces")
	defer dbRTMetrics.ObserveDurationAndLog()

	nss := []*db.K8sNamespaces{}
	if err := e.Where("subnet_id = ? ", subnetID).Find(&nss); err != nil {
		return nss, err
	}

	return nss, nil
}

func DeleteNamespace(s *db.SessionWrapper, namespaceID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteNamespace")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := s.Exec("delete from k8s_namespaces where namespace_id = ? ", namespaceID)
	return err
}

func DeleteNamespaceBySubnet(e *db.EngineWrapper, subnetID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteNamespace")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("delete from k8s_namespaces where sub_id = ? ", subnetID)
	return err
}

func DeleteNamespaces(s *db.SessionWrapper, vpcID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteNamespaces")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := s.Exec("delete from k8s_namespaces where vpc_id = ? ", vpcID)
	return err
}
