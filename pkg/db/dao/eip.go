package dao

import (
	"fmt"

	"github.com/sirupsen/logrus"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

func AddEip(e *db.EngineWrapper, eip *db.Eips) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddEIP")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.InsertOne(eip)
	return err
}

func CheckEipByVpcID(e *db.EngineWrapper, vpcID string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckEipByVpcID")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Table("eips").Where("vpc_id = ? and deleted = false", vpcID).Exist()
}

func CheckEipByName(e *db.EngineWrapper, name string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckEipByName")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Table("eips").Where("name = ? and deleted = false", name).Exist()
}

func GetAllEIPsWithIDPrefix(e *db.EngineWrapper, idPrefix string) ([]*db.Eips, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListEIPs")
	defer dbRTMetrics.ObserveDurationAndLog()

	eipDatas := []*db.Eips{}
	err := e.Where("deleted = false and id like ?", "%"+idPrefix+"%").Find(&eipDatas)
	return eipDatas, err
}

func GetEipByResourceID(e *db.EngineWrapper, ID string) (*db.Eips, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetEIP")
	defer dbRTMetrics.ObserveDurationAndLog()

	eip := &db.Eips{}
	var err error
	find := false
	if find, err = e.Where("id = ? and deleted = ?", ID, false).Get(eip); err != nil {
		return eip, err
	}

	if !find {
		return nil, fmt.Errorf("EIP not found, id = %s", ID)
	}

	return eip, nil
}

func GetEipByID(e *db.EngineWrapper, eipID string, ignoreDeleteRecord bool) (*db.Eips, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetEIP")
	defer dbRTMetrics.ObserveDurationAndLog()

	if eipID == "" {
		return nil, fmt.Errorf("eip id is nil")
	}

	eipData := db.Eips{}
	q := e.Where("eip_id = ?", eipID)
	if ignoreDeleteRecord {
		q = q.And("deleted = ?", false)
	}
	has, err := q.Get(&eipData)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, fmt.Errorf("EIP %v not found", eipID)
	}

	return &eipData, nil
}

func GetEipByNameRetry(e *db.EngineWrapper, eipName string) (*db.Eips, error) {
	var eip *db.Eips
	var err error
	for i := 0; i < 3; i++ {
		eip, err = GetEipByName(e, eipName)
		if err == nil {
			break
		}
		logrus.Errorln("GetEipByName Retry...", eipName, err)
	}
	return eip, err
}
func GetEipByName(e *db.EngineWrapper, eipName string) (*db.Eips, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetEIP")
	defer dbRTMetrics.ObserveDurationAndLog()

	eip := &db.Eips{}
	has, err := e.Where("name = ?", eipName).Get(eip)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, fmt.Errorf("eip %v not found", eipName)
	}

	return eip, nil
}

func ListEips(e *db.EngineWrapper, vpcID string) ([]*db.Eips, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListEIP")
	defer dbRTMetrics.ObserveDurationAndLog()

	eips := []*db.Eips{}
	err := e.Where("vpc_id = ? and deleted = ? ", vpcID, false).Find(&eips)

	return eips, err
}

func ListEipsByTenantID(e *db.EngineWrapper, tenantID string) ([]*db.Eips, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListEIP")
	defer dbRTMetrics.ObserveDurationAndLog()

	eips := []*db.Eips{}
	err := e.Where("tenant_id = ? and deleted = ? ", tenantID, false).Find(&eips)

	return eips, err
}

func ListEipsByTenantIDWithAllZone(e *db.EngineWrapper, tenantID string) ([]*db.Eips, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListEIP")
	defer dbRTMetrics.ObserveDurationAndLog()

	eips := []*db.Eips{}
	err := e.Engine.Where("tenant_id = ? and deleted = ? ", tenantID, false).Find(&eips)

	return eips, err
}

func GetAllEIPs(e *db.EngineWrapper, includeDeleted bool) ([]*db.Eips, error) {
	eipDatas := []*db.Eips{}
	var err error
	if includeDeleted {
		err = e.Find(&eipDatas)
	} else {
		err = e.Where("deleted = false").Find(&eipDatas)
	}
	return eipDatas, err
}

func UpdateEip(e *db.EngineWrapper, eip *db.Eips) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateEIP")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.ID(eip.EIPID).Update(eip)
	return err
}

func DeleteEip(e *db.EngineWrapper, eipID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteEIP")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update eips set deleted = ?, state = DELETED where eip_id = ? ", true, eipID)
	return err
}

func GetEipPool(e *db.EngineWrapper, id string) (*db.EipPools, error) {
	eipPoolData := db.EipPools{}
	has, err := e.Where("eip_pool_id = ?", id).Get(&eipPoolData)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, fmt.Errorf("EIP pool [id %v] not found", id)
	}

	return &eipPoolData, nil
}

func AllocEip(e *db.EngineWrapper, line string, num uint32) (*[]db.EipPools, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("AllocEip")
	defer dbRTMetrics.ObserveDurationAndLog()

	eipPoolData := []db.EipPools{}
	err := e.SQL(`
		select * from eip_pools
		where cidr = (
			select cidr from eip_pools
			where allocated = ? and line = ?
			group by cidr
			having count(*) >= ?
			limit 1
		) and line = ? and allocated = ?
		order by inet(eip_pools.eip_ip)
		limit ?`, false, line, num, line, false, num).
		Find(&eipPoolData)
	if err != nil {
		return nil, err
	}
	if len(eipPoolData) != int(num) {
		err = fmt.Errorf("allocatable EIP sku line %s not enough, allocate=%d, need=%d", line, len(eipPoolData), num)
		return nil, err
	}

	return &eipPoolData, nil
}

func SetEipState(e *db.EngineWrapper, eipID, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetStateEIP")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update eips set state = ?, deleted = ? where eip_id = ? ", state, state == "DELETED", eipID)
	return err
}

func SetEipPoolAllocated(e *db.EngineWrapper, eipPoolIDs []string, allocated bool) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateEIPPools")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.In("eip_pool_id", eipPoolIDs).Cols("allocated").Update(&db.EipPools{Allocated: allocated})
	return err
}

func SetEipPoolAllocatedByEIPs(e *db.EngineWrapper, eips []string, allocated bool) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateEIPPools")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.In("eip_ip", eips).Cols("allocated").Update(&db.EipPools{Allocated: allocated})
	return err
}

func GetNatGatewayExternalIpPool(e *db.EngineWrapper, id string) (*db.NatGatewayExternalIpPools, bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetNATGatewayExternalIP")
	defer dbRTMetrics.ObserveDurationAndLog()

	natGwIPData := db.NatGatewayExternalIpPools{}
	has, err := e.Where("nat_gw_external_ip_id = ?", id).Get(&natGwIPData)

	return &natGwIPData, has, err
}

func UpdateEipBw(e *db.EngineWrapper, eipID string, bw int32) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateEIP")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update eips set bw = ?, upstream_bw = ?, downstream_bw = ? where eip_id = ?", bw, bw, bw, eipID)
	return err
}

func UpdateEipAssociationID(e *db.EngineWrapper, eipID string, associationID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateEipAssociationID")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update eips set association_id = ? where eip_id = ?", associationID, eipID)
	return err
}

func GetSlbTypedEIPs(e *db.EngineWrapper) ([]*db.Eips, error) {
	eipDatas := []*db.Eips{}
	err := e.SQL(`select * from eips where deleted = false and association_type = 'SLB'`).Find(&eipDatas)
	if err != nil {
		return nil, err
	}

	return eipDatas, err
}

func GetTenantEipLine(e *db.EngineWrapper, tenantID string) (*db.TenantSkuLine, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetTenantEipLine")
	defer dbRTMetrics.ObserveDurationAndLog()

	tenantLine := db.TenantSkuLine{}
	has, err := e.Where("tenant_id = ? and deleted = false", tenantID).Get(&tenantLine)
	if err != nil {
		return nil, err
	}

	if !has {
		logrus.Infof("tenant %v not bind any Eip line", tenantID)
		return nil, nil
	}

	return &tenantLine, nil
}

func GetEipSkuLineMeta(e *db.EngineWrapper) (map[string][]*db.SkuLineMeta, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetEipSkuLine")
	defer dbRTMetrics.ObserveDurationAndLog()

	skuLines := []*db.SkuLineMeta{}
	err := e.Where("deleted = false").Find(&skuLines)
	if err != nil {
		return nil, err
	}

	res := make(map[string][]*db.SkuLineMeta)
	for i, each := range skuLines {
		if _, ok := res[each.Sku]; !ok {
			res[each.Sku] = []*db.SkuLineMeta{}
		}

		res[each.Sku] = append(res[each.Sku], skuLines[i])
	}

	return res, err
}
