package dao

import (
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

func InsertBosonService(e *db.EngineWrapper, service db.BosonServices) error {
	dbRTMetrics := exporter.InitDBRTMetrics("InsertBosonService")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.InsertOne(&service)

	return err
}

func GetBosonService(e *db.EngineWrapper, serviceName string) (*db.BosonServices, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetBosonService")
	defer dbRTMetrics.ObserveDurationAndLog()

	s := &db.BosonServices{}
	has, err := e.Where("service_name = ?", serviceName).And("deleted = ?", false).Get(s)
	if err != nil {
		return nil, err
	}

	if !has {
		return nil, nil
	}

	return s, nil
}

func ListBosonServices(e *db.EngineWrapper) ([]*db.BosonServices, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListBosonServices")
	defer dbRTMetrics.ObserveDurationAndLog()

	services := []*db.BosonServices{}
	err := e.Where("deleted = false").Find(&services)
	return services, err
}

func ListBosonServicesByRefResourceID(e *db.EngineWrapper, resourceID string) ([]*db.BosonServices, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListBosonServicesByRefResourceID")
	defer dbRTMetrics.ObserveDurationAndLog()

	services := []*db.BosonServices{}
	err := e.Where("deleted = false and ref_resource_id = ?", resourceID).Find(&services)
	return services, err
}

func DeleteBosonServiceByServiceName(e *db.EngineWrapper, serviceName string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteBosonServiceByServiceName")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec(
		"update boson_services set deleted = true where service_name = ?",
		serviceName,
	)
	if err != nil {
		return err
	}

	_, err = e.Exec(
		"update boson_service_endpoints set deleted = true where service_name = ?", serviceName,
	)

	return err
}

func ListBosonServiceEndpoints(e *db.EngineWrapper, serviceName string) ([]*db.BosonServiceEndpoints, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListBosonServiceEndpoints")
	defer dbRTMetrics.ObserveDurationAndLog()

	endpoints := []*db.BosonServiceEndpoints{}
	err := e.Where("deleted = false and service_name = ?", serviceName).Find(&endpoints)

	return endpoints, err
}

func ClearBosonServiceEndpoints(e *db.EngineWrapper, serviceName string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("ClearBosonServiceEndpoints")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec(
		"update boson_service_endpoints set deleted = true where service_name = ?", serviceName,
	)

	return err
}

func InsertBosonServiceEndpoints(e *db.EngineWrapper, endpoints []*db.BosonServiceEndpoints) error {
	dbRTMetrics := exporter.InitDBRTMetrics("InsertBosonServiceEndpoints")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.InsertMulti(&endpoints)

	return err
}

func DeleteBosonServiceEndpoints(e *db.EngineWrapper, endpointIDs []int64) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteBosonServiceEndpoints")
	defer dbRTMetrics.ObserveDurationAndLog()

	condKey := "id"
	_, err := e.Table("boson_service_endpoints").
		In(condKey, endpointIDs).
		Update(map[string]interface{}{"deleted": true})

	return err
}

func UpdateBosonServiceEndpoints(e *db.EngineWrapper, endpoints []db.BosonServiceEndpoints) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateBosonServiceEndpoints")
	defer dbRTMetrics.ObserveDurationAndLog()

	for _, endpoint := range endpoints {
		_, err := e.Table("boson_service_endpoints").
			ID(endpoint.ID).
			AllCols().
			Update(endpoint)
		if err != nil {
			return err
		}
	}

	return nil
}
