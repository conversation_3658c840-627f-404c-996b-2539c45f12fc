package dao

import (
	"fmt"
	"sync"

	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

var poolMutex sync.Mutex

func CheckSubnet(e *db.EngineWrapper, name string, zone string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckSubnet")
	defer dbRTMetrics.ObserveDurationAndLog()

	checkData := db.Subnets{}
	return e.Where("name = ? and zone = ?", name, zone).Exist(&checkData)
}

func AddSubnet(e *db.EngineWrapper, subnet *db.Subnets) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddSubnet")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.InsertOne(subnet)
	return err
}

func ListSubnets(e *db.EngineWrapper, vpcID string) ([]*db.Subnets, error) {
	sns := []*db.Subnets{}
	if err := e.Where("vpc_id = ? and deleted = ? ", vpcID, false).Find(&sns); err != nil {
		return nil, err
	}

	return sns, nil
}

func GetBmsSubnet(e *db.EngineWrapper, scope, vpcID string) (*db.Subnets, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSubnet")
	defer dbRTMetrics.ObserveDurationAndLog()

	sn := db.Subnets{}
	has, err := e.Where("scope = ? and provider = 'CONTROLLER' and deleted = false and vpc_id = ?", scope, vpcID).Get(&sn)
	if err == nil && !has {
		err = fmt.Errorf("No subnet with CONTROLLER provider scope = %s and vpc = %s", scope, vpcID)
	}

	return &sn, err
}

func GetSubnet(e *db.EngineWrapper, subnetID string) (*db.Subnets, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSubnet")
	defer dbRTMetrics.ObserveDurationAndLog()

	sn := &db.Subnets{}
	var err error
	find := false
	if find, err = e.Where("subnet_id = ? and deleted = ?", subnetID, false).Get(sn); err != nil {
		return sn, err
	}

	if !find {
		return sn, fmt.Errorf("Subnet not found, subnet_id = %s", subnetID)
	}

	return sn, nil
}

func GetSubnetByName(e *db.EngineWrapper, name, zone string) (*db.Subnets, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetSubnetByName")
	defer dbRTMetrics.ObserveDurationAndLog()

	subnetData := db.Subnets{}
	has, err := e.Where("name = ? and zone = ?", name, zone).Get(&subnetData)
	if err != nil {
		return nil, err
	}
	if !has {
		return nil, fmt.Errorf("Subnet %v not exist", name)
	}

	return &subnetData, nil
}

func UpdateSubnet(e *db.EngineWrapper, r *db.Subnets) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateSubnet")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.ID(r.SubnetID).Update(r)
	return err
}

func DeleteSubnets(s *db.SessionWrapper, vpcID string) error {
	_, err := s.Exec("update subnets set state = 'DELETED',  deleted = true where vpc_id = ? ", vpcID)
	return err
}

func DeleteSubnet(s *db.SessionWrapper, subnetID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteSubnet")
	defer dbRTMetrics.ObserveDurationAndLog()
	_, err := s.Exec("update subnets set state = 'DELETED', deleted = ? where subnet_id = ? ", true, subnetID)
	return err
}

func ListSubnetsByTenant(e *db.EngineWrapper, tenantID string) ([]*db.Subnets, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListSubnets")
	defer dbRTMetrics.ObserveDurationAndLog()

	subnetDatas := []*db.Subnets{}
	err := e.Where("tenant_id = ? and deleted = ?", tenantID, false).Find(&subnetDatas)

	return subnetDatas, err
}

func ListSubnetsByTenantWithAllZone(e *db.EngineWrapper, tenantID string) ([]*db.Subnets, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListVPCs")
	defer dbRTMetrics.ObserveDurationAndLog()

	subnetDatas := []*db.Subnets{}
	err := e.Engine.Where("tenant_id = ? and deleted = ?", tenantID, false).Find(&subnetDatas)

	return subnetDatas, err
}

func GetVpcOvnServiceSubnet(e *db.EngineWrapper, vpcID string) (*db.Subnets, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetVpcOvnServiceSubnet")
	defer dbRTMetrics.ObserveDurationAndLog()

	sn := db.Subnets{}
	has, err := e.Where(
		"scope = ? and provider = ? and network_type = ? and deleted = false and vpc_id = ?",
		network.SubnetProperties_SERVICE.String(),
		network.SubnetProperties_OVN.String(),
		network.SubnetProperties_GENEVE.String(),
		vpcID,
	).Get(&sn)
	if err == nil && !has {
		err = fmt.Errorf("No subnet with scope = %s and provider = %s and network_type = %s and vpc_id = %s",
			network.SubnetProperties_SERVICE.String(),
			network.SubnetProperties_OVN.String(),
			network.SubnetProperties_GENEVE.String(),
			vpcID,
		)
	}

	return &sn, err
}

func GetOvnMaxVniIBSubnet(e *db.EngineWrapper) (*db.Subnets, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetOvnMaxVniIBSubnet")
	defer dbRTMetrics.ObserveDurationAndLog()

	sn := db.Subnets{}
	has, err := e.Where(
		"scope = ? and provider = ? and network_type = ? and deleted = false",
		network.SubnetProperties_TRAINING.String(),
		network.SubnetProperties_OVN.String(),
		network.SubnetProperties_IB.String(),
	).Desc("vni").Limit(1).Get(&sn)
	if err == nil && !has {
		err = fmt.Errorf("No subnet with scope = %s and provider = %s and network_type = %s",
			network.SubnetProperties_SERVICE.String(),
			network.SubnetProperties_OVN.String(),
			network.SubnetProperties_GENEVE.String(),
		)
	}

	return &sn, err
}

func CheckOvnIBSubnet(e *db.EngineWrapper, vni int) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckOvnIBSubnet")
	defer dbRTMetrics.ObserveDurationAndLog()

	checkData := db.Subnets{}
	return e.Where(
		"scope = ? and provider = ? and network_type = ? and vni = ? and deleted = false",
		network.SubnetProperties_TRAINING.String(),
		network.SubnetProperties_OVN.String(),
		network.SubnetProperties_IB.String(),
		vni,
	).Exist(&checkData)
}
