package dao

import (
	"fmt"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

func GetPccByName(e *db.EngineWrapper, pccName string) (*db.Pccs, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetPCC")
	defer dbRTMetrics.ObserveDurationAndLog()

	pcc := &db.Pccs{}
	var err error
	find := false
	if find, err = e.Where("name = ? AND (local_state != 'DELETED' OR remote_state != 'DELETED')", pccName).Get(pcc); err != nil {
		return pcc, err
	}

	if !find {
		return nil, fmt.Errorf("PCC not found, name = %s", pccName)
	}

	return pcc, nil
}

func UpdatePCCLocalState(e *db.EngineWrapper, pcc *db.Pccs) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdatePCC")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.ID(pcc.PCCID).Cols("local_state").Update(pcc)
	return err
}

func UpdatePCCRemoteState(e *db.EngineWrapper, pcc *db.Pccs) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdatePCC")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.ID(pcc.PCCID).Cols("remote_state").Update(pcc)
	return err
}
