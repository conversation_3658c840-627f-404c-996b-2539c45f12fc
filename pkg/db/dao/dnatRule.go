package dao

import (
	"fmt"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

func AddDnatRule(e *db.EngineWrapper, rule *db.DnatRules) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddDNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.InsertOne(rule)
	return err
}

func GetDnatRule(e *db.EngineWrapper, ruleID string) (*db.DnatRules, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetDNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	rule := &db.DnatRules{}
	find, err := e.Where("dnat_rule_id = ? and deleted = ?", ruleID, false).Get(rule)

	if err != nil {
		return rule, err
	}

	if !find {
		return rule, fmt.Errorf("DnatRule not found, dnat_rule_id = %s", ruleID)
	}

	return rule, nil
}

func GetAllDnatRules(e *db.EngineWrapper, includeDeleted bool) ([]*db.DnatRules, error) {
	dnatDatas := []*db.DnatRules{}
	var err error
	if includeDeleted {
		err = e.Find(&dnatDatas)
	} else {
		err = e.Where("deleted = false").Find(&dnatDatas)
	}

	return dnatDatas, err
}

func GetDnatRuleByName(e *db.EngineWrapper, ruleName string) (*db.DnatRules, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetDNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	rule := &db.DnatRules{}
	find, err := e.Where("name = ? and deleted = ?", ruleName, false).Get(rule)

	if err != nil {
		return rule, err
	}

	if !find {
		return rule, fmt.Errorf("DnatRule not found, name = %s", ruleName)
	}

	return rule, nil
}

func ExistDnatRule(e *db.EngineWrapper, name string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Where("deleted = false").Exist(&db.DnatRules{
		Name: name,
	})
}

func ExistDnatRuleByNatGatewayPort(e *db.EngineWrapper, natGatewayPort int32, protocol, natgwId string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	checkData := db.DnatRules{}
	return e.Where("nat_gw_port = ? and protocol = ? and nat_gateway_id = ? and deleted = false", natGatewayPort, protocol, natgwId).Exist(&checkData)
}

func CheckDnatRule(e *db.EngineWrapper, outPortMin, protocol, natgwId, eipId string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	checkData := db.DnatRules{}
	return e.Where("outer_port_min = ? and protocol = ? and nat_gateway_id = ? and eip_id = ? and deleted = false", outPortMin, protocol, natgwId, eipId).Exist(&checkData)
}

func ListDnatRules(e *db.EngineWrapper, eipID string) ([]*db.DnatRules, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListDNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	rules := []*db.DnatRules{}
	err := e.Where("eip_id = ? and deleted = ? ", eipID, false).Find(&rules)

	if err != nil {
		return rules, err
	}

	return rules, nil
}

func UpdateDnatRule(e *db.EngineWrapper, rule *db.DnatRules) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateDNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.ID(rule.DnatRuleID).Update(rule)
	return err
}

func DeleteDnatRule(e *db.EngineWrapper, ruleID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteDNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update dnat_rules set deleted = ?, state = ? where dnat_rule_id = ? ", true, "DELETED", ruleID)
	return err
}

func SetDnatRuleState(e *db.EngineWrapper, ruleID, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetStateDNATRule")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update dnat_rules set state = ?, deleted = ? where dnat_rule_id = ? ", state, state == "DELETED", ruleID)
	return err
}

func GetEipRuleCount(e *db.EngineWrapper, eipID string) (int64, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("FindAndCountDNATRules")
	defer dbRTMetrics.ObserveDurationAndLog()

	rules := []db.DnatRules{}
	return e.Where("eip_id = ? and deleted = ?", eipID, false).FindAndCount(&rules)
}

func CheckDNATRuleExistForCreate(
	e *db.EngineWrapper, inner_port_min string, protocol string, inner_ip string, internal_instance_name string,
) (has bool, err error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckDNATRuleExistForCreate")
	defer dbRTMetrics.ObserveDurationAndLog()

	checkData := db.DnatRules{}
	s := e.Where("inner_port_min = ? and protocol = ? and deleted = false", inner_port_min, protocol)
	if inner_ip != "" {
		has, err = s.And("inner_ip = ?", inner_ip).Exist(&checkData)
	} else {
		has, err = s.And("internal_instance_name = ?", internal_instance_name).Exist(&checkData)
	}

	if err != nil {
		return has, err
	}

	return has, nil
}
