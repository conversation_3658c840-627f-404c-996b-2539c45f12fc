package dao

import (
	"errors"
	"fmt"

	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

const CIDR_VPCGW_SCOPE = "VPCGW"

func ListNatGateways(e *db.EngineWrapper, vpcID string) ([]*db.NatGateways, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListNatGateways")
	defer dbRTMetrics.ObserveDurationAndLog()

	natGws := []*db.NatGateways{}
	err := e.Where("vpc_id = ? and deleted = false", vpcID).Find(&natGws)
	if err != nil {
		return natGws, err
	}

	return natGws, nil
}

func ListNatGatewaysBySubnetID(e *db.EngineWrapper, subnetID string) ([]*db.NatGateways, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListNatGateways")
	defer dbRTMetrics.ObserveDurationAndLog()

	natGws := []*db.NatGateways{}
	err := e.Where("subnet_id = ? and deleted = false", subnetID).Find(&natGws)
	if err != nil {
		return natGws, err
	}

	return natGws, nil
}

func GetNATGateway(e *db.EngineWrapper, natgwID string) (*db.NatGateways, bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetNATGateway")
	defer dbRTMetrics.ObserveDurationAndLog()

	natGW := db.NatGateways{}
	has, err := e.ID(natgwID).Get(&natGW)
	if err != nil {
		return nil, false, err
	}

	return &natGW, has, nil
}

func GetNATGatewayByName(e *db.EngineWrapper, name string) (*db.NatGateways, bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetNATGateway")
	defer dbRTMetrics.ObserveDurationAndLog()

	natGW := db.NatGateways{
		Name: name,
	}

	has, err := e.Get(&natGW)
	if err != nil {
		return nil, false, err
	}

	return &natGW, has, nil
}

func DeleteNatGateway(e *db.EngineWrapper, ID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteNatGateway")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update nat_gateways set state = 'DELETED', deleted = true where nat_gateway_id = ?", ID)
	return err
}

func DeleteNatGateways(e *db.SessionWrapper, vpcID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteNatGateways")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update nat_gateways set state = 'DELETED', deleted = true where vpc_id = ?", vpcID)
	return err
}

func ReleaseNatGatewayExternalIP(e *db.EngineWrapper, natGwExternalIpID string) error {
	_, err := e.Exec("update nat_gateway_external_ip_pools set allocated = false, nat_gateway_id = ? where nat_gw_external_ip_id = ?", "", natGwExternalIpID)
	return err
}

func ReleaseNatGatewayExternalIPByHaGWID(e *db.EngineWrapper, haNatGwId string) error {
	_, err := e.Exec("update nat_gateway_external_ip_pools set allocated = false, nat_gateway_id = ? where nat_gateway_id = ?", "", haNatGwId)
	return err
}

func FetchNatGatewayEip(e *db.EngineWrapper) (db.NatGatewayExternalIpPools, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetNatGatewayExternalIpPool")
	defer dbRTMetrics.ObserveDurationAndLog()

	natGwEip := db.NatGatewayExternalIpPools{}
	has, err := e.Table("nat_gateway_external_ip_pools").Join("INNER", "cidr_pools",
		"nat_gateway_external_ip_pools.cidr_id = cidr_pools.cidr_pool_id").
		Where("nat_gateway_external_ip_pools.allocated = false and cidr_pools.zone_prp = ?", db.AZ).
		Get(&natGwEip)
	if err != nil {
		return natGwEip, err
	}
	if !has {
		err = errors.New("Allocatable NAT Gateway ExternalIP is empty")
	}
	return natGwEip, err
}

func FetchMultiNatGatewayEip(e *db.SessionWrapper, count byte) ([]db.NatGatewayExternalIpPools, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("FetchMultiNatGatewayEip")
	defer dbRTMetrics.ObserveDurationAndLog()

	cidrPoolsForVpcNatGw := []db.CidrPools{}
	err := e.Table("cidr_pools").Where("scope = ? and cidr_pools.zone_prp = ?", CIDR_VPCGW_SCOPE, db.AZ).Find(&cidrPoolsForVpcNatGw)
	if err != nil {
		logrus.Errorf("get cidr for vpcnatgw error %s", err)
		return nil, err
	}
	logrus.Infof("get cidr for vpcnatgw %+v", cidrPoolsForVpcNatGw)

	netGwEips := []db.NatGatewayExternalIpPools{}
	for _, cidrPool := range cidrPoolsForVpcNatGw {
		err := e.Table("nat_gateway_external_ip_pools").
			Where("nat_gateway_external_ip_pools.allocated = false and nat_gateway_external_ip_pools.cidr_id = ?", cidrPool.CIDRPoolID).
			OrderBy("inet(nat_gateway_external_ip_pools.nat_gw_external_ip)").Limit(int(count)).Find(&netGwEips)
		if err != nil {
			logrus.Errorf("get eips for ha-nat-gateway error %s", err)
			return netGwEips, err
		}
		if len(netGwEips) != int(count) {
			logrus.Errorf("eips number is %d not meet requirement %d", len(netGwEips), count)
			// 清空切片
			netGwEips = []db.NatGatewayExternalIpPools{}
			continue
		} else {
			logrus.Infof("eip number is %d meet requirement %d", len(netGwEips), count)
			break
		}
	}

	if len(netGwEips) != int(count) || netGwEips == nil {
		err = fmt.Errorf("can not allocate %d nat Gateway IP, get %d from db", count, len(netGwEips))
	}
	return netGwEips, err
}

func SetNatGwEipPoolAllocated(e *db.EngineWrapper, natgwID string, natgwEIPID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateNATGatewayExternalIPPools")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update nat_gateway_external_ip_pools set allocated = ?, nat_gateway_id = ? where nat_gw_external_ip_id = ?", true, natgwID, natgwEIPID)
	return err
}

func SetMultiNatGwEipPoolAllocated(e *db.SessionWrapper, natgwID string, natgwEIPIDs []string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateMultiNATGatewayExternalIPPools")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Table("nat_gateway_external_ip_pools").In("nat_gw_external_ip_id", natgwEIPIDs).
		Update(map[string]interface{}{"allocated": true, "nat_gateway_id": natgwID})
	return err

}

func AddNatGateway(e *db.EngineWrapper, natgw *db.NatGateways) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddNATGateway")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Insert(natgw)
	return err
}

func CheckNatGateway(e *db.EngineWrapper, name string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckNatGateway")
	defer dbRTMetrics.ObserveDurationAndLog()

	checkData := db.NatGateways{}
	return e.Where("name = ?", name).Exist(&checkData)
}
