package dao

import (
	"fmt"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
)

func AddVPCACL(e *db.EngineWrapper, acl *db.VpcAcls) error {
	dbRTMetrics := exporter.InitDBRTMetrics("AddVPCACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.InsertOne(acl)
	return err
}

func GetVPCACL(e *db.EngineWrapper, aclID string) (*db.VpcAcls, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetVPCACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	acl := &db.VpcAcls{}
	find, err := e.Where("vpc_acl_id = ? and deleted = ?", aclID, false).Get(acl)

	if err != nil {
		return acl, err
	}

	if !find {
		return acl, fmt.Errorf("VPCACL not found, acl_id = %s", aclID)
	}

	return acl, nil
}

func GetVPCACLsByType(e *db.EngineWrapper, t string) ([]*db.VpcAcls, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetVPCACLByType")
	defer dbRTMetrics.ObserveDurationAndLog()
	acls := []*db.VpcAcls{}
	err := e.Where("deleted = false and type = ?", t).Find(&acls)

	return acls, err
}

func GetVPCACLByName(e *db.EngineWrapper, name string) (*db.VpcAcls, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetVPCACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	acl := &db.VpcAcls{}
	find, err := e.Where("name = ? and deleted = false", name).Get(acl)

	if err != nil {
		return acl, err
	}

	if !find {
		return acl, fmt.Errorf("VPCACL not found, name = %s", name)
	}

	return acl, nil
}

func ExistVPCACL(e *db.EngineWrapper, name string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckVPCACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	return e.Where("deleted = false").Exist(&db.VpcAcls{
		Name: name,
	})
}

func ExistVPCACLByPriorityType(e *db.EngineWrapper, priority int32, aclType, vpcID string) (bool, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("CheckVPCACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	// return e.Where("deleted = false").Exist(&db.VpcAcls{
	// 	Priority: priority,
	// 	Type:     aclType,
	// 	VPCID:    vpcID,
	// })
	acl := &db.VpcAcls{}
	find, err := e.Where("priority = ? and type = ? and vpc_id = ? and deleted = false", priority, aclType, vpcID).Get(acl)

	if err != nil {
		return false, err
	}

	if !find {
		return false, nil
	}
	return true, nil
}

func GetVPCACLByPriorityType(e *db.EngineWrapper, priority int32, aclType, vpcID string) (*db.VpcAcls, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("GetVPCACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	acl := &db.VpcAcls{}
	find, err := e.Where("priority = ? and type = ? and vpc_id = ? and deleted = false", priority, aclType, vpcID).Get(acl)

	if err != nil {
		return acl, err
	}

	if !find {
		return acl, fmt.Errorf("VPCACL not found, priority = %d and type = %s and vpc_id = %s and deleted = false", priority, aclType, vpcID)
	}

	return acl, nil
}

func ListVPCACLs(e *db.EngineWrapper, vpcID string) ([]*db.VpcAcls, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListVPCACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	acls := []*db.VpcAcls{}
	err := e.Where("vpc_id = ? and deleted = ? ", vpcID, false).Find(&acls)

	if err != nil {
		return acls, err
	}

	return acls, nil
}

func UpdateVPCACL(e *db.EngineWrapper, acl *db.VpcAcls) error {
	dbRTMetrics := exporter.InitDBRTMetrics("UpdateVPCACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.ID(acl.VpcAclID).Update(acl)
	return err
}

func DeleteVPCACL(e *db.EngineWrapper, aclID string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("DeleteVPCACL")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update vpc_acls set deleted = true, state = ? where vpc_acl_id = ? ", "DELETED", aclID)
	return err
}

func SetVPCACLState(e *db.EngineWrapper, aclID, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetVPCACLState")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update vpc_acls set state = ?, deleted = ? where vpc_acl_id = ? ", state, state == "DELETED", aclID)
	return err
}

func SetVPCACLStateByName(e *db.EngineWrapper, aclName, state string) error {
	dbRTMetrics := exporter.InitDBRTMetrics("SetVPCACLStateByName")
	defer dbRTMetrics.ObserveDurationAndLog()

	_, err := e.Exec("update vpc_acls set state = ?, deleted = ? where name = ? ", state, state == "DELETED", aclName)
	return err
}
