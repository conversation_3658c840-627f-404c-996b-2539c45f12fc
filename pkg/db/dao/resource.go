package dao

import (
	"fmt"
	"strconv"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
)

func ListResourceGroupResourcesByRGUIDAndZone(e *db.EngineWrapper, resource []*interface{}, resourceTable string, condition string, params map[string]interface{}, orderBy string, pageToken int, pageSize int32) (int32, error) {
	sqlStr := fmt.Sprintf(`select count(*) from %s where `, resourceTable)

	// count
	countSQL := sqlStr + condition

	count, err := e.Exec(countSQL, params)
	if err != nil {
		return 0, err
	}

	// select
	sqlStr = fmt.Sprintf(`select * from %s where `, resourceTable)
	offset := int32(pageToken-1) * pageSize
	querySQL := sqlStr + condition + orderBy + " LIMIT " + strconv.Itoa(int(pageSize)) + " OFFSET " + strconv.Itoa(int(offset))

	resources, err := e.Exec(querySQL, params)
	if err != nil {
		return 0, err
	}

	if count == resources {
		return 0, nil
	}
	//return int32(count), nil
	return 0, nil
}
