# 该文件只是https://gitlab.bj.sensetime.com/elementary/higgs/higgs-errorinfo-module/-/tree/boson-dev/boson的镜像，不实际生效
# common
"serviceError":
  other: "Service operation failed, please try again later"
"internalErrWithDetial":
  other: Service operation failed, {{.detail}}
"dbErr":
  other: Database Operation Error
"crErr":
  other: CR Operation Error
"apiUnimplemented":
  other: API is not supported

"invalidArgument":
  other: Invalid request argument, {{.detail}}

# dnat
"dnatNotFound":
  other: The DNAT rule does not exist
"dnatDeleted":
  other: The DNAT rule has been deleted
"dnatInBinding":
  other: The DNAT rule is still bound
"invalidDnatProperties":
  other: Invalid DNAT rule parameter
"invalidUnbindProperties":
  other: Invalid unbind parameter
"EipDnatRuleExist":
  other: The EIP DNAT rule already existed
"NgwIdNotSpecify":
  other: Nat Gateway ID not specify
"EipNotSpecify":
  other: EIP not specify
"EipNotActivated":
  other: EIP not activated
"ErrEipNotExisted":
  other: EIP not existed
"InternalServerError":
  other: Internal Server Error
"EipDnatRuleQuotaUsedUp":
  other: EIP DNAT rule quota used up

# snat
"snatNotFound":
  other: The SNAT rule does not exist
"snatExist":
  other: The SNAT rule exist
"snatDeleted":
  other: The SNAT rule has been deleted
"invalidSnatProperties":
  other: Invalid SNAT rule parameter

# ip address
"ipaNotFound":
  other: The IP address does not exist
"ipaDeleted":
  other: The IP address has been deleted
"ipaExist":
  other: The IP address already exists
"ipaInUse":
  other: The IP address is in use
"ipaCannotBeBound":
  other: The IP address cannot be bound
"ipaHasBeenBound":
  other: The IP address has been bound
"ipaBoundFailed":
  other: IP address binding failed
"ipaCannotBeUnbound":
  other: The IP address cannot be unbound
"ipaHasBeenUnbound":
  other: The IP address has been unbound
"ipaUnboundFailed":
  other: IP address unbinding failed
"invalidProtocol":
  other: Invalid protocol
"invalidSubnetProperties":
  other: Invalid subnet parameter
"invalidSubnetIP":
  other: Invalid subnet IP
"allocateIPFailed":
  other: Failed to assign IP

# acl
"aclNotFound":
  other: ACL dose not exist
"aclExist":
  other: ACL exist
"aclPriorityExist":
  other: ACL priority exist

# eip
"eipNotFound":
  other: EIP dose not exist
"eipDeleted":
  other: EIP deleted
"eipPoolNotFound":
  other: EIP pool dose not exist
"eipStateUnactive":
  other: EIP state unactive

# dcgw
"dcgwExist":
  other: DCGW exist
"dcgwNotFound":
  other: DCGW does not exist

# dcvc
"dcvcExist":
  other: DCVC exist
"dcvcNotFound":
  other: DCVC does not exist

# vpc
"vpcNotFound":
  other: VPC does not exist
"vpcDgwNotFound":
  other: VPC DGW does not exist

# vpc peering
"pccNotFound":
  other: PCC does not exist
"vpcPeeringNotFound":
  other: VPC PEERING does not exist
"vpcQuotaNotFound":
  other: VPC QUOTA does not exist
"vpcPeeringResourceExhausted":
  other: VPC PEERING QUOTA Exhausted

# haNatGw
"haNatGwNotFound":
  other: HA NAT GATEWAY does not exist

# natGw
"natGwNotFound":
  other: NAT GATEWAY does not exist

# slb
"slbNotFound":
  other: SLB does not exist
"slbListenerNotFound":
 other: SLB Listener does not exist
 "slbTargetGroupNotFound":
 other: SLB TargetGroup does not exist
 "slbTargetNotFound":
 other: SLB Target does not exist

# vnic
"vipNotFound":
  other: VIP does not exist
"subnetNotFound":
  other: SUBNET does not exist
