package errors

import (
	"context"
	goerrors "errors"

	higgs_errorinfo_module "gitlab.bj.sensetime.com/elementary/higgs/higgs-errorinfo-module"
	higgs_error "gitlab.bj.sensetime.com/elementary/higgs/higgs-golang-toolkit/utils/errors"
	"gitlab.bj.sensetime.com/elementary/higgs/higgs-golang-toolkit/utils/i18n"
	"google.golang.org/grpc/codes"
)

/*
	api错误返回原则：
	1. 前端参数检验错误，提示出哪个字段有问题；
	2. 未找到或已存在，直接返回该资源未找到或已存在；
	3. 后端错误，返回错误关键词提示，
	   除非明确报错信息可直接返回外，其他尽量返回错误关键词，
	   禁止返回错误堆栈，内部ip等敏感信息；
	4. 权限错误，返回无权限访问（{账户}{用户}{接口}{权限}）；

*/

var (
	Domain = "boson"

	// common
	ServiceError          = "serviceError"
	InternalErrWithDetial = "internalErrWithDetial"
	DbErr                 = "dbErr"
	CrErr                 = "crErr"
	ApiUnimplemented      = "apiUnimplemented"

	InvalidStatus   = "invalidStatus"
	InvalidArgument = "invalidArgument"

	NotFound = "NotFound"
	Exist    = "Exist"

	// hanatgw
	HaNatGwNotFound = "haNatGwNotFound"

	// natgw
	NatGwNotFound = "natGwNotFound"

	// eip
	EipNotFound      = "eipNotFound"
	EipDeleted       = "eipDeleted"
	EipPoolNotFound  = "eipPoolNotFound"
	EipStateUnactive = "eipStateUnactive"

	// vpc
	VpcNotFound    = "vpcNotFound"
	VpcDeleted     = "vpcDeleted"
	VpcDgwNotFound = "vpcDgwNotFound"

	// vpc preeing
	PccNotFound                 = "pccNotFound"
	VpcPeeringNotFound          = "vpcPeeringNotFound"
	VpcPeeringCreateError       = "VpcPeeringCreateError"
	VpcQuotaNotFound            = "vpcQuotaNotFound"
	VpcPeeringResourceExhausted = "vpcPeeringResourceExhausted"

	// dnat
	DnatNotFound            = "dnatNotFound"
	DnatDeleted             = "dnatDeleted"
	DnatInBinding           = "dnatInBinding"
	InvalidDnatProperties   = "invalidDnatProperties"
	InvalidUnbindProperties = "invalidUnbindProperties"

	// snat
	SnatNotFound          = "snatNotFound"
	SnatDeleted           = "snatDeleted"
	InvalidSnatProperties = "invalidSnatProperties"
	SnatExist             = "snatExist"

	// ipa
	IpaNotFound             = "ipaNotFound"
	IpaDeleted              = "ipaDeleted"
	IpaExist                = "ipaExist"
	IpaInUse                = "ipaInUse"
	IpaCannotBeBound        = "ipaCannotBeBound"
	IpaHasBeenBound         = "ipaHasBeenBound"
	IpaBoundFailed          = "ipaBoundFailed"
	IpaCannotBeUnbound      = "ipaCannotBeUnbound"
	IpaHasBeenUnbound       = "ipaHasBeenUnbound"
	IpaUnboundFailed        = "ipaUnboundFailed"
	InvalidProtocol         = "invalidProtocol"
	InvalidSubnetProperties = "invalidSubnetProperties"
	InvalidSubnetIP         = "invalidSubnetIP"
	AllocateIPFailed        = "allocateIPFailed"

	// acl
	AclNotFound          = "aclNotFound"
	AclEipIDError        = "eipIDNotMatch"
	AclVpcIDError        = "vpcIDNotMatch"
	InvalidAclProperties = "invalidAclProperties"
	AclExist             = "aclExist"
	AclPriorityExist     = "aclPriorityExist"
	AclCreateError       = "aclCreateError"

	// dcgw
	DcgwExist    = "dcgwExist"
	DcgwNotFound = "dcgwNotFound"

	// dcvc
	DcvcExist    = "dcvcExist"
	DcvcNotFound = "dcvcNotFound"

	// slb
	SlbNotFound            = "slbNotFound"
	SlbListenerNotFound    = "slbListenerNotFound"
	SlbTargetGroupNotFound = "slbTargetGroupNotFound"
	SlbTargetNotFound      = "slbTargetNotFound"

	// vnic
	VipNotFound    = "vipNotFound"
	SubnetNotFound = "subnetNotFound"
)

func NewBundle() {
	bundle := higgs_errorinfo_module.InitBosonBundle()
	i18n.RegisterBundle(bundle)
}

/*
	示例：

国际化配置文件配置如下：
"forbidden":
other: 用户在当前{{.service}}范围内没有{{.permission}}权限，请联系用户管理员授权。

调用样例：
```

	errorDetail := higgs_error.CreateEIBuilder(ctx, codes.PermissionDenied, reason.PermissionDenied, errInfoModule.IAM).
	WithMetadata(map[string]string{
	  "scope":      "/rm",
	  "permission": "iam.user.get",
	}).
	WithLocaleFormatData(map[string]interface{}{
	  "service":    "访问控制",
	  "permission": "iam.user.get",
	}).
	Build()

	return higgs_error.GenAIPError(ctx, errorDetail)

```
实现效果：
```

	{
	  "code": 7,
	  "message": "PermissionDenied",
	  "details": [
	    {
	      "@type": "type.googleapis.com/google.rpc.LocalizedMessage",
	      "locale": "zh-CN",
	      "message": "用户在当前 访问控制 范围内没有 iam.user.get 权限，请联系用户管理员授权。"
	    },
	    {
	      "@type": "type.googleapis.com/google.rpc.ErrorInfo",
	      "reason": "PERMISSION_DENIED",
	      "domain": "IAM",
	      "metadata": {
	        "scope":      "/rm",
	        "permission": "iam.user.get"
	      }
	    },
	    {
	      "@type": "type.googleapis.com/sensetime.core.higgs.error_detail.v1.LogInfo",
	      "log_id": "de265ea7-892a-4471-b09a-752e7c22441e",
	      "trace_id": "",
	      "level": "UNSPECIFIED"
	    }
	  ]
	}

```
*/
func NewHiggsError(ctx context.Context, code codes.Code, reason string, domain string,
	metaData map[string]string, localeFormatData map[string]interface{}) error {
	errorDetail := higgs_error.CreateEIBuilder(ctx, code, reason, domain).
		WithMetadata(metaData).
		WithLocaleFormatData(localeFormatData).
		Build()

	return higgs_error.GenAIPError(ctx, errorDetail)

}

func New(msg string) error {
	return goerrors.New(msg)
}
