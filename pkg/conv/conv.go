package conv

import (
	"fmt"
	ipav1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/ipa/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func IpasDbToApi(ipas *db.Ipas) *ipav1.IPA {
	return &ipav1.IPA{
		Id:           ipas.IpaID,
		Name:         ipas.Name,
		DisplayName:  ipas.DisplayName,
		Description:  ipas.Description,
		ResourceType: ipas.ResourceType,
		CreatorId:    ipas.CreatorID,
		OwnerId:      ipas.OwnerID,
		TenantId:     ipas.TenantID,
		Zone:         ipas.Zone,
		Properties: &ipav1.IPAProperties{
			SubnetId: ipas.SubnetID,
			Protocol: ipas.Protocol,
			Ip:       ipas.IP,
			Mac:      ipas.MAC,
			GwIp:     ipas.GatewayIP,
			Cidr:     ipas.CIDR,
			Mtu:      int32(ipas.MTU),
			Ipmi:     ipas.IPMI,
			NatGwIp:  GetBmsPodGwIpByCIDR(ipas.CIDR),
		},
		State:      ipav1.IPA_State(ipav1.IPA_State_value[ipas.State]),
		Deleted:    ipas.Deleted,
		CreateTime: timestamppb.New(ipas.CreateTime),
		UpdateTime: timestamppb.New(ipas.UpdateTime),
	}
}

func GetBmsPodGwIpByCIDR(cidr string) string {
	lastIP, err := utils.GetTheLastAddrFromCIDR(cidr)
	if err != nil {
		return fmt.Sprintf("Can't get bms pod gw ip from %s", cidr)
	}
	return lastIP
}
