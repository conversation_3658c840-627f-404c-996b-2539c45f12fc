package parser

import (
	"context"
	"encoding/json"

	"github.com/apache/rocketmq-client-go/v2/consumer"
	"github.com/apache/rocketmq-client-go/v2/primitive"

	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
)

// Parser 消费 message 转 struct
type Parser struct {
	MessageSelector consumer.MessageSelector
	Starter         func(ctx context.Context, ext ...*primitive.MessageExt) (consumer.ConsumeResult, error)
}

// NewParser 初始化 Parser
func NewParser(f func(*types.RMBody, string)) *Parser {
	p := &Parser{
		MessageSelector: consumer.MessageSelector{},
		Starter: func(ctx context.Context, ext ...*primitive.MessageExt) (consumer.ConsumeResult, error) {
			logrus.Infof("Received a message: %+v", ext)
			for i := range ext {
				err, b := parseExt(ext[i])
				if err != nil {
					logrus.Errorln(err)
					continue
				}
				f(b, ext[i].Message.Topic)
			}
			return consumer.ConsumeSuccess, nil
		},
	}
	return p
}

func parseExt(i *primitive.MessageExt) (error, *types.RMBody) {
	var b types.RMBody
	err := json.Unmarshal(i.Message.Body, &b)
	return err, &b
}

// NewParser 初始化 Parser
func NewParserCloudEvent(f func(msg *types.CloudEventMsg)) *Parser {
	p := &Parser{
		MessageSelector: consumer.MessageSelector{},
		Starter: func(ctx context.Context, ext ...*primitive.MessageExt) (consumer.ConsumeResult, error) {
			for i := range ext {
				msg := types.CloudEventMsg{}
				err := json.Unmarshal(ext[i].Message.Body, &msg)
				if err != nil {
					logrus.Errorln(err)
					continue
				}

				e, err := json.Marshal(msg)
				if err != nil {
					logrus.Errorln(err)
					continue
				}
				logrus.Infof("Received a CloudEvent event: %s\n", e)

				if msg.Type == nil || msg.Data == nil {
					logrus.Error("Parse cloud event msg fail, Type or Data is nil", "Type", msg.Type, "Data", msg.Data)
					continue
				}

				f(&msg)
			}
			return consumer.ConsumeSuccess, nil
		},
	}
	return p
}
