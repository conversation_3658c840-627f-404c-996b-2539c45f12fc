package receiver

import (
	"context"

	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/consumer"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/sirupsen/logrus"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
)

// Receiver 消费者实体
type Receiver struct {
	Consumer rocketmq.PushConsumer
	Option   string
}

// NewReceiver 根据配置初始化 Receiver
func NewReceiver(rpconf *config.Config, group string) (*Receiver, error) {
	c, err := rocketmq.NewPushConsumer(
		consumer.WithNameServer(rpconf.MQ.Default.NameServer),
		consumer.WithGroupName(group),
		consumer.WithConsumerModel(consumer.Clustering),
		consumer.WithInstance(rpconf.MQ.Default.InstanceName),
		consumer.WithConsumerOrder(true),
		consumer.WithCredentials(primitive.Credentials{
			AccessKey: rpconf.MQ.Default.AccessKey,
			SecretKey: rpconf.MQ.Default.SecretKey,
		}),
	)

	r := &Receiver{
		Consumer: c,
		Option:   "opt",
	}

	return r, err
}

// Start 开启消费订阅
func (r *Receiver) Start(topic string, messageSelector consumer.MessageSelector, f func(ctx context.Context, ext ...*primitive.MessageExt) (consumer.ConsumeResult, error)) error {
	logrus.Infof("Starting %v Receiver", topic)

	if err := r.Consumer.Subscribe(topic, messageSelector, f); err != nil {
		logrus.Fatalf("Receiver %v start FAILED", topic)
		return err
	}

	logrus.Infof("Started %v Receiver", topic)
	return r.Consumer.Start()
}

// ShutDown 关闭消费
func (r *Receiver) ShutDown() error {
	return r.Consumer.Shutdown()
}
