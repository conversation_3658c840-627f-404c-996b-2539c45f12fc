package exporter

import (
	"fmt"
	"net/http"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/sirupsen/logrus"

	_ "net/http/pprof"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

const (
	domain = "boson"
	app    = "provider"

	metricsPath = "/metrics"
)

var Exporter *ProviderExporter

type ProviderExporter struct {
	config *config.Config
}

func NewExporter(config *config.Config) *ProviderExporter {
	return &ProviderExporter{
		config: config,
	}
}

func (e *ProviderExporter) init() {

	logrus.Infof("metrics env: %v, %v, %v, %v", e.config.Env, e.config.Boson.VPCDefaultRegion, e.config.Boson.VPCDefaultAZ, e.config.Boson.PRP)

	constLabel["env"] = utils.EmptyToBackup(e.config.Env, "dev")
	constLabel["region"] = utils.EmptyToBackup(e.config.Boson.VPCDefaultRegion, "cn-sh-01z")
	constLabel["az"] = utils.EmptyToBackup(e.config.Boson.VPCDefaultAZ, "cn-sh-01a")
	constLabel["prp"] = utils.EmptyToBackup(e.config.Boson.PRP, "ch-sh-01a-prp01")

	initVec()

	prometheus.MustRegister(HTTPRequestRT, MQRequestRT, DBRequestRT, K8sRequestRT)
}

func (e *ProviderExporter) Serve(stopCh <-chan struct{}) {

	go func() {
		defer func() {
			<-stopCh
		}()

		e.init()

		http.Handle(metricsPath, promhttp.Handler())
		http.HandleFunc("/", func(w http.ResponseWriter, r *http.Request) {
			_, _ = w.Write([]byte(`<html>
            <head><title>Boson Provider Exporter</title></head>
            <body>
            <h1>Boson Provider Exporter</h1>
            <p><a href='` + metricsPath + `'>Metrics</a></p>
            </body>
            </html>`))
		})

		logrus.Infof("Metrics Server start :%d", e.config.Boson.MetricsPort)
		logrus.Infof("Pprof Server start :%d, reuse metrices server port", e.config.Boson.MetricsPort)
		logrus.Fatalln(http.ListenAndServe(fmt.Sprintf(":%d", e.config.Boson.MetricsPort), nil))
	}()

}
