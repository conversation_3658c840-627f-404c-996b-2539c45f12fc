package exporter

import (
	"strings"
	"time"

	"github.com/prometheus/client_golang/prometheus"

	"fmt"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

var (
	constLabel = map[string]string{"env": "dev", "region": "sh-regional", "az": "sh-01", "prp": "a"}

	httpRTLabelKeys = []string{"http__handler", "http__method", "http__code", "tenant__code"}
	mqRTLabelKeys   = []string{"mq__handler", "mq__method", "mq__result", "tenant__code"}
	dbRTLabelKeys   = []string{"operation__name"}
	k8sRTLabelKeys  = []string{"operation__name"}

	HTTPRequestRT *prometheus.SummaryVec

	MQRequestRT *prometheus.SummaryVec

	DBRequestRT *prometheus.SummaryVec

	K8sRequestRT *prometheus.SummaryVec
)

func initVec() {
	HTTPRequestRT = prometheus.NewSummaryVec(prometheus.SummaryOpts{
		Name:        metricsNameGen(domain, app, "http_request_rt", "ms"),
		Help:        "http response time, unit ms",
		Objectives:  utils.Objectives,
		ConstLabels: constLabel,
	}, httpRTLabelKeys)

	MQRequestRT = prometheus.NewSummaryVec(prometheus.SummaryOpts{
		Name:        metricsNameGen(domain, app, "mq_request_rt", "ms"),
		Help:        "mq response time, unit ms",
		Objectives:  utils.Objectives,
		ConstLabels: constLabel,
	}, mqRTLabelKeys)

	DBRequestRT = prometheus.NewSummaryVec(prometheus.SummaryOpts{
		Name:        metricsNameGen(domain, app, "db_request_rt", "ms"),
		Help:        "db response time, unit ms",
		Objectives:  utils.Objectives,
		ConstLabels: constLabel,
	}, dbRTLabelKeys)

	K8sRequestRT = prometheus.NewSummaryVec(prometheus.SummaryOpts{
		Name:        metricsNameGen(domain, app, "k8s_request_rt", "ms"),
		Help:        "k8s response time, unit ms",
		Objectives:  utils.Objectives,
		ConstLabels: constLabel,
	}, k8sRTLabelKeys)
}

type MetricsData interface {
	ObserveDurationAndLog()
}

type HTTPRTMetrics struct {
	StartTime  time.Time
	Handle     string
	Method     string
	TenantCode string
	Code       string
}

type MQRTMetrics struct {
	StartTime  time.Time
	Handle     string
	Method     string
	TenantCode string
	Result     string
}

type DBRTMetrics struct {
	StartTime time.Time
	Ops       string
}

type K8sRTMetrics struct {
	StartTime time.Time
	Ops       string
}

func InitHTTPRTMetrics(name, handle, method string) *HTTPRTMetrics {
	return &HTTPRTMetrics{
		TenantCode: utils.NameToTenantCode(name),
		StartTime:  time.Now(),
		Code:       "400",
		Handle:     handle,
		Method:     method,
	}
}

func InitMQRTMetrics(name, handle, method string) *MQRTMetrics {
	return &MQRTMetrics{
		TenantCode: utils.NameToTenantCode(name),
		StartTime:  time.Now(),
		Result:     types.ActionResultFailed,
		Handle:     handle,
		Method:     method,
	}
}

func InitDBRTMetrics(ops string) *DBRTMetrics {
	return &DBRTMetrics{
		StartTime: time.Now(),
		Ops:       ops,
	}
}

func InitK8sRTMetrics(ops string) *K8sRTMetrics {
	return &K8sRTMetrics{
		StartTime: time.Now(),
		Ops:       ops,
	}
}

func (m *HTTPRTMetrics) ObserveDurationAndLog() {
	rt := time.Since(m.StartTime).Nanoseconds() / 1e6
	utils.SlowOpsLog(Exporter.config.Boson.SlowOpsThresholdMilliSecondsConfig.HTTPRequestRT, rt, fmt.Sprintf("%+v", m))
	HTTPRequestRT.WithLabelValues(
		m.Handle, m.Method, m.Code, m.TenantCode).Observe(float64(rt))
}

func (m *MQRTMetrics) ObserveDurationAndLog() {
	rt := time.Since(m.StartTime).Nanoseconds() / 1e6
	utils.SlowOpsLog(Exporter.config.Boson.SlowOpsThresholdMilliSecondsConfig.MQRequestRT, rt, fmt.Sprintf("%+v", m))
	MQRequestRT.WithLabelValues(
		m.Handle, m.Method, m.Result, m.TenantCode).Observe(float64(rt))
}

func (m *DBRTMetrics) ObserveDurationAndLog() {
	rt := time.Since(m.StartTime).Nanoseconds() / 1e6
	utils.SlowOpsLog(Exporter.config.Boson.SlowOpsThresholdMilliSecondsConfig.DBRequestRT, rt, fmt.Sprintf("%+v", m))
	DBRequestRT.WithLabelValues(
		m.Ops).Observe(float64(rt))
}

func (m *K8sRTMetrics) ObserveDurationAndLog() {
	rt := time.Since(m.StartTime).Nanoseconds() / 1e6
	utils.SlowOpsLog(Exporter.config.Boson.SlowOpsThresholdMilliSecondsConfig.K8sRequestRT, rt, fmt.Sprintf("%+v", m))
	K8sRequestRT.WithLabelValues(
		m.Ops).Observe(float64(rt))
}

func metricsNameGen(strs ...string) string {
	return strings.Join(strs, "__")
}
