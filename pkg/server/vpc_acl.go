package server

import (
	"context"
	"fmt"
	"net"
	"strconv"
	"strings"

	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"github.com/sirupsen/logrus"
	networkv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	vpcv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	aip "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils/google-aip"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	k8s_errors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

const (
	VPCACLKind                 = "VpcAcl"
	VpcAclNamespace            = "plat-boson-infra"
	VpcNameLabel               = "boson.sensetime.com/vpc-name"
	VpcAclResourceLabel        = "boson.sensetime.com/vpc-acl-source"
	VpcAclResourceAPIValue     = "API"
	VpcAclServiceLabel         = "boson.sensetime.com/vpc-acl-service"
	VpcAclResourceServiceValue = "service"
)

// VPCsACLsServer
type VPCsACLsServer struct {
	*vpc.UnimplementedACLsServer
}

func invertVpcACL(acl *db.VpcAcls) *vpc.ACL {
	return &vpc.ACL{
		Id:           acl.ID,
		Name:         acl.Name,
		DisplayName:  acl.DisplayName,
		Description:  acl.Description,
		Uid:          acl.VpcAclID,
		ResourceType: acl.ResourceType,
		CreatorId:    acl.CreatorID,
		OwnerId:      acl.OwnerID,
		TenantId:     acl.TenantID,
		Zone:         acl.Zone,
		State:        vpc.ACL_State(vpc.ACL_State_value[acl.State]),
		CreateTime:   timestamppb.New(acl.CreateTime),
		UpdateTime:   timestamppb.New(acl.UpdateTime),
		Properties: &vpc.ACLProperties{
			Action:   vpc.ACLProperties_ACLAction(vpc.ACLProperties_ACLAction_value[acl.Action]),
			Src:      acl.Src,
			Dest:     acl.Dest,
			SrcPort:  acl.SrcPort,
			DestPort: acl.DestPort,
			Protocol: acl.Protocol,
			Priority: acl.Priority,
			Type:     vpc.ACLProperties_ACLType(vpc.ACLProperties_ACLType_value[acl.Type]),
		},
	}
}

func (VPCsACLsServer) ListACLs(ctx context.Context, request *vpc.ListACLsRequest) (res *vpc.ListACLsResponse, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "ACL", "ListACLs")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	vpcData, err := dao.GetVpcByName(rp.BosonProvider.Engine, request.GetVpcName())
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if !strings.Contains(vpcData.ID, request.ResourceGroupName) || !strings.Contains(vpcData.ID, request.SubscriptionName) {
		err = fmt.Errorf("request %v not match ID %s", request, vpcData.ID)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	/* return all acls, no fileter or pagesize*/
	aclDatas, err := dao.ListVPCACLs(rp.BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	acls := []*vpc.ACL{}
	for _, v := range aclDatas {
		acls = append(acls, invertVpcACL(v))
	}
	totalSize := len(acls)
	nextPageToken, err := aip.CalcNextPageToken(totalSize, len(acls), request)
	if err != nil {
		reason := fmt.Sprintf("calc NextPageToken %v , err: %v", zap.Any("request", request), zap.Error(err))
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}
	res = &vpc.ListACLsResponse{
		TotalSize:     int32(totalSize),
		NextPageToken: nextPageToken,
		Acls:          acls,
	}

	logrus.Infof("list VPC ACLs %v", request)
	return res, nil
}

func (VPCsACLsServer) GetACL(ctx context.Context, request *vpc.GetACLRequest) (res *vpc.ACL, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics(request.AclName, "ACL", "GetACL")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s does not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	vpcData, err := dao.GetVpcByName(rp.BosonProvider.Engine, request.VpcName)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if !strings.Contains(vpcData.ID, request.ResourceGroupName) || !strings.Contains(vpcData.ID, request.SubscriptionName) {
		err = fmt.Errorf("request %v not match ID %s", request, vpcData.ID)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	// TODO: @miaozhanyong, here may has security risk, maybe should use acl id
	aclData, err := dao.GetVPCACLByName(rp.BosonProvider.Engine, request.AclName)
	if err != nil {
		logrus.Errorf("Get acl %s error: %s", request.AclName, err)
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.AclNotFound, errors.Domain, nil, nil)
	}

	if vpcData.VPCID != aclData.VPCID {
		err = fmt.Errorf("request vpc_id %s does not match acl's vpc_id %s", vpcData.VPCID, aclData.VPCID)
		logrus.Errorf("Get acl %s error: %s", request.AclName, err)
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	res = invertVpcACL(aclData)
	logrus.Infoln("Get VPC ACL: ", request.AclName)

	return res, nil
}

func validateVpcAclAddress(aclAddr string) error {
	fields := strings.Split(aclAddr, ",")
	for _, v := range fields {
		if strings.Contains(v, "/") {
			// validate subnet
			_, _, err := net.ParseCIDR(v)
			if err != nil {
				return err
			}
		} else if strings.Contains(v, "-") {
			// validate ip range
			return fmt.Errorf("no support ip range: %s", v)
			/*
				ips := strings.Split(v, "-")
				if len(ips) != 2 {
					return fmt.Errorf("invalid ip range: %s", v)
				}
				startIP := net.ParseIP(ips[0])
				endIP := net.ParseIP(ips[1])
				result := bytes.Compare(startIP, endIP)
				if result >= 0 {
					return fmt.Errorf("invalid ip range: %s", v)
				}
			*/
		} else {
			// validate ip
			if net.ParseIP(v) == nil {
				return fmt.Errorf("invalid ip address: %s", v)
			}
		}
	}

	return nil
}

func validateVpcAclPort(port string) error {
	fields := strings.Split(port, ",")
	for _, v := range fields {
		if strings.Contains(v, "-") {
			ps := strings.Split(v, "-")
			if len(ps) != 2 {
				return fmt.Errorf("invalid port range: %s", v)
			}
			p1, err := strconv.Atoi(ps[0])
			if err != nil || p1 > 65635 || p1 <= 0 {
				return fmt.Errorf("invalid port range: %s", v)
			}
			p2, err := strconv.Atoi(ps[1])
			if err != nil || p2 > 65635 || p2 <= 0 {
				return fmt.Errorf("invalid port range: %s", v)
			}
			if p2 <= p1 {
				return fmt.Errorf("invalid port range: %s", v)
			}
		} else {
			if p, err := strconv.Atoi(v); err != nil || p > 65635 || p <= 0 {
				return fmt.Errorf("invalid port: %s", port)
			}
		}
	}

	return nil
}

/*get apc acl capacity*/
func getAclCap(t vpcv1.ACLProperties_ACLType) int32 {
	cap := int32(0)

	switch t {
	case vpcv1.ACLProperties_SYSTEM_SERVICE_ACL:
		cap = int32(rp.SYSTEM_ACL_GATEWAY_SERVICE_MAX - rp.SYSTEM_ACL_GATEWAY_SERVICE_MIN)
	case vpcv1.ACLProperties_SYSTEM_TENANT_ACL:
		cap = int32(rp.SYSTEM_ACL_RESERVED_PRIORITY_MAX - rp.SYSTEM_ACL_RESERVED_PRIORITY_MIN)
	case vpcv1.ACLProperties_SYSTEM_DEFAULT_ACL:
		cap = int32(rp.SYSTEM_ACL_HIGH_PRIORITY_MAX - rp.SYSTEM_ACL_HIGH_PRIORITY_MIN)
	case vpcv1.ACLProperties_CUSTOM_ACL:
		cap = int32(rp.CUSTOM_ACL_MID_PRIORITY_MAX - rp.CUSTOM_ACL_MID_PRIORITY_MIN)
	}

	return cap
}

func CalcAclCRPriority(t vpcv1.ACLProperties_ACLType, pri int32) int {
	crPri := int(0)

	switch t {
	case vpcv1.ACLProperties_SYSTEM_SERVICE_ACL:
		crPri = int(pri) + int(rp.SYSTEM_ACL_GATEWAY_SERVICE_MIN)
	case vpcv1.ACLProperties_SYSTEM_TENANT_ACL:
		crPri = int(pri) + int(rp.SYSTEM_ACL_RESERVED_PRIORITY_MIN)
	case vpcv1.ACLProperties_SYSTEM_DEFAULT_ACL:
		crPri = int(pri) + int(rp.SYSTEM_ACL_HIGH_PRIORITY_MIN)
	case vpcv1.ACLProperties_CUSTOM_ACL:
		crPri = int(pri) + int(rp.CUSTOM_ACL_MID_PRIORITY_MIN)
	}

	return crPri
}

func AllocatePriority(t vpcv1.ACLProperties_ACLType, step int, usedPriority []int) (int, error) {
	var start, end int
	switch t {
	case vpcv1.ACLProperties_SYSTEM_SERVICE_ACL:
		start = rp.SYSTEM_ACL_GATEWAY_SERVICE_MAX
		step = 0 - step
		end = rp.SYSTEM_ACL_GATEWAY_SERVICE_MIN
	// TODO: for other type, may need to start from different number and with
	// different step
	default:
		start = rp.SYSTEM_ACL_GATEWAY_SERVICE_MIN
	}

	endFunc := func(start, end int, index int) bool {
		if start >= end {
			return (index > end)
		}

		return (index <= end)
	}

	for i := start; endFunc(start, end, i); i = i + step {
		if !utils.IsIn(i, usedPriority) {
			return i, nil
		}
	}

	return -1, fmt.Errorf("alloc priority for type %s with step %d failed", t, step)
}

func validateVpcACL(acl *vpc.ACL) error {
	/*
	*1. src,dest ip checking
	*2. sport,dport checking
	*3. priority checking
	*4. protocol checking
	*5. id checking
	*6. name
	*7. type
	 */

	if acl.Name == "" || !strings.HasPrefix(acl.Name, "acl-") {
		return fmt.Errorf("invalid acl name format: %s", acl.Name)
	}

	if acl.Properties.Type != vpcv1.ACLProperties_SYSTEM_TENANT_ACL {
		return fmt.Errorf("acl type just only support SYSTEM_TENANT_ACL: %s", acl.Properties.Type.String())
	}

	priority_max := getAclCap(acl.Properties.Type)
	if acl.Properties.Priority < 1 || acl.Properties.Priority > priority_max {
		return fmt.Errorf("acl priority %d out of range [1, %d]", acl.Properties.Priority, priority_max)
	}

	if acl.Properties.Protocol != "" {
		if !utils.In(acl.Properties.Protocol, []string{rp.ProtocolALL, rp.ProtocolTCP, rp.ProtocolUDP, rp.ProtocolICMP}) {
			return fmt.Errorf("invalid acl protocol %s", acl.Properties.Protocol)
		}
	}

	if acl.Properties.Src != "" {
		if err := validateVpcAclAddress(acl.Properties.Src); err != nil {
			return err
		}
	}

	if acl.Properties.Dest != "" {
		if err := validateVpcAclAddress(acl.Properties.Dest); err != nil {
			return err
		}
	}

	if acl.Properties.SrcPort != "" {
		if err := validateVpcAclPort(acl.Properties.SrcPort); err != nil {
			return err
		}

		if !utils.In(acl.Properties.Protocol, []string{rp.ProtocolTCP, rp.ProtocolUDP}) {
			return fmt.Errorf("invalid acl protocol %s with source port", acl.Properties.Protocol)
		}
	}

	if acl.Properties.DestPort != "" {
		if err := validateVpcAclPort(acl.Properties.DestPort); err != nil {
			return err
		}
		if !utils.In(acl.Properties.Protocol, []string{rp.ProtocolTCP, rp.ProtocolUDP}) {
			return fmt.Errorf("invalid acl protocol %s with dest port", acl.Properties.Protocol)
		}
	}

	return nil
}

func (VPCsACLsServer) CreateACL(ctx context.Context, request *vpc.CreateACLRequest) (res *vpc.ACL, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics(request.AclName, "ACL", "CreateACL")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
		/*
			if err != nil {
				cloudAuditCreateAclError(ctx, request.AclName, request, struct{}{}, err.Error())
			} else {
				cloudAuditCreateAclSuccess(ctx, request.AclName, request, struct{}{})
			}
		*/
	}()

	/*
	* 1. arguments checking
	* 2. db
	* 3. cr
	 */
	logrus.Infof("Create VPC ACL called: %+v", request)
	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.Acl == nil || request.Acl.Properties == nil {
		reason := fmt.Sprintf("error request: %+v, acl or acl properties is empty", request)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if !strings.Contains(request.Acl.Id, request.ResourceGroupName) || !strings.Contains(request.Acl.Id, request.SubscriptionName) ||
		!strings.Contains(request.Acl.Id, request.Zone) {
		err = fmt.Errorf("request %v not match ID %s", request, request.Acl)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.Acl.Properties.Src != "" {
		request.Acl.Properties.Src = strings.TrimSpace(request.Acl.Properties.Src)
	}

	if request.Acl.Properties.Dest != "" {
		request.Acl.Properties.Dest = strings.TrimSpace(request.Acl.Properties.Dest)
	}

	if request.Acl.Properties.SrcPort != "" {
		request.Acl.Properties.SrcPort = strings.TrimSpace(request.Acl.Properties.SrcPort)
	}

	if request.Acl.Properties.DestPort != "" {
		request.Acl.Properties.DestPort = strings.TrimSpace(request.Acl.Properties.DestPort)
	}

	if request.Acl.CreateTime == nil {
		request.Acl.CreateTime = timestamppb.Now()
	}

	if request.Acl.UpdateTime == nil {
		request.Acl.UpdateTime = request.Acl.CreateTime
	}

	if request.Acl.Properties.Protocol == "" {
		request.Acl.Properties.Protocol = rp.ProtocolALL
	} else {
		request.Acl.Properties.Protocol = strings.ToLower(request.Acl.Properties.Protocol)
	}

	if request.Acl.Name == "" {
		request.Acl.Name = request.AclName
	}

	if request.Acl.Uid == "" {
		request.Acl.Uid = utils.UUIDGen()
	}

	if err := validateVpcACL(request.Acl); err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	vpcData, err := dao.GetVpcByName(rp.BosonProvider.Engine, request.VpcName)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if !strings.Contains(vpcData.ID, request.ResourceGroupName) || !strings.Contains(vpcData.ID, request.SubscriptionName) {
		err = fmt.Errorf("request %v not match ID %s", request, vpcData.ID)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	has, err := dao.ExistVPCACL(rp.BosonProvider.Engine, request.AclName)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if has {
		err = fmt.Errorf("VPC acl %v has been exist", request.AclName)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.AlreadyExists, errors.AclExist, errors.Domain, nil, nil)
	}

	has, err = dao.ExistVPCACLByPriorityType(rp.BosonProvider.Engine, request.Acl.Properties.Priority, request.Acl.Properties.Type.String(), vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if has {
		err = fmt.Errorf("VPC acl %v priority %d has been exist", request.AclName, request.Acl.Properties.Priority)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.AlreadyExists, errors.AclPriorityExist, errors.Domain, nil, nil)
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetVPC")
	vpcCr, err := rp.BosonProvider.KubeovnClient.Vpcs().Get(context.Background(), vpcData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	// create DB
	aclData := &db.VpcAcls{
		VpcAclID:     request.Acl.Uid,
		DisplayName:  request.Acl.DisplayName,
		Description:  request.Acl.Description,
		Action:       request.Acl.Properties.Action.String(),
		Src:          request.Acl.Properties.Src,
		SrcPort:      request.Acl.Properties.SrcPort,
		Dest:         request.Acl.Properties.Dest,
		DestPort:     request.Acl.Properties.DestPort,
		Protocol:     request.Acl.Properties.Protocol,
		Priority:     request.Acl.Properties.Priority,
		Type:         request.Acl.Properties.Type.String(),
		VPCID:        vpcData.VPCID,
		ID:           request.Acl.Id,
		Name:         request.AclName,
		ResourceType: request.Acl.ResourceType,
		Zone:         request.Zone,
		State:        vpc.ACL_CREATING.String(),
		CreatorID:    request.Acl.CreatorId,
		OwnerID:      request.Acl.OwnerId,
		TenantID:     request.Acl.TenantId,
		CreateTime:   request.Acl.CreateTime.AsTime(),
		UpdateTime:   request.Acl.UpdateTime.AsTime(),
		Deleted:      false,
	}

	err = dao.AddVPCACL(rp.BosonProvider.Engine, aclData)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if strings.Contains(request.Acl.Properties.SrcPort, "-") {
		request.Acl.Properties.SrcPort = strings.ReplaceAll(request.Acl.Properties.SrcPort, "-", ":")
	}

	if strings.Contains(request.Acl.Properties.DestPort, "-") {
		request.Acl.Properties.DestPort = strings.ReplaceAll(request.Acl.Properties.DestPort, "-", ":")
	}

	// create ACL CR
	acl := &networkv1.VpcAcl{
		TypeMeta: metav1.TypeMeta{
			Kind:       VPCACLKind,
			APIVersion: networkv1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: request.AclName,
			Labels: map[string]string{
				VpcNameLabel:        vpcData.Name,
				VpcAclResourceLabel: VpcAclResourceAPIValue,
			},
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpcCr, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    rp.VPCKind,
				}),
			},
			Annotations: map[string]string{
				rp.AnnoRegion: db.Region,
				rp.AnnoAz:     db.AZ,
			},
		},

		Spec: networkv1.VpcAclSpec{
			Src:      aclData.Src,
			SrcPort:  aclData.SrcPort,
			Dest:     aclData.Dest,
			DestPort: aclData.DestPort,
			AclID:    aclData.VpcAclID,
			Vpc:      vpcData.Name,
			Action:   strings.ToLower(aclData.Action),
			Protocol: aclData.Protocol,
			Priority: CalcAclCRPriority(request.GetAcl().Properties.Type, aclData.Priority),
		},
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("CreateACL")
	_, err = rp.BosonProvider.BosonNetClient.VpcAcls(VpcAclNamespace).Create(context.Background(), acl, metav1.CreateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		// to do, not rollback for debug trace
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	// notify the upper layer
	logrus.Infoln("Created EIP ACL: ", request.AclName)

	return request.Acl, nil
}

func (VPCsACLsServer) UpdateACL(ctx context.Context, request *vpc.UpdateACLRequest) (res *vpc.ACL, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics(request.AclName, "ACL", "UpdateACL")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
		/*
			if err != nil {
				cloudAuditUpdateAclError(ctx, request.AclName, request, struct{}{}, err.Error())
			} else {
				cloudAuditUpdateAclSuccess(ctx, request.AclName, request, struct{}{})
			}
		*/
	}()

	logrus.Infof("Update VPC ACL called: %+v", request)
	if request.AclName == "" || request.VpcName == "" {
		reason := fmt.Sprintf("error request: %+v, acl name or vpc name is empty", request)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	// now just support description& displayname, will be support more acl properties in feature
	if request.Acl.Description == "" && request.Acl.GetDisplayName() == "" {
		err = fmt.Errorf("request properties %v invalid, Description and displayname are empty", request.Acl)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	aclData, err := dao.GetVPCACLByName(rp.BosonProvider.Engine, request.AclName)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.AclNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	has, err := dao.CheckVpcExists(rp.BosonProvider.Engine, request.VpcName)
	if err != nil || !has {
		logrus.Errorln(err)
		if !has {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if !strings.Contains(aclData.ID, request.ResourceGroupName) || !strings.Contains(aclData.ID, request.SubscriptionName) {
		err = fmt.Errorf("request %v not match ID %s", request, aclData.ID)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if utils.In("description", request.UpdateMask.Paths) {
		aclData.Description = request.Acl.Description
	}

	if utils.In("displayName", request.UpdateMask.Paths) {
		aclData.DisplayName = request.Acl.GetDisplayName()
	}

	// update db
	if err := dao.UpdateVPCACL(rp.BosonProvider.Engine, aclData); err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	logrus.Infoln("update VPC ACL: ", request.AclName)

	return invertVpcACL(aclData), nil
}

func (VPCsACLsServer) DeleteACL(ctx context.Context, request *vpc.DeleteACLRequest) (res *emptypb.Empty, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.AclName, "ACL", "DeleteACL")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
		/*
			if err != nil {
				cloudAuditDeleteAclError(ctx, resourceUID, request, struct{}{}, err.Error())
			} else {
				cloudAuditDeleteAclSuccess(ctx, resourceUID, request, struct{}{})
			}
		*/
	}()

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	aclData, err := dao.GetVPCACLByName(rp.BosonProvider.Engine, request.AclName)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.AclNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if !strings.Contains(aclData.ID, request.ResourceGroupName) || !strings.Contains(aclData.ID, request.SubscriptionName) {
		err = fmt.Errorf("request %v not match aclData ID %s", request, aclData.ID)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	vpcData, err := dao.GetVpc(rp.BosonProvider.Engine, aclData.VPCID)
	if err != nil || vpcData == nil {
		logrus.Errorf("fail to get vpc %s: %v", aclData.VPCID, err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if vpcData.Name != request.VpcName {
		err = fmt.Errorf("request %v not match aclData VpcName %s", request, aclData.VPCID)
		logrus.Error(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if strings.EqualFold(aclData.State, vpc.ACL_CREATING.String()) ||
		strings.EqualFold(aclData.State, vpc.ACL_UPDATING.String()) ||
		strings.EqualFold(aclData.State, vpc.ACL_DELETING.String()) {
		reason := fmt.Sprintf("VPC ACL %v can't be deleted with state: %v", request.AclName, aclData.State)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetACL")

	_, err = rp.BosonProvider.BosonNetClient.VpcAcls(VpcAclNamespace).
		Get(context.Background(), request.AclName, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		if k8s_errors.IsNotFound(err) {
			if err := dao.SetVPCACLState(rp.BosonProvider.Engine, aclData.VpcAclID, vpc.ACL_DELETED.String()); err != nil {
				logrus.Errorln(err)
				return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
			}
		} else {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
		}
	} else {
		// update db
		if err := dao.SetVPCACLState(rp.BosonProvider.Engine, aclData.VpcAclID, vpc.ACL_DELETING.String()); err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		// delete CR
		k8sRTMetrics = exporter.InitK8sRTMetrics("DeleteACL")
		err = rp.BosonProvider.BosonNetClient.VpcAcls(VpcAclNamespace).
			Delete(context.Background(), request.AclName, metav1.DeleteOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil {
			logrus.Errorln(err)
			if err := dao.SetACLState(rp.BosonProvider.Engine, aclData.VpcAclID, vpc.ACL_FAILED.String()); err != nil {
				logrus.Errorln(err)
				return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
			}
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
		}
	}

	logrus.Infof("Deleting VPC ACL %v in VPC %v", request.AclName, request.VpcName)

	return &emptypb.Empty{}, nil
}

/*
func cloudAuditCreateVpcAclError(
	ctx context.Context, resourceID string, request *vpc.CreateACLRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendACL(
		ctx,
		types.CloudAuditOperationTypeCreateAcls, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.Acl.Name,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditCreateVpcAclSuccess(
	ctx context.Context, resourceID string, request *vpc.CreateACLRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendACL(
		ctx,
		types.CloudAuditOperationTypeCreateAcls, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.AclName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("create success"),
	)
}

func cloudAuditUpdateVpcAclError(
	ctx context.Context, resourceUID string, request *vpc.UpdateACLRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendACL(
		ctx,
		types.CloudAuditOperationTypeUpdateAcls, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceUID, request.AclName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditUpdateVpcAclSuccess(
	ctx context.Context, resourceUID string, request *vpc.UpdateACLRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendACL(
		ctx,
		types.CloudAuditOperationTypeUpdateAcls, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceUID, request.AclName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("update success"),
	)
}

func cloudAuditDeleteVpcAclError(
	ctx context.Context, resourceUID string, request *vpc.DeleteACLRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendACL(
		ctx,
		types.CloudAuditOperationTypeDeleteAcls, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceUID, request.AclName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditDeleteVpcAclSuccess(
	ctx context.Context, resourceUID string, request *vpc.DeleteACLRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeDeleteAcls, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceUID, request.AclName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("delete success"),
	)
}
*/
