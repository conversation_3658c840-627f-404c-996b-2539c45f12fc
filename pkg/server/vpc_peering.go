package server

import (
	"context"
	"fmt"
	"math/rand"
	"strings"
	"time"

	v1api "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	v1 "github.com/kubeovn/kube-ovn/pkg/client/clientset/versioned/typed/kubeovn/v1"
	kubeovninformer "github.com/kubeovn/kube-ovn/pkg/client/informers/externalversions"
	v1lister "github.com/kubeovn/kube-ovn/pkg/client/listers/kubeovn/v1"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/emptypb"
	k8serrors "k8s.io/apimachinery/pkg/api/errors"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/selection"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
)

const (
	remoteZoneLabel = "pcc.boson.sensetime.com/remote_zone"
	vpcLabel        = "pcc.boson.sensetime.com/vpc"
)

type VpcPeeringServer struct {
	vpc.UnimplementedVpcPeeringsServer
	vpcPeeringLister v1lister.VpcPeeringLister
	client           v1.KubeovnV1Client
}

func NewVpcPeeringServer() *VpcPeeringServer {
	factory := kubeovninformer.NewSharedInformerFactoryWithOptions(resourceprovider.BosonProvider.KubeovnClientset, 0)
	peeringInformer := factory.Kubeovn().V1().VpcPeerings()
	peeringLister := peeringInformer.Lister()

	stop := make(chan struct{})
	factory.Start(stop)

	return &VpcPeeringServer{
		vpcPeeringLister: peeringLister,
		client:           *resourceprovider.BosonProvider.KubeovnClient,
	}
}

func (s *VpcPeeringServer) CreateVpcPeering(ctx context.Context, request *vpc.CreateVpcPeeringRequest) (*vpc.VpcPeering, error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "VpcPeering", "CreateVpcPeering")
	var err error
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	properties := request.VpcPeering.Properties

	pcc, err := dao.GetPccByName(resourceprovider.BosonProvider.Engine, request.VpcPeeringName)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.PccNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	var isLocal bool

	switch db.AZ {
	case pcc.LocalZone:
		isLocal = true
	case pcc.RemoteZone:
		isLocal = false
	default:
		if db.Region == pcc.LocalZone {
			// XXX: fix for 01a
			isLocal = true
		} else {
			if db.Region == pcc.RemoteZone {
				isLocal = false
			} else {
				err := fmt.Errorf("db zone %s mismatch: local zone: %s, remote zone: %s, db region %s",
					db.AZ, pcc.LocalZone, pcc.RemoteZone, db.Region)
				logrus.Errorln(err)
				return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
					"detail": err.Error(),
				})
			}
		}
	}

	vpcData, err := dao.GetVpcByName(resourceprovider.BosonProvider.Engine, properties.Vpc)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	vpcQuota, err := dao.GetVpcQuotaByVpcID(resourceprovider.BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcQuotaNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	selector := labels.NewSelector()
	r, _ := labels.NewRequirement(vpcLabel, selection.Equals, []string{properties.Vpc})
	selector.Add(*r)

	vpcps, err := s.vpcPeeringLister.List(selector)
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}

	if len(vpcps) >= int(vpcQuota.PeerCount) {
		reason := fmt.Sprintf("PCC quota for VPC %v is used up", vpcData.Name)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.ResourceExhausted, errors.VpcPeeringResourceExhausted, errors.Domain, nil, nil)
	}

	localGatewayNodes := resourceprovider.BosonProvider.RpConfig.Boson.TSGatewayNodes
	rand.Seed(time.Now().UnixNano())
	rand.Shuffle(len(localGatewayNodes), func(i, j int) {
		localGatewayNodes[i], localGatewayNodes[j] = localGatewayNodes[j], localGatewayNodes[i]
	})
	vpcp := &v1api.VpcPeering{
		ObjectMeta: metav1.ObjectMeta{
			Name: request.VpcPeeringName,
			Labels: map[string]string{
				remoteZoneLabel: request.VpcPeering.Properties.RemoteZone,
				vpcLabel:        properties.Vpc,
			},
		},
		Spec: v1api.VpcPeeringSpec{
			Vpc:               properties.Vpc,
			Type:              properties.Type,
			TransitSwitch:     properties.TransitSwitch,
			LocalGatewayIp:    properties.LocalGatewayIp,
			LocalGatewayNodes: localGatewayNodes,
			RemoteVpc:         properties.RemoteVpc,
			RemoteCIDR:        properties.RemoteCidr,
			RemoteGatewayIp:   properties.RemoteGatewayIp,
		},
	}

	vpcp, err = s.client.VpcPeerings().Create(context.Background(), vpcp, metav1.CreateOptions{})
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	if isLocal {
		pcc.LocalState = vpc.State_CREATING.String()
		err = dao.UpdatePCCLocalState(resourceprovider.BosonProvider.Engine, pcc)
	} else {
		pcc.RemoteState = vpc.State_CREATING.String()
		err = dao.UpdatePCCRemoteState(resourceprovider.BosonProvider.Engine, pcc)
	}

	if err != nil {
		logrus.Warnf("Update PCC Failed: %s", err.Error())
	}

	return &vpc.VpcPeering{
		Name:       vpcp.Name,
		Zone:       db.AZ,
		State:      vpc.State_CREATING,
		Properties: request.VpcPeering.Properties,
	}, nil
}

func (s *VpcPeeringServer) DeleteVpcPeering(ctx context.Context, req *vpc.DeleteVpcPeeringRequest) (*emptypb.Empty, error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "VIP", "DeleteVIP")
	var err error
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	res := &emptypb.Empty{}
	res.Reset()

	err = s.client.VpcPeerings().Delete(context.Background(), req.VpcPeeringName, metav1.DeleteOptions{})

	if k8serrors.IsNotFound(err) {
		pcc, err := dao.GetPccByName(resourceprovider.BosonProvider.Engine, req.VpcPeeringName)
		if err != nil {
			logrus.Errorln(err)
			// not found
			if strings.Contains(err.Error(), "not found") {
				return res, nil
			}
			return res, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}

		switch db.AZ {
		case pcc.LocalZone:
			pcc.LocalState = "DELETED"
			err = dao.UpdatePCCLocalState(resourceprovider.BosonProvider.Engine, pcc)
		case pcc.RemoteZone:
			pcc.RemoteState = "DELETED"
			err = dao.UpdatePCCRemoteState(resourceprovider.BosonProvider.Engine, pcc)
		}

		if err != nil {
			logrus.Errorln(err)
			return res, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}

		return res, nil
	}

	if err != nil {
		logrus.Errorln(err)
		return res, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	return res, nil
}

func (s *VpcPeeringServer) UpdateVpcPeering(ctx context.Context, request *vpc.UpdateVpcPeeringRequest) (*vpc.VpcPeering, error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics("", "VpcPeering", "UpdateVpcPeering")

	defer func() {
		httpRTMetrics.Code = "404"
		httpRTMetrics.ObserveDurationAndLog()
	}()

	return nil, errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
}

func (s *VpcPeeringServer) GetVpcPeering(ctx context.Context, request *vpc.GetVpcPeeringRequest) (*vpc.VpcPeering, error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "VpcPeering", "GetVpcPeering")
	var err error
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	vpcp, err := s.vpcPeeringLister.Get(request.VpcPeeringName)
	if err != nil {
		if kerrors.IsNotFound(err) {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcPeeringNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	}

	vpcData, err := dao.GetVpcByName(resourceprovider.BosonProvider.Engine, vpcp.Spec.Vpc)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	state := vpc.State_ACTIVE

	for _, cond := range vpcp.Status.Conditions {
		if cond.Type != "Ready" {
			state = vpc.State_CREATING
			break
		}
	}

	cidrs := strings.Split(vpcData.CIDR, ",")
	return &vpc.VpcPeering{
		Name:  vpcp.Name,
		Zone:  db.AZ,
		State: state,
		Properties: &vpc.VpcPeeringProperties{
			Type:              vpcp.Spec.Type,
			LocalCidr:         cidrs,
			TransitSwitch:     vpcp.Spec.TransitSwitch,
			LocalGatewayIp:    vpcp.Spec.LocalGatewayIp,
			LocalGatewayNodes: vpcp.Spec.LocalGatewayNodes,
			RemoteCidr:        vpcp.Spec.RemoteCIDR,
			RemoteGatewayIp:   vpcp.Spec.RemoteGatewayIp,
			RemoteVpc:         vpcp.Spec.RemoteVpc,
			RemoteZone:        vpcp.Labels[remoteZoneLabel],
		},
	}, nil

}

func (s *VpcPeeringServer) ListVpcPeering(ctx context.Context, request *vpc.ListVpcPeeringRequest) (*vpc.ListVpcPeeringResponse, error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "VpcPeering", "ListVpcPeering")
	var err error
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	// TODO: selector
	selector := labels.Everything()

	vpcps, err := s.vpcPeeringLister.List(selector)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	}

	res := &vpc.ListVpcPeeringResponse{
		TotalSize:   int32(len(vpcps)),
		VpcPeerings: []*vpc.VpcPeering{},
	}

	for _, vpcp := range vpcps {
		state := vpc.State_ACTIVE

		for _, cond := range vpcp.Status.Conditions {
			if cond.Type != "Ready" {
				state = vpc.State_CREATING
				break
			}
		}

		vpcData, err := dao.GetVpcByName(resourceprovider.BosonProvider.Engine, vpcp.Spec.Vpc)
		if err != nil {
			logrus.Errorln(err)
			if strings.Contains(err.Error(), "not found") {
				return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
			}
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}

		cidrs := strings.Split(vpcData.CIDR, ",")
		res.VpcPeerings = append(res.VpcPeerings, &vpc.VpcPeering{
			Name:  vpcp.Name,
			Zone:  db.AZ,
			State: state,
			Properties: &vpc.VpcPeeringProperties{
				Type:              vpcp.Spec.Type,
				LocalCidr:         cidrs,
				TransitSwitch:     vpcp.Spec.TransitSwitch,
				LocalGatewayIp:    vpcp.Spec.LocalGatewayIp,
				LocalGatewayNodes: vpcp.Spec.LocalGatewayNodes,
				RemoteCidr:        vpcp.Spec.RemoteCIDR,
				RemoteGatewayIp:   vpcp.Spec.RemoteGatewayIp,
				RemoteVpc:         vpcp.Spec.RemoteVpc,
				RemoteZone:        vpcp.Labels[remoteZoneLabel],
			},
		})
	}

	return res, nil
}

func (s *VpcPeeringServer) ResizeVpcPeering(ctx context.Context, request *vpc.ResizeVpcPeeringRequest) (*vpc.VpcPeering, error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "VpcPeering", "ResizeVpcPeering")

	defer func() {
		httpRTMetrics.Code = "404"
		httpRTMetrics.ObserveDurationAndLog()
	}()

	return nil, errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
}

func (s *VpcPeeringServer) ReleaseVpcPeering(ctx context.Context, request *vpc.ReleaseVpcPeeringRequest) (*emptypb.Empty, error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "VpcPeering", "ReleaseVpcPeering")

	defer func() {
		httpRTMetrics.Code = "404"
		httpRTMetrics.ObserveDurationAndLog()
	}()

	return nil, errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
}
