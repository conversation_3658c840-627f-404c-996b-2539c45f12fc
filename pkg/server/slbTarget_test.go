package server

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
	slbv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/slb/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"google.golang.org/grpc/codes"
)

// TODO：Null pointer
// func TestTargetGroupAddTargetsErr(t *testing.T) {
// 	p := gomonkey.NewPatches()
// 	defer p.Reset()

// 	var h *exporter.HTTPRTMetrics
// 	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
// 	p.ApplyFunc(cloudAuditAddSlbTargetError, func(ctx context.Context, resourceID string, request *slbv1.TargetGroupAddTargetsRequest, response interface{}, reason string) {
// 	})
// 	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
// 		Engine: db.NewEngineWrapper(mockEngine),
// 	})
// 	p.ApplyFunc(dao.GetSLBByResourceID, func(_ *db.EngineWrapper, _ string, _ bool) (*db.Slbs, error) {
// 		return nil, fmt.Errorf("slb not exist")
// 	})
// 	slbServer := SLBsServer{}

// 	p.ApplyGlobalVar(&db.AZ, "test")

// 	request := &slbv1.CreateListenerRequest{
// 		Zone:     "test11",
// 		Listener: &slbv1.Listener{},
// 	}

// 	ctx := context.Background()

// 	_, err := slbServer.CreateListener(ctx, request)
// 	assert.NotNil(t, err)

// 	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
// 		"detail": "SLB Server get slb DB record fail.errslb not exist",
// 	})
// 	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
// }

func TestTargetGroupRemoveTargetsErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditRemoveSlbTargetError, func(ctx context.Context, resourceID string, request *slbv1.TargetGroupRemoveTargetsRequest, response interface{}, reason string) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetSLBByResourceID, func(_ *db.EngineWrapper, _ string, _ bool) (*db.Slbs, error) {
		return nil, fmt.Errorf("err")
	})
	slbServer := SLBsServer{}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &slbv1.TargetGroupRemoveTargetsRequest{
		Zone: "test11",
	}

	ctx := context.Background()

	_, err := slbServer.TargetGroupRemoveTargets(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "err",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestTargetsUpdateErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditUpdateSlbTargetError, func(ctx context.Context, resourceID string, request *slbv1.TargetsUpdateRequest, response interface{}, reason string) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetSLBByResourceID, func(_ *db.EngineWrapper, _ string, _ bool) (*db.Slbs, error) {
		return nil, fmt.Errorf("slb not exist")
	})
	slbServer := SLBsServer{}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &slbv1.TargetsUpdateRequest{
		Zone: "test11",
	}

	ctx := context.Background()

	_, err := slbServer.TargetsUpdate(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.SlbTargetGroupNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestListTargetsErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetSLBByResourceID, func(_ *db.EngineWrapper, _ string, _ bool) (*db.Slbs, error) {
		return nil, fmt.Errorf("slb not exist")
	})
	slbServer := SLBsServer{}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &slbv1.ListTargetsRequest{
		Zone:    "test11",
		SlbName: "test",
	}

	ctx := context.Background()

	_, err := slbServer.ListTargets(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.SlbTargetGroupNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetTargetErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetSLBTargetByResourceID, func(_ *db.EngineWrapper, _ string) (*db.SlbTargets, error) {
		return nil, fmt.Errorf("target not exist")
	})
	slbServer := SLBsServer{}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &slbv1.GetTargetRequest{
		Zone:    "test11",
		SlbName: "test",
	}

	ctx := context.Background()

	_, err := slbServer.GetTarget(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.SlbTargetNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}
