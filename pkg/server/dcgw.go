package server

import (
	"context"
	"fmt"
	"net"
	"net/http"
	"strings"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"

	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/dc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// DCGWsServer 为 DCGWsServer interface 的实现
type DCGWsServer struct {
	*dc.UnimplementedDCGWsServer
}

const (
	MasterStandby = "master-standby"
)

func (DCGWsServer) CreateDCGW(ctx context.Context, request *dc.CreateDCGWRequest) (res *dc.DCGW, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.DcgwName, "DCGW", "CreateDCGW")
	resourceUID := request.DcgwName
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditCreateDcgwError(ctx, resourceUID, request, struct{}{}, err.Error())
		} else {
			cloudAuditCreateDcgwSuccess(ctx, resourceUID, request, struct{}{})
		}
	}()
	logrus.Infof("CreateDCGW called: %+v", request)

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	has, err := dao.ExistDcgw(resourceprovider.BosonProvider.Engine, request.DcgwName)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if has {
		err = fmt.Errorf("DCGW: %s already exist", request.DcgwName)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.AlreadyExists, errors.DcgwExist, errors.Domain, nil, nil)
	}

	if err = validateParametersForCreateDcgw(request); err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	// allocate
	dcgwData, err := AddDcgwFromAllocateResources(ctx, request)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	}

	return invertDcgw(dcgwData), nil
}

func (DCGWsServer) DeleteDCGW(ctx context.Context, request *dc.DeleteDCGWRequest) (res *emptypb.Empty, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.DcgwName, "DCGW", "DeleteDCGW")
	resourceUID := request.DcgwName
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditDeleteDcgwError(ctx, resourceUID, request, struct{}{}, err.Error())
		} else {
			cloudAuditDeleteDcgwSuccess(ctx, resourceUID, request, struct{}{})
		}
	}()
	logrus.Infof("DeleteDCGW called: %+v", request)

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	has, err := dao.ExistDcgwResource(resourceprovider.BosonProvider.Engine, request.DcgwName, request.SubscriptionName, request.ResourceGroupName)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if !has {
		reason := fmt.Sprintf("DCGW %v not exist", request.DcgwName)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DcgwNotFound, errors.Domain, nil, nil)
	}

	// check dcvc
	has, err = dao.ExistDcvcByDcgwName(resourceprovider.BosonProvider.Engine, request.DcgwName)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if has {
		reason := fmt.Sprintf("DCVCByDcgwName %v exist", request.DcgwName)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.AlreadyExists, errors.DcvcExist, errors.Domain, nil, nil)
	}

	if err := dao.SetDcgwState(resourceprovider.BosonProvider.Engine, request.DcgwName, dc.DCGW_DELETED.String()); err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	return &emptypb.Empty{}, nil
}

func (DCGWsServer) UpdateDCGW(ctx context.Context, request *dc.UpdateDCGWRequest) (res *dc.DCGW, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.DcgwName, "DCGW", "UpdateDCGW")
	resourceUID := request.DcgwName
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditUpdateDcgwError(ctx, resourceUID, request, struct{}{}, err.Error())
		} else {
			cloudAuditUpdateDcgwSuccess(ctx, resourceUID, request, struct{}{})
		}
	}()
	logrus.Infof("UpdateDCGW called: %+v", request)

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.Dcgw.GetDisplayName() == "" && request.Dcgw.Description == "" && request.Dcgw.Properties == nil {
		reason := "just support to update DisplayName、Description、Properties"
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	has, err := dao.ExistDcgwResource(resourceprovider.BosonProvider.Engine, request.DcgwName, request.SubscriptionName, request.ResourceGroupName)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if !has {
		reason := fmt.Sprintf("DCGW %v not exist", request.DcgwName)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DcgwNotFound, errors.Domain, nil, nil)
	}

	dcgwData, err := dao.GetDcgwByName(resourceprovider.BosonProvider.Engine, request.DcgwName)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DcgwNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if request.Dcgw.GetDisplayName() != "" {
		dcgwData.DisplayName = request.Dcgw.GetDisplayName()
	}
	if request.Dcgw.Description != "" {
		dcgwData.Description = request.Dcgw.Description
	}
	if request.Dcgw.Properties != nil {
		for _, cidr := range request.Dcgw.Properties.RemoteCidr {
			if netIP := net.ParseIP(cidr); netIP == nil {
				if ip, ipNet, err := net.ParseCIDR(cidr); err != nil || !ip.Equal(ipNet.IP) {
					reason := fmt.Sprintf("element %s in cidrs=%s format error(not ip or cidr or network address for the given prefix), please correct it", cidr, request.Dcgw.Properties.RemoteCidr)
					logrus.Errorln(reason)
					return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
						"detail": reason,
					})
				}
			}
		}
		if dcgwData.LocalCidr == strings.Join(request.Dcgw.Properties.LocalCidr, ",") && dcgwData.RemoteCidr == strings.Join(request.Dcgw.Properties.RemoteCidr, ",") {
			logrus.Infof("DCGW %s properties update skip", dcgwData.Name)
		} else {
			dcgwData.LocalCidr = strings.Join(request.Dcgw.Properties.LocalCidr, ",")
			dcgwData.RemoteCidr = strings.Join(request.Dcgw.Properties.RemoteCidr, ",")
		}
	}

	dcvcDatas, err := dao.GetDcvcByDcgwName(resourceprovider.BosonProvider.Engine, dcgwData.Name)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	for _, dcvcData := range dcvcDatas {
		// 1. update dcvc db
		k8sRTMetrics := exporter.InitK8sRTMetrics("GetDCVC")
		dcvcCr, err := resourceprovider.BosonProvider.BosonNetClient.DcVcs().Get(context.TODO(), dcvcData.Name, metav1.GetOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DcvcNotFound, errors.Domain, nil, nil)
		}
		if len(dcgwData.LocalCidr) > 0 {
			dcvcData.LocalCidr = dcgwData.LocalCidr
			dcvcCr.Spec.LocalCidr = strings.Split(dcgwData.LocalCidr, ",")
		}
		if len(dcgwData.RemoteCidr) > 0 {
			dcvcData.RemoteCidr = dcgwData.RemoteCidr
			dcvcCr.Spec.RemoteCidr = strings.Split(dcgwData.RemoteCidr, ",")
		}
		// 2. update dcvc cr
		dcvcData.State = dc.DCVC_UPDATING.String()
		dcvcData.UpdateTime = timestamppb.Now().AsTime()
		if err := dao.UpdateDCVC(resourceprovider.BosonProvider.Engine, dcvcData); err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateDCVC")
		_, err = resourceprovider.BosonProvider.BosonNetClient.DcVcs().Update(context.TODO(), dcvcCr, metav1.UpdateOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
		}

	}

	dcgwData.UpdateTime = timestamppb.Now().AsTime()
	err = dao.UpdateDCGW(resourceprovider.BosonProvider.Engine, dcgwData)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	return invertDcgw(dcgwData), nil
}

func (DCGWsServer) GetDCGW(ctx context.Context, request *dc.GetDCGWRequest) (res *dc.DCGW, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.DcgwName, "DCGW", "GetDCGW")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()
	logrus.Infof("GetDCGW called: %+v", request)

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	has, err := dao.ExistDcgwResource(resourceprovider.BosonProvider.Engine, request.DcgwName, request.SubscriptionName, request.ResourceGroupName)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if !has {
		err = fmt.Errorf("DCGW: %s not exist", request.DcgwName)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DcgwNotFound, errors.Domain, nil, nil)
	}

	var dcgwData *db.Dcgws
	dcgwData, err = dao.GetDcgwByName(resourceprovider.BosonProvider.Engine, request.DcgwName)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DcgwNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	return invertDcgw(dcgwData), nil
}

func (DCGWsServer) ListDCGWs(ctx context.Context, request *dc.ListDCGWsRequest) (res *dc.ListDCGWsResponse, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics("", "DCGW", "ListDCGWs")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()
	//logrus.Infof("ListDCGWs called: %+v", request)

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	var dcgwDatas []*db.Dcgws
	dcgwDatas, err = dao.ListDcgwsByTenant(resourceprovider.BosonProvider.Engine, request.TenantId)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	dcgws := make([]*dc.DCGW, len(dcgwDatas))

	for i, dcgwData := range dcgwDatas {
		dcgws[i] = invertListDcgw(dcgwData)
	}

	res = &dc.ListDCGWsResponse{
		Dcgws:     dcgws,
		TotalSize: int32(len(dcgws)),
	}

	return res, nil
}

func validateParametersForCreateDcgw(request *dc.CreateDCGWRequest) error {
	if request.Dcgw == nil || request.Dcgw.Properties == nil {
		err := fmt.Errorf("request Properties is nil")
		return err
	}

	if request.Dcgw.Properties.VpcId == "" {
		err := fmt.Errorf("request Properties is required")
		return err
	}

	return nil
}

func invertDcgw(dcgwData *db.Dcgws) *dc.DCGW {

	dcvcDatas, _ := dao.GetDcvcByDcgwName(resourceprovider.BosonProvider.Engine, dcgwData.Name)
	dcvcs := make([]*dc.DCVCElement, len(dcvcDatas))
	for i, dcvcData := range dcvcDatas {
		dcvcs[i] = &dc.DCVCElement{
			Name:        dcvcData.Name,
			DisplayName: dcvcData.DisplayName,
		}
	}

	return &dc.DCGW{
		Id:           dcgwData.ID,
		Name:         dcgwData.Name,
		DisplayName:  dcgwData.DisplayName,
		Description:  dcgwData.Description,
		Uid:          dcgwData.DcgwID,
		ResourceType: dcgwData.ResourceType,
		CreatorId:    dcgwData.CreatorID,
		OwnerId:      dcgwData.OwnerID,
		TenantId:     dcgwData.TenantID,
		Zone:         dcgwData.Zone,
		State:        dc.DCGW_State(dc.DCGW_State_value[dcgwData.State]),
		Properties: &dc.DCGWProperties{
			VpcId:             dcgwData.VPCID,
			HaMode:            dcgwData.HaMode,
			DeployMode:        dcgwData.DeployMode,
			InternalVip:       dcgwData.InternalVip,
			MonitorIp:         dcgwData.MonitorIp,
			HeartbeatNic:      dcgwData.HeartbeatNic,
			ExternalVip:       dcgwData.ExternalVip,
			LocalCidr:         strings.Split(dcgwData.LocalCidr, ","),
			RemoteCidr:        strings.Split(dcgwData.RemoteCidr, ","),
			Dcvcs:             dcvcs,
			SubscriptionName:  dcgwData.SubscriptionName,
			ResourceGroupName: dcgwData.ResourceGroupName,
		},
		CreateTime: timestamppb.New(dcgwData.CreateTime),
		UpdateTime: timestamppb.New(dcgwData.UpdateTime),
	}
}

func invertListDcgw(dcgwData *db.Dcgws) *dc.DCGW {

	dcvcDatas, _ := dao.GetDcvcByDcgwNameWithAllZone(resourceprovider.BosonProvider.Engine, dcgwData.Name)
	dcvcs := make([]*dc.DCVCElement, len(dcvcDatas))
	for i, dcvcData := range dcvcDatas {
		dcvcs[i] = &dc.DCVCElement{
			Name:        dcvcData.Name,
			DisplayName: dcvcData.DisplayName,
		}
	}

	return &dc.DCGW{
		Id:           dcgwData.ID,
		Name:         dcgwData.Name,
		DisplayName:  dcgwData.DisplayName,
		Description:  dcgwData.Description,
		Uid:          dcgwData.DcgwID,
		ResourceType: dcgwData.ResourceType,
		CreatorId:    dcgwData.CreatorID,
		OwnerId:      dcgwData.OwnerID,
		TenantId:     dcgwData.TenantID,
		Zone:         dcgwData.Zone,
		State:        dc.DCGW_State(dc.DCGW_State_value[dcgwData.State]),
		Properties: &dc.DCGWProperties{
			VpcId:             dcgwData.VPCID,
			HaMode:            dcgwData.HaMode,
			DeployMode:        dcgwData.DeployMode,
			InternalVip:       dcgwData.InternalVip,
			MonitorIp:         dcgwData.MonitorIp,
			HeartbeatNic:      dcgwData.HeartbeatNic,
			ExternalVip:       dcgwData.ExternalVip,
			LocalCidr:         strings.Split(dcgwData.LocalCidr, ","),
			RemoteCidr:        strings.Split(dcgwData.RemoteCidr, ","),
			Dcvcs:             dcvcs,
			SubscriptionName:  dcgwData.SubscriptionName,
			ResourceGroupName: dcgwData.ResourceGroupName,
		},
		CreateTime: timestamppb.New(dcgwData.CreateTime),
		UpdateTime: timestamppb.New(dcgwData.UpdateTime),
	}
}

func AddDcgwFromAllocateResources(ctx context.Context, request *dc.CreateDCGWRequest) (dcgwData *db.Dcgws, err error) {

	// update DB
	dcgwData = &db.Dcgws{
		DcgwID:       utils.UUIDGen(),
		VPCID:        request.Dcgw.Properties.VpcId,
		DisplayName:  utils.EmptyToBackup(request.Dcgw.DisplayName, request.DcgwName),
		Description:  request.Dcgw.Description,
		ID:           request.Dcgw.Id,
		Name:         request.DcgwName,
		ResourceType: request.Dcgw.ResourceType,

		SubscriptionName:  request.SubscriptionName,
		ResourceGroupName: request.ResourceGroupName,

		HaMode:       utils.EmptyToBackup(request.Dcgw.Properties.HaMode, MasterStandby),
		DeployMode:   utils.EmptyToBackup(request.Dcgw.Properties.DeployMode, NSA),
		InternalVip:  request.Dcgw.Properties.InternalVip,
		MonitorIp:    request.Dcgw.Properties.MonitorIp,
		HeartbeatNic: request.Dcgw.Properties.HeartbeatNic,
		ExternalVip:  request.Dcgw.Properties.ExternalVip,
		LocalCidr:    strings.Join(request.Dcgw.Properties.LocalCidr, ","),
		RemoteCidr:   strings.Join(request.Dcgw.Properties.RemoteCidr, ","),

		Zone:       request.Zone,
		State:      dc.DCGW_ACTIVE.String(), // ACTIVE
		CreatorID:  request.Dcgw.CreatorId,
		OwnerID:    request.Dcgw.OwnerId,
		TenantID:   request.Dcgw.TenantId,
		CreateTime: request.Dcgw.CreateTime.AsTime(),
		UpdateTime: request.Dcgw.UpdateTime.AsTime(),
		Deleted:    false,
	}
	if err = dao.AddDCGW(resourceprovider.BosonProvider.Engine, dcgwData); err != nil {
		logrus.Errorln(err)
		return nil, err
	}

	return dcgwData, nil
}

func cloudAuditCreateDcgwError(
	ctx context.Context, resourceID string, request *dc.CreateDCGWRequest, response interface{}, reason string,
) {
	resourceprovider.BosonProvider.Processor.CloudAuditSendDCGW(
		ctx,
		types.CloudAuditOperationTypeCreateDcgws, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.Dcgw.Name,
		resourceprovider.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditCreateDcgwSuccess(
	ctx context.Context, resourceID string, request *dc.CreateDCGWRequest, response interface{},
) {
	resourceprovider.BosonProvider.Processor.CloudAuditSendDCGW(
		ctx,
		types.CloudAuditOperationTypeCreateDcgws, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.DcgwName,
		resourceprovider.BosonProvider.Processor.CloudAuditSendMsg("create success"),
	)
}

func cloudAuditUpdateDcgwError(
	ctx context.Context, resourceUID string, request *dc.UpdateDCGWRequest, response interface{}, reason string,
) {
	resourceprovider.BosonProvider.Processor.CloudAuditSendDCGW(
		ctx,
		types.CloudAuditOperationTypeUpdateDcgws, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceUID, request.DcgwName,
		resourceprovider.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditUpdateDcgwSuccess(
	ctx context.Context, resourceUID string, request *dc.UpdateDCGWRequest, response interface{},
) {
	resourceprovider.BosonProvider.Processor.CloudAuditSendDCGW(
		ctx,
		types.CloudAuditOperationTypeUpdateDcgws, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceUID, request.DcgwName,
		resourceprovider.BosonProvider.Processor.CloudAuditSendMsg("update success"),
	)
}

func cloudAuditDeleteDcgwError(
	ctx context.Context, resourceUID string, request *dc.DeleteDCGWRequest, response interface{}, reason string,
) {
	resourceprovider.BosonProvider.Processor.CloudAuditSendDCGW(
		ctx,
		types.CloudAuditOperationTypeDeleteDcgws, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceUID, request.DcgwName,
		resourceprovider.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditDeleteDcgwSuccess(
	ctx context.Context, resourceUID string, request *dc.DeleteDCGWRequest, response interface{},
) {
	resourceprovider.BosonProvider.Processor.CloudAuditSendDCGW(
		ctx,
		types.CloudAuditOperationTypeDeleteDcgws, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceUID, request.DcgwName,
		resourceprovider.BosonProvider.Processor.CloudAuditSendMsg("delete success"),
	)
}
