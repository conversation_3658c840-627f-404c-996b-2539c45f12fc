package server

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey"
	v1 "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"github.com/stretchr/testify/assert"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"google.golang.org/grpc/codes"
	"k8s.io/apimachinery/pkg/labels"
)

func TestCreateVpcPeeringNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetPccByName, func(_ *db.EngineWrapper, _ string) (*db.Pccs, error) {
		return nil, fmt.Errorf("pcc not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vpc.CreateVpcPeeringRequest{
		Zone: "test",
		VpcPeering: &vpc.VpcPeeringCreate{
			Properties: &vpc.VpcPeeringProperties{},
		},
	}

	vpcPeeringServer := VpcPeeringServer{}
	ctx := context.Background()

	_, err := vpcPeeringServer.CreateVpcPeering(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.PccNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestUpdateVpcPeering(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vpc.UpdateVpcPeeringRequest{
		Zone: "test",
	}

	vpcPeeringServer := VpcPeeringServer{}
	ctx := context.Background()

	_, err := vpcPeeringServer.UpdateVpcPeering(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

type MockVpcPeeringLister struct{}

func (MockVpcPeeringLister) List(selector labels.Selector) (ret []*v1.VpcPeering, err error) {
	return nil, fmt.Errorf("err")
}
func (MockVpcPeeringLister) Get(name string) (*v1.VpcPeering, error) {
	return nil, fmt.Errorf("err")
}

func TestGetVpcPeeringErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vpc.GetVpcPeeringRequest{
		Zone: "test",
	}

	vpcPeeringServer := VpcPeeringServer{
		vpcPeeringLister: &MockVpcPeeringLister{},
	}

	ctx := context.Background()

	_, err := vpcPeeringServer.GetVpcPeering(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestListVpcPeeringErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vpc.ListVpcPeeringRequest{
		Zone: "test",
	}

	vpcPeeringServer := VpcPeeringServer{
		vpcPeeringLister: &MockVpcPeeringLister{},
	}

	ctx := context.Background()

	_, err := vpcPeeringServer.ListVpcPeering(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestResizeVpcPeering(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vpc.ResizeVpcPeeringRequest{
		Zone: "test",
	}

	vpcPeeringServer := VpcPeeringServer{}
	ctx := context.Background()

	_, err := vpcPeeringServer.ResizeVpcPeering(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestReleaseVpcPeering(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vpc.ReleaseVpcPeeringRequest{
		Zone: "test",
	}

	vpcPeeringServer := VpcPeeringServer{}
	ctx := context.Background()

	_, err := vpcPeeringServer.ReleaseVpcPeering(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}
