package server

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"google.golang.org/grpc/codes"
)

func TestValidateAclAddress(t *testing.T) {
	if err := validateAclAddress("**************-*************"); err != nil {
		t.Log(err)
	} else {
		t.Log("valid ip range")
	}
}

func TestListACLsAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.ListACLsRequest{
		Zone: "test11",
	}

	aclServer := ACLsServer{}
	ctx := context.Background()

	_, err := aclServer.ListACLs(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
	// st1, _ := status.FromError(errHiggs)
	// t.Log(st1.Details()...)
	// st2, _ := status.FromError(err)
	// t.Log(st2.Details()...)
}

func TestListACLsEipNotfound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&db.AZ, "test")

	p.ApplyFunc(utils.GenEipResourceID, func(_ string, _ string, _ string, _ string) string {
		return "1234"
	})

	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})

	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return nil, fmt.Errorf("ACL not found")
	})

	aclServer := ACLsServer{}
	ctx := context.Background()

	request := &eip.GetACLRequest{
		Zone: "test",
	}

	_, err := aclServer.GetACL(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound,
		errors.EipNotFound, errors.Domain, nil, nil)

	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetACLAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.GetACLRequest{
		Zone: "test11",
	}

	aclServer := ACLsServer{}
	ctx := context.Background()

	_, err := aclServer.GetACL(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 does not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetACLEipNotfound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&db.AZ, "test")

	p.ApplyFunc(utils.GenEipResourceID, func(_ string, _ string, _ string, _ string) string {
		return "1234"
	})

	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})

	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return &db.Eips{}, fmt.Errorf("EIP not found")
	})

	aclServer := ACLsServer{}
	ctx := context.Background()

	request := &eip.GetACLRequest{
		Zone: "test",
	}

	_, err := aclServer.GetACL(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound,
		errors.EipNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetACLACLNotfound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&db.AZ, "test")

	p.ApplyFunc(utils.GenEipResourceID, func(_ string, _ string, _ string, _ string) string {
		return "1234"
	})

	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})

	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return &db.Eips{}, nil
	})

	p.ApplyFunc(dao.GetACLByName, func(_ *db.EngineWrapper, _ string) (*db.Acls, error) {
		return nil, fmt.Errorf("ACL not found, name = ")
	})

	aclServer := ACLsServer{}
	ctx := context.Background()

	request := &eip.GetACLRequest{
		Zone: "test",
	}

	_, err := aclServer.GetACL(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound,
		errors.AclNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestCreateACLAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditCreateAclError, func(ctx context.Context, resourceID string, request *eip.CreateACLRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditCreateAclSuccess, func(ctx context.Context, resourceID string, request *eip.CreateACLRequest,
		response interface{}) {
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.CreateACLRequest{
		Zone: "test11",
	}

	aclServer := ACLsServer{}
	ctx := context.Background()

	_, err := aclServer.CreateACL(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestCreateACLEipNotfound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditCreateAclError, func(ctx context.Context, resourceID string, request *eip.CreateACLRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditCreateAclSuccess, func(ctx context.Context, resourceID string, request *eip.CreateACLRequest,
		response interface{}) {
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	p.ApplyFunc(validateACL, func(_ *eip.ACL) error {
		return nil
	})
	p.ApplyFunc(utils.GenEipResourceID, func(_ string, _ string, _ string, _ string) string {
		return "1234"
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})

	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return &db.Eips{}, fmt.Errorf("EIP not found")
	})

	aclServer := ACLsServer{}
	ctx := context.Background()

	request := &eip.CreateACLRequest{
		Zone: "test",
		Acl: &eip.ACL{
			Name:       "test",
			Properties: &eip.ACLProperties{},
		},
	}

	_, err := aclServer.CreateACL(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound,
		errors.EipNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteACLAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditDeleteAclError, func(ctx context.Context, resourceID string, request *eip.DeleteACLRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditDeleteAclSuccess, func(ctx context.Context, resourceID string, request *eip.DeleteACLRequest,
		response interface{}) {
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.DeleteACLRequest{
		Zone: "test11",
	}

	aclServer := ACLsServer{}
	ctx := context.Background()

	_, err := aclServer.DeleteACL(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteACLEipNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditDeleteAclError, func(ctx context.Context, resourceID string, request *eip.DeleteACLRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditDeleteAclSuccess, func(ctx context.Context, resourceID string, request *eip.DeleteACLRequest,
		response interface{}) {
	})
	p.ApplyFunc(utils.GenEipResourceID, func(_ string, _ string, _ string, _ string) string {
		return "1234"
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})

	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return &db.Eips{}, fmt.Errorf("EIP not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.DeleteACLRequest{
		Zone: "test",
	}

	aclServer := ACLsServer{}
	ctx := context.Background()

	_, err := aclServer.DeleteACL(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound,
		errors.EipNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteACLAclNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditDeleteAclError, func(ctx context.Context, resourceID string, request *eip.DeleteACLRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditDeleteAclSuccess, func(ctx context.Context, resourceID string, request *eip.DeleteACLRequest,
		response interface{}) {
	})
	p.ApplyFunc(utils.GenEipResourceID, func(_ string, _ string, _ string, _ string) string {
		return "1234"
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return &db.Eips{}, nil
	})
	p.ApplyFunc(dao.GetACLByName, func(_ *db.EngineWrapper, _ string) (*db.Acls, error) {
		return &db.Acls{}, fmt.Errorf("ACL not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.DeleteACLRequest{
		Zone: "test",
	}

	aclServer := ACLsServer{}
	ctx := context.Background()

	_, err := aclServer.DeleteACL(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound,
		errors.AclNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}
