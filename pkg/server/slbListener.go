package server

import (
	"context"
	"fmt"
	"reflect"
	"sort"
	"strings"

	"github.com/sirupsen/logrus"
	netControllerV1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netControllerApis "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/controllers/apis"
	netControllerSlbSdk "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/controllers/slb/sdk"
	slbv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/slb/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func (s SLBsServer) CreateListener(ctx context.Context, request *slbv1.CreateListenerRequest) (listener *slbv1.Listener, err error) {
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics(request.ListenerName, "Listener", "CreateListener")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditCreateSlbListenerError(ctx, request.ListenerName, request, struct{}{}, err.Error())
		} else {
			cloudAuditCreateSlbListenerSuccess(ctx, request.ListenerName, request, struct{}{})
		}
	}()

	s.createListenerRequestFill(ctx, request)

	logrus.Info("Create listener start.", "slb ", request.SlbName, "listener ", request.ListenerName, "request", request)

	slbRecord, err := s.createListenerGetWithCheck(ctx, request)
	if err != nil {
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	err = s.createListenerCR(ctx, request, slbRecord)
	if err != nil {
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	err = s.createListener(ctx, request, slbRecord)
	if err != nil {
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	listenerRecord, err := s.getListenerRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.ListenerName, false,
	)
	if err != nil {
		logrus.Errorf("Generate listener fail. get listenerDB err:%s\n", err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	listener, err = s.generateListener(slbRecord, listenerRecord)
	if err != nil {
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	logrus.Info("Create listener success", "slb", request.SlbName, "listener", request.ListenerName, "request", request)
	return listener, nil
}

func (s SLBsServer) UpdateListener(ctx context.Context, request *slbv1.UpdateListenerRequest) (listenerResponse *slbv1.Listener, err error) {
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics(request.ListenerName, "Listener", "UpdateListener")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditUpdateSlbListenerError(ctx, request.ListenerName, request, struct{}{}, err.Error())
		} else {
			cloudAuditUpdateSlbListenerSuccess(ctx, request.ListenerName, request, struct{}{})
		}
	}()

	logrus.Infof("update listener:%+v starting\n", request)

	slbRecord, listenerNew, err := s.updateListenerGetWithCheck(ctx, request)
	if err != nil {
		logrus.Error("Update listener fail. check with get slb err:", err.Error())
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	err = s.updateListenerCR(ctx, request, slbRecord, listenerNew)
	if err != nil {
		logrus.Error("Update listener fail. update listener CR err:", err.Error())
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	err = s.updateListener(ctx, request, slbRecord, listenerNew)
	if err != nil {
		logrus.Error("Update listener fail. update listener err:", err.Error())
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	listenerRecord, err := s.getListenerRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.ListenerName, false,
	)
	if err != nil {
		logrus.Errorf("Update listener fail. get listener err:%s\n", err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	listenerResponse, err = s.generateListener(slbRecord, listenerRecord)
	if err != nil {
		logrus.Errorf("Update listener fail. generate listener err:%s\n", err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	return listenerResponse, nil
}

func (s SLBsServer) DeleteListener(ctx context.Context, request *slbv1.DeleteListenerRequest) (rsp *emptypb.Empty, err error) {
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics(request.ListenerName, "Listener", "DeleteListener")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditDeleteSlbListenerError(ctx, request.ListenerName, request, struct{}{}, err.Error())
		} else {
			cloudAuditDeleteSlbListenerSuccess(ctx, request.ListenerName, request, struct{}{})
		}
	}()

	engine := resourceprovider.BosonProvider.Engine
	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		logrus.Errorf("Delete listener fail, slb:%s not exist, err:%s\n", request.SlbName, err)
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	listenerRecord, err := s.getListenerRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.ListenerName, true,
	)
	if err != nil {
		logrus.Errorf(
			"Delete listener fail, slb:%s listener:%s not exist, err:%s\n",
			request.SlbName, request.ListenerName, err,
		)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if listenerRecord.Deleted {
		logrus.Info("Listener already deleted", "slb", request.SlbName, "listener", request.ListenerName)
		return &emptypb.Empty{}, nil
	}

	// CR delete
	err = s.deleteListenerCR(ctx, request, slbRecord, listenerRecord)
	if err != nil {
		logrus.Errorf(
			"Delete listener fail, slb:%s listener:%s delete CR err:%s\n",
			request.SlbName, request.ListenerName, err,
		)
		return &emptypb.Empty{}, nil
	}

	// DB deleting
	err = dao.SetListenerState(
		engine, slbRecord.SlbID, listenerRecord.ListenerID,
		listenerStateName(slbv1.Listener_DELETING),
	)
	if err != nil {
		logrus.Errorf(
			"Delete listener fail, slb:%s listener:%s set db deleting err:%s\n",
			request.SlbName, request.ListenerName, err,
		)
		return &emptypb.Empty{}, nil
	}
	logrus.Infof("SLB %s listener:%s change to %s", slbRecord.SlbID, listenerRecord.ListenerID, listenerStateName(slbv1.Listener_DELETING))

	return &emptypb.Empty{}, nil
}

func (s SLBsServer) ListListeners(ctx context.Context, request *slbv1.ListListenersRequest) (*slbv1.ListListenersResponse, error) {
	var err error = nil
	var slbRecord *db.Slbs
	engine := resourceprovider.BosonProvider.Engine
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics("", "Listener", "ListListeners")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("List SLB Listerner with %v", request)

	// TODO: fixme: line214: nil pointer
	slbRecord, err = s.listListenersCheckAndGet(ctx, request)
	if err != nil {
		logrus.Errorf("list slb:%s listeners fail. check request err:%s", slbRecord.SlbID, err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	listenersResponse := &slbv1.ListListenersResponse{}
	listenerRecords, err := dao.GetSLBListenersBySlbID(engine, slbRecord.SlbID, false)
	if err != nil {
		logrus.Errorf("list slb:%s listeners fail. list DBs err:%s", slbRecord.SlbID, err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	for _, listenerRecord := range listenerRecords {
		listenerStatusInfo, err := s.generateListenerStatusInfo(slbRecord, listenerRecord)
		if err != nil {
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		listenersResponse.ListenerStatusInfos = append(listenersResponse.ListenerStatusInfos, listenerStatusInfo)
	}

	listenersResponse.NextPageToken = "0"
	listenersResponse.TotalSize = int32(len(listenersResponse.ListenerStatusInfos))

	return listenersResponse, nil
}

func (s SLBsServer) GetListener(ctx context.Context, request *slbv1.GetListenerRequest) (listenerResponse *slbv1.Listener, err error) {
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics(request.ListenerName, "Listener", "GetListener")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("Get SLB Listener with %v", request)
	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		logrus.Errorf("Get listener fail, slb:%s not exist, err:%s\n", request.SlbName, err)
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbNotFound, errors.Domain, nil, nil)
		}
		return nil, err
	}
	listenerRecord, err := s.getListenerRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.ListenerName, false,
	)
	if err != nil {
		logrus.Errorf(
			"Get listener fail, slb:%s listener:%s not exist, err:%s\n",
			request.SlbName, request.ListenerName, err,
		)
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbListenerNotFound, errors.Domain, nil, nil)
		}
		return nil, err
	}

	listenerResponse, err = s.generateListener(slbRecord, listenerRecord)
	if err != nil {
		logrus.Errorf(
			"Get listener fail, slb:%s listener:%s generate response err:%s\n",
			request.SlbName, request.ListenerName, err,
		)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	return listenerResponse, nil
}

func (s SLBsServer) GetListenerStatus(
	ctx context.Context, request *slbv1.GetListenerStatusRequest,
) (listenerStatusResponse *slbv1.ListenerStatus, err error) {
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics(request.ListenerName, "Listener", "GetListenerStatus")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("GetListener status with %v", request)
	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		logrus.Errorf("Get listener status fail, slb:%s not exist, err:%s\n", request.SlbName, err)
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbNotFound, errors.Domain, nil, nil)
		}
		return nil, err
	}
	listenerRecord, err := s.getListenerRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.ListenerName, false,
	)
	if err != nil {
		logrus.Errorf(
			"Get listener status fail, slb:%s listener:%s not exist, err:%s\n",
			request.SlbName, request.ListenerName, err,
		)
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbListenerNotFound, errors.Domain, nil, nil)
		}
		return nil, err
	}

	listenerStatusResponse, err = s.generateListenerStatus(slbRecord, listenerRecord)
	if err != nil {
		logrus.Errorf(
			"Get listener status fail, slb:%s listener:%s generate response fail, err:%s\n",
			request.SlbName, request.ListenerName, err,
		)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	return listenerStatusResponse, nil
}

func (s SLBsServer) StartListener(ctx context.Context, request *slbv1.StartListenerRequest) (rsp *emptypb.Empty, err error) {
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics(request.ListenerName, "Listener", "StartListener")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditStartSlbListenerError(ctx, request.ListenerName, request, struct{}{}, err.Error())
		} else {
			cloudAuditStartSlbListenerSuccess(ctx, request.ListenerName, request, struct{}{})
		}
	}()

	slbRecord, listenerRecord, needStart, err := s.startListenerGetWithCheck(ctx, request)
	if err != nil {
		logrus.Errorf(
			"Start slb:%s listener:%s fail. check with get slb err:%s",
			request.SlbName, request.ListenerName, err.Error(),
		)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if !needStart {
		return &emptypb.Empty{}, nil
	}

	// start CR
	err = s.startListenerCR(ctx, request, slbRecord, listenerRecord)
	if err != nil {
		logrus.Errorf(
			"Start slb:%s listener:%s fail. start CR err:%s",
			slbRecord.SlbID, listenerRecord.ListenerID, err.Error(),
		)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	// update db starting
	err = s.startListener(ctx, slbRecord, listenerRecord)
	if err != nil {
		logrus.Errorf(
			"Start slb:%s listener:%s fail. start err:%s",
			slbRecord.SlbID, listenerRecord.ListenerID, err.Error(),
		)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	return &emptypb.Empty{}, nil
}

func (s SLBsServer) StopListener(ctx context.Context, request *slbv1.StopListenerRequest) (rsp *emptypb.Empty, err error) {
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics(request.ListenerName, "Listener", "StopListener")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditStopSlbListenerError(ctx, request.ListenerName, request, struct{}{}, err.Error())
		} else {
			cloudAuditStopSlbListenerSuccess(ctx, request.ListenerName, request, struct{}{})
		}
	}()

	slbRecord, listenerRecord, needStop, err := s.stopListenerGetWithCheck(ctx, request)
	if err != nil {
		logrus.Errorf(
			"Start slb:%s listener:%s fail. check with get slb err:%s",
			request.SlbName, request.ListenerName, err.Error(),
		)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if !needStop {
		return &emptypb.Empty{}, nil
	}

	// stop CR
	err = s.stopListenerCR(ctx, request, slbRecord, listenerRecord)
	if err != nil {
		logrus.Errorf(
			"Stop slb:%s listener:%s fail. stop CR err:%s",
			slbRecord.SlbID, listenerRecord.ListenerID, err.Error(),
		)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	// update db stopping
	err = s.stopListener(ctx, slbRecord, listenerRecord)
	if err != nil {
		logrus.Errorf(
			"Stop slb:%s listener:%s fail. stop err:%s",
			slbRecord.SlbID, listenerRecord.ListenerID, err.Error(),
		)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	return &emptypb.Empty{}, nil
}

func (s SLBsServer) listenerExist(ctx context.Context, subscription string, resourceGroup string, zone string, slbName string, listenerName string) (bool, error) {
	engine := resourceprovider.BosonProvider.Engine
	listenerResourceId := dao.SlbListenerResourceId(subscription, resourceGroup, zone, slbName, listenerName)
	has, err := dao.CheckSLBListenerByResourceID(engine, listenerResourceId)
	if err != nil {
		logrus.Errorln("SLB Server get listener DB record fail.", "err", err.Error())
		return false, err
	}

	return has, nil
}

func (s SLBsServer) generateListenerInfos(slbRecord *db.Slbs, listenerRecords []*db.SlbListeners) ([]*slbv1.ListenerInfo, error) {
	listenerInfos := []*slbv1.ListenerInfo{}
	for _, listenerRecord := range listenerRecords {
		info, err := s.generateListenerInfoOne(slbRecord, listenerRecord)
		if err != nil {
			logrus.Errorln(
				"SLB Server generate listenerInfos fail.", "err", err.Error(),
				"slb", slbRecord.SlbID, "listener", listenerRecord.ListenerID,
			)
			return nil, err
		}
		listenerInfos = append(listenerInfos, info)
	}

	return listenerInfos, nil
}

func (s SLBsServer) generateListenerInfoOne(slbRecord *db.Slbs, listenerRecord *db.SlbListeners) (*slbv1.ListenerInfo, error) {
	info := slbv1.ListenerInfo{}
	info.Id = listenerRecord.ID
	info.Uid = listenerRecord.ListenerID
	info.Name = listenerRecord.Name
	info.DisplayName = listenerRecord.DisplayName
	info.Properties = &slbv1.ListenerProperties{
		Protocol: slbListenerProtocolTypeValue(listenerRecord.Protocol),
		Port:     listenerRecord.Port,
		DefaultForwardAction: &slbv1.ForwardAction{
			Type:                     slbForwardActionTypeValue(listenerRecord.ForwardActionType),
			ForwardTargetGroupConfig: nil, // 下文赋值
		},
		OriginalClientIpAddress: nil, // 暂不支持
	}

	if info.Properties.DefaultForwardAction.Type == slbv1.ForwardAction_FORWARD_TARGET_GROUP {
		targetGroups, err := s.generateTargetGroupInfos(slbRecord.SlbID, listenerRecord.ListenerID)
		if err != nil {
			logrus.Error(
				"Generate listenerInfo one fail. generate targetGroupInfo error",
				"slb", slbRecord.SlbID, "listener", listenerRecord.ListenerID,
			)
			return nil, err
		}
		config := &slbv1.ForwardTargetGroupConfig{}
		config.TargetGroups = targetGroups
		info.Properties.DefaultForwardAction.ForwardTargetGroupConfig = config
	}

	return &info, nil
}

func (s SLBsServer) generateListener(slbRecord *db.Slbs, listenerRecord *db.SlbListeners) (*slbv1.Listener, error) {
	listener := slbv1.Listener{
		Id:           listenerRecord.ID,
		Uid:          listenerRecord.ListenerID,
		Name:         listenerRecord.Name,
		DisplayName:  listenerRecord.DisplayName,
		Description:  listenerRecord.Description,
		ResourceType: listenerRecord.ResourceType,
		CreatorId:    listenerRecord.CreatorID,
		OwnerId:      listenerRecord.OwnerID,
		TenantId:     listenerRecord.TenantID,
		Zone:         listenerRecord.Zone,
		State:        listenerStateValue(listenerRecord.State),
		Tags:         nil,
		Properties:   nil,
		Deleted:      listenerRecord.Deleted,
		CreateTime:   timestamppb.New(listenerRecord.CreateTime),
		UpdateTime:   timestamppb.New(listenerRecord.UpdateTime),
	}

	listenerInfo, err := s.generateListenerInfoOne(slbRecord, listenerRecord)
	if err != nil {
		logrus.Error("Generate listener fail.", "slb", slbRecord.SlbID, "listener", listenerRecord.ListenerID)
		return nil, err
	}
	listener.Properties = listenerInfo.Properties

	return &listener, nil
}

func (s SLBsServer) generateListenerStatusInfo(slbRecord *db.Slbs, listenerRecord *db.SlbListeners) (*slbv1.ListenerStatusInfo, error) {
	listener, err := s.generateListener(slbRecord, listenerRecord)
	if err != nil {
		logrus.Error("Generate listener status info fail. generate listener error", "slb", slbRecord.SlbID, "listener", listenerRecord.ListenerID)
		return nil, err
	}

	listenerStatus, err := s.generateListenerStatus(slbRecord, listenerRecord)
	if err != nil {
		logrus.Error("Generate listener status info fail. generate status error", "slb", slbRecord.SlbID, "listener", listenerRecord.ListenerID)
		return nil, err
	}

	listenerStatusInfo := &slbv1.ListenerStatusInfo{
		Listener: listener,
		Status:   listenerStatus,
	}

	return listenerStatusInfo, nil
}

func (s SLBsServer) generateListenerStatus(slbRecord *db.Slbs, listenerRecord *db.SlbListeners) (*slbv1.ListenerStatus, error) {
	hcStatus, err := s.generateListenerHealthCheckStatus(slbRecord, listenerRecord)
	if err != nil {
		logrus.Error("Generate listener status fail. generate health check status error", "slb", slbRecord.SlbID, "listener", listenerRecord.ListenerID)
		return nil, err
	}

	listenStatus := &slbv1.ListenerStatus{
		HealthCheckStatus: hcStatus,
	}
	return listenStatus, nil
}

func (s SLBsServer) generateListenerHealthCheckStatus(slbRecord *db.Slbs, listenerRecord *db.SlbListeners) (*slbv1.ListenerHealthCheckStatus, error) {
	hcState, err := s.generateListenerHealthCheckState(slbRecord, listenerRecord)
	if err != nil {
		return nil, err
	}

	return &slbv1.ListenerHealthCheckStatus{
		State: hcState,
	}, nil
}

/*
ListenerHealthCheckStatus_INITIAL         ：存在一个 initial
ListenerHealthCheckStatus_HEALTH          ：全部都是 health
ListenerHealthCheckStatus_PARTLY_HEALTH   ：存在一个unhealth
ListenerHealthCheckStatus_UNHEALTH        ：所有都是unhealth
ListenerHealthCheckStatus_DISABLE         : 没有后端组，或者全部后端组都是disable，或者全部后端组都没有后端
*/
func (s SLBsServer) generateListenerHealthCheckState(slbRecord *db.Slbs, listenerRecord *db.SlbListeners) (slbv1.ListenerHealthCheckStatus_HealthCheckState, error) {
	engine := resourceprovider.BosonProvider.Engine
	assos, err := dao.ListSlbAssociationsByForwardRule(engine, []string{listenerRecord.ListenerID}, associationsType_listener)
	if err != nil {
		logrus.Error(
			"generate listener health check status fail. list listener target groups err",
			"slb", slbRecord.Name, "listenerID", listenerRecord.ListenerID, "error", err,
		)
		return slbv1.ListenerHealthCheckStatus_INITIAL, err
	}

	if len(assos) == 0 {
		logrus.Info(fmt.Sprintf(
			"listener:%s not asso target group, set hc state %s",
			listenerRecord.ListenerID, slbv1.ListenerHealthCheckStatus_HealthCheckState_name[int32(slbv1.ListenerHealthCheckStatus_DISABLE)],
		))
		return slbv1.ListenerHealthCheckStatus_DISABLE, nil
	}

	slbDpClient := resourceprovider.BosonProvider.SlbDataplane
	stateMap := make(map[netControllerApis.SlbTargetHealthCheckState][]*netControllerSlbSdk.SlbTargetStatus)
	targetStatusAll := []*netControllerSlbSdk.SlbTargetStatus{}
	for index := range assos {
		targetStatusList, err := slbDpClient.ListTargets(slbRecord.Name, assos[index].TargetGroupID)
		if err != nil {
			logrus.Error(
				"generate listener health check status fail. list listener asso targets status err",
				"slb", slbRecord.Name, "listenerID", listenerRecord.ListenerID, "targetGroupID", assos[index].TargetGroupID, "error", err,
			)
			return slbv1.ListenerHealthCheckStatus_INITIAL, err
		}

		for indexTarget := range targetStatusList {
			if _, ok := stateMap[targetStatusList[indexTarget].HcStatus.State]; !ok {
				stateMap[targetStatusList[indexTarget].HcStatus.State] = []*netControllerSlbSdk.SlbTargetStatus{}
			}
			stateMap[targetStatusList[indexTarget].HcStatus.State] = append(
				stateMap[targetStatusList[indexTarget].HcStatus.State], targetStatusList[indexTarget],
			)
			targetStatusAll = append(targetStatusAll, targetStatusList[indexTarget])
		}
	}

	if len(targetStatusAll) == 0 {
		logrus.Info(fmt.Sprintf(
			"listener:%s targets is null, set hc state %s",
			listenerRecord.ListenerID, slbv1.ListenerHealthCheckStatus_HealthCheckState_name[int32(slbv1.ListenerHealthCheckStatus_DISABLE)],
		))
		return slbv1.ListenerHealthCheckStatus_DISABLE, nil
	}

	if targets, ok := stateMap[netControllerApis.SlbTargetHealthCheckState_initial]; ok {
		if len(targets) > 0 {
			return slbv1.ListenerHealthCheckStatus_INITIAL, nil
		}
	}

	if targets, ok := stateMap[netControllerApis.SlbTargetHealthCheckState_health]; ok {
		if len(targets) == len(targetStatusAll) {
			return slbv1.ListenerHealthCheckStatus_HEALTH, nil
		}
	}

	if targets, ok := stateMap[netControllerApis.SlbTargetHealthCheckState_unhealth]; ok {
		if len(targets) == len(targetStatusAll) {
			return slbv1.ListenerHealthCheckStatus_UNHEALTH, nil
		}
	}

	if targets, ok := stateMap[netControllerApis.SlbTargetHealthCheckState_disable]; ok {
		if len(targets) == len(targetStatusAll) {
			return slbv1.ListenerHealthCheckStatus_DISABLE, nil
		}
	}

	return slbv1.ListenerHealthCheckStatus_PARTLY_HEALTH, nil
}

func (s SLBsServer) createListenerGetWithCheck(
	ctx context.Context, request *slbv1.CreateListenerRequest,
) (*db.Slbs, error) {
	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		return nil, err
	}

	has, err := s.listenerExist(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.ListenerName)
	if err != nil {
		return nil, err
	}
	if has {
		err = fmt.Errorf(
			"Create listener fail. slb:%s, listener:%s exist\n",
			slbRecord.SlbID, request.Listener.Uid,
		)
		logrus.Errorln(err.Error())
		return nil, err
	}

	// quota check
	err = s.listenerQuota(slbRecord, request)
	if err != nil {
		return nil, err
	}

	// .Protocol
	if !s.listenerProtocolValid(request.Listener.Properties.Protocol) {
		err = fmt.Errorf(
			"Create listener fail. slb:%s listener:%s protocol:%s is error\n",
			slbRecord.SlbID, request.Listener.Uid,
			request.Listener.Properties.Protocol.String(),
		)
		logrus.Errorln(err.Error())
		return nil, err
	}

	// .Port
	if !s.listenerPortValid(int(request.Listener.Properties.Port)) {
		err = fmt.Errorf(
			"Create listener fail. slb:%s listener:%s port:%d is error\n",
			slbRecord.SlbID, request.Listener.Uid,
			request.Listener.Properties.Port,
		)
		logrus.Errorln(err.Error())
		return nil, err
	}

	// Protocol:port
	protocolPortValid, err := s.listenerProtocolPortValidCreate(
		slbRecord, request.Listener.Properties.Protocol, int(request.Listener.Properties.Port),
	)
	if err != nil {
		logrus.Errorln(err.Error())
		return nil, err
	}
	if !protocolPortValid {
		err = fmt.Errorf(
			"Create listener fail. slb:%s listener:%s protocol:%s port:%d already exist\n",
			slbRecord.SlbID, request.Listener.Uid,
			request.Listener.Properties.Protocol.String(),
			request.Listener.Properties.Port,
		)
		logrus.Errorln(err.Error())
		return nil, err
	}

	// .DefaultForwardAction
	err = s.listenerActionValid(slbRecord, request)
	if err != nil {
		return nil, err
	}

	// .OriginalClientIpAddress
	// fixme not support

	return slbRecord, nil
}

func (s SLBsServer) createListenerCR(
	ctx context.Context, request *slbv1.CreateListenerRequest, slbRecord *db.Slbs,
) error {
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSLB")
	slbOld, err := resourceprovider.BosonProvider.BosonNetClient.SLBs().Get(
		ctx, resourceprovider.SlbCRName(slbRecord.Name), metav1.GetOptions{},
	)
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Listener CR create fail.", "GetSLB err:", err.Error())
		return err
	}

	slb := slbOld.DeepCopy()
	listener := s.generateListenerCR(request.Listener)
	slb.Spec.Listeners = append(slb.Spec.Listeners, *listener)
	slb.Spec.Listeners = sortListener(slb.Spec.Listeners)
	if reflect.DeepEqual(slb.Spec, slbOld.Spec) {
		msg := fmt.Sprintf(
			"slb:%s create listener:%s skip, already exist, CR:\n%+v\n",
			slbRecord.Name, request.ListenerName, slbOld,
		)
		logrus.Info(msg)
		return nil
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateSLB")
	_, err = resourceprovider.BosonProvider.BosonNetClient.SLBs().Update(ctx, slb, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Listener CR create fail. err: ", err.Error())
		return err
	}

	return nil
}

func (s SLBsServer) updateListenerCR(
	ctx context.Context, request *slbv1.UpdateListenerRequest, slbRecord *db.Slbs, listenerNew *slbv1.Listener,
) error {
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSLB")
	slbOld, err := resourceprovider.BosonProvider.BosonNetClient.SLBs().Get(
		ctx, resourceprovider.SlbCRName(slbRecord.Name), metav1.GetOptions{},
	)
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Listener CR update fail.", "GetSLB err:", err.Error())
		return err
	}

	slb := slbOld.DeepCopy()
	hit := false
	index := 0
	var listenerTmp netControllerV1.Listener
	for index, listenerTmp = range slb.Spec.Listeners {
		if listenerTmp.ListenerID == listenerNew.Uid {
			hit = true
			break
		}
	}
	if !hit {
		err := fmt.Errorf("update listener cr fail, slb%s can not find listener:%s", slbRecord.SlbID, listenerNew.Uid)
		logrus.Error(err.Error())
		return err
	}

	// listenerNew 需要是全量数据
	listenerCR := s.generateListenerCR(listenerNew)
	slb.Spec.Listeners[index] = *listenerCR
	if reflect.DeepEqual(slb.Spec, slbOld.Spec) {
		return nil
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateSLB")
	_, err = resourceprovider.BosonProvider.BosonNetClient.SLBs().Update(ctx, slb, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Listener CR update fail. err: ", err.Error())
		return err
	}

	return nil
}

func (s SLBsServer) deleteListenerCR(
	ctx context.Context, request *slbv1.DeleteListenerRequest, slbRecord *db.Slbs, listenerRecord *db.SlbListeners,
) error {
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSLB")
	slbOld, err := resourceprovider.BosonProvider.BosonNetClient.SLBs().Get(
		ctx, resourceprovider.SlbCRName(slbRecord.Name), metav1.GetOptions{},
	)
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Listener CR delete fail.", "GetSLB err:", err.Error())
		return err
	}

	slb := slbOld.DeepCopy()
	slb.Spec.Listeners = []netControllerV1.Listener{}
	for _, listenerTmp := range slbOld.Spec.Listeners {
		if listenerTmp.ListenerID == listenerRecord.ListenerID {
			continue
		}
		slb.Spec.Listeners = append(slb.Spec.Listeners, listenerTmp)
	}
	slb.Spec.Listeners = sortListener(slb.Spec.Listeners)

	if reflect.DeepEqual(slb.Spec, slbOld.Spec) {
		return nil
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateSLB")
	_, err = resourceprovider.BosonProvider.BosonNetClient.SLBs().Update(ctx, slb, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Listener CR delete fail. err: ", err.Error())
		return err
	}

	return nil
}

func (s SLBsServer) generateListenerCR(listener *slbv1.Listener) *netControllerV1.Listener {
	listenerCR := &netControllerV1.Listener{
		ListenerID:           listener.Uid,
		Protocol:             slbListenerProtocolTypeName(listener.Properties.Protocol),
		Port:                 listener.Properties.Port,
		DefaultForwardAction: netControllerV1.ForwardAction{},
		Option:               Listener_Start,
	}

	if listener.Properties.DefaultForwardAction != nil {
		defaultForwardAction := netControllerV1.ForwardAction{
			ActionType:               slbForwardActionTypeName(listener.Properties.DefaultForwardAction.Type),
			ForwardTargetGroupConfig: netControllerV1.ForwardTargetGroupConfig{},
		}
		if listener.Properties.DefaultForwardAction.ForwardTargetGroupConfig != nil {
			defaultForwardAction.ForwardTargetGroupConfig = netControllerV1.ForwardTargetGroupConfig{
				TargetGroupIDs: sortTargetGroupIDs(s.getTargetGroupIDs(
					listener.Properties.DefaultForwardAction.ForwardTargetGroupConfig.TargetGroups,
				)),
			}
		}
		listenerCR.DefaultForwardAction = defaultForwardAction
	}

	return listenerCR
}

func (s SLBsServer) createListener(
	ctx context.Context, request *slbv1.CreateListenerRequest, slbRecord *db.Slbs,
) error {
	engine := resourceprovider.BosonProvider.Engine

	// listener DB
	listener := db.SlbListeners{
		ID:                            request.Listener.Id,
		ListenerID:                    request.Listener.Uid,
		Name:                          request.Listener.Name,
		Zone:                          request.Listener.Zone,
		State:                         listenerStateName(slbv1.Listener_CREATING),
		DisplayName:                   request.Listener.DisplayName,
		Description:                   request.Listener.Description,
		ResourceType:                  request.Listener.ResourceType,
		SlbID:                         slbRecord.SlbID,
		ForwardActionType:             slbForwardActionTypeName(request.Listener.Properties.DefaultForwardAction.Type),
		Protocol:                      slbListenerProtocolTypeName(request.Listener.Properties.Protocol),
		Port:                          int32(request.Listener.Properties.Port),
		OriginalClientIpAddressEnable: false, // 暂不支持
		CreatorID:                     request.Listener.CreatorId,
		OwnerID:                       request.Listener.OwnerId,
		TenantID:                      request.Listener.TenantId,
		CreateTime:                    request.Listener.CreateTime.AsTime(),
		UpdateTime:                    request.Listener.UpdateTime.AsTime(),
		Deleted:                       false,
		Tags:                          "",
	}
	err := dao.InsertListener(engine, listener)
	if err != nil {
		logrus.Errorln("Create listener DB fail.", "insert listener record err:", err.Error())
		return err
	}

	// associations
	err = s.createListenerAssos(ctx, request, slbRecord)
	if err != nil {
		return err
	}

	return nil
}

func (s SLBsServer) createListenerAssos(
	ctx context.Context, request *slbv1.CreateListenerRequest, slbRecord *db.Slbs,
) error {
	engine := resourceprovider.BosonProvider.Engine

	assos := []*db.SlbForwardRuleAssociateTargetGroups{}
	if request.Listener.Properties.DefaultForwardAction == nil ||
		request.Listener.Properties.DefaultForwardAction.ForwardTargetGroupConfig == nil ||
		request.Listener.Properties.DefaultForwardAction.ForwardTargetGroupConfig.TargetGroups == nil {
		return nil
	}
	ids := s.getTargetGroupIDs(
		request.Listener.Properties.DefaultForwardAction.ForwardTargetGroupConfig.TargetGroups,
	)
	if len(ids) == 0 {
		return nil
	}

	for _, targetGroupID := range ids {
		assos = append(assos, &db.SlbForwardRuleAssociateTargetGroups{
			SlbID:           slbRecord.SlbID,
			ForwardRuleID:   request.Listener.Uid,
			TargetGroupID:   targetGroupID,
			ForwardRuleType: associationsType_listener,
			Deleted:         false,
		})
	}

	err := dao.InsertSlbAssociations(engine, assos)
	if err != nil {
		logrus.Errorln("Create listener DB fail.", "insert slb associations err:", err.Error())
		return err
	}

	return nil
}

func (s SLBsServer) updateListener(
	ctx context.Context, request *slbv1.UpdateListenerRequest, slbRecord *db.Slbs, listenerNew *slbv1.Listener,
) error {
	engine := resourceprovider.BosonProvider.Engine
	listenerRecord, err := s.getListenerRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.ListenerName, false,
	)
	if err != nil {
		logrus.Error(
			"Update listener fail. get listener db err",
			"slb", slbRecord.Name, "listener", listenerNew.Name, "error", err,
		)
		return err
	}

	// .DisplayName
	listenerRecord.DisplayName = listenerNew.DisplayName

	// .Description
	listenerRecord.Description = listenerNew.Description

	// .Protocol
	listenerRecord.Protocol = slbListenerProtocolTypeName(listenerNew.Properties.Protocol)

	// ..Port
	listenerRecord.Port = listenerNew.Properties.Port

	// ..DefaultForwardAction
	// 不支持修改

	// ..TargetGroups
	err = s.updateListenerTargetGroup(ctx, request, slbRecord, listenerNew, listenerRecord)
	if err != nil {
		logrus.Error(
			"Update listener fail. update listener targetGroup db err",
			"slb", slbRecord.Name, "listener", listenerNew.Name, "error", err,
		)
		return err
	}

	err = dao.UpdateSlbListener(engine, *listenerRecord)
	if err != nil {
		logrus.Error(
			"Update listener fail. update listener db err",
			"slb", slbRecord.Name, "listener", listenerNew.Name, "error", err,
		)
		return err
	}

	return nil
}

func (s SLBsServer) updateListenerTargetGroup(
	ctx context.Context, request *slbv1.UpdateListenerRequest,
	slbRecord *db.Slbs, listenerNew *slbv1.Listener, listenerRecord *db.SlbListeners,
) error {
	engine := resourceprovider.BosonProvider.Engine

	assosOld, err := dao.ListSlbAssociationsByForwardRule(engine, []string{listenerRecord.ListenerID}, associationsType_listener)
	if err != nil {
		logrus.Error(
			"Update listener DB fail. list listener target groups err",
			"slb", slbRecord.Name, "listenerID", listenerRecord.ListenerID, "error", err,
		)
		return err
	}
	// key: TargetGroupID, value: ListenerID
	assosOldMap := make(map[string]string)
	for _, assoTmp := range assosOld {
		assosOldMap[assoTmp.TargetGroupID] = assoTmp.ForwardRuleID
	}

	ids := s.getTargetGroupIDs(
		listenerNew.Properties.DefaultForwardAction.ForwardTargetGroupConfig.TargetGroups,
	)
	// key: TargetGroupID, value: ListenerID
	assosNewMap := make(map[string]string)
	for _, targetGroupID := range ids {
		assosNewMap[targetGroupID] = listenerRecord.ListenerID
	}

	// generate add
	idsAdd := []string{}
	for targetGroupIdNew := range assosNewMap {
		if _, ok := assosOldMap[targetGroupIdNew]; !ok {
			idsAdd = append(idsAdd, targetGroupIdNew)
		}
	}

	// generate delete
	idsDel := []string{}
	for targetGroupIdOld := range assosOldMap {
		if _, ok := assosNewMap[targetGroupIdOld]; !ok {
			idsDel = append(idsDel, targetGroupIdOld)
		}

	}

	logrus.Infof("Update listener:%s target group add:%+v del:%+v\n", listenerRecord.ListenerID, idsAdd, idsDel)

	// add db
	if len(idsAdd) != 0 {
		assos := []*db.SlbForwardRuleAssociateTargetGroups{}
		for _, targetGroupID := range idsAdd {
			assos = append(assos, &db.SlbForwardRuleAssociateTargetGroups{
				SlbID:           slbRecord.SlbID,
				ForwardRuleID:   listenerRecord.ListenerID,
				TargetGroupID:   targetGroupID,
				ForwardRuleType: associationsType_listener,
				Deleted:         false,
			})
		}
		err = dao.InsertSlbAssociations(engine, assos)
		if err != nil {
			logrus.Errorln(
				"Update listener DB fail.", "insert slb associations err:",
				"slb", slbRecord.Name, "listenerID", listenerRecord.ListenerID,
				"associations", assos, "err", err.Error(),
			)
			return err
		}
	}

	// del db
	if len(idsDel) != 0 {
		for _, targetGroupID := range idsDel {
			asso := db.SlbForwardRuleAssociateTargetGroups{
				SlbID:         slbRecord.SlbID,
				ForwardRuleID: listenerRecord.ListenerID,
				TargetGroupID: targetGroupID,
			}
			err = dao.DeleteSlbAssociations(engine, asso)
			if err != nil {
				logrus.Errorln(
					"Update listener DB fail.", "delete slb associations err:",
					"slb", slbRecord.Name, "listenerID", listenerRecord.ListenerID,
					"association", asso, "err", err.Error(),
				)
				continue
			}
		}
	}

	return nil
}

func (s SLBsServer) listListenersCheckAndGet(ctx context.Context, request *slbv1.ListListenersRequest) (*db.Slbs, error) {
	if request.SlbName == "" {
		err := fmt.Errorf("listListenersCheck fail. slbName is nil")
		logrus.Error(err)
		return nil, err
	}

	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		logrus.Errorln("Get SLB status one fail. get SLB record err.", err.Error())
		return nil, err
	}

	return slbRecord, nil
}

func ReleaseListener(slbRecord *db.Slbs, listenerID string) error {
	engine := resourceprovider.BosonProvider.Engine

	err := dao.ClearSlbAssociationsByForwardRuleIDs(engine, []string{listenerID})
	if err != nil {
		logrus.Error(
			"Delete listener fail. clear slb associations error",
			"slb", slbRecord.SlbID, "listener", listenerID,
		)
		return err
	}

	logrus.Infof("Delete slb:%s listener:%s", slbRecord.SlbID, listenerID)
	err = dao.DeleteListener(engine,
		slbRecord.SlbID, listenerID, listenerStateName(slbv1.Listener_DELETED),
	)
	if err != nil {
		logrus.Error(
			"Delete listener fail. delete listener db error",
			" slb ", slbRecord.SlbID, " listener ", listenerID,
		)
		return err
	}

	return nil
}

func slbListenerProtocolTypeName(protocol slbv1.ListenerProperties_ListenProtocol) string {
	return slbv1.ListenerProperties_ListenProtocol_name[int32(protocol)]
}

func slbListenerProtocolTypeValue(protocol string) slbv1.ListenerProperties_ListenProtocol {
	return slbv1.ListenerProperties_ListenProtocol(slbv1.ListenerProperties_ListenProtocol_value[protocol])
}

func slbForwardActionTypeName(actionType slbv1.ForwardAction_ForwardActionType) string {
	return slbv1.ForwardAction_ForwardActionType_name[int32(actionType)]
}

func slbForwardActionTypeValue(actionType string) slbv1.ForwardAction_ForwardActionType {
	return slbv1.ForwardAction_ForwardActionType(slbv1.ForwardAction_ForwardActionType_value[actionType])
}

func listenerStateName(state slbv1.Listener_State) string {
	return slbv1.Listener_State_name[int32(state)]
}

func listenerStateValue(state string) slbv1.Listener_State {
	return slbv1.Listener_State(slbv1.Listener_State_value[state])
}

func (s SLBsServer) updateListenerGetWithCheck(
	ctx context.Context, request *slbv1.UpdateListenerRequest,
) (*db.Slbs, *slbv1.Listener, error) {
	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		return nil, nil, err
	}
	listenerRecord, err := s.getListenerRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.ListenerName, false,
	)
	if err != nil {
		logrus.Errorf("Update listener fail. get slb:%s listenerDB:%s err:%s\n", slbRecord.SlbID, request.ListenerName, err)
		return nil, nil, err
	}
	listenerNew, err := s.generateListener(slbRecord, listenerRecord)
	if err != nil {
		return nil, nil, err
	}

	maskMap := generateFieldMaskMap(request.UpdateMask)

	// .DisplayName

	if listenerDisplayNameIsSet(maskMap) {
		listenerNew.DisplayName = request.Listener.GetDisplayName()
	}

	// .Description
	if listenerDescriptionIsSet(maskMap) {
		listenerNew.Description = request.Listener.Description
	}

	if request.Listener.Properties == nil {
		return slbRecord, listenerNew, nil
	}

	// .Protocol
	if listenerPropertiesProtocolIsSet(maskMap) {
		if !s.listenerProtocolValid(request.Listener.Properties.Protocol) {
			err := fmt.Errorf(
				"update listener:%s protocol:%+v(%s) is invalid",
				request.ListenerName,
				request.Listener.Properties.Protocol,
				slbListenerProtocolTypeName(request.Listener.Properties.Protocol),
			)
			logrus.Errorf("update listener check protocol err:%s", err)
			return nil, nil, err
		}
		listenerNew.Properties.Protocol = request.Listener.Properties.Protocol
	}

	// .Port
	if listenerPropertiesPortIsSet(maskMap) {
		if !s.listenerPortValid(int(request.Listener.Properties.Port)) {
			err := fmt.Errorf(
				"update listener:%s port:%d is invalid",
				request.ListenerName,
				request.Listener.Properties.Port,
			)
			logrus.Errorf("update listener check port err:%s", err)
			return nil, nil, err
		}
		listenerNew.Properties.Port = request.Listener.Properties.Port
	}

	// Protocol:Port
	protocolPortValid, err := s.listenerProtocolPortValidUpdate(
		slbRecord, listenerRecord, listenerNew.Properties.Protocol, int(listenerNew.Properties.Port),
	)
	if err != nil {
		logrus.Errorln(err.Error())
		return nil, nil, err
	}
	if !protocolPortValid {
		err := fmt.Errorf(
			"update listener:%s protocol:%s port:%d is invalid, already exist",
			request.ListenerName,
			listenerNew.Properties.Protocol.String(),
			request.Listener.Properties.Port,
		)
		logrus.Errorf("update listener check port err:%s", err)
		return nil, nil, err
	}

	// .DefaultForwardAction 不支持修改
	// nothing

	// ..TargetGroups
	defaultForwardActionCheckFn := func() error {
		// .Type
		if listenerPropertiesDefaultForwardActionTypeIsSet(maskMap) {
			if request.Listener.Properties.DefaultForwardAction.Type != slbv1.ForwardAction_FORWARD_TARGET_GROUP {
				err := fmt.Errorf(
					"listener:%s default forward action type:%d is error",
					request.ListenerName,
					request.Listener.Properties.DefaultForwardAction.Type,
				)
				logrus.Error(err.Error())
				return err
			}
			if request.Listener.Properties.DefaultForwardAction.Type != listenerNew.Properties.DefaultForwardAction.Type {
				err := fmt.Errorf(
					"listener:%s default forward action type not support change old:%d new:%d",
					request.ListenerName,
					listenerNew.Properties.DefaultForwardAction.Type,
					request.Listener.Properties.DefaultForwardAction.Type,
				)
				logrus.Error(err.Error())
				return err
			}
		}

		if request.Listener.Properties.DefaultForwardAction.ForwardTargetGroupConfig == nil {
			return nil
		}

		// .ForwardTargetGroupConfig
		// fieldmaskpb.FieldMask 在处理 repeated 的时候，貌似有点问题，需要深入看下，这里先这样处理
		// TODO 问题：不能精确识别数组的每一个修改，怀疑需要在数组元素的定义里面，也加上 FieldMask
		forwardConfig := request.Listener.Properties.DefaultForwardAction.ForwardTargetGroupConfig
		if listenerPropertiesDefaultForwardActionForwardTargetGroupConfigTargetGroupsIsSet(maskMap) {
			if len(forwardConfig.TargetGroups) == 0 {
				listenerNew.Properties.DefaultForwardAction.ForwardTargetGroupConfig.TargetGroups = []*slbv1.TargetGroupInfo{}
			} else {
				ids := s.getTargetGroupIDs(forwardConfig.TargetGroups)
				engine := resourceprovider.BosonProvider.Engine
				has, err := dao.CheckTargetGroupIDs(engine, slbRecord.SlbID, ids)
				if err != nil {
					err = fmt.Errorf(
						"Update listener fail. slb:%s listener:%s check TargetGroups:%v error:%s\n",
						slbRecord.SlbID, listenerRecord.ListenerID, ids, err.Error(),
					)
					logrus.Errorln(err.Error())
					return err
				}

				if !has {
					err = fmt.Errorf(
						"Update listener fail. slb:%s listener:%s TargetGroups:%v not exist\n",
						slbRecord.SlbID, listenerRecord.ListenerID, ids,
					)
					logrus.Errorln(err.Error())
					return err
				}

				// fixme 当前只会有一个，就不额外的处理 adds, dels, updates 了
				listenerNew.Properties.DefaultForwardAction.ForwardTargetGroupConfig.TargetGroups =
					request.Listener.Properties.DefaultForwardAction.ForwardTargetGroupConfig.TargetGroups
			}
		}

		return nil
	}
	if listenerNew.Properties.DefaultForwardAction != nil {
		err := defaultForwardActionCheckFn()
		if err != nil {
			logrus.Errorf("update listener check default forward action err:%s", err)
			return nil, nil, err
		}
	}

	// .OriginalClientIpAddress
	// fixme not support

	return slbRecord, listenerNew, nil
}

func (s SLBsServer) listenerProtocolValid(protocol slbv1.ListenerProperties_ListenProtocol) bool {
	switch protocol {
	case slbv1.ListenerProperties_TCP, slbv1.ListenerProperties_UDP:
		return true
	}
	return false
}

func (s SLBsServer) listenerPortValid(port int) bool {
	if port > 0 && port < 65535 {
		return true
	}

	return false
}

func (s SLBsServer) listenerProtocolPortValidCreate(
	slbRecord *db.Slbs, protocol slbv1.ListenerProperties_ListenProtocol, port int,
) (bool, error) {
	engine := resourceprovider.BosonProvider.Engine

	listenerRecords, err := dao.GetSLBListenersBySlbID(engine, slbRecord.SlbID, false)
	if err != nil {
		logrus.Errorf("list slb:%s listeners fail. list DBs err:%s", slbRecord.SlbID, err)
		return false, err
	}

	// key: {protocol}:{port}, value: bool
	protocolPortMap := make(map[string]bool)
	for _, listener := range listenerRecords {
		key := fmt.Sprintf("%s:%d", listener.Protocol, listener.Port)
		protocolPortMap[key] = true
	}

	// ref:
	// Protocol: slbListenerProtocolTypeName(request.Listener.Properties.Protocol),
	// Port:     int32(request.Listener.Properties.Port),
	checkKey := fmt.Sprintf("%s:%d", slbListenerProtocolTypeName(protocol), int32(port))
	if _, ok := protocolPortMap[checkKey]; ok {
		// already exist
		return false, nil
	}

	return true, nil
}

func (s SLBsServer) listenerProtocolPortValidUpdate(
	slbRecord *db.Slbs, listenerRecord *db.SlbListeners,
	protocol slbv1.ListenerProperties_ListenProtocol, port int,
) (bool, error) {
	engine := resourceprovider.BosonProvider.Engine

	listenerRecords, err := dao.GetSLBListenersBySlbID(engine, slbRecord.SlbID, false)
	if err != nil {
		logrus.Errorf("list slb:%s listeners fail. list DBs err:%s", slbRecord.SlbID, err)
		return false, err
	}

	// key: {protocol}:{port}, value: bool
	protocolPortMap := make(map[string]bool)
	for _, listener := range listenerRecords {
		if listener.ListenerID == listenerRecord.ListenerID {
			// skip self
			continue
		}
		key := fmt.Sprintf("%s:%d", listener.Protocol, listener.Port)
		protocolPortMap[key] = true
	}

	// ref:
	// Protocol: slbListenerProtocolTypeName(request.Listener.Properties.Protocol),
	// Port:     int32(request.Listener.Properties.Port),
	checkKey := fmt.Sprintf("%s:%d", slbListenerProtocolTypeName(protocol), int32(port))
	if _, ok := protocolPortMap[checkKey]; ok {
		// already exist
		return false, nil
	}

	return true, nil
}

func (s SLBsServer) listenerActionValid(slbRecord *db.Slbs, request *slbv1.CreateListenerRequest) error {
	if request.Listener.Properties.DefaultForwardAction == nil {
		err := fmt.Errorf(
			"Create listener fail. slb:%s listener:%s DefaultForwardAction is nil\n",
			slbRecord.SlbID, request.Listener.Uid,
		)
		logrus.Errorln(err.Error())
		return err
	}

	// .Type
	if request.Listener.Properties.DefaultForwardAction.Type != slbv1.ForwardAction_FORWARD_TARGET_GROUP {
		err := fmt.Errorf(
			"Create listener fail. slb:%s listener:%s DefaultForwardAction type:%s is error\n",
			slbRecord.SlbID, request.Listener.Uid,
			request.Listener.Properties.DefaultForwardAction.Type.String(),
		)
		logrus.Errorln(err.Error())
		return err
	}

	if request.Listener.Properties.DefaultForwardAction.ForwardTargetGroupConfig == nil {
		return nil
	}

	// .IDs 的存在性检查
	if len(request.Listener.Properties.DefaultForwardAction.ForwardTargetGroupConfig.TargetGroups) != 0 {
		ids := s.getTargetGroupIDs(
			request.Listener.Properties.DefaultForwardAction.ForwardTargetGroupConfig.TargetGroups,
		)
		engine := resourceprovider.BosonProvider.Engine
		has, err := dao.CheckTargetGroupIDs(engine, slbRecord.SlbID, ids)
		if err != nil {
			err = fmt.Errorf(
				"Create listener fail. slb:%s listener:%s check TargetGroups:%v error:%s\n",
				slbRecord.SlbID, request.Listener.Uid,
				ids,
				err.Error(),
			)
			logrus.Errorln(err.Error())
			return err
		}

		if !has {
			err = fmt.Errorf(
				"Create listener fail. slb:%s listener:%s TargetGroups:%v not exist\n",
				slbRecord.SlbID, request.Listener.Uid,
				ids,
			)
			logrus.Errorln(err.Error())
			return err
		}
	}

	return nil
}

func (s SLBsServer) listenerQuota(slbRecord *db.Slbs, request *slbv1.CreateListenerRequest) error {
	engine := resourceprovider.BosonProvider.Engine
	vpcQuota, err := dao.GetVpcQuotaByVpcID(engine, slbRecord.VpcID)
	if err != nil {
		logrus.Errorf(
			"vpc:%s slb:%s listener:%s create fail, get quota err:%s",
			slbRecord.VpcID, slbRecord.SlbID, request.ListenerName, err,
		)
		return err
	}

	listenersRecord, err := dao.GetSLBListenersBySlbID(engine, slbRecord.SlbID, false)
	if err != nil {
		logrus.Errorf(
			"vpc:%s slb:%s listener:%s create fail, list listener db err:%s",
			slbRecord.VpcID, slbRecord.SlbID, request.ListenerName, err,
		)
		return err
	}
	count := len(listenersRecord)
	if count >= int(vpcQuota.ListenerPerSlb) {
		err := fmt.Errorf(
			"vpc:%s slb:%s listener:%s create fail, Listener limit exceeded, quota:%d current count:%d",
			slbRecord.VpcID, slbRecord.SlbID, request.ListenerName, vpcQuota.ListenerPerSlb, count,
		)
		logrus.Errorln(err)
		return err
	}

	return nil
}

func (s SLBsServer) startListenerGetWithCheck(
	ctx context.Context, request *slbv1.StartListenerRequest,
) (*db.Slbs, *db.SlbListeners, bool, error) { // bool: needStart
	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		return nil, nil, false, err
	}
	listenerRecord, err := s.getListenerRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.ListenerName, false,
	)
	if err != nil {
		logrus.Errorf("Start listener fail. get slb:%s listenerDB:%s err:%s\n", slbRecord.SlbID, request.ListenerName, err)
		return nil, nil, false, err
	}

	if listenerRecord.State == listenerStateName(slbv1.Listener_ACTIVE) {
		logrus.Infof(
			"Start slb:%s listener:%s already active",
			slbRecord.SlbID, listenerRecord.ListenerID,
		)
		return nil, nil, false, err
	}

	if listenerRecord.State == listenerStateName(slbv1.Listener_STARTING) {
		logrus.Infof(
			"Start slb:%s listener:%s starting",
			slbRecord.SlbID, listenerRecord.ListenerID,
		)
		return nil, nil, false, err
	}

	if listenerRecord.State == listenerStateName(slbv1.Listener_STOPPING) ||
		listenerRecord.State == listenerStateName(slbv1.Listener_STOPPED) {
		logrus.Infof(
			"Start slb:%s listener:%s state:%s will starting",
			slbRecord.SlbID, listenerRecord.ListenerID, listenerRecord.State,
		)
		return slbRecord, listenerRecord, true, err
	}

	err = fmt.Errorf(
		"Start slb:%s listener:%s state:%s is error, can not start",
		slbRecord.SlbID, listenerRecord.ListenerID, listenerRecord.State,
	)
	return nil, nil, false, err
}

func (s SLBsServer) startListenerCR(
	ctx context.Context, request *slbv1.StartListenerRequest, slbRecord *db.Slbs, listenerRecord *db.SlbListeners,
) error {
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSLB")
	slbOld, err := resourceprovider.BosonProvider.BosonNetClient.SLBs().Get(
		ctx, resourceprovider.SlbCRName(slbRecord.Name), metav1.GetOptions{},
	)
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Listener CR start fail.", "GetSLB err:", err.Error())
		return err
	}

	slb := slbOld.DeepCopy()
	hit := false
	index := 0
	var listenerTmp netControllerV1.Listener
	for index, listenerTmp = range slb.Spec.Listeners {
		if listenerTmp.ListenerID == listenerRecord.ListenerID {
			hit = true
			break
		}
	}
	if !hit {
		err := fmt.Errorf("update listener cr fail, slb%s can not find listener:%s", slbRecord.SlbID, listenerRecord.ListenerID)
		logrus.Error(err.Error())
		return err
	}

	listenerCR := slb.Spec.Listeners[index].DeepCopy()
	listenerCR.Option = Listener_Start // TODO 定义成 const，看从net-controller能不能引用进来
	slb.Spec.Listeners[index] = *listenerCR

	if reflect.DeepEqual(slb.Spec, slbOld.Spec) {
		return nil
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateSLB")
	_, err = resourceprovider.BosonProvider.BosonNetClient.SLBs().Update(ctx, slb, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Listener CR start fail. err: ", err.Error())
		return err
	}

	return nil
}

func (s SLBsServer) startListener(
	ctx context.Context, slbRecord *db.Slbs, listenerRecord *db.SlbListeners,
) error {
	engine := resourceprovider.BosonProvider.Engine
	err := dao.SetListenerState(
		engine, slbRecord.SlbID, listenerRecord.ListenerID, listenerStateName(slbv1.Listener_STARTING),
	)
	if err != nil {
		logrus.Errorf(
			"Start slb:%s listener:%s fail, update db state:%s err:%s",
			slbRecord.SlbID, listenerRecord.ListenerID, listenerStateName(slbv1.Listener_STARTING), err,
		)
		return err
	}
	logrus.Infof("SLB %s listener:%s change to %s", slbRecord.SlbID, listenerRecord.ListenerID, listenerStateName(slbv1.Listener_STARTING))
	return nil
}

func (s SLBsServer) stopListenerGetWithCheck(
	ctx context.Context, request *slbv1.StopListenerRequest,
) (*db.Slbs, *db.SlbListeners, bool, error) { // bool: needStop
	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		return nil, nil, false, err
	}
	listenerRecord, err := s.getListenerRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.ListenerName, false,
	)
	if err != nil {
		logrus.Errorf("Stop listener fail. get slb:%s listenerDB:%s err:%s\n", slbRecord.SlbID, request.ListenerName, err)
		return nil, nil, false, err
	}

	if listenerRecord.State == listenerStateName(slbv1.Listener_STOPPED) {
		logrus.Infof(
			"Stop slb:%s listener:%s already stopped",
			slbRecord.SlbID, listenerRecord.ListenerID,
		)
		return nil, nil, false, err
	}

	if listenerRecord.State == listenerStateName(slbv1.Listener_STOPPING) {
		logrus.Infof(
			"Stop slb:%s listener:%s stopping",
			slbRecord.SlbID, listenerRecord.ListenerID,
		)
		return nil, nil, false, err
	}

	if listenerRecord.State == listenerStateName(slbv1.Listener_STARTING) ||
		listenerRecord.State == listenerStateName(slbv1.Listener_ACTIVE) {
		logrus.Infof(
			"Stop slb:%s listener:%s state:%s will stopping",
			slbRecord.SlbID, listenerRecord.ListenerID, listenerRecord.State,
		)
		return slbRecord, listenerRecord, true, err
	}

	err = fmt.Errorf(
		"Stop slb:%s listener:%s state:%s is error, can not stop",
		slbRecord.SlbID, listenerRecord.ListenerID, listenerRecord.State,
	)
	return nil, nil, false, err
}

func (s SLBsServer) stopListenerCR(
	ctx context.Context, request *slbv1.StopListenerRequest, slbRecord *db.Slbs, listenerRecord *db.SlbListeners,
) error {
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSLB")
	slbOld, err := resourceprovider.BosonProvider.BosonNetClient.SLBs().Get(
		ctx, resourceprovider.SlbCRName(slbRecord.Name), metav1.GetOptions{},
	)
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Listener CR stop fail.", "GetSLB err:", err.Error())
		return err
	}

	slb := slbOld.DeepCopy()
	hit := false
	index := 0
	var listenerTmp netControllerV1.Listener
	for index, listenerTmp = range slb.Spec.Listeners {
		if listenerTmp.ListenerID == listenerRecord.ListenerID {
			hit = true
			break
		}
	}
	if !hit {
		err := fmt.Errorf("update listener cr fail, slb%s can not find listener:%s", slbRecord.SlbID, listenerRecord.ListenerID)
		logrus.Error(err.Error())
		return err
	}

	listenerCR := slb.Spec.Listeners[index].DeepCopy()
	listenerCR.Option = Listener_Stop // TODO 定义成 const，看从net-controller能不能引用进来
	slb.Spec.Listeners[index] = *listenerCR

	if reflect.DeepEqual(slb.Spec, slbOld.Spec) {
		return nil
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateSLB")
	_, err = resourceprovider.BosonProvider.BosonNetClient.SLBs().Update(ctx, slb, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Listener CR stop fail. err: ", err.Error())
		return err
	}

	return nil
}

func (s SLBsServer) stopListener(
	ctx context.Context, slbRecord *db.Slbs, listenerRecord *db.SlbListeners,
) error {
	engine := resourceprovider.BosonProvider.Engine
	err := dao.SetListenerState(
		engine, slbRecord.SlbID, listenerRecord.ListenerID, listenerStateName(slbv1.Listener_STOPPING),
	)
	if err != nil {
		logrus.Errorf(
			"Stop slb:%s listener:%s fail, update db state:%s err:%s",
			slbRecord.SlbID, listenerRecord.ListenerID, listenerStateName(slbv1.Listener_STOPPING), err,
		)
		return err
	}
	logrus.Infof("SLB %s listener:%s change to %s", slbRecord.SlbID, listenerRecord.ListenerID, listenerStateName(slbv1.Listener_STOPPING))
	return nil
}

func (s SLBsServer) createListenerRequestFill(ctx context.Context, request *slbv1.CreateListenerRequest) {
	// 数据面资源，需要自己管理 ID、uid 等信息
	id := dao.SlbListenerResourceId(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.ListenerName)
	uid := slbDataResourceUid()
	creatorId := GetUserIDFromHeader(ctx)
	ownerID := creatorId
	tenantID := GetTenantIDFromHeader(ctx)
	zone := request.Zone
	createTime := timestamppb.Now()
	updateTime := createTime

	request.Listener.Id = id
	request.Listener.Uid = uid
	request.Listener.CreatorId = creatorId
	request.Listener.OwnerId = ownerID
	request.Listener.TenantId = tenantID
	request.Listener.Zone = zone
	request.Listener.CreateTime = createTime
	request.Listener.UpdateTime = updateTime
}

func sortListener(listeners []netControllerV1.Listener) []netControllerV1.Listener {
	s := listenerSlice(listeners)
	sort.Sort(s)
	return s
}

type listenerSlice []netControllerV1.Listener

func (s listenerSlice) Len() int {
	return len(s)
}

func (s listenerSlice) Less(i, j int) bool {
	return s[i].ListenerID < s[j].ListenerID
}

func (s listenerSlice) Swap(i, j int) {
	s[i], s[j] = *s[j].DeepCopy(), *s[i].DeepCopy()
}

func listenerDisplayNameIsSet(fieldMaskMap fieldMaskMapType) bool {
	const mask = "display_name"
	if fieldMaskMap == nil {
		return false
	}

	_, ok := fieldMaskMap[mask]
	return ok
}

func listenerDescriptionIsSet(fieldMaskMap fieldMaskMapType) bool {
	const mask = "description"
	if fieldMaskMap == nil {
		return false
	}

	_, ok := fieldMaskMap[mask]
	return ok
}

func listenerPropertiesProtocolIsSet(fieldMaskMap fieldMaskMapType) bool {
	const mask = "properties.protocol"
	if fieldMaskMap == nil {
		return false
	}

	_, ok := fieldMaskMap[mask]
	return ok
}

func listenerPropertiesPortIsSet(fieldMaskMap fieldMaskMapType) bool {
	const mask = "properties.port"
	if fieldMaskMap == nil {
		return false
	}

	_, ok := fieldMaskMap[mask]
	return ok
}

func listenerPropertiesDefaultForwardActionTypeIsSet(fieldMaskMap fieldMaskMapType) bool {
	const mask = "properties.default_forward_action.type"
	if fieldMaskMap == nil {
		return false
	}

	_, ok := fieldMaskMap[mask]
	return ok
}

func listenerPropertiesDefaultForwardActionForwardTargetGroupConfigTargetGroupsIsSet(fieldMaskMap fieldMaskMapType) bool {
	const mask = "properties.default_forward_action.forward_target_group_config.target_groups"
	if fieldMaskMap == nil {
		return false
	}

	_, ok := fieldMaskMap[mask]
	return ok
}

// TODO 定义成 const，看从net-controller能不能引用进来
const (
	Listener_Start = "START"
	Listener_Stop  = "STOP"
)

func (s SLBsServer) getListenerRecordByResourceId(
	subscription, resourceGroup, zone, slbName, listenerName string, includeDeleted bool,
) (*db.SlbListeners, error) {
	resourceID := dao.SlbListenerResourceId(subscription, resourceGroup, zone, slbName, listenerName)
	engine := resourceprovider.BosonProvider.Engine
	return dao.GetSLBListenerByResourceId(engine, resourceID, includeDeleted)
}
