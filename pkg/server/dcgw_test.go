package server

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/dc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"google.golang.org/grpc/codes"
)

func TestCreateDCGWAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditCreateDcgwError, func(ctx context.Context, resourceID string, request *dc.CreateDCGWRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditCreateDcgwSuccess, func(ctx context.Context, resourceID string, request *dc.CreateDCGWRequest,
		response interface{}) {
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.CreateDCGWRequest{
		Zone: "test11",
	}

	dcgwsServer := DCGWsServer{}
	ctx := context.Background()

	_, err := dcgwsServer.CreateDCGW(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestCreateDCGWExist(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditCreateDcgwError, func(ctx context.Context, resourceID string, request *dc.CreateDCGWRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditCreateDcgwSuccess, func(ctx context.Context, resourceID string, request *dc.CreateDCGWRequest,
		response interface{}) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.ExistDcgw, func(_ *db.EngineWrapper, _ string) (bool, error) {
		return true, nil
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.CreateDCGWRequest{
		Zone: "test",
	}

	dcgwsServer := DCGWsServer{}
	ctx := context.Background()

	_, err := dcgwsServer.CreateDCGW(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.AlreadyExists, errors.DcgwExist, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteDCGWAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditDeleteDcgwError, func(ctx context.Context, resourceID string, request *dc.DeleteDCGWRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditDeleteDcgwSuccess, func(ctx context.Context, resourceID string, request *dc.DeleteDCGWRequest,
		response interface{}) {
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.DeleteDCGWRequest{
		Zone: "test11",
	}

	dcgwsServer := DCGWsServer{}
	ctx := context.Background()

	_, err := dcgwsServer.DeleteDCGW(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteDCGWNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditDeleteDcgwError, func(ctx context.Context, resourceID string, request *dc.DeleteDCGWRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditDeleteDcgwSuccess, func(ctx context.Context, resourceID string, request *dc.DeleteDCGWRequest,
		response interface{}) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.ExistDcgwResource, func(_ *db.EngineWrapper, _ string, _ string, _ string) (bool, error) {
		return false, nil
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.DeleteDCGWRequest{
		Zone: "test",
	}

	dcgwsServer := DCGWsServer{}
	ctx := context.Background()

	_, err := dcgwsServer.DeleteDCGW(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.DcgwNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestUpdateDCGWAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditUpdateDcgwError, func(ctx context.Context, resourceID string, request *dc.UpdateDCGWRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditUpdateDcgwSuccess, func(ctx context.Context, resourceID string, request *dc.UpdateDCGWRequest,
		response interface{}) {
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.UpdateDCGWRequest{
		Zone: "test11",
	}

	dcgwsServer := DCGWsServer{}
	ctx := context.Background()

	_, err := dcgwsServer.UpdateDCGW(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestUpdateDCGWNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditUpdateDcgwError, func(ctx context.Context, resourceID string, request *dc.UpdateDCGWRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditUpdateDcgwSuccess, func(ctx context.Context, resourceID string, request *dc.UpdateDCGWRequest,
		response interface{}) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.ExistDcgwResource, func(_ *db.EngineWrapper, _ string, _ string, _ string) (bool, error) {
		return false, nil
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.UpdateDCGWRequest{
		Zone: "test",
		Dcgw: &dc.DCGWUpdateProperties{
			Description: "test",
		},
	}

	dcgwsServer := DCGWsServer{}
	ctx := context.Background()

	_, err := dcgwsServer.UpdateDCGW(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.DcgwNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetDCGWAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.GetDCGWRequest{
		Zone: "test11",
	}

	dcgwsServer := DCGWsServer{}
	ctx := context.Background()

	_, err := dcgwsServer.GetDCGW(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetDCGWNotFound1(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.ExistDcgwResource, func(_ *db.EngineWrapper, _ string, _ string, _ string) (bool, error) {
		return false, nil
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.GetDCGWRequest{
		Zone: "test",
	}

	dcgwsServer := DCGWsServer{}
	ctx := context.Background()

	_, err := dcgwsServer.GetDCGW(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.DcgwNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetDCGWNotFound2(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.ExistDcgwResource, func(_ *db.EngineWrapper, _ string, _ string, _ string) (bool, error) {
		return true, nil
	})
	p.ApplyFunc(dao.GetDcgwByName, func(_ *db.EngineWrapper, _ string) (*db.Dcgws, error) {
		return nil, fmt.Errorf("dcgw not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.GetDCGWRequest{
		Zone: "test",
	}

	dcgwsServer := DCGWsServer{}
	ctx := context.Background()

	_, err := dcgwsServer.GetDCGW(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.DcgwNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestListDCGWAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.ListDCGWsRequest{
		Zone: "test11",
	}

	dcgwsServer := DCGWsServer{}
	ctx := context.Background()

	_, err := dcgwsServer.ListDCGWs(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}
