package server

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"google.golang.org/grpc/codes"
)

func TestListSNATRulesNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return nil, fmt.Errorf("Eip not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.ListSNATRulesRequest{
		Zone: "test",
	}

	snatRulesServer := SNATRulesServer{}
	ctx := context.Background()

	_, err := snatRulesServer.ListSNATRules(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetSNATRuleNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return &db.Eips{}, nil
	})
	p.ApplyFunc(dao.GetSnatRule, func(_ *db.EngineWrapper, _ string) (*db.SnatRules, error) {
		return nil, fmt.Errorf("SnatRule not found")
	})
	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.GetSNATRuleRequest{
		Zone: "test",
	}

	snatRulesServer := SNATRulesServer{}
	ctx := context.Background()

	_, err := snatRulesServer.GetSNATRule(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.SnatNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestCreateSNATRuleNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditBindDnatError, func(ctx context.Context, resourceID string, request *eip.BindDNATRuleRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditBindDnatSuccess, func(ctx context.Context, resourceID string, request *eip.BindDNATRuleRequest,
		response interface{}) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetEipByID, func(_ *db.EngineWrapper, _ string, _ bool) (*db.Eips, error) {
		return &db.Eips{}, fmt.Errorf("EIP not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.CreateSNATRuleRequest{
		Zone: "test",
		SnatRule: &eip.SNATRule{
			Properties: &eip.SNATRuleProperties{},
		},
	}

	snatRulesServer := SNATRulesServer{}
	ctx := context.Background()

	_, err := snatRulesServer.CreateSNATRule(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteSNATRuleErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return nil, fmt.Errorf("unknown err")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.DeleteSNATRuleRequest{
		Zone: "test",
	}

	snatRulesServer := SNATRulesServer{}
	ctx := context.Background()

	_, err := snatRulesServer.DeleteSNATRule(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}
