package server

import (
	"context"
	"encoding/json"
	"fmt"
	"net"
	"net/http"
	"regexp"
	"strings"

	networkv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	k8s_errors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"

	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/metrics"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
)

// EIPsServer 为 EIPsServer interface 的实现
type EIPsServer struct {
	*eip.UnimplementedEIPsServer
}

func (EIPsServer) ListEIPs(ctx context.Context, request *eip.ListEIPsRequest) (res *eip.ListEIPsResponse, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics("", "EIP", "ListEIPs")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	idPrefix := utils.GenResourcePrefix(request.SubscriptionName, request.ResourceGroupName, request.Zone)
	eipDatas, err := dao.GetAllEIPsWithIDPrefix(resourceprovider.BosonProvider.Engine, idPrefix)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	eips := make([]*eip.EIP, len(eipDatas))

	for i, eipData := range eipDatas {
		eips[i] = &eip.EIP{
			Id:           eipData.ID,
			Name:         eipData.Name,
			DisplayName:  eipData.DisplayName,
			Description:  eipData.Description,
			Uid:          eipData.EIPID,
			ResourceType: eipData.ResourceType,
			CreatorId:    eipData.CreatorID,
			OwnerId:      eipData.OwnerID,
			TenantId:     eipData.TenantID,
			Zone:         eipData.Zone,
			State:        eip.EIP_State(eip.EIP_State_value[eipData.State]),
			Deleted:      eipData.Deleted,
			CreateTime:   timestamppb.New(eipData.CreateTime),
			UpdateTime:   timestamppb.New(eipData.UpdateTime),
			Properties: &eip.EIPProperties{
				VpcId:           eipData.VPCID,
				AssociationId:   eipData.AssociationID,
				AssociationType: eip.EIPProperties_AType(eip.EIPProperties_AType_value[eipData.AssociationType]),
				Resources: &eip.Resources{
					BillingItems: &eip.BillingItems{
						Bw: eipData.BW,
					},
					LimitRateItems: &eip.LimitRateItems{
						UpStreamBw:   eipData.UpStreamBW,
						DownStreamBw: eipData.DownStreamBW,
					},
				},
				InternalEip:        eipData.InternalEIP,
				SnatDstCidrEnabled: eipData.SnatDstCidrEnabled,
			},
		}
	}

	res = &eip.ListEIPsResponse{
		Eips:      eips,
		TotalSize: int32(len(eips)),
	}

	logrus.Infof("list EIP total size: %d", len(eips))

	return res, nil
}

func (EIPsServer) GetEIPStatus(ctx context.Context, request *eip.GetEIPStatusRequest) (res *eip.EIPStatus, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "EIP", "GetEIPStatus")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(resourceprovider.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if eipData.Deleted {
		err = fmt.Errorf("EIP %v has been deleted", request.EipName)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	dnatCount, err := dao.GetEipRuleCount(resourceprovider.BosonProvider.Engine, eipData.EIPID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	vpcData, err := dao.GetVpc(resourceprovider.BosonProvider.Engine, eipData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	eipPoolData, err := dao.GetEipPool(resourceprovider.BosonProvider.Engine, eipData.EIPPoolID)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipPoolNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	snat, err := dao.ExistDefaultSnatRule(resourceprovider.BosonProvider.Engine, eipData.EIPID, eipData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	dbSnatRules, err := dao.ListSnatRules(resourceprovider.BosonProvider.Engine, eipData.EIPID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	snatInfo := []*eip.SnatStatus{}
	for _, rule := range dbSnatRules {
		snatInfo = append(snatInfo, &eip.SnatStatus{
			Name:        rule.Name,
			InnerIp:     rule.InnerIP,
			OuterIp:     rule.OuterIP,
			Policy:      "dst",
			PolicyValue: rule.DstCidrs,
		})
	}
	aclds, err := dao.ListACLs(resourceprovider.BosonProvider.Engine, eipData.EIPID)
	if err != nil {
		logrus.Warnln(err)
		// TODO: do not return here
	}
	acls := []*eip.EIPACL{}
	for _, v := range aclds {
		acls = append(acls, &eip.EIPACL{
			AclName: v.Name,
			AclProperties: &eip.ACLProperties{
				Action:   eip.ACLProperties_ACLAction(eip.ACLProperties_ACLAction_value[v.Action]),
				Src:      v.Src,
				Dest:     v.Dest,
				DestPort: v.DestPort,
				Protocol: v.Protocol,
				Priority: v.Priority,
			},
			AclState: v.State,
		})
	}
	res = &eip.EIPStatus{
		EipInfo: &eip.EIP{
			Id:           eipData.ID,
			Name:         eipData.Name,
			DisplayName:  eipData.DisplayName,
			Description:  eipData.Description,
			Uid:          eipData.EIPID,
			ResourceType: eipData.ResourceType,
			CreatorId:    eipData.CreatorID,
			OwnerId:      eipData.OwnerID,
			TenantId:     eipData.TenantID,
			Zone:         eipData.Zone,
			State:        eip.EIP_State(eip.EIP_State_value[eipData.State]),
			Deleted:      eipData.Deleted,
			CreateTime:   timestamppb.New(eipData.CreateTime),
			UpdateTime:   timestamppb.New(eipData.UpdateTime),
			Properties: &eip.EIPProperties{
				VpcId:           eipData.VPCID,
				AssociationId:   eipData.AssociationID,
				AssociationType: eip.EIPProperties_AType(eip.EIPProperties_AType_value[eipData.AssociationType]),
				Resources: &eip.Resources{
					BillingItems: &eip.BillingItems{
						Bw: eipData.BW,
					},
					LimitRateItems: &eip.LimitRateItems{
						UpStreamBw:   eipData.UpStreamBW,
						DownStreamBw: eipData.DownStreamBW,
					},
				},
				Sku:                eipPoolData.Sku,
				DefaultSnat:        snat,
				AclEnabled:         len(acls) > 0,
				Acls:               acls,
				InternalEip:        eipData.InternalEIP,
				SnatDstCidrEnabled: eipData.SnatDstCidrEnabled,
			},
		},
		EipIp:     eipData.EIPIP,
		SnatCount: int32(len(dbSnatRules)),
		SnatInfo:  snatInfo,
		DnatCount: int32(dnatCount),
		VpcInfo: &eip.VPCStatus{
			Id:          eipData.VPCID,
			DisplayName: vpcData.DisplayName,
			Name:        vpcData.Name,
		},
	}

	logrus.Infof("Get EIP status: %s", request.EipName)
	return res, nil
}

/*
func getEipDefaultSnatMap(tenantID string) (map[string]bool, error) {
	eips, err := getEipCRs(tenantID)
	if err != nil {
		return nil, err
	}
	// key: eipCR.name, value: defaultSnat
	defaultSnatMap := make(map[string]bool)
	for _, eip := range eips {
		if eip.Annotations[rp.AnnoEipDefaultSnat] == rp.AnnoEipDefaultSnatEnabled {
			defaultSnatMap[eip.Name] = true
		} else {
			defaultSnatMap[eip.Name] = false
		}
	}

	// TODO delete
	logrus.Info(fmt.Sprintf("defaultSnatMap: %+v", defaultSnatMap))

	return defaultSnatMap, nil
}
*/

/*
func getEipCRs(tenantID string) ([]*eipv1.EIP, error) {
	// get all cr, do disable
	k8sRTMetrics := exporter.InitK8sRTMetrics("ListEIPs")
	// TODO: use informer to reduce api-server accessing
	eipList, err := rp.BosonProvider.BosonNetClient.EIPs().List(context.TODO(), metav1.ListOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}
	eips := []*eipv1.EIP{}
	for _, eip := range eipList.Items {
		if eip.Spec.TenantID != tenantID {
			continue
		}
		eips = append(eips, &eip)
	}
	return eips, nil
}
*/

func (EIPsServer) GetEIP(ctx context.Context, request *eip.GetEIPRequest) (res *eip.EIP, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics("", "EIP", "GetEIP")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(resourceprovider.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	res = &eip.EIP{
		Id:           eipData.ID,
		Name:         eipData.Name,
		DisplayName:  eipData.DisplayName,
		Description:  eipData.Description,
		Uid:          eipData.EIPID,
		ResourceType: eipData.ResourceType,
		CreatorId:    eipData.CreatorID,
		OwnerId:      eipData.OwnerID,
		TenantId:     eipData.TenantID,
		Zone:         eipData.Zone,
		State:        eip.EIP_State(eip.EIP_State_value[eipData.State]),
		Deleted:      eipData.Deleted,
		CreateTime:   timestamppb.New(eipData.CreateTime),
		UpdateTime:   timestamppb.New(eipData.UpdateTime),
		Properties: &eip.EIPProperties{
			VpcId:           eipData.VPCID,
			AssociationId:   eipData.AssociationID,
			AssociationType: eip.EIPProperties_AType(eip.EIPProperties_AType_value[eipData.AssociationType]),
			Resources: &eip.Resources{
				BillingItems: &eip.BillingItems{
					Bw: eipData.BW,
				},
				LimitRateItems: &eip.LimitRateItems{
					UpStreamBw:   eipData.UpStreamBW,
					DownStreamBw: eipData.DownStreamBW,
				},
			},
			InternalEip:        eipData.InternalEIP,
			SnatDstCidrEnabled: eipData.SnatDstCidrEnabled,
		},
	}

	return res, nil
}

func (EIPsServer) CreateEIP(ctx context.Context, request *eip.CreateEIPRequest) (res *eip.EIP, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.EipName, "EIP", "CreateEIP")

	defer func() {
		httpRTMetrics.Code = "404"
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("Create EIP: %s", request.EipName)
	return nil, errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
}

func (EIPsServer) UpdateEIP(ctx context.Context, request *eip.UpdateEIPRequest) (res *eip.EIP, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.EipName, "EIP", "UpdateEIP")

	defer func() {
		httpRTMetrics.Code = "404"
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("Update EIP: %s", request.EipName)
	return nil, errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
}

func (EIPsServer) DeleteEIP(ctx context.Context, request *eip.DeleteEIPRequest) (res *emptypb.Empty, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.EipName, "EIP", "DeleteEIP")

	defer func() {
		httpRTMetrics.Code = "404"
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("Delete EIP: %s", request.EipName)
	return nil, errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
}

func (EIPsServer) GetEIPMetrics(ctx context.Context, request *eip.GetEIPMetricsRequest) (res *eip.EIPMetrics, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "EIP", "GetEIPMetrics")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	tenantID := GetTenantIDFromHeader(ctx)
	logrus.Infof("GetEIPMetrics tenantID: %v", tenantID)
	if tenantID == "" {
		reason := "tenant ID is empty"
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	eipDatas, err := dao.ListEipsByTenantIDWithAllZone(resourceprovider.BosonProvider.Engine, tenantID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	exception := 0
	var bw int32 = 0
	for _, e := range eipDatas {
		if e.State == eip.EIPProperties_AType_name[int32(eip.EIP_FAILED)] || e.State == eip.EIPProperties_AType_name[int32(eip.EIP_EXPIRESTOPPED)] {
			exception++
		}
		bw += e.BW
	}

	return metrics.EIPMetrics(int32(len(eipDatas)), int32(exception), bw, GetHeaderAcceptLanguage(ctx)), nil
}

func eipTypeNameIsDnat(typeName string) bool {
	if typeName == eip.EIPProperties_AType_name[int32(eip.EIPProperties_NATGW)] ||
		typeName == eip.EIPProperties_AType_name[int32(eip.EIPProperties_NATGW_AND_BM)] {
		return true
	}
	return false
}

// EIPsServer 为 EIPsServer interface 的实现
type EIPDataServiceServer struct {
	*eip.UnimplementedEIPDataServiceServer
}

func (EIPDataServiceServer) ListSlbTypedAvailableEIPs(
	ctx context.Context, request *eip.ListSlbTypedAvailableEIPsRequest,
) (res *eip.ListSlbTypedAvailableEIPsResponse, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "EIP", "ListSlbTypedAvailableEIPs")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	eipDatas, err := getSlbTypedAvailableEIP()
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	eips := []*eip.EIP{}
	for _, eipData := range eipDatas {
		subscriptions, resourceGroups, err := getEipSubscriptionsResourceGroups(eipData.ID)
		if err != nil {
			return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
				"detail": err.Error(),
			})
		}

		if !(subscriptions == request.SubscriptionName &&
			resourceGroups == request.ResourceGroupName &&
			eipData.Zone == request.Zone) {
			continue
		}

		eips = append(eips, &eip.EIP{
			Id:           eipData.ID,
			Name:         eipData.Name,
			DisplayName:  eipData.DisplayName,
			Description:  eipData.Description,
			Uid:          eipData.EIPID,
			ResourceType: eipData.ResourceType,
			CreatorId:    eipData.CreatorID,
			OwnerId:      eipData.OwnerID,
			TenantId:     eipData.TenantID,
			Zone:         eipData.Zone,
			State:        eip.EIP_State(eip.EIP_State_value[eipData.State]),
			Deleted:      eipData.Deleted,
			CreateTime:   timestamppb.New(eipData.CreateTime),
			UpdateTime:   timestamppb.New(eipData.UpdateTime),
			Properties: &eip.EIPProperties{
				VpcId:           eipData.VPCID,
				AssociationId:   eipData.AssociationID,
				AssociationType: eip.EIPProperties_AType(eip.EIPProperties_AType_value[eipData.AssociationType]),
				Resources: &eip.Resources{
					BillingItems: &eip.BillingItems{
						Bw: eipData.BW,
					},
					LimitRateItems: &eip.LimitRateItems{
						UpStreamBw:   eipData.UpStreamBW,
						DownStreamBw: eipData.DownStreamBW,
					},
				},
				InternalEip:        eipData.InternalEIP,
				SnatDstCidrEnabled: eipData.SnatDstCidrEnabled,
			},
		})
	}

	res = &eip.ListSlbTypedAvailableEIPsResponse{
		Eips:      eips,
		TotalSize: int32(len(eips)),
	}

	logrus.Infof("List %d slb EIP in %s", len(eips), request.Zone)
	return res, nil
}

func getEipSubscriptionsResourceGroups(id string) (subscriptions string, resourceGroups string, err error) {
	re := regexp.MustCompile(`/subscriptions/([^/]+)/resourceGroups/([^/]+)`)
	matches := re.FindStringSubmatch(id)
	if len(matches) == 3 {
		return matches[1], matches[2], nil
	}

	return "", "", fmt.Errorf("get eip subscriptions and resourceGroups fail")
}

func getSlbTypedAvailableEIP() ([]*db.Eips, error) {
	engine := resourceprovider.BosonProvider.Engine

	slbs, err := dao.GetInternetSlbsRunningWithEip(engine)
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}
	// key: eip_id, value: bool
	eipUsedMap := make(map[string]bool)
	for _, slb := range slbs {
		eipUsedMap[slb.EipID] = true
	}

	eipsAvailable := []*db.Eips{}
	eips, err := dao.GetSlbTypedEIPs(engine)
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}
	for _, eip := range eips {
		if _, ok := eipUsedMap[eip.EIPID]; ok {
			continue
		}

		// unused
		eipsAvailable = append(eipsAvailable, eip)
	}

	return eipsAvailable, nil
}

func cloudAuditUpdateEipDstCidrError(
	ctx context.Context, resourceUID string, request *eip.UpdateEIPDstCIDRRequest, response interface{}, reason string,
) {
	resourceprovider.BosonProvider.Processor.CloudAuditSendEIP(
		ctx,
		types.CloudAuditOperationTypeUpdateDstCidr, types.CloudAuditResourceTypeEipDstCidr,
		types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceUID, request.EipName,
		resourceprovider.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditUpdateEipDstCidrSuccess(
	ctx context.Context, resourceUID string, request *eip.UpdateEIPDstCIDRRequest, response interface{},
) {
	resourceprovider.BosonProvider.Processor.CloudAuditSendEIP(
		ctx,
		types.CloudAuditOperationTypeUpdateDstCidr, types.CloudAuditResourceTypeEipDstCidr,
		types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceUID, request.EipName,
		resourceprovider.BosonProvider.Processor.CloudAuditSendMsg("update success"),
	)
}

func (EIPDataServiceServer) UpdateEIPDstCIDR(ctx context.Context, request *eip.UpdateEIPDstCIDRRequest) (res *eip.EIPDstCIDR, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics(request.EipName, "EIP", "UpdateEIPDstCIDR")
	resourceUID := request.EipName
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditUpdateEipDstCidrError(ctx, resourceUID, request, struct{}{}, err.Error())
		} else {
			cloudAuditUpdateEipDstCidrSuccess(ctx, resourceUID, request, struct{}{})
		}
	}()

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if len(request.EipDstCidr.DstCidrs) == 0 {
		err = fmt.Errorf("not support update internal eip dst cidr to empty")
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(resourceprovider.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorf("Get eip data by id=%s failed, err: %v", id, err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	vpcQuota, err := dao.GetVpcQuotaByVpcID(resourceprovider.BosonProvider.Engine, eipData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcQuotaNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	cidrs := strings.Split(request.EipDstCidr.DstCidrs, ",")
	if len(cidrs) > int(vpcQuota.InternalEIPDstCidrCount) {
		err = fmt.Errorf("internal eip len(dst_cidrs)=%d, exceed quota=%d", len(cidrs), vpcQuota.InternalEIPDstCidrCount)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}
	for _, cidr := range cidrs {
		if netIP := net.ParseIP(cidr); netIP == nil {
			if ip, ipNet, err := net.ParseCIDR(cidr); err != nil || !ip.Equal(ipNet.IP) {
				reason := fmt.Sprintf("element %s in cidrs=%s format error(not ip or cidr or network address for the given prefix), please correct it", cidr, cidrs)
				logrus.Errorln(reason)
				return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
					"detail": reason,
				})
			}
		}
	}

	// update eip cr annotation
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetEIP")
	eipCr, err := resourceprovider.BosonProvider.BosonNetClient.EIPs().Get(context.TODO(), request.EipName, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("Get eip cr failed, err: %v", err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	eipInternalConfAnno := eipCr.ObjectMeta.Annotations[resourceprovider.AnnoEIPInternalConf]
	if len(eipInternalConfAnno) == 0 {
		err = fmt.Errorf("not found internal eip conf in eip %s's cr, not support update dst cidr", request.EipName)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}
	var eipElasticNicConf ovn.ElasticNicConfElement
	err = json.Unmarshal([]byte(eipInternalConfAnno), &eipElasticNicConf)
	if err != nil {
		logrus.Errorf("Unmarshal internal eip conf annotation failed, err: %v", err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}
	oldDstCidrs := eipElasticNicConf.Info["snat_dst_cidr"]
	if oldDstCidrs == request.EipDstCidr.DstCidrs {
		logrus.Warnf("Internal Eip dst cidrs not change")
		return &eip.EIPDstCIDR{EipName: request.EipName, DstCidrs: request.EipDstCidr.DstCidrs}, nil
	}

	eipElasticNicConf.Info["snat_dst_cidr"] = request.EipDstCidr.DstCidrs
	eipENCB, err := json.Marshal(eipElasticNicConf)
	if err != nil {
		reason := fmt.Sprintf("Marshal eip elastic nic conf annotation failed, err: %v", err)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}
	eipCr.ObjectMeta.Annotations[resourceprovider.AnnoEIPInternalConf] = string(eipENCB)

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateEIP")
	_, err = resourceprovider.BosonProvider.BosonNetClient.EIPs().Update(context.TODO(), eipCr, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("Update internal eip cr's dst cidr failed, err: %v", err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	err = dao.UpdateSnatRuleDstCidrs(resourceprovider.BosonProvider.Engine, eipData.EIPID, request.EipDstCidr.DstCidrs)
	if err != nil {
		logrus.Errorf("Update snat rule db dst_cidrs=%s failed, eip_id=%s, err: %v", request.EipDstCidr.DstCidrs, eipData.EIPID, err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	var existAcl bool = false
	aclName := utils.GenAclNameForInternalEip(request.EipName)
	aclCr, err := resourceprovider.BosonProvider.BosonNetClient.VpcAcls(VpcAclNamespace).Get(context.Background(), aclName, metav1.GetOptions{})
	if err != nil && !k8s_errors.IsNotFound(err) {
		logrus.Errorf("Check vpc acl name=%s exist failed, err: %v", aclName, err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	if err == nil {
		existAcl = true
	}

	res = &eip.EIPDstCIDR{
		EipName:  request.EipName,
		DstCidrs: request.EipDstCidr.DstCidrs,
	}
	if !existAcl {
		// create vpc acl
		err = createAclForDstCidrs(request, aclName, request.EipDstCidr.DstCidrs, eipData)
		if err != nil {
			logrus.Errorf("Create acl %s for internal eip failed, dst_cidrs=%s, err: %v", aclName, request.EipDstCidr.DstCidrs, err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
		}
		return
	}

	// update vpc acl
	err = updateAclForDstCidrs(aclCr, request.EipDstCidr.DstCidrs)
	if err != nil {
		logrus.Errorf("Update acl %s for internal eip failed, dst_cidrs=%s, err: %v", aclName, request.EipDstCidr.DstCidrs, err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	// add cloudEdit
	return
}

func createAclForDstCidrs(request *eip.UpdateEIPDstCIDRRequest, aclName string, dstCidrs string, eipData *db.Eips) (err error) {
	logrus.Infof("Create ACL=%s for Internal EIP Dst CIDRs: %s", aclName, dstCidrs)

	vpcData, err := dao.GetVpc(resourceprovider.BosonProvider.Engine, eipData.VPCID)
	if err != nil {
		logrus.Errorf("Get vpc data by vpc id=%s failed, err: %v", eipData.VPCID, err)
		return err
	}

	snatData, err := dao.GetSnatRuleByInternalEipID(resourceprovider.BosonProvider.Engine, eipData.EIPID)
	if err != nil {
		logrus.Errorf("Get snat rule of internal eip failed, eip id=%s, err=%v", eipData.EIPID, err)
		return err
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetVPC")
	vpcCr, err := resourceprovider.BosonProvider.KubeovnClient.Vpcs().Get(context.TODO(), vpcData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("Get vpc cr failed, vpc name=%s, err: %v", vpcData.Name, err)
		return err
	}

	// list all vpc acls cr
	k8sRTMetrics = exporter.InitK8sRTMetrics("ListVpcAcls")
	aclCrList, err := resourceprovider.BosonProvider.BosonNetClient.VpcAcls(VpcAclNamespace).List(
		context.TODO(),
		metav1.ListOptions{LabelSelector: fmt.Sprintf("%s=%s", VpcNameLabel, vpcData.Name)})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("List acl cr failed, vpc name=%s, err: %v", vpcCr.GetName(), err)
		return err
	}

	usedPriority := []int{}
	for _, aclCr := range aclCrList.Items {
		usedPriority = append(usedPriority, aclCr.Spec.Priority)
	}

	allocPriority, err := AllocatePriority(vpc.ACLProperties_SYSTEM_SERVICE_ACL, 5, usedPriority)
	if err != nil {
		logrus.Errorf("Create acl cr name=%s failed, err=%v", aclName, err)
		return err
	}

	eipNicName, err := utils.GenEipNicName(request.EipName)
	if err != nil {
		logrus.Errorf("Generate eip nic from eip name failed, eip_name=%s, err: %v", request.EipName, err)
		return err
	}

	// create ACL CR
	acl := &networkv1.VpcAcl{
		TypeMeta: metav1.TypeMeta{
			Kind:       VPCACLKind,
			APIVersion: networkv1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      aclName,
			Namespace: VpcAclNamespace,
			Labels: map[string]string{
				VpcAclResourceLabel: VpcAclResourceServiceValue,
				VpcAclServiceLabel:  snatData.Name,
				VpcNameLabel:        vpcData.Name,
			},
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpcCr, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    resourceprovider.VPCKind,
				}),
			},
			Annotations: map[string]string{
				resourceprovider.AnnoRegion: db.Region,
				resourceprovider.AnnoAz:     db.AZ,
			},
		},

		Spec: networkv1.VpcAclSpec{
			AclID:     aclName,
			Action:    strings.ToLower(vpc.ACLProperties_ALLOW.String()),
			Dest:      dstCidrs,
			Priority:  allocPriority,
			Direction: "egress",
			ExtendNic: eipNicName,
			Vpc:       vpcData.Name,
		},
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("CreateACL")
	_, err = resourceprovider.BosonProvider.BosonNetClient.VpcAcls(VpcAclNamespace).Create(context.Background(), acl, metav1.CreateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	logrus.Infof("Created ACL for Internal eip success, acl name=%s", aclName)
	return nil
}

func updateAclForDstCidrs(aclCr *networkv1.VpcAcl, dstCidrs string) (err error) {
	logrus.Infof("Update acl cr=%s for Internal EIP Dst CIDRs: %s", aclCr.Name, dstCidrs)

	// update ACL CR
	aclCr.Spec.Dest = dstCidrs
	k8sRTMetrics := exporter.InitK8sRTMetrics("UpdateVpcAcl")
	_, err = resourceprovider.BosonProvider.BosonNetClient.VpcAcls(VpcAclNamespace).Update(context.TODO(), aclCr, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("Update acl cr %s failed, err: %v", aclCr.Name, err)
		return err
	}

	return
}
