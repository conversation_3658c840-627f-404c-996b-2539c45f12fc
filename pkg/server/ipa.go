package server

import (
	"context"
	"fmt"
	"net"
	"strings"
	"time"

	"github.com/allisson/go-pglock/v3"
	"github.com/patrickmn/go-cache"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	empty "google.golang.org/protobuf/types/known/emptypb"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"

	ipaCRv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/ipa/v1"
	ipav1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/ipa/v1"
	vpcv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/conv"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	utilnet "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils/network"
)

const (
	VLanMTU    = 1500
	VXLanMTU   = 1450
	retryCount = 10
)

type IPAServer struct {
	*ipa.UnimplementedIPAsServer
	// db engine
	e *db.EngineWrapper
	c *cache.Cache
	l *utilnet.LockByKey
}

func NewIPAServer() *IPAServer {

	return &IPAServer{
		e: rp.BosonProvider.Engine,
		// NOTE(litianqing)
		// do not use certain size cache, if we need that, please rewrite here.
		// 请注意，我们没有使用固定size的cache，比如100个item，因为我们的subnet数量并不多。
		// 后续在有1000级别的subnet的时候，可以增加限制，来减少provider的内存使用量
		c: cache.New(5*time.Minute, 10*time.Minute),
		l: &utilnet.LockByKey{},
	}

}

const IPAKind = "IPA"

func (s *IPAServer) ListIPAs(ctx context.Context, request *ipav1.ListIPAsRequest) (res *ipav1.ListIPAsResponse, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics("", "IPA", "ListIPAs")

	defer func() {
		httpRTMetrics.Code = "404"
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("List IPA in %s", request.Zone)
	return nil, errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
}

func (s *IPAServer) GetIPA(ctx context.Context, request *ipav1.IPAUIDRequest) (res *ipav1.IPA, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics("", "IPA", "GetIPA")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	dbRTMetrics := exporter.InitDBRTMetrics("GetIPA")

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	ipa, err := dao.GetIPA(s.e, request.Id)
	dbRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("Get IPA %s error: %s", request.Id, err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.IpaNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	logrus.Infof("Get IPA: %s", ipa.Name)
	return conv.IpasDbToApi(ipa), nil
}

func (s *IPAServer) getSubnet(ctx context.Context, subnetId string) (*db.Subnets, error) {
	var sn *db.Subnets
	var err error
	unlock := s.l.Lock(subnetId)
	defer unlock()

	snInCache, ok := s.c.Get(subnetId)
	if !ok { // if not in cache
		// get from db
		sn, err = dao.GetSubnet(s.e, subnetId)
		if err != nil {
			logrus.Errorln(err)
			return nil, err
		}
		// put it into subnet
		if err = s.c.Add(subnetId, sn, cache.DefaultExpiration); err != nil {
			logrus.Errorln(err)
			return nil, err
		}
		logrus.Infof("get subnet(id:%s) from db done", subnetId)
	} else {
		// change type from interface {}
		if sn, ok = snInCache.(*db.Subnets); !ok {
			logrus.Errorln(fmt.Errorf("subnet(id:%s) in cache type is not wanted", subnetId))
			return nil, err
		}
		logrus.Infof("get subnet(id:%s) from cache done, cache total size is %d", subnetId, s.c.ItemCount())
	}

	return sn, nil
}

func (s *IPAServer) getSubnetCr(ctx context.Context, snName string, ipa *db.Ipas) (*ipaCRv1.Subnet, error) {
	var snCR *ipaCRv1.Subnet
	var err error

	unlock := s.l.Lock(snName)
	defer unlock()

	snCRInCache, ok := s.c.Get(snName)
	if !ok {
		snCR, err = rp.BosonProvider.BosonNetClient.Subnets().Get(ctx, snName, metav1.GetOptions{})
		if err != nil {
			logrus.Errorf("occurs error: %v during create CR for ipa [%s,%s] from subnet %s", err, ipa.IpaID, ipa.IP, snName)
			return nil, err
		}
		if err := s.c.Add(snName, snCR, cache.DefaultExpiration); err != nil {
			logrus.Errorf("put subnet cr(name:%s) into cache error %s", snName, err.Error())
			return nil, err
		}
		logrus.Infof("get subnet cr (name: %s) from k8s done", snName)
	} else {
		if snCR, ok = snCRInCache.(*ipaCRv1.Subnet); !ok {
			logrus.Errorln(fmt.Errorf("subnet cr (name:%s) in cache type is not wanted", snName))
			return nil, err
		} else {
			logrus.Infof("get subnet cr(name: %s) from cache done, cache total size is %d", snName, s.c.ItemCount())
		}
	}

	return snCR, nil
}

// NOTE(litianqing)
// 这里只有pg分布式锁的逻辑，可以测试极简逻辑的耗时
// func (s *IPAServer) CreateIPA(ctx context.Context, request *ipav1.CreateIPARequest) (res *ipav1.IPA, err error) {
// 	seed := fmt.Sprintf("%s-%s", db.AZ, request.SubnetId)
// 	pglockId := utils.HashStringToInt32(seed)
// 	logrus.Infof("Init PG Lock ID %d for %s (az-subnetid)", pglockId, seed)
// 	dlock, err := pglock.NewLock(ctx, int64(pglockId), s.e.DB().DB)
// 	if err != nil {
// 		logrus.Errorln(err)
// 		return nil, errors.NewHiggsError(ctx, codes.Internal, "serviceError", "boson")
// 	}
// 	if err := dlock.WaitAndLock(ctx); err != nil {
// 		logrus.Errorln(err)
// 		return nil, errors.NewHiggsError(ctx, codes.Internal, "serviceError", "boson")
// 	}

// 	ipa := &ipav1.IPA{
// 		Id:           utils.UUIDGen(),
// 		Name:         "test",
// 		DisplayName:  "test",
// 		Description:  "test",
// 		ResourceType: "xx",
// 		CreatorId:    "test",
// 		OwnerId:      "test",
// 		TenantId:     "test",
// 		Zone:         "xx",
// 		Properties: &ipav1.IPAProperties{
// 			SubnetId: request.SubnetId,
// 			Protocol: "IPv4",
// 			Ip:       "*********",
// 			Mac:      "10:70:fd:6d:e8:83",
// 			GwIp:     "********",
// 			Cidr:     "********/24",
// 			Mtu:      1500,
// 			Ipmi:     "*********",
// 		},
// 		State:      0,
// 		Deleted:    false,
// 		CreateTime: timestamppb.New(time.Now()),
// 		UpdateTime: timestamppb.New(time.Now()),
// 	}

// 	sn, err := s.getSubnet(ctx, request.SubnetId)
// 	if err != nil {
// 		logrus.Errorf("get subnet from db/cache (id:%s) error %s", request.SubnetId, err.Error())
// 		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InvalidSubnetProperties, errors.Domain)
// 	}
// 	logrus.Infof("get from db done %s", sn.SubnetID)

// 	// snCR, err := rp.BosonProvider.BosonNetClient.Subnets().Get(ctx, sn.Name, metav1.GetOptions{})
// 	// if err != nil {
// 	// 	logrus.Errorf("occurs error: %v during create CR for ipafrom subnet %s", err, sn.Name)
// 	// 	return nil, err
// 	// }
// 	// logrus.Infof("get cr from k8s done %s", snCR.Name)

// 	logrus.Infof("create ipa %s", ipa.Id)
// 	_ = dlock.Unlock(ctx)
// 	return ipa, nil
// }

func (s *IPAServer) CreateIPA(ctx context.Context, request *ipav1.CreateIPARequest) (res *ipav1.IPA, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "IPA", "CreateIPA")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("Create IPA with %v", request)

	if request.Protocol != utilnet.ProtocolIPv4 {
		err := fmt.Errorf("only support ipv4")
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	dbRTMetrics := exporter.InitDBRTMetrics("GetSubnet")
	sn, err := s.getSubnet(ctx, request.SubnetId)
	dbRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("get subnet from db/cache (id:%s) error %s", request.SubnetId, err.Error())
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	}

	if sn.Provider != vpcv1.SubnetProperties_CONTROLLER.String() {
		err := fmt.Errorf("invalidate subnet %s's provider %s ", sn.SubnetID, sn.Provider)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	_, ipnet, err := net.ParseCIDR(sn.CIDR)
	if err != nil {
		reason := fmt.Sprintf("Parse subnet %s's cidr %s error: %v", sn.SubnetID, sn.CIDR, err)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if len(request.Ip) > 0 {
		if contains := ipnet.Contains(net.ParseIP(request.Ip)); !contains {
			err := fmt.Errorf("The IP %s does not match the subnet %s's CIDR %s", request.Ip, sn.SubnetID, sn.CIDR)
			logrus.Error(err)
			return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
				"detail": err.Error(),
			})
		}
	}

	excludeIPs := utilnet.ExpandExcludeIPs(strings.Split(sn.ReservedIPs, ","), sn.CIDR)
	v4ExcludeIPs, _ := utilnet.SplitIpsByProtocol(excludeIPs)
	logrus.Infof("The IPv4 exclude IPs %s", v4ExcludeIPs)

	timeOut := resourceprovider.BosonProvider.RpConfig.Boson.IPAPgLockTimeOut
	logrus.Infof("pg lock timeout is %d second", timeOut)

	seed := fmt.Sprintf("%s-%s", db.AZ, request.SubnetId)
	pglockId := utils.HashStringToInt32(seed)
	logrus.Infof("subName: %s,init PG Lock ID %d for %s (az-subnetid)", request.SubscriptionName, pglockId, seed)

	var dlock pglock.Lock
	childCtx, cancel := context.WithTimeout(ctx, time.Duration(timeOut)*time.Second)
	dbLockRTMetrics := exporter.InitDBRTMetrics("GetSubnetLock")
	func() {
		defer cancel()

		dlock, err = pglock.NewLock(childCtx, int64(pglockId), s.e.DB().DB)
		if err != nil {
			logrus.Errorf("subName: %s, new pglock for pgLockID %d failed, error: %s", request.SubscriptionName, pglockId, err.Error())
			return
		}
		logrus.Infof("subName: %s, new pglock for pgLockID %d success", request.SubscriptionName, pglockId)

		err = dlock.WaitAndLock(childCtx)
		if err != nil {
			logrus.Errorf("subName: %s, get pglock for pgLockID %d failed, error %s", request.SubscriptionName, pglockId, err.Error())
			return
		}
	}()

	dbLockRTMetrics.ObserveDurationAndLog()
	if childCtx.Err() == context.DeadlineExceeded {
		logrus.Errorf("subName: %s, get pglock for pgLockID %d timeout", request.SubscriptionName, pglockId)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	} else if err != nil {
		logrus.Errorf("subName: %s, get pglock for pgLockID %d error %s", request.SubscriptionName, pglockId, err.Error())
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	} else {
		logrus.Infof("subName: %s, get pglock for pgLockID %d success", request.SubscriptionName, pglockId)
	}
	defer dlock.Close()

	// If the ip is empty, then allocate it from the subnet
	if len(request.Ip) == 0 {
		ip, err := s.allocateIPv4Address(sn.SubnetID, sn.CIDR, sn.GatewayIP, v4ExcludeIPs)
		if err != nil {
			_ = dlock.Unlock(ctx)
			logrus.Error(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.AllocateIPFailed, errors.Domain, nil, nil)
		}
		logrus.Infof("The allocated IP for subnet %s is %s", sn.SubnetID, ip)
		request.Ip = ip
	} else {
		// Check the IP existence
		dbRTMetrics = exporter.InitDBRTMetrics("CheckIPA")
		exist, err := dao.Exist(s.e, sn.SubnetID, request.Ip)
		dbRTMetrics.ObserveDurationAndLog()
		if err != nil {
			_ = dlock.Unlock(ctx)
			err := fmt.Errorf("Query IPAs with subnetID: %s IP: %s error %s", sn.SubnetID, request.Ip, err)
			logrus.Error(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}

		if exist {
			_ = dlock.Unlock(ctx)
			err := fmt.Errorf("The IP %s already exist in the subnet %s", request.Ip, sn.SubnetID)
			logrus.Error(err)
			return nil, errors.NewHiggsError(ctx, codes.AlreadyExists, errors.IpaExist, errors.Domain, nil, nil)
		}
	}

	// Generate Mac address
	// if len(request.Mac) == 0 {
	// 	request.Mac = utilnet.NewMac().String()
	// 	logrus.Infof("Generate mac %s for IP %s", request.Mac, request.Ip)
	// }

	mtu := VXLanMTU
	if sn.NetworkType == vpcv1.SubnetProperties_VLAN.String() {
		mtu = VLanMTU
	}

	ipaID := utils.UUIDGen()
	ipaName := "ip-" + utils.Substring(sn.TenantID, 0, 20) + "-" + ipaID[:8]
	description := "vpc: " + sn.VPCID
	ipa := &db.Ipas{
		IpaID:       ipaID,
		DisplayName: request.Ip,
		Description: description,
		SubnetID:    request.SubnetId,
		Protocol:    request.Protocol,
		IP:          request.Ip,
		//MAC:          request.Mac,
		CIDR: sn.CIDR,
		MTU:  mtu,
		//IPMI:         request.Ipa.Properties.Ipmi,
		GatewayIP:    sn.GatewayIP,
		State:        ipav1.IPA_CREATED.String(),
		ID:           utils.GenRMID(request.SubscriptionName, request.ResourceGroupName, request.Zone, "ipa", ipaName),
		Name:         ipaName,
		ResourceType: "network.vpc.v1.ipa",
		Zone:         request.Zone,
		CreatorID:    sn.CreatorID,
		OwnerID:      sn.OwnerID,
		TenantID:     sn.TenantID,
	}

	dbRTMetrics = exporter.InitDBRTMetrics("AddIPA")

	if err := dao.AddIPA(s.e, ipa); err != nil {
		_ = dlock.Unlock(ctx)
		dbRTMetrics.ObserveDurationAndLog()
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	dbRTMetrics.ObserveDurationAndLog()
	_ = dlock.Unlock(ctx)
	logrus.Infof("subName: %s, unlock pglock for pgLockID %d", request.SubscriptionName, pglockId)

	dbRTMetrics = exporter.InitDBRTMetrics("GetIPA")
	ipa, err = dao.GetIPA(s.e, ipa.IpaID)
	dbRTMetrics.ObserveDurationAndLog()
	if err != nil {
		// not rollback, to fix the db manually
		logrus.Errorf("occurs error: %v during create ipa [%s,%s] from subnet %s", err, ipa.IpaID, ipa.IP, sn.SubnetID)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSubnet")
	// create IPA CR
	snCR, err := s.getSubnetCr(ctx, sn.Name, ipa)
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("get subnet cr(name: %s) from k8s error %s", sn.Name, err.Error())
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	ipaCR := &ipaCRv1.IPA{
		TypeMeta: metav1.TypeMeta{
			Kind:       IPAKind,
			APIVersion: ipaCRv1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: ipa.Name,
			Annotations: map[string]string{
				rp.AnnoRegion: db.Region,
				rp.AnnoAz:     db.AZ,
			},
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(snCR, schema.GroupVersionKind{
					Group:   metav1.SchemeGroupVersion.Group,
					Version: metav1.SchemeGroupVersion.Version,
					Kind:    rp.SubnetKind,
				}),
			},
		},

		Spec: ipaCRv1.IPASpec{
			Subnet:   snCR.Name,
			Protocol: ipa.Protocol,
			IP:       ipa.IP,
			GwIP:     ipa.GatewayIP,
			CIDR:     ipa.CIDR,
			MTU:      ipa.MTU,
			IPMI:     ipa.IPMI,
			MAC:      ipa.MAC,
		},
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("CreateIPA")
	_, err = rp.BosonProvider.BosonNetClient.IPAs().Create(context.Background(), ipaCR, metav1.CreateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		// rollback
		dbRTMetrics := exporter.InitDBRTMetrics("UpdateIPA")
		if err := dao.DeleteIPA(s.e, ipa.IpaID); err != nil {
			dbRTMetrics.ObserveDurationAndLog()
			logrus.Errorf("rollback to delete IPA %s error: %s", ipa.IpaID, err)
		}
		dbRTMetrics.ObserveDurationAndLog()
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	return conv.IpasDbToApi(ipa), nil
}

func (s *IPAServer) DeleteIPA(ctx context.Context, request *ipav1.IPAUIDRequest) (res *empty.Empty, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics("", "IPA", "DeleteIPA")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	dbRTMetrics := exporter.InitDBRTMetrics("GetIPA")
	logrus.Infof("Delete IPA with %v", request)

	ipa, err := dao.GetIPA(s.e, request.Id)
	dbRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("Delete IPA %s error: %s", request.Id, err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.IpaNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if ipa.State != ipav1.IPA_CREATED.String() && ipa.State != ipav1.IPA_FAILED.String() {
		err := fmt.Errorf("Can't delete the %s IPA that is being used by %s", ipa.State, ipa.IPMI)
		logrus.Error(err)
		return nil, errors.NewHiggsError(ctx, codes.Unavailable, errors.IpaInUse, errors.Domain, nil, nil)
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("DeleteIPA")

	// delete CR
	if err := rp.BosonProvider.BosonNetClient.IPAs().Delete(ctx, ipa.Name, metav1.DeleteOptions{}); err != nil {
		logrus.Error(err)
		k8sRTMetrics.ObserveDurationAndLog()
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}
	k8sRTMetrics.ObserveDurationAndLog()

	dbRTMetrics = exporter.InitDBRTMetrics("UpdateIPA")
	if err := dao.DeleteIPA(s.e, request.Id); err != nil {
		logrus.Errorf("Delete IPA %s error: %s", request.Id, err)
		dbRTMetrics.ObserveDurationAndLog()
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	dbRTMetrics.ObserveDurationAndLog()
	return &empty.Empty{}, nil
}

func (s *IPAServer) BindIPA(ctx context.Context, request *ipav1.BindingIPARequest) (res *empty.Empty, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics("", "IPA", "BindIPA")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	dbRTMetrics := exporter.InitDBRTMetrics("GetIPA")
	logrus.Infof("Bind IPA with %v", request)

	ipa, err := dao.GetIPA(s.e, request.Id)
	dbRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("Get IPA %s error: %s while binding", request.Id, err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.IpaNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	/*support repeated bind  */
	if ipa.State == ipav1.IPA_BINDING.String() || ipa.State == ipav1.IPA_ACTIVE.String() || ipa.State == ipav1.IPA_PROVISION.String() {
		ipaIp := net.ParseIP(ipa.IPMI)
		ipmi := net.ParseIP(request.Ipmi)
		if strings.EqualFold(ipa.MAC, request.Mac) && ipaIp.Equal(ipmi) {
			logrus.Debugf("repeat to bind IPa %s MAC: %s bms IPMI: %s", ipa.Name, request.Mac, request.Ipmi)
			return &empty.Empty{}, nil
		}
	}

	if ipa.State != ipav1.IPA_CREATED.String() {
		err := fmt.Errorf("Can't bind the %s IPA %s with bms %s", ipa.State, ipa.Name, request.Ipmi)
		logrus.Error(err)
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.IpaBoundFailed, errors.Domain, nil, nil)
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetIPA")
	oipa := db.Ipas{}
	has, err := s.e.Where("ipmi = ? and mac = ? and ipa_id != ? and deleted = false", request.Ipmi, request.Mac, ipa.IpaID).Get(&oipa)
	if err != nil {
		logrus.Errorf("Get IPA with ipmi %s and mac %s error: %s", request.Ipmi, request.Mac, err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if has {
		err := fmt.Errorf("Can't bind ipa %s with  the mac %s of ipmi %s that has been bond with other ipas %s", ipa.IpaID, request.Mac, request.Ipmi, oipa.Name)
		logrus.Error(err)
		return nil, errors.NewHiggsError(ctx, codes.AlreadyExists, errors.IpaHasBeenBound, errors.Domain, nil, nil)
	}

	ipaCR, err := rp.BosonProvider.BosonNetClient.IPAs().Get(ctx, ipa.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("get IPa %s CR error: %s while binding", ipa.Name, err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	if request.Mode != "disable" {
		ipa.Mode = "enable"
	} else {
		ipa.Mode = "disable"
	}
	ipa.MAC = request.Mac
	ipa.IPMI = request.Ipmi
	dbRTMetrics = exporter.InitDBRTMetrics("UpdateIPA")
	if err := dao.UpdateIPA(s.e, ipa); err != nil {
		logrus.Errorf("Update IPA %s error: %s", request.Id, err)
		dbRTMetrics.ObserveDurationAndLog()
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	dbRTMetrics.ObserveDurationAndLog()

	for i := 1; i <= retryCount; i++ {
		newCR := ipaCR.DeepCopy()
		newCR.Spec.IPMI = ipa.IPMI
		newCR.Spec.MAC = ipa.MAC
		newCR.Spec.Bind = true

		if newCR.ObjectMeta.Annotations == nil {
			newCR.ObjectMeta.Annotations = make(map[string]string)
		}
		newCR.ObjectMeta.Annotations[rp.AnnoBindingIPAMode] = ipa.Mode

		k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateIPA")

		if _, err := rp.BosonProvider.BosonNetClient.IPAs().Update(ctx, newCR, metav1.UpdateOptions{}); err != nil {
			k8sRTMetrics.ObserveDurationAndLog()
			if i < retryCount {

				k8sRTMetrics = exporter.InitK8sRTMetrics("GetIPA")

				ipaCR, err = rp.BosonProvider.BosonNetClient.IPAs().Get(ctx, ipa.Name, metav1.GetOptions{})
				k8sRTMetrics.ObserveDurationAndLog()
				if err != nil {
					logrus.Errorf("retry get IPa %s CR during bind with  error: %s", ipa.Name, err)
					return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
				}
			} else {
				logrus.Errorf("bind IPA CR %s to IPMI: %s with error: %s", newCR.Name, ipa.IPMI, err)
				return nil, errors.NewHiggsError(ctx, codes.Internal, errors.IpaBoundFailed, errors.Domain, nil, nil)
			}
		} else {
			k8sRTMetrics.ObserveDurationAndLog()
			return &empty.Empty{}, nil
		}
	}

	return &empty.Empty{}, nil
}

func (s *IPAServer) UpdateIPA(ctx context.Context, request *ipav1.UpdateIPARequest) (res *empty.Empty, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "IPA", "UpdateIPA")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	if request.Id == "" && request.OriginalMac == "" {
		err := fmt.Errorf("invalid request with empty id and mac")
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	// fetch ipa db
	var ipa *db.Ipas
	if request.Id != "" {
		ipa, err = dao.GetIPA(s.e, request.Id)
		if err != nil {
			logrus.Errorf("Get IPA %s error(%s) while updating", request.Id, err)
			if strings.Contains(err.Error(), "not found") {
				return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.IpaNotFound, errors.Domain, nil, nil)
			}
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
	} else if request.OriginalMac != "" {
		var has bool
		ipa, has, err = dao.GetIPAByMac(s.e, request.OriginalMac)
		if err != nil {
			logrus.Errorf("Get IPA %s error(%s) while updating", request.Id, err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		if !has {
			logrus.Infof("no IPA with %v", request.OriginalMac)
			return &empty.Empty{}, nil
		}
	}

	if ipa.State != ipav1.IPA_ACTIVE.String() {
		err := fmt.Errorf("Can't update the %s IPA %s", ipa.State, ipa.Name)
		logrus.Error(err)
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	// fetch ipa cr
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetIPA")
	ipaCR, err := rp.BosonProvider.BosonNetClient.IPAs().Get(ctx, ipa.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("get IPa %s CR error: %s", ipa.Name, err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	// update ipa db
	ipa.MAC = request.NewMac
	err = dao.UpdateIPA(s.e, ipa)
	if err != nil {
		logrus.Errorf("update IPa %s error: %s", ipa.Name, err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	// update ipa cr
	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateIPA")
	for i := 0; i < retryCount; i++ {
		ipaCR.Spec.MAC = request.NewMac
		if _, err := rp.BosonProvider.BosonNetClient.IPAs().Update(ctx, ipaCR, metav1.UpdateOptions{}); err != nil {
			k8sRTMetrics.ObserveDurationAndLog()
			if i < retryCount {
				k8sRTMetrics := exporter.InitK8sRTMetrics("GetIPA")

				ipaCR, err = rp.BosonProvider.BosonNetClient.IPAs().Get(ctx, ipa.Name, metav1.GetOptions{})
				k8sRTMetrics.ObserveDurationAndLog()
				if err != nil {
					logrus.Errorf("retry get IPa %s CR error: %s", ipa.Name, err)
					return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
				}
			} else {
				logrus.Errorf("update IPA CR %s with error: %s", ipaCR.Name, err)
				return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
			}
		} else {
			k8sRTMetrics.ObserveDurationAndLog()
		}
	}

	logrus.Infof("Update IPA with %v", request)
	return &empty.Empty{}, nil
}

func (s *IPAServer) UnbindIPA(ctx context.Context, request *ipav1.BindingIPARequest) (res *empty.Empty, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics("", "IPA", "UnbindIPA")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	dbRTMetrics := exporter.InitDBRTMetrics("GetIPA")
	logrus.Infof("unBind IPA with %v", request)

	ipa, err := dao.GetIPA(s.e, request.Id)
	dbRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("Get IPA %s error(%s) while unbinding", request.Id, err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.IpaNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	ipaIp := net.ParseIP(ipa.IPMI)
	ipmi := net.ParseIP(request.Ipmi)

	/*support repeated unbind  */
	if ipa.State == ipav1.IPA_UNBINDING.String() || ipa.State == ipav1.IPA_CREATED.String() {
		if strings.EqualFold(ipa.MAC, request.Mac) && ipaIp.Equal(ipmi) {
			logrus.Debugf("repeat to unbind IPa %s MAC: %s IPMI: %s", ipa.Name, request.Mac, request.Ipmi)
			return &empty.Empty{}, nil
		}
	}

	if ipa.State != ipav1.IPA_ACTIVE.String() {
		err := fmt.Errorf("Can't unbind the %s IPA %s", ipa.State, ipa.Name)
		logrus.Error(err)
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.IpaUnboundFailed, errors.Domain, nil, nil)
	}

	if !strings.EqualFold(ipa.MAC, request.Mac) || !ipaIp.Equal(ipmi) {
		err := status.Errorf(codes.InvalidArgument, "invalid parameter to unbind IPa %s with MAC[ipa %s: request %s] IPMI[ipa %s: request %s]", ipa.Name, ipa.MAC, request.Mac, ipa.IPMI, request.Ipmi)
		logrus.Error(err.Error())
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetIPA")

	ipaCR, err := rp.BosonProvider.BosonNetClient.IPAs().Get(ctx, ipa.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("get IPa %s CR error: %s", ipa.Name, err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	// Unbind
	for i := 1; i <= retryCount; i++ {
		newCR := ipaCR.DeepCopy()
		newCR.Spec.Bind = false

		k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateIPA")

		if _, err := rp.BosonProvider.BosonNetClient.IPAs().Update(ctx, newCR, metav1.UpdateOptions{}); err != nil {
			k8sRTMetrics.ObserveDurationAndLog()
			if i < retryCount {

				k8sRTMetrics := exporter.InitK8sRTMetrics("GetIPA")

				ipaCR, err = rp.BosonProvider.BosonNetClient.IPAs().Get(ctx, ipa.Name, metav1.GetOptions{})
				k8sRTMetrics.ObserveDurationAndLog()
				if err != nil {
					logrus.Errorf("retry get IPa %s CR error: %s", ipa.Name, err)
					return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
				}
			} else {
				logrus.Errorf("unbind IPA CR %s to IPMI: %s with error: %s", newCR.Name, ipa.IPMI, err)
				return nil, errors.NewHiggsError(ctx, codes.Internal, errors.IpaUnboundFailed, errors.Domain, nil, nil)
			}
		} else {
			k8sRTMetrics.ObserveDurationAndLog()
			return &empty.Empty{}, nil
		}
	}

	return &empty.Empty{}, nil
}

func (s *IPAServer) allocateIPv4Address(subnetID, cidr, gw string, reservedIPs []string) (string, error) {

	dbRTMetrics := exporter.InitDBRTMetrics("ListSubnets")

	ips, err := dao.ListSubnetIPs(s.e, subnetID)
	dbRTMetrics.ObserveDurationAndLog()
	if err != nil {
		err := fmt.Errorf("List IPs from the subnet %s error: %s", subnetID, err)
		logrus.Errorf("No IPA in the subnet %s: %s", subnetID, err)
		return "", err
	}

	m := map[string]bool{}
	for _, v := range ips {
		m[v] = true
	}

	firstIP, err := utilnet.FirstIP(cidr)
	if err != nil {
		return "", err
	}
	lastIP, err := utilnet.LastIP(cidr)
	if err != nil {
		return "", err
	}
	logrus.Infof("CIDR %s firstIP: %s lastIP: %s gw: %s", cidr, firstIP, lastIP, gw)

	for ip := firstIP; utilnet.CIDRContainIP(cidr, ip) && ip != string(utilnet.IP(lastIP).Add(1)); ip = string(utilnet.IP(ip).Add(1)) {
		if IsReservedIP(ip, reservedIPs) {
			continue
		}
		if strings.EqualFold(ip, gw) {
			continue
		}

		if _, ok := m[ip]; !ok {
			return ip, nil
		}
	}

	return "", fmt.Errorf("no ip address in cidr %s subnet %s", cidr, subnetID)
}

func IsReservedIP(ip string, reservedIPs []string) bool {
	for _, ex := range reservedIPs {
		if utilnet.ContainsIPs(ex, ip) {
			return true
		}
	}

	return false
}
