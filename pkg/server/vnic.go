package server

import (
	"context"
	"fmt"
	"time"

	v1api "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	v1 "github.com/kubeovn/kube-ovn/pkg/client/clientset/versioned/typed/kubeovn/v1"
	v1lister "github.com/kubeovn/kube-ovn/pkg/client/listers/kubeovn/v1"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/selection"

	kubeovninformer "github.com/kubeovn/kube-ovn/pkg/client/informers/externalversions"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vnic/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

const (
	subnetLabel     = "boson.sensetime.com/subnet"
	vnicAnnotation  = "boson.sensetime.com/vnic"
	ipReservedLabel = "ovn.kubernetes.io/ip_reserved"
	vnicIdLabel     = "boson.sensetime.com/vnic_id"
	bosonNamespace  = "plat-boson-service"
)

// ACLsServer
type VNICsServer struct {
	vnic.UnimplementedVNicsServer
	ipLister     v1lister.VipLister
	subnetLister v1lister.SubnetLister
	client       v1.KubeovnV1Client
}

func convertVipName(subnet, vip string) string {
	return fmt.Sprintf("%s.%s", subnet, vip)
}

func ipReserved(ip *v1api.Vip) bool {
	return ip.Labels[ipReservedLabel] != ""
}

func NewVNICServer() *VNICsServer {
	factory := kubeovninformer.NewSharedInformerFactoryWithOptions(resourceprovider.BosonProvider.KubeovnClientset, 0)
	ipInformer := factory.Kubeovn().V1().Vips()
	subnetInformer := factory.Kubeovn().V1().Subnets()
	ipLister := ipInformer.Lister()
	subnetLister := subnetInformer.Lister()

	stop := make(chan struct{})
	factory.Start(stop)

	return &VNICsServer{
		ipLister:     ipLister,
		subnetLister: subnetLister,
		client:       *resourceprovider.BosonProvider.KubeovnClient,
	}
}

func (s *VNICsServer) ListVNics(ctx context.Context, request *vnic.ListVNicRequest) (res *vnic.ListVNicsResponse, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "VNic", "ListVNics")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	selector := labels.NewSelector()
	r, _ := labels.NewRequirement(subnetLabel, selection.Equals, []string{request.SubnetName})
	selector.Add(*r)
	vips, err := s.ipLister.List(selector)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	}

	res = &vnic.ListVNicsResponse{
		TotalSize: int32(len(vips)),
		Vnics:     []*vnic.VNic{},
	}

	for _, ip := range vips {
		subnet, _ := s.subnetLister.Get(ip.Spec.Subnet)
		var vpc string
		if subnet != nil {
			vpc = subnet.Spec.Vpc
		}

		name := ip.Name

		if ip.Annotations != nil && ip.Annotations[vnicAnnotation] != "" {
			name = ip.Annotations[vnicAnnotation]
		}

		state := vnic.State_ACTIVE
		if ipReserved(ip) {
			state = vnic.State_RESERVED
		}
		res.Vnics = append(res.Vnics, &vnic.VNic{
			Name: name,
			Zone: db.AZ,
			Properties: &vnic.VNicProperties{
				IpAddrV4: ip.Spec.V4ip,
				MacAddr:  ip.Spec.MacAddress,
				Subnet:   ip.Spec.Subnet,
				Vpc:      vpc,
				VnicId:   ip.Labels[vnicIdLabel],
			},
			State:      state,
			CreateTime: timestamppb.New(ip.CreationTimestamp.Time),
			UpdateTime: timestamppb.New(ip.CreationTimestamp.Time),
		})
	}

	logrus.Infof("list ACLs %v", request)
	return res, nil
}

func (s *VNICsServer) GetVNic(ctx context.Context, request *vnic.GetVNicRequest) (res *vnic.VNic, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "VNIC", "GetVNic")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	ip, err := s.ipLister.Get(convertVipName(request.Subnet, request.VnicName))
	if err != nil {
		logrus.Errorln(err)
		if kerrors.IsNotFound(err) {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	}

	subnet, _ := s.subnetLister.Get(ip.Spec.Subnet)
	var vpc string
	if subnet != nil {
		vpc = subnet.Spec.Vpc
	}

	name := ip.Name
	if ip.Annotations != nil && ip.Annotations[vnicAnnotation] != "" {
		name = ip.Annotations[vnicAnnotation]
	}

	state := vnic.State_ACTIVE
	if ipReserved(ip) {
		state = vnic.State_RESERVED
	}

	return &vnic.VNic{
		Name: name,
		Zone: db.AZ,
		Properties: &vnic.VNicProperties{
			IpAddrV4: ip.Status.V4ip,
			MacAddr:  ip.Status.Mac,
			Subnet:   ip.Spec.Subnet,
			Vpc:      vpc,
			VnicId:   ip.Labels[vnicIdLabel],
		},
		State:      state,
		CreateTime: timestamppb.New(ip.CreationTimestamp.Time),
		UpdateTime: timestamppb.New(ip.CreationTimestamp.Time),
	}, nil
}

func (s *VNICsServer) CreateVNic(ctx context.Context, request *vnic.CreateVNicRequest) (res *vnic.VNic, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "VNIC", "CreateVNic")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	subnet, err := s.subnetLister.Get(request.Vnic.Subnet)
	if err != nil {
		logrus.Errorln(err)
		if kerrors.IsNotFound(err) {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SubnetNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	}

	vipName := convertVipName(subnet.Name, request.VnicName)

	id := utils.UUIDGen()

	ip := &v1api.Vip{
		ObjectMeta: metav1.ObjectMeta{
			Name: vipName,
			Labels: map[string]string{
				vnicIdLabel:     id,
				subnetLabel:     subnet.Name,
				ipReservedLabel: "",
			},
			Annotations: map[string]string{
				vnicAnnotation: request.VnicName,
			},
		},
		Spec: v1api.VipSpec{
			Namespace: bosonNamespace,
			Subnet:    subnet.Name,
		},
	}

	ip, err = s.client.Vips().Create(context.Background(), ip, metav1.CreateOptions{})
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	for i := 0; i < 100; i++ {
		ip, err = s.ipLister.Get(vipName)
		if err != nil && !kerrors.IsNotFound(err) {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
		}
		if ip != nil && ip.Status.Mac != "" {
			break
		}
		time.Sleep(100 * time.Millisecond)
	}

	if ip == nil || ip.Status.Mac == "" {
		err = fmt.Errorf("fail to assign ip addr")
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	state := vnic.State_ACTIVE
	if ipReserved(ip) {
		state = vnic.State_RESERVED
	}

	return &vnic.VNic{
		Name: request.VnicName,
		Zone: db.AZ,
		Properties: &vnic.VNicProperties{
			IpAddrV4: ip.Status.V4ip,
			MacAddr:  ip.Status.Mac,
			Subnet:   ip.Spec.Subnet,
			Vpc:      subnet.Spec.Vpc,
			VnicId:   ip.Labels[vnicIdLabel],
		},
		State:      state,
		CreateTime: timestamppb.New(ip.CreationTimestamp.Time),
		UpdateTime: timestamppb.New(ip.CreationTimestamp.Time),
	}, nil
}

func (s *VNICsServer) DeleteVNic(ctx context.Context, request *vnic.DeleteVNicRequest) (res *emptypb.Empty, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "VIP", "DeleteVIP")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	res = &emptypb.Empty{}
	res.Reset()

	_, err = s.subnetLister.Get(request.Subnet)
	if err != nil {
		logrus.Errorln(err)
		if kerrors.IsNotFound(err) {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SubnetNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	}

	vipName := convertVipName(request.Subnet, request.VnicName)
	err = s.client.Vips().Delete(context.Background(), vipName, metav1.DeleteOptions{})
	return
}
