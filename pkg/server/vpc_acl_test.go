package server

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"google.golang.org/grpc/codes"
)

func TestListVpcACLsAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vpc.ListACLsRequest{
		Zone: "test11",
	}

	vpcsACLsServer := VPCsACLsServer{}
	ctx := context.Background()

	_, err := vpcsACLsServer.ListACLs(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestListVpcACLsNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetVpcByName, func(_ *db.EngineWrapper, _ string) (*db.Vpcs, error) {
		return nil, fmt.Errorf("vpc not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vpc.ListACLsRequest{
		Zone: "test",
	}

	vpcsACLsServer := VPCsACLsServer{}
	ctx := context.Background()

	_, err := vpcsACLsServer.ListACLs(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetVpcACLsAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vpc.GetACLRequest{
		Zone: "test11",
	}

	vpcsACLsServer := VPCsACLsServer{}
	ctx := context.Background()

	_, err := vpcsACLsServer.GetACL(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 does not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetVpcACLsNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetVpcByName, func(_ *db.EngineWrapper, _ string) (*db.Vpcs, error) {
		return nil, fmt.Errorf("vpc not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vpc.GetACLRequest{
		Zone: "test",
	}

	vpcsACLsServer := VPCsACLsServer{}
	ctx := context.Background()

	_, err := vpcsACLsServer.GetACL(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestCreateVpcACLsAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetVpcByName, func(_ *db.EngineWrapper, _ string) (*db.Vpcs, error) {
		return nil, fmt.Errorf("vpc not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vpc.CreateACLRequest{
		Zone: "test11",
	}

	vpcsACLsServer := VPCsACLsServer{}
	ctx := context.Background()

	_, err := vpcsACLsServer.CreateACL(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteVpcACLsAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vpc.DeleteACLRequest{
		Zone: "test11",
	}

	vpcsACLsServer := VPCsACLsServer{}
	ctx := context.Background()

	_, err := vpcsACLsServer.DeleteACL(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}
