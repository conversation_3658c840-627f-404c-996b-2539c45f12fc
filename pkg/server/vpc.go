package server

import (
	"context"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/metrics"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

const (
	DefaultLanguage      = "en"
	RDMACacheTTL         = 10 * time.Minute
	LabelRDMAProtocol    = "diamond.sensetime.com/nic-training-protocol"
	LabelRDMABandwidth   = "diamond.sensetime.com/nic-training-bandwidth"
	LabelRDMANicCount    = "diamond.sensetime.com/nic-training-port"
	LabelRDMAClusterName = "diamond.sensetime.com/belong-resource-training"
)

// VPCsServer 为 VPCsServer interface 的实现
type VPCsServer struct {
	*vpc.UnimplementedVPCsServer
}

func (VPCsServer) ListVPCs(ctx context.Context, request *vpc.ListVPCsRequest) (res *vpc.ListVPCsResponse, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics("", "VPC", "ListVPCs")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	if request.Zone != db.Region {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.Region)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	idPrefix := utils.GenResourcePrefix(request.SubscriptionName, request.ResourceGroupName, request.Zone)
	vpcDatas, err := dao.GetAllVpcsWithIDPrefix(resourceprovider.BosonProvider.Engine, idPrefix)
	// NOTE:If there is no matching data, vpcDatas and err will both be nil. Empty is allowed
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	vpcs := make([]*vpc.VPC, len(vpcDatas))

	for i, vpcData := range vpcDatas {
		// TODO(lijianhong): hardcode just use the first cidr for return.
		cidrs := strings.Split(vpcData.CIDR, ",")
		vpcs[i] = &vpc.VPC{
			Id:           vpcData.ID,
			Name:         vpcData.Name,
			DisplayName:  vpcData.DisplayName,
			Description:  vpcData.Description,
			Uid:          vpcData.VPCID,
			ResourceType: vpcData.ResourceType,
			CreatorId:    vpcData.CreatorID,
			OwnerId:      vpcData.OwnerID,
			TenantId:     vpcData.TenantID,
			Zone:         vpcData.Zone,
			State:        vpc.VPC_State(vpc.VPC_State_value[vpcData.State]),
			Deleted:      vpcData.Deleted,
			CreateTime:   timestamppb.New(vpcData.CreateTime),
			UpdateTime:   timestamppb.New(vpcData.UpdateTime),
			Properties: &vpc.VPCProperties{
				Cidr:      cidrs[0],
				IsDefault: vpcData.IsDefault,
			},
		}
	}

	res = &vpc.ListVPCsResponse{
		Vpcs:      vpcs,
		TotalSize: int32(len(vpcs)),
	}

	return res, nil
}

func (VPCsServer) GetVPC(ctx context.Context, request *vpc.GetVPCRequest) (res *vpc.VPC, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.VpcName, "VPC", "GetVPC")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	if request.Zone != db.Region {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.Region)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	id := utils.GenVpcResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.VpcName)
	vpcData, err := dao.GetVpcByID(resourceprovider.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	// TODO(lijianhong): hardcode just use the first cidr for return.
	cidrs := strings.Split(vpcData.CIDR, ",")
	res = &vpc.VPC{
		Id:           vpcData.ID,
		Name:         vpcData.Name,
		DisplayName:  vpcData.DisplayName,
		Description:  vpcData.Description,
		Uid:          vpcData.VPCID,
		ResourceType: vpcData.ResourceType,
		CreatorId:    vpcData.CreatorID,
		OwnerId:      vpcData.OwnerID,
		TenantId:     vpcData.TenantID,
		Zone:         vpcData.Zone,
		State:        vpc.VPC_State(vpc.VPC_State_value[vpcData.State]),
		Deleted:      vpcData.Deleted,
		CreateTime:   timestamppb.New(vpcData.CreateTime),
		UpdateTime:   timestamppb.New(vpcData.UpdateTime),
		Properties: &vpc.VPCProperties{
			Cidr:      cidrs[0],
			IsDefault: vpcData.IsDefault,
		},
	}

	return res, nil
}

func (VPCsServer) GetVPCStatus(ctx context.Context, request *vpc.GetVPCStatusRequest) (res *vpc.VPCStatus, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.VpcName, "VPC", "GetVPCStatus")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("Get VPC status with %v", request)
	if request.Zone != db.Region {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.Region)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	id := utils.GenVpcResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.VpcName)
	vpcData, err := dao.GetVpcByID(resourceprovider.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	// TODO(lijianhong): hardcode just use the first cidr for return.
	cidrs := strings.Split(vpcData.CIDR, ",")
	res = &vpc.VPCStatus{
		Cidr:        cidrs[0],
		NatGateways: []*vpc.NATGatewayStatus{},
		Namespaces:  []*vpc.NamespaceStatus{},
		Subnets:     []*vpc.SubnetStatus{},
		Eips:        []*vpc.EIPStatus{},
	}

	subnets, err := dao.ListSubnets(resourceprovider.BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	for _, subnet := range subnets {
		res.Subnets = append(res.Subnets, &vpc.SubnetStatus{
			Name:        subnet.Name,
			Cidr:        subnet.CIDR,
			Id:          subnet.SubnetID,
			Provider:    subnet.Provider,
			Scope:       subnet.Scope,
			NetworkType: subnet.NetworkType,
		})
	}

	nss, err := dao.ListNamespaces(resourceprovider.BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	for _, ns := range nss {
		sn, err := dao.GetSubnet(resourceprovider.BosonProvider.Engine, ns.SubnetID)
		if err != nil {
			logrus.Errorln(err)
			if strings.Contains(err.Error(), "not found") {
				return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SubnetNotFound, errors.Domain, nil, nil)
			}
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		res.Namespaces = append(res.Namespaces, &vpc.NamespaceStatus{
			Name:   ns.Name,
			Subnet: sn.Name,
		})
	}

	hagws, err := dao.ListHaNatGateways(resourceprovider.BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	for _, hagw := range hagws {
		haGwVip, has, err := dao.GetNatGatewayExternalIpPool(resourceprovider.BosonProvider.Engine, hagw.NatGwExternalIPID) //:= db.NatGatewayExternalIpPools{}
		if err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		if !has {
			err = fmt.Errorf("nat_gw_external_ip_id %v not exist for gw %v vpc %v", hagw.NatGwExternalIPID, hagw.Name, vpcData.Name)
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
				"detail": err.Error(),
			})
		}

		// only vip is insert
		res.NatGateways = append(res.NatGateways, &vpc.NATGatewayStatus{
			Name: hagw.Name,
			Ip:   haGwVip.NatGwExternalIP,
			Id:   hagw.HaNatGatewayId,
		})
	}

	dgws, err := dao.ListDGateways(resourceprovider.BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	for _, dgw := range dgws {
		res.DgwGateways = append(res.DgwGateways, &vpc.DGWGatewayStatus{
			Name:       dgw.Name,
			AdminState: vpc.DGWGatewayStatus_AdminState(vpc.DGWGatewayStatus_AdminState_value[dgw.AdminState]),
			Ip:         dgw.GwExternalIP,
			Policy:     dgw.GwPolicy,
			Id:         dgw.DistributeGatewayID,
		})
	}

	eips, err := dao.ListEips(resourceprovider.BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	snats, err := dao.ListDefaultSnatRules(resourceprovider.BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	for _, eip := range eips {
		isDefault := false
		if eipTypeNameIsDnat(eip.AssociationType) {
			for _, snat := range snats {
				if snat.EIPID == eip.EIPID {
					isDefault = true
				}
			}
		}

		res.Eips = append(res.Eips, &vpc.EIPStatus{
			Name:            eip.Name,
			Ip:              eip.EIPIP,
			Id:              eip.EIPID,
			DefaultSnat:     isDefault,
			AssociationType: vpcEipStatusTypeValue(eip.AssociationType),
			AssociationId:   eip.AssociationID,
		})
	}

	return res, nil
}

func (VPCsServer) GetVPCDataStatus(ctx context.Context, request *vpc.GetVPCStatusRequest) (res *vpc.VPCStatus, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.VpcName, "VPC", "GetVPCStatus")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("Get VPC status with %v", request)
	if request.Zone != db.Region {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.Region)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}
	id := utils.GenVpcResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.VpcName)
	vpcData, err := dao.GetVpcByID(resourceprovider.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	// TODO(lijianhong): hardcode just use the first cidr for return.
	cidrs := strings.Split(vpcData.CIDR, ",")
	res = &vpc.VPCStatus{
		Cidr:        cidrs[0],
		NatGateways: []*vpc.NATGatewayStatus{},
		Namespaces:  []*vpc.NamespaceStatus{},
		Subnets:     []*vpc.SubnetStatus{},
		Eips:        []*vpc.EIPStatus{},
	}

	subnets, err := dao.ListSubnets(resourceprovider.BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	for _, subnet := range subnets {
		res.Subnets = append(res.Subnets, &vpc.SubnetStatus{
			Name:        subnet.Name,
			Cidr:        subnet.CIDR,
			Id:          subnet.SubnetID,
			Provider:    subnet.Provider,
			Scope:       subnet.Scope,
			NetworkType: subnet.NetworkType,
		})
	}

	nss, err := dao.ListNamespaces(resourceprovider.BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	for _, ns := range nss {
		sn, err := dao.GetSubnet(resourceprovider.BosonProvider.Engine, ns.SubnetID)
		if err != nil {
			logrus.Errorln(err)
			if strings.Contains(err.Error(), "not found") {
				return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SubnetNotFound, errors.Domain, nil, nil)
			}
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		res.Namespaces = append(res.Namespaces, &vpc.NamespaceStatus{
			Name:   ns.Name,
			Subnet: sn.Name,
		})
	}

	hagws, err := dao.ListHaNatGateways(resourceprovider.BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	for _, hagw := range hagws {
		haGwVip, has, err := dao.GetNatGatewayExternalIpPool(resourceprovider.BosonProvider.Engine, hagw.NatGwExternalIPID) //:= db.NatGatewayExternalIpPools{}
		if err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		if !has {
			err = fmt.Errorf("nat_gw_external_ip_id %v not exist for gw %v vpc %v", hagw.NatGwExternalIPID, hagw.Name, vpcData.Name)
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
				"detail": err.Error(),
			})
		}

		// only vip is insert
		res.NatGateways = append(res.NatGateways, &vpc.NATGatewayStatus{
			Name: hagw.Name,
			Ip:   haGwVip.NatGwExternalIP,
			Id:   hagw.HaNatGatewayId,
		})
	}

	dgws, err := dao.ListDGateways(resourceprovider.BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	for _, dgw := range dgws {
		res.DgwGateways = append(res.DgwGateways, &vpc.DGWGatewayStatus{
			Name:       dgw.Name,
			AdminState: vpc.DGWGatewayStatus_AdminState(vpc.DGWGatewayStatus_AdminState_value[dgw.AdminState]),
			Ip:         dgw.GwExternalIP,
			Policy:     dgw.GwPolicy,
			Id:         dgw.DistributeGatewayID,
		})
	}

	eips, err := dao.ListEips(resourceprovider.BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	snats, err := dao.ListDefaultSnatRules(resourceprovider.BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	for _, eip := range eips {
		isDefault := false
		if eipTypeNameIsDnat(eip.AssociationType) {
			for _, snat := range snats {
				if snat.EIPID == eip.EIPID {
					isDefault = true
				}
			}
		}

		res.Eips = append(res.Eips, &vpc.EIPStatus{
			Name:            eip.Name,
			Ip:              eip.EIPIP,
			Id:              eip.EIPID,
			DefaultSnat:     isDefault,
			AssociationType: vpcEipStatusTypeValue(eip.AssociationType),
			AssociationId:   eip.AssociationID,
		})
	}

	return res, nil
}

func (VPCsServer) CreateVPC(ctx context.Context, request *vpc.CreateVPCRequest) (res *vpc.VPC, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.VpcName, "VPC", "CreateVPC")

	defer func() {
		httpRTMetrics.Code = "404"
		httpRTMetrics.ObserveDurationAndLog()
	}()

	return nil, errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
}

func (VPCsServer) UpdateVPCDgw(ctx context.Context, request *vpc.UpdateVPCDgwRequest) (res *vpc.DGWGateway, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics(request.VpcName, "VPC", "UpdateVPCDgw")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("update vpc dgw called: %+v", request)

	id := utils.GenVpcDGWResourceID(
		request.SubscriptionName,
		request.ResourceGroupName,
		request.Zone,
		request.VpcName,
		request.DgwName)
	dgw, has, err := dao.GetDGatewayByID(resourceprovider.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if !has {
		err = fmt.Errorf("the vpc dgw %v doesn't exsit", request.DgwName)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcDgwNotFound, errors.Domain, nil, nil)
	}

	if dgw.State == vpc.DGWGatewayStatus_DELETING.String() || dgw.State == vpc.DGWGatewayStatus_DELETED.String() {
		err = fmt.Errorf("the vpc dgw %v with state %v not be updated", request.DgwName, dgw.State)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.Dgw == nil || request.Dgw.AdminState == vpc.DGWGateway_INVALID && request.Dgw.GwPolicy == "" {
		err = fmt.Errorf("no specify update action for the vpc dgw %v with %v", request.DgwName, request.Dgw)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.Dgw.AdminState != vpc.DGWGateway_INVALID && request.Dgw.AdminState.String() != dgw.AdminState {
		dgw.AdminState = request.Dgw.AdminState.String()
	}

	if request.Dgw.GwPolicy != "" && request.Dgw.GwPolicy != dgw.GwPolicy {
		dgw.GwPolicy = request.Dgw.GwPolicy
	}

	if err := resourceprovider.UpdateDgw(dgw); err != nil {
		reason := fmt.Errorf("update dgw %v fail, error: %v", request.DgwName, err)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	}

	res = &vpc.DGWGateway{
		Name: dgw.Name,
		Id:   dgw.ID,

		DisplayName:  dgw.DisplayName,
		Description:  dgw.Description,
		Uid:          dgw.DistributeGatewayID,
		ResourceType: dgw.ResourceType,
		CreatorId:    dgw.CreatorID,
		OwnerId:      dgw.OwnerID,
		TenantId:     dgw.TenantID,
		Zone:         dgw.Zone,
		State:        vpc.DGWGateway_State(vpc.DGWGateway_State_value[dgw.State]),
		AdminState:   vpc.DGWGateway_AdminState(vpc.DGWGateway_AdminState_value[dgw.AdminState]),
		GwExternalIp: dgw.GwExternalIP,
		GwPolicy:     dgw.GwPolicy,
		InternalIp:   dgw.InternalIP,
		Deleted:      dgw.Deleted,
		CreateTime:   timestamppb.New(dgw.CreateTime),
		UpdateTime:   timestamppb.New(dgw.UpdateTime),
	}
	return res, nil
}

func (VPCsServer) UpdateVPC(ctx context.Context, request *vpc.UpdateVPCRequest) (res *vpc.VPC, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.VpcName, "VPC", "UpdateVPC")

	defer func() {
		httpRTMetrics.Code = "404"
		httpRTMetrics.ObserveDurationAndLog()
	}()

	return nil, errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
}

func (VPCsServer) DeleteVPC(ctx context.Context, request *vpc.DeleteVPCRequest) (res *emptypb.Empty, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.VpcName, "VPC", "DeleteVPC")

	defer func() {
		httpRTMetrics.Code = "404"
		httpRTMetrics.ObserveDurationAndLog()
	}()

	return nil, errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
}

func (VPCsServer) GetVPCMetrics(ctx context.Context, request *vpc.GetVPCMetricsRequest) (res *vpc.VPCMetrics, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "VPC", "GetVPCMetrics")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("Get vpc metrics: %+v", request)
	tenantID := GetTenantIDFromHeader(ctx)
	if tenantID == "" {
		err = fmt.Errorf("tenant ID is empty")
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	vpcDatas, err := dao.ListVpcsByTenantWithAllZone(resourceprovider.BosonProvider.Engine, tenantID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	exception := 0
	for _, v := range vpcDatas {
		if v.State == vpc.VPC_State_name[int32(vpc.VPC_FAILED)] {
			exception++
		}
	}

	subnetDatas, err := dao.ListSubnetsByTenantWithAllZone(resourceprovider.BosonProvider.Engine, tenantID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	return metrics.VPCMetrics(int32(len(vpcDatas)), int32(exception), int32(len(subnetDatas)), GetHeaderAcceptLanguage(ctx)), nil
}

func GetTenantIDFromHeader(ctx context.Context) string {
	md, err := metadata.FromIncomingContext(ctx)
	if !err {
		return ""
	}
	if len(md.Get("x-tenant-id")) == 0 {
		return ""
	}
	return md.Get("x-tenant-id")[0]
}

func GetUserIDFromHeader(ctx context.Context) string {
	md, err := metadata.FromIncomingContext(ctx)
	if !err {
		return ""
	}
	if len(md.Get("x-user-Id")) == 0 {
		return ""
	}
	return md.Get("x-user-Id")[0]
}

func GetHeaderAcceptLanguage(ctx context.Context) string {
	md, err := metadata.FromIncomingContext(ctx)
	if !err {
		return DefaultLanguage
	}
	if len(md.Get("grpcgateway-accept-language")) == 0 {
		return DefaultLanguage
	}
	return md.Get("grpcgateway-accept-language")[0]
}

func vpcEipStatusTypeValue(eipType string) vpc.EIPStatus_AType {
	return vpc.EIPStatus_AType(vpc.EIPStatus_AType_value[eipType])
}

func (VPCsServer) GetVPCRDMAStatus(ctx context.Context, request *vpc.GetVPCRDMARequest) (res *vpc.VPCRDMAClustersStatus, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "VPC", "GetVPCRDMAStatus")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("Get vpc rdma status: %+v", request)

	// 返回缓存数据
	types.RDMACache.Mutex.RLock()
	defer types.RDMACache.Mutex.RUnlock()
	logrus.Infof("return cache data with %d clusters", len(types.RDMACache.Data.ClustersStatus))

	return types.RDMACache.Data, nil
}
