package server

import (
	"context"
	"fmt"
	"net/http"
	"net/url"
	"strconv"

	"github.com/sirupsen/logrus"
	slbv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/slb/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"google.golang.org/grpc/codes"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
)

type SLBsServer struct {
	*slbv1.UnimplementedSLBDataServiceServer
}

func (s SLBsServer) ListSLBsStatus(ctx context.Context, request *slbv1.ListSLBsStatusRequest) (*slbv1.ListSLBsStatusResponse, error) {
	var err error = nil
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "SLB", "ListSLBsStatus")
	defer func() {
		if err != nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("List SLB with %v", request)

	err = s.listSLBsStatusCheck(request)
	if err != nil {
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	slbs := []*slbv1.SLBStatus{}
	slbNamesArgs := request.SlbNames
	if len(slbNamesArgs) != 0 {
		// 这个 parse 的实现不合理，但是当前没有好的办法，先这样处理，后面有时间再专门调试下这个api的参数
		// TODO fixme
		slbNamesArg, err := s.parseSlbNames(slbNamesArgs[0])
		if err != nil {
			logrus.Error("Parse slbNames error", "slbNames", slbNamesArgs[0], "err", err.Error())
			return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
				"detail": err.Error(),
			})
		}
		for _, slbName := range slbNamesArg {
			slbResourceId := dao.SlbResourceId(request.SubscriptionName, request.ResourceGroupName, request.Zone, slbName)
			slbRecord, err := dao.GetSLBByResourceID(resourceprovider.BosonProvider.Engine, slbResourceId, false)
			if err != nil {
				logrus.Errorln("List SLB status fail. get SLB record err.", err.Error())
				continue
			}
			slbTmp, errGet := s.generateSLBStatusOne(slbRecord)
			if errGet != nil {
				err = errGet
				logrus.Errorln("List slb status fail.", "get one error", "slb", slbName, "err", errGet.Error())
				continue
			}
			slbs = append(slbs, slbTmp)
		}
	} else {
		// all
		tenantID := GetTenantIDFromHeader(ctx)
		slbRecords, err := dao.ListSLBs(resourceprovider.BosonProvider.Engine, tenantID)
		if err != nil {
			logrus.Errorln("List SLB status fail. get SLB record list err.", err.Error())
			slbRecords = []*db.Slbs{}
		}

		for _, slbRecord := range slbRecords {
			slbTmp, errGet := s.generateSLBStatusOne(slbRecord)
			if errGet != nil {
				err = errGet
				logrus.Errorln("List slb status fail.", "get one error", "slb", slbRecord.Name, "err", errGet.Error())
				return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
			}
			slbs = append(slbs, slbTmp)
		}
	}

	// TODO 分页

	return &slbv1.ListSLBsStatusResponse{
		Slbs:          slbs,
		NextPageToken: strconv.Itoa(0),
		TotalSize:     int32(len(slbs)),
	}, nil
}

func (s SLBsServer) GetSLBStatus(ctx context.Context, request *slbv1.GetSLBStatusRequest) (*slbv1.SLBStatus, error) {
	var err error = nil
	httpRTMetrics := exporter.InitHTTPRTMetrics(request.SlbName, "SLB", "GetSLBStatus")
	defer func() {
		if err != nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("GetStatus SLB with %v", request)

	err = s.getSLBStatusRequestWithCheck(request)
	if err != nil {
		return &slbv1.SLBStatus{}, nil
	}

	slbResourceId := dao.SlbResourceId(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	slbRecord, err := dao.GetSLBByResourceID(resourceprovider.BosonProvider.Engine, slbResourceId, false)
	if err != nil {
		logrus.Errorln("Get SLB status one fail. get SLB record err.", err.Error())
		return &slbv1.SLBStatus{}, nil
	}

	slbStatus, err := s.generateSLBStatusOne(slbRecord)
	if err != nil {
		return &slbv1.SLBStatus{}, nil
	}

	return slbStatus, nil
}

func (s SLBsServer) getSlbDbByResourceId(ctx context.Context, subscription, resourceGroup, zone, slbName string) (*db.Slbs, error) {
	engine := resourceprovider.BosonProvider.Engine
	slbResourceId := dao.SlbResourceId(subscription, resourceGroup, zone, slbName)
	slb, err := dao.GetSLBByResourceID(engine, slbResourceId, false)
	if err != nil {
		logrus.Errorln("SLB Server get slb DB record fail.", "err", err.Error())
		return nil, err
	}

	return slb, nil
}

func (s SLBsServer) generateSLBStatusOne(slbRecord *db.Slbs) (*slbv1.SLBStatus, error) {
	engine := resourceprovider.BosonProvider.Engine
	internetVip, err := s.getInternetVIP(slbRecord)
	if err != nil {
		return nil, err
	}

	intranetVip := ""
	if resourceprovider.BosonProviderConfig.Boson.SlbExposeOverlayVip {
		intranetVip, err = s.getIntranetVIP(slbRecord)
		if err != nil {
			return nil, err
		}
	}

	basicNetworkVip := ""
	exposeBasicNetworkVip := false

	exposeBasicNetworkVip = resourceprovider.BosonProviderConfig.Boson.SlbExposeBasicNetworkVip
	if exposeBasicNetworkVip {
		basicNetworkVip, err = s.getBasicNetworkVip(slbRecord)
		if err != nil {
			return nil, err
		}
	}

	slb := s.generateSLBConfig(slbRecord)
	vpcInfo, err := s.generateVpcInfo(slbRecord)
	if err != nil {
		return nil, err
	}
	eipInfo, err := s.generateEipInfo(slbRecord)
	if err != nil {
		return nil, err
	}
	listenerRecords, err := dao.GetSLBListenersBySlbID(engine, slbRecord.SlbID, false)
	if err != nil {
		logrus.Errorln("SLB Server get listener DB records fail.", "err", err.Error(), "slb", slbRecord.SlbID)
		return nil, err
	}
	listeners, err := s.generateListenerInfos(slbRecord, listenerRecords)
	if err != nil {
		return nil, err
	}

	return &slbv1.SLBStatus{
		Slb:                   slb,
		InternetVip:           internetVip,
		IntranetVip:           intranetVip,
		BasicNetworkVip:       basicNetworkVip,
		ExposeBasicNetworkVip: exposeBasicNetworkVip,
		Vpc:                   vpcInfo,
		Eip:                   eipInfo,
		Listeners:             listeners,
	}, nil
}

func (s SLBsServer) generateVpcInfo(slbRecord *db.Slbs) (*slbv1.VPCInfo, error) {
	vpcRecord, err := dao.GetVpc(resourceprovider.BosonProvider.Engine, slbRecord.VpcID)
	if err != nil {
		logrus.Errorln("Generate SLB vpc info fail.", "err", err)
		return nil, err
	}
	vpc := &slbv1.VPCInfo{
		Id:          vpcRecord.ID,
		Uid:         vpcRecord.VPCID,
		Name:        vpcRecord.Name,
		DisplayName: vpcRecord.DisplayName,
	}
	return vpc, nil
}

func (s SLBsServer) generateEipInfo(slbRecord *db.Slbs) (*slbv1.EIPInfo, error) {
	if slbRecord.LoadBalancerType != slbTypeName(slbv1.LoadBalancerType_INTERNET) {
		return nil, nil
	}

	if slbRecord.EipID == "" {
		return nil, nil
	}

	eipRecord, err := dao.GetEipByID(resourceprovider.BosonProvider.Engine, slbRecord.EipID, true)
	if err != nil {
		logrus.Errorln("Generate SLB eip info fail.", "err", err)
		return nil, err
	}

	eip := &slbv1.EIPInfo{
		Id:          eipRecord.ID,
		Uid:         eipRecord.EIPID,
		Name:        eipRecord.Name,
		DisplayName: eipRecord.DisplayName,
	}
	return eip, nil
}

func (s SLBsServer) generateSLBConfig(slbRecord *db.Slbs) *slbv1.SLB {
	slb := &slbv1.SLB{
		Id:           slbRecord.ID,
		Uid:          slbRecord.SlbID,
		Name:         slbRecord.Name,
		DisplayName:  slbRecord.DisplayName,
		ResourceType: slbRecord.ResourceType,
		CreatorId:    slbRecord.CreatorID,
		OwnerId:      slbRecord.OwnerID,
		TenantId:     slbRecord.TenantID,
		Zone:         slbRecord.Zone,
		State:        slbStateTypeValue(slbRecord.State),
		SkuId:        slbRecord.Sku,
		Tags:         nil, // default nil
		Properties:   nil,
		OrderInfo:    nil, // default nil
		Deleted:      slbRecord.Deleted,
		CreateTime:   timestamppb.New(slbRecord.CreateTime),
		UpdateTime:   timestamppb.New(slbRecord.UpdateTime),
	}

	properties := s.generateSlbProperties(slbRecord)
	slb.Properties = properties
	return slb
}

func (s SLBsServer) generateSlbProperties(slbRecord *db.Slbs) *slbv1.SLBProperties {
	properties := &slbv1.SLBProperties{
		Resources: nil,
		Type:      slbNameType(slbRecord.LoadBalancerType),
		VpcId:     slbRecord.VpcID,
		IpVersion: slbIPVersionType(slbRecord.IPVersion),
		EipId:     slbRecord.EipID,
	}

	resources := s.generateSLBResource(slbRecord)
	properties.Resources = resources

	return properties
}

func (s SLBsServer) generateSLBResource(slbRecord *db.Slbs) *slbv1.Resources {
	return &slbv1.Resources{CapacityLimitations: &slbv1.CapacityLimitations{
		TcpCps:      int32(slbRecord.TcpCps),
		TcpConns:    int32(slbRecord.TcpConns),
		UdpCps:      int32(slbRecord.UdpCps),
		UdpConns:    int32(slbRecord.UdpConns),
		TcpsslCps:   int32(slbRecord.TcpsslCps),
		TcpsslConns: int32(slbRecord.TcpsslConns),
		HttpCps:     int32(slbRecord.HttpCps),
		HttpConns:   int32(slbRecord.HttpConns),
		HttpQps:     int32(slbRecord.HttpQps),
		HttpsCps:    int32(slbRecord.HttpsCps),
		HttpsConns:  int32(slbRecord.HttpsConns),
		HttpsQps:    int32(slbRecord.HttpsQps),
	}}
}

func (s SLBsServer) getInternetVIP(slb *db.Slbs) (string, error) {
	if slb.LoadBalancerType != slbTypeName(slbv1.LoadBalancerType_INTERNET) {
		return "", nil
	}
	if len(slb.EipID) == 0 {
		return "", nil
	}
	eip, err := dao.GetEipByID(resourceprovider.BosonProvider.Engine, slb.EipID, false)
	if err != nil {
		logrus.Errorln("Get eip fail. err", err.Error())
		return "", err
	}

	return eip.EIPIP, nil
}

func (s SLBsServer) getIntranetVIP(slb *db.Slbs) (string, error) {
	slbDpClient := resourceprovider.BosonProvider.SlbDataplane
	slbStatus, err := slbDpClient.GetSlbStatus(slb.Name)
	if err != nil {
		logrus.Errorln(fmt.Sprintf("get slb:%s intranet vip fail, err:%s", slb.Name, err))
		return "", err
	}

	return slbStatus.OverlayVip, nil
}

func (s SLBsServer) getBasicNetworkVip(slb *db.Slbs) (string, error) {
	if len(slb.BasicNetworkVipID) == 0 {
		logrus.Errorln(errors.New("empty basic network vip"))
		return "", errors.New("empty basic network vip")
	}

	basicNetworkVip, err := resourceprovider.GetBasicNetworkVip(slb.BasicNetworkVipID)
	if err != nil {
		return "", err
	}

	return basicNetworkVip.Ip.SlbExternalIp, nil
}

func (s SLBsServer) getSLBStatusRequestWithCheck(request *slbv1.GetSLBStatusRequest) error {
	if request.Zone != db.AZ {
		err := fmt.Errorf("Get SLB status check fail. request zone:%s not match %s\n", request.Zone, db.AZ)
		logrus.Errorln(err.Error())
		return err
	}

	return nil
}

func (s SLBsServer) listSLBsStatusCheck(request *slbv1.ListSLBsStatusRequest) error {
	if request.Zone != db.AZ {
		err := fmt.Errorf("List SLB status check fail. request zone:%s not match %s\n", request.Zone, db.AZ)
		logrus.Errorln(err.Error())
		return err
	}

	return nil
}

func (s SLBsServer) parseSlbNames(slbNames string) ([]string, error) {
	values, err := url.ParseQuery(slbNames)
	if err != nil {
		return nil, err
	}

	key := "slb_names"
	names, ok := values[key]
	if !ok {
		err := fmt.Errorf("Parse slbNames:%s fail, key:%s not exist\n", slbNames, key)
		return nil, err
	}

	namesReturn := []string{}
	for _, name := range names {
		if len(name) != 0 {
			namesReturn = append(namesReturn, name)
		}
	}

	if len(namesReturn) == 0 {
		err := fmt.Errorf("Parse slbNames:%s fail, values is nil\n", slbNames)
		return nil, err
	}

	return namesReturn, nil
}

func slbDataResourceUid() string {
	return utils.UUIDGen()
}

func slbTypeName(slbType slbv1.LoadBalancerType) string {
	return slbv1.LoadBalancerType_name[int32(slbType)]
}

func slbNameType(typeName string) slbv1.LoadBalancerType {
	return slbv1.LoadBalancerType(
		slbv1.LoadBalancerType_value[typeName],
	)
}

func slbIPVersionType(ipVersion string) slbv1.SLBIPVersion {
	return slbv1.SLBIPVersion(
		slbv1.SLBIPVersion_value[ipVersion],
	)
}

func slbStateTypeValue(state string) slbv1.SLB_State {
	return slbv1.SLB_State(
		slbv1.SLB_State_value[state],
	)
}

// fixme 这里需要看有什么开源库能做处理
type fieldMaskMapType map[string]bool

func generateFieldMaskMap(mask *fieldmaskpb.FieldMask) fieldMaskMapType {
	maskMap := make(fieldMaskMapType)
	if mask == nil || len(mask.Paths) == 0 {
		return maskMap
	}

	for _, m := range mask.Paths {
		maskMap[m] = true
	}

	return maskMap
}

func cloudAuditCreateSlbListenerSuccess(
	ctx context.Context, resourceID string, request *slbv1.CreateListenerRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbListener,
		types.CloudAuditOperationTypeCreateSlbListener, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("create success"),
	)
}

func cloudAuditCreateSlbListenerError(
	ctx context.Context, resourceID string, request *slbv1.CreateListenerRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbListener,
		types.CloudAuditOperationTypeCreateSlbListener, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditUpdateSlbListenerSuccess(
	ctx context.Context, resourceID string, request *slbv1.UpdateListenerRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbListener,
		types.CloudAuditOperationTypeUpdateSlbListener, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("update success"),
	)
}

func cloudAuditUpdateSlbListenerError(
	ctx context.Context, resourceID string, request *slbv1.UpdateListenerRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbListener,
		types.CloudAuditOperationTypeUpdateSlbListener, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditStartSlbListenerSuccess(
	ctx context.Context, resourceID string, request *slbv1.StartListenerRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbListener,
		types.CloudAuditOperationTypeUpdateSlbListener, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("start success"),
	)
}

func cloudAuditStartSlbListenerError(
	ctx context.Context, resourceID string, request *slbv1.StartListenerRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbListener,
		types.CloudAuditOperationTypeUpdateSlbListener, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(fmt.Sprintf("start fail:%s", reason)),
	)
}

func cloudAuditStopSlbListenerSuccess(
	ctx context.Context, resourceID string, request *slbv1.StopListenerRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbListener,
		types.CloudAuditOperationTypeUpdateSlbListener, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("stop success"),
	)
}

func cloudAuditStopSlbListenerError(
	ctx context.Context, resourceID string, request *slbv1.StopListenerRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbListener,
		types.CloudAuditOperationTypeUpdateSlbListener, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(fmt.Sprintf("stop fail:%s", reason)),
	)
}

func cloudAuditDeleteSlbListenerSuccess(
	ctx context.Context, resourceID string, request *slbv1.DeleteListenerRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbListener,
		types.CloudAuditOperationTypeDeleteSlbListener, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("delete success"),
	)
}

func cloudAuditDeleteSlbListenerError(
	ctx context.Context, resourceID string, request *slbv1.DeleteListenerRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbListener,
		types.CloudAuditOperationTypeDeleteSlbListener, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditCreateSlbTargetGroupSuccess(
	ctx context.Context, resourceID string, request *slbv1.CreateTargetGroupRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbTargetGroup,
		types.CloudAuditOperationTypeCreateSlbTargetGroup, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("create success"),
	)
}

func cloudAuditCreateSlbTargetGroupError(
	ctx context.Context, resourceID string, request *slbv1.CreateTargetGroupRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbTargetGroup,
		types.CloudAuditOperationTypeCreateSlbTargetGroup, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditUpdateSlbTargetGroupSuccess(
	ctx context.Context, resourceID string, request *slbv1.UpdateTargetGroupRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbTargetGroup,
		types.CloudAuditOperationTypeUpdateSlbTargetGroup, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("update success"),
	)
}

func cloudAuditUpdateSlbTargetGroupError(
	ctx context.Context, resourceID string, request *slbv1.UpdateTargetGroupRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbTargetGroup,
		types.CloudAuditOperationTypeUpdateSlbTargetGroup, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditDeleteSlbTargetGroupSuccess(
	ctx context.Context, resourceID string, request *slbv1.DeleteTargetGroupRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbTargetGroup,
		types.CloudAuditOperationTypeDeleteSlbTargetGroup, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("delete success"),
	)
}

func cloudAuditDeleteSlbTargetGroupError(
	ctx context.Context, resourceID string, request *slbv1.DeleteTargetGroupRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbTargetGroup,
		types.CloudAuditOperationTypeDeleteSlbTargetGroup, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditAddSlbTargetSuccess(
	ctx context.Context, resourceID string, request *slbv1.TargetGroupAddTargetsRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbTarget,
		types.CloudAuditOperationTypeSlbAddTarget, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("add success"),
	)
}

func cloudAuditAddSlbTargetError(
	ctx context.Context, resourceID string, request *slbv1.TargetGroupAddTargetsRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbTarget,
		types.CloudAuditOperationTypeSlbAddTarget, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditUpdateSlbTargetSuccess(
	ctx context.Context, resourceID string, request *slbv1.TargetsUpdateRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbTarget,
		types.CloudAuditOperationTypeSlbUpdateTarget, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("update success"),
	)
}

func cloudAuditUpdateSlbTargetError(
	ctx context.Context, resourceID string, request *slbv1.TargetsUpdateRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbTarget,
		types.CloudAuditOperationTypeSlbUpdateTarget, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditRemoveSlbTargetSuccess(
	ctx context.Context, resourceID string, request *slbv1.TargetGroupRemoveTargetsRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbTarget,
		types.CloudAuditOperationTypeSlbRemoveTarget, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("remove success"),
	)
}

func cloudAuditRemoveSlbTargetError(
	ctx context.Context, resourceID string, request *slbv1.TargetGroupRemoveTargetsRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendSlb(
		ctx, types.CloudAuditResourceTypeSlbTarget,
		types.CloudAuditOperationTypeSlbRemoveTarget, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.SlbName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}
