package server

import (
	"context"
	"fmt"
	"math"
	"net/http"
	"reflect"
	"strconv"
	"strings"
	"time"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"

	"github.com/allisson/go-pglock/v3"
	"github.com/sirupsen/logrus"
	dcvcv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/dc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	utilnet "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils/network"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	k8s_errors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

// DCVCsServer 为 DCVCsServer interface 的实现
type DCVCsServer struct {
	*dc.UnimplementedDCVCsServer
}

const (
	DCVCKind              = "DCVC"
	SA                    = "sa"
	NSA                   = "nsa"
	DeployModeAnnotation  = "boson.sensetime.com/deploy_mode"
	HaNatGwNameAnnotation = "boson.sensetime.com/hanatgw_name"

	CHECKING = "CHECKING"
	SUCCESS  = "SUCCESS"
	FAIL     = "FAIL"
)

func (DCVCsServer) CreateDCVC(ctx context.Context, request *dc.CreateDCVCRequest) (res *dc.DCVC, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.DcvcName, "DCVC", "CreateDCVC")
	resourceUID := request.DcvcName
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditCreateDcvcError(ctx, resourceUID, request, struct{}{}, err.Error())
		} else {
			cloudAuditCreateDcvcSuccess(ctx, resourceUID, request, struct{}{})
		}
	}()
	logrus.Infof("CreateDCVC called: %+v", request)

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	has, err := dao.ExistDcvc(resourceprovider.BosonProvider.Engine, request.DcvcName)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if has {
		err = fmt.Errorf("DCVC: %s already exist", request.DcvcName)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.AlreadyExists, errors.DcvcExist, errors.Domain, nil, nil)
	}

	if err = validateParametersForCreateDcvc(request); err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	// allocate
	dcvcData, err := AddDcvcFromAllocateVxlanResources(ctx, request, resourceprovider.BosonProvider.RpConfig.Boson.DcVxlanCIDR, nil)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	}
	dchcData, err := AddDCHC(request)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	vpcData, err := dao.GetVpc(resourceprovider.BosonProvider.Engine, dcvcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("CreateDCVC")
	dcvcCr := &dcvcv1.DcVc{
		TypeMeta: metav1.TypeMeta{
			Kind:       DCVCKind,
			APIVersion: dcvcv1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: request.DcvcName,
			Annotations: map[string]string{
				resourceprovider.AnnoRegion: db.Region,
				resourceprovider.AnnoAz:     db.AZ,
			},
		},
		Spec: dcvcv1.DcVcSpec{
			TenantID:           request.Dcvc.TenantId,
			Vpc:                vpcData.Name,
			RouteType:          dcvcData.ResourceType,
			UpStreamBW:         dcvcData.UpStreamBW,
			DownStreamBW:       dcvcData.DownStreamBW,
			DcplId:             dcvcData.DcplId,
			DcgwName:           dcvcData.DcgwName,
			VlanId:             dcvcData.VlanId,
			CustomerConnectIp:  dcvcData.CustomerConnectIp,
			SenseCoreConnectIp: dcvcData.SenseCoreConnectIp,
			VxlanName:          dcvcData.VxlanName,
			VxlanId:            dcvcData.VxlanId,
			LocalVtepIp:        dcvcData.LocalVtepIp,
			RemoteVtepIp:       dcvcData.RemoteVtepIp,
			AossCidr:           resourceprovider.BosonProvider.RpConfig.Boson.BosonDefaultDgw.PolicyCIDR,
			ConsoleCidr:        resourceprovider.BosonProvider.RpConfig.Boson.DcConsoleCIDR,
			LocalVxlanIp:       fmt.Sprintf("%s/%d", dcvcData.LocalVxlanIp, 30),
			RemoteVxlanIp:      fmt.Sprintf("%s/%d", dcvcData.RemoteVxlanIp, 30),
			LocalCidr:          strings.Split(dcvcData.LocalCidr, ","),
			RemoteCidr:         strings.Split(dcvcData.RemoteCidr, ","),
			HealthCheck: dcvcv1.DcVcHealthCheck{
				Enable:   dchcData.Enable,
				TargetIp: dchcData.TargetIp,
				Interval: dchcData.Interval,
				Counter:  dchcData.Counter,
			},
		},
	}

	dcgwData, err := dao.GetDcgwByName(resourceprovider.BosonProvider.Engine, request.Dcvc.Properties.DcgwName)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DcgwNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if dcgwData.DeployMode == NSA {
		dcvcCr.ObjectMeta.Annotations[DeployModeAnnotation] = NSA
		haNatGwData, has, err := dao.GetHaNatGatewayByVpcId(resourceprovider.BosonProvider.Engine, dcgwData.VPCID)
		if err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		if !has {
			logrus.Errorf("GetHaNatGatewayByVpcId %s not exists in db", dcgwData.VPCID)
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.HaNatGwNotFound, errors.Domain, nil, nil)
		}
		dcvcCr.ObjectMeta.Annotations[HaNatGwNameAnnotation] = haNatGwData.Name
	} else {
		dcvcCr.ObjectMeta.Annotations[DeployModeAnnotation] = SA
	}

	_, err = resourceprovider.BosonProvider.BosonNetClient.DcVcs().Create(context.Background(), dcvcCr, metav1.CreateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err.Error())
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	return invertDcvc(dcvcData, dchcData), nil
}

func (DCVCsServer) DeleteDCVC(ctx context.Context, request *dc.DeleteDCVCRequest) (res *emptypb.Empty, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.DcvcName, "DCVC", "DeleteDCVC")
	resourceUID := request.DcvcName
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditDeleteDcvcError(ctx, resourceUID, request, struct{}{}, err.Error())
		} else {
			cloudAuditDeleteDcvcSuccess(ctx, resourceUID, request, struct{}{})
		}
	}()
	logrus.Infof("DeleteDCVC called: %+v", request)

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	has, err := dao.ExistDcvcResource(resourceprovider.BosonProvider.Engine, request.DcvcName, request.SubscriptionName, request.ResourceGroupName)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if !has {
		reason := fmt.Sprintf("DCVC %v not exist", request.DcvcName)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DcvcNotFound, errors.Domain, nil, nil)
	}

	has, err = dao.ExistDchcByDcvcName(resourceprovider.BosonProvider.Engine, request.DcvcName)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if has {
		if err := dao.SetDchcDeletedByDcvcName(resourceprovider.BosonProvider.Engine, request.DcvcName); err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
	}

	if err := dao.SetDcvcState(resourceprovider.BosonProvider.Engine, request.DcvcName, dc.DCVC_DELETING.String()); err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	k8sRTMetrics := exporter.InitK8sRTMetrics("DeleteDCVC")
	err = resourceprovider.BosonProvider.BosonNetClient.DcVcs().Delete(context.TODO(), request.DcvcName, metav1.DeleteOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		if k8s_errors.IsNotFound(err) {
			if err = dao.SetDcvcState(resourceprovider.BosonProvider.Engine, request.DcvcName, dc.DCVC_State_name[int32(dc.DCVC_DELETED)]); err != nil {
				return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
			}
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	return &emptypb.Empty{}, nil
}

func (DCVCsServer) UpdateDCVC(ctx context.Context, request *dc.UpdateDCVCRequest) (res *dc.DCVC, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.DcvcName, "DCVC", "UpdateDCVC")
	resourceUID := request.DcvcName
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditUpdateDcvcError(ctx, resourceUID, request, struct{}{}, err.Error())
		} else {
			cloudAuditUpdateDcvcSuccess(ctx, resourceUID, request, struct{}{})
		}
	}()
	logrus.Infof("UpdateDCVC called: %+v", request)

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.Dcvc.GetDisplayName() == "" && request.Dcvc.Description == "" && request.Dcvc.Properties == nil {
		reason := "just support to update DisplayName、Description、Properties"
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	has, err := dao.ExistDcvcResource(resourceprovider.BosonProvider.Engine, request.DcvcName, request.SubscriptionName, request.ResourceGroupName)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if !has {
		reason := fmt.Sprintf("DCVC %v not exist", request.DcvcName)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DcvcNotFound, errors.Domain, nil, nil)
	}

	dcvcData, err := dao.GetDcvcByName(resourceprovider.BosonProvider.Engine, request.DcvcName)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DcvcNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	has, err = dao.ExistDchcByDcvcName(resourceprovider.BosonProvider.Engine, request.DcvcName)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if !has {
		reason := fmt.Sprintf("DCHC By DCVC %v not exist", request.DcvcName)
		logrus.Infoln(reason)
		dchcData, err := AddDCHCByUpdate(request, dcvcData)
		if err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		logrus.Infof("DCHC %v By DCVC %v add success", dchcData.Name, request.DcvcName)
	}
	dchcData, err := dao.GetDchcByDcvcName(resourceprovider.BosonProvider.Engine, request.DcvcName)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetDCVC")
	dcvcCr, err := resourceprovider.BosonProvider.BosonNetClient.DcVcs().Get(context.TODO(), request.DcvcName, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}
	dcvcCrOld := dcvcCr.DeepCopy()

	if request.Dcvc.GetDisplayName() != "" {
		dcvcData.DisplayName = request.Dcvc.GetDisplayName()
	}
	if request.Dcvc.Description != "" {
		dcvcData.Description = request.Dcvc.Description
	}

	if request.Dcvc.Properties != nil {
		if request.Dcvc.Properties.RouteType != "" {
			dcvcData.RouteType = request.Dcvc.Properties.RouteType
			dcvcCr.Spec.RouteType = request.Dcvc.Properties.RouteType
		}
		if request.Dcvc.Properties.LimitRateItems != nil && request.Dcvc.Properties.LimitRateItems.UpStreamBw != 0 {
			dcvcData.UpStreamBW = request.Dcvc.Properties.LimitRateItems.UpStreamBw
			dcvcCr.Spec.UpStreamBW = request.Dcvc.Properties.LimitRateItems.UpStreamBw
		}
		if request.Dcvc.Properties.LimitRateItems != nil && request.Dcvc.Properties.LimitRateItems.DownStreamBw != 0 {
			dcvcData.DownStreamBW = request.Dcvc.Properties.LimitRateItems.DownStreamBw
			dcvcCr.Spec.DownStreamBW = request.Dcvc.Properties.LimitRateItems.DownStreamBw
		}

		if request.Dcvc.Properties.VlanId != "" {
			dcvcData.VlanId = request.Dcvc.Properties.VlanId
			dcvcCr.Spec.VlanId = request.Dcvc.Properties.VlanId
		}
		if request.Dcvc.Properties.CustomerConnectIp != "" {
			dcvcData.CustomerConnectIp = request.Dcvc.Properties.CustomerConnectIp
			dcvcCr.Spec.CustomerConnectIp = request.Dcvc.Properties.CustomerConnectIp
		}
		if request.Dcvc.Properties.SensecoreConnectIp != "" {
			dcvcData.SenseCoreConnectIp = request.Dcvc.Properties.SensecoreConnectIp
			dcvcCr.Spec.SenseCoreConnectIp = request.Dcvc.Properties.SensecoreConnectIp
		}
		if len(request.Dcvc.Properties.LocalCidr) > 0 {
			dcvcData.LocalCidr = strings.Join(request.Dcvc.Properties.LocalCidr, ",")
			dcvcCr.Spec.LocalCidr = request.Dcvc.Properties.LocalCidr
		}
		if len(request.Dcvc.Properties.RemoteCidr) > 0 {
			dcvcData.RemoteCidr = strings.Join(request.Dcvc.Properties.RemoteCidr, ",")
			dcvcCr.Spec.RemoteCidr = request.Dcvc.Properties.RemoteCidr
		}
	}

	if request.Dcvc.Properties != nil {
		if request.Dcvc.Properties.HealthCheck == nil {
			request.Dcvc.Properties.HealthCheck = &dc.HealtCheck{}
		}
		dchcData.Enable = request.Dcvc.Properties.HealthCheck.Enable
		dcvcCr.Spec.HealthCheck.Enable = dchcData.Enable

		dchcData.TargetIp = utils.EmptyToBackup(request.Dcvc.Properties.HealthCheck.TargetIp, strings.Split(request.Dcvc.Properties.CustomerConnectIp, "/")[0])
		dcvcCr.Spec.HealthCheck.TargetIp = dchcData.TargetIp

		dchcData.Interval = utils.EmptyToBackupInt32(request.Dcvc.Properties.HealthCheck.GetInterval(), 2)
		dcvcCr.Spec.HealthCheck.Interval = dchcData.Interval

		dchcData.Counter = utils.EmptyToBackupInt32(request.Dcvc.Properties.HealthCheck.GetCounter(), 5)
		dcvcCr.Spec.HealthCheck.Counter = dchcData.Counter
	}

	if !reflect.DeepEqual(dcvcCrOld.Spec, dcvcCr.Spec) {
		dcvcData.State = dc.DCVC_UPDATING.String()
	}
	dcvcData.UpdateTime = timestamppb.Now().AsTime()
	if err := dao.UpdateDCVC(resourceprovider.BosonProvider.Engine, dcvcData); err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if dchcData.RunStatus == "" {
		dchcData.RunStatus = utils.GetInitRunStatus(dchcData.Enable, CHECKING)
	} else {
		dchcData.RunStatus = utils.GetInitRunStatus(dchcData.Enable, dchcData.RunStatus)
	}
	dcvcCr.Status.HealthStatus.RunStatus = dchcData.RunStatus
	dchcData.UpdateTime = timestamppb.Now().AsTime()
	logrus.Infof("dchcData : %+v", dchcData)
	if err := dao.UpdateDCHC(resourceprovider.BosonProvider.Engine, dchcData); err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateDCVC")
	_, err = resourceprovider.BosonProvider.BosonNetClient.DcVcs().Update(context.TODO(), dcvcCr, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	return invertDcvc(dcvcData, dchcData), nil
}

func (DCVCsServer) GetDCVC(ctx context.Context, request *dc.GetDCVCRequest) (res *dc.DCVC, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.DcvcName, "DCVC", "GetDCVC")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()
	logrus.Infof("GetDCVC called: %+v", request)

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	has, err := dao.ExistDcvcResource(resourceprovider.BosonProvider.Engine, request.DcvcName, request.SubscriptionName, request.ResourceGroupName)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if !has {
		err = fmt.Errorf("DCVC: %s not exist", request.DcvcName)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DcvcNotFound, errors.Domain, nil, nil)
	}

	var dcvcData *db.Dcvcs
	dcvcData, err = dao.GetDcvcByName(resourceprovider.BosonProvider.Engine, request.DcvcName)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DcvcNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	var dchcData *db.Dchcs
	has, err = dao.ExistDchcByDcvcName(resourceprovider.BosonProvider.Engine, request.DcvcName)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if has {
		dchcData, err = dao.GetDchcByDcvcName(resourceprovider.BosonProvider.Engine, request.DcvcName)
		if err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
	}

	return invertDcvc(dcvcData, dchcData), nil
}

func (DCVCsServer) ListDCVCs(ctx context.Context, request *dc.ListDCVCsRequest) (res *dc.ListDCVCsResponse, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics("", "DCVC", "ListDCVCs")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()
	//logrus.Infof("ListDCVCs called: %+v", request)

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	var dcvcDatas []*db.Dcvcs
	dcvcDatas, err = dao.ListDcvcsByTenant(resourceprovider.BosonProvider.Engine, request.TenantId)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	dcvcs := make([]*dc.DCVC, len(dcvcDatas))

	for i, dcvcData := range dcvcDatas {
		var dchcData *db.Dchcs
		dchcData, err = dao.ListDCVCGetDchcByDcvcName(resourceprovider.BosonProvider.Engine, dcvcData.Name)
		if err != nil {
			logrus.Warningln(err)
		}
		if dchcData.TargetIp == strings.Split(dcvcData.CustomerConnectIp, "/")[0] {
			dchcData.TargetIp = ""
		}
		dcvcs[i] = invertDcvc(dcvcData, dchcData)
	}

	res = &dc.ListDCVCsResponse{
		Dcvcs:     dcvcs,
		TotalSize: int32(len(dcvcs)),
	}

	return res, nil
}

func validateParametersForCreateDcvc(request *dc.CreateDCVCRequest) error {
	if request.Dcvc == nil || request.Dcvc.Properties == nil {
		err := fmt.Errorf("request Properties is nil")
		return err
	}

	if request.Dcvc.Properties.DcplId == "" {
		err := fmt.Errorf("request DcplId is required")
		return err
	}
	if request.Dcvc.Properties.DcgwName == "" {
		err := fmt.Errorf("request DcgwName is required")
		return err
	}

	return nil
}

func invertDcvc(dcvcData *db.Dcvcs, dchcData *db.Dchcs) *dc.DCVC {
	healthCheck := &dc.HealtCheck{}
	if dchcData != nil {
		interval := new(int32)
		*interval = dchcData.Interval
		counter := new(int32)
		*counter = dchcData.Counter
		healthCheck = &dc.HealtCheck{
			Enable:    dchcData.Enable,
			TargetIp:  dchcData.TargetIp,
			Interval:  interval,
			Counter:   counter,
			RunStatus: dchcData.RunStatus,
			RunTime:   timestamppb.New(dchcData.RunTime),
		}
	}
	return &dc.DCVC{
		Id:           dcvcData.ID,
		Name:         dcvcData.Name,
		DisplayName:  dcvcData.DisplayName,
		Description:  dcvcData.Description,
		Uid:          dcvcData.DcvcID,
		ResourceType: dcvcData.ResourceType,
		CreatorId:    dcvcData.CreatorID,
		OwnerId:      dcvcData.OwnerID,
		TenantId:     dcvcData.TenantID,
		Zone:         dcvcData.Zone,
		State:        dc.DCVC_State(dc.DCVC_State_value[dcvcData.State]),
		Properties: &dc.DCVCProperties{
			RouteType: dcvcData.RouteType,
			LimitRateItems: &dc.LimitRateItems{
				UpStreamBw:   dcvcData.UpStreamBW,
				DownStreamBw: dcvcData.DownStreamBW,
			},
			DcplId:             dcvcData.DcplId,
			DcgwName:           dcvcData.DcgwName,
			VlanId:             dcvcData.VlanId,
			CustomerConnectIp:  dcvcData.CustomerConnectIp,
			SensecoreConnectIp: dcvcData.SenseCoreConnectIp,
			VxlanName:          dcvcData.VxlanName,
			VxlanId:            dcvcData.VxlanId,
			LocalVtepIp:        dcvcData.LocalVtepIp,
			RemoteVtepIp:       dcvcData.RemoteVtepIp,
			LocalVxlanIp:       dcvcData.LocalVxlanIp,
			RemoteVxlanIp:      dcvcData.RemoteVxlanIp,
			LocalCidr:          strings.Split(dcvcData.LocalCidr, ","),
			RemoteCidr:         strings.Split(dcvcData.RemoteCidr, ","),
			SubscriptionName:   dcvcData.SubscriptionName,
			ResourceGroupName:  dcvcData.ResourceGroupName,
			HealthCheck:        healthCheck,
		},
		CreateTime: timestamppb.New(dcvcData.CreateTime),
		UpdateTime: timestamppb.New(dcvcData.UpdateTime),
	}
}

func allocateVxlanName(dcgwName string) (int64, error) {
	count, err := dao.GetDcvcsCountByDcgwId(resourceprovider.BosonProvider.Engine, dcgwName)
	if err != nil {
		logrus.Errorln(err)
		return 0, err
	}
	return count, nil
}

func allocateVxlanId(dcgwName string) (int, error) {
	var vxlanId = 100000
	for vxlanId < 200000 {
		vxlanId++
		ok, err := dao.ExistDcvcByVxlanId(resourceprovider.BosonProvider.Engine, strconv.Itoa(vxlanId))
		if err != nil {
			logrus.Errorln(err)
			return 0, err
		}
		if !ok {
			return vxlanId, nil
		}
	}
	return 0, fmt.Errorf("no free dcgw [%s] vxlan id", dcgwName)
}

func AddDcvcFromAllocateVxlanResources(ctx context.Context, request *dc.CreateDCVCRequest, cidr string, reservedIPs []string) (dcvcData *db.Dcvcs, err error) {
	timeOut := resourceprovider.BosonProvider.RpConfig.Boson.DcPgLockTimeOut
	logrus.Infof("pg lock timeout is %d second", timeOut)

	seed := fmt.Sprintf("%s-%s", db.AZ, resourceprovider.PGLOCK_DCVC)
	pglockId := utils.HashStringToInt32(seed)
	logrus.Infof("dcvc: %s,init PG Lock ID %d for %s (region-dcgwName)", request.DcvcName, pglockId, seed)

	var dlock pglock.Lock
	childCtx, cancel := context.WithTimeout(ctx, time.Duration(timeOut)*time.Second)
	dbLockRTMetrics := exporter.InitDBRTMetrics("PgLockWhenCreateDCGW")
	err = func() error {
		defer cancel()
		dlock, err = pglock.NewLock(childCtx, int64(pglockId), resourceprovider.BosonProvider.Engine.DB().DB)
		if err != nil {
			logrus.Errorf("dcvc: %s, new pglock for pgLockID %d failed, error: %s", request.DcvcName, pglockId, err.Error())
			return err
		}
		logrus.Infof("dcvc: %s, new pglock for pgLockID %d success", request.DcvcName, pglockId)

		err = dlock.WaitAndLock(childCtx)
		if err != nil {
			logrus.Errorf("dcvc: %s, get pglock for pgLockID %d failed, error %s", request.DcvcName, pglockId, err.Error())
			return err
		}
		return nil
	}()
	dbLockRTMetrics.ObserveDurationAndLog()
	defer dlock.Close()

	defer func() {
		_ = dlock.Unlock(ctx) // protect end here
		logrus.Infof("dcvc: %s, unlock pglock for pgLockID %d", request.DcvcName, pglockId)
	}()

	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}

	vxlanIndex, err := allocateVxlanName(request.Dcvc.Properties.DcgwName)
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}
	vxlanId, err := allocateVxlanId(request.Dcvc.Properties.DcgwName)
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}

	remoteVxlanIp, err := allocateIPv4Address(request.Dcvc.Properties.DcgwName, cidr, reservedIPs, 30)
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}

	dcgwData, err := dao.GetDcgwByName(resourceprovider.BosonProvider.Engine, request.Dcvc.Properties.DcgwName)
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}

	var upStreamBw, downStreamBw int32 = 0, 0
	if request.Dcvc.Properties.LimitRateItems != nil {
		upStreamBw = request.Dcvc.Properties.LimitRateItems.UpStreamBw
		downStreamBw = request.Dcvc.Properties.LimitRateItems.DownStreamBw
	}

	haNatGwData, has, err := dao.GetHaNatGatewayByVpcId(resourceprovider.BosonProvider.Engine, dcgwData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}
	if !has {
		logrus.Errorf("GetHaNatGatewayByVpcId %s not exists in db", dcgwData.VPCID)
		return nil, err
	}

	gwExternalIP, has, err := dao.GetNatGatewayExternalIpPool(resourceprovider.BosonProvider.Engine, haNatGwData.NatGwExternalIPID)
	if err != nil {
		logrus.Errorln(has, err)
		return nil, err
	}
	if !has {
		err := fmt.Errorf("there is no nat gateway external IP with id %s", haNatGwData.NatGwExternalIPID)
		logrus.Errorln(err)
		return nil, err
	}

	vtepIps := []string{}
	if err := resourceprovider.BosonProvider.Engine.Engine.SQL(
		"SELECT dcads.vtep_ip FROM dcads JOIN net_port ON dcads.net_device_id = net_port.net_device_id JOIN dcpls ON net_port.net_port_id = dcpls.net_port_id WHERE dcpls.dcpl_id = ? and deleted = ?",
		request.Dcvc.Properties.DcplId, false).Find(&vtepIps); err != nil {
		logrus.Errorln(err)
		return nil, err
	}
	if len(vtepIps) != 1 || vtepIps[0] == "" {
		err := fmt.Errorf("there is no vtep_ip with dcpl_id %s", request.Dcvc.Properties.DcplId)
		logrus.Errorln(err)
		return nil, err
	}

	// update DB
	dcvcData = &db.Dcvcs{
		DcvcID:             utils.UUIDGen(),
		DisplayName:        utils.EmptyToBackup(request.Dcvc.DisplayName, request.DcvcName),
		Description:        request.Dcvc.Description,
		ID:                 request.Dcvc.Id,
		Name:               request.DcvcName,
		ResourceType:       request.Dcvc.ResourceType,
		SubscriptionName:   request.SubscriptionName,
		ResourceGroupName:  request.ResourceGroupName,
		VPCID:              dcgwData.VPCID,
		DcplId:             request.Dcvc.Properties.DcplId,
		DcgwName:           request.Dcvc.Properties.DcgwName,
		RouteType:          request.Dcvc.Properties.RouteType,
		UpStreamBW:         upStreamBw,
		DownStreamBW:       downStreamBw,
		VlanId:             request.Dcvc.Properties.VlanId,
		CustomerConnectIp:  request.Dcvc.Properties.CustomerConnectIp,
		SenseCoreConnectIp: request.Dcvc.Properties.SensecoreConnectIp,
		VxlanName:          fmt.Sprintf("vxlan%d", vxlanIndex),
		VxlanId:            strconv.Itoa(vxlanId),
		LocalVtepIp:        gwExternalIP.NatGwExternalIP,
		RemoteVtepIp:       vtepIps[0],
		LocalVxlanIp:       string(utilnet.IP(remoteVxlanIp).Add(1)),
		RemoteVxlanIp:      remoteVxlanIp,
		LocalCidr:          utils.EmptyToBackup(strings.Join(request.Dcvc.Properties.LocalCidr, ","), dcgwData.LocalCidr),
		RemoteCidr:         utils.EmptyToBackup(strings.Join(request.Dcvc.Properties.RemoteCidr, ","), dcgwData.RemoteCidr),
		Zone:               request.Zone,
		State:              dc.DCVC_CREATING.String(),
		CreatorID:          request.Dcvc.CreatorId,
		OwnerID:            request.Dcvc.OwnerId,
		TenantID:           request.Dcvc.TenantId,
		CreateTime:         request.Dcvc.CreateTime.AsTime(),
		UpdateTime:         request.Dcvc.UpdateTime.AsTime(),
		Deleted:            false,
	}
	if err = dao.AddDCVC(resourceprovider.BosonProvider.Engine, dcvcData); err != nil {
		logrus.Errorln(err)
		return nil, err
	}

	return dcvcData, nil
}

func AddDCHC(request *dc.CreateDCVCRequest) (dchcData *db.Dchcs, err error) {
	dchcID := utils.UUIDGen()
	dchcName := "dchc-" + utils.Substring(request.Dcvc.TenantId, 0, 20) + "-" + dchcID[:8]
	if request.Dcvc.Properties != nil && request.Dcvc.Properties.HealthCheck == nil {
		request.Dcvc.Properties.HealthCheck = &dc.HealtCheck{}
	}
	dchcData = &db.Dchcs{
		DchcID:     dchcID,
		Name:       dchcName,
		DcvcName:   request.DcvcName,
		Enable:     request.Dcvc.Properties.HealthCheck.Enable,
		TargetIp:   utils.EmptyToBackup(request.Dcvc.Properties.HealthCheck.TargetIp, strings.Split(request.Dcvc.Properties.CustomerConnectIp, "/")[0]),
		Interval:   utils.EmptyToBackupInt32(request.Dcvc.Properties.HealthCheck.GetInterval(), 2),
		Counter:    utils.EmptyToBackupInt32(request.Dcvc.Properties.HealthCheck.GetCounter(), 5),
		HcType:     "icmp",
		RunStatus:  utils.GetInitRunStatus(request.Dcvc.Properties.HealthCheck.Enable, CHECKING),
		RunTime:    request.Dcvc.Properties.HealthCheck.RunTime.AsTime(),
		Zone:       request.Zone,
		CreateTime: request.Dcvc.CreateTime.AsTime(),
		UpdateTime: request.Dcvc.UpdateTime.AsTime(),
		Deleted:    false,
	}
	if err = dao.AddDCHC(resourceprovider.BosonProvider.Engine, dchcData); err != nil {
		logrus.Errorln(err)
		return nil, err
	}

	return dchcData, nil
}

func AddDCHCByUpdate(request *dc.UpdateDCVCRequest, dcvcData *db.Dcvcs) (dchcData *db.Dchcs, err error) {
	dchcID := utils.UUIDGen()
	dchcName := "dchc-" + utils.Substring(dcvcData.TenantID, 0, 20) + "-" + dchcID[:8]
	updateTime := timestamppb.Now().AsTime()
	if request.Dcvc.Properties != nil && request.Dcvc.Properties.HealthCheck == nil {
		request.Dcvc.Properties.HealthCheck = &dc.HealtCheck{}
	}
	dchcData = &db.Dchcs{
		DchcID:     dchcID,
		Name:       dchcName,
		DcvcName:   request.DcvcName,
		Enable:     request.Dcvc.Properties.HealthCheck.Enable,
		TargetIp:   utils.EmptyToBackup(request.Dcvc.Properties.HealthCheck.TargetIp, strings.Split(request.Dcvc.Properties.CustomerConnectIp, "/")[0]),
		Interval:   utils.EmptyToBackupInt32(request.Dcvc.Properties.HealthCheck.GetInterval(), 2),
		Counter:    utils.EmptyToBackupInt32(request.Dcvc.Properties.HealthCheck.GetCounter(), 5),
		HcType:     "icmp",
		RunStatus:  utils.GetInitRunStatus(request.Dcvc.Properties.HealthCheck.Enable, CHECKING),
		RunTime:    request.Dcvc.Properties.HealthCheck.RunTime.AsTime(),
		Zone:       request.Zone,
		CreateTime: updateTime,
		UpdateTime: updateTime,
		Deleted:    false,
	}
	if err = dao.AddDCHC(resourceprovider.BosonProvider.Engine, dchcData); err != nil {
		logrus.Errorln(err)
		return nil, err
	}

	return dchcData, nil
}

func allocateIPv4Address(dcgwName, cidr string, reservedIPs []string, mask int) (string, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListAllocateIPs")

	ips, err := dao.ListAllocateRemoteVxlanIPs(resourceprovider.BosonProvider.Engine, dcgwName)
	dbRTMetrics.ObserveDurationAndLog()
	if err != nil {
		err = fmt.Errorf("list allocate ips from dcgw error: %s", err)
		return "", err
	}

	m := map[string]bool{}
	for _, v := range ips {
		m[v] = true
	}

	firstIP, err := utilnet.FirstIP(cidr)
	if err != nil {
		return "", err
	}
	lastIP, err := utilnet.LastIP(cidr)
	if err != nil {
		return "", err
	}
	step := int64(math.Pow(2, float64(32-mask)))

	for ip := firstIP; utilnet.CIDRContainIP(cidr, ip) && ip != string(utilnet.IP(lastIP).Add(1)); ip = string(utilnet.IP(ip).Add(step)) {
		if IsReservedIP(ip, reservedIPs) {
			continue
		}

		if _, ok := m[ip]; !ok && utilnet.CIDRContainIP(cidr, string(utilnet.IP(ip).Add(step-1))) {
			return ip, nil
		}
	}
	return "", fmt.Errorf("no ip address in subnet %s", cidr)
}

func cloudAuditCreateDcvcError(
	ctx context.Context, resourceID string, request *dc.CreateDCVCRequest, response interface{}, reason string,
) {
	resourceprovider.BosonProvider.Processor.CloudAuditSendDCVC(
		ctx,
		types.CloudAuditOperationTypeCreateDcvcs, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.Dcvc.Name,
		resourceprovider.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditCreateDcvcSuccess(
	ctx context.Context, resourceID string, request *dc.CreateDCVCRequest, response interface{},
) {
	resourceprovider.BosonProvider.Processor.CloudAuditSendDCVC(
		ctx,
		types.CloudAuditOperationTypeCreateDcvcs, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.DcvcName,
		resourceprovider.BosonProvider.Processor.CloudAuditSendMsg("create success"),
	)
}

func cloudAuditUpdateDcvcError(
	ctx context.Context, resourceUID string, request *dc.UpdateDCVCRequest, response interface{}, reason string,
) {
	resourceprovider.BosonProvider.Processor.CloudAuditSendDCVC(
		ctx,
		types.CloudAuditOperationTypeUpdateDcvcs, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceUID, request.DcvcName,
		resourceprovider.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditUpdateDcvcSuccess(
	ctx context.Context, resourceUID string, request *dc.UpdateDCVCRequest, response interface{},
) {
	resourceprovider.BosonProvider.Processor.CloudAuditSendDCVC(
		ctx,
		types.CloudAuditOperationTypeUpdateDcvcs, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceUID, request.DcvcName,
		resourceprovider.BosonProvider.Processor.CloudAuditSendMsg("update success"),
	)
}

func cloudAuditDeleteDcvcError(
	ctx context.Context, resourceUID string, request *dc.DeleteDCVCRequest, response interface{}, reason string,
) {
	resourceprovider.BosonProvider.Processor.CloudAuditSendDCVC(
		ctx,
		types.CloudAuditOperationTypeDeleteDcvcs, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceUID, request.DcvcName,
		resourceprovider.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditDeleteDcvcSuccess(
	ctx context.Context, resourceUID string, request *dc.DeleteDCVCRequest, response interface{},
) {
	resourceprovider.BosonProvider.Processor.CloudAuditSendDCVC(
		ctx,
		types.CloudAuditOperationTypeDeleteDcvcs, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceUID, request.DcvcName,
		resourceprovider.BosonProvider.Processor.CloudAuditSendMsg("delete success"),
	)
}
