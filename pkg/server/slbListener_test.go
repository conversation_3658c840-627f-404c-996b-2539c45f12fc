package server

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
	slbv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/slb/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"google.golang.org/grpc/codes"
)

func TestCreateListenerErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditCreateSlbListenerError, func(ctx context.Context, resourceID string, request *slbv1.CreateListenerRequest, response interface{}, reason string) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetSLBByResourceID, func(_ *db.EngineWrapper, _ string, _ bool) (*db.Slbs, error) {
		return nil, fmt.Errorf("err")
	})
	slbServer := SLBsServer{}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &slbv1.CreateListenerRequest{
		Zone:     "test11",
		Listener: &slbv1.Listener{},
	}

	ctx := context.Background()

	_, err := slbServer.CreateListener(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "err",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestUpdateListenerErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditUpdateSlbListenerError, func(ctx context.Context, resourceID string, request *slbv1.UpdateListenerRequest, response interface{}, reason string) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetSLBByResourceID, func(_ *db.EngineWrapper, _ string, _ bool) (*db.Slbs, error) {
		return nil, fmt.Errorf("err")
	})
	slbServer := SLBsServer{}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &slbv1.UpdateListenerRequest{
		Zone: "test11",
	}

	ctx := context.Background()

	_, err := slbServer.UpdateListener(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "err",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteListenerErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditDeleteSlbListenerError, func(ctx context.Context, resourceID string, request *slbv1.DeleteListenerRequest, response interface{}, reason string) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetSLBByResourceID, func(_ *db.EngineWrapper, _ string, _ bool) (*db.Slbs, error) {
		return nil, fmt.Errorf("slb not exist")
	})
	slbServer := SLBsServer{}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &slbv1.DeleteListenerRequest{
		Zone: "test11",
	}

	ctx := context.Background()

	_, err := slbServer.DeleteListener(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.SlbNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

// TODO: test panic
// func TestListListenerErr(t *testing.T) {
// 	p := gomonkey.NewPatches()
// 	defer p.Reset()

// 	var h *exporter.HTTPRTMetrics
// 	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
// 	p.ApplyFunc(cloudAuditDeleteSlbListenerError, func(ctx context.Context, resourceID string, request *slbv1.DeleteListenerRequest, response interface{}, reason string) {
// 	})
// 	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
// 		Engine: db.NewEngineWrapper(mockEngine),
// 	})
// 	p.ApplyFunc(dao.GetSLBByResourceID, func(_ *db.EngineWrapper, _ string, _ bool) (*db.Slbs, error) {
// 		return nil, fmt.Errorf("slb not exist")
// 	})
// 	slbServer := SLBsServer{}

// 	p.ApplyGlobalVar(&db.AZ, "test")

// 	request := &slbv1.ListListenersRequest{
// 		Zone:    "test11",
// 		SlbName: "test",
// 	}

// 	ctx := context.Background()

// 	_, err := slbServer.ListListeners(ctx, request)
// 	assert.NotNil(t, err)

// 	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
// 		"detail": "Get SLB status one fail. get SLB record err.SLB Server get slb DB record fail.errslb not exist",
// 	})
// 	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
// }

func TestGetListenerErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditDeleteSlbListenerError, func(ctx context.Context, resourceID string, request *slbv1.DeleteListenerRequest, response interface{}, reason string) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetSLBByResourceID, func(_ *db.EngineWrapper, _ string, _ bool) (*db.Slbs, error) {
		return nil, fmt.Errorf("slb not exist")
	})
	slbServer := SLBsServer{}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &slbv1.GetListenerRequest{
		Zone: "test11",
	}

	ctx := context.Background()

	_, err := slbServer.GetListener(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.SlbNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}
