package server

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
	ipav1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/ipa/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	utilnet "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils/network"
	"google.golang.org/grpc/codes"
)

// TODO: fixme
/*
var ipaServer *IPAServer

func init() {
	rpConfig := config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))
	pgInfo := fmt.Sprintf("host=%s port=%s user=%s "+
		"password=%s dbname=%s sslmode=disable",
		rpConfig.PG.Host, rpConfig.PG.Port, rpConfig.PG.User, rpConfig.PG.Password, rpConfig.PG.DB)

	orm, err := xorm.NewEngine("postgres", pgInfo)
	if err != nil {
		logrus.Fatalf("PG connection failed: %v, error: %v", pgInfo, err.Error())
	}

	ipaServer = &IPAServer{e: db.NewEngineWrapper(orm)}
}

func TestCreateIPA(t *testing.T) {
	_, err := dao.GetSubnet(ipaServer.e, "c8cf6075-dbca-48f4-8e91-8b804949d052")
	if err != nil {
		err = dao.AddSubnet(ipaServer.e, &db.Subnets{
			SubnetID:    "c8cf6075-dbca-48f4-8e91-8b804949d052",
			CIDR:        "*********/25",
			GatewayIP:   "*********",
			VPCID:       "807cb71d-cfc5-495e-b853-ff24ca96a611",
			Scope:       "SERVICE",
			Provider:    "CONTROLLER",
			NetworkType: "VLAN",
			ReservedIPs: "*********..*********5,*********20..*********27",
			VNI:         101,
		})
		if err != nil {
			t.Error(err)
			return
		}
	}

	for i := 0; i < 3; i++ {
		ipa, err := ipaServer.CreateIPA(context.Background(), &ipav1.CreateIPARequest{
			SubscriptionName:  "e76e8641-c9c2-4be1-aba3-fb56fb90db60",
			ResourceGroupName: "default",
			Zone:              "sh-01",
			SubnetId:          "c8cf6075-dbca-48f4-8e91-8b804949d052",
			Protocol:          "IPv4",
		})
		if err != nil {
			t.Logf("Create ipa failed: %s", err)
			return
		}
		t.Logf("Create IPA %s success", ipa.Id)
	}
}

func TestBindIPA(t *testing.T) {
	_, err := ipaServer.BindIPA(context.Background(), &ipav1.BindingIPARequest{
		Id:   "dc50f504-aae6-454e-b255-c78eaa53ff11",
		Mac:  "96:19:b2:1d:b3:76",
		Ipmi: "************",
	})
	if err != nil {
		t.Logf("Bind ipa failed: %s", err)
		return
	}
	t.Logf("Bind IPA success")
}

func TestUnbindIPA(t *testing.T) {
	_, err := ipaServer.UnbindIPA(context.Background(), &ipav1.BindingIPARequest{
		Id:   "dc50f504-aae6-454e-b255-c78eaa53ff11",
		Mac:  "96:19:b2:1d:b3:76",
		Ipmi: "************",
	})
	if err != nil {
		t.Logf("Unbind ipa failed: %s", err)
		return
	}
	t.Logf("Unbind IPA success")
}

func TestDeleteIPA(t *testing.T) {
	_, err := ipaServer.DeleteIPA(context.Background(), &ipav1.IPAUIDRequest{Id: "4451f0b3-a3f5-4f1c-b4fc-fdde2fad3999"})
	if err != nil {
		t.Logf("Delete ipa failed: %s", err)
		return
	}
	t.Logf("Delete IPA success")
}
*/

func TestListIPAsAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &ipav1.ListIPAsRequest{
		Zone: "test11",
	}

	ipaServer := IPAServer{}
	ctx := context.Background()

	_, err := ipaServer.ListIPAs(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetIPAsAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &ipav1.IPAUIDRequest{
		Zone: "test11",
	}

	ipaServer := IPAServer{}
	ctx := context.Background()

	_, err := ipaServer.GetIPA(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetIPNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	var d *exporter.DBRTMetrics
	p.ApplyMethod(reflect.TypeOf(d), "ObserveDurationAndLog", func(_ *exporter.DBRTMetrics) {})

	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetIPA, func(_ *db.EngineWrapper, _ string) (*db.Ipas, error) {
		return nil, fmt.Errorf("IPA not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &ipav1.IPAUIDRequest{
		Zone: "test",
	}

	ipaServer := IPAServer{}
	ctx := context.Background()

	_, err := ipaServer.GetIPA(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.IpaNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestCreateIPAsParamErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	var d *exporter.DBRTMetrics
	p.ApplyMethod(reflect.TypeOf(d), "ObserveDurationAndLog", func(_ *exporter.DBRTMetrics) {})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &ipav1.CreateIPARequest{
		Zone:     "test",
		Protocol: utilnet.ProtocolIPv6,
	}

	ipaServer := IPAServer{}
	ctx := context.Background()

	_, err := ipaServer.CreateIPA(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "only support ipv4",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteIPAsInUseErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	var d *exporter.DBRTMetrics
	p.ApplyMethod(reflect.TypeOf(d), "ObserveDurationAndLog", func(_ *exporter.DBRTMetrics) {})

	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetIPA, func(_ *db.EngineWrapper, _ string) (*db.Ipas, error) {
		return &db.Ipas{
			State: ipav1.IPA_BINDING.String(),
		}, nil
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &ipav1.IPAUIDRequest{
		Zone: "test",
	}

	ipaServer := IPAServer{}
	ctx := context.Background()

	_, err := ipaServer.DeleteIPA(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Unavailable, errors.IpaInUse, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestBindIPAErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	var d *exporter.DBRTMetrics
	p.ApplyMethod(reflect.TypeOf(d), "ObserveDurationAndLog", func(_ *exporter.DBRTMetrics) {})

	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetIPA, func(_ *db.EngineWrapper, _ string) (*db.Ipas, error) {
		return &db.Ipas{
			State: ipav1.IPA_FAILED.String(),
		}, nil
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &ipav1.BindingIPARequest{
		Zone: "test",
	}

	ipaServer := IPAServer{}
	ctx := context.Background()

	_, err := ipaServer.BindIPA(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.IpaBoundFailed, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestUpdateIPAErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	var d *exporter.DBRTMetrics
	p.ApplyMethod(reflect.TypeOf(d), "ObserveDurationAndLog", func(_ *exporter.DBRTMetrics) {})

	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetIPA, func(_ *db.EngineWrapper, _ string) (*db.Ipas, error) {
		return &db.Ipas{
			State: ipav1.IPA_FAILED.String(),
		}, nil
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &ipav1.UpdateIPARequest{
		Zone:        "test",
		Id:          "",
		OriginalMac: "",
	}

	ipaServer := IPAServer{}
	ctx := context.Background()

	_, err := ipaServer.UpdateIPA(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "invalid request with empty id and mac",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestUnBindIPAErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	var d *exporter.DBRTMetrics
	p.ApplyMethod(reflect.TypeOf(d), "ObserveDurationAndLog", func(_ *exporter.DBRTMetrics) {})

	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetIPA, func(_ *db.EngineWrapper, _ string) (*db.Ipas, error) {
		return &db.Ipas{
			State: ipav1.IPA_FAILED.String(),
		}, nil
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &ipav1.BindingIPARequest{
		Zone: "test",
	}

	ipaServer := IPAServer{}
	ctx := context.Background()

	_, err := ipaServer.UnbindIPA(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.IpaUnboundFailed, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}
