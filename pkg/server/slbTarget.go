package server

import (
	"context"
	"fmt"
	"reflect"
	"sort"
	"strings"

	"github.com/sirupsen/logrus"
	netControllerV1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netControllerApis "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/controllers/apis"
	netControllerSlbSdk "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/controllers/slb/sdk"
	slbv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/slb/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	utilnet "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils/network"
	"google.golang.org/grpc/codes"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func (s SLBsServer) TargetGroupAddTargets(
	ctx context.Context, request *slbv1.TargetGroupAddTargetsRequest,
) (response *slbv1.TargetGroupAddTargetsResponse, err error) {
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics(request.TargetGroupName, "TargetGroup", "TargetGroupAddTargets")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditAddSlbTargetError(ctx, request.TargetGroupName, request, struct{}{}, err.Error())
		} else {
			cloudAuditAddSlbTargetSuccess(ctx, request.TargetGroupName, request, struct{}{})
		}
	}()

	err = s.addTargetsRequestFill(ctx, request)
	if err != nil {
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbTargetGroupNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DbErr, errors.Domain, nil, nil)
	}
	logrus.Info(
		"TargetGroup add targets start", "slb", request.SlbName, "TargetGroup", request.TargetGroupName, "request", request,
	)
	slbRecord, targetGroupRecord, err := s.targetGroupAddTargetsGetWithCheck(ctx, request)
	if err != nil {
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	err = s.createTargetCR(ctx, request, slbRecord, targetGroupRecord)
	if err != nil {
		logrus.Errorf(
			"TargetGroup add targets fail, slb:%s targetGroup:%s create CR err:%s",
			request.SlbName, request.TargetGroupName, err,
		)
		return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.CrErr, errors.Domain, nil, nil)
	}

	err = s.createTarget(ctx, request, slbRecord, targetGroupRecord)
	if err != nil {
		logrus.Errorf(
			"TargetGroup add targets fail, slb:%s targetGroup:%s create err:%s",
			request.SlbName, request.TargetGroupName, err,
		)
		return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DbErr, errors.Domain, nil, nil)
	}

	engine := resourceprovider.BosonProvider.Engine
	targetRecords, err := dao.GetSLBTargetsByTargetGroupID(engine, targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID)
	if err != nil {
		logrus.Errorf(
			"TargetGroup add targets fail. slb:%s targetGroup:%s get Target dbs err:%s",
			request.SlbName, request.TargetGroupName, err,
		)
		return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DbErr, errors.Domain, nil, nil)
	}
	currentTargets := s.generateTargets(targetRecords)
	response = &slbv1.TargetGroupAddTargetsResponse{
		CurrTargets: currentTargets,
	}

	return response, nil
}

func (s SLBsServer) TargetGroupRemoveTargets(
	ctx context.Context, request *slbv1.TargetGroupRemoveTargetsRequest,
) (response *slbv1.TargetGroupRemoveTargetsResponse, err error) {
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics(request.TargetGroupName, "TargetGroup", "TargetGroupRemoveTargets")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditRemoveSlbTargetError(ctx, request.TargetGroupName, request, struct{}{}, err.Error())
		} else {
			cloudAuditRemoveSlbTargetSuccess(ctx, request.TargetGroupName, request, struct{}{})
		}
	}()

	logrus.Info(
		"TargetGroup remove targets start",
		"slb", request.SlbName, "TargetGroup", request.TargetGroupName, "request", request,
	)
	slbRecord, targetGroupRecord, err := s.targetGroupRemoveTargetsGetWithCheck(ctx, request)
	if err != nil {
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbTargetGroupNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	err = s.deleteTargetCR(ctx, request, slbRecord, targetGroupRecord)
	if err != nil {
		logrus.Errorf(
			"TargetGroup remove targets fail, slb:%s targetGroup:%s delete CR err:%s",
			request.SlbName, request.TargetGroupName, err,
		)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	// DB deleting
	engine := resourceprovider.BosonProvider.Engine
	for _, targetID := range request.TargetIds {
		err = dao.SetTargetState(
			engine, slbRecord.SlbID, targetGroupRecord.TargetGroupID, targetID,
			targetStateName(slbv1.Target_State(slbv1.Target_REMOVING)),
		)
		if err != nil {
			logrus.Errorf(
				"TargetGroup remove targets fail, slb:%s targetGroup:%s target:%s set db deleting err:%s",
				request.SlbName, request.TargetGroupName, targetID, err,
			)
			continue
		}
	}

	targetRecords, err := dao.GetSLBTargetsByTargetGroupID(engine, targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID)
	if err != nil {
		logrus.Errorf(
			"TargetGroup remove targets fail. slb:%s targetGroup:%s get Target dbs err:%s",
			request.SlbName, request.TargetGroupName, err,
		)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	currentTargets := s.generateTargets(targetRecords)
	response = &slbv1.TargetGroupRemoveTargetsResponse{
		CurrTargets: currentTargets,
	}

	return response, nil
}

func (s SLBsServer) TargetsUpdate(
	ctx context.Context, request *slbv1.TargetsUpdateRequest,
) (response *slbv1.TargetGroupTargets, err error) {
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics(request.TargetGroupName, "TargetGroup", "TargetsUpdate")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditUpdateSlbTargetError(ctx, request.TargetGroupName, request, struct{}{}, err.Error())
		} else {
			cloudAuditUpdateSlbTargetSuccess(ctx, request.TargetGroupName, request, struct{}{})
		}
	}()

	logrus.Infof("update targets:%+v starting\n", request)

	slbRecord, targetGroupRecord, targetsNew, err := s.targetGroupUpdateTargetsGetWithCheck(ctx, request)
	if err != nil {
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbTargetGroupNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	err = s.updateTargetCR(ctx, request, slbRecord, targetGroupRecord, targetsNew)
	if err != nil {
		logrus.Errorf(
			"TargetGroup update targets fail, slb:%s targetGroup:%s update CR err:%s",
			request.SlbName, request.TargetGroupName, err,
		)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	err = s.updateTarget(ctx, request, slbRecord, targetGroupRecord, targetsNew)
	if err != nil {
		logrus.Errorf(
			"TargetGroup remove targets fail, slb:%s targetGroup:%s delete err:%s",
			request.SlbName, request.TargetGroupName, err,
		)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	engine := resourceprovider.BosonProvider.Engine
	targetRecords, err := dao.GetSLBTargetsByTargetGroupID(engine, targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID)
	if err != nil {
		logrus.Errorf(
			"TargetGroup update targets fail. slb:%s targetGroup:%s get Target dbs err:%s",
			request.SlbName, request.TargetGroupName, err,
		)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	currentTargets := s.generateTargets(targetRecords)
	response = &slbv1.TargetGroupTargets{
		CurrTargets: currentTargets,
	}

	return response, nil
}

func (s SLBsServer) ListTargets(
	ctx context.Context, request *slbv1.ListTargetsRequest,
) (*slbv1.ListTargetsResponse, error) {
	var err error = nil
	var slbRecord *db.Slbs
	var targetGroupRecord *db.SlbTargetGroups
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics("", "Targets", "ListTargets")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	slbRecord, targetGroupRecord, err = s.listTargetsCheckAndGet(ctx, request)
	if err != nil {
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbTargetGroupNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	engine := resourceprovider.BosonProvider.Engine
	targetRecords, err := dao.GetSLBTargetsByTargetGroupID(engine, slbRecord.SlbID, targetGroupRecord.TargetGroupID)
	if err != nil {
		logrus.Errorf(
			"Targets list fail. slb:%s targetGroup:%s get Target dbs err:%s",
			request.SlbName, request.TargetGroupName, err,
		)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	currentTargets, err := s.generateTargetInfos(slbRecord, targetGroupRecord, targetRecords)
	if err != nil {
		logrus.Errorf(
			"Targets list fail. slb:%s targetGroup:%s get Target info err:%s",
			request.SlbName, request.TargetGroupName, err,
		)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	response := &slbv1.ListTargetsResponse{
		TargetInfos: currentTargets,
	}

	response.NextPageToken = "0"
	response.TotalSize = int32(len(response.TargetInfos))

	return response, nil
}

func (s SLBsServer) GetTarget(ctx context.Context, request *slbv1.GetTargetRequest) (*slbv1.Target, error) {
	var err error = nil
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics("", "Targets", "GetTarget")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	engine := resourceprovider.BosonProvider.Engine
	targetResourceID := dao.TargetResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName, request.TargetName)
	targetRecord, err := dao.GetSLBTargetByResourceID(engine, targetResourceID)
	if err != nil {
		logrus.Errorf(
			"Target get fail. slb:%s targetGroup:%s get Target dbs err:%s",
			request.SlbName, request.TargetGroupName, err,
		)
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbTargetNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	response := s.generateTarget(targetRecord)
	return response, nil
}

func (s SLBsServer) targetGroupAddTargetsGetWithCheck(
	ctx context.Context, request *slbv1.TargetGroupAddTargetsRequest,
) (*db.Slbs, *db.SlbTargetGroups, error) {
	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		return nil, nil, err
	}

	targetGroupRecord, err := s.getTargetGroupRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName,
	)
	if err != nil {
		logrus.Errorf("add targets fail. get slb:%s targetGroup:%s err:%s", slbRecord.SlbID, request.TargetGroupName, err)
		return nil, nil, err
	}

	err = s.targetQuota(slbRecord, targetGroupRecord, request)
	if err != nil {
		logrus.Errorf(
			"add targets fail. slb:%s targetGroup:%s quota check err:%s", slbRecord.SlbID, request.TargetGroupName, err,
		)
		return nil, nil, err
	}

	// .Type
	if targetGroupRecord.Type == targetTypeName(slbv1.TargetProperties_CCI_DEPLOYMENT_INSTANCE) {
		err := fmt.Errorf(
			"target group %s type %s is not permit add targets manual",
			targetGroupRecord.TargetGroupID, targetGroupRecord.Type,
		)
		logrus.Errorln(err)
		return nil, nil, err
	}

	// .Target
	if request.Targets == nil || len(request.Targets) == 0 {
		err := fmt.Errorf(
			"slb:%s target group:%s add targets check error, params Targets is nil",
			slbRecord.SlbID, request.TargetGroupName,
		)
		logrus.Error(err.Error())
		return nil, nil, err
	}

	targetsMap, err := s.generateTargetsMap(targetGroupRecord)
	if err != nil {
		logrus.Errorln("generate targets map err", err)
		return nil, nil, err
	}
	engine := resourceprovider.BosonProvider.Engine
	subnets, err := dao.ListSubnets(engine, slbRecord.VpcID)
	if err != nil {
		logrus.Errorf("add target fail. slb%s get vpc:%s subnets error:%s", slbRecord.Name, slbRecord.VpcID, err)
		return nil, nil, err
	}
	for _, target := range request.Targets {
		// 存在性检查
		if _, ok := targetsMap[target.Uid]; ok {
			err := fmt.Errorf(
				"add target fail. slb:%s targetGroup:%s target:%s check already exist",
				slbRecord.SlbID, request.TargetGroupName, target.Name,
			)
			logrus.Errorln(err)
			return nil, nil, err
		}

		// 合法性检查
		err = s.targetAddCheck(targetGroupRecord, target, subnets)
		if err != nil {
			logrus.Errorf(
				"add target fail. slb:%s targetGroup:%s target:%s check fail, err:%s",
				slbRecord.SlbID, request.TargetGroupName, target.Name, err,
			)
			return nil, nil, err
		}
	}

	return slbRecord, targetGroupRecord, nil
}

func (s SLBsServer) generateTargetsMap(targetGroupRecord *db.SlbTargetGroups) (map[string]*db.SlbTargets, error) {
	targetsMap := make(map[string]*db.SlbTargets)
	engine := resourceprovider.BosonProvider.Engine
	targetRecords, err := dao.GetSLBTargetsByTargetGroupID(engine, targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID)
	if err != nil {
		logrus.Errorf("generate targets map fail. get Target dbs err:%s", err)
		return nil, err
	}

	for _, targetRecord := range targetRecords {
		targetsMap[targetRecord.TargetID] = targetRecord
	}

	return targetsMap, nil
}

func (s SLBsServer) targetAddCheck(targetGroupRecord *db.SlbTargetGroups, target *slbv1.Target, subnets []*db.Subnets) error {
	// .Target.Properties.Ipv4Address
	// ip check, 合法ip，且在vpc overlay subnet
	if target.Properties.Type == slbv1.TargetProperties_BMS_INSTANCE {
		if target.Properties.Ipv4Address != "" {
			dataInstance := false

			for _, subnet := range subnets {
				if subnet.Provider == vpc.SubnetProperties_CONTROLLER.String() && subnet.Scope == vpc.SubnetProperties_DATA.String() {
					if utilnet.CIDRContainIP(subnet.CIDR, target.Properties.Ipv4Address) {
						dataInstance = true
						break
					}
				}
			}

			if !dataInstance {
				err := fmt.Errorf("target %s type %s ipaddress:%s is invalid, only data bms instance", target.Uid, target.Properties.Type, target.Properties.Ipv4Address)
				logrus.Errorln(err.Error())
				return err
			}
		}
	}

	// .Target.Properties.Port
	// TODO

	// .Target.Properties.Weight
	// 不支持

	// .Target.Properties.Type
	if !targetGroupAddTargetTypeMatch(targetGroupRecord.Type, targetTypeName(target.Properties.Type)) {
		err := fmt.Errorf(
			"target %s type :%s is error, not match TargetGroup:%s type:%s",
			target.Uid, target.Properties.Type, targetGroupRecord.TargetGroupID, targetGroupRecord.Type,
		)
		logrus.Errorln(err)
		return err
	}

	// .Target.Properties.InstanceName
	// TODO

	return nil
}

func (s SLBsServer) createTargetCR(
	ctx context.Context, request *slbv1.TargetGroupAddTargetsRequest,
	slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups,
) error {
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSLB")
	slbOld, err := resourceprovider.BosonProvider.BosonNetClient.SLBs().Get(
		ctx, resourceprovider.SlbCRName(slbRecord.Name), metav1.GetOptions{},
	)
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("TargetGroup CR create fail.", "GetSLB err:", err.Error())
		return err
	}

	slb := slbOld.DeepCopy()
	hit := false
	index := 0
	var targetGroupTmp netControllerV1.TargetGroup
	for index, targetGroupTmp = range slb.Spec.TargetGroups {
		if targetGroupTmp.TargetGroupID == targetGroupRecord.TargetGroupID {
			hit = true
			break
		}
	}
	if !hit {
		err := fmt.Errorf(
			"create target cr fail, slb%s can not find targetGroup:%s", slbRecord.SlbID, targetGroupRecord.TargetGroupID,
		)
		logrus.Error(err.Error())
		return err
	}

	targetGroupNew := s.generateTargetGroupCRwithTargetsAdd(ctx, slb.Spec.TargetGroups[index].DeepCopy(), request)
	slb.Spec.TargetGroups[index] = *targetGroupNew
	if reflect.DeepEqual(slb.Spec, slbOld.Spec) {
		return nil
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateSLB")
	_, err = resourceprovider.BosonProvider.BosonNetClient.SLBs().Update(ctx, slb, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Target CR create fail. err: ", err.Error())
		return err
	}

	return nil
}

func (s SLBsServer) generateTargetGroupCRwithTargetsAdd(
	ctx context.Context, targetGroupOld *netControllerV1.TargetGroup,
	request *slbv1.TargetGroupAddTargetsRequest,
) *netControllerV1.TargetGroup {
	targetGroupNew := targetGroupOld.DeepCopy()

	for _, target := range request.Targets {
		targetCR := generateTargetCR(target)
		targetGroupNew.Targets = append(targetGroupNew.Targets, targetCR)
	}
	targetGroupNew.Targets = sortTarget(targetGroupNew.Targets)

	return targetGroupNew
}

func generateTargetCR(target *slbv1.Target) *netControllerV1.Target {
	targetCR := netControllerV1.Target{
		TargetID:     target.Uid,
		IPAddress:    target.Properties.Ipv4Address,
		Port:         int(target.Properties.Port),
		Weight:       int(target.Properties.Weight),
		InstanceName: target.Properties.InstanceName,
	}
	return &targetCR
}

func (s SLBsServer) createTarget(
	ctx context.Context, request *slbv1.TargetGroupAddTargetsRequest,
	slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups,
) error {
	engine := resourceprovider.BosonProvider.Engine

	targetRecords := []*db.SlbTargets{}
	for _, target := range request.Targets {
		targetRecord := generateTargetDB(target, slbRecord, targetGroupRecord.TargetGroupID)
		targetRecords = append(targetRecords, targetRecord)
	}

	err := dao.InsertTargets(engine, targetRecords)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	return nil
}

func generateTargetDB(
	target *slbv1.Target, slbRecord *db.Slbs, targetGroupId string,
) *db.SlbTargets {
	targetRecord := &db.SlbTargets{
		TargetID:      target.Uid,
		Name:          target.Name,
		Zone:          target.Zone,
		State:         targetStateName(slbv1.Target_ADDING),
		DisplayName:   target.DisplayName,
		Description:   target.Description,
		ResourceType:  target.ResourceType,
		SlbID:         slbRecord.SlbID,
		TargetGroupID: targetGroupId,
		IpAddress:     target.Properties.Ipv4Address,
		Port:          target.Properties.Port,
		Weight:        target.Properties.Weight,
		Type:          targetTypeName(target.Properties.Type),
		InstanceName:  target.Properties.InstanceName,
		ID:            target.Id,
		CreatorID:     target.CreatorId,
		OwnerID:       target.OwnerId,
		TenantID:      target.TenantId,
		CreateTime:    target.CreateTime.AsTime(),
		UpdateTime:    target.UpdateTime.AsTime(),
		Deleted:       false,
		Tags:          "",
	}
	return targetRecord
}

func (s SLBsServer) generateTargets(targetRecords []*db.SlbTargets) []*slbv1.Target {
	targets := []*slbv1.Target{}
	for _, targetRecord := range targetRecords {
		target := s.generateTarget(targetRecord)
		targets = append(targets, target)
	}

	return targets
}

func (s SLBsServer) generateTarget(targetRecord *db.SlbTargets) *slbv1.Target {
	target := &slbv1.Target{
		Id:           targetRecord.ID,
		Uid:          targetRecord.TargetID,
		Name:         targetRecord.Name,
		DisplayName:  targetRecord.DisplayName,
		Description:  targetRecord.Description,
		ResourceType: targetRecord.ResourceType,
		CreatorId:    targetRecord.CreatorID,
		OwnerId:      targetRecord.OwnerID,
		TenantId:     targetRecord.TenantID,
		Zone:         targetRecord.Zone,
		State:        targetStateValue(targetRecord.State),
		Tags:         nil,
		Properties:   nil,
		Deleted:      targetRecord.Deleted,
		CreateTime:   timestamppb.New(targetRecord.CreateTime),
		UpdateTime:   timestamppb.New(targetRecord.UpdateTime),
	}

	target.Properties = &slbv1.TargetProperties{
		Ipv4Address:  targetRecord.IpAddress,
		Port:         targetRecord.Port,
		Weight:       targetRecord.Weight,
		Type:         targetTypeValue(targetRecord.Type),
		InstanceName: targetRecord.InstanceName,
	}
	return target
}

func (s SLBsServer) generateTargetInfos(slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups, targetRecords []*db.SlbTargets) ([]*slbv1.TargetInfo, error) {
	slbDpClient := resourceprovider.BosonProvider.SlbDataplane
	targetStatusList, err := slbDpClient.ListTargets(slbRecord.Name, targetGroupRecord.TargetGroupID)
	if err != nil {
		logrus.Error("generate target infos fail, get target dp status error", "target group", targetGroupRecord.TargetGroupID)
		return nil, err
	}

	// key:targetID
	statusMap := make(map[string]*netControllerSlbSdk.SlbTargetStatus)
	for index := range targetStatusList {
		logrus.Info(fmt.Sprintf(
			"list slb:%s tg:%s target:%s status:%+v",
			slbRecord.Name, targetGroupRecord.TargetGroupID,
			targetStatusList[index].TargetId, targetStatusList[index].HcStatus,
		))

		statusMap[targetStatusList[index].TargetId] = targetStatusList[index]
	}

	logrus.Info("statusMap", statusMap)

	targetInfos := []*slbv1.TargetInfo{}
	for _, targetRecord := range targetRecords {
		target := s.generateTarget(targetRecord)
		status := &slbv1.TargetStatus{
			HealthCheck: &slbv1.TargetHealthCheckStatus{
				State: slbv1.TargetHealthCheckStatus_INITIAL,
			},
		}

		logrus.Info("targetID", targetRecord.TargetID, "status", statusMap[targetRecord.TargetID])
		if _, ok := statusMap[targetRecord.TargetID]; ok {
			status = s.generateTargetStatus(targetRecord, statusMap[targetRecord.TargetID])
		}

		targetInfos = append(targetInfos, &slbv1.TargetInfo{
			Target: target,
			Status: status,
		})
	}

	return targetInfos, err
}

func (s SLBsServer) generateTargetStatus(targetRecord *db.SlbTargets, status *netControllerSlbSdk.SlbTargetStatus) *slbv1.TargetStatus {
	return &slbv1.TargetStatus{
		HealthCheck: &slbv1.TargetHealthCheckStatus{
			State: s.toTargetState(status.HcStatus.State),
		},
	}
}

func (s SLBsServer) toTargetState(state netControllerApis.SlbTargetHealthCheckState) slbv1.TargetHealthCheckStatus_State {
	logrus.Info(fmt.Sprintf("state: %s to TargetState", state))
	switch state {
	case netControllerApis.SlbTargetHealthCheckState_initial:
		return slbv1.TargetHealthCheckStatus_INITIAL
	case netControllerApis.SlbTargetHealthCheckState_health:
		return slbv1.TargetHealthCheckStatus_HEALTH
	case netControllerApis.SlbTargetHealthCheckState_unhealth:
		return slbv1.TargetHealthCheckStatus_UNHEALTH
	case netControllerApis.SlbTargetHealthCheckState_disable:
		return slbv1.TargetHealthCheckStatus_DISABLE
	}

	return slbv1.TargetHealthCheckStatus_INITIAL
}

func targetTypeName(targetType slbv1.TargetProperties_SLBTargetType) string {
	return slbv1.TargetProperties_SLBTargetType_name[int32(targetType)]
}

func targetTypeValue(targetType string) slbv1.TargetProperties_SLBTargetType {
	return slbv1.TargetProperties_SLBTargetType(
		slbv1.TargetProperties_SLBTargetType_value[targetType],
	)
}

func targetStateValue(state string) slbv1.Target_State {
	return slbv1.Target_State(slbv1.Target_State_value[state])
}

func targetStateName(state slbv1.Target_State) string {
	return slbv1.Target_State_name[int32(state)]
}

func (s SLBsServer) targetGroupRemoveTargetsGetWithCheck(
	ctx context.Context, request *slbv1.TargetGroupRemoveTargetsRequest,
) (*db.Slbs, *db.SlbTargetGroups, error) {
	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		return nil, nil, err
	}

	targetGroupRecord, err := s.getTargetGroupRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName,
	)
	if err != nil {
		logrus.Errorf("remove targets fail. get slb:%s targetGroup:%s err:%s", slbRecord.SlbID, request.TargetGroupName, err)
		return nil, nil, err
	}

	// .Type
	if targetGroupRecord.Type == targetTypeName(slbv1.TargetProperties_CCI_DEPLOYMENT_INSTANCE) {
		err := fmt.Errorf(
			"target group %s type %s is not permit remove targets manual",
			targetGroupRecord.TargetGroupID, targetGroupRecord.Type,
		)
		logrus.Errorln(err)
		return nil, nil, err
	}

	return slbRecord, targetGroupRecord, nil
}

func (s SLBsServer) deleteTargetCR(
	ctx context.Context, request *slbv1.TargetGroupRemoveTargetsRequest,
	slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups,
) error {
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSLB")
	slbOld, err := resourceprovider.BosonProvider.BosonNetClient.SLBs().Get(
		ctx, resourceprovider.SlbCRName(slbRecord.Name), metav1.GetOptions{},
	)
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("TargetGroup CR create fail.", "GetSLB err:", err.Error())
		return err
	}

	slb := slbOld.DeepCopy()
	hit := false
	index := 0
	var targetGroupTmp netControllerV1.TargetGroup
	for index, targetGroupTmp = range slb.Spec.TargetGroups {
		if targetGroupTmp.TargetGroupID == targetGroupRecord.TargetGroupID {
			hit = true
			break
		}
	}
	if !hit {
		err := fmt.Errorf(
			"delete target cr fail, slb%s can not find targetGroup:%s", slbRecord.SlbID, targetGroupRecord.TargetGroupID,
		)
		logrus.Error(err.Error())
		return err
	}

	targetGroupNew := s.generateTargetGroupCRwithTargetsRemove(ctx, slb.Spec.TargetGroups[index].DeepCopy(), request)
	slb.Spec.TargetGroups[index] = *targetGroupNew
	if reflect.DeepEqual(slb.Spec, slbOld.Spec) {
		return nil
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateSLB")
	_, err = resourceprovider.BosonProvider.BosonNetClient.SLBs().Update(ctx, slb, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Target CR delete fail. err: ", err.Error())
		return err
	}

	return nil
}

func (s SLBsServer) generateTargetGroupCRwithTargetsRemove(
	ctx context.Context, targetGroupOld *netControllerV1.TargetGroup,
	request *slbv1.TargetGroupRemoveTargetsRequest,
) *netControllerV1.TargetGroup {
	targetGroupNew := targetGroupOld.DeepCopy()
	targetGroupNew.Targets = []*netControllerV1.Target{}

	// key: targetId
	targetsDeleteMap := make(map[string]bool)
	for _, targetID := range request.TargetIds {
		targetsDeleteMap[targetID] = true
	}

	for _, target := range targetGroupOld.Targets {
		if _, ok := targetsDeleteMap[target.TargetID]; ok {
			continue
		}

		targetGroupNew.Targets = append(targetGroupNew.Targets, target.DeepCopy())
	}
	targetGroupNew.Targets = sortTarget(targetGroupNew.Targets)

	return targetGroupNew
}

func ReleaseTarget(
	slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups, targetID string,
) error {
	engine := resourceprovider.BosonProvider.Engine

	logrus.Infof(
		"Delete slb:%s target group:%s target:%s",
		slbRecord.SlbID, targetGroupRecord.TargetGroupID, targetID,
	)
	err := dao.DeleteTargets(
		engine, slbRecord.SlbID, targetGroupRecord.TargetGroupID,
		[]string{targetID}, targetStateName(slbv1.Target_REMOVED),
	)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	return nil
}

func (s SLBsServer) targetGroupUpdateTargetsGetWithCheck(
	ctx context.Context, request *slbv1.TargetsUpdateRequest,
) (*db.Slbs, *db.SlbTargetGroups, []*slbv1.Target, error) {
	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		return nil, nil, nil, err
	}

	targetGroupRecord, err := s.getTargetGroupRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName,
	)
	if err != nil {
		logrus.Errorf(
			"update targets fail. get slb:%s targetGroup:%s err:%s",
			slbRecord.SlbID, request.TargetGroupName, err,
		)
		return nil, nil, nil, err
	}

	// .Type
	if targetGroupRecord.Type == targetTypeName(slbv1.TargetProperties_CCI_DEPLOYMENT_INSTANCE) {
		err := fmt.Errorf(
			"target group %s type %s is not permit update targets manual",
			targetGroupRecord.TargetGroupID, targetGroupRecord.Type,
		)
		logrus.Errorln(err)
		return nil, nil, nil, err
	}

	// .Target
	targets := []*slbv1.Target{}
	targetsMap, err := s.generateTargetsMap(targetGroupRecord)
	if err != nil {
		logrus.Errorln("generate targets map err", err)
		return nil, nil, nil, err
	}
	for _, targetUpdate := range request.TargetGroupTargets {
		// 存在性检查
		targetRecord, ok := targetsMap[targetUpdate.Uid]
		if !ok {
			err := fmt.Errorf(
				"update target fail. slb:%s targetGroup:%s target:%s check not exist",
				slbRecord.SlbID, request.TargetGroupName, targetUpdate.Uid,
			)
			logrus.Errorln(err)
			return nil, nil, nil, err
		}

		// 合法性检查
		target, err := s.targetUpdateGet(targetGroupRecord, targetRecord, targetUpdate)
		if err != nil {
			logrus.Errorf(
				"update target fail. slb:%s targetGroup:%s target:%s check fail, err:%s",
				slbRecord.SlbID, request.TargetGroupName, target.Uid, err,
			)
			return nil, nil, nil, err
		}

		targets = append(targets, target)
	}

	return slbRecord, targetGroupRecord, targets, nil
}

func (s SLBsServer) targetUpdateGet(
	targetGroupRecord *db.SlbTargetGroups, targetRecord *db.SlbTargets, targetUpdate *slbv1.TargetUpdate,
) (*slbv1.Target, error) {
	target := s.generateTarget(targetRecord)

	// .Port
	target.Properties.Port = targetUpdate.Properties.Port

	return target, nil
}

func (s SLBsServer) updateTargetCR(
	ctx context.Context, request *slbv1.TargetsUpdateRequest,
	slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups, targets []*slbv1.Target,
) error {
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSLB")
	slbOld, err := resourceprovider.BosonProvider.BosonNetClient.SLBs().Get(
		ctx, resourceprovider.SlbCRName(slbRecord.Name), metav1.GetOptions{},
	)
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("TargetGroup CR create fail.", "GetSLB err:", err.Error())
		return err
	}

	slb := slbOld.DeepCopy()
	hit := false
	index := 0
	var targetGroupTmp netControllerV1.TargetGroup
	for index, targetGroupTmp = range slb.Spec.TargetGroups {
		if targetGroupTmp.TargetGroupID == targetGroupRecord.TargetGroupID {
			hit = true
			break
		}
	}
	if !hit {
		err := fmt.Errorf(
			"update target cr fail, slb%s can not find targetGroup:%s", slbRecord.SlbID, targetGroupRecord.TargetGroupID,
		)
		logrus.Error(err.Error())
		return err
	}

	targetGroupNew := s.generateTargetGroupCRwithTargetsUpdate(ctx, slb.Spec.TargetGroups[index].DeepCopy(), targets)
	slb.Spec.TargetGroups[index] = *targetGroupNew
	if reflect.DeepEqual(slb.Spec, slbOld.Spec) {
		return nil
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateSLB")
	_, err = resourceprovider.BosonProvider.BosonNetClient.SLBs().Update(ctx, slb, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Target CR delete fail. err: ", err.Error())
		return err
	}

	return nil
}

func (s SLBsServer) generateTargetGroupCRwithTargetsUpdate(
	ctx context.Context, targetGroupOld *netControllerV1.TargetGroup, targets []*slbv1.Target,
) *netControllerV1.TargetGroup {
	targetGroupNew := targetGroupOld.DeepCopy()

	// key: target.Uid
	targetsUpdateMap := make(map[string]*slbv1.Target)
	for _, target := range targets {
		targetsUpdateMap[target.Uid] = target
	}

	for index, targetTmp := range targetGroupNew.Targets {
		targetUpdate, ok := targetsUpdateMap[targetTmp.TargetID]
		if !ok {
			continue
		}

		targetNew := generateTargetCR(targetUpdate)
		targetGroupNew.Targets[index] = targetNew
	}

	return targetGroupNew
}

func (s SLBsServer) updateTarget(
	ctx context.Context, request *slbv1.TargetsUpdateRequest,
	slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups, targetsNew []*slbv1.Target,
) error {
	engine := resourceprovider.BosonProvider.Engine

	targetRecords, err := dao.GetSLBTargetsByTargetGroupID(engine, slbRecord.SlbID, targetGroupRecord.TargetGroupID)
	if err != nil {
		logrus.Errorf("update target err:%s", err)
		return err
	}

	// key: target.Uid
	targetsNewMap := make(map[string]*slbv1.Target)
	for _, target := range targetsNew {
		targetsNewMap[target.Uid] = target
	}

	targetRecordsUpdate := []db.SlbTargets{}
	for _, targetRecord := range targetRecords {
		target, ok := targetsNewMap[targetRecord.TargetID]
		if !ok {
			continue
		}

		// .DisplayName
		targetRecord.DisplayName = target.DisplayName

		// .Port
		targetRecord.Port = target.Properties.Port

		targetRecordsUpdate = append(targetRecordsUpdate, *targetRecord)
	}

	if len(targetRecordsUpdate) > 0 {
		// TODO 批处理
		for index := range targetRecordsUpdate {
			err = dao.UpdateTarget(engine, &targetRecordsUpdate[index])
			if err != nil {
				// TODO 回滚
				logrus.Errorf("update target db err:%s", err)
				return err
			}
		}
	}

	return nil
}

func (s SLBsServer) listTargetsCheckAndGet(
	ctx context.Context, request *slbv1.ListTargetsRequest,
) (*db.Slbs, *db.SlbTargetGroups, error) {
	if request.SlbName == "" {
		err := fmt.Errorf("listTargetsCheckAndGet fail. slbName is nil")
		logrus.Error(err)
		return nil, nil, err
	}

	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		// TODO 处理下日志
		logrus.Errorln("Get SLB status one fail. get SLB record err.", err.Error())
		return nil, nil, err
	}

	targetGroupRecord, err := s.getTargetGroupRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName,
	)
	if err != nil {
		logrus.Errorln("Get SLB status one fail. get SLB record err.", err.Error())
		return nil, nil, err
	}

	return slbRecord, targetGroupRecord, nil
}

func (s SLBsServer) targetQuota(
	slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups, request *slbv1.TargetGroupAddTargetsRequest,
) error {
	engine := resourceprovider.BosonProvider.Engine
	vpcQuota, err := dao.GetVpcQuotaByVpcID(engine, slbRecord.VpcID)
	if err != nil {
		logrus.Errorf(
			"vpc:%s slb:%s targetGroup:%s add targets fail, get quota err:%s",
			slbRecord.VpcID, slbRecord.SlbID, request.TargetGroupName, err,
		)
		return err
	}

	records, err := dao.GetSLBTargetsByTargetGroupID(engine, slbRecord.SlbID, targetGroupRecord.TargetGroupID)
	if err != nil {
		logrus.Errorf(
			"vpc:%s slb:%s targetGroup:%s add targets fail, list targetGroup db err:%s",
			slbRecord.VpcID, slbRecord.SlbID, request.TargetGroupName, err,
		)
		return err
	}
	count := len(records)
	if count >= int(vpcQuota.TargetPerTargetGroup) {
		err := fmt.Errorf(
			"vpc:%s slb:%s targetGroup:%s add targets fail, targets limit exceeded, quota:%d current count:%d",
			slbRecord.VpcID, slbRecord.SlbID, targetGroupRecord.TargetGroupID, vpcQuota.TargetPerTargetGroup, count,
		)
		logrus.Errorln(err)
		return err
	}

	return nil
}

// TODO: fixme: line1093: nil pointer
func (s SLBsServer) addTargetsRequestFill(ctx context.Context, request *slbv1.TargetGroupAddTargetsRequest) error {
	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		logrus.Errorf("add targets fail for fill. get slb:%s err:%s", slbRecord.SlbID, err)
		return err
	}

	targetGroupRecord, err := s.getTargetGroupRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName,
	)
	if err != nil {
		logrus.Errorf("add targets fail for fill. get slb:%s targetGroup:%s err:%s", slbRecord.SlbID, request.TargetGroupName, err)
		return err
	}

	// 数据面资源，需要自己管理 ID、uid 等信息
	for index := range request.Targets {
		request.Targets[index] = targetRequestFillCommonWithoutTargetID(
			request.Targets[index],
			targetGroupRecord.ID,
			targetGroupRecord.CreatorID,
			targetGroupRecord.TenantID,
			targetGroupRecord.Zone,
		)
	}

	return nil
}

func sortTarget(targets []*netControllerV1.Target) []*netControllerV1.Target {
	s := targetSlice(targets)
	sort.Sort(s)
	return s
}

type targetSlice []*netControllerV1.Target

func (s targetSlice) Len() int {
	return len(s)
}

func (s targetSlice) Less(i, j int) bool {
	return s[i].TargetID < s[j].TargetID
}

func (s targetSlice) Swap(i, j int) {
	s[i], s[j] = s[j].DeepCopy(), s[i].DeepCopy()
}

func targetGroupAddTargetTypeMatch(tgType string, tType string) bool {
	if tTypes, ok := typePermitMap[tgType]; ok {
		for _, t := range tTypes {
			if t == tType {
				return true
			}
		}
	}

	return false
}

var (
	// key: TargetGroupType, value: []string{TargetType}
	typePermitMap = map[string][]string{
		slbv1.TargetGroupProperties_SLBTargetType_name[int32(slbv1.TargetGroupProperties_CCI_DEPLOYMENT_INSTANCE)]: {
			slbv1.TargetProperties_SLBTargetType_name[int32(slbv1.TargetProperties_CCI_DEPLOYMENT_INSTANCE)],
		},
		slbv1.TargetGroupProperties_SLBTargetType_name[int32(slbv1.TargetGroupProperties_SERVER_INSTANCE)]: {
			slbv1.TargetProperties_SLBTargetType_name[int32(slbv1.TargetProperties_ECS_INSTANCE)],
			slbv1.TargetProperties_SLBTargetType_name[int32(slbv1.TargetProperties_BMS_INSTANCE)],
		},
	}
)
