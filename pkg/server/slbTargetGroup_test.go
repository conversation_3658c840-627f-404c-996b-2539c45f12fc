package server

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
	slbv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/slb/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"google.golang.org/grpc/codes"
)

func TestCreateTargetGroupErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditCreateSlbTargetGroupError, func(ctx context.Context, resourceID string, request *slbv1.CreateTargetGroupRequest, response interface{}, reason string) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetSLBByResourceID, func(_ *db.EngineWrapper, _ string, _ bool) (*db.Slbs, error) {
		return nil, fmt.Errorf("err")
	})
	slbServer := SLBsServer{}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &slbv1.CreateTargetGroupRequest{
		Zone:        "test",
		TargetGroup: &slbv1.TargetGroup{},
	}

	ctx := context.Background()

	_, err := slbServer.CreateTargetGroup(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "err",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestUpdateTargetGroupErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditUpdateSlbTargetGroupError, func(ctx context.Context, resourceID string, request *slbv1.UpdateTargetGroupRequest, response interface{}, reason string) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetSLBByResourceID, func(_ *db.EngineWrapper, _ string, _ bool) (*db.Slbs, error) {
		return nil, fmt.Errorf("slb not exist")
	})
	slbServer := SLBsServer{}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &slbv1.UpdateTargetGroupRequest{
		Zone: "test11",
	}

	ctx := context.Background()

	_, err := slbServer.UpdateTargetGroup(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.SlbTargetGroupNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteTargetGroupErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditDeleteSlbTargetGroupError, func(ctx context.Context, resourceID string, request *slbv1.DeleteTargetGroupRequest, response interface{}, reason string) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetSLBByResourceID, func(_ *db.EngineWrapper, _ string, _ bool) (*db.Slbs, error) {
		return nil, fmt.Errorf("slb not exist")
	})
	slbServer := SLBsServer{}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &slbv1.DeleteTargetGroupRequest{
		Zone:    "test11",
		SlbName: "test",
	}

	ctx := context.Background()

	_, err := slbServer.DeleteTargetGroup(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.SlbNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestListTargetGroupsErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})

	slbServer := SLBsServer{}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &slbv1.ListTargetGroupsRequest{
		Zone:    "test",
		SlbName: "",
	}

	ctx := context.Background()

	_, err := slbServer.ListTargetGroups(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "listTargetGroups fail. slbName is nil",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetTargetGroupErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetSLBByResourceID, func(_ *db.EngineWrapper, _ string, _ bool) (*db.Slbs, error) {
		return nil, fmt.Errorf("slb not exist")
	})
	slbServer := SLBsServer{}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &slbv1.GetTargetGroupRequest{
		Zone:    "test",
		SlbName: "",
	}

	ctx := context.Background()

	_, err := slbServer.GetTargetGroup(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.SlbNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}
