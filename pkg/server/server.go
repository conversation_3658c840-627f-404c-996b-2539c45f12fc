package server

import (
	"context"
	"fmt"
	"math"
	"net"
	"net/http"

	grpc_validator "github.com/grpc-ecosystem/go-grpc-middleware/validator"
	"github.com/grpc-ecosystem/grpc-gateway/v2/runtime"
	"github.com/sirupsen/logrus"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/reflection"
	"google.golang.org/protobuf/encoding/protojson"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/dc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/ipa/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/quota/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/slb/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vnic/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
)

func Serve(grpcPort, httpPort int, stopCh <-chan struct{}) {
	go grpcServer(grpcPort, stopCh)
	go httpServer(grpcPort, httpPort, stopCh)
}

func healthz(w http.ResponseWriter, r *http.Request, pathParams map[string]string) {

	fmt.Fprintf(w, "ok")

}

func httpServer(grpcPort, httpPort int, stopCh <-chan struct{}) {

	mux := runtime.NewServeMux(runtime.WithIncomingHeaderMatcher(CustomMatcher),
		runtime.WithMarshalerOption(runtime.MIMEWildcard, &runtime.JSONPb{
			MarshalOptions:   protojson.MarshalOptions{UseProtoNames: true, UseEnumNumbers: false, EmitUnpopulated: true},
			UnmarshalOptions: protojson.UnmarshalOptions{DiscardUnknown: true},
		}))

	opts := []grpc.DialOption{grpc.WithTransportCredentials(insecure.NewCredentials())}

	err := vpc.RegisterVPCsHandlerFromEndpoint(context.Background(), mux, fmt.Sprintf(":%d", grpcPort), opts)
	if err != nil {
		logrus.Fatalln(err)
	}

	err = eip.RegisterDNATRulesHandlerFromEndpoint(context.Background(), mux, fmt.Sprintf(":%d", grpcPort), opts)
	if err != nil {
		logrus.Fatalln(err)
	}

	err = eip.RegisterACLsHandlerFromEndpoint(context.Background(), mux, fmt.Sprintf(":%d", grpcPort), opts)
	if err != nil {
		logrus.Fatalln(err)
	}

	err = eip.RegisterEIPsHandlerFromEndpoint(context.Background(), mux, fmt.Sprintf(":%d", grpcPort), opts)
	if err != nil {
		logrus.Fatalln(err)
	}

	err = eip.RegisterEIPDataServiceHandlerFromEndpoint(context.Background(), mux, fmt.Sprintf(":%d", grpcPort), opts)
	if err != nil {
		logrus.Fatalln(err)
	}

	err = ipa.RegisterIPAsHandlerFromEndpoint(context.Background(), mux, fmt.Sprintf(":%d", grpcPort), opts)
	if err != nil {
		logrus.Fatalln(err)
	}

	err = vnic.RegisterVNicsHandlerFromEndpoint(context.Background(), mux, fmt.Sprintf(":%d", grpcPort), opts)
	if err != nil {
		logrus.Fatalln(err)
	}

	err = vpc.RegisterVpcPeeringsHandlerFromEndpoint(context.Background(), mux, fmt.Sprintf(":%d", grpcPort), opts)
	if err != nil {
		logrus.Fatalln(err)
	}

	err = vpc.RegisterACLsHandlerFromEndpoint(context.Background(), mux, fmt.Sprintf(":%d", grpcPort), opts)
	if err != nil {
		logrus.Fatalln(err)
	}

	err = dc.RegisterDCGWsHandlerFromEndpoint(context.Background(), mux, fmt.Sprintf(":%d", grpcPort), opts)
	if err != nil {
		logrus.Fatalln(err)
	}

	err = dc.RegisterDCVCsHandlerFromEndpoint(context.Background(), mux, fmt.Sprintf(":%d", grpcPort), opts)
	if err != nil {
		logrus.Fatalln(err)
	}

	err = quota.RegisterQuotaServiceHandlerFromEndpoint(context.Background(), mux, fmt.Sprintf(":%d", grpcPort), opts)
	if err != nil {
		logrus.Fatalln(err)
	}

	if resourceprovider.BosonProvider.RpConfig.Boson.SlbEnable {
		err = slb.RegisterSLBDataServiceHandlerFromEndpoint(context.Background(), mux, fmt.Sprintf(":%d", grpcPort), opts)
		if err != nil {
			logrus.Fatalln(err)
		}
	}

	pat, err := runtime.NewPattern(1, []int{2, 0}, []string{"healthz"}, "")
	if err != nil {
		logrus.Fatalln(err)
	}

	mux.Handle("GET", pat, healthz)

	patIngress, err := runtime.NewPattern(1, []int{2, 0, 2, 1}, []string{"network", "healthz"}, "")
	if err != nil {
		logrus.Fatalln(err)
	}

	mux.Handle("GET", patIngress, healthz)

	defer func() {
		<-stopCh
	}()

	logrus.Infoln("HTTP server start", fmt.Sprintf(":%d", httpPort))
	err = http.ListenAndServe(fmt.Sprintf(":%d", httpPort), mux)
	if err != nil {
		logrus.Fatalln(err)
	}
}

func grpcServer(port int, stopCh <-chan struct{}) {
	listen, err := net.Listen("tcp", fmt.Sprintf(":%d", port))
	if err != nil {
		logrus.Fatalln(err)
	}

	options := []grpc.ServerOption{
		grpc.MaxRecvMsgSize(math.MaxInt32),
		grpc.MaxSendMsgSize(**********),
		grpc.UnaryInterceptor(
			grpc_validator.UnaryServerInterceptor(),
		),
	}

	server := grpc.NewServer(options...)
	vpc.RegisterVPCsServer(server, VPCsServer{})
	vpc.RegisterVpcPeeringsServer(server, NewVpcPeeringServer())
	vpc.RegisterACLsServer(server, VPCsACLsServer{})
	eip.RegisterDNATRulesServer(server, DNATRulesServer{})
	eip.RegisterACLsServer(server, ACLsServer{})
	eip.RegisterEIPsServer(server, EIPsServer{})
	eip.RegisterEIPDataServiceServer(server, EIPDataServiceServer{})
	ipa.RegisterIPAsServer(server, NewIPAServer())
	vnic.RegisterVNicsServer(server, NewVNICServer())
	dc.RegisterDCGWsServer(server, DCGWsServer{})
	dc.RegisterDCVCsServer(server, DCVCsServer{})
	quota.RegisterQuotaServiceServer(server, QuotasServer{})

	if resourceprovider.BosonProvider.RpConfig.Boson.SlbEnable {
		slb.RegisterSLBDataServiceServer(server, SLBsServer{})
	}
	reflection.Register(server)

	defer func() {
		server.Stop()
		listen.Close()
		<-stopCh
	}()

	logrus.Infoln("GRPC server start", fmt.Sprintf(":%d", port))
	err = server.Serve(listen)
	if err != nil {
		logrus.Fatalln(err)
	}
}

func CustomMatcher(key string) (string, bool) {
	switch key {
	case "X-User-Id", "X-User-Name":
		return key, true
	case "X-Tenant-Id", "X-Tenant-Code":
		return key, true
	default:
		return runtime.DefaultHeaderMatcher(key)
	}
}
