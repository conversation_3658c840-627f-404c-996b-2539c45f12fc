package server

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"google.golang.org/grpc/codes"
)

func TestListDNATRulesAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.ListDNATRulesRequest{
		Zone: "test11",
	}

	dnatRulesServer := DNATRulesServer{}
	ctx := context.Background()

	_, err := dnatRulesServer.ListDNATRules(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestListDNATRulesNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return nil, fmt.Errorf("Eip not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.ListDNATRulesRequest{
		Zone: "test",
	}

	dnatRulesServer := DNATRulesServer{}
	ctx := context.Background()

	_, err := dnatRulesServer.ListDNATRules(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestListDNATRulesInternalErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return &db.Eips{
			AssociationType: "tt",
		}, nil
	})
	p.ApplyFunc(eipTypeNameIsDnat, func(_ string) bool {
		return false
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.ListDNATRulesRequest{
		Zone: "test",
	}

	dnatRulesServer := DNATRulesServer{}
	ctx := context.Background()

	_, err := dnatRulesServer.ListDNATRules(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
		"detail": "eip type:tt not support dnat",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestBindDNATRuleAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditBindDnatError, func(ctx context.Context, resourceID string, request *eip.BindDNATRuleRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditBindDnatSuccess, func(ctx context.Context, resourceID string, request *eip.BindDNATRuleRequest,
		response interface{}) {
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.BindDNATRuleRequest{
		Zone: "test11",
		DnatRule: &eip.DNATRule{
			Name: "test",
		},
	}

	dnatRulesServer := DNATRulesServer{}
	ctx := context.Background()

	_, err := dnatRulesServer.BindDNATRule(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestUnbindDNATRuleAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditUnbindDnatError, func(ctx context.Context, resourceID string, request *eip.UnbindDNATRuleRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditUnbindDnatSuccess, func(ctx context.Context, resourceID string, request *eip.UnbindDNATRuleRequest,
		response interface{}) {
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.UnbindDNATRuleRequest{
		Zone: "test11",
	}

	dnatRulesServer := DNATRulesServer{}
	ctx := context.Background()

	_, err := dnatRulesServer.UnbindDNATRule(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetDNATRuleAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.GetDNATRuleRequest{
		Zone: "test11",
	}

	dnatRulesServer := DNATRulesServer{}
	ctx := context.Background()

	_, err := dnatRulesServer.GetDNATRule(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestCreateDNATRuleAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditCreateDnatError, func(ctx context.Context, resourceID string, request *eip.CreateDNATRuleRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditCreateDnatSuccess, func(ctx context.Context, resourceID string, request *eip.CreateDNATRuleRequest,
		response interface{}) {
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.CreateDNATRuleRequest{
		Zone: "test11",
	}

	dnatRulesServer := DNATRulesServer{}
	ctx := context.Background()

	_, err := dnatRulesServer.CreateDNATRule(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteDNATRuleAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditDeleteDnatError, func(ctx context.Context, resourceID string, request *eip.DeleteDNATRuleRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditDeleteDnatSuccess, func(ctx context.Context, resourceID string, request *eip.DeleteDNATRuleRequest,
		response interface{}) {
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.DeleteDNATRuleRequest{
		Zone: "test11",
	}

	dnatRulesServer := DNATRulesServer{}
	ctx := context.Background()

	_, err := dnatRulesServer.DeleteDNATRule(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestListMDNATRulesAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &eip.ListMDNATRulesRequest{
		Zone: "test11",
	}

	dnatRulesServer := DNATRulesServer{}
	ctx := context.Background()

	_, err := dnatRulesServer.ListMDNATRules(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}
