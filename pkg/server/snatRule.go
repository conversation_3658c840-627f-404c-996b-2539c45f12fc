package server

import (
	"context"
	"fmt"
	"strings"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
)

// SNATRulesServer 为 SNATRulesServer interface 的实现
type SNATRulesServer struct {
	*eip.UnimplementedSNATRulesServer
}

func (SNATRulesServer) ListSNATRules(ctx context.Context, request *eip.ListSNATRulesRequest) (res *eip.ListSNATRulesResponse, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics("", "SNATRule", "ListSNATRules")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(rp.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if eipData.State == eip.EIP_DELETED.String() {
		return &eip.ListSNATRulesResponse{}, nil
	}

	rulesData, err := dao.ListSnatRules(rp.BosonProvider.Engine, eipData.EIPID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	rules := make([]*eip.SNATRule, len(rulesData))
	for i, ruleData := range rulesData {
		rules[i] = &eip.SNATRule{
			Id:           ruleData.ID,
			Name:         ruleData.Name,
			DisplayName:  ruleData.DisplayName,
			Description:  ruleData.Description,
			Uid:          ruleData.SnatRuleID,
			ResourceType: ruleData.ResourceType,
			CreatorId:    ruleData.CreatorID,
			OwnerId:      ruleData.OwnerID,
			TenantId:     ruleData.TenantID,
			Zone:         ruleData.Zone,
			State:        eip.SNATRule_State(eip.SNATRule_State_value[ruleData.State]),
			CreateTime:   timestamppb.New(ruleData.CreateTime),
			UpdateTime:   timestamppb.New(ruleData.UpdateTime),
			Properties: &eip.SNATRuleProperties{
				VpcId: ruleData.VPCID,
				EipId: ruleData.EIPID,
			},
		}
	}

	res = &eip.ListSNATRulesResponse{
		TotalSize: int32(len(rulesData)),
		SnatRules: rules,
	}

	logrus.Infof("list snat rules %v", request)

	return res, nil
}

func (SNATRulesServer) GetSNATRule(ctx context.Context, request *eip.GetSNATRuleRequest) (res *eip.SNATRule, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.SnatRuleName, "SNATRule", "GetSNATRule")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(rp.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	ruleData, err := dao.GetSnatRule(rp.BosonProvider.Engine, eipData.EIPID)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SnatNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if ruleData.Name != request.SnatRuleName {
		reason := fmt.Sprintf("snatRule %v not exist", request.SnatRuleName)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SnatNotFound, errors.Domain, nil, nil)
	}

	res = &eip.SNATRule{
		Id:           ruleData.ID,
		Name:         ruleData.Name,
		DisplayName:  ruleData.DisplayName,
		Description:  ruleData.Description,
		Uid:          ruleData.SnatRuleID,
		ResourceType: ruleData.ResourceType,
		CreatorId:    ruleData.CreatorID,
		OwnerId:      ruleData.OwnerID,
		TenantId:     ruleData.TenantID,
		Zone:         ruleData.Zone,
		State:        eip.SNATRule_State(eip.SNATRule_State_value[ruleData.State]),
		CreateTime:   timestamppb.New(ruleData.CreateTime),
		UpdateTime:   timestamppb.New(ruleData.UpdateTime),
		Properties: &eip.SNATRuleProperties{
			VpcId: ruleData.VPCID,
			EipId: ruleData.EIPID,
		},
	}

	return res, nil
}

func (SNATRulesServer) CreateSNATRule(ctx context.Context, request *eip.CreateSNATRuleRequest) (res *eip.SNATRule, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.SnatRuleName, "SNATRule", "CreateSNATRule")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	logrus.Infof("Create snatRule called: %+v", request)

	if request.SnatRule == nil || request.SnatRule.Properties == nil {
		reason := fmt.Sprintf("error request: %+v, snat rule or snat rule properties is empty", request)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	eipData, err := dao.GetEipByID(rp.BosonProvider.Engine, request.SnatRule.Properties.EipId, false)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	snatDatas, err := dao.ListSnatRulesByVPC(rp.BosonProvider.Engine, request.SnatRule.Properties.VpcId)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if len(snatDatas) != 0 {
		reason := fmt.Sprintf("VPC %s has set snat to eip_id %s", request.SnatRule.Properties.VpcId, snatDatas[0].EIPID)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.AlreadyExists, errors.SnatExist, errors.Domain, nil, nil)
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetEIP")

	eipCr, err := rp.BosonProvider.BosonNetClient.EIPs().Get(context.Background(), eipData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	if eipCr.Status.State != types.StateActive {
		reason := fmt.Sprintf("EIP %s is in %s state", request.SnatRule.Properties.EipId, eipCr.Status.State)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.EipStateUnactive, errors.Domain, nil, nil)
	}

	if request.SnatRule.CreateTime == nil {
		request.SnatRule.CreateTime = timestamppb.Now()
	}

	if request.SnatRule.UpdateTime == nil {
		request.SnatRule.UpdateTime = request.SnatRule.CreateTime
	}

	// update DB
	sRuleData := db.SnatRules{
		SnatRuleID:  request.SnatRule.Uid,
		DisplayName: request.SnatRule.DisplayName,
		Description: request.SnatRule.Description,
		VPCID:       request.SnatRule.Properties.VpcId,
		EIPID:       request.SnatRule.Properties.EipId,

		ID:           request.SnatRule.Id,
		Name:         request.SnatRuleName,
		ResourceType: request.SnatRule.ResourceType,
		Zone:         request.SnatRule.Zone,
		State:        eip.SNATRule_CREATING.String(),
		CreatorID:    request.SnatRule.CreatorId,
		OwnerID:      request.SnatRule.OwnerId,
		TenantID:     request.SnatRule.TenantId,
		CreateTime:   request.SnatRule.CreateTime.AsTime(),
		UpdateTime:   request.SnatRule.UpdateTime.AsTime(),
		Deleted:      false,
	}

	err = dao.AddSnatRule(rp.BosonProvider.Engine, &sRuleData)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateEIP")

	eipCr.Annotations["snat"] = "enabled"
	_, err = rp.BosonProvider.BosonNetClient.EIPs().Update(context.Background(), eipCr, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	//notify the upper layer
	logrus.Infoln("Created EIP SNAT rule: ", request.SnatRuleName)

	return request.SnatRule, nil
}

// delete rule; unbind first if the rule state is ACTIVE
func (SNATRulesServer) DeleteSNATRule(ctx context.Context, request *eip.DeleteSNATRuleRequest) (res *emptypb.Empty, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.SnatRuleName, "SNATRule", "DeleteSNATRule")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	_, err = dao.GetEipByResourceID(rp.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	snat, err := dao.GetSnatRuleByName(rp.BosonProvider.Engine, request.SnatRuleName)

	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SnatNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if strings.EqualFold(snat.State, eip.SNATRule_State_name[int32(eip.SNATRule_CREATING)]) ||
		strings.EqualFold(snat.State, eip.SNATRule_State_name[int32(eip.SNATRule_UPDATING)]) ||
		strings.EqualFold(snat.State, eip.SNATRule_State_name[int32(eip.SNATRule_BINDING)]) ||
		strings.EqualFold(snat.State, eip.SNATRule_State_name[int32(eip.SNATRule_UNBINDING)]) {
		reason := fmt.Sprintf("EIP snat rule %v can't be deleted with state: %v", request.SnatRuleName, snat.State)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if strings.EqualFold(snat.State, eip.SNATRule_DELETED.String()) {
		return &emptypb.Empty{}, nil
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetEIP")

	eipCr, err := rp.BosonProvider.BosonNetClient.EIPs().Get(context.Background(), request.EipName, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateEIP")

	eipCr.Annotations["snat"] = "disabled"
	_, err = rp.BosonProvider.BosonNetClient.EIPs().Update(context.Background(), eipCr, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	if err := dao.SetSnatRuleState(rp.BosonProvider.Engine, snat.SnatRuleID, "DELETED"); err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	logrus.Infoln("Deleted EiP snat rule: ", request.SnatRuleName)

	return &emptypb.Empty{}, nil
}
