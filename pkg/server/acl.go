package server

import (
	"bytes"
	"context"
	"fmt"
	"net"
	"net/http"
	"strings"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"

	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"github.com/sirupsen/logrus"
	networkv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	vpcv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	aip "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils/google-aip"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	k8s_errors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

func invertACL(acl *db.Acls) *eip.ACL {
	dn := new(string)
	*dn = acl.DisplayName
	return &eip.ACL{
		Id:           acl.ID,
		Name:         acl.Name,
		DisplayName:  dn,
		Description:  acl.Description,
		Uid:          acl.AclID,
		ResourceType: acl.ResourceType,
		CreatorId:    acl.CreatorID,
		OwnerId:      acl.OwnerID,
		TenantId:     acl.TenantID,
		Zone:         acl.Zone,
		State:        eip.ACL_State(eip.ACL_State_value[acl.State]),
		CreateTime:   timestamppb.New(acl.CreateTime),
		UpdateTime:   timestamppb.New(acl.UpdateTime),
		Properties: &eip.ACLProperties{
			Action:   eip.ACLProperties_ACLAction(eip.ACLProperties_ACLAction_value[acl.Action]),
			Src:      acl.Src,
			Dest:     acl.Dest,
			DestPort: acl.DestPort,
			Protocol: acl.Protocol,
			Priority: acl.Priority,
		},
	}
}

// ACLsServer
type ACLsServer struct {
	*eip.UnimplementedACLsServer
}

func (ACLsServer) ListACLs(ctx context.Context, request *eip.ListACLsRequest) (res *eip.ListACLsResponse, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "ACL", "ListACLs")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(rp.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if eipData.State == eip.EIP_DELETED.String() {
		return &eip.ListACLsResponse{}, nil
	}

	aclDatas, err := dao.ListACLs(rp.BosonProvider.Engine, eipData.EIPID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	acls := []*eip.ACL{}
	for _, v := range aclDatas {
		acls = append(acls, invertACL(v))
	}
	totalSize := len(acls)
	nextPageToken, err := aip.CalcNextPageToken(totalSize, len(acls), request)
	if err != nil {
		reason := fmt.Sprintf("calc NextPageToken %v , err: %v", zap.Any("request", request), zap.Error(err))
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}
	res = &eip.ListACLsResponse{
		TotalSize:     int32(totalSize),
		NextPageToken: nextPageToken,
		Acls:          acls,
	}

	logrus.Infof("list ACLs %v", request)
	return res, nil
}

func (ACLsServer) GetACL(ctx context.Context, request *eip.GetACLRequest) (res *eip.ACL, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics(request.AclName, "ACL", "GetACL")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s does not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(rp.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	aclData, err := dao.GetACLByName(rp.BosonProvider.Engine, request.AclName)
	if err != nil {
		logrus.Errorf("Get acl %s error: %s", request.AclName, err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.AclNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if eipData.EIPID != aclData.EIPID {
		reason := fmt.Sprintf("ACL %v not belong to EIP %v", request.AclName, request.EipName)
		err = errors.New(reason)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if eipData.EIPID != aclData.EIPID {
		err = fmt.Errorf("request eip_id %s does not match acl's eip_id %s", eipData.EIPID, aclData.EIPID)
		logrus.Errorf("Get acl %s error: %s", request.AclName, err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	res = invertACL(aclData)
	logrus.Infoln("Get EIP ACL: ", request.AclName)

	return res, nil
}

func validateAclAddress(aclAddr string) error {
	fields := strings.Split(aclAddr, ",")
	for _, v := range fields {
		if strings.Contains(v, "/") {
			// validate subnet
			_, _, err := net.ParseCIDR(v)
			if err != nil {
				return err
			}
		} else if strings.Contains(v, "-") {
			// validate ip range
			ips := strings.Split(v, "-")
			if len(ips) != 2 {
				return fmt.Errorf("invalid ip range: %s", v)
			}
			startIP := net.ParseIP(ips[0])
			endIP := net.ParseIP(ips[1])
			result := bytes.Compare(startIP, endIP)
			if result >= 0 {
				return fmt.Errorf("invalid ip range: %s", v)
			}
		} else {
			// validate ip
			if net.ParseIP(v) == nil {
				return fmt.Errorf("invalid ip: %s", v)
			}
		}
	}

	return nil
}

func validateACL(acl *eip.ACL) error {
	if len(acl.Properties.Src) == 0 {
		return errors.New("the source addresses in the ACL is empty")
	}

	return validateAclAddress(acl.Properties.Src)
}

func (ACLsServer) CreateACL(ctx context.Context, request *eip.CreateACLRequest) (res *eip.ACL, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics(request.AclName, "ACL", "CreateACL")
	resourceUID := request.AclName
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditCreateAclError(ctx, resourceUID, request, struct{}{}, err.Error())
		} else {
			cloudAuditCreateAclSuccess(ctx, resourceUID, request, struct{}{})
		}
	}()

	logrus.Infof("Create ACL called: %+v", request)

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.Acl == nil || request.Acl.Properties == nil {
		reason := fmt.Sprintf("error request: %+v, acl or acl properties is empty", request)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if err := validateACL(request.Acl); err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(rp.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	vpcQuota, err := dao.GetVpcQuotaByVpcID(rp.BosonProvider.Engine, eipData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcQuotaNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	aclQuota := vpcQuota.PublicEIPAclCount
	if eipData.InternalEIP {
		aclQuota = vpcQuota.InternalEIPAclCount
	}
	cidrs := strings.Split(request.Acl.Properties.Src, ",")
	if len(cidrs) > int(aclQuota) {
		reason := fmt.Sprintf("acl len(acl)=%d, exceed quota=%d", len(cidrs), aclQuota)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetEIP")
	eipCr, err := rp.BosonProvider.BosonNetClient.EIPs().Get(context.Background(), eipData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	if request.Acl.CreateTime == nil {
		request.Acl.CreateTime = timestamppb.Now()
	}

	if request.Acl.UpdateTime == nil {
		request.Acl.UpdateTime = request.Acl.CreateTime
	}

	dest, err := getAclDest(ctx, eipData.VPCID, eipData.AssociationID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	}
	logrus.Infof("get dest address %s for eip %s", dest, eipData.EIPIP)

	// update DB
	aclData := &db.Acls{
		AclID:        request.Acl.Uid,
		DisplayName:  request.Acl.GetDisplayName(),
		Description:  request.Acl.Description,
		Action:       request.Acl.Properties.Action.String(),
		Src:          request.Acl.Properties.Src,
		Dest:         dest,
		DestPort:     request.Acl.Properties.DestPort,
		Protocol:     request.Acl.Properties.Protocol,
		Priority:     request.Acl.Properties.Priority,
		VPCID:        eipData.VPCID,
		EIPID:        eipData.EIPID,
		EIPIP:        eipData.EIPIP,
		ID:           request.Acl.Id,
		Name:         request.AclName,
		ResourceType: request.Acl.ResourceType,
		Zone:         request.Zone,
		State:        eip.ACL_CREATING.String(),
		CreatorID:    request.Acl.CreatorId,
		OwnerID:      request.Acl.OwnerId,
		TenantID:     request.Acl.TenantId,
		CreateTime:   request.Acl.CreateTime.AsTime(),
		UpdateTime:   request.Acl.UpdateTime.AsTime(),
		Deleted:      false,
	}

	err = dao.AddACL(rp.BosonProvider.Engine, aclData)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	// create ACL CR
	acl := &networkv1.ACL{
		TypeMeta: metav1.TypeMeta{
			Kind:       rp.ACLKind,
			APIVersion: networkv1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: request.AclName,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(eipCr, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    rp.EIPKind,
				}),
			},
			Annotations: map[string]string{
				rp.AnnoRegion:          db.Region,
				rp.AnnoAz:              db.AZ,
				rp.AnnoEipInternalType: "false",
			},
		},

		Spec: networkv1.ACLSpec{
			EIP:      eipData.EIPIP,
			AclID:    request.Acl.Uid,
			Action:   request.Acl.Properties.Action.String(),
			Src:      request.Acl.Properties.Src,
			Dest:     dest,
			DestPort: request.Acl.Properties.DestPort,
			Protocol: request.Acl.Properties.Protocol,
			Priority: int(request.Acl.Properties.Priority),
		},
	}

	if eipData.InternalEIP {
		acl.ObjectMeta.Annotations[rp.AnnoEipInternalType] = "true"
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("CreateACL")
	_, err = rp.BosonProvider.BosonNetClient.ACLs(rp.BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs).Create(context.Background(), acl, metav1.CreateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		// to do, not rollback for debug trace
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	// notify the upper layer
	logrus.Infoln("Created EIP ACL: ", request.AclName)

	return request.Acl, nil
}

func (ACLsServer) UpdateACL(ctx context.Context, request *eip.UpdateACLRequest) (res *eip.ACL, err error) {
	httpRTMetrics := exporter.InitHTTPRTMetrics(request.AclName, "ACL", "UpdateACL")
	resourceUID := request.AclName
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditUpdateAclError(ctx, resourceUID, request, struct{}{}, err.Error())
		} else {
			cloudAuditUpdateAclSuccess(ctx, resourceUID, request, struct{}{})
		}
	}()

	logrus.Infof("Update ACL called: %+v", request)

	if request.AclName == "" || request.EipName == "" {
		reason := fmt.Sprintf("error request: %+v, acl name or eip name is empty", request)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(rp.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if eipData.State != eip.EIP_ACTIVE.String() {
		reason := fmt.Sprintf("no permit to update ACL under the eip state %v", eipData.State)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	// state: ACTIVE
	aclData, err := dao.GetACLByName(rp.BosonProvider.Engine, request.AclName)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.AclNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if eipData.EIPID != aclData.EIPID {
		reason := fmt.Sprintf("ACL %v not belong to EIP %v", request.AclName, request.EipName)
		err = errors.New(reason)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if aclData.Action == request.Acl.Properties.Action.String() && aclData.Src == request.Acl.Properties.Src {
		reason := "ACL src and action have not changed"
		logrus.Errorf(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	// // to do
	// if aclData.State == eip.ACL_UPDATING.String() {
	// 	return invertACL(aclData), nil
	// }

	if aclData.State != eip.ACL_ACTIVE.String() {
		reason := fmt.Sprintf("ACL %v state: %v can't be Updated", request.AclName, aclData.State)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if err := validateACL(request.Acl); err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}
	vpcQuota, err := dao.GetVpcQuotaByVpcID(rp.BosonProvider.Engine, eipData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.VpcQuotaNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	aclQuota := vpcQuota.PublicEIPAclCount
	if eipData.InternalEIP {
		aclQuota = vpcQuota.InternalEIPAclCount
	}
	cidrs := strings.Split(request.Acl.Properties.Src, ",")
	if len(cidrs) > int(aclQuota) {
		reason := fmt.Sprintf("acl len(acl)=%d, exceed quota=%d", len(cidrs), aclQuota)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	// dest, err := getAclDest(ctx, eipData.VPCID, eipData.AssociationID)
	// if err != nil {
	// 	logrus.Errorln(err)
	// 	return nil, higgs_error.NewAIPError(ctx, codes.Internal, errors.ServiceError, errors.Domain)
	// }
	// logrus.Infof("get dest address %s for eip %s", dest, eipData.EIPIP)
	aclData.Action = request.Acl.Properties.Action.String()
	aclData.Src = request.Acl.Properties.Src
	// TODO: do not update the acl dest here
	// aclData.Dest = dest
	aclData.DestPort = request.Acl.Properties.DestPort
	aclData.Protocol = request.Acl.Properties.Protocol
	aclData.Priority = request.Acl.Properties.Priority

	// update db
	aclData.State = eip.ACL_UPDATING.String()
	if err := dao.UpdateACL(rp.BosonProvider.Engine, aclData); err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	// update CR
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetACL")
	acl, err := rp.BosonProvider.BosonNetClient.ACLs(rp.BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs).Get(context.Background(), aclData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	acl.Spec.Src = aclData.Src
	acl.Spec.Action = aclData.Action
	if len(acl.Annotations) == 0 {
		acl.Annotations = map[string]string{}
	}
	acl.Annotations[rp.AnnoRegion] = db.Region
	acl.Annotations[rp.AnnoAz] = db.AZ
	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateACL")
	_, err = rp.BosonProvider.BosonNetClient.ACLs(rp.BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs).Update(context.Background(), acl, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		// to do, not rollback for debug trace
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	logrus.Infoln("update EIP ACL: ", request.AclName)

	return invertACL(aclData), nil
}

func (ACLsServer) DeleteACL(ctx context.Context, request *eip.DeleteACLRequest) (res *emptypb.Empty, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.AclName, "ACL", "DeleteACL")
	resourceUID := request.AclName
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditDeleteAclError(ctx, resourceUID, request, struct{}{}, err.Error())
		} else {
			cloudAuditDeleteAclSuccess(ctx, resourceUID, request, struct{}{})
		}
	}()

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(rp.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	aclData, err := dao.GetACLByName(rp.BosonProvider.Engine, request.AclName)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.AclNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if eipData.EIPID != aclData.EIPID {
		reason := fmt.Sprintf("ACL %v not belong to EIP %v", request.AclName, request.EipName)
		err = errors.New(reason)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if strings.EqualFold(aclData.State, eip.ACL_CREATING.String()) ||
		strings.EqualFold(aclData.State, eip.ACL_UPDATING.String()) ||
		strings.EqualFold(aclData.State, eip.ACL_DELETING.String()) {
		reason := fmt.Sprintf("EIP ACL %v can't be deleted with state: %v", request.AclName, aclData.State)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if strings.EqualFold(aclData.State, eip.ACL_DELETED.String()) {
		return &emptypb.Empty{}, nil
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetACL")

	_, err = rp.BosonProvider.BosonNetClient.ACLs(rp.BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs).
		Get(context.Background(), request.AclName, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		if k8s_errors.IsNotFound(err) {
			if err := dao.SetACLState(rp.BosonProvider.Engine, aclData.AclID, eip.ACL_DELETED.String()); err != nil {
				logrus.Errorln(err)
				return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
			}
		} else {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
		}
	} else {
		// update db
		if err := dao.SetACLState(rp.BosonProvider.Engine, aclData.AclID, eip.ACL_DELETING.String()); err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		// delete CR
		k8sRTMetrics = exporter.InitK8sRTMetrics("DeleteACL")
		err = rp.BosonProvider.BosonNetClient.ACLs(rp.BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs).
			Delete(context.Background(), request.AclName, metav1.DeleteOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil {
			logrus.Errorln(err)
			if err := dao.SetACLState(rp.BosonProvider.Engine, aclData.AclID, eip.ACL_FAILED.String()); err != nil {
				logrus.Errorln(err)
				return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
			}
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
		}
	}

	logrus.Infoln("Deleting EIP ACL: ", request.AclName)

	return &emptypb.Empty{}, nil
}

func getAclDest(ctx context.Context, vpcID, natGwID string) (dest string, err error) {
	var vpcData *db.Vpcs
	if vpcData, err = dao.GetVpc(rp.BosonProvider.Engine, vpcID); err != nil {
		logrus.Errorln(err)
		return "", err
	}
	gwData, has, err := dao.GetHaNatGateway(rp.BosonProvider.Engine, natGwID)
	if err != nil {
		logrus.Errorln(has, err)
		return "", err
	}
	if !has {
		err := fmt.Errorf("there is no nat gateway with id %s", natGwID)
		logrus.Errorln(err)
		return "", err
	}
	gwExternalIP, has, err := dao.GetNatGatewayExternalIpPool(rp.BosonProvider.Engine, gwData.NatGwExternalIPID)
	if err != nil {
		logrus.Errorln(has, err)
		return "", err
	}
	if !has {
		err := fmt.Errorf("there is no nat gateway external IP with id %s", gwData.NatGwExternalIPID)
		logrus.Errorln(err)
		return "", err
	}

	dest = gwExternalIP.NatGwExternalIP
	if vpcData.SubnetType == vpcv1.SubnetType_POD_BMS_SERVICE.String() || vpcData.SubnetType == vpcv1.SubnetType_POD_BMS_SERVICE_TRAINING.String() || vpcData.SubnetType == vpcv1.SubnetType_POD_SERVICE_BMS.String() {
		bmsSubnet, err := dao.GetBmsSubnet(rp.BosonProvider.Engine, vpcv1.SubnetProperties_SERVICE.String(), vpcID)
		if err != nil {
			logrus.Errorln(err)
			return "", err
		}
		dest = fmt.Sprintf("%s,%s", gwExternalIP.NatGwExternalIP, bmsSubnet.CIDR)
	}

	if err := validateAclAddress(dest); err != nil {
		logrus.Errorf("validate dest address %s error: %s", dest, err)
		return "", err
	}

	return dest, nil
}

func cloudAuditCreateAclError(
	ctx context.Context, resourceID string, request *eip.CreateACLRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendACL(
		ctx,
		types.CloudAuditOperationTypeCreateAccessControl, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.Acl.Name,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditCreateAclSuccess(
	ctx context.Context, resourceID string, request *eip.CreateACLRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendACL(
		ctx,
		types.CloudAuditOperationTypeCreateAccessControl, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.AclName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("create success"),
	)
}

func cloudAuditUpdateAclError(
	ctx context.Context, resourceUID string, request *eip.UpdateACLRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendACL(
		ctx,
		types.CloudAuditOperationTypeUpdateAccessControl, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceUID, request.AclName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditUpdateAclSuccess(
	ctx context.Context, resourceUID string, request *eip.UpdateACLRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendACL(
		ctx,
		types.CloudAuditOperationTypeUpdateAccessControl, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceUID, request.AclName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("update success"),
	)
}

func cloudAuditDeleteAclError(
	ctx context.Context, resourceUID string, request *eip.DeleteACLRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendACL(
		ctx,
		types.CloudAuditOperationTypeDeleteAccessControl, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceUID, request.AclName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditDeleteAclSuccess(
	ctx context.Context, resourceUID string, request *eip.DeleteACLRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendACL(
		ctx,
		types.CloudAuditOperationTypeDeleteAccessControl, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceUID, request.AclName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("delete success"),
	)
}
