package server

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"google.golang.org/grpc/codes"
)

func TestListEIPsInternalErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetAllEIPsWithIDPrefix, func(_ *db.EngineWrapper, _ string) ([]*db.Eips, error) {
		return nil, fmt.Errorf("connection refused")
	})

	eipsServer := EIPsServer{}
	ctx := context.Background()

	request := &eip.ListEIPsRequest{
		Zone: "test",
	}

	_, err := eipsServer.ListEIPs(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetEIPStatusAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.AZ, "test")
	request := &eip.GetEIPStatusRequest{
		Zone: "test11",
	}

	eipsServer := EIPsServer{}
	ctx := context.Background()

	_, err := eipsServer.GetEIPStatus(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetEIPStatusNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return nil, fmt.Errorf("Eip not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")
	request := &eip.GetEIPStatusRequest{
		Zone: "test",
	}

	eipsServer := EIPsServer{}
	ctx := context.Background()

	_, err := eipsServer.GetEIPStatus(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetEIPStatusDeleted(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return &db.Eips{
			Deleted: true,
		}, nil
	})

	p.ApplyGlobalVar(&db.AZ, "test")
	request := &eip.GetEIPStatusRequest{
		Zone:    "test",
		EipName: "abc",
	}

	eipsServer := EIPsServer{}
	ctx := context.Background()

	_, err := eipsServer.GetEIPStatus(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "EIP abc has been deleted",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetEIPStatus(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return &db.Eips{
			Deleted:   false,
			EIPID:     "abcd",
			EIPPoolID: "abcd",
			VPCID:     "abcd",
		}, nil
	})
	p.ApplyFunc(dao.GetEipRuleCount, func(_ *db.EngineWrapper, _ string) (int64, error) {
		return 1, nil
	})
	p.ApplyFunc(dao.GetVpc, func(_ *db.EngineWrapper, _ string) (*db.Vpcs, error) {
		return &db.Vpcs{}, nil
	})
	p.ApplyFunc(dao.GetEipPool, func(_ *db.EngineWrapper, _ string) (*db.EipPools, error) {
		return nil, fmt.Errorf("EIP POOL not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")
	request := &eip.GetEIPStatusRequest{
		Zone:    "test",
		EipName: "abc",
	}

	eipsServer := EIPsServer{}
	ctx := context.Background()

	_, err := eipsServer.GetEIPStatus(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.EipPoolNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetEIPNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return nil, fmt.Errorf("Eip not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")
	request := &eip.GetEIPRequest{
		Zone: "test",
	}

	eipsServer := EIPsServer{}
	ctx := context.Background()

	_, err := eipsServer.GetEIP(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestCreateEIP(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return nil, fmt.Errorf("Eip not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")
	request := &eip.CreateEIPRequest{
		Zone: "test",
	}

	eipsServer := EIPsServer{}
	ctx := context.Background()

	_, err := eipsServer.CreateEIP(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestUpdateEIP(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return nil, fmt.Errorf("Eip not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")
	request := &eip.UpdateEIPRequest{
		Zone: "test",
	}

	eipsServer := EIPsServer{}
	ctx := context.Background()

	_, err := eipsServer.UpdateEIP(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteEIP(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return nil, fmt.Errorf("Eip not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")
	request := &eip.DeleteEIPRequest{
		Zone: "test",
	}

	eipsServer := EIPsServer{}
	ctx := context.Background()

	_, err := eipsServer.DeleteEIP(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetEIPMetricsParamErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetEipByResourceID, func(_ *db.EngineWrapper, _ string) (*db.Eips, error) {
		return nil, fmt.Errorf("Eip not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")
	request := &eip.GetEIPMetricsRequest{}

	eipsServer := EIPsServer{}
	ctx := context.Background()

	_, err := eipsServer.GetEIPMetrics(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "tenant ID is empty",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestListSlbTypedAvailableEIPsErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(getSlbTypedAvailableEIP, func() ([]*db.Eips, error) {
		return nil, fmt.Errorf("connection refused")
	})

	p.ApplyGlobalVar(&db.AZ, "test")
	request := &eip.ListSlbTypedAvailableEIPsRequest{}

	eipDataServer := EIPDataServiceServer{}
	ctx := context.Background()

	_, err := eipDataServer.ListSlbTypedAvailableEIPs(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}
