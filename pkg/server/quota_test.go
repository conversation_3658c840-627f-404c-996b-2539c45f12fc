package server

import (
	"context"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/quota/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"google.golang.org/grpc/codes"
)

func TestListQuotaAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	request := &quota.ListQuotasRequest{}

	quotagwsServer := QuotasServer{}
	ctx := context.Background()

	_, err := quotagwsServer.ListQuotas(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}
