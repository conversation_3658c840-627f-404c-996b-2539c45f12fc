package server

import (
	"bytes"
	"context"
	"fmt"
	"net/http"
	"strconv"
	"strings"
	"time"

	"github.com/allisson/go-pglock/v3"
	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"github.com/sirupsen/logrus"
	"go.einride.tech/aip/filtering"
	"go.uber.org/zap"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	k8s_errors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"

	dnatv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	aip "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils/google-aip"
	utilnet "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils/network"
	"go.einride.tech/aip/ordering"
	"go.einride.tech/aip/pagination"
)

func invertDnatrule(ruleData *db.DnatRules) *eip.DNATRule {
	return &eip.DNATRule{
		Id:           ruleData.ID,
		Name:         ruleData.Name,
		DisplayName:  ruleData.DisplayName,
		Description:  ruleData.Description,
		Uid:          ruleData.DnatRuleID,
		ResourceType: ruleData.ResourceType,
		CreatorId:    ruleData.CreatorID,
		OwnerId:      ruleData.OwnerID,
		TenantId:     ruleData.TenantID,
		Zone:         ruleData.Zone,
		State:        eip.DNATRule_State(eip.DNATRule_State_value[ruleData.State]),
		CreateTime:   timestamppb.New(ruleData.CreateTime),
		UpdateTime:   timestamppb.New(ruleData.UpdateTime),
		Properties: &eip.DNATRuleProperties{
			NatGatewayId:         ruleData.NatGatewayID,
			EipId:                ruleData.EIPID,
			ExternalIp:           ruleData.OuterIP,
			ExternalPort:         fmt.Sprintf("%d", ruleData.OuterPortMax),
			InternalIp:           ruleData.InternalIP,
			Protocol:             ruleData.Protocol,
			InternalPort:         fmt.Sprintf("%d", ruleData.InnerPortMax),
			Priority:             ruleData.Priority,
			InternalInstanceType: eip.DNATRuleProperties_InstanceType(eip.DNATRuleProperties_InstanceType_value[ruleData.InternalInstanceType]),
			InternalInstanceName: ruleData.InternalInstanceName,
		},
	}
}

// DNATRulesServer 为 DNATRulesServer interface 的实现
type DNATRulesServer struct {
	*eip.UnimplementedDNATRulesServer
}

func (DNATRulesServer) ListDNATRules(ctx context.Context, request *eip.ListDNATRulesRequest) (res *eip.ListDNATRulesResponse, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics("", "DNATRule", "ListDNATRules")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(resourceprovider.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if !eipTypeNameIsDnat(eipData.AssociationType) {
		err = fmt.Errorf("eip type:%s not support dnat", eipData.AssociationType)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if eipData.State == eip.EIP_DELETED.String() {
		return &eip.ListDNATRulesResponse{}, nil
	}

	eips := []string{request.GetEipName()}
	request.Filter = fixFilter(request.Filter)
	dnatRules, totalSize, err := getDnatRules(ctx, eips, request)
	if err != nil {
		return nil, err
	}

	nextPageToken, err := aip.CalcNextPageToken(totalSize, len(dnatRules), request)
	if err != nil {
		reason := fmt.Sprintf("calc NextPageToken %v , err: %v", zap.Any("request", request), zap.Error(err))
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}
	res = &eip.ListDNATRulesResponse{
		TotalSize:     int32(totalSize),
		NextPageToken: nextPageToken,
		DnatRules:     dnatRules,
	}

	logrus.Infof("list dnat rules %v", request)
	return res, nil
}

func (DNATRulesServer) BindDNATRule(ctx context.Context, request *eip.BindDNATRuleRequest) (res *eip.DNATRule, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.DnatRuleName, "DNATRule", "BindDNATRule")
	resourceUID := request.DnatRule.Name
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditBindDnatError(ctx, resourceUID, request, struct{}{}, err.Error())
		} else {
			cloudAuditBindDnatSuccess(ctx, resourceUID, request, struct{}{})
		}
	}()

	logrus.Infof("Bind dnatRule called: %+v", request)

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.DnatRule == nil || request.DnatRule.Properties == nil {
		reason := fmt.Sprintf("error request: %+v, dnatRule or DnatRule properties is empty", request)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	request.DnatRule.Properties.Protocol = strings.ToLower(request.DnatRule.Properties.Protocol)

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	var eipData *db.Eips
	eipData, err = dao.GetEipByResourceID(resourceprovider.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if !eipTypeNameIsDnat(eipData.AssociationType) {
		err = fmt.Errorf("eip type:%s not support dnat", eipData.AssociationType)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.DnatRule.Name != "" && request.DnatRule.Name != request.DnatRuleName {
		reason := fmt.Sprintf("invalid parameter DnatRuleName %s with Name %s in dnat rule", request.DnatRuleName, request.DnatRule.Name)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	var ruleData *db.DnatRules
	ruleData, err = dao.GetDnatRuleByName(rp.BosonProvider.Engine, request.DnatRule.Name)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DnatNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if eipData.EIPID != ruleData.EIPID {
		reason := fmt.Sprintf("DnatRule %v not belong to EIP %v", request.DnatRuleName, request.EipName)
		err = errors.New(reason)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if eipData.EIPID != ruleData.EIPID {
		reason := fmt.Sprintf("invalid partemeter eip name %v with eip id %s in dnat rule properties", request.EipName, ruleData.EIPID)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if eipData.State != eip.EIP_ACTIVE.String() {
		reason := fmt.Sprintf("no permit to bind dnat rule under the eip state %v", eipData.State)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if request.DnatRule.Properties.InternalInstanceType == eip.DNATRuleProperties_UNSPECIFIED ||
		request.DnatRule.Properties.InternalPort == "" ||
		(request.DnatRule.Properties.InternalInstanceName == "" && request.DnatRule.Properties.InternalIp == "") ||
		(request.DnatRule.Properties.InternalInstanceType == eip.DNATRuleProperties_ECS && request.DnatRule.Properties.InternalIp == "") ||
		(request.DnatRule.Properties.InternalInstanceType == eip.DNATRuleProperties_BMS && request.DnatRule.Properties.InternalIp == "") {
		reason := fmt.Sprintf("invalid parameter, must specify the instance parameters %v ", request.DnatRule.Properties)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	// state: CREATED
	// instance type, name/ip, port
	// protocol& name/ip not repeat
	if ruleData.State == eip.DNATRule_BINDING.String() || ruleData.State == eip.DNATRule_ACTIVE.String() {
		if strings.EqualFold(strconv.Itoa(int(ruleData.InnerPortMin)), request.DnatRule.Properties.InternalPort) &&
			strings.EqualFold(ruleData.InternalInstanceType, request.DnatRule.Properties.InternalInstanceType.String()) &&
			strings.EqualFold(ruleData.InternalIP, request.DnatRule.Properties.InternalIp) &&
			strings.EqualFold(ruleData.InternalInstanceName, request.DnatRule.Properties.InternalInstanceName) {
			return invertDnatrule(ruleData), nil
		}
		reason := fmt.Sprintf("dnatRule %v not exist", request.DnatRuleName)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DnatNotFound, errors.Domain, nil, nil)
	}

	if ruleData.State != eip.DNATRule_CREATED.String() {
		reason := fmt.Sprintf("dnatRule %v state: %v can't be bond", request.DnatRuleName, ruleData.State)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	// not support service bms instance
	if request.DnatRule.Properties.InternalInstanceType == eip.DNATRuleProperties_BMS && eipData.InternalEIP {
		var subnets []*db.Subnets
		subnets, err = dao.ListSubnets(rp.BosonProvider.Engine, eipData.VPCID)
		if err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.DbErr, errors.Domain, nil, nil)
		}
		dataInstance := false
		for _, subnet := range subnets {
			if subnet.Provider == network.SubnetProperties_CONTROLLER.String() && subnet.Scope == network.SubnetProperties_DATA.String() {
				if utilnet.CIDRContainIP(subnet.CIDR, request.DnatRule.Properties.InternalIp) {
					dataInstance = true
					break
				}
			}
		}
		if !dataInstance {
			reason := fmt.Sprintf("EIP dnat rule %s only support data bms instance", request.DnatRuleName)
			err = errors.New(reason)
			logrus.Errorln(reason)
			return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
				"detail": reason,
			})
		}
	}

	iPort := strings.Split(request.DnatRule.Properties.InternalPort, ",")
	if len(iPort) != 1 {
		reason := fmt.Sprintf("EIP Dnat rule: %v InternalPort invalided: %v", request.DnatRuleName, request.DnatRule.Properties.InternalPort)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	port, err1 := strconv.Atoi(iPort[0])
	if err1 != nil || port < 1 || port > 65535 {
		reason := fmt.Sprintf("EIP Dnat rule: %v InternalPort invalided: %v", request.DnatRuleName, request.DnatRule.Properties.InternalPort)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	// update db
	ruleData.InternalInstanceType = request.DnatRule.Properties.InternalInstanceType.String()
	ruleData.InternalInstanceName = request.DnatRule.Properties.InternalInstanceName
	ruleData.InternalIP = request.DnatRule.Properties.InternalIp
	ruleData.InnerPortMax, ruleData.InnerPortMin = int32(port), int32(port)
	ruleData.State = eip.DNATRule_BINDING.String()

	if err = dao.UpdateDnatRule(rp.BosonProvider.Engine, ruleData); err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	// update CR
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetDnat")
	var drule *dnatv1.Dnat
	drule, err = rp.BosonProvider.BosonNetClient.Dnats().Get(context.Background(), ruleData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	// annotation&labels
	if drule.ObjectMeta.Annotations == nil {
		drule.ObjectMeta.Annotations = make(map[string]string)
	}
	drule.ObjectMeta.Labels = make(map[string]string)
	drule.ObjectMeta.Annotations["boson.sensetime.com/action"] = "bind"
	drule.ObjectMeta.Labels["lepton.sensetime.com/instance-type"] = ruleData.InternalInstanceType
	drule.ObjectMeta.Labels["lepton.sensetime.com/instance-name"] = ruleData.InternalInstanceName
	drule.Spec.InnerPorts = fmt.Sprintf("%d", ruleData.InnerPortMin)
	drule.Spec.InternalIP = ruleData.InternalIP

	k8sRTMetrics = exporter.InitK8sRTMetrics("BindDnat")

	_, err = rp.BosonProvider.BosonNetClient.Dnats().Update(context.Background(), drule, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		// to do, not rollback for debug trace
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	//notify the upper layer
	logrus.Infoln("Bind EIP DNAT rule: ", request.DnatRuleName)

	return invertDnatrule(ruleData), nil
}

func (DNATRulesServer) UnbindDNATRule(ctx context.Context, request *eip.UnbindDNATRuleRequest) (res *eip.DNATRule, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.DnatRuleName, "DNATRule", "UnbindDNATRule")
	resourceUID := request.DnatRuleName
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditUnbindDnatError(ctx, resourceUID, request, struct{}{}, err.Error())
		} else {
			cloudAuditUnbindDnatSuccess(ctx, resourceUID, request, struct{}{})
		}
	}()

	logrus.Infof("Unbind dnatRule called: %+v", request)

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.DnatRuleName == "" || request.EipName == "" {
		reason := fmt.Sprintf("error request: %+v, dnat rule name or eip name is empty", request)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(resourceprovider.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if !eipTypeNameIsDnat(eipData.AssociationType) {
		err = fmt.Errorf("eip type:%s not support dnat", eipData.AssociationType)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if eipData.State == eip.EIP_DELETED.String() {
		reason := fmt.Sprintf("no permit to unbind dnat rule under the eip state %v", eipData.State)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	// state: ACTIVE
	ruleData, err := dao.GetDnatRuleByName(rp.BosonProvider.Engine, request.DnatRuleName)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DnatNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if eipData.EIPID != ruleData.EIPID {
		reason := fmt.Sprintf("DnatRule %v not belong to EIP %v", request.DnatRuleName, request.EipName)
		err = errors.New(reason)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if ruleData.State == eip.DNATRule_UNBINDING.String() || ruleData.State == eip.DNATRule_CREATED.String() {
		return invertDnatrule(ruleData), nil
	}

	if ruleData.State != eip.DNATRule_ACTIVE.String() {
		reason := fmt.Sprintf("dnatRule %v state: %v can't be unbond", request.DnatRuleName, ruleData.State)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	// update dnat rule state in the db
	ruleData.State = eip.DNATRule_UNBINDING.String()
	if err := dao.SetDnatRuleState(rp.BosonProvider.Engine, ruleData.DnatRuleID, ruleData.State); err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	// update CR
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetDnat")
	drule, err := rp.BosonProvider.BosonNetClient.Dnats().Get(context.Background(), ruleData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	// annotation&labels
	if drule.ObjectMeta.Annotations == nil {
		drule.ObjectMeta.Annotations = make(map[string]string)
	}
	drule.ObjectMeta.Annotations["boson.sensetime.com/action"] = "unbind"

	k8sRTMetrics = exporter.InitK8sRTMetrics("UnbindDnat")
	_, err = rp.BosonProvider.BosonNetClient.Dnats().Update(context.Background(), drule, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		// to do, not rollback for debug trace
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	//notify the upper layer
	logrus.Infoln("Unbind EIP DNAT rule: ", request.DnatRuleName)

	return invertDnatrule(ruleData), nil
}

func (DNATRulesServer) UnbindDNATRules(ctx context.Context, request *eip.UnbindDNATRulesRequest) (res *eip.ListDNATRulesResponse, err error) {
	// check request parameter validate
	// update CR

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.EipName, "DNATRule", "UnbindDNATRules")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditBatchUnbindDnatError(ctx, request, struct{}{}, err.Error())
		} else {
			cloudAuditBatchUnbindDnatSuccess(ctx, request, struct{}{})
		}
	}()

	logrus.Infof("Unbind dnatRules called: %+v", request)

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.DnatRules.FilterValues == nil || len(request.DnatRules.FilterValues) == 0 || request.EipName == "" {
		reason := fmt.Sprintf("error request: %+v, dnat values or eip name is empty", request)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(resourceprovider.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if !eipTypeNameIsDnat(eipData.AssociationType) {
		err = fmt.Errorf("eip type:%s not support dnat", eipData.AssociationType)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if eipData.State == eip.EIP_CREATING.String() || eipData.State == eip.EIP_DELETING.String() || eipData.State == eip.EIP_DELETED.String() {
		reason := fmt.Sprintf("no permit to unbind dnat rule under the eip state %v", eipData.State)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.DnatRules.FilterType == eip.DnatRules_EIP_IP && request.DnatRules.FilterValues[0] != request.EipName {
		reason := fmt.Sprintf("invalid filter values %v", request.DnatRules.FilterValues[0])
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	var condition bytes.Buffer
	condition.WriteString(fmt.Sprintf("eip_id = '%s' and deleted = false ", eipData.EIPID))
	switch request.DnatRules.FilterType {
	case eip.DnatRules_INTERNAL_IP:
		condition.WriteString(" and inner_ip in (")
		if len(request.DnatRules.FilterValues) > 0 {
			condition.WriteString(fmt.Sprintf("'%s' ", request.DnatRules.FilterValues[0]))
		}
	case eip.DnatRules_INTERNAL_INSTANCE_NAME:
		condition.WriteString("and internal_instance_name in (")
		if len(request.DnatRules.FilterValues) > 0 {
			condition.WriteString(fmt.Sprintf("'%s' ", request.DnatRules.FilterValues[0]))
		}
	case eip.DnatRules_RULE_NAME:
		condition.WriteString("and name in (")
		s := []string{}
		for _, v := range request.DnatRules.FilterValues {
			s = append(s, fmt.Sprintf("'%s'", v))
		}

		condition.WriteString(strings.Join(s, ","))
	}

	if request.DnatRules.FilterType != eip.DnatRules_EIP_IP {
		condition.WriteString(")")
	}

	dbRTMetrics := exporter.InitDBRTMetrics("ListDNATRules")
	logrus.Infoln("Unbind EIP DNAT rules condition: ", condition.String())
	rulesData := []db.DnatRules{}
	err = rp.BosonProvider.Engine.Where(condition.String()).Find(&rulesData)
	dbRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)

		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	for _, ruleData := range rulesData {
		if ruleData.State == eip.DNATRule_UNBINDING.String() || ruleData.State == eip.DNATRule_CREATED.String() {
			continue
		}
		if ruleData.State != eip.DNATRule_ACTIVE.String() {
			reason := fmt.Sprintf("dnatRule %v state: %v can't be unbond", ruleData.Name, ruleData.State)
			err = errors.New(reason)
			logrus.Errorln(reason)
			return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
				"detail": reason,
			})
		}
	}

	rules := []*eip.DNATRule{}

	for _, ruleData := range rulesData {
		if ruleData.State == eip.DNATRule_UNBINDING.String() || ruleData.State == eip.DNATRule_CREATED.String() {
			rules = append(rules, invertDnatrule(&ruleData))
			continue
		}

		// update dnat rule state in the db
		ruleData.State = eip.DNATRule_UNBINDING.String()
		if err := dao.SetDnatRuleState(rp.BosonProvider.Engine, ruleData.DnatRuleID, ruleData.State); err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		// update CR
		k8sRTMetrics := exporter.InitK8sRTMetrics("GetDnat")
		drule, err := rp.BosonProvider.BosonNetClient.Dnats().Get(context.Background(), ruleData.Name, metav1.GetOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
		}

		// annotation&labels
		if drule.ObjectMeta.Annotations == nil {
			drule.ObjectMeta.Annotations = make(map[string]string)
		}
		drule.ObjectMeta.Annotations["boson.sensetime.com/action"] = "unbind"

		k8sRTMetrics = exporter.InitK8sRTMetrics("UnbindDnat")

		_, err = rp.BosonProvider.BosonNetClient.Dnats().Update(context.Background(), drule, metav1.UpdateOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
		}

		rules = append(rules, invertDnatrule(&ruleData))
	}

	//notify the upper layer
	logrus.Infoln("Unbind EIP DNAT rules: ", request)

	res = &eip.ListDNATRulesResponse{
		TotalSize: int32(len(rulesData)),
		DnatRules: rules,
	}

	return res, nil
}

func (DNATRulesServer) UpdateDNATRule(ctx context.Context, request *eip.UpdateDNATRuleRequest) (res *eip.DNATRule, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.DnatRuleName, "DNATRule", "UpdateDNATRule")
	resourceUID := request.DnatRuleName
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditUpdateDnatError(ctx, resourceUID, request, struct{}{}, err.Error())
		} else {
			cloudAuditUpdateDnatSuccess(ctx, resourceUID, request, struct{}{})
		}
	}()

	logrus.Infof("Update dnatRule called: %+v", request)

	if request.DnatRuleName == "" || request.EipName == "" {
		reason := fmt.Sprintf("error request: %+v, dnat rule name or eip name is empty", request)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(resourceprovider.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if !eipTypeNameIsDnat(eipData.AssociationType) {
		err = fmt.Errorf("eip type:%s not support dnat", eipData.AssociationType)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	// state: ACTIVE
	ruleData, err := dao.GetDnatRuleByName(rp.BosonProvider.Engine, request.DnatRuleName)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DnatNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if request.DnatRule.DisplayName != "" || request.DnatRule.Description != "" {
		update := false
		if request.DnatRule.DisplayName != "" && request.DnatRule.DisplayName != ruleData.DisplayName {
			update = true
			ruleData.DisplayName = request.DnatRule.DisplayName
		}

		if request.DnatRule.Description != "" && request.DnatRule.Description != ruleData.Description {
			update = true
			ruleData.Description = request.DnatRule.Description
		}

		if update {
			if err := dao.UpdateDnatRule(rp.BosonProvider.Engine, ruleData); err != nil {
				logrus.Errorln(err)
				return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
			}
		}
		return invertDnatrule(ruleData), nil
	}

	if eipData.EIPID != ruleData.EIPID {
		reason := fmt.Sprintf("DnatRule %v not belong to EIP %v", request.DnatRuleName, request.EipName)
		err = errors.New(reason)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if eipData.State != eip.EIP_ACTIVE.String() {
		reason := fmt.Sprintf("no permit to unbind dnat rule under the eip state %v", eipData.State)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	// to do
	if ruleData.State == eip.DNATRule_UPDATING.String() {
		return invertDnatrule(ruleData), nil
	}

	if ruleData.State != eip.DNATRule_ACTIVE.String() {
		reason := fmt.Sprintf("dnatRule %v state: %v can't be Updated", request.DnatRuleName, ruleData.State)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	// check parameter
	if request.DnatRule.Properties.InternalIp != "" {
		ruleData.InternalIP = request.DnatRule.Properties.InternalIp
	}

	if request.DnatRule.Properties.InternalInstanceName != "" {
		ruleData.InternalInstanceName = request.DnatRule.Properties.InternalInstanceName
	}
	ruleData.InternalInstanceType = request.DnatRule.Properties.InternalInstanceType.String()

	if request.DnatRule.Properties.InternalPort != "" {
		port := 0
		iPort := strings.Split(request.DnatRule.Properties.InternalPort, ",")
		if len(iPort) != 1 {
			reason := fmt.Sprintf("EIP Dnat rule: %v InternalPort invalided: %v", request.DnatRuleName, request.DnatRule.Properties.InternalPort)
			err = errors.New(reason)
			logrus.Errorln(reason)
			return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
				"detail": reason,
			})
		}

		if port, err = strconv.Atoi(iPort[0]); err != nil || port < 1 || port > 65535 {
			reason := fmt.Sprintf("EIP Dnat rule: %v InternalPort invalided: %v", request.DnatRuleName, request.DnatRule.Properties.InternalPort)
			err = errors.New(reason)
			logrus.Errorln(reason)
			return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
				"detail": reason,
			})
		}

		ruleData.InnerPortMax, ruleData.InnerPortMin = int32(port), int32(port)
	}

	// not support service bms instance
	if request.DnatRule.Properties.InternalInstanceType == eip.DNATRuleProperties_BMS && eipData.InternalEIP {
		var subnets []*db.Subnets
		subnets, err = dao.ListSubnets(rp.BosonProvider.Engine, eipData.VPCID)
		if err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.DbErr, errors.Domain, nil, nil)
		}
		dataInstance := false
		for _, subnet := range subnets {
			if subnet.Provider == network.SubnetProperties_CONTROLLER.String() && subnet.Scope == network.SubnetProperties_DATA.String() {
				if utilnet.CIDRContainIP(subnet.CIDR, request.DnatRule.Properties.InternalIp) {
					dataInstance = true
					break
				}
			}
		}
		if !dataInstance {
			reason := fmt.Sprintf("EIP dnat rule %s only support data bms instance", request.DnatRuleName)
			err = errors.New(reason)
			logrus.Errorln(reason)
			return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
				"detail": reason,
			})
		}
	}

	// update db
	ruleData.State = eip.DNATRule_UPDATING.String()

	if err := dao.UpdateDnatRule(rp.BosonProvider.Engine, ruleData); err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	// update CR
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetDnat")
	drule, err := rp.BosonProvider.BosonNetClient.Dnats().Get(context.Background(), ruleData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	// annotation&labels
	if drule.ObjectMeta.Annotations == nil {
		drule.ObjectMeta.Annotations = make(map[string]string)
	}
	drule.ObjectMeta.Annotations["boson.sensetime.com/action"] = "update"
	drule.ObjectMeta.Labels["lepton.sensetime.com/instance-type"] = ruleData.InternalInstanceType
	drule.ObjectMeta.Labels["lepton.sensetime.com/instance-name"] = ruleData.InternalInstanceName
	drule.Spec.InnerPorts = fmt.Sprintf("%d", ruleData.InnerPortMin)
	drule.Spec.InternalIP = ruleData.InternalIP

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateDnat")

	_, err = rp.BosonProvider.BosonNetClient.Dnats().Update(context.Background(), drule, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		// to do, not rollback for debug trace
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	//notify the upper layer
	logrus.Infoln("update EIP DNAT rule: ", request.DnatRuleName)

	return invertDnatrule(ruleData), nil
}

func (DNATRulesServer) GetDNATRule(ctx context.Context, request *eip.GetDNATRuleRequest) (res *eip.DNATRule, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.DnatRuleName, "DNATRule", "GetDNATRule")

	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(resourceprovider.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if !eipTypeNameIsDnat(eipData.AssociationType) {
		err = fmt.Errorf("eip type:%s not support dnat", eipData.AssociationType)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, nil)
	}

	dbRTMetrics := exporter.InitDBRTMetrics("GetDNATRule")

	ruleData := db.DnatRules{}
	has, err := rp.BosonProvider.Engine.Where("eip_id = ? and name = ?", eipData.EIPID, request.DnatRuleName).Get(&ruleData)
	dbRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if !has {
		reason := fmt.Sprintf("dnatRule %v not exist", request.DnatRuleName)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DnatNotFound, errors.Domain, nil, nil)
	}

	if ruleData.Deleted {
		reason := fmt.Sprintf("dnatRule %v is deleted", request.DnatRuleName)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}
	if eipData.EIPID != ruleData.EIPID {
		reason := fmt.Sprintf("DnatRule %v not belong to EIP %v", request.DnatRuleName, request.EipName)
		err = errors.New(reason)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	res = &eip.DNATRule{
		Id:           ruleData.ID,
		Name:         ruleData.Name,
		DisplayName:  ruleData.DisplayName,
		Description:  ruleData.Description,
		Uid:          ruleData.DnatRuleID,
		ResourceType: ruleData.ResourceType,
		CreatorId:    ruleData.CreatorID,
		OwnerId:      ruleData.OwnerID,
		TenantId:     ruleData.TenantID,
		Zone:         ruleData.Zone,
		State:        eip.DNATRule_State(eip.DNATRule_State_value[ruleData.State]),
		CreateTime:   timestamppb.New(ruleData.CreateTime),
		UpdateTime:   timestamppb.New(ruleData.UpdateTime),
		Properties: &eip.DNATRuleProperties{
			NatGatewayId:         ruleData.NatGatewayID,
			EipId:                ruleData.EIPID,
			ExternalIp:           ruleData.OuterIP,
			ExternalPort:         fmt.Sprintf("%d", ruleData.OuterPortMax),
			InternalIp:           ruleData.InternalIP,
			Protocol:             ruleData.Protocol,
			InternalPort:         fmt.Sprintf("%d", ruleData.InnerPortMax),
			Priority:             ruleData.Priority,
			InternalInstanceType: eip.DNATRuleProperties_InstanceType(eip.DNATRule_State_value[ruleData.InternalInstanceType]),
			InternalInstanceName: ruleData.InternalInstanceName,
		},
	}

	return res, nil
}

func (DNATRulesServer) CreateDNATRule(ctx context.Context, request *eip.CreateDNATRuleRequest) (res *eip.DNATRule, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.DnatRuleName, "DNATRule", "CreateDNATRule")
	resourceUID := request.DnatRuleName
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditCreateDnatError(ctx, resourceUID, request, struct{}{}, err.Error())
		} else {
			cloudAuditCreateDnatSuccess(ctx, resourceUID, request, struct{}{})
		}
	}()

	logrus.Infof("Create dnatRule called: %+v", request)

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.DnatRule == nil || request.DnatRule.Properties == nil {
		reason := fmt.Sprintf("error request: %+v, dnat rule or dnat rule properties is empty", request)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if request.DnatRule.Properties.InternalInstanceType == eip.DNATRuleProperties_BMS && request.DnatRule.Properties.InternalIp == "" {
		reason := fmt.Sprintf("error request, invaild InstanceType: %+v", request)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.DnatRule.Properties.InternalInstanceType == eip.DNATRuleProperties_ECS && request.DnatRule.Properties.InternalIp == "" {
		reason := fmt.Sprintf("error request, invaild InstanceType: %+v", request)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}
	request.DnatRule.Properties.Protocol = strings.ToLower(request.DnatRule.Properties.Protocol)

	eipData, err := dao.GetEipByID(rp.BosonProvider.Engine, request.DnatRule.Properties.EipId, false)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if !eipTypeNameIsDnat(eipData.AssociationType) {
		err = fmt.Errorf("eip type:%s not support dnat", eipData.AssociationType)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, nil)
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetEIP")
	eipCr, err := rp.BosonProvider.BosonNetClient.EIPs().Get(context.Background(), eipData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	natGw, has, err := dao.GetHaNatGateway(rp.BosonProvider.Engine, request.DnatRule.Properties.NatGatewayId)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	if !has {
		reason := fmt.Sprintf("natgw with id %s not exists", request.DnatRule.Properties.NatGatewayId)
		err = errors.New(reason)
		logrus.Errorf(reason)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.HaNatGwNotFound, errors.Domain, nil, nil)
	}

	// will be removed after all natgw update ha mode, miaozhanyong
	gwName := natGw.Name
	if natGw.HaMde == "disable" {
		natGw, has, errin := dao.GetNATGateway(rp.BosonProvider.Engine, request.DnatRule.Properties.NatGatewayId)
		if errin != nil {
			logrus.Errorln(errin)
			err = errin
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		if !has {
			reason := fmt.Sprintf("natgw with id %s not exists", request.DnatRule.Properties.NatGatewayId)
			err = errors.New(reason)
			logrus.Errorf(reason)
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.NatGwNotFound, errors.Domain, nil, nil)
		}
		gwName = natGw.Name
	}

	timeOut := resourceprovider.BosonProvider.RpConfig.Boson.DnatPgLockTimeOut
	logrus.Infof("pg lock timeout is %d second", timeOut)

	// use az-gwid as seed to shard pg lock
	// here, we not use eip to shard, because the race point is in gw.
	seed := fmt.Sprintf("%s-%s", db.AZ, gwName)
	pglockId := utils.HashStringToInt32(seed)
	logrus.Infof("dnat: %s,init PG Lock ID %d for %s (az-gwName)", request.DnatRuleName, pglockId, seed)

	var dlock pglock.Lock
	childCtx, cancel := context.WithTimeout(ctx, time.Duration(timeOut)*time.Second)
	dbLockRTMetrics := exporter.InitDBRTMetrics("GetEIPLockWhenCreateDnatRule")
	err = func() error {
		defer cancel()
		dlock, err = pglock.NewLock(childCtx, int64(pglockId), rp.BosonProvider.Engine.DB().DB)
		if err != nil {
			logrus.Errorf("dnat: %s, new pglock for pgLockID %d failed, error: %s", request.DnatRuleName, pglockId, err.Error())
			return err
		}
		logrus.Infof("dnat: %s, new pglock for pgLockID %d success", request.DnatRuleName, pglockId)

		err = dlock.WaitAndLock(childCtx)
		if err != nil {
			logrus.Errorf("dnat: %s, get pglock for pgLockID %d failed, error %s", request.DnatRuleName, pglockId, err.Error())
			return err
		}
		return nil
	}()
	dbLockRTMetrics.ObserveDurationAndLog()
	defer dlock.Close()

	defer func() {
		_ = dlock.Unlock(ctx) // protect end here
		logrus.Infof("dnat: %s, unlock pglock for pgLockID %d", request.DnatRuleName, pglockId)
	}()

	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	// 1. validate in protect area by pg lock
	if reason, err := rp.ValidateParametersForCreateDNATRule(request); err != nil {
		logrus.Errorln(reason, err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	if request.DnatRule.CreateTime == nil {
		request.DnatRule.CreateTime = timestamppb.Now()
	}

	if request.DnatRule.UpdateTime == nil {
		request.DnatRule.UpdateTime = request.DnatRule.CreateTime
	}

	// update DB
	dRuleData := db.DnatRules{
		DnatRuleID:           request.DnatRule.Uid,
		DisplayName:          request.DnatRule.DisplayName,
		Description:          request.DnatRule.Description,
		OuterIP:              request.DnatRule.Properties.ExternalIp,
		Protocol:             request.DnatRule.Properties.Protocol,
		InternalIP:           request.DnatRule.Properties.InternalIp,
		InternalInstanceType: request.DnatRule.Properties.InternalInstanceType.String(),
		InternalInstanceName: request.DnatRule.Properties.InternalInstanceName,
		NatGatewayID:         request.DnatRule.Properties.NatGatewayId,
		EIPID:                request.DnatRule.Properties.EipId,
		Priority:             request.DnatRule.Properties.Priority,

		ID:           request.DnatRule.Id,
		Name:         request.DnatRuleName,
		ResourceType: request.DnatRule.ResourceType,
		Zone:         request.DnatRule.Zone,
		State:        eip.DNATRule_CREATING.String(),
		CreatorID:    request.DnatRule.CreatorId,
		OwnerID:      request.DnatRule.OwnerId,
		TenantID:     request.DnatRule.TenantId,
		CreateTime:   request.DnatRule.CreateTime.AsTime(),
		UpdateTime:   request.DnatRule.UpdateTime.AsTime(),
		Deleted:      false,
	}

	port, _ := strconv.ParseInt(request.DnatRule.Properties.InternalPort, 10, 32)
	dRuleData.InnerPortMax, dRuleData.InnerPortMin = int32(port), int32(port)
	port, _ = strconv.ParseInt(request.DnatRule.Properties.ExternalPort, 10, 32)
	dRuleData.OuterPortMin, dRuleData.OuterPortMax = int32(port), int32(port)

	// 2.  allocate gw port in protect area by pg lock
	gwPort, err := rp.AllocateNatGatewayPort(dRuleData.Protocol, dRuleData.NatGatewayID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	}
	dRuleData.NatGatewayPort = gwPort

	err = dao.AddDnatRule(rp.BosonProvider.Engine, &dRuleData)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	// create EipDnatRule CR
	drule := &dnatv1.Dnat{
		TypeMeta: metav1.TypeMeta{
			Kind:       rp.DNATKind,
			APIVersion: dnatv1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: request.DnatRuleName,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(eipCr, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    rp.EIPKind,
				}),
			},
			Annotations: map[string]string{
				"boson.sensetime.com/nat_gw_port": strconv.FormatInt(int64(dRuleData.NatGatewayPort), 10),
				rp.AnnoRegion:                     db.Region,
				rp.AnnoAz:                         db.AZ,
				rp.AnnoEipInternalType:            "false",
			},
		},

		Spec: dnatv1.DnatSpec{
			GatewayName: gwName,
			EIP:         dRuleData.OuterIP,
			OuterPorts:  fmt.Sprintf("%d", dRuleData.OuterPortMin),
			Protocol:    dRuleData.Protocol,
			InternalIP:  dRuleData.InternalIP,
			InnerPorts:  fmt.Sprintf("%d", dRuleData.InnerPortMin),
		},
	}

	if request.DnatRule.Properties.InternalInstanceType != eip.DNATRuleProperties_UNSPECIFIED {
		// to do annotation&labels
		//drule.ObjectMeta.Annotations = make(map[string]string)
		drule.ObjectMeta.Labels = make(map[string]string)
		//drule.ObjectMeta.Annotations["boson.sensetime.com/action"] = "bind"
		drule.ObjectMeta.Labels["lepton.sensetime.com/instance-type"] = dRuleData.InternalInstanceType
		drule.ObjectMeta.Labels["lepton.sensetime.com/instance-name"] = dRuleData.InternalInstanceName
	}

	if eipData.InternalEIP {
		drule.ObjectMeta.Annotations[resourceprovider.AnnoEipInternalType] = "true"
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("CreateDnat")

	_, err = rp.BosonProvider.BosonNetClient.Dnats().Create(context.Background(), drule, metav1.CreateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		// to do, not rollback for debug trace
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	//notify the upper layer
	logrus.Infoln("Created EIP DNAT rule: ", request.DnatRuleName)

	return request.DnatRule, nil
}

// delete rule; unbind first if the rule state is ACTIVE
func (DNATRulesServer) DeleteDNATRule(ctx context.Context, request *eip.DeleteDNATRuleRequest) (res *emptypb.Empty, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics(request.DnatRuleName, "DNATRule", "DeleteDNATRule")
	resourceUID := request.DnatRuleName
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditDeleteDnatError(ctx, resourceUID, request, struct{}{}, err.Error())
		} else {
			cloudAuditDeleteDnatSuccess(ctx, resourceUID, request, struct{}{})
		}
	}()

	dbRTMetrics := exporter.InitDBRTMetrics("DeleteDNATRule")

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(resourceprovider.BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		if strings.Contains(err.Error(), "not found") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	dnat := db.DnatRules{}
	has, err := rp.BosonProvider.Engine.Where("name = ?", request.DnatRuleName).Get(&dnat)
	dbRTMetrics.ObserveDurationAndLog()

	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if !has {
		reason := fmt.Sprintf("EIP dnat rule %v not exist", request.DnatRuleName)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.DnatNotFound, errors.Domain, nil, nil)
	}
	if eipData.EIPID != dnat.EIPID {
		reason := fmt.Sprintf("DnatRule %v not belong to EIP %v", request.DnatRuleName, request.EipName)
		err = errors.New(reason)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if strings.EqualFold(dnat.State, eip.DNATRule_State_name[int32(eip.DNATRule_CREATING)]) ||
		strings.EqualFold(dnat.State, eip.DNATRule_State_name[int32(eip.DNATRule_UPDATING)]) ||
		strings.EqualFold(dnat.State, eip.DNATRule_State_name[int32(eip.DNATRule_BINDING)]) ||
		strings.EqualFold(dnat.State, eip.DNATRule_State_name[int32(eip.DNATRule_UNBINDING)]) {
		reason := fmt.Sprintf("EIP dnat rule %v can't be deleted with state: %v", request.DnatRuleName, dnat.State)
		err = errors.New(reason)
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	if strings.EqualFold(dnat.State, eip.DNATRule_DELETED.String()) {
		return &emptypb.Empty{}, nil
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetDnat")

	_, err = rp.BosonProvider.BosonNetClient.Dnats().Get(context.Background(), request.DnatRuleName, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		if k8s_errors.IsNotFound(err) {
			if err := dao.SetDnatRuleState(rp.BosonProvider.Engine, dnat.DnatRuleID, eip.DNATRule_DELETED.String()); err != nil {
				logrus.Errorln(err)
				return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DnatNotFound, errors.Domain, nil, nil)
			}
		} else {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
		}
	} else {
		// update db
		if err := dao.SetDnatRuleState(rp.BosonProvider.Engine, dnat.DnatRuleID, eip.DNATRule_DELETING.String()); err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		//delete CR
		k8sRTMetrics = exporter.InitK8sRTMetrics("DeleteDnat")
		err = rp.BosonProvider.BosonNetClient.Dnats().Delete(context.Background(), request.DnatRuleName, metav1.DeleteOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil {
			logrus.Errorln(err)
			if err := dao.SetDnatRuleState(rp.BosonProvider.Engine, dnat.DnatRuleID, eip.DNATRule_FAILED.String()); err != nil {
				logrus.Errorln(err)
				return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
			}
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
		}
	}

	logrus.Infoln("Deleting EiP dnat rule: ", request.DnatRuleName)

	return &emptypb.Empty{}, nil
}

// TODO: need confirm: List requested multi-eip DNATRules, but the URL dosen't has eip_name
func (DNATRulesServer) ListMDNATRules(
	ctx context.Context, request *eip.ListMDNATRulesRequest,
) (*eip.ListDNATRulesResponse, error) {
	var err error = nil
	httpRTMetrics := exporter.InitHTTPRTMetrics("", "DNATRule", "ListDNATRules")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	eips := request.GetEipNames()
	if eips == nil {
		err = errors.New("params error, eip_names is nil")
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	request.Filter = fixFilter(request.Filter)
	dnatRules, totalSize, err := getDnatRules(ctx, eips, request)
	if err != nil {
		return nil, err
	}

	nextPageToken, err := aip.CalcNextPageToken(totalSize, len(dnatRules), request)
	if err != nil {
		reason := fmt.Sprintf("calc NextPageToken %v , err: %v", zap.Any("request", request), zap.Error(err))
		logrus.Errorln(reason)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}
	res := &eip.ListDNATRulesResponse{
		TotalSize:     int32(totalSize),
		NextPageToken: nextPageToken,
		DnatRules:     dnatRules,
	}

	logrus.Infof("list mul dnat rules %v", request)
	return res, nil
}

func getDnatRules(ctx context.Context, eipNames []string, requestParam interface{}) ([]*eip.DNATRule, int, error) {
	rules := make([]*eip.DNATRule, 0)
	eipIDs := []string{}
	for _, eipName := range eipNames {
		eipData, err := dao.GetEipByName(rp.BosonProvider.Engine, eipName)
		if err != nil {
			logrus.Errorln(err)
			if strings.Contains(err.Error(), "not found") {
				return nil, 0, errors.NewHiggsError(ctx, codes.NotFound, errors.EipNotFound, errors.Domain, nil, nil)
			}
			return rules, 0, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		if !eipTypeNameIsDnat(eipData.AssociationType) {
			err = fmt.Errorf("eip type:%s not support dnat", eipData.AssociationType)
			logrus.Errorln(err)
			return rules, 0, errors.NewHiggsError(ctx, codes.Internal, errors.InternalErrWithDetial, errors.Domain, nil, map[string]interface{}{
				"detail": err.Error(),
			})
		}

		if eipData.State == eip.EIP_DELETED.String() {
			logrus.Debugln(fmt.Sprintf("eip:%s is deleted", eipName))
			continue
		}
		eipIDs = append(eipIDs, fmt.Sprintf("'%s'", eipData.EIPID))
	}
	if len(eipIDs) == 0 {
		return rules, 0, nil
	}

	pageToken, pageSize, err := aip.ParsePageTokenAndPageSize(requestParam.(pagination.Request))
	if err != nil {
		reason := fmt.Sprintf("invalid parameters to failed to ParsePageTokenAndPageSize %v , err: %v", zap.Any("request", requestParam), zap.Error(err))
		logrus.Errorln(reason)
		return nil, 0, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	declarationOptions := []filtering.DeclarationOption{
		filtering.DeclareStandardFunctions(),
		filtering.DeclareIdent("name", filtering.TypeString),
		filtering.DeclareIdent("display_name", filtering.TypeString),
		filtering.DeclareIdent("inner_ip", filtering.TypeString),
		filtering.DeclareIdent("internal_instance_type", filtering.TypeString),
		filtering.DeclareIdent("internal_instance_name", filtering.TypeString),
		filtering.DeclareIdent("protocol", filtering.TypeString),
		filtering.DeclareIdent("outer_port_min", filtering.TypeInt),
		filtering.DeclareIdent("outer_port_max", filtering.TypeInt),
		filtering.DeclareIdent("inner_port_min", filtering.TypeInt),
		filtering.DeclareIdent("inner_port_max", filtering.TypeInt),
		filtering.DeclareIdent("state", filtering.TypeString),
		filtering.DeclareIdent("create_time", filtering.TypeTimestamp),
		filtering.DeclareIdent("update_time", filtering.TypeTimestamp),
	}

	fieldMap := map[string]string{
		"name":                   "name",
		"inner_ip":               "inner_ip",
		"internal_instance_type": "internal_instance_type",
		"internal_instance_name": "internal_instance_name",
		"protocol":               "protocol",
		"outer_port_min":         "outer_port_min",
		"outer_port_max":         "outer_port_max",
		"inner_port_min":         "inner_port_min",
		"inner_port_max":         "inner_port_max",
		"create_time":            "create_time",
		"update_time":            "update_time",
		"display_name":           "display_name",
		"state":                  "state",
	}

	condition, params, err := aip.ParseFilter(requestParam.(filtering.Request), declarationOptions, fieldMap)
	if err != nil {
		reason := fmt.Sprintf("invalid parameters to failed to ParseFilter %v %v", zap.Any("request", requestParam), zap.Error(err))
		logrus.Errorln(reason)
		return nil, 0, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}
	orderBy, err := aip.ParseOrderBy(requestParam.(ordering.Request), fieldMap)
	if err != nil {
		reason := fmt.Sprintf("invalid parameters to failed to ParseOrderBy %v %v", zap.Any("request", requestParam), zap.Error(err))
		logrus.Errorln(reason)
		return nil, 0, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": reason,
		})
	}

	// 去除双引号
	orderBy = strings.ReplaceAll(orderBy, `"`, "")
	logrus.Infof("list parameter %v %v %v %v %v", pageToken, pageSize, condition, params, orderBy)

	condTmp := utils.FormatCondition(condition, params)
	limit := int(pageSize)
	offset := (pageToken - 1) * int(pageSize)

	baseWhere := fmt.Sprintf("eip_id in (%s) AND deleted = false", strings.Join(eipIDs, ","))

	if len(condTmp) > 0 {
		baseWhere = fmt.Sprintf("%s AND %s", condTmp, baseWhere)
	}

	cond := fmt.Sprintf(
		"SELECT * FROM dnat_rules WHERE %s %s LIMIT %d OFFSET %d",
		baseWhere, orderBy, limit, offset,
	)

	condTotalSize := baseWhere

	logrus.Infof("list dnat rules's sql: %s, total size sql: %s", cond, condTotalSize)

	ruleData := db.DnatRules{}
	dbRTMetrics := exporter.InitDBRTMetrics("ListDNATRules")
	totalSize, err := rp.BosonProvider.Engine.Where(condTotalSize).Count(&ruleData)
	if err != nil {
		dbRTMetrics.ObserveDurationAndLog()
		reason := fmt.Sprintf("query db failed, %v %v", zap.Any("request", requestParam), zap.Error(err))
		logrus.Errorln(reason)
		return nil, 0, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	dbRTMetrics.ObserveDurationAndLog()

	dbRTMetrics = exporter.InitDBRTMetrics("ListDNATRules")
	rulesData := []db.DnatRules{}
	// Limit(int(pageSize)*pageToken, (pageToken-1)*int(pageSize))
	if err := rp.BosonProvider.Engine.SQL(cond).Find(&rulesData); err != nil {
		dbRTMetrics.ObserveDurationAndLog()
		reason := fmt.Sprintf("query db failed, %v %v", zap.Any("request", requestParam), zap.Error(err))
		logrus.Errorln(reason)
		return nil, 0, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	dbRTMetrics.ObserveDurationAndLog()

	for _, ruleData := range rulesData {
		rules = append(rules, invertDnatrule(&ruleData))
	}

	return rules, int(totalSize), nil
}

func fixFilter(filter string) string {
	// 为了符合 aip , 对 filter 进行替换
	// 入参：使用 &、 || 来拼接参数
	// 出参: 使用 " AND "、" OR " 替换 &、 ||
	// 转换port参数outer_port_min inner_port_min

	return strings.ReplaceAll(strings.ReplaceAll(strings.ReplaceAll(filter, "&", " AND "), "||", " OR "), "_port", "_port_min")
}

func cloudAuditCreateDnatError(
	ctx context.Context, resourceID string, request *eip.CreateDNATRuleRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeCreateDnatRules, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceID, request.DnatRule.Name,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditCreateDnatSuccess(
	ctx context.Context, resourceID string, request *eip.CreateDNATRuleRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeCreateDnatRules, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceID, request.DnatRule.Name,
		rp.BosonProvider.Processor.CloudAuditSendMsg("create success"),
	)
}

func cloudAuditUpdateDnatError(
	ctx context.Context, resourceUID string, request *eip.UpdateDNATRuleRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeUpdateDnatRules, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceUID, request.DnatRule.Name,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditUpdateDnatSuccess(
	ctx context.Context, resourceUID string, request *eip.UpdateDNATRuleRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeUpdateDnatRules, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceUID, request.DnatRule.Name,
		rp.BosonProvider.Processor.CloudAuditSendMsg("create success"),
	)
}

func cloudAuditDeleteDnatError(
	ctx context.Context, resourceUID string, request *eip.DeleteDNATRuleRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeDeleteDnatRules, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceUID, request.DnatRuleName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditDeleteDnatSuccess(
	ctx context.Context, resourceUID string, request *eip.DeleteDNATRuleRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeDeleteDnatRules, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceUID, request.DnatRuleName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("create success"),
	)
}

func cloudAuditBindDnatError(
	ctx context.Context, resourceUID string, request *eip.BindDNATRuleRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeBindDnatRules, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceUID, request.DnatRule.Name,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditBindDnatSuccess(
	ctx context.Context, resourceUID string, request *eip.BindDNATRuleRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeBindDnatRules, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceUID, request.DnatRule.Name,
		rp.BosonProvider.Processor.CloudAuditSendMsg("create success"),
	)
}

func cloudAuditUnbindDnatError(
	ctx context.Context, resourceUID string, request *eip.UnbindDNATRuleRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeUnbindDnatRules, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		resourceUID, request.DnatRuleName,
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditUnbindDnatSuccess(
	ctx context.Context, resourceUID string, request *eip.UnbindDNATRuleRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeUnbindDnatRules, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		resourceUID, request.DnatRuleName,
		rp.BosonProvider.Processor.CloudAuditSendMsg("create success"),
	)
}

func cloudAuditBatchUnbindDnatError(
	ctx context.Context, request *eip.UnbindDNATRulesRequest, response interface{}, reason string,
) {
	rp.BosonProvider.Processor.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeBatchUnbindDnatRules, types.CloudAuditEventRatingWarning,
		request, response, http.StatusBadRequest,
		"", "",
		rp.BosonProvider.Processor.CloudAuditSendMsg(reason),
	)
}

func cloudAuditBatchUnbindDnatSuccess(
	ctx context.Context, request *eip.UnbindDNATRulesRequest, response interface{},
) {
	rp.BosonProvider.Processor.CloudAuditSendEipDnat(
		ctx,
		types.CloudAuditOperationTypeBatchUnbindDnatRules, types.CloudAuditEventRatingNormal,
		request, response, http.StatusOK,
		"", "",
		rp.BosonProvider.Processor.CloudAuditSendMsg("create success"),
	)
}
