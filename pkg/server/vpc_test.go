package server

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"google.golang.org/grpc/codes"
)

func TestListVPCsRegionErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.Region, "test")

	request := &vpc.ListVPCsRequest{
		Zone: "test11",
	}

	vpcsServer := VPCsServer{}
	ctx := context.Background()

	_, err := vpcsServer.ListVPCs(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetVPCsRegionErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.Region, "test")

	request := &vpc.GetVPCRequest{
		Zone: "test11",
	}

	vpcsServer := VPCsServer{}
	ctx := context.Background()

	_, err := vpcsServer.GetVPC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetVPCsNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetVpcByID, func(_ *db.EngineWrapper, _ string) (*db.Vpcs, error) {
		return nil, fmt.Errorf("vpc not found")
	})

	p.ApplyGlobalVar(&db.Region, "test")

	request := &vpc.GetVPCRequest{
		Zone: "test",
	}

	vpcsServer := VPCsServer{}
	ctx := context.Background()

	_, err := vpcsServer.GetVPC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetVPCStatusRegionErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.Region, "test")

	request := &vpc.GetVPCStatusRequest{
		Zone: "test11",
	}

	vpcsServer := VPCsServer{}
	ctx := context.Background()

	_, err := vpcsServer.GetVPCStatus(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetVPCStatusNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetVpcByID, func(_ *db.EngineWrapper, _ string) (*db.Vpcs, error) {
		return nil, fmt.Errorf("vpc not found")
	})

	p.ApplyGlobalVar(&db.Region, "test")

	request := &vpc.GetVPCStatusRequest{
		Zone: "test",
	}

	vpcsServer := VPCsServer{}
	ctx := context.Background()

	_, err := vpcsServer.GetVPCStatus(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetVPCDataStatusRegionErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.Region, "test")

	request := &vpc.GetVPCStatusRequest{
		Zone: "test11",
	}

	vpcsServer := VPCsServer{}
	ctx := context.Background()

	_, err := vpcsServer.GetVPCDataStatus(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetVPCDataStatusNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetVpcByID, func(_ *db.EngineWrapper, _ string) (*db.Vpcs, error) {
		return nil, fmt.Errorf("vpc not found")
	})

	p.ApplyGlobalVar(&db.Region, "test")

	request := &vpc.GetVPCStatusRequest{
		Zone: "test",
	}

	vpcsServer := VPCsServer{}
	ctx := context.Background()

	_, err := vpcsServer.GetVPCDataStatus(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestCreateVPC(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetVpcByID, func(_ *db.EngineWrapper, _ string) (*db.Vpcs, error) {
		return nil, fmt.Errorf("vpc not found")
	})

	p.ApplyGlobalVar(&db.Region, "test")

	request := &vpc.CreateVPCRequest{
		Zone: "test",
	}

	vpcsServer := VPCsServer{}
	ctx := context.Background()

	_, err := vpcsServer.CreateVPC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestUpdateVPCDgwNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetDGatewayByID, func(_ *db.EngineWrapper, _ string) (*db.DistributeGateways, bool, error) {
		return nil, false, nil
	})

	p.ApplyGlobalVar(&db.Region, "test")

	request := &vpc.UpdateVPCDgwRequest{
		Zone: "test",
	}

	vpcsServer := VPCsServer{}
	ctx := context.Background()

	_, err := vpcsServer.UpdateVPCDgw(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.VpcDgwNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestUpdateVPC(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetVpcByID, func(_ *db.EngineWrapper, _ string) (*db.Vpcs, error) {
		return nil, fmt.Errorf("vpc not found")
	})

	p.ApplyGlobalVar(&db.Region, "test")

	request := &vpc.UpdateVPCRequest{
		Zone: "test",
	}

	vpcsServer := VPCsServer{}
	ctx := context.Background()

	_, err := vpcsServer.UpdateVPC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteVPC(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.GetVpcByID, func(_ *db.EngineWrapper, _ string) (*db.Vpcs, error) {
		return nil, fmt.Errorf("vpc not found")
	})

	p.ApplyGlobalVar(&db.Region, "test")

	request := &vpc.DeleteVPCRequest{
		Zone: "test",
	}

	vpcsServer := VPCsServer{}
	ctx := context.Background()

	_, err := vpcsServer.DeleteVPC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Unimplemented, errors.ApiUnimplemented, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}
