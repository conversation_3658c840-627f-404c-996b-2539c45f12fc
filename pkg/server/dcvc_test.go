package server

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey"
	"github.com/stretchr/testify/assert"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/dc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"google.golang.org/grpc/codes"
)

func TestCreateDCVCAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditCreateDcvcError, func(ctx context.Context, resourceID string, request *dc.CreateDCVCRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditCreateDcvcSuccess, func(ctx context.Context, resourceID string, request *dc.CreateDCVCRequest,
		response interface{}) {
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.CreateDCVCRequest{
		Zone: "test11",
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.CreateDCVC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestCreateDCVCExist(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditCreateDcvcError, func(ctx context.Context, resourceID string, request *dc.CreateDCVCRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditCreateDcvcSuccess, func(ctx context.Context, resourceID string, request *dc.CreateDCVCRequest,
		response interface{}) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.ExistDcvc, func(_ *db.EngineWrapper, _ string) (bool, error) {
		return true, nil
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.CreateDCVCRequest{
		Zone: "test",
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.CreateDCVC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.AlreadyExists, errors.DcvcExist, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestCreateDCVCParamErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditCreateDcvcError, func(ctx context.Context, resourceID string, request *dc.CreateDCVCRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditCreateDcvcSuccess, func(ctx context.Context, resourceID string, request *dc.CreateDCVCRequest,
		response interface{}) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.ExistDcvc, func(_ *db.EngineWrapper, _ string) (bool, error) {
		return false, nil
	})
	p.ApplyFunc(validateParametersForCreateDcvc, func(_ *dc.CreateDCVCRequest) error {
		return fmt.Errorf("request Properties is nil")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.CreateDCVCRequest{
		Zone: "test",
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.CreateDCVC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request Properties is nil",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestCreateDCVCNotFound1(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditCreateDcvcError, func(ctx context.Context, resourceID string, request *dc.CreateDCVCRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditCreateDcvcSuccess, func(ctx context.Context, resourceID string, request *dc.CreateDCVCRequest,
		response interface{}) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
		RpConfig: &config.Config{
			Boson: config.BosonDefault{
				DcVxlanCIDR: "1.1.1.1/16",
			},
		},
	})
	p.ApplyFunc(dao.ExistDcvc, func(_ *db.EngineWrapper, _ string) (bool, error) {
		return false, nil
	})
	p.ApplyFunc(validateParametersForCreateDcvc, func(_ *dc.CreateDCVCRequest) error {
		return nil
	})
	p.ApplyFunc(AddDcvcFromAllocateVxlanResources, func(_ context.Context, _ *dc.CreateDCVCRequest, _ string, _ []string) (*db.Dcvcs, error) {
		return &db.Dcvcs{}, nil
	})
	p.ApplyFunc(AddDCHC, func(_ *dc.CreateDCVCRequest) (*db.Dchcs, error) {
		return &db.Dchcs{}, nil
	})
	p.ApplyFunc(dao.GetVpc, func(_ *db.EngineWrapper, _ string) (*db.Vpcs, error) {
		return nil, fmt.Errorf("VPC not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.CreateDCVCRequest{
		Zone: "test",
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.CreateDCVC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.VpcNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestCreateDCVCNotFound2(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditCreateDcvcError, func(ctx context.Context, resourceID string, request *dc.CreateDCVCRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditCreateDcvcSuccess, func(ctx context.Context, resourceID string, request *dc.CreateDCVCRequest,
		response interface{}) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
		RpConfig: &config.Config{
			Boson: config.BosonDefault{
				DcVxlanCIDR: "1.1.1.1/16",
			},
		},
	})
	p.ApplyFunc(dao.ExistDcvc, func(_ *db.EngineWrapper, _ string) (bool, error) {
		return false, nil
	})
	p.ApplyFunc(validateParametersForCreateDcvc, func(_ *dc.CreateDCVCRequest) error {
		return nil
	})
	p.ApplyFunc(AddDcvcFromAllocateVxlanResources, func(_ context.Context, _ *dc.CreateDCVCRequest, _ string, _ []string) (*db.Dcvcs, error) {
		return &db.Dcvcs{}, nil
	})
	p.ApplyFunc(AddDCHC, func(_ *dc.CreateDCVCRequest) (*db.Dchcs, error) {
		return &db.Dchcs{}, nil
	})
	p.ApplyFunc(dao.GetVpc, func(_ *db.EngineWrapper, _ string) (*db.Vpcs, error) {
		return &db.Vpcs{}, nil
	})
	p.ApplyFunc(dao.GetDcgwByName, func(_ *db.EngineWrapper, _ string) (*db.Dcgws, error) {
		return nil, fmt.Errorf("Dcgw not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.CreateDCVCRequest{
		Zone: "test",
		Dcvc: &dc.DCVC{
			Properties: &dc.DCVCProperties{
				DcgwName: "test",
			},
		},
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.CreateDCVC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.DcgwNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestCreateDCVCNotFound3(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditCreateDcvcError, func(ctx context.Context, resourceID string, request *dc.CreateDCVCRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditCreateDcvcSuccess, func(ctx context.Context, resourceID string, request *dc.CreateDCVCRequest,
		response interface{}) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
		RpConfig: &config.Config{
			Boson: config.BosonDefault{
				DcVxlanCIDR: "1.1.1.1/16",
			},
		},
	})
	p.ApplyFunc(dao.ExistDcvc, func(_ *db.EngineWrapper, _ string) (bool, error) {
		return false, nil
	})
	p.ApplyFunc(validateParametersForCreateDcvc, func(_ *dc.CreateDCVCRequest) error {
		return nil
	})
	p.ApplyFunc(AddDcvcFromAllocateVxlanResources, func(_ context.Context, _ *dc.CreateDCVCRequest, _ string, _ []string) (*db.Dcvcs, error) {
		return &db.Dcvcs{}, nil
	})
	p.ApplyFunc(AddDCHC, func(_ *dc.CreateDCVCRequest) (*db.Dchcs, error) {
		return &db.Dchcs{}, nil
	})
	p.ApplyFunc(dao.GetVpc, func(_ *db.EngineWrapper, _ string) (*db.Vpcs, error) {
		return &db.Vpcs{}, nil
	})
	p.ApplyFunc(dao.GetDcgwByName, func(_ *db.EngineWrapper, _ string) (*db.Dcgws, error) {
		return &db.Dcgws{
			DeployMode: NSA,
		}, nil
	})
	p.ApplyFunc(dao.GetHaNatGatewayByVpcId, func(_ *db.EngineWrapper, _ string) (*db.HaNatGateways, bool, error) {
		return nil, false, nil
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.CreateDCVCRequest{
		Zone: "test",
		Dcvc: &dc.DCVC{
			Properties: &dc.DCVCProperties{
				DcgwName: "test",
			},
		},
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.CreateDCVC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.HaNatGwNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteDCVCAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditDeleteDcvcError, func(ctx context.Context, resourceID string, request *dc.DeleteDCVCRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditDeleteDcvcSuccess, func(ctx context.Context, resourceID string, request *dc.DeleteDCVCRequest,
		response interface{}) {
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.DeleteDCVCRequest{
		Zone: "test11",
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.DeleteDCVC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteDCVCNotFound(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditDeleteDcvcError, func(ctx context.Context, resourceID string, request *dc.DeleteDCVCRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditDeleteDcvcSuccess, func(ctx context.Context, resourceID string, request *dc.DeleteDCVCRequest,
		response interface{}) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.ExistDcvcResource, func(_ *db.EngineWrapper, _ string, _ string, _ string) (bool, error) {
		return false, nil
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.DeleteDCVCRequest{
		Zone:              "test",
		DcvcName:          "",
		SubscriptionName:  "",
		ResourceGroupName: "",
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.DeleteDCVC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.DcvcNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestUpdateDCVCAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditUpdateDcvcError, func(ctx context.Context, resourceID string, request *dc.UpdateDCVCRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditUpdateDcvcSuccess, func(ctx context.Context, resourceID string, request *dc.UpdateDCVCRequest,
		response interface{}) {
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.UpdateDCVCRequest{
		Zone: "test11",
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.UpdateDCVC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestUpdateDCVCParamErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditUpdateDcvcError, func(ctx context.Context, resourceID string, request *dc.UpdateDCVCRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditUpdateDcvcSuccess, func(ctx context.Context, resourceID string, request *dc.UpdateDCVCRequest,
		response interface{}) {
	})

	p.ApplyGlobalVar(&db.AZ, "test")
	displayName := ""
	request := &dc.UpdateDCVCRequest{
		Zone: "test",
		Dcvc: &dc.DCVCUpdateProperties{
			DisplayName: &displayName,
			Description: "",
			Properties:  nil,
		},
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.UpdateDCVC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "just support to update DisplayName、Description、Properties",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestUpdateDCVCNotFound1(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditUpdateDcvcError, func(ctx context.Context, resourceID string, request *dc.UpdateDCVCRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditUpdateDcvcSuccess, func(ctx context.Context, resourceID string, request *dc.UpdateDCVCRequest,
		response interface{}) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.ExistDcvcResource, func(_ *db.EngineWrapper, _ string, _ string, _ string) (bool, error) {
		return false, nil
	})

	p.ApplyGlobalVar(&db.AZ, "test")
	displayName := "test"
	request := &dc.UpdateDCVCRequest{
		Zone: "test",
		Dcvc: &dc.DCVCUpdateProperties{
			DisplayName: &displayName,
			Description: "test",
			Properties:  nil,
		},
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.UpdateDCVC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.DcvcNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestUpdateDCVCNotFound2(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyFunc(cloudAuditUpdateDcvcError, func(ctx context.Context, resourceID string, request *dc.UpdateDCVCRequest,
		response interface{}, reason string) {
	})
	p.ApplyFunc(cloudAuditUpdateDcvcSuccess, func(ctx context.Context, resourceID string, request *dc.UpdateDCVCRequest,
		response interface{}) {
	})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.ExistDcvcResource, func(_ *db.EngineWrapper, _ string, _ string, _ string) (bool, error) {
		return true, nil
	})
	p.ApplyFunc(dao.GetDcvcByName, func(_ *db.EngineWrapper, _ string) (*db.Dcvcs, error) {
		return nil, fmt.Errorf("Dcvc not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")
	displayName := "test"
	request := &dc.UpdateDCVCRequest{
		Zone: "test",
		Dcvc: &dc.DCVCUpdateProperties{
			DisplayName: &displayName,
			Description: "test",
			Properties:  nil,
		},
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.UpdateDCVC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.DcvcNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetDCVCAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.GetDCVCRequest{
		Zone: "test11",
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.GetDCVC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetDCVCNotFound1(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.ExistDcvcResource, func(_ *db.EngineWrapper, _ string, _ string, _ string) (bool, error) {
		return false, nil
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.GetDCVCRequest{
		Zone: "test",
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.GetDCVC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.DcvcNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestGetDCVCNotFound2(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})
	p.ApplyGlobalVar(&rp.BosonProvider, &rp.ResourceProvider{
		Engine: db.NewEngineWrapper(mockEngine),
	})
	p.ApplyFunc(dao.ExistDcvcResource, func(_ *db.EngineWrapper, _ string, _ string, _ string) (bool, error) {
		return true, nil
	})
	p.ApplyFunc(dao.GetDcvcByName, func(_ *db.EngineWrapper, _ string) (*db.Dcvcs, error) {
		return nil, fmt.Errorf("Dcvc not found")
	})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.GetDCVCRequest{
		Zone: "test",
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.GetDCVC(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.DcvcNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestListDCVCAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &dc.ListDCVCsRequest{
		Zone: "test11",
	}

	dcvcsServer := DCVCsServer{}
	ctx := context.Background()

	_, err := dcvcsServer.ListDCVCs(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}
