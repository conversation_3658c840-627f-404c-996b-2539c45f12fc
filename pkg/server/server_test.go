package server

import (
	"reflect"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	ed "gitlab.bj.sensetime.com/elementary/higgs/higgs-apis/api/higgs/error_detail/v1"
	"google.golang.org/grpc/status"
	"xorm.io/xorm"
)

var mockEngine = &xorm.Engine{}

func init() {
	errors.NewBundle()
}

func CompareHiggsError(err1, err2 error) bool {
	st1, ok1 := status.FromError(err1)
	st2, ok2 := status.FromError(err2)

	if !ok1 || !ok2 {
		return false
	}

	if st1.Code() != st2.Code() || st1.Message() != st2.Message() {
		return false
	}

	details1 := st1.Details()
	details2 := st2.Details()

	if len(details1) != len(details2) {
		return false
	}

	for i := range details1 {

		if _, ok := details1[i].(*ed.LogInfo); ok {
			continue
		}
		if _, ok := details2[i].(*ed.LogInfo); ok {
			continue
		}
		// 比较每个 detail 的具体内容，这里使用 reflect.DeepEqual
		if !reflect.DeepEqual(details1[i], details2[i]) {
			return false
		}
	}
	return true
}
