package server

import (
	"context"
	"fmt"
	"strings"

	"github.com/sirupsen/logrus"
	netControllerV1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	slbv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/slb/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func (s SLBsServer) createTargetGroupBosonService(
	ctx context.Context, request *slbv1.CreateTargetGroupRequest, slbRecord *db.Slbs,
) error {
	switch request.TargetGroup.Properties.Type {
	case slbv1.TargetGroupProperties_CCI_DEPLOYMENT_INSTANCE:
		return s.createTargetGroupBosonServiceCci(ctx, request, slbRecord)
	}
	return nil
}

func (s SLBsServer) updateTargetGroupBosonService(
	ctx context.Context, request *slbv1.UpdateTargetGroupRequest, slbRecord *db.Slbs, targetGroupNew *slbv1.TargetGroup,
) error {
	switch targetGroupNew.Properties.Type {
	case slbv1.TargetGroupProperties_CCI_DEPLOYMENT_INSTANCE:
		return s.updateTargetGroupBosonServiceCci(ctx, slbRecord, targetGroupNew)
	}
	return nil
}

func (s SLBsServer) deleteTargetGroupBosonService(ctx context.Context,
	request *slbv1.DeleteTargetGroupRequest, slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups,
) error {
	switch targetGroupRecord.Type {
	case targetGroupTypeName(slbv1.TargetGroupProperties_CCI_DEPLOYMENT_INSTANCE):
		return s.deleteTargetGroupBosonServiceCci(ctx, request, slbRecord, targetGroupRecord)
	}
	return nil
}

func (s SLBsServer) createTargetGroupBosonServiceCci(
	ctx context.Context, request *slbv1.CreateTargetGroupRequest, slbRecord *db.Slbs,
) error {
	// CR,DB of TargetGroup is already
	// .1 if cci is not nil, register boson service
	tgDB, err := getTargetGroupDB(slbRecord.SlbID, request.TargetGroup.Name)
	if err != nil {
		return err
	}
	if tgDB.CciDeploymentInstanceName != "" {
		err = registerBosonServiceCci(slbRecord, request.TargetGroup.Name, tgDB.CciDeploymentInstanceName)
		if err != nil {
			return err
		}
	}

	// .2 others none
	return nil
}

func (s SLBsServer) updateTargetGroupBosonServiceCci(
	ctx context.Context, slbRecord *db.Slbs, targetGroupNew *slbv1.TargetGroup,
) error {
	// CR,DB of TargetGroup is already
	// .1 if not change return
	serviceOld, err := getBosonServiceOld(slbRecord.Name, targetGroupNew.Name)
	if err != nil {
		return err
	}
	serviceNewName := cciBosonServiceName(slbRecord.SlbID, targetGroupNew.Name, targetGroupNew.Properties.CciInfo.Name)

	if serviceOld == nil {
		if targetGroupNew.Properties.CciInfo.Name == "" {
			return nil
		} else {
			// nil -> new
			// .1 register new boson service if not exist
			err := registerBosonServiceCciIfNotExist(slbRecord, targetGroupNew.Name, targetGroupNew.Properties.CciInfo.Name)
			if err != nil {
				return err
			}

			// .x none
		}
	} else {
		if targetGroupNew.Properties.CciInfo.Name == "" {
			// old -> nil
			// .1 unregister old boson service
			err := unregisterBosonServiceCci(serviceOld)
			if err != nil {
				return err
			}
			// .x none
		} else {
			// old -> new
			if serviceOld.ServiceName != serviceNewName {
				// cci change to other
				// .1 unregister old boson service
				err := unregisterBosonServiceCci(serviceOld)
				if err != nil {
					return err
				}

				// .2 register new boson service
				err = registerBosonServiceCciIfNotExist(slbRecord, targetGroupNew.Name, targetGroupNew.Properties.CciInfo.Name)
				if err != nil {
					return err
				}

				// .x none
			} /* else {
				// cci info update
				// none
			}*/
		}
	}

	return nil
}

func (s SLBsServer) deleteTargetGroupBosonServiceCci(ctx context.Context,
	request *slbv1.DeleteTargetGroupRequest, slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups,
) error {
	// CR,DB of TargetGroup is removing

	serviceOld, err := getBosonServiceOld(slbRecord.Name, targetGroupRecord.Name)
	if err != nil {
		return err
	}
	if serviceOld != nil {
		err = unregisterBosonServiceCci(serviceOld)
		if err != nil {
			return err
		}
	}

	return nil
}

func getTargetGroupDB(slbID string, targetGroupName string) (*db.SlbTargetGroups, error) {
	engine := resourceprovider.BosonProvider.Engine
	tgDB, err := dao.GetSLBTargetGroupByName(engine, slbID, targetGroupName)
	if err != nil {
		logrus.Error("get target group fail", "slb", slbID, "target group", targetGroupName, "error", err)
		return nil, err
	}

	return tgDB, nil
}

func registerBosonServiceCci(slbRecord *db.Slbs, targetGroupName string, cciName string) error {
	engine := resourceprovider.BosonProvider.Engine
	vpcDb, err := dao.GetVpc(engine, slbRecord.VpcID)
	if err != nil {
		logrus.Error(
			"create BosonServiceCCI fail, get vpc error",
			"slb", slbRecord.Name, "target group", targetGroupName, "vpc", slbRecord.VpcID,
		)
		return err
	}
	vpcCr, err := resourceprovider.BosonProvider.KubeovnClient.Vpcs().Get(context.Background(), vpcDb.Name, metav1.GetOptions{})
	if err != nil {
		logrus.Error(
			"create BosonServiceCCI fail, get vpc cr error",
			"slb", slbRecord.Name, "target group", targetGroupName, "vpc", vpcDb.Name,
		)
		return err
	}

	labels := map[string]string{
		netControllerV1.BosonServiceLabels_Kind:              string(netControllerV1.BosonServiceKind_CCI),
		netControllerV1.BosonServiceLabels_OwnerResourceKind: string(netControllerV1.BosonServiceOwnerKind_SLB),
		netControllerV1.BosonServiceLabels_OwnerResourceID:   slbRecord.Name,
	}

	service := &netControllerV1.BosonService{
		ObjectMeta: metav1.ObjectMeta{
			Name:   cciBosonServiceName(slbRecord.SlbID, targetGroupName, cciName),
			Labels: labels,
		},
		Spec: netControllerV1.BosonServiceSpec{
			Namespace: vpcCr.Spec.Namespaces[0],
			Name:      cciName,
			Kind:      string(netControllerV1.BosonServiceKind_CCI),
			Zone:      slbRecord.Zone,
		},
	}
	serviceClient := resourceprovider.BosonProvider.BosonNetClient.BosonServices()
	_, err = serviceClient.Create(context.Background(), service, metav1.CreateOptions{})
	if err != nil {
		logrus.Error(
			"register boson service cci fail, create CR error",
			"slb", slbRecord.SlbID, "target group", targetGroupName, "cci", cciName,
			"error", err,
		)
		return err
	}

	// db
	serviceDB := db.BosonServices{
		RefResourceID:     cciBosonServiceRefResourceID(slbRecord.Name, targetGroupName),
		ServiceName:       service.Name,
		EndpointNamespace: service.Spec.Namespace,
		EndpointName:      service.Spec.Name,
		EndpointKind:      service.Spec.Kind,
		EndpointZone:      service.Spec.Zone,
		Deleted:           false,
	}
	err = dao.InsertBosonService(engine, serviceDB)
	if err != nil {
		logrus.Error(
			"register boson service cci fail, create DB error",
			"slb", slbRecord.SlbID, "target group", targetGroupName, "cci", cciName,
			"error", err,
		)
		return err
	}
	return nil
}

func unregisterBosonServiceCci(service *db.BosonServices) error {
	serviceClient := resourceprovider.BosonProvider.BosonNetClient.BosonServices()
	err := serviceClient.Delete(context.Background(), service.ServiceName, metav1.DeleteOptions{})
	if err != nil {
		logrus.Error(
			"unregister boson service cci fail, delete CR error",
			"service", service.ServiceName, "error", err,
		)
		return err
	}

	// db
	// 在 informer 里面处理
	return nil
}

func registerBosonServiceCciIfNotExist(slbRecord *db.Slbs, targetGroupName string, cciName string) error {
	serviceName := cciBosonServiceName(slbRecord.SlbID, targetGroupName, cciName)
	serviceNewCheck, err := bosonServiceByName(serviceName)
	if err != nil {
		return nil
	}
	if serviceNewCheck != nil {
		return nil
	}
	err = registerBosonServiceCci(slbRecord, targetGroupName, cciName)
	if err != nil {
		return err
	}
	return nil
}

func bosonServiceByResourceID(resourceID string) ([]*db.BosonServices, error) {
	engine := resourceprovider.BosonProvider.Engine
	services, err := dao.ListBosonServicesByRefResourceID(engine, resourceID)
	if err != nil {
		logrus.Error("get boson service by resourceID fail, get db error", "resourceID", resourceID, "error", err)
		return nil, err
	}
	return services, nil
}

func bosonServiceByName(serviceName string) (*db.BosonServices, error) {
	if len(serviceName) == 0 {
		return nil, nil
	}

	engine := resourceprovider.BosonProvider.Engine
	service, err := dao.GetBosonService(engine, serviceName)
	if err != nil {
		logrus.Error("get boson service by serviceName fail, get db error", "serviceName", serviceName, "error", err)
		return nil, err
	}
	if service == nil {
		return nil, nil
	}
	return service, nil
}

func cciBosonServiceName(slbID string, targetGroupName string, cciName string) string {
	return fmt.Sprintf("%s.%s.%s", slbID, targetGroupName, cciName)
}

const CCI_RefResourceID_SEP = "."

func cciBosonServiceRefResourceID(slbName string, targetGroupName string) string {
	return fmt.Sprintf("%s%s%s", slbName, CCI_RefResourceID_SEP, targetGroupName)
}

func parseCciBosonServiceRefResourceID(refResourceID string) (string, string) {
	ss := strings.Split(refResourceID, CCI_RefResourceID_SEP)
	slbName := ss[0]
	tgName := ss[1]
	return slbName, tgName
}

func getBosonServiceOld(slbName string, targetGroupName string) (*db.BosonServices, error) {
	services, err := bosonServiceByResourceID(cciBosonServiceRefResourceID(slbName, targetGroupName))
	if err != nil {
		return nil, err
	}
	var serviceOld *db.BosonServices
	if len(services) == 0 {
		// 不存在
		serviceOld = nil
	} else if len(services) == 1 {
		// 存在
		serviceOld = services[0]
	} else {
		// 绑定多个，不支持
		err = fmt.Errorf("target group not support BIND MUL CCI, target group:%s", targetGroupName)
		logrus.Error(err)
		return nil, err
	}

	return serviceOld, nil
}

func bosonServiceEndpointsByServiceName(serviceName string) ([]*db.BosonServiceEndpoints, error) {
	engine := resourceprovider.BosonProvider.Engine
	endpoints, err := dao.ListBosonServiceEndpoints(engine, serviceName)
	if err != nil {
		logrus.Error("list boson service endpoints by serviceName fail, get db error", "serviceName", serviceName, "error", err)
		return nil, err
	}
	return endpoints, nil
}

func BosonServiceEndpointsUpdateForCci(serviceName string) error {
	engine := resourceprovider.BosonProvider.Engine
	serviceDB, err := dao.GetBosonService(engine, serviceName)
	if err != nil {
		logrus.Error("update boson service by serviceName fail, get service db error", "serviceName", serviceName, "error", err)
		return err
	}

	slbName, tgName := parseCciBosonServiceRefResourceID(serviceDB.RefResourceID)
	slbDB, err := dao.GetSLBByName(engine, slbName, false)
	if err != nil {
		logrus.Error(
			"update boson service by serviceName fail, get slb db error",
			"serviceName", serviceName, "ref resourceID", serviceDB.RefResourceID, "slb", slbName, "error", err,
		)
		return err
	}
	targetGroupDB, err := dao.GetSLBTargetGroupByNameExist(engine, slbDB.SlbID, tgName)
	if err != nil {
		logrus.Error(
			"update boson service by serviceName fail, get slb db error",
			"serviceName", serviceName, "ref resourceID", serviceDB.RefResourceID,
			"slb", slbName, "target group", tgName, "error", err,
		)
		return err
	}
	if targetGroupDB == nil {
		logrus.Info(
			"update boson service by serviceName skip, target group not exist",
			"serviceName", serviceName, "ref resourceID", serviceDB.RefResourceID,
			"slb", slbName, "target group", tgName,
		)
		return nil
	}

	err = updateForEndpointsChange(slbDB, targetGroupDB)
	if err != nil {
		logrus.Error(
			"update boson service by serviceName fail, change process error",
			"serviceName", serviceName, "ref resourceID", serviceDB.RefResourceID,
			"slb", slbName, "target group", tgName, "error", err,
		)
		return err
	}
	return nil
}
