package server

import (
	"context"
	"encoding/json"
	"fmt"
	"reflect"
	"regexp"
	"sort"
	"strings"

	"github.com/sirupsen/logrus"
	netControllerV1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	slbv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/slb/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"google.golang.org/grpc/codes"
	"google.golang.org/protobuf/types/known/emptypb"
	"google.golang.org/protobuf/types/known/timestamppb"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
)

func (s SLBsServer) CreateTargetGroup(ctx context.Context, request *slbv1.CreateTargetGroupRequest) (
	targetGroup *slbv1.TargetGroup, err error,
) {
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics(request.TargetGroupName, "TargetGroup", "CreateTargetGroup")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditCreateSlbTargetGroupError(ctx, request.TargetGroupName, request, struct{}{}, err.Error())
		} else {
			cloudAuditCreateSlbTargetGroupSuccess(ctx, request.TargetGroupName, request, struct{}{})
		}
	}()

	s.createTargetGroupRequestFill(ctx, request)
	logrus.Info("Create TargetGroup start.", "slb ", request.SlbName, "TargetGroup ", request.TargetGroupName, "request", request)
	slbRecord, err := s.createTargetGroupGetWithCheck(ctx, request)
	if err != nil {
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	err = s.createTargetGroupCR(ctx, request, slbRecord)
	if err != nil {
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.CrErr, errors.Domain, nil, nil)
	}

	err = s.createTargetGroupDB(ctx, request, slbRecord)
	if err != nil {
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.DbErr, errors.Domain, nil, nil)
	}

	err = s.createTargetGroupBosonService(ctx, request, slbRecord)
	if err != nil {
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.ServiceError, errors.Domain, nil, nil)
	}

	err = s.createHealthCheckDB(ctx, request)
	if err != nil {
		return nil, errors.NewHiggsError(ctx, codes.FailedPrecondition, errors.DbErr, errors.Domain, nil, nil)
	}

	engine := resourceprovider.BosonProvider.Engine
	resourceID := dao.TargetGroupResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName)
	targetGroupRecord, err := dao.GetSLBTargetGroupByResourceId(engine, resourceID)
	if err != nil {
		logrus.Errorf("Generate targetGroup fail. get targetGroup err:%s\n", err)
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbTargetGroupNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	targetGroup, err = s.generateTargetGroup(slbRecord, targetGroupRecord)
	if err != nil {
		logrus.Errorf("Generate targetGroup fail. err:%s\n", err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	}

	logrus.Info("Create TargetGroup success", "slb", request.SlbName, "TargetGroup", request.TargetGroupName, "request", request)
	return targetGroup, nil
}
func (s SLBsServer) UpdateTargetGroup(
	ctx context.Context, request *slbv1.UpdateTargetGroupRequest,
) (targetGroupResponse *slbv1.TargetGroup, err error) {
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics(request.TargetGroupName, "TargetGroup", "UpdateTargetGroup")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditUpdateSlbTargetGroupError(ctx, request.TargetGroupName, request, struct{}{}, err.Error())
		} else {
			cloudAuditUpdateSlbTargetGroupSuccess(ctx, request.TargetGroupName, request, struct{}{})
		}
	}()

	logrus.Infof("update target group:%+v starting\n", request)

	slbRecord, targetGroupNew, err := s.updateTargetGroupGetWithCheck(ctx, request)
	if err != nil {
		logrus.Error("update targetGroup fail. get slb err:", err.Error())
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbTargetGroupNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	logrus.Infof("slb:%s update target group new:%+v\n", slbRecord.Name, targetGroupNew)

	err = s.updateTargetGroupCR(ctx, request, slbRecord, targetGroupNew)
	if err != nil {
		logrus.Error("update targetGroup fail. update CR err:", err.Error())
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	err = s.updateTargetGroupDB(ctx, request, slbRecord, targetGroupNew)
	if err != nil {
		logrus.Error("update targetGroup fail. update targetGroup err:", err.Error())
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	err = s.updateHealthCheckDB(ctx, targetGroupNew.Uid, targetGroupNew)
	if err != nil {
		logrus.Error("update targetGroup fail. health check err:", err.Error())
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	err = s.updateTargetGroupBosonService(ctx, request, slbRecord, targetGroupNew)
	if err != nil {
		logrus.Error("update targetGroup fail. update targetGroup boson service err:", err.Error())
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.CrErr, errors.Domain, nil, nil)
	}

	targetGroupRecord, err := s.getTargetGroupRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName,
	)
	if err != nil {
		logrus.Error("update targetGroup fail. get targetGroup DB err:", err.Error())
		return nil, err
	}

	targetGroupResponse, err = s.generateTargetGroup(slbRecord, targetGroupRecord)
	if err != nil {
		logrus.Error("update targetGroup fail. generate targetGroup response err:", err.Error())
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbTargetGroupNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	return targetGroupResponse, nil
}
func (s SLBsServer) DeleteTargetGroup(ctx context.Context, request *slbv1.DeleteTargetGroupRequest) (rsp *emptypb.Empty, err error) {
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics(request.TargetGroupName, "TargetGroup", "DeleteTargetGroup")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()

		if err != nil {
			cloudAuditDeleteSlbTargetGroupError(ctx, request.TargetGroupName, request, struct{}{}, err.Error())
		} else {
			cloudAuditDeleteSlbTargetGroupSuccess(ctx, request.TargetGroupName, request, struct{}{})
		}
	}()

	engine := resourceprovider.BosonProvider.Engine
	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		logrus.Errorf("Delete targetGroup fail, slb:%s not exist, err:%s\n", request.SlbName, err)
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	targetGroupRecord, err := s.getTargetGroupRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName,
	)
	if err != nil {
		logrus.Errorf(
			"Delete targetGroup fail, slb:%s targetGroup:%s not exist, err:%s\n",
			request.SlbName, request.TargetGroupName, err,
		)
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbTargetGroupNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	if targetGroupRecord.Deleted {
		logrus.Info("TargetGroup already deleted", "slb", request.SlbName, "targetGroup", request.TargetGroupName)
		return &emptypb.Empty{}, nil
	}

	// delete check
	err = s.deleteTargetGroupCheck(ctx, slbRecord, targetGroupRecord)
	if err != nil {
		logrus.Errorf(
			"Delete targetGroup fail, slb:%s targetGroup:%s delete-check, err:%s\n",
			request.SlbName, request.TargetGroupName, err,
		)
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	// CR delete
	err = s.deleteTargetGroupCR(ctx, request, slbRecord, targetGroupRecord)
	if err != nil {
		logrus.Errorf(
			"Delete targetGroup fail, slb:%s targetGroup:%s delete CR, err:%s\n",
			request.SlbName, request.TargetGroupName, err,
		)
		return &emptypb.Empty{}, nil
	}

	// DB deleting
	err = dao.DeleteHealthCheck(engine, targetGroupRecord.TargetGroupID)
	if err != nil {
		logrus.Errorf(
			"Delete targetGroup fail, slb:%s targetGroup:%s set db deleting err:%s\n",
			request.SlbName, request.TargetGroupName, err,
		)
		return &emptypb.Empty{}, nil
	}

	err = dao.SetTargetGroupState(
		engine, slbRecord.SlbID, targetGroupRecord.TargetGroupID,
		targetGroupStateName(slbv1.TargetGroup_DELETING),
	)
	if err != nil {
		logrus.Errorf(
			"Delete targetGroup fail, slb:%s targetGroup:%s set db deleting err:%s\n",
			request.SlbName, request.TargetGroupName, err,
		)
		// 删除不报错
		return &emptypb.Empty{}, nil
	}

	err = s.deleteTargetGroupBosonService(ctx, request, slbRecord, targetGroupRecord)
	if err != nil {
		logrus.Errorf(
			"Delete targetGroup fail, slb:%s targetGroup:%s delete boson service CR, err:%s\n",
			request.SlbName, request.TargetGroupName, err,
		)
		return &emptypb.Empty{}, nil
	}

	logrus.Infof(
		"Delete targetGroup, slb:%s targetGroup:%s set state:%s\n",
		request.SlbName, request.TargetGroupName, targetGroupStateName(slbv1.TargetGroup_DELETING),
	)

	return &emptypb.Empty{}, nil
}
func (s SLBsServer) ListTargetGroups(
	ctx context.Context, request *slbv1.ListTargetGroupsRequest,
) (*slbv1.ListTargetGroupsResponse, error) {
	var err error = nil
	var slbRecord *db.Slbs
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics("", "TargetGroups", "ListTargetGroups")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	slbRecord, err = s.listTargetGroupsCheckAndGet(ctx, request)
	if err != nil {
		return nil, errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
			"detail": err.Error(),
		})
	}

	targetGroupResponse := &slbv1.ListTargetGroupsResponse{}
	engine := resourceprovider.BosonProvider.Engine
	targetGroupRecords, err := dao.GetSLBTargetGroupsBySlbID(engine, slbRecord.SlbID, false)
	if err != nil {
		logrus.Errorf("list slb:%s targetGroups fail. list DBs err:%s", slbRecord.SlbID, err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}
	for _, targetGroupRecord := range targetGroupRecords {
		targetGroupStatus, err := s.generateTargetGroupStatus(slbRecord, targetGroupRecord)
		if err != nil {
			logrus.Errorf("list slb:%s targetGroups fail. generateTargetGroupStatus:%s", slbRecord.SlbID, err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		targetGroupResponse.TargetGroups = append(targetGroupResponse.TargetGroups, targetGroupStatus)
	}

	targetGroupResponse.NextPageToken = "0"
	targetGroupResponse.TotalSize = int32(len(targetGroupResponse.TargetGroups))

	return targetGroupResponse, nil
}
func (s SLBsServer) GetTargetGroup(ctx context.Context, request *slbv1.GetTargetGroupRequest) (targetGroup *slbv1.TargetGroup, err error) {
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics("", "TargetGroups", "GetTargetGroup")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		logrus.Errorf("Get TargetGroup fail, slb:%s not exist, err:%s\n", request.SlbName, err)
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	targetGroupRecord, err := s.getTargetGroupRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName,
	)
	if err != nil {
		logrus.Errorf(
			"Get TargetGroup fail, slb:%s targetGroup:%s not exist, err:%s\n",
			request.SlbName, request.TargetGroupName, err,
		)
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbTargetGroupNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	targetGroup, err = s.generateTargetGroup(slbRecord, targetGroupRecord)
	if err != nil {
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	return targetGroup, nil
}
func (s SLBsServer) GetTargetGroupStatus(
	ctx context.Context, request *slbv1.GetTargetGroupStatusRequest,
) (targetGroupStatusResponse *slbv1.TargetGroupStatus, err error) {
	defer func() {
		httpRTMetrics := exporter.InitHTTPRTMetrics("", "TargetGroups", "GetTargetGroupStatus")
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		logrus.Errorf("Get TargetGroup status fail, slb:%s not exist, err:%s\n", request.SlbName, err)
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	targetGroupRecord, err := s.getTargetGroupRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName,
	)
	if err != nil {
		logrus.Errorf(
			"Get TargetGroup fail, slb:%s targetGroup:%s not exist, err:%s\n",
			request.SlbName, request.TargetGroupName, err,
		)
		if strings.Contains(err.Error(), "not exist") {
			return nil, errors.NewHiggsError(ctx, codes.NotFound, errors.SlbTargetGroupNotFound, errors.Domain, nil, nil)
		}
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	targetGroupStatusResponse, err = s.generateTargetGroupStatus(slbRecord, targetGroupRecord)
	if err != nil {
		logrus.Errorf(
			"Get TargetGroup fail, slb:%s targetGroup:%s generate response fail, err:%s\n",
			request.SlbName, request.TargetGroupName, err,
		)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	return targetGroupStatusResponse, nil
}

func (s SLBsServer) generateTargetGroupCRWithTarget(
	slbRecord *db.Slbs,
	targetGroupCrOld *netControllerV1.TargetGroup,
	targetGroupNew *slbv1.TargetGroup,
	targetGroupRecord *db.SlbTargetGroups,
) (*netControllerV1.TargetGroup, error) {
	targetGroupCrNew := s.generateTargetGroupCR(targetGroupNew)
	if targetGroupNew.Properties.Type == slbv1.TargetGroupProperties_CCI_DEPLOYMENT_INSTANCE {
		if targetGroupCrOld.CCIDeploymentInstanceName == "" {
			if targetGroupNew.Properties.CciInfo.Name == "" {
				// unbind cci
				err := s.unbindCciCr(slbRecord, targetGroupRecord, targetGroupCrNew)
				if err != nil {
					return nil, err
				}
			} else {
				// bind cci
				err := s.bindCciCr(slbRecord, targetGroupRecord, targetGroupCrNew, targetGroupNew)
				if err != nil {
					return nil, err
				}
			}
		} else {
			if targetGroupNew.Properties.CciInfo.Name == "" {
				// unbind cci
				err := s.unbindCciCr(slbRecord, targetGroupRecord, targetGroupCrNew)
				if err != nil {
					return nil, err
				}
			} else {
				if targetGroupCrOld.CCIDeploymentInstanceName != targetGroupNew.Properties.CciInfo.Name {
					// old unbind cci
					err := s.unbindCciCr(slbRecord, targetGroupRecord, targetGroupCrNew)
					if err != nil {
						return nil, err
					}
					// new bind cci
					err = s.bindCciCr(slbRecord, targetGroupRecord, targetGroupCrNew, targetGroupNew)
					if err != nil {
						return nil, err
					}
				} else {
					// update
					err := s.updateCciCr(slbRecord, targetGroupRecord, targetGroupCrNew, targetGroupNew)
					if err != nil {
						return nil, err
					}
				}
			}
		}
	}

	if targetGroupNew.Properties.Type == slbv1.TargetGroupProperties_SERVER_INSTANCE {
		s.fillServerTargets(targetGroupCrNew, targetGroupCrOld)
	}

	targetGroupCrNew.Targets = sortTarget(targetGroupCrNew.Targets)
	return targetGroupCrNew, nil
}

func (s SLBsServer) updateCciCr(
	slbRecord *db.Slbs,
	targetGroupRecord *db.SlbTargetGroups,
	targetGroupCr *netControllerV1.TargetGroup,
	targetGroupNew *slbv1.TargetGroup,
) error {
	logrus.Info("update cci cr")
	return s.updateTargetGroupCrWithCci(slbRecord, targetGroupRecord, targetGroupCr, targetGroupNew)
}

func (s SLBsServer) updateTargetGroupCrWithCci(
	slbRecord *db.Slbs,
	targetGroupRecord *db.SlbTargetGroups,
	targetGroupCr *netControllerV1.TargetGroup,
	targetGroupNew *slbv1.TargetGroup,
) error {
	// 1. generate targets cr
	targetCRs, err := generateTargetsWithCciInfo(
		slbRecord,
		targetGroupRecord.ID,
		targetGroupRecord.Name,
		targetGroupRecord.CreatorID,
		targetGroupRecord.TenantID,
		targetGroupRecord.Zone,
		targetGroupNew.Properties.CciInfo,
		targetGroupRecord.TargetGroupID,
	)
	if err != nil {
		logrus.Error("update target group CR with cci fail", "error:", err)
		return err
	}

	// 2. set targets cr
	targetGroupCr.Targets = sortTarget(targetCRs)

	// 3. set targets db
	err = updateTargetsDB(slbRecord, targetGroupRecord, targetCRs)
	if err != nil {
		return err
	}

	return nil
}

func updateForEndpointsChange(
	slbRecord *db.Slbs,
	targetGroupRecord *db.SlbTargetGroups,
) error {
	// 1. generate targets cr with endpoints
	cciInfo := cciDeploymentInstanceInfo(
		targetGroupRecord.CciDeploymentInstanceName,
		targetGroupRecord.CciDeploymentInstanceDisplayName,
		"",
		"",
		targetGroupRecord.CciDeploymentInstancePort,
	)
	targets, err := generateTargetsWithCciInfo(
		slbRecord,
		targetGroupRecord.ID,
		targetGroupRecord.Name,
		targetGroupRecord.CreatorID,
		targetGroupRecord.TenantID,
		targetGroupRecord.Zone,
		cciInfo,
		targetGroupRecord.TargetGroupID,
	)
	if err != nil {
		logrus.Error("update for endpoints change", "error:", err)
		return err
	}

	// 2. set targets cr
	err = updateForEndpointsChangeSetCr(slbRecord, targetGroupRecord, targets)
	if err != nil {
		return nil
	}

	// 3. update targets db
	err = updateTargetsDB(slbRecord, targetGroupRecord, targets)
	if err != nil {
		return nil
	}

	return nil
}

func updateForEndpointsChangeSetCr(
	slbRecord *db.Slbs,
	targetGroupRecord *db.SlbTargetGroups,
	targets []*netControllerV1.Target,
) error {
	slbCrOld, tgIndex, hit, err := getTargetGroupCrInSlb(slbRecord, targetGroupRecord.TargetGroupID)
	if err != nil {
		logrus.Error("update for endpoints change set CR fail", "error", err)
		return err
	}
	if !hit {
		return nil
	}
	slbCr := slbCrOld.DeepCopy()
	targetGroupCrNew := &slbCr.Spec.TargetGroups[tgIndex]
	targetGroupCrNew.Targets = sortTarget(targets)
	slbCr.Spec.TargetGroups[tgIndex] = *targetGroupCrNew
	if reflect.DeepEqual(slbCr.Spec, slbCrOld.Spec) {
		return nil
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("UpdateSLB")
	_, err = resourceprovider.BosonProvider.BosonNetClient.SLBs().Update(context.Background(), slbCr, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Target group CR update fail. err: ", err.Error())
		return err
	}

	return nil
}

func getTargetGroupCrInSlb(
	slbRecord *db.Slbs,
	targetGroupID string,
) (*netControllerV1.SLB, int, bool, error) {
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSLB")
	slbOld, err := resourceprovider.BosonProvider.BosonNetClient.SLBs().Get(
		context.Background(), resourceprovider.SlbCRName(slbRecord.Name), metav1.GetOptions{},
	)
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("TargetGroup CR update fail.", "GetSLB err:", err.Error())
		return nil, 0, false, err
	}

	slb := slbOld.DeepCopy()
	hit := false
	index := 0
	var targetGroupTmp netControllerV1.TargetGroup
	for index, targetGroupTmp = range slb.Spec.TargetGroups {
		if targetGroupTmp.TargetGroupID == targetGroupID {
			hit = true
			break
		}
	}
	if !hit {
		logrus.Errorln("update targetGroup cr fail, can not find targetGroup ", "slb ", slbRecord.SlbID, "target group ", targetGroupID)
		return slb, 0, false, nil
	}

	return slb, index, true, nil
}

func (s SLBsServer) bindCciCr(
	slbRecord *db.Slbs,
	targetGroupRecord *db.SlbTargetGroups,
	targetGroupCr *netControllerV1.TargetGroup,
	targetGroupNew *slbv1.TargetGroup,
) error {
	logrus.Info("bind cci cr")
	return s.updateTargetGroupCrWithCci(slbRecord, targetGroupRecord, targetGroupCr, targetGroupNew)
}

func (s SLBsServer) unbindCciCr(
	slbRecord *db.Slbs,
	targetGroupRecord *db.SlbTargetGroups,
	targetGroupCr *netControllerV1.TargetGroup,
) error {
	logrus.Info("unbind cci cr")

	// 1. clear targets cr
	targetGroupCr.Targets = []*netControllerV1.Target{}
	// 2. clear targets db
	targetsDdOld, err := getTargetsDb(slbRecord, targetGroupRecord)
	if err != nil {
		return err
	}
	if len(targetsDdOld) != 0 {
		for _, t := range targetsDdOld {
			engine := resourceprovider.BosonProvider.Engine
			err = dao.SetTargetState(
				engine, targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, t.TargetID,
				targetStateName(slbv1.Target_State(slbv1.Target_REMOVING)),
			)
			if err != nil {
				logrus.Error(
					"generate target group with targets fail, del targets db error", "error", err,
					"slb", slbRecord.SlbID, "target group", targetGroupRecord.TargetGroupID, "target", t.TargetID,
				)
				return err
			}
		}
	}

	return nil
}

func updateTargetsDB(
	slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups,
	targetCRs []*netControllerV1.Target,
) error {
	// .1 old targets
	targetsDdOld, err := getTargetsDb(slbRecord, targetGroupRecord)
	if err != nil {
		return err
	}

	// .2 new targets
	targetsDdNew := []*db.SlbTargets{}
	for _, t := range targetCRs {
		logrus.Info("generate new targets from cr", t)
		targetRequest := generateTargetRequestWithTargetID(
			t.InstanceName, t.IPAddress, t.Port, t.Weight,
			targetGroupRecord.ID,
			targetGroupRecord.CreatorID,
			targetGroupRecord.TenantID,
			targetGroupRecord.Zone,
			t.TargetID,
		)
		if targetRequest == nil {
			continue
		}
		tDB := generateTargetDB(targetRequest, slbRecord, targetGroupRecord.TargetGroupID)
		logrus.Info("generate new target db", tDB)
		targetsDdNew = append(targetsDdNew, tDB)
	}

	adds, dels, updates := diffTargetDb(targetsDdOld, targetsDdNew)
	engine := resourceprovider.BosonProvider.Engine
	// a. adds
	if len(adds) != 0 {
		err := dao.InsertTargets(engine, adds)
		if err != nil {
			logrus.Error(
				"generate target group with targets fail, add targets db error", "error", err,
				"slb", slbRecord.SlbID, "target group", targetGroupRecord.TargetGroupID,
			)
			return err
		}
	}
	// b. dels
	if len(dels) != 0 {
		for _, t := range dels {
			err := dao.SetTargetState(
				engine, targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, t.TargetID,
				targetStateName(slbv1.Target_State(slbv1.Target_REMOVING)),
			)
			if err != nil {
				logrus.Error(
					"generate target group with targets fail, del targets db error", "error", err,
					"slb", slbRecord.SlbID, "target group", targetGroupRecord.TargetGroupID, "target", t.TargetID,
				)
				return err
			}
		}

	}
	// c. updates
	if len(updates) != 0 {
		for _, t := range updates {
			logrus.Info("update target db", t)
			err := dao.UpdateTarget(engine, t)
			if err != nil {
				logrus.Error(
					"generate target group with targets fail, update targets db error", "error", err,
					"slb", slbRecord.SlbID, "target group", targetGroupRecord.TargetGroupID, "target", t.TargetID,
				)
				return err
			}
		}
	}

	return nil
}

func diffTargetDb(old, new []*db.SlbTargets) ([]*db.SlbTargets, []*db.SlbTargets, []*db.SlbTargets) {
	adds := []*db.SlbTargets{}
	dels := []*db.SlbTargets{}
	updates := []*db.SlbTargets{}
	oldMap := map[string]*db.SlbTargets{}
	newMap := map[string]*db.SlbTargets{}
	for index, t := range old {
		oldMap[t.Name] = old[index]
	}
	for index, t := range new {
		newMap[t.Name] = new[index]
	}

	// adds
	for index, t := range new {
		if _, ok := oldMap[t.Name]; !ok {
			adds = append(adds, new[index])
		}
	}

	// dels
	for index, t := range old {
		if _, ok := newMap[t.Name]; !ok {
			dels = append(dels, old[index])
		}
	}

	// updates
	for index, t := range old {
		if _, ok := newMap[t.Name]; ok {
			// diff
			if !targetDdEqual(old[index], newMap[t.Name]) {
				if !targetIsValid(newMap[t.Name]) {
					dels = append(dels, old[index])
				} else {
					// 这里的new可能是生成的新的，所以需要从老的上获取下 TargetID
					newMap[t.Name].TargetID = t.TargetID
					updates = append(updates, newMap[t.Name])
				}
			}
		}
	}

	return adds, dels, updates
}

func targetIsValid(target *db.SlbTargets) bool {
	return target.IpAddress != "" && target.Port != 0
}

func targetDdEqual(old, new *db.SlbTargets) bool {
	if old.Port == new.Port &&
		old.IpAddress == new.IpAddress &&
		old.Weight == new.Weight {
		return true
	}

	return false
}

func getTargetsDb(slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups) ([]*db.SlbTargets, error) {
	engine := resourceprovider.BosonProvider.Engine
	targets, err := dao.GetSLBTargetsByTargetGroupID(engine, slbRecord.SlbID, targetGroupRecord.TargetGroupID)
	if err != nil {
		logrus.Error("get targets fail", "error", err, "slb", slbRecord.SlbID, "target group", targetGroupRecord.TargetGroupID)
		return nil, err
	}

	return targets, nil
}

func generateTargetsWithCciInfo(
	slbRecord *db.Slbs,
	tgResourceID, tgName, createID, tenantID, zone string,
	cciInfo *slbv1.CciDeploymentInstanceInfo,
	tgUid string,
) ([]*netControllerV1.Target, error) {
	targetCRs := []*netControllerV1.Target{}
	// .1 generate targets check
	// a. port
	if cciInfo.Port == 0 {
		logrus.Info("generate targets with cci skip, port is 0")
		return targetCRs, nil
	}
	// x. othres none

	// .2 list endpoints
	cciNewName := cciInfo.Name
	serviceNewName := cciBosonServiceName(slbRecord.SlbID, tgName, cciNewName)
	endpoints, err := bosonServiceEndpointsByServiceName(serviceNewName)
	if err != nil {
		return nil, nil
	}

	// .3 generate
	for _, e := range endpoints {
		tDB, err := getSLBTargetByName(tgUid, e.Name)
		if err != nil {
			logrus.Error("generate targets with cci info fail", "error:", err)
			return nil, err
		}

		var targetRequest *slbv1.Target
		logrus.Info("generate targets with cci info, get target db by name,", "tgID:", tgUid, "name:", e.Name, "db:", tDB)
		if tDB != nil {
			targetRequest = generateTargetRequestWithTargetID(
				e.Name, e.Ip, int(cciInfo.Port), 0,
				tgResourceID, createID, tenantID, zone, tDB.TargetID,
			)
		} else {
			targetRequest = generateTargetRequestWithoutTargetID(
				e.Name, e.Ip, int(cciInfo.Port), 0,
				tgResourceID, createID, tenantID, zone,
			)
		}

		if targetRequest == nil {
			continue
		}
		targetCR := generateTargetCR(targetRequest)
		targetCRs = append(targetCRs, targetCR)
	}

	return targetCRs, nil
}

func generateTargetRequestWithTargetID(
	name string, ip string, port int, weight int,
	tgID, createID, tenantID, zone string, expireTargetID string,
) *slbv1.Target {
	if ip == "" || port == 0 {
		logrus.Info("generate target request skip, ip or port is nil", "ip", ip, "port", port)
		return nil
	}
	targetRequest := targetRequestFillCommonWithTargetID(
		&slbv1.Target{Name: name}, tgID, createID, tenantID, zone, expireTargetID,
	)
	targetRequest.DisplayName = name
	targetRequest.Description = ""
	targetRequest.ResourceType = "network.slb.v1.target"
	targetRequest.Properties = &slbv1.TargetProperties{
		Ipv4Address:  ip,
		Port:         int32(port),
		Weight:       int32(weight),
		Type:         slbv1.TargetProperties_CCI_DEPLOYMENT_INSTANCE,
		InstanceName: name,
	}

	return targetRequest
}

func (s SLBsServer) checkHealthCheckConfig(slbID, uid string, properties *slbv1.TargetGroupProperties) error {
	healthCheck := properties.HealthCheck
	if healthCheck != nil && healthCheck.Enable {
		if (healthCheck.Type != slbv1.HealthCheck_TCP) && (healthCheck.Type != slbv1.HealthCheck_HTTP) {
			return fmt.Errorf("Create TargetGroup fail. slb:%s targetGroup:%s, health check type %d not support",
				slbID, uid, healthCheck.Type)
		}
		if healthCheck.Type == slbv1.HealthCheck_TCP && healthCheck.TcpHealthCheckConfig == nil {
			return fmt.Errorf("Create TargetGroup fail. slb:%s targetGroup:%s, health check tcp config must be specified",
				slbID, uid)
		}
		if healthCheck.Type == slbv1.HealthCheck_HTTP {
			if healthCheck.HttpHealthCheckConfig == nil {
				return fmt.Errorf("Create TargetGroup fail. slb:%s targetGroup:%s, health check tcp config must be specified",
					slbID, uid)
			}
			// host
			if healthCheck.HttpHealthCheckConfig.HostType == slbv1.HTTPHealthCheckConfig_NAME {
				pattern := `^([\w][\w\-]+\.)+[\w][\w\-]+$`
				regex, err := regexp.Compile(pattern)
				if err != nil {
					return fmt.Errorf("Create TargetGroup fail. slb:%s targetGroup:%s, regular express err", slbID, uid)
				}
				matched := regex.MatchString(healthCheck.HttpHealthCheckConfig.Host)
				if !matched {
					return fmt.Errorf("Create TargetGroup fail. slb:%s targetGroup:%s, domain illegal", slbID, uid)
				}
			}
			// path
			pattern := `^((\/([\w\-]+\.?)+)+)|\/$`
			regex, err := regexp.Compile(pattern)
			if err != nil {
				return fmt.Errorf("Create TargetGroup fail. slb:%s targetGroup:%s, regular express err", slbID, uid)
			}
			matched := regex.MatchString(healthCheck.HttpHealthCheckConfig.Path)
			if !matched {
				return fmt.Errorf("Create TargetGroup fail. slb:%s targetGroup:%s, path illegal", slbID, uid)
			}
			if len(healthCheck.HttpHealthCheckConfig.ReturnCode) == 0 {
				return fmt.Errorf("Create TargetGroup fail. slb:%s targetGroup:%s, at least one return code must be specified", slbID, uid)
			}
		}
	}

	return nil
}

func (s SLBsServer) createTargetGroupGetWithCheck(
	ctx context.Context, request *slbv1.CreateTargetGroupRequest,
) (*db.Slbs, error) {
	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		return nil, err
	}

	has, err := s.checkTargetGroup(ctx, request)
	if err != nil {
		return nil, err
	}
	if has {
		err := fmt.Errorf(
			"Create SLB:%s TargetGroup fail. targetGroup:%s exist\n", slbRecord.SlbID, request.TargetGroup.Uid,
		)
		logrus.Errorln(err.Error())
		return nil, err
	}

	// quota
	err = s.targetGroupQuota(slbRecord, request)
	if err != nil {
		logrus.Errorf(
			"Create SLB:%s TargetGroup:%s fail. quota check err:%s\n",
			slbRecord.SlbID, request.TargetGroup.Uid, err,
		)
		return nil, err
	}

	if request.TargetGroup == nil || request.TargetGroup.Properties == nil {
		err := fmt.Errorf(
			"Create SLB:%s TargetGroup:%s fail. create request is error, TargetGroup or TargetGroup.Properties is nil.",
			slbRecord.SlbID, request.TargetGroup.Uid,
		)
		logrus.Errorln(err.Error())
		return nil, err
	}

	// .Type
	if _, ok := slbv1.TargetGroupProperties_SLBTargetType_name[int32(request.TargetGroup.Properties.Type)]; !ok {
		err := fmt.Errorf(
			"Create SLB:%s TargetGroup fail. targetGroup:%s type invalid:%s\n",
			slbRecord.SlbID, request.TargetGroup.Uid, request.TargetGroup.Properties.Type.String(),
		)
		logrus.Errorln(err.Error())
		return nil, err
	}

	// .Scheduler
	// 仅支持 TargetGroupProperties_RR
	if request.TargetGroup.Properties.Scheduler != slbv1.TargetGroupProperties_RR {
		err := fmt.Errorf(
			"Create TargetGroup fail. slb:%s targetGroup:%s Scheduler invalid:%s\n",
			slbRecord.SlbID, request.TargetGroup.Uid, request.TargetGroup.Properties.Scheduler.String(),
		)
		logrus.Errorln(err.Error())
		return nil, err
	}

	// .Protocol
	if !s.targetProtocolValid(request.TargetGroup.Properties.Protocol) {
		err := fmt.Errorf(
			"Create TargetGroup fail. slb:%s targetGroup:%s Protocol invalid:%s\n",
			slbRecord.SlbID, request.TargetGroup.Uid, request.TargetGroup.Properties.Protocol.String(),
		)
		logrus.Errorln(err.Error())
		return nil, err
	}

	// .HealthCheck
	err = s.checkHealthCheckConfig(slbRecord.SlbID, request.TargetGroup.Uid, request.TargetGroup.Properties)
	if err != nil {
		err := fmt.Errorf(
			"Create TargetGroup fail. slb:%s targetGroup:%s health check failed:%s\n",
			slbRecord.SlbID, request.TargetGroup.Uid, err.Error(),
		)
		logrus.Errorln(err.Error())
		return nil, err
	}
	// .Weight
	// fixme not support

	// .CciDeploymentInstanceInfo
	if request.TargetGroup.Properties.Type == slbv1.TargetGroupProperties_CCI_DEPLOYMENT_INSTANCE && request.TargetGroup.Properties.CciInfo != nil {
		// TODO fixme 需要调用CCI接口，判定是否存在，否则我们后端的代码必定是会出问题的
		logrus.Info("Create TargetGroup check. cci info:", request.TargetGroup.Properties.CciInfo)
	}

	return slbRecord, nil
}

func (s SLBsServer) checkTargetGroup(ctx context.Context, request *slbv1.CreateTargetGroupRequest) (bool, error) {
	engine := resourceprovider.BosonProvider.Engine
	resourceID := dao.TargetGroupResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName)
	has, err := dao.CheckTargetGroupResourceID(engine, resourceID)
	if err != nil {
		logrus.Errorln("SLB Server check TargetGroup DB record fail.", "err", err.Error())
		return false, err
	}

	return has, nil
}

func (s SLBsServer) createTargetGroupCR(
	ctx context.Context, request *slbv1.CreateTargetGroupRequest, slbRecord *db.Slbs,
) error {
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSLB")
	slbOld, err := resourceprovider.BosonProvider.BosonNetClient.SLBs().Get(
		ctx, resourceprovider.SlbCRName(slbRecord.Name), metav1.GetOptions{},
	)
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("TargetGroup CR create fail.", "GetSLB err:", err.Error())
		return err
	}

	slb := slbOld.DeepCopy()
	targetGroup := s.generateTargetGroupCR(request.TargetGroup)
	slb.Spec.TargetGroups = append(slb.Spec.TargetGroups, *targetGroup)
	slb.Spec.TargetGroups = sortTargetGroup(slb.Spec.TargetGroups)
	if reflect.DeepEqual(slb.Spec, slbOld.Spec) {
		return nil
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateSLB")
	_, err = resourceprovider.BosonProvider.BosonNetClient.SLBs().Update(ctx, slb, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("TargetGroup CR create fail. err: ", err.Error())
		return err
	}

	return nil
}

func (s SLBsServer) generateTargetGroupCR(targetGroup *slbv1.TargetGroup) *netControllerV1.TargetGroup {
	targetGroupCR := &netControllerV1.TargetGroup{
		TargetGroupID:             targetGroup.Uid,
		TargetType:                targetGroupTypeName(targetGroup.Properties.Type),
		Scheduler:                 slbSchedulerTypeName(targetGroup.Properties.Scheduler),
		Protocol:                  targetGroupProtocolTypeName(targetGroup.Properties.Protocol),
		HealthCheck:               netControllerV1.HealthCheck{Enable: false},
		Weight:                    int(targetGroup.Properties.Weight),
		CCIDeploymentInstanceName: "", // 后文赋值
		Targets:                   []*netControllerV1.Target{},
	}

	if targetGroup.Properties.Type == slbv1.TargetGroupProperties_CCI_DEPLOYMENT_INSTANCE {
		if targetGroup.Properties.CciInfo != nil {
			targetGroupCR.CCIDeploymentInstanceName = targetGroup.Properties.CciInfo.Name
		}
	}

	healthCheck := targetGroup.Properties.HealthCheck
	if healthCheck != nil {
		targetGroupCR.HealthCheck.Enable = healthCheck.Enable
		targetGroupCR.HealthCheck.HealthCheckType = slbv1.HealthCheck_HealthCheckType_name[int32(healthCheck.Type)]
		targetGroupCR.HealthCheck.Timeout = healthCheck.Timeout
		targetGroupCR.HealthCheck.Interval = healthCheck.Interval
		targetGroupCR.HealthCheck.HealthThreshold = healthCheck.HealthThreshold
		targetGroupCR.HealthCheck.UnhealthThreshold = healthCheck.UnhealthThreshold
		if healthCheck.TcpHealthCheckConfig != nil {
			targetGroupCR.HealthCheck.TCPHealthCheckConfig.Port = healthCheck.TcpHealthCheckConfig.Port
		}
		if healthCheck.HttpHealthCheckConfig != nil {
			targetGroupCR.HealthCheck.HTTPHealthCheckConfig.Host = healthCheck.HttpHealthCheckConfig.Host
			targetGroupCR.HealthCheck.HTTPHealthCheckConfig.HostType = slbv1.HTTPHealthCheckConfig_HttpHostType_name[int32(healthCheck.HttpHealthCheckConfig.HostType)]
			targetGroupCR.HealthCheck.HTTPHealthCheckConfig.Path = healthCheck.HttpHealthCheckConfig.Path
			targetGroupCR.HealthCheck.HTTPHealthCheckConfig.Method = slbv1.HTTPHealthCheckConfig_HTTPMethod_name[int32(healthCheck.HttpHealthCheckConfig.Method)]
			targetGroupCR.HealthCheck.HTTPHealthCheckConfig.Version = slbv1.HTTPHealthCheckConfig_HTTPVersion_name[int32(healthCheck.HttpHealthCheckConfig.Version)]
			targetGroupCR.HealthCheck.HTTPHealthCheckConfig.RetCodes = make([]string, 0)
			for _, code := range healthCheck.HttpHealthCheckConfig.ReturnCode {
				targetGroupCR.HealthCheck.HTTPHealthCheckConfig.RetCodes = append(targetGroupCR.HealthCheck.HTTPHealthCheckConfig.RetCodes,
					slbv1.HTTPHealthCheckConfig_HTTPReturnCode_name[int32(code)])
			}
		}
	}

	return targetGroupCR
}

func targetGroupTypeName(targetType slbv1.TargetGroupProperties_SLBTargetType) string {
	return slbv1.TargetGroupProperties_SLBTargetType_name[int32(targetType)]
}

func targetGroupTypeValue(targetType string) slbv1.TargetGroupProperties_SLBTargetType {
	return slbv1.TargetGroupProperties_SLBTargetType(slbv1.TargetGroupProperties_SLBTargetType_value[targetType])
}

func slbSchedulerTypeName(schedulerType slbv1.TargetGroupProperties_TargetGroupSchedulerType) string {
	return slbv1.TargetGroupProperties_TargetGroupSchedulerType_name[int32(schedulerType)]
}

func slbSchedulerTypeValue(schedulerType string) slbv1.TargetGroupProperties_TargetGroupSchedulerType {
	return slbv1.TargetGroupProperties_TargetGroupSchedulerType(
		slbv1.TargetGroupProperties_TargetGroupSchedulerType_value[schedulerType],
	)
}

func targetGroupProtocolTypeName(protocolType slbv1.TargetGroupProperties_TargetProtocol) string {
	return slbv1.TargetGroupProperties_TargetProtocol_name[int32(protocolType)]
}

func targetGroupProtocolTypeValue(protocolType string) slbv1.TargetGroupProperties_TargetProtocol {
	return slbv1.TargetGroupProperties_TargetProtocol(
		slbv1.TargetGroupProperties_TargetProtocol_value[protocolType],
	)
}

func (s SLBsServer) createTargetGroupDB(
	ctx context.Context, request *slbv1.CreateTargetGroupRequest, slbRecord *db.Slbs,
) error {
	engine := resourceprovider.BosonProvider.Engine

	targetGroup := db.SlbTargetGroups{
		TargetGroupID: request.TargetGroup.Uid,
		Name:          request.TargetGroup.Name,
		Zone:          request.TargetGroup.Zone,
		State:         targetGroupStateName(slbv1.TargetGroup_CREATING),
		DisplayName:   request.TargetGroup.DisplayName,
		Description:   request.TargetGroup.Description,
		ResourceType:  request.TargetGroup.ResourceType,
		SlbID:         slbRecord.SlbID,
		Type:          targetGroupTypeName(request.TargetGroup.Properties.Type),
		Scheduler:     slbSchedulerTypeName(request.TargetGroup.Properties.Scheduler),
		Protocol:      targetGroupProtocolTypeName(request.TargetGroup.Properties.Protocol),
		Weight:        0, // 暂不支持
		ID:            request.TargetGroup.Id,
		CreatorID:     request.TargetGroup.CreatorId,
		OwnerID:       request.TargetGroup.OwnerId,
		TenantID:      request.TargetGroup.TenantId,
		CreateTime:    request.TargetGroup.CreateTime.AsTime(),
		UpdateTime:    request.TargetGroup.UpdateTime.AsTime(),
		Deleted:       false,
		Tags:          "",
	}

	if request.TargetGroup.Properties.CciInfo != nil {
		fillTargetGroupDbCciInfo(&targetGroup, request.TargetGroup.Properties.CciInfo)
	}

	err := dao.InsertTargetGroup(engine, targetGroup)
	if err != nil {
		logrus.Errorln("Create TargetGroup fail", err.Error())
		return err
	}

	return nil
}

func healthCheckTypeName(hcType slbv1.HealthCheck_HealthCheckType) string {
	return slbv1.HealthCheck_HealthCheckType_name[int32(hcType)]
}

func healthCheckTypeValue(hcType string) slbv1.HealthCheck_HealthCheckType {
	return slbv1.HealthCheck_HealthCheckType(slbv1.HealthCheck_HealthCheckType_value[hcType])
}

func (s SLBsServer) createHealthCheckDB(
	ctx context.Context, request *slbv1.CreateTargetGroupRequest,
) error {
	engine := resourceprovider.BosonProvider.Engine

	healthCheck := db.SlbHealthChecks{}

	healthCheck.TargetGroupID = request.TargetGroup.Uid
	healthCheck.Enable = false
	healthCheck.Deleted = false
	healthCheck.Type = healthCheckTypeName(slbv1.HealthCheck_INVALID)

	reqHealthCheck := request.TargetGroup.Properties.HealthCheck
	if reqHealthCheck != nil {
		healthCheck.Enable = reqHealthCheck.Enable
		healthCheck.Type = healthCheckTypeName(reqHealthCheck.Type)
		healthCheck.Interval = reqHealthCheck.Interval
		healthCheck.Timeout = reqHealthCheck.Timeout
		healthCheck.HealthThreshold = reqHealthCheck.HealthThreshold
		healthCheck.UnhealthThreshold = reqHealthCheck.UnhealthThreshold
		if reqHealthCheck.Type == slbv1.HealthCheck_TCP {
			d, _ := json.Marshal(reqHealthCheck.TcpHealthCheckConfig)
			healthCheck.HealthCheckConfig = string(d)
		}
		if reqHealthCheck.Type == slbv1.HealthCheck_HTTP {
			d, _ := json.Marshal(reqHealthCheck.HttpHealthCheckConfig)
			healthCheck.HealthCheckConfig = string(d)
		}
	}

	err := dao.InsertHealthCheck(engine, &healthCheck)
	if err != nil {
		logrus.Errorln("Create HealthCheck fail", err.Error())
		return err
	}

	return nil
}

func (s SLBsServer) updateHealthCheckDB(
	ctx context.Context, tgId string, targetGroup *slbv1.TargetGroup,
) error {
	engine := resourceprovider.BosonProvider.Engine

	healthCheck := db.SlbHealthChecks{}

	healthCheck.TargetGroupID = tgId
	healthCheck.Enable = false
	healthCheck.Deleted = false
	healthCheck.Type = healthCheckTypeName(slbv1.HealthCheck_INVALID)

	reqHealthCheck := targetGroup.Properties.HealthCheck
	if reqHealthCheck != nil {
		healthCheck.Enable = reqHealthCheck.Enable
		healthCheck.Type = healthCheckTypeName(reqHealthCheck.Type)
		healthCheck.Interval = reqHealthCheck.Interval
		healthCheck.Timeout = reqHealthCheck.Timeout
		healthCheck.HealthThreshold = reqHealthCheck.HealthThreshold
		healthCheck.UnhealthThreshold = reqHealthCheck.UnhealthThreshold
		if reqHealthCheck.Type == slbv1.HealthCheck_TCP {
			d, _ := json.Marshal(reqHealthCheck.TcpHealthCheckConfig)
			healthCheck.HealthCheckConfig = string(d)
		}
		if reqHealthCheck.Type == slbv1.HealthCheck_HTTP {
			d, _ := json.Marshal(reqHealthCheck.HttpHealthCheckConfig)
			healthCheck.HealthCheckConfig = string(d)
		}
	}

	err := dao.UpdateHealthCheck(engine, &healthCheck)
	if err != nil {
		logrus.Errorln("Update HealthCheck fail", err.Error())
		return err
	}

	return nil
}

func (s SLBsServer) generateTargetGroup(
	slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups,
) (*slbv1.TargetGroup, error) {
	targetGroup := &slbv1.TargetGroup{
		Id:           targetGroupRecord.ID,
		Uid:          targetGroupRecord.TargetGroupID,
		Name:         targetGroupRecord.Name,
		DisplayName:  targetGroupRecord.DisplayName,
		Description:  targetGroupRecord.Description,
		ResourceType: targetGroupRecord.ResourceType,
		CreatorId:    targetGroupRecord.CreatorID,
		OwnerId:      targetGroupRecord.OwnerID,
		TenantId:     targetGroupRecord.TenantID,
		Zone:         targetGroupRecord.Zone,
		State:        targetGroupStateValue(targetGroupRecord.State),
		Tags:         nil,
		Properties:   nil,
		Deleted:      targetGroupRecord.Deleted,
		CreateTime:   timestamppb.New(targetGroupRecord.CreateTime),
		UpdateTime:   timestamppb.New(targetGroupRecord.UpdateTime),
	}

	properties := s.generateTargetGroupProperties(targetGroupRecord)
	targetGroup.Properties = properties

	return targetGroup, nil
}

func (s SLBsServer) generateTargetGroupProperties(
	targetGroupRecord *db.SlbTargetGroups,
) *slbv1.TargetGroupProperties {
	properties := &slbv1.TargetGroupProperties{
		Type:        targetGroupTypeValue(targetGroupRecord.Type),
		Scheduler:   slbSchedulerTypeValue(targetGroupRecord.Scheduler),
		Protocol:    targetGroupProtocolTypeValue(targetGroupRecord.Protocol),
		HealthCheck: nil, // 后文赋值
		Weight:      0,   // 暂不支持
		CciInfo:     nil, // 后文赋值
	}

	hc := s.generateHealthCheckConfig(targetGroupRecord.TargetGroupID)
	if hc != nil {
		properties.HealthCheck = hc
	}

	properties.CciInfo = cciDeploymentInstanceInfo(
		targetGroupRecord.CciDeploymentInstanceName,
		targetGroupRecord.CciDeploymentInstanceDisplayName,
		"",
		"",
		targetGroupRecord.CciDeploymentInstancePort,
	)
	return properties
}

func targetGroupStateValue(state string) slbv1.TargetGroup_State {
	return slbv1.TargetGroup_State(slbv1.TargetGroup_State_value[state])
}

func targetGroupStateName(state slbv1.TargetGroup_State) string {
	return slbv1.TargetGroup_State_name[int32(state)]
}

func (s SLBsServer) updateTargetGroupGetWithCheck(
	ctx context.Context, request *slbv1.UpdateTargetGroupRequest,
) (*db.Slbs, *slbv1.TargetGroup, error) {
	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		return nil, nil, err
	}

	targetGroupRecord, err := s.getTargetGroupRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName,
	)
	if err != nil {
		logrus.Errorf("Update targetGroup fail. get slb:%s targetGroup:%s err:%s", slbRecord.SlbID, request.TargetGroupName, err)
		return nil, nil, err
	}

	targetGroupNew, err := s.generateTargetGroup(slbRecord, targetGroupRecord)
	if err != nil {
		logrus.Errorf("Update targetGroup fail. generate slb:%s targetGroup:%s err:%s", slbRecord.SlbID, request.TargetGroupName, err)
		return nil, nil, err
	}

	maskMap := generateFieldMaskMap(request.UpdateMask)

	// .DisplayName
	if targetGroupDisplayNameIsSet(maskMap) {
		targetGroupNew.DisplayName = request.TargetGroup.GetDisplayName()
	}

	// .Description
	if targetGroupDescriptionIsSet(maskMap) {
		targetGroupNew.Description = request.TargetGroup.Description
	}

	if request.TargetGroup.Properties == nil {
		return slbRecord, targetGroupNew, nil
	}

	// .Scheduler
	// 暂不支持修改

	// .Protocol
	if targetGroupPropertiesProtocolIsSet(maskMap) {
		if !s.targetProtocolCheck(request.TargetGroup.Properties.Protocol) {
			err := fmt.Errorf(
				"update target group:%s check fail. protocol:%+v(%s) is invalid",
				request.TargetGroupName,
				request.TargetGroup.Properties.Protocol,
				targetGroupProtocolTypeName(request.TargetGroup.Properties.Protocol),
			)
			logrus.Errorf("update target group check protocol err:%s", err)
			return nil, nil, err
		}
		targetGroupNew.Properties.Protocol = request.TargetGroup.Properties.Protocol
	}

	// .HealthCheck
	err = s.checkHealthCheckConfig(slbRecord.SlbID, targetGroupRecord.TargetGroupID, request.TargetGroup.Properties)
	if err != nil {
		err := fmt.Errorf(
			"Update TargetGroup fail. slb:%s targetGroup:%s health check failed:%s\n",
			slbRecord.SlbID, targetGroupRecord.TargetGroupID, err.Error(),
		)
		logrus.Errorln(err.Error())
		return nil, nil, err
	}
	reqHc := request.TargetGroup.Properties.HealthCheck
	if reqHc != nil {
		targetGroupNew.Properties.HealthCheck = reqHc
	}
	// .Weight
	// 暂不支持修改

	// .CciInfo
	if request.TargetGroup.Properties.CciInfo != nil {
		if targetGroupPropertiesCciInfoNameIsSet(maskMap) {
			targetGroupNew.Properties.CciInfo.Name = request.TargetGroup.Properties.CciInfo.Name
		}
		if targetGroupPropertiesCciInfoDisplayNameIsSet(maskMap) {
			targetGroupNew.Properties.CciInfo.DisplayName = request.TargetGroup.Properties.CciInfo.DisplayName
		}
		if targetGroupPropertiesCciInfoPortIsSet(maskMap) {
			targetGroupNew.Properties.CciInfo.Port = request.TargetGroup.Properties.CciInfo.Port
		}
	} else {
		// request.TargetGroup.Properties.CciInfo == nil
		if targetGroupPropertiesCciInfoIsSet(maskMap) {
			targetGroupNew.Properties.CciInfo = cciDeploymentInstanceInfoDefault()
		}
	}

	return slbRecord, targetGroupNew, nil
}

func (s SLBsServer) targetProtocolValid(protocol slbv1.TargetGroupProperties_TargetProtocol) bool {
	switch protocol {
	case slbv1.TargetGroupProperties_DEFAULT:
		return true
	case slbv1.TargetGroupProperties_TCP, slbv1.TargetGroupProperties_UDP:
		return true
	}
	return false
}

func (s SLBsServer) targetProtocolCheck(protocol slbv1.TargetGroupProperties_TargetProtocol) bool {
	if !s.targetProtocolValid(protocol) {
		return false
	}

	return protocolCheck(protocol)
}

func protocolCheck(protocol slbv1.TargetGroupProperties_TargetProtocol) bool {
	// 检查相关的 listener 是否能够支持转发，例如
	// https->http ok
	// https->udp fail
	// 同等，在 listener 那边修改监听协议的时候，也要做检查
	// todo
	return true
}

func (s SLBsServer) updateTargetGroupCR(
	ctx context.Context, request *slbv1.UpdateTargetGroupRequest, slbRecord *db.Slbs,
	targetGroupNew *slbv1.TargetGroup,
) error {
	slbOld, index, hit, err := getTargetGroupCrInSlb(slbRecord, targetGroupNew.Uid)
	if err != nil {
		logrus.Error("update target group CR fail", "error", err)
		return err
	}
	if !hit {
		return nil
	}
	slb := slbOld.DeepCopy()
	targetGroupRecord, err := s.getTargetGroupRecordByResourceId(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName,
	)
	if err != nil {
		logrus.Errorf(
			"Update targetGroup fail, generate target CR error. get slb:%s targetGroup:%s err:%s",
			slbRecord.SlbID, request.TargetGroupName, err,
		)
		return err
	}

	// targetGroupNew 需要是全量数据
	targetGroupCR, err := s.generateTargetGroupCRWithTarget(slbRecord, &slb.Spec.TargetGroups[index], targetGroupNew, targetGroupRecord)
	if err != nil {
		return err
	}
	slb.Spec.TargetGroups[index] = *targetGroupCR
	if reflect.DeepEqual(slb.Spec, slbOld.Spec) {
		return nil
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("UpdateSLB")
	_, err = resourceprovider.BosonProvider.BosonNetClient.SLBs().Update(ctx, slb, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Listener CR update fail. err: ", err.Error())
		return err
	}

	return nil
}

func (s SLBsServer) updateTargetGroupDB(
	ctx context.Context, request *slbv1.UpdateTargetGroupRequest, slbRecord *db.Slbs, targetGroupNew *slbv1.TargetGroup,
) error {
	engine := resourceprovider.BosonProvider.Engine

	targetGroupRecordNewFn := func(slbID string, targetGroupName string) (*db.SlbTargetGroups, error) {
		targetGroupRecord, err := s.getTargetGroupRecordByResourceId(
			request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName,
		)
		if err != nil {
			logrus.Error(
				"Update targetGroup fail. get targetGroup db err",
				"slb", slbRecord.Name, "targetGroup", targetGroupNew.Name, "error", err,
			)
			return nil, err
		}
		return targetGroupRecord, nil
	}

	targetGroupRecordNew, err := targetGroupRecordNewFn(slbRecord.SlbID, targetGroupNew.Name)
	if err != nil {
		return err
	}

	// .DisplayName
	targetGroupRecordNew.DisplayName = targetGroupNew.DisplayName

	// .Description
	targetGroupRecordNew.Description = targetGroupNew.Description

	// .Scheduler
	// 暂不支持修改

	// .Protocol
	targetGroupRecordNew.Protocol = targetGroupProtocolTypeName(targetGroupNew.Properties.Protocol)

	// .Weight
	// 暂不支持修改

	// .CciInfo
	err = s.updateTargetGroupCciInfo(request, slbRecord, targetGroupRecordNew, targetGroupNew)
	if err != nil {
		logrus.Error(
			"Update targetGroup fail. update targetGroup cci info err",
			"slb", slbRecord.Name, "targetGroup", targetGroupNew.Name, "error", err,
		)
		return err
	}

	err = dao.UpdateSlbTargetGroup(engine, *targetGroupRecordNew)
	if err != nil {
		logrus.Error(
			"Update targetGroup fail. update targetGroup db err",
			"slb", slbRecord.Name, "targetGroup", targetGroupNew.Name, "error", err,
		)
		return err
	}

	return nil
}

func (s SLBsServer) deleteTargetGroupCheck(
	ctx context.Context, slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups,
) error {
	engine := resourceprovider.BosonProvider.Engine

	// .1 是否存在 监听器、七层规则 引用
	assos, err := dao.ListSlbAssociationsByTargetGroupID(engine, []string{targetGroupRecord.TargetGroupID})
	if err != nil {
		errNew := fmt.Errorf(
			"Delete targetGroup check fail. list slb:%s targetGroup:%s association err:%s",
			slbRecord.SlbID, targetGroupRecord.TargetGroupID, err.Error(),
		)
		logrus.Errorln(errNew.Error())
		return errNew
	}
	if len(assos) != 0 {
		errNew := fmt.Errorf(
			"Delete targetGroup check fail. list slb:%s targetGroup:%s associations:%v not nil, clean first",
			slbRecord.SlbID, targetGroupRecord.TargetGroupID, assos,
		)
		logrus.Errorln(errNew.Error())
		return errNew
	}

	return nil
}

func (s SLBsServer) deleteTargetGroupCR(ctx context.Context,
	request *slbv1.DeleteTargetGroupRequest, slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups,
) error {
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSLB")
	slbOld, err := resourceprovider.BosonProvider.BosonNetClient.SLBs().Get(
		ctx, resourceprovider.SlbCRName(slbRecord.Name), metav1.GetOptions{},
	)
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("TargetGroup CR delete fail.", "GetSLB err:", err.Error())
		return err
	}

	slb := slbOld.DeepCopy()
	slb.Spec.TargetGroups = []netControllerV1.TargetGroup{}
	for _, targetGroupTmp := range slbOld.Spec.TargetGroups {
		if targetGroupTmp.TargetGroupID == targetGroupRecord.TargetGroupID {
			continue
		}
		slb.Spec.TargetGroups = append(slb.Spec.TargetGroups, targetGroupTmp)
	}

	slb.Spec.TargetGroups = sortTargetGroup(slb.Spec.TargetGroups)

	if reflect.DeepEqual(slb.Spec, slbOld.Spec) {
		return nil
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateSLB")
	_, err = resourceprovider.BosonProvider.BosonNetClient.SLBs().Update(ctx, slb, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("TargetGroup CR delete fail. err: ", err.Error())
		return err
	}

	return nil
}

func ReleaseTargetGroup(slbRecord *db.Slbs, targetGroupID string) error {
	engine := resourceprovider.BosonProvider.Engine
	targetGroupRecord, err := dao.GetSLBTargetGroupByID(engine, slbRecord.SlbID, targetGroupID)
	if err != nil {
		logrus.Error(
			"Delete TargetGroup fail. get targetGroup db error",
			err, "slb", slbRecord.SlbID, "TargetGroup", targetGroupID,
		)
		return err
	}

	logrus.Infof("Delete slb:%s target group:%s", slbRecord.SlbID, targetGroupID)

	// .1 Associations
	err = dao.ClearSlbAssociationsByTargetGroupIDs(engine, []string{targetGroupID})
	if err != nil {
		logrus.Error(
			"Delete TargetGroup fail. clear slb associations error",
			err, "slb", slbRecord.SlbID, "TargetGroup", targetGroupID,
		)
	}

	// .2 Targets
	targetRecords, err := dao.GetSLBTargetsByTargetGroupID(engine, slbRecord.SlbID, targetGroupID)
	if err != nil {
		logrus.Error(
			"Delete TargetGroup fail. get target db list error",
			err, "slb", slbRecord.SlbID, "TargetGroup", targetGroupID,
		)
	} else {
		for _, record := range targetRecords {
			err = ReleaseTarget(slbRecord, targetGroupRecord, record.TargetID)
			if err != nil {
				logrus.Error(
					"Delete TargetGroup error. release target list error",
					err, "slb", slbRecord.SlbID, "TargetGroup", targetGroupID, "Target", record.TargetID,
				)
				continue
			}
		}
	}

	// .x self
	err = dao.DeleteTargetGroup(engine,
		slbRecord.SlbID, targetGroupID, targetGroupStateName(slbv1.TargetGroup_DELETED),
	)
	if err != nil {
		logrus.Error(
			"Delete TargetGroup fail. delete TargetGroup db error",
			"slb", slbRecord.SlbID, "TargetGroup", targetGroupID,
		)
	}

	return nil
}

func (s SLBsServer) listTargetGroupsCheckAndGet(
	ctx context.Context, request *slbv1.ListTargetGroupsRequest,
) (*db.Slbs, error) {
	if request.SlbName == "" {
		err := fmt.Errorf("listTargetGroups fail. slbName is nil")
		logrus.Error(err)
		return nil, err
	}

	slbRecord, err := s.getSlbDbByResourceId(ctx, request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName)
	if err != nil {
		logrus.Errorln("Get SLB status one fail. get SLB record err.", err.Error())
		return nil, err
	}

	return slbRecord, nil
}

func (s SLBsServer) generateHealthCheckConfig(tgId string) *slbv1.HealthCheck {
	engine := resourceprovider.BosonProvider.Engine
	healthCheck, err := dao.GetHealthCheckByTargetGroupId(engine, tgId)
	if err != nil {
		logrus.Errorf("Generate TargetGroupStatus fail, get health check err:%s\n", err)
		return nil
	}
	hc := &slbv1.HealthCheck{}
	if healthCheck != nil {
		hc.Enable = healthCheck.Enable
		hc.Type = healthCheckTypeValue(healthCheck.Type)
		hc.Timeout = healthCheck.Timeout
		hc.Interval = healthCheck.Interval
		hc.HealthThreshold = healthCheck.HealthThreshold
		hc.UnhealthThreshold = healthCheck.UnhealthThreshold
		hc.Timeout = healthCheck.Timeout
		hc.TcpHealthCheckConfig = nil
		hc.HttpHealthCheckConfig = nil
		if healthCheckTypeValue(healthCheck.Type) == slbv1.HealthCheck_TCP {
			tcpHc := &slbv1.TCPHealthCheckConfig{}
			_ = json.Unmarshal([]byte(healthCheck.HealthCheckConfig), tcpHc)
			hc.TcpHealthCheckConfig = tcpHc
		}
		if healthCheckTypeValue(healthCheck.Type) == slbv1.HealthCheck_HTTP {
			httpHc := &slbv1.HTTPHealthCheckConfig{}
			_ = json.Unmarshal([]byte(healthCheck.HealthCheckConfig), httpHc)
			hc.HttpHealthCheckConfig = httpHc
		}
	}

	return hc
}

func (s SLBsServer) generateTargetGroupStatus(
	slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups,
) (*slbv1.TargetGroupStatus, error) {
	targetGroup, err := s.generateTargetGroup(slbRecord, targetGroupRecord)
	if err != nil {
		logrus.Errorf("Generate TargetGroupStatus fail, generate TargetGroup err:%s\n", err)
		return nil, err
	}

	targetGroupStatus := &slbv1.TargetGroupStatus{
		TargetGroup: targetGroup,
		Listeners:   nil, // 后文赋值
		Vpc:         nil, // 后文赋值
	}

	// .Listeners
	listenerInfos, err := s.generateTargetGroupStatusListenerInfo(slbRecord, targetGroupRecord)
	if err != nil {
		return nil, err
	}
	targetGroupStatus.Listeners = listenerInfos

	// .Vpc
	vpcInfo, err := s.generateVpcInfo(slbRecord)
	if err != nil {
		return nil, err
	}
	targetGroupStatus.Vpc = vpcInfo

	return targetGroupStatus, nil
}

func (s SLBsServer) generateTargetGroupStatusListenerInfo(
	slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups,
) ([]*slbv1.ListenerInfo, error) {
	engine := resourceprovider.BosonProvider.Engine
	assos, err := dao.ListSlbAssociationsByTargetGroupIDWithType(
		engine, []string{targetGroupRecord.TargetGroupID}, associationsType_listener,
	)
	if err != nil {
		return nil, err
	}
	if len(assos) == 0 {
		return []*slbv1.ListenerInfo{}, nil
	}
	listenerIDs := []string{}
	for _, asso := range assos {
		listenerIDs = append(listenerIDs, asso.ForwardRuleID)
	}

	listenerRecords, err := dao.ListSLBListeners(engine, slbRecord.SlbID, listenerIDs)
	if err != nil {
		logrus.Errorf(
			"generate TargetGroup status listenerInfo fail. get slb:%s listener:%s db err:%s",
			slbRecord.SlbID, listenerIDs, err,
		)
		return nil, err
	}
	listeners, err := s.generateListenerInfos(slbRecord, listenerRecords)
	if err != nil {
		return nil, err
	}
	return listeners, nil
}

// TODO 做成枚举
const (
	associationsType_listener = "listener"
	// associationsType_rule     = "rule"
)

func (s SLBsServer) targetGroupQuota(
	slbRecord *db.Slbs, request *slbv1.CreateTargetGroupRequest,
) error {
	engine := resourceprovider.BosonProvider.Engine
	vpcQuota, err := dao.GetVpcQuotaByVpcID(engine, slbRecord.VpcID)
	if err != nil {
		logrus.Errorf(
			"vpc:%s slb:%s targetGroup:%s create fail, get quota err:%s",
			slbRecord.VpcID, slbRecord.SlbID, request.TargetGroupName, err,
		)
		return err
	}

	records, err := dao.GetSLBTargetGroupsBySlbID(engine, slbRecord.SlbID, false)
	if err != nil {
		logrus.Errorf(
			"vpc:%s slb:%s targetGroup:%s create fail, list targetGroup db err:%s",
			slbRecord.VpcID, slbRecord.SlbID, request.TargetGroupName, err,
		)
		return err
	}
	count := len(records)
	if count >= int(vpcQuota.TargetGroupPerSlb) {
		err := fmt.Errorf(
			"vpc:%s slb:%s targetGroup:%s create fail, targetGroup limit exceeded, quota:%d current count:%d",
			slbRecord.VpcID, slbRecord.SlbID, request.TargetGroupName, vpcQuota.TargetGroupPerSlb, count,
		)
		logrus.Errorln(err)
		return err
	}

	return nil
}

func (s SLBsServer) createTargetGroupRequestFill(ctx context.Context, request *slbv1.CreateTargetGroupRequest) {
	// 数据面资源，需要自己管理 ID、uid 等信息
	id := dao.TargetGroupResourceID(
		request.SubscriptionName, request.ResourceGroupName, request.Zone, request.SlbName, request.TargetGroupName,
	)
	uid := slbDataResourceUid()
	creatorId := GetUserIDFromHeader(ctx)
	ownerID := creatorId
	tenantID := GetTenantIDFromHeader(ctx)
	zone := request.Zone
	createTime := timestamppb.Now()
	updateTime := createTime

	request.TargetGroup.Id = id
	request.TargetGroup.Uid = uid
	request.TargetGroup.CreatorId = creatorId
	request.TargetGroup.OwnerId = ownerID
	request.TargetGroup.TenantId = tenantID
	request.TargetGroup.Zone = zone
	request.TargetGroup.CreateTime = createTime
	request.TargetGroup.UpdateTime = updateTime
}

func sortTargetGroupIDs(ids []string) []string {
	sortedSlice := sort.StringSlice(ids)
	sortedSlice.Sort()
	return sortedSlice
}

func sortTargetGroup(targetGroups []netControllerV1.TargetGroup) []netControllerV1.TargetGroup {
	s := targetGroupSlice(targetGroups)
	sort.Sort(s)
	return s
}

type targetGroupSlice []netControllerV1.TargetGroup

func (s targetGroupSlice) Len() int {
	return len(s)
}

func (s targetGroupSlice) Less(i, j int) bool {
	return s[i].TargetGroupID < s[j].TargetGroupID
}

func (s targetGroupSlice) Swap(i, j int) {
	s[i], s[j] = *s[j].DeepCopy(), *s[i].DeepCopy()
}

func (s SLBsServer) updateTargetGroupCciInfo(
	request *slbv1.UpdateTargetGroupRequest, slbRecord *db.Slbs,
	targetGroupRecord *db.SlbTargetGroups, targetGroupNew *slbv1.TargetGroup,
) error {
	// .1 cci change, unbind cci db
	if targetGroupRecord.CciDeploymentInstanceName != targetGroupNew.Properties.CciInfo.Name {
		if targetGroupRecord.CciDeploymentInstanceName != "" {
			err := unbindCciDb(targetGroupRecord, targetGroupRecord.CciDeploymentInstanceName)
			if err != nil {
				logrus.Errorf("format string")
				return err
			}
		}
	}

	// .2 update db col
	fillTargetGroupDbCciInfo(targetGroupRecord, targetGroupNew.Properties.CciInfo)

	return nil
}

func unbindCciDb(targetGroupRecord *db.SlbTargetGroups, cciInstanceName string) error {
	// 移除所有的 targets
	engine := resourceprovider.BosonProvider.Engine
	targetsRecord, err := dao.GetSLBTargetsByTargetGroupID(
		engine, targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID,
	)
	if err != nil {
		logrus.Errorf(
			"slb:%s targetGroup:%s unbind cci:%s fail, get targets err:%s",
			targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, cciInstanceName, err,
		)
		return err
	}

	if len(targetsRecord) == 0 {
		return nil
	}

	for _, targetRecord := range targetsRecord {
		err = dao.SetTargetState(
			engine, targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, targetRecord.TargetID,
			targetStateName(slbv1.Target_State(slbv1.Target_REMOVING)),
		)
		if err != nil {
			logrus.Errorf(
				"slb:%s targetGroup:%s unbind cci:%s fail, set target removing err:%s",
				targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, cciInstanceName, err,
			)
			continue
		}
	}

	return nil
}

func targetGroupDisplayNameIsSet(fieldMaskMap fieldMaskMapType) bool {
	const mask = "display_name"
	if fieldMaskMap == nil {
		return false
	}

	_, ok := fieldMaskMap[mask]
	return ok
}

func targetGroupDescriptionIsSet(fieldMaskMap fieldMaskMapType) bool {
	const mask = "description"
	if fieldMaskMap == nil {
		return false
	}

	_, ok := fieldMaskMap[mask]
	return ok
}

func targetGroupPropertiesProtocolIsSet(fieldMaskMap fieldMaskMapType) bool {
	const mask = "properties.protocol"
	if fieldMaskMap == nil {
		return false
	}

	_, ok := fieldMaskMap[mask]
	return ok
}

func targetGroupPropertiesCciInfoNameIsSet(fieldMaskMap fieldMaskMapType) bool {
	const mask = "properties.cci_info.name"
	if fieldMaskMap == nil {
		return false
	}

	_, ok := fieldMaskMap[mask]
	return ok
}

func targetGroupPropertiesCciInfoDisplayNameIsSet(fieldMaskMap fieldMaskMapType) bool {
	const mask = "properties.cci_info.display_name"
	if fieldMaskMap == nil {
		return false
	}

	_, ok := fieldMaskMap[mask]
	return ok
}

func targetGroupPropertiesCciInfoPortIsSet(fieldMaskMap fieldMaskMapType) bool {
	const mask = "properties.cci_info.port"
	if fieldMaskMap == nil {
		return false
	}

	_, ok := fieldMaskMap[mask]
	return ok
}

func targetGroupPropertiesCciInfoIsSet(fieldMaskMap fieldMaskMapType) bool {
	const mask = "properties.cci_info"
	if fieldMaskMap == nil {
		return false
	}

	_, ok := fieldMaskMap[mask]
	return ok
}

func (s SLBsServer) generateTargetGroupInfos(slbId string, listenerId string) ([]*slbv1.TargetGroupInfo, error) {
	engine := resourceprovider.BosonProvider.Engine
	assos, err := dao.ListSlbAssociationsByForwardRule(engine, []string{listenerId}, associationsType_listener)
	if err != nil {
		logrus.Error(
			"Generate targetGroupInfo fail. list listener associations error",
			"slb", slbId, "listener", listenerId,
		)
		return nil, err
	}

	targetGroupIDs := []string{}
	for _, asso := range assos {
		targetGroupIDs = append(targetGroupIDs, asso.TargetGroupID)
	}
	targetGroups, err := dao.ListTargetGroupsByID(engine, targetGroupIDs)
	if err != nil {
		logrus.Error(
			"Generate targetGroupInfo fail. list TargetGroups error",
			"slb", slbId, "listener", listenerId, "targetGroupIDs", targetGroupIDs,
		)
		return nil, err
	}

	infos := []*slbv1.TargetGroupInfo{}
	for _, targetGroup := range targetGroups {
		infos = append(infos, &slbv1.TargetGroupInfo{
			Id:          targetGroup.ID,
			Uid:         targetGroup.TargetGroupID,
			Name:        targetGroup.Name,
			DisplayName: targetGroup.DisplayName,
		})
	}

	return infos, nil
}

func (s SLBsServer) getTargetGroupIDs(targetGroups []*slbv1.TargetGroupInfo) []string {
	ids := []string{}
	if targetGroups == nil {
		return ids
	}

	for _, targetGroup := range targetGroups {
		ids = append(ids, targetGroup.Uid)
	}

	return ids
}

func fillTargetGroupDbCciInfo(targetGroup *db.SlbTargetGroups, cciInfo *slbv1.CciDeploymentInstanceInfo) {
	targetGroup.CciDeploymentInstanceName = cciInfo.Name
	targetGroup.CciDeploymentInstanceDisplayName = cciInfo.DisplayName
	targetGroup.CciDeploymentInstanceId = cciInfo.Id
	targetGroup.CciDeploymentInstanceUid = cciInfo.Uid
	targetGroup.CciDeploymentInstancePort = cciInfo.Port
}

func cciDeploymentInstanceInfoDefault() *slbv1.CciDeploymentInstanceInfo {
	return &slbv1.CciDeploymentInstanceInfo{
		Name:        "",
		DisplayName: "",
		Id:          "", // 暂未使用
		Uid:         "", // 暂未使用
		Port:        0,
	}
}

func cciDeploymentInstanceInfo(name, displayName, id, uid string, port int32) *slbv1.CciDeploymentInstanceInfo {
	return &slbv1.CciDeploymentInstanceInfo{
		Name:        name,
		DisplayName: displayName,
		Id:          "", // 暂未使用
		Uid:         "", // 暂未使用
		Port:        port,
	}
}

func (s SLBsServer) getTargetGroupRecordByResourceId(
	subscription, resourceGroup, zone, slbName, targetGroupName string,
) (*db.SlbTargetGroups, error) {
	engine := resourceprovider.BosonProvider.Engine
	targetGroupResourceId := dao.TargetGroupResourceID(subscription, resourceGroup, zone, slbName, targetGroupName)
	targetGroupRecord, err := dao.GetSLBTargetGroupByResourceId(engine, targetGroupResourceId)
	if err != nil {
		logrus.Error(
			"get targetGroup db err", "resourceID", targetGroupResourceId,
		)
		return nil, err
	}
	return targetGroupRecord, nil
}

func (s SLBsServer) fillServerTargets(targetGroupCrNew *netControllerV1.TargetGroup, targetGroupCrOld *netControllerV1.TargetGroup) {
	targetGroupCrNew.Targets = []*netControllerV1.Target{}
	for index := range targetGroupCrOld.Targets {
		targetGroupCrNew.Targets = append(targetGroupCrNew.Targets, targetGroupCrOld.Targets[index].DeepCopy())
	}
}

func getSLBTargetByName(tgUid string, targetName string) (*db.SlbTargets, error) {
	engine := resourceprovider.BosonProvider.Engine
	targetDB, err := dao.GetSLBTargetByNameHas(engine, tgUid, targetName)
	if err != nil {
		logrus.Error("get slb target by name error", "error:", err)
		return nil, err
	}

	return targetDB, nil
}

func generateTargetRequestWithoutTargetID(
	name string, ip string, port int, weight int,
	tgResourceID, createID, tenantID, zone string,
) *slbv1.Target {
	if ip == "" || port == 0 {
		logrus.Info("generate target request skip, ip or port is nil", "ip", ip, "port", port)
		return nil
	}
	targetRequest := targetRequestFillCommonWithoutTargetID(
		&slbv1.Target{Name: name}, tgResourceID, createID, tenantID, zone,
	)
	targetRequest.DisplayName = name
	targetRequest.Description = ""
	targetRequest.ResourceType = "network.slb.v1.target"
	targetRequest.Properties = &slbv1.TargetProperties{
		Ipv4Address:  ip,
		Port:         int32(port),
		Weight:       int32(weight),
		Type:         slbv1.TargetProperties_CCI_DEPLOYMENT_INSTANCE,
		InstanceName: name,
	}

	return targetRequest
}

func targetRequestFillCommonWithoutTargetID(
	target *slbv1.Target,
	tgResourceID, createID, tenantID, zone string,
) *slbv1.Target {
	id := dao.TargetResourceIdByTargetGroupResourceID(tgResourceID, target.Name)
	uid := slbDataResourceUid()
	createTime := timestamppb.Now()
	updateTime := createTime

	logrus.Info("target request fill without tID,", "target new uid:", uid)

	target.Id = id
	target.Uid = uid
	target.CreatorId = createID
	target.OwnerId = createID
	target.TenantId = tenantID
	target.Zone = zone
	target.CreateTime = createTime
	target.UpdateTime = updateTime

	return target
}

func targetRequestFillCommonWithTargetID(
	target *slbv1.Target,
	tgResourceID, createID, tenantID, zone string,
	expireTargetID string,
) *slbv1.Target {
	id := dao.TargetResourceIdByTargetGroupResourceID(tgResourceID, target.Name)
	uid := expireTargetID
	createTime := timestamppb.Now()
	updateTime := createTime

	logrus.Info("target request fill with tID,", "target uid:", expireTargetID)

	target.Id = id
	target.Uid = uid
	target.CreatorId = createID
	target.OwnerId = createID
	target.TenantId = tenantID
	target.Zone = zone
	target.CreateTime = createTime
	target.UpdateTime = updateTime

	return target
}
