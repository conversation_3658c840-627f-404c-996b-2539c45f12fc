package server

import (
	"context"
	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/quota/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"google.golang.org/grpc/codes"
	"reflect"
)

type QuotasServer struct {
	*quota.UnimplementedQuotaServiceServer
}

func (QuotasServer) ListQuotas(ctx context.Context, request *quota.ListQuotasRequest) (res *quota.ListQuotasResponse, err error) {

	httpRTMetrics := exporter.InitHTTPRTMetrics("", "Quota", "ListQuotas")
	defer func() {
		if err == nil {
			httpRTMetrics.Code = "200"
		}
		httpRTMetrics.ObserveDurationAndLog()
	}()

	tenantID := GetTenantIDFromHeader(ctx)
	logrus.Infof("in service, list quotas tenant id: %s", tenantID)
	if tenantID == "" {
		logrus.Errorln("tenantID is required")
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.InvalidArgument, errors.Domain, nil, nil)
	}

	vpcDatas, err := dao.ListVpcsByTenantWithAllZone(resourceprovider.BosonProvider.Engine, tenantID)
	if err != nil {
		logrus.Errorln(err)
		return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
	}

	quotas := make(map[string]*quota.ListQuotasResponse_QuotaValue, 50)
	for _, v := range vpcDatas {
		quotaValue := make(map[string]int64, 30)
		vpcQuota, err := dao.GetVpcQuotaByVpcID(resourceprovider.BosonProvider.Engine, v.VPCID)
		if err != nil {
			logrus.Errorln(err)
			return nil, errors.NewHiggsError(ctx, codes.Internal, errors.DbErr, errors.Domain, nil, nil)
		}
		logrus.Infof("tenant %s vpcQuota: %v", tenantID, vpcQuota)
		MergeVpcQuotaToMap(quotaValue, *vpcQuota)
		quotas[v.Name] = &quota.ListQuotasResponse_QuotaValue{Values: quotaValue}
	}
	logrus.Infof("tenant %s quotas: %v", tenantID, quotas)

	res = &quota.ListQuotasResponse{
		Quotas:    quotas,
		TotalSize: int32(len(quotas)),
	}

	return res, nil
}

func MergeVpcQuotaToMap(quotas map[string]int64, vpcQuota db.VpcQuota) {
	val := reflect.ValueOf(vpcQuota)
	typ := reflect.TypeOf(vpcQuota)

	for i := 0; i < val.NumField(); i++ {
		field := typ.Field(i)
		value := val.Field(i)

		if value.Kind() == reflect.Int64 {
			key := field.Tag.Get("json")
			if key == "" {
				key = field.Name
			}
			quotas[key] += value.Int()
		}
	}
}
