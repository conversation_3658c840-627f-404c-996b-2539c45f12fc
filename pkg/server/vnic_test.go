package server

import (
	"context"
	"fmt"
	"reflect"
	"testing"

	"github.com/agiledragon/gomonkey"
	v1 "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"github.com/stretchr/testify/assert"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vnic/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/errors"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"google.golang.org/grpc/codes"
	kerrors "k8s.io/apimachinery/pkg/api/errors"
	"k8s.io/apimachinery/pkg/labels"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

func TestListVNicsAzErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vnic.ListVNicRequest{
		Zone: "test11",
	}

	vnicsServer := VNICsServer{}
	ctx := context.Background()

	_, err := vnicsServer.ListVNics(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.InvalidArgument, errors.InvalidArgument, errors.Domain, nil, map[string]interface{}{
		"detail": "request zone test11 not match provider zone test",
	})
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

type MockVipLister struct{}

func (MockVipLister) List(selector labels.Selector) (ret []*v1.Vip, err error) {
	return nil, nil
}
func (MockVipLister) Get(name string) (*v1.Vip, error) {
	return nil, fmt.Errorf("err")
}

func TestGetVNicErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	vnicsServer := VNICsServer{
		ipLister: &MockVipLister{},
	}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vnic.GetVNicRequest{
		Zone: "test11",
	}

	ctx := context.Background()

	_, err := vnicsServer.GetVNic(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.Internal, errors.ServiceError, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

type MockSubNetLister struct{}

func (MockSubNetLister) List(selector labels.Selector) (ret []*v1.Subnet, err error) {
	return nil, nil
}
func (MockSubNetLister) Get(name string) (*v1.Subnet, error) {
	qualifiedResource := schema.GroupResource{Group: "v1", Resource: "subnet"}
	return nil, kerrors.NewNotFound(qualifiedResource, "subnet")
}

func TestCreateVNicErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	vnicsServer := VNICsServer{
		subnetLister: &MockSubNetLister{},
	}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vnic.CreateVNicRequest{
		Zone: "test11",
		Vnic: &vnic.VNicCreate{
			Subnet: "1.1.1.1/16",
		},
	}

	ctx := context.Background()

	_, err := vnicsServer.CreateVNic(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.SubnetNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}

func TestDeleteVNicErr(t *testing.T) {
	p := gomonkey.NewPatches()
	defer p.Reset()

	var h *exporter.HTTPRTMetrics
	p.ApplyMethod(reflect.TypeOf(h), "ObserveDurationAndLog", func(_ *exporter.HTTPRTMetrics) {})

	vnicsServer := VNICsServer{
		subnetLister: &MockSubNetLister{},
	}

	p.ApplyGlobalVar(&db.AZ, "test")

	request := &vnic.DeleteVNicRequest{
		Zone:   "test11",
		Subnet: "1.1.1.1/16",
	}

	ctx := context.Background()

	_, err := vnicsServer.DeleteVNic(ctx, request)
	assert.NotNil(t, err)

	errHiggs := errors.NewHiggsError(ctx, codes.NotFound, errors.SubnetNotFound, errors.Domain, nil, nil)
	assert.Equal(t, true, CompareHiggsError(err, errHiggs))
}
