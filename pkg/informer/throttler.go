package informer

import (
	"sync"
	"time"

	"github.com/sirupsen/logrus"
)

// UpdateThrottler 更新限流器，用于减少频繁的缓存更新操作
// 工作原理：
// 1. 第一次触发时立即执行更新，并开始30秒限流期
// 2. 限流期内的触发被标记为待处理，但不执行
// 3. 限流期结束后，如果有待处理的触发，执行一次更新并重新开始限流期
type UpdateThrottler struct {
	interval       time.Duration // 限流间隔
	timer          *time.Timer   // 定时器
	mutex          sync.Mutex    // 保护并发访问
	updateFunc     func() error  // 更新函数
	stopChan       chan struct{} // 停止信号
	triggerChan    chan struct{} // 触发信号
	isThrottling   bool          // 是否正在限流中
	pendingTrigger bool          // 是否有待处理的触发
}

// NewUpdateThrottler 创建新的更新限流器
func NewUpdateThrottler(interval time.Duration, updateFunc func() error) *UpdateThrottler {
	return &UpdateThrottler{
		interval:    interval,
		updateFunc:  updateFunc,
		stop<PERSON>han:    make(chan struct{}),
		triggerChan: make(chan struct{}, 100), // 缓冲通道，避免阻塞
	}
}

// Start 启动限流器
func (t *UpdateThrottler) Start() {
	go t.run()
}

// Stop 停止限流器
func (t *UpdateThrottler) Stop() {
	close(t.stopChan)
	t.mutex.Lock()
	if t.timer != nil {
		t.timer.Stop()
	}
	t.mutex.Unlock()
}

// Trigger 触发一次更新请求
func (t *UpdateThrottler) Trigger() {
	select {
	case t.triggerChan <- struct{}{}:
		// 成功发送触发信号
	default:
		// 通道已满，丢弃这次触发（避免阻塞）
		logrus.Warnf("UpdateThrottler trigger channel is full, dropping trigger")
	}
}

// run 限流器的主循环
func (t *UpdateThrottler) run() {
	for {
		select {
		case <-t.triggerChan:
			t.handleTrigger()
		case <-t.stopChan:
			logrus.Infof("UpdateThrottler stopped")
			return
		}
	}
}

// handleTrigger 处理触发信号
func (t *UpdateThrottler) handleTrigger() {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	// 如果正在限流中，标记有待处理的触发，但不执行
	if t.isThrottling {
		if !t.pendingTrigger {
			t.pendingTrigger = true
			logrus.Infof("Throttling active, marking pending trigger")
		} else {
			logrus.Infof("Throttling active, pending trigger already marked")
		}
		return
	}

	// 立即执行更新
	logrus.Infof("Executing immediate update (not throttling)")
	err := t.updateFunc()
	if err != nil {
		logrus.Errorf("Failed to execute immediate update: %v", err)
	}

	// 开始限流期
	t.isThrottling = true
	t.pendingTrigger = false

	// 启动限流定时器
	t.timer = time.AfterFunc(t.interval, t.onThrottleExpired)

	logrus.Infof("Started throttle period for %v", t.interval)
}

// onThrottleExpired 限流期到期的回调函数
func (t *UpdateThrottler) onThrottleExpired() {
	t.mutex.Lock()
	defer t.mutex.Unlock()

	t.isThrottling = false

	// 如果限流期间有待处理的触发，执行一次更新
	if t.pendingTrigger {
		logrus.Infof("Throttle period ended, executing pending update")
		err := t.updateFunc()
		if err != nil {
			logrus.Errorf("Failed to execute pending update: %v", err)
		}

		// 重新开始限流期
		t.isThrottling = true
		t.pendingTrigger = false
		t.timer = time.AfterFunc(t.interval, t.onThrottleExpired)
		logrus.Infof("Restarted throttle period for %v", t.interval)
	} else {
		logrus.Infof("Throttle period ended, no pending triggers")
		t.timer = nil
	}
}

// IsThrottling 返回当前是否在限流中（用于测试）
func (t *UpdateThrottler) IsThrottling() bool {
	t.mutex.Lock()
	defer t.mutex.Unlock()
	return t.isThrottling
}

// HasPendingTrigger 返回是否有待处理的触发（用于测试）
func (t *UpdateThrottler) HasPendingTrigger() bool {
	t.mutex.Lock()
	defer t.mutex.Unlock()
	return t.pendingTrigger
}
