package informer

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/cache"

	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netclientset "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned"
	netinformer "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/informers/externalversions/network/v1"
	vpcv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

// sync the VpcAcl status from CR
// if there are different, update the VpcAcl status in db
func syncVpcAclCR() error {
	defer logrus.Infoln("finish vpc Acl sync between db and cr")

	logrus.Infoln("Start vpc Acl sync between db and cr")
	// get all the records in db, update from the status of cr
	ds, err := dao.GetVPCACLsByType(rp.BosonProvider.Engine, vpcv1.ACLProperties_SYSTEM_TENANT_ACL.String())
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	crs, err := rp.BosonProvider.BosonNetClient.VpcAcls(VpcAclNamespace).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	makeCrState := func(crs *netv1.VpcAclList) *(map[string]string) {
		m := make(map[string]string)
		for _, cr := range crs.Items {
			m[cr.Name] = cr.Status.State
		}
		return &m
	}

	crsState := makeCrState(crs)
	for _, data := range ds {
		state, ok := (*crsState)[data.Name]
		if !ok {
			logrus.Warnf("no VpcAcl cr for %s in %s", data.Name, db.AZ)
			state = vpcv1.ACL_DELETED.String()
		}
		if data.State != state {
			if err := dao.SetVPCACLState(rp.BosonProvider.Engine, data.VpcAclID, state); err != nil {
				logrus.Errorln(err)
				return err
			}
			logrus.Infof("sync vpc Acl %v status from %v to %v\n", data.Name, data.State, state)
		}
	}
	return nil
}

// VpcAclController k8s controller
type VpcAclController struct {
	ClientSet netclientset.Interface
	Informer  cache.SharedIndexInformer
	ns        string
}

// Run starts the process for listening for dgw changes and acting upon those changes
func (c *VpcAclController) Run(stop <-chan struct{}) {
	defer logrus.Errorln("Stop VpcAclController")
	logrus.Infoln("Start VpcAclController")

	// Execute go function
	c.Informer.Run(stop)

	// Wait till we receive a stop signal
	<-stop
}

// VpcAclController init VpcAcl controller EventHandler
func (c *VpcAclController) NewVpcAclController() {
	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalln(err)
	}

	cs, err := netclientset.NewForConfig(cfg)
	if err != nil {
		// to do
		logrus.Fatalln(err)
	}

	informer := netinformer.NewVpcAclInformer(cs, c.ns, time.Hour*24, nil)

	c.ClientSet = cs
	c.Informer = informer

	c.Informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.createVpcAcl,
		UpdateFunc: c.updateVpcAcl,
		DeleteFunc: c.deleteVpcAcl,
	})
}

func (c *VpcAclController) createVpcAcl(obj interface{}) {

}

func (c *VpcAclController) updateVpcAcl(oldObj interface{}, obj interface{}) {
	o1 := oldObj.(*netv1.VpcAcl)
	o2 := obj.(*netv1.VpcAcl)

	// update the db state via to the State
	if o2.Status.State != o1.Status.State {
		logrus.Infof("Update vpc acl %v status from %v to %v\n", o1.Name, o1.Status.State, o2.Status.State)
		state := ""
		switch o2.Status.State {
		case vpcv1.ACL_DELETED.String():
			state = o2.Status.State
		// CREATING - ACTIVE
		case vpcv1.ACL_ACTIVE.String():
			state = o2.Status.State
		// """" - CREATING
		case vpcv1.ACL_CREATING.String():
			state = o2.Status.State
		// "" - UPDATING
		case vpcv1.ACL_UPDATING.String():
			state = o2.Status.State
		case vpcv1.ACL_DELETING.String():
			state = o2.Status.State
		case vpcv1.ACL_FAILED.String():
			state = o2.Status.State
		default:
			logrus.Errorf("others VpcAcl[%v] status translate case from %v to %v\n", o1.Name, o1.Status.State, o2.Status.State)
		}

		if state != "" && o2.Spec.Priority >= rp.SYSTEM_ACL_RESERVED_PRIORITY_MIN {
			logrus.Infof("Update VpcAcl[%v] status from %v to %v\n", o1.Name, o1.Status.State, o2.Status.State)

			err := dao.SetVPCACLStateByName(rp.BosonProvider.Engine, o2.Name, state)
			if err != nil {
				logrus.Errorln(err)
				return
			}
		}

	}
}

func (c *VpcAclController) deleteVpcAcl(obj interface{}) {
	o := obj.(*netv1.VpcAcl)
	logrus.Infof("delete vpc acl[%v] with status from %v \n", o.Name, o.Status.State)
	err := dao.SetVPCACLStateByName(rp.BosonProvider.Engine, o.Name, vpcv1.ACL_DELETED.String())
	if err != nil {
		logrus.Errorln(err)
		return
	}
}
