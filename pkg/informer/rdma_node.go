package informer

import (
	"context"
	"fmt"
	"strconv"
	"strings"
	"sync"
	"time"

	"github.com/sirupsen/logrus"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/util/workqueue"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

const (
	bosonNamespace = "plat-boson-service"
	// RDMA相关标签
	LabelRDMAProtocol    = "diamond.sensetime.com/nic-training-protocol"
	LabelRDMABandwidth   = "diamond.sensetime.com/nic-training-bandwidth"
	LabelRDMANicCount    = "diamond.sensetime.com/nic-training-port"
	LabelRDMAClusterName = "diamond.sensetime.com/belong-resource-training"

	// 定时器去重配置
	DefaultDebounceInterval = 30 * time.Second // 默认去重间隔：30秒内的多次触发只执行一次
)

// UpdateDebouncer 更新去重器，用于减少频繁的缓存更新操作
type UpdateDebouncer struct {
	interval    time.Duration // 去重间隔
	timer       *time.Timer   // 定时器
	mutex       sync.Mutex    // 保护并发访问
	updateFunc  func() error  // 更新函数
	stopChan    chan struct{} // 停止信号
	triggerChan chan struct{} // 触发信号
}

// NewUpdateDebouncer 创建新的更新去重器
func NewUpdateDebouncer(interval time.Duration, updateFunc func() error) *UpdateDebouncer {
	return &UpdateDebouncer{
		interval:    interval,
		updateFunc:  updateFunc,
		stopChan:    make(chan struct{}),
		triggerChan: make(chan struct{}, 100), // 缓冲通道，避免阻塞
	}
}

// Start 启动去重器
func (d *UpdateDebouncer) Start() {
	go d.run()
}

// Stop 停止去重器
func (d *UpdateDebouncer) Stop() {
	close(d.stopChan)
	d.mutex.Lock()
	if d.timer != nil {
		d.timer.Stop()
	}
	d.mutex.Unlock()
}

// Trigger 触发一次更新请求
func (d *UpdateDebouncer) Trigger() {
	select {
	case d.triggerChan <- struct{}{}:
		// 成功发送触发信号
	default:
		// 通道已满，丢弃这次触发（避免阻塞）
		logrus.Warnf("UpdateDebouncer trigger channel is full, dropping trigger")
	}
}

// run 去重器的主循环
func (d *UpdateDebouncer) run() {
	for {
		select {
		case <-d.triggerChan:
			d.handleTrigger()
		case <-d.stopChan:
			logrus.Infof("UpdateDebouncer stopped")
			return
		}
	}
}

// handleTrigger 处理触发信号
func (d *UpdateDebouncer) handleTrigger() {
	d.mutex.Lock()
	defer d.mutex.Unlock()

	// 如果定时器已经存在，说明在去重间隔内，重置定时器
	if d.timer != nil {
		d.timer.Stop()
		logrus.Debugf("Reset debounce timer due to new trigger")
	}

	// 创建新的定时器
	d.timer = time.AfterFunc(d.interval, func() {
		logrus.Infof("Debounce timer expired, executing update")
		err := d.updateFunc()
		if err != nil {
			logrus.Errorf("Failed to execute debounced update: %v", err)
		}

		d.mutex.Lock()
		d.timer = nil
		d.mutex.Unlock()
	})

	logrus.Debugf("Debounce timer started/reset for %v", d.interval)
}

// RDMANodeController 用于监听RDMA相关节点变化的控制器
type RDMANodeController struct {
	Clientset kubernetes.Interface
	Informer  cache.SharedIndexInformer
	Queue     workqueue.RateLimitingInterface
	Debouncer *UpdateDebouncer
}

func NewRDMANodeController() *RDMANodeController {
	clientset, err := utils.GetClientSet()
	if err != nil {
		logrus.Fatalln(err)
	}

	// 创建去重器，支持从环境变量加载配置
	debouncer := NewUpdateDebouncerWithConfig(func() error {
		return UpdateRDMACache(context.Background())
	})

	labelSelector := LabelRDMAProtocol
	nodeListWatcher := cache.NewFilteredListWatchFromClient(
		clientset.CoreV1().RESTClient(),
		"nodes",
		metav1.NamespaceAll,
		func(options *metav1.ListOptions) {
			options.LabelSelector = labelSelector
		},
	)

	queue := workqueue.NewRateLimitingQueue(workqueue.DefaultControllerRateLimiter())

	informer := cache.NewSharedIndexInformer(
		nodeListWatcher,
		&corev1.Node{},
		time.Hour*24,
		cache.Indexers{cache.NamespaceIndex: cache.MetaNamespaceIndexFunc},
	)

	// 添加事件处理函数
	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc: func(obj interface{}) {
			key, err := cache.MetaNamespaceKeyFunc(obj)
			if err == nil {
				queue.Add(key)
			}
		},
		UpdateFunc: func(oldObj, newObj interface{}) {
			oldNode := oldObj.(*corev1.Node)
			newNode := newObj.(*corev1.Node)

			// 检查RDMA相关标签是否有变化
			if hasRDMALabelChanges(oldNode, newNode) {
				key, err := cache.MetaNamespaceKeyFunc(newObj)
				if err == nil {
					queue.Add(key)
				}
			}
		},
		DeleteFunc: func(obj interface{}) {
			key, err := cache.DeletionHandlingMetaNamespaceKeyFunc(obj)
			if err == nil {
				queue.Add(key)
			}
		},
	})

	rdmaNodeController := &RDMANodeController{
		Clientset: clientset,
		Informer:  informer,
		Queue:     queue,
		Debouncer: debouncer,
	}

	return rdmaNodeController
}

// hasRDMALabelChanges 检查节点的RDMA相关标签是否有变化
func hasRDMALabelChanges(oldNode, newNode *corev1.Node) bool {
	rdmaLabels := []string{
		LabelRDMAProtocol,
		LabelRDMABandwidth,
		LabelRDMANicCount,
		LabelRDMAClusterName,
	}

	for _, label := range rdmaLabels {
		if oldNode.Labels[label] != newNode.Labels[label] {
			return true
		}
	}

	return false
}

// Run 启动RDMA节点控制器
func (c *RDMANodeController) Run(stopCh <-chan struct{}) {
	defer logrus.Errorln("Stop RDMANodeController")
	logrus.Infoln("Start RDMANodeController")

	// 启动去重器
	c.Debouncer.Start()
	defer c.Debouncer.Stop()

	// 启动informer
	go c.Informer.Run(stopCh)

	// 等待informer同步
	if !cache.WaitForCacheSync(stopCh, c.Informer.HasSynced) {
		logrus.Errorln("Timed out waiting for RDMA node informer caches to sync")
		return
	}

	// 启动工作队列处理
	go c.runWorker()

	defer c.Queue.ShutDown()

	// 初始化缓存
	if err := UpdateRDMACache(context.Background()); err != nil {
		logrus.Errorf("Failed to initialize RDMA cache: %v", err)
	}

	// 等待停止信号
	<-stopCh
}

// runWorker 运行工作队列处理循环
func (c *RDMANodeController) runWorker() {
	for c.processNextItem() {
	}
}

// processNextItem 处理工作队列中的下一个项目
func (c *RDMANodeController) processNextItem() bool {
	// 从队列中获取下一个项目
	key, quit := c.Queue.Get()
	if quit {
		return false
	}

	// 标记该项目正在处理
	defer c.Queue.Done(key)

	// 触发去重器，而不是直接更新缓存
	c.Debouncer.Trigger()

	// 总是标记为成功处理，因为实际的缓存更新由去重器负责
	c.Queue.Forget(key)

	return true
}

func UpdateRDMACache(ctx context.Context) error {
	// 获取互斥锁以更新缓存
	types.RDMACache.Mutex.Lock()
	defer types.RDMACache.Mutex.Unlock()

	newData := &vpc.VPCRDMAClustersStatus{}

	clientset, err := utils.GetClientSet()
	if err != nil {
		return err
	}

	nodes, err := clientset.CoreV1().Nodes().List(ctx, metav1.ListOptions{
		LabelSelector:   LabelRDMAProtocol,
		ResourceVersion: "0", // 强制使用apiserver缓存
	})
	if err != nil {
		logrus.Errorf("failed to list nodes, err: %v", err)
		return err
	}

	// 按协议类型分组节点
	protocolGroups := make(map[string][]*corev1.Node)
	for i := range nodes.Items {
		node := &nodes.Items[i]
		protocol := node.Labels[LabelRDMAProtocol]
		protocolGroups[protocol] = append(protocolGroups[protocol], node)
	}

	for protocol, nodeGroup := range protocolGroups {
		logrus.Infof("protocol: %s, node count: %d", protocol, len(nodeGroup))

		// 按照集群名字分组
		rdmaClusters := make(map[string][]*corev1.Node)
		for _, node := range nodeGroup {
			clusterName := node.Labels[LabelRDMAClusterName]
			if clusterName != "" {
				rdmaClusters[clusterName] = append(rdmaClusters[clusterName], node)
			}
		}

		for name, clusterNodes := range rdmaClusters {
			logrus.Infof("handle rdma cluster: %s", name)

			clusterStatus := &vpc.ClusterStatus{
				Name: name,
			}

			if protocol == "IB" {
				clusterStatus.Protocol = vpc.ClusterStatus_IB
			} else if protocol == "RoCE" {
				clusterStatus.Protocol = vpc.ClusterStatus_RoCE
			} else if protocol == "None" {
				clusterStatus.Protocol = vpc.ClusterStatus_None
			}

			networkInfors := getRDMAClusterNetworkInfos(name, clusterNodes)
			clusterStatus.NetworkInfos = networkInfors

			if protocol == "IB" {
				ibProperties, err := getIBClusterProperty(ctx, clientset, name)
				if err != nil {
					logrus.Errorf("failed to get ib cluster property: %v", err)
					continue
				}
				clusterStatus.Properties = ibProperties
			}

			newData.ClustersStatus = append(newData.ClustersStatus, clusterStatus)
		}

	}

	// 更新缓存
	types.RDMACache.Data = newData

	logrus.Infof("update rdma cache success: %v", types.RDMACache.Data)

	return nil
}

func getRDMAClusterNetworkInfos(name string, clusterNodes []*corev1.Node) []*vpc.NetworkInfo {

	bandwidthNicCountGroups := make(map[string]map[string][]*corev1.Node)
	for _, node := range clusterNodes {
		bandwidth := node.Labels[LabelRDMABandwidth]
		nicCount := node.Labels[LabelRDMANicCount]

		if bandwidth == "" {
			bandwidth = "0"
		}
		if nicCount == "" {
			nicCount = "0"
		}

		if bandwidthNicCountGroups[bandwidth] == nil {
			bandwidthNicCountGroups[bandwidth] = make(map[string][]*corev1.Node)
		}
		bandwidthNicCountGroups[bandwidth][nicCount] = append(bandwidthNicCountGroups[bandwidth][nicCount], node)
	}

	networkInfos := []*vpc.NetworkInfo{}
	for bandwidth, nicCountGroups := range bandwidthNicCountGroups {
		for nicCount, nodes := range nicCountGroups {
			logrus.Infof("rdma cluster: %s, bandwidth: %s, nicCount: %s, node count: %d", name, bandwidth, nicCount, len(nodes))
			nicCountInt, _ := strconv.Atoi(nicCount)

			networkInfo := &vpc.NetworkInfo{
				Bandwidth: bandwidth,
				NicCount:  int32(nicCountInt),
				NodeCount: int32(len(nodes)),
			}
			networkInfos = append(networkInfos, networkInfo)
		}
	}

	return networkInfos
}

func getIBClusterProperty(ctx context.Context, clientset *kubernetes.Clientset, name string) (map[string]string, error) {
	configMapName := "boson-ibm-" + name + "-config"
	configMap, err := clientset.CoreV1().ConfigMaps(bosonNamespace).Get(ctx, configMapName, metav1.GetOptions{})
	if err != nil {
		logrus.Errorf("failed to get configmap %s: %v", configMapName, err)
		return nil, err
	}

	logrus.Infof("get ib %s configmap success", name)

	yamlData := configMap.Data["boson-ib-manager.yaml"]
	if yamlData == "" {
		err := fmt.Errorf("empty configmap %v", configMap)
		logrus.Errorln(err)
		return nil, err
	}

	enableSharp := false
	if strings.Contains(yamlData, "enableSharp: true") {
		enableSharp = true
	}

	logrus.Infof("ib %s enableSharp: %v", name, enableSharp)

	properties := map[string]string{"sharp_enable": strconv.FormatBool(enableSharp)}

	return properties, nil
}
