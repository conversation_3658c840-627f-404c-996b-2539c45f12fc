package informer

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/cache"

	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netclientset "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned"
	netinformer "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/informers/externalversions/network/v1"
	eipv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

// sync the acl status from CR
// if there are different, update the acl status in db
func syncAclCR() error {
	defer logrus.Infoln("finish ACL sync between db and cr")

	logrus.Infoln("Start ACL sync between db and cr")
	// get all the records in db, update from the status of cr
	ds, err := dao.GetAllACLs(rp.BosonProvider.Engine, true)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	crs, err := rp.BosonProvider.BosonNetClient.ACLs(
		rp.BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	makeCrState := func(crs *netv1.ACLList) *(map[string]string) {
		m := make(map[string]string)
		for _, cr := range crs.Items {
			m[cr.Name] = cr.Status.State
		}
		return &m
	}

	crsState := makeCrState(crs)
	for _, data := range ds {
		state, ok := (*crsState)[data.Name]
		if !ok {
			logrus.Errorf("no ACL cr for %s", data.Name)
			continue
		}
		if data.State != state {
			if err := dao.SetACLState(rp.BosonProvider.Engine, data.AclID, state); err != nil {
				logrus.Errorln(err)
				return err
			}
			logrus.Infof("sync ACL %v status from %v to %v\n", data.Name, data.State, state)
		}
	}
	return nil
}

// ACLController k8s controller
type ACLController struct {
	ClientSet netclientset.Interface
	Informer  cache.SharedIndexInformer
	ns        string
}

// Run starts the process for listening for dgw changes and acting upon those changes
func (c *ACLController) Run(stop <-chan struct{}) {
	defer logrus.Errorln("Stop ACLController")
	logrus.Infoln("Start ACLController")

	// Execute go function
	c.Informer.Run(stop)

	// Wait till we receive a stop signal
	<-stop
}

// ACLController init ACL controller EventHandler
func (c *ACLController) NewACLController() {
	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalln(err)
	}

	cs, err := netclientset.NewForConfig(cfg)
	if err != nil {
		// to do
		logrus.Fatalln(err)
	}

	if c.ns = rp.BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs; c.ns == "" {
		reason := fmt.Sprintf("invalid boson infra namespace config %v", rp.BosonProvider.RpConfig.Boson.BosonDefaultDgw)
		logrus.Fatalln(reason)
	}

	informer := netinformer.NewACLInformer(cs, c.ns, time.Hour*24, nil)

	c.ClientSet = cs
	c.Informer = informer

	c.Informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.createACL,
		UpdateFunc: c.updateACL,
		DeleteFunc: c.deleteACL,
	})
}

func (c *ACLController) createACL(obj interface{}) {

}

func (c *ACLController) updateACL(oldObj interface{}, obj interface{}) {
	o1 := oldObj.(*netv1.ACL)
	o2 := obj.(*netv1.ACL)

	// update the db state via to the State
	if o2.Status.State != o1.Status.State {
		logrus.Infof("Update DGW %v status from %v to %v\n", o1.Name, o1.Status.State, o2.Status.State)
		state := ""
		switch o2.Status.State {
		case eipv1.ACL_DELETED.String():
			state = o2.Status.State
		// CREATING - ACTIVE
		case eipv1.ACL_ACTIVE.String():
			state = o2.Status.State
		// """" - CREATING
		case eipv1.ACL_CREATING.String():
			state = o2.Status.State
		// "" - UPDATING
		case eipv1.ACL_UPDATING.String():
			state = o2.Status.State
		case eipv1.ACL_DELETING.String():
			state = o2.Status.State
		case eipv1.ACL_FAILED.String():
			state = o2.Status.State
		default:
			logrus.Errorf("others ACL[%v] status translate case from %v to %v\n", o1.Name, o1.Status.State, o2.Status.State)
		}

		if state != "" {
			logrus.Infof("Update ACL[%v] status from %v to %v\n", o1.Name, o1.Status.State, o2.Status.State)
			// FIXME: multi-cluster not considered
			err := dao.SetACLStateByName(rp.BosonProvider.Engine, o2.Name, state)
			if err != nil {
				logrus.Errorln(err)
				return
			}
		}

	}
}

func (c *ACLController) deleteACL(obj interface{}) {
	o := obj.(*netv1.ACL)
	logrus.Infof("delete ACL[%v] with status from %v \n", o.Name, o.Status.State)
	err := dao.SetACLStateByName(rp.BosonProvider.Engine, o.Name, o.Status.State)
	if err != nil {
		logrus.Errorln(err)
		return
	}
}
