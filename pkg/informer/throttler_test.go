package informer

import (
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
)

// 测试用的模拟更新函数
var (
	mockUpdateCount int
	mockUpdateMutex sync.Mutex
)

func resetMockUpdateCount() {
	mockUpdateMutex.Lock()
	defer mockUpdateMutex.Unlock()
	mockUpdateCount = 0
}

func getMockUpdateCount() int {
	mockUpdateMutex.Lock()
	defer mockUpdateMutex.Unlock()
	return mockUpdateCount
}

func mockUpdateFunc() error {
	mockUpdateMutex.Lock()
	defer mockUpdateMutex.Unlock()
	mockUpdateCount++
	return nil
}

// TestUpdateThrottler_SingleTrigger 测试单次触发
func TestUpdateThrottler_SingleTrigger(t *testing.T) {
	resetMockUpdateCount()

	throttler := NewUpdateThrottler(100*time.Millisecond, mockUpdateFunc)
	throttler.Start()
	defer throttler.Stop()

	// 触发一次
	throttler.Trigger()

	// 立即检查，应该已经执行了一次（立即执行）
	time.Sleep(10 * time.Millisecond) // 给一点时间让goroutine执行
	assert.Equal(t, 1, getMockUpdateCount(), "Should execute immediately on first trigger")

	// 检查是否在限流中
	assert.True(t, throttler.IsThrottling(), "Should be throttling after first trigger")

	// 等待限流期结束
	time.Sleep(120 * time.Millisecond)
	assert.False(t, throttler.IsThrottling(), "Should not be throttling after period ends")
}

// TestUpdateThrottler_MultipleTriggers 测试多次触发限流
func TestUpdateThrottler_MultipleTriggers(t *testing.T) {
	resetMockUpdateCount()

	throttler := NewUpdateThrottler(200*time.Millisecond, mockUpdateFunc)
	throttler.Start()
	defer throttler.Stop()

	// 第一次触发应该立即执行
	throttler.Trigger()
	time.Sleep(10 * time.Millisecond)
	assert.Equal(t, 1, getMockUpdateCount(), "Should execute immediately on first trigger")
	assert.True(t, throttler.IsThrottling(), "Should be throttling")

	// 快速触发多次，应该被限流
	for i := 0; i < 4; i++ {
		throttler.Trigger()
		time.Sleep(30 * time.Millisecond)
	}

	// 仍然只执行了一次，但有待处理的触发
	assert.Equal(t, 1, getMockUpdateCount(), "Should still be 1 during throttling")
	assert.True(t, throttler.HasPendingTrigger(), "Should have pending trigger")

	// 等待限流期结束
	time.Sleep(200 * time.Millisecond)

	// 现在应该执行了待处理的更新
	assert.Equal(t, 2, getMockUpdateCount(), "Should execute pending update after throttle period")
}

// TestUpdateThrottler_SeparatedTriggers 测试分离的触发
func TestUpdateThrottler_SeparatedTriggers(t *testing.T) {
	resetMockUpdateCount()

	throttler := NewUpdateThrottler(100*time.Millisecond, mockUpdateFunc)
	throttler.Start()
	defer throttler.Stop()

	// 第一次触发
	throttler.Trigger()
	time.Sleep(10 * time.Millisecond)
	assert.Equal(t, 1, getMockUpdateCount(), "Should execute immediately on first trigger")

	// 等待限流期结束
	time.Sleep(120 * time.Millisecond)
	assert.False(t, throttler.IsThrottling(), "Should not be throttling")

	// 第二次触发（与第一次间隔足够长）
	throttler.Trigger()
	time.Sleep(10 * time.Millisecond)
	assert.Equal(t, 2, getMockUpdateCount(), "Should execute immediately on second trigger")
}

// TestUpdateThrottler_ConcurrentTriggers 测试并发触发
func TestUpdateThrottler_ConcurrentTriggers(t *testing.T) {
	resetMockUpdateCount()

	throttler := NewUpdateThrottler(200*time.Millisecond, mockUpdateFunc)
	throttler.Start()
	defer throttler.Stop()

	// 并发触发多次
	var wg sync.WaitGroup
	for i := 0; i < 20; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()
			throttler.Trigger()
		}()
	}
	wg.Wait()

	// 等待处理完成
	time.Sleep(50 * time.Millisecond)

	// 应该立即执行了一次
	assert.Equal(t, 1, getMockUpdateCount(), "Should execute once immediately")
	assert.True(t, throttler.HasPendingTrigger(), "Should have pending trigger")

	// 等待限流期结束
	time.Sleep(200 * time.Millisecond)

	// 应该执行了待处理的更新
	assert.Equal(t, 2, getMockUpdateCount(), "Should execute pending update")
}

// TestUpdateThrottler_NoExtraExecution 测试限流期内无额外执行
func TestUpdateThrottler_NoExtraExecution(t *testing.T) {
	resetMockUpdateCount()

	throttler := NewUpdateThrottler(150*time.Millisecond, mockUpdateFunc)
	throttler.Start()
	defer throttler.Stop()

	// 第一次触发
	throttler.Trigger()
	time.Sleep(10 * time.Millisecond)
	assert.Equal(t, 1, getMockUpdateCount(), "Should execute once")

	// 等待限流期结束，但不触发新的请求
	time.Sleep(200 * time.Millisecond)

	// 应该仍然只执行了一次
	assert.Equal(t, 1, getMockUpdateCount(), "Should not execute extra times")
	assert.False(t, throttler.IsThrottling(), "Should not be throttling")
	assert.False(t, throttler.HasPendingTrigger(), "Should not have pending trigger")
}

// TestUpdateThrottler_Stop 测试停止功能
func TestUpdateThrottler_Stop(t *testing.T) {
	resetMockUpdateCount()

	throttler := NewUpdateThrottler(100*time.Millisecond, mockUpdateFunc)
	throttler.Start()

	// 触发一次
	throttler.Trigger()
	time.Sleep(10 * time.Millisecond)
	assert.Equal(t, 1, getMockUpdateCount(), "Should execute once")

	// 立即停止
	throttler.Stop()

	// 等待一段时间，确保即使限流期到期也不会执行待处理的更新
	time.Sleep(150 * time.Millisecond)

	// 应该仍然只执行了一次
	assert.Equal(t, 1, getMockUpdateCount(), "Should not execute after stop")
}

// BenchmarkUpdateThrottler 性能测试
func BenchmarkUpdateThrottler(b *testing.B) {
	resetMockUpdateCount()

	throttler := NewUpdateThrottler(10*time.Millisecond, mockUpdateFunc)
	throttler.Start()
	defer throttler.Stop()

	b.ResetTimer()

	for i := 0; i < b.N; i++ {
		throttler.Trigger()
	}

	// 等待所有操作完成
	time.Sleep(50 * time.Millisecond)
}
