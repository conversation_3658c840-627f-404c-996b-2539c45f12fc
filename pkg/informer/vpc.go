package informer

import (
	"strings"
	"time"

	v1 "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	ovnclientset "github.com/kubeovn/kube-ovn/pkg/client/clientset/versioned"
	ovninformer "github.com/kubeovn/kube-ovn/pkg/client/informers/externalversions/kubeovn/v1"
	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"k8s.io/client-go/tools/cache"
)

// VPCController k8s controller
type VPCController struct {
	VPCClientSet ovnclientset.Interface
	VPCInformer  cache.SharedIndexInformer
}

// Run starts the process for listening for VPC changes and acting upon those changes
func (c *VPCController) Run(stopVPC <-chan struct{}) {
	// When this function completes, mark the go function as done
	//defer wg.Done()
	defer logrus.Errorln("Stop VPCController")

	logrus.Infoln("Start VPCController")

	// Execute go function
	go c.VPCInformer.Run(stopVPC)

	// Wait till we receive a stop signal
	<-stopVPC
}

// VPCController 初始化 VPC controller EventHandler
func (c *VPCController) NewVPCController() {

	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalln(err)
	}

	cs, err := ovnclientset.NewForConfig(cfg)
	if err != nil {
		logrus.Fatalln(err)
	}

	informer := ovninformer.NewVpcInformer(cs, time.Hour*24, nil)

	c.VPCClientSet = cs
	c.VPCInformer = informer

	c.VPCInformer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.createVPC,
		UpdateFunc: c.updateVPC,
		DeleteFunc: c.deleteVPC,
	})
}

func (c *VPCController) createVPC(obj interface{}) {
}

func (c *VPCController) updateVPC(oldObj interface{}, obj interface{}) {
	o1 := oldObj.(*v1.Vpc)
	o2 := obj.(*v1.Vpc)
	logrus.Infof("Update VPC from %+v to %+v", o1, o2)

	if o2.Annotations["boson.sensetime.com/dgw-localport-name"] != "" {
		dgwName := strings.Replace(o2.Name, "vpc-", "dgw-", 1)
		_, has, err := dao.GetDGatewayByName(rp.BosonProvider.Engine, dgwName)
		if err != nil {
			logrus.Errorf("Get VPC: %s dgw: %s fail: %+v ", o2.Name, dgwName, err)
			return
		}

		if !has {
			// localport create finish
			admin := rp.BosonProvider.RpConfig.Boson.BosonDefaultDgw.Enable
			subnet := rp.BosonProvider.RpConfig.Boson.BosonDefaultDgw.ExternalSubnet
			gwPolicy := rp.BosonProvider.RpConfig.Boson.BosonDefaultDgw.PolicyCIDR
			if err := rp.CreateDgw(o2.Name, dgwName, admin, subnet,
				o2.Annotations["boson.sensetime.com/dgw-localport-ipaddress"], gwPolicy); err != nil {
				logrus.Errorf("error to create dgw for vpc %s, detail %s", o2.Name, err)
			}
		}
	}
}

func (c *VPCController) deleteVPC(obj interface{}) {
	vpc := obj.(*v1.Vpc)
	logrus.Infof("deleted vpc %s", vpc.Name)
	if err := rp.DeleteDgw(strings.Replace(vpc.Name, "vpc-", "dgw-", 1)); err != nil {
		logrus.Errorf("error to delete dgw for vpc %s, detail %s", vpc.Name, err)
	}
}
