package informer

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/cache"

	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netclientset "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned"
	netinformer "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/informers/externalversions/network/v1"
	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	rp "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

// sync the dgw status from CR
// if there are different, update the dgw status in db
// if f the CR does not exist, lgo err (recreated??)
// if the CR exists and no record, ingore this now
func syncDgwCR() error {
	defer logrus.Infoln("finish dgw sync between db and cr")

	logrus.Infoln("Start dgw sync between db and cr")
	// get all the records in db, update from the status of cr
	dgwDatas, err := dao.GetAllDGateway(rp.BosonProvider.Engine, true)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	ruleCrs, err := resourceprovider.BosonProvider.BosonNetClient.DGWs(
		resourceprovider.BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs).List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	makeCrState := func(crs *netv1.DGWList) *(map[string]string) {
		m := make(map[string]string)
		for _, cr := range crs.Items {
			m[cr.Name] = cr.Status.State
		}
		return &m
	}

	ruleCrsState := makeCrState(ruleCrs)
	//logrus.Infof("dgw cr status: %v", *ruleCrsState)

	for _, dgwData := range dgwDatas {
		state, ok := (*ruleCrsState)[dgwData.Name]
		if !ok {
			logrus.Errorf("no dgw cr for %s", dgwData.Name)
			continue
		}
		if dgwData.State != state {
			if err := dao.SetDGatewayStateByName(resourceprovider.BosonProvider.Engine, dgwData.Name, state); err != nil {
				logrus.Errorln(err)
				return err
			}
			logrus.Infof("sync dgw %v status from %v to %v\n", dgwData.Name, dgwData.State, state)
			//resourceprovider.BosonProvider.Processor.StateMessageSend(dgwData.ID, state)
		}
	}
	return nil
}

// DgwController k8s controller
type DgwController struct {
	ClientSet netclientset.Interface
	Informer  cache.SharedIndexInformer
	ns        string
}

// Run starts the process for listening for dgw changes and acting upon those changes
func (c *DgwController) Run(stop <-chan struct{}) {

	defer logrus.Errorln("Stop DgwController")

	logrus.Infoln("Start DgwController")

	// Execute go function
	c.Informer.Run(stop)

	// Wait till we receive a stop signal
	<-stop
}

// DgwController 初始化 dgw controller EventHandler
func (c *DgwController) NewDgwController() {
	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalln(err)
	}

	cs, err := netclientset.NewForConfig(cfg)
	if err != nil {
		// to do
		logrus.Fatalln(err)
	}

	if c.ns = rp.BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs; c.ns == "" {
		reason := fmt.Sprintf("invalid dgw config %v", rp.BosonProvider.RpConfig.Boson.BosonDefaultDgw)
		logrus.Fatalln(reason)
	}

	informer := netinformer.NewDGWInformer(cs, c.ns, time.Hour*24, nil)

	c.ClientSet = cs
	c.Informer = informer

	c.Informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.createDgw,
		UpdateFunc: c.updateDgw,
		DeleteFunc: c.deleteDgw,
	})
}

func (c *DgwController) createDgw(obj interface{}) {

}

func (c *DgwController) updateDgw(oldObj interface{}, obj interface{}) {
	o1 := oldObj.(*netv1.DGW)
	o2 := obj.(*netv1.DGW)

	// update the db state via to the State
	if o2.Status.State != o1.Status.State {
		logrus.Infof("Update DGW %v status from %v to %v\n", o1.Name, o1.Status.State, o2.Status.State)
		state := ""
		switch o2.Status.State {
		case network.DGWGatewayStatus_DELETED.String():
			state = o2.Status.State
		// CREATING - ACTIVE
		case network.DGWGatewayStatus_ACTIVE.String():
			state = o2.Status.State
		// """" - CREATING
		case network.DGWGatewayStatus_CREATING.String():
			state = o2.Status.State
		case network.DGWGatewayStatus_UPDATING.String():
			state = o2.Status.State
		// "" - INACTIVE
		case network.DGWGatewayStatus_INACTIVE.String():
			state = o2.Status.State
		case network.DGWGatewayStatus_DELETING.String():
			state = o2.Status.State
		case network.DGWGatewayStatus_FAILED.String():
			state = o2.Status.State
		default:
			logrus.Errorf("others DGW[%v] status translate case from %v to %v\n", o1.Name, o1.Status.State, o2.Status.State)
		}

		if state != "" {
			logrus.Infof("Update DGW[%v] status from %v to %v\n", o1.Name, o1.Status.State, o2.Status.State)
			// FIXME: multi-cluster not considered
			err := dao.SetDGatewayStateByName(rp.BosonProvider.Engine, o2.Name, state)

			if err != nil {
				logrus.Errorln(err)
				return
			}
		}

	}
}

func (c *DgwController) deleteDgw(obj interface{}) {
	o := obj.(*netv1.DGW)
	logrus.Infof("delete dgw[%v] with status from %v \n", o.Name, o.Status.State)

	err := dao.SetDGatewayStateByName(rp.BosonProvider.Engine, o.Name, o.Status.State)
	if err != nil {
		logrus.Errorln(err)
		return
	}
}
