package informer

import (
	"context"
	"fmt"
	"os"
	"strings"

	ovnapisv1 "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	netapisv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"

	kubeovnclientset "github.com/kubeovn/kube-ovn/pkg/client/clientset/versioned"
	networkclientset "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned"

	kubeovnv1 "github.com/kubeovn/kube-ovn/pkg/client/clientset/versioned/typed/kubeovn/v1"
	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned/typed/network/v1"

	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	kubeinformers "k8s.io/client-go/informers"
	v1 "k8s.io/client-go/informers/core/v1"

	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	"gopkg.in/yaml.v2"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"
	"k8s.io/utils/ptr"
)

var (
	VpcAclNamespace     = "plat-boson-infra"
	VpcNameLabel        = "boson.sensetime.com/vpc-name"
	VpcAclResourceLabel = "boson.sensetime.com/vpc-acl-source"
	VpcAclResourceValue = "configmap"

	ConfigMapName   = "boson-provider-config"
	ConfigMapAclKey = "boson-provider.yaml"
	OvnVpc          = "ovn-cluster"
)

type ConfigmapController struct {
	K8sClient        *kubernetes.Clientset
	OvnClient        *kubeovnclientset.Clientset
	NetworkClientSet *networkclientset.Clientset
	KubeovnClient    *kubeovnv1.KubeovnV1Client
	BosonNetClient   *netv1.NetworkV1Client
	Informer         v1.ConfigMapInformer
}

func (c *ConfigmapController) Run(stop <-chan struct{}) {
	defer logrus.Errorln("Stop ConfigmapController")
	logrus.Infoln("Start ConfigampController")

	c.Informer.Informer().Run(stop)
	c.Informer.Informer().HasSynced()

	<-stop
}

// ConfigmapController init Configmap controller EventHandler
func (c *ConfigmapController) NewConfigmapController() {

	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalf("K8s config error: %v", err.Error())
	}

	kubeovnClient, err := kubeovnv1.NewForConfig(cfg)
	if err != nil {
		logrus.Fatalf("Kubeovn connection failed: %v", err.Error())
	}

	bosonNetClient, err := netv1.NewForConfig(cfg)
	if err != nil {
		logrus.Fatalf("Boson net connection failed: %v", err.Error())
	}

	networkclientSet, err := utils.GetNetworkClientSet()
	if err != nil {
		logrus.Fatalf("K8s connection failed: %v", err.Error())
	}

	k8sclient, err := utils.GetClientSet()
	if err != nil {
		logrus.Fatalf("K8s connection failed: %v", err.Error())
	}

	cmInformerFactory := kubeinformers.NewSharedInformerFactoryWithOptions(k8sclient, 0,
		kubeinformers.WithTweakListOptions(func(listOption *metav1.ListOptions) {
			listOption.AllowWatchBookmarks = true
		}), kubeinformers.WithNamespace(os.Getenv("BOSON_PROVIDER_POD_NAMESPACE")))
	configMapInformer := cmInformerFactory.Core().V1().ConfigMaps()

	c.KubeovnClient = kubeovnClient
	c.BosonNetClient = bosonNetClient
	c.NetworkClientSet = networkclientSet
	c.K8sClient = k8sclient
	c.Informer = configMapInformer

	c.Informer.Informer().AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.AddVPCAclConfig,
		UpdateFunc: c.UpdateVPCAclConfig,
		DeleteFunc: c.DeleteVPCAclConfig,
	})
}

func (c *ConfigmapController) AddVPCAclConfig(obj interface{}) {
	cm := obj.(*corev1.ConfigMap)
	if cm.Name != ConfigMapName {
		return
	}
	if cm.Data[ConfigMapAclKey] != "" {
		var cmData config.Config
		err := yaml.Unmarshal([]byte(cm.Data[ConfigMapAclKey]), &cmData)
		if err != nil {
			logrus.Errorf("error boson-provider.yaml format %v in boson-provider-config, %v", cm.Data[ConfigMapAclKey], err)
			return
		}
		// TODO filter acl
		c.SyncVPCAcls(cmData.Boson.VPCAcls)

	}
}

func (c *ConfigmapController) UpdateVPCAclConfig(oldObj interface{}, newObj interface{}) {
	cm := newObj.(*corev1.ConfigMap)
	if cm.Name != ConfigMapName {
		return
	}
	if cm.Data[ConfigMapAclKey] != "" {
		var cmData config.Config
		err := yaml.Unmarshal([]byte(cm.Data[ConfigMapAclKey]), &cmData)
		if err != nil {
			logrus.Errorf("error boson-provider.yaml format %v in boson-provider-config, %v", cm.Data[ConfigMapAclKey], err)
			return
		}
		// TODO filter acl
		c.SyncVPCAcls(cmData.Boson.VPCAcls)

	}
}

func (c *ConfigmapController) DeleteVPCAclConfig(obj interface{}) {
	cm := obj.(*corev1.ConfigMap)
	if cm.Name != ConfigMapName {
		return
	}
	vpcAcls := make([]config.VpcAcl, 0)
	c.SyncVPCAcls(vpcAcls)

}

func (c *ConfigmapController) SyncVPCAcls(vpcAcls []config.VpcAcl) {

	vpcsCli := c.KubeovnClient.Vpcs()
	vpcAclsCli := c.BosonNetClient.VpcAcls(VpcAclNamespace)
	vpcList, err := vpcsCli.List(context.Background(), metav1.ListOptions{})
	if err != nil {
		logrus.Error("can not list vpc in k8s")
		return
	}

	for _, vpc := range vpcList.Items {

		// skip the ovn vpc acl config
		if vpc.Name == OvnVpc {
			continue
		}

		labelSelector := fmt.Sprintf("%s=%s,%s=%s", VpcAclResourceLabel, VpcAclResourceValue, VpcNameLabel, vpc.Name)
		acls, err := vpcAclsCli.List(context.Background(), metav1.ListOptions{LabelSelector: labelSelector})
		if err != nil {
			logrus.Warningf("can not list acl in vpc: %s in k8s, err: %s", vpc.Name, err)
			continue
		}

		toCreate, toUpdate, toDelete := c.filterAcls(vpc, vpcAcls, acls)

		for _, acl := range toCreate {
			logrus.Infof("intend to create vpc acl with vpc [%s], acl-name [%s], acl-priority[%d]", vpc.Name, acl.Name, acl.Spec.Priority)
			_, err = vpcAclsCli.Create(context.Background(), &acl, metav1.CreateOptions{})
			if err != nil {
				logrus.Warningf("can not create vpc acl with vpc [%s], acl-name [%s], acl-priority[%d], with err: %s", vpc.Name, acl.Name, acl.Spec.Priority, err)
			}
		}
		for _, acl := range toUpdate {
			logrus.Infof("intend to update vpc acl with vpc [%s], acl-name [%s], acl-priority[%d]", vpc.Name, acl.Name, acl.Spec.Priority)
			_, err = vpcAclsCli.Update(context.Background(), &acl, metav1.UpdateOptions{})
			if err != nil {
				logrus.Warningf("can not update vpc acl with vpc [%s], acl-name [%s], acl-priority[%d], with err: %s", vpc.Name, acl.Name, acl.Spec.Priority, err)
			}
		}
		for _, acl := range toDelete {
			logrus.Infof("intend to delete vpc acl with vpc [%s], acl-name [%s], acl-priority[%d]", vpc.Name, acl.Name, acl.Spec.Priority)
			err = vpcAclsCli.Delete(context.Background(), acl.Name, metav1.DeleteOptions{})
			if err != nil {
				logrus.Warningf("can not delete vpc acl with vpc [%s], acl-name [%s], acl-priority[%d], with err: %s", vpc.Name, acl.Name, acl.Spec.Priority, err)
			}
		}

	}

}

func (c *ConfigmapController) filterAcls(vpc ovnapisv1.Vpc, vpcAcls []config.VpcAcl, acls *netapisv1.VpcAclList) ([]netapisv1.VpcAcl, []netapisv1.VpcAcl, []netapisv1.VpcAcl) {
	toCreate := make([]netapisv1.VpcAcl, 0)
	toUpdate := make([]netapisv1.VpcAcl, 0)
	toDelete := make([]netapisv1.VpcAcl, 0)

	for _, vpcAcl := range vpcAcls {
		found := false
		var acl netapisv1.VpcAcl
		for _, acl = range acls.Items {
			spec := acl.Spec
			if vpcAcl.Priority == spec.Priority {
				found = true
				if vpcAcl.SrcIp == spec.Src && vpcAcl.SrcPort == spec.SrcPort && vpcAcl.DestIp == spec.Dest && vpcAcl.DestPort == spec.DestPort && vpcAcl.Action == spec.Action && vpcAcl.Protocol == spec.Protocol {
					break
				}
				acl.Spec.Src = vpcAcl.SrcIp
				acl.Spec.SrcPort = vpcAcl.SrcPort
				acl.Spec.Dest = vpcAcl.DestIp
				acl.Spec.DestPort = vpcAcl.DestPort
				acl.Spec.Action = vpcAcl.Action
				acl.Spec.Protocol = vpcAcl.Protocol
				toUpdate = append(toUpdate, acl)
			}
		}
		if !found {
			name := fmt.Sprintf("acl-%s-%s", strings.TrimPrefix(vpc.Name, "vpc-"), utils.Rand8())

			ownerReference := metav1.OwnerReference{
				APIVersion:         vpc.APIVersion,
				Controller:         ptr.To[bool](true),
				BlockOwnerDeletion: ptr.To[bool](true),
				Kind:               vpc.Kind,
				Name:               vpc.Name,
				UID:                vpc.UID,
			}
			acl := netapisv1.VpcAcl{
				TypeMeta: metav1.TypeMeta{
					Kind:       "VpcAcl",
					APIVersion: netapisv1.SchemeGroupVersion.String(),
				},
				ObjectMeta: metav1.ObjectMeta{
					Name:      name,
					Namespace: VpcAclNamespace,
					Labels: map[string]string{
						VpcNameLabel:        vpc.Name,
						VpcAclResourceLabel: VpcAclResourceValue,
					},
					OwnerReferences: []metav1.OwnerReference{
						ownerReference,
					},
				},
				Spec: netapisv1.VpcAclSpec{
					Src:      vpcAcl.SrcIp,
					SrcPort:  vpcAcl.SrcPort,
					Dest:     vpcAcl.DestIp,
					DestPort: vpcAcl.DestPort,
					AclID:    name,
					Vpc:      vpc.Name,
					Action:   vpcAcl.Action,
					Protocol: vpcAcl.Protocol,
					Priority: vpcAcl.Priority,
				},
			}
			toCreate = append(toCreate, acl)
		}
	}
	for _, acl := range acls.Items {
		found := false
		var vpcAcl config.VpcAcl
		for _, vpcAcl = range vpcAcls {
			if acl.Spec.Priority == vpcAcl.Priority {
				found = true
				break
			}
		}
		if !found {
			toDelete = append(toDelete, acl)
		}
	}

	return toCreate, toUpdate, toDelete
}
