package informer

import (
	"github.com/sirupsen/logrus"
	"k8s.io/client-go/tools/cache"
	"k8s.io/klog/v2"

	v1 "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	ovninformer "github.com/kubeovn/kube-ovn/pkg/client/informers/externalversions/kubeovn/v1"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

type VpcPeeringController struct {
	Informer cache.SharedIndexInformer
}

// Run starts the process for listening for HaNatGw status changes and acting upon those changes
func (c *VpcPeeringController) Run(stop <-chan struct{}) {
	defer logrus.Errorln("Stop VpcPeeringController")

	logrus.Infoln("Start VpcPeeringController")

	// Execute go function
	c.Informer.Run(stop)

	logrus.Warn("wait for caches to sync")
	if ok := cache.WaitForCacheSync(stop, c.Informer.HasSynced); !ok {
		logrus.Fatalf("failed to wait for caches to sync")
	}

	// Wait till we receive a stop signal
	<-stop
}

func (c *VpcPeeringController) NewVpcPeeringController() {
	cs, err := utils.GetKubeovnClientSet()
	if err != nil {
		klog.Fatalln(err)
	}

	c.Informer = ovninformer.NewVpcPeeringInformer(cs, 0, nil)

	c.Informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.CreateVpcPeering,
		UpdateFunc: c.UpdateVpcPeering,
		DeleteFunc: c.DeleteVpcPeering,
	})
}

func (c *VpcPeeringController) CreateVpcPeering(obj interface{}) {
	vpcp := obj.(*v1.VpcPeering)
	logrus.Infof("create vpcp %s", vpcp.Name)
}

func (c *VpcPeeringController) UpdateVpcPeering(_old interface{}, new interface{}) {
	vpcp := new.(*v1.VpcPeering)

	logrus.Infof("update vpcp %s", vpcp.Name)
	pcc, err := dao.GetPccByName(resourceprovider.BosonProvider.Engine, vpcp.Name)
	if err != nil {
		logrus.Errorf("get pcc %s failed %s", pcc.Name, err)
		return
	}

	var isLocal bool
	switch db.AZ {
	case pcc.LocalZone:
		isLocal = true
	case pcc.RemoteZone:
		isLocal = false
	default:
		if db.Region == pcc.LocalZone {
			// XXX: test for dev44-01a
			isLocal = true
		} else {
			if db.Region == pcc.RemoteZone {
				isLocal = false
			} else {
				// Nothing to do
				logrus.Errorf("db zone %s mismatch: local zone: %s, remote zone: %s, ",
					db.AZ, pcc.LocalZone, pcc.RemoteZone)
				return
			}
		}
	}

	state := vpc.State_ACTIVE
	for _, cond := range vpcp.Status.Conditions {
		if cond.Type != "Ready" {
			state = vpc.State_CREATING
			break
		}
	}

	if isLocal {
		if pcc.LocalState != state.String() {
			pcc.LocalState = state.String()
			logrus.Infof("update pcc %s, state: %s", pcc.Name, state)
			err = dao.UpdatePCCLocalState(resourceprovider.BosonProvider.Engine, pcc)
		}
	} else {
		if pcc.RemoteState != state.String() {
			pcc.RemoteState = state.String()
			logrus.Infof("update pcc %s, state: %s", pcc.Name, state)
			err = dao.UpdatePCCRemoteState(resourceprovider.BosonProvider.Engine, pcc)
		}
	}

	if err != nil {
		logrus.Errorln(err)
		return
	}
}

func (c *VpcPeeringController) DeleteVpcPeering(obj interface{}) {
	vpcp := obj.(*v1.VpcPeering)
	logrus.Infof("delete Pcc [%s]", vpcp.Name)

	pcc, err := dao.GetPccByName(resourceprovider.BosonProvider.Engine, vpcp.Name)
	if err != nil {
		logrus.Errorf("get pcc %s failed %s", pcc.Name, err)
		return
	}

	switch db.AZ {
	case pcc.LocalZone:
		pcc.LocalState = "DELETED"
		err = dao.UpdatePCCLocalState(resourceprovider.BosonProvider.Engine, pcc)
	case pcc.RemoteZone:
		pcc.RemoteState = "DELETED"
		err = dao.UpdatePCCRemoteState(resourceprovider.BosonProvider.Engine, pcc)
	default:

		if db.Region == pcc.LocalZone {
			// XXX: test for dev44-01a
			pcc.LocalState = "DELETED"
			err = dao.UpdatePCCLocalState(resourceprovider.BosonProvider.Engine, pcc)
		} else {
			if db.Region == pcc.RemoteZone {
				pcc.RemoteState = "DELETED"
				err = dao.UpdatePCCLocalState(resourceprovider.BosonProvider.Engine, pcc)
			} else {
				// Nothing to do
				logrus.Errorf("db zone %s mismatch: local zone: %s, remote zone: %s, ",
					db.AZ, pcc.LocalZone, pcc.RemoteZone)
				return
			}
		}
	}

	if err != nil {
		logrus.Errorln(err)
		return
	}
}
