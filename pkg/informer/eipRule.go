package informer

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/util/retry"

	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netclientset "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned"
	netinformer "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/informers/externalversions/network/v1"
	eip "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/natGateway/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

// EipRuleController k8s controller
type EipRuleController struct {
	ClientSet netclientset.Interface
	Informer  cache.SharedIndexInformer
}

// Run starts the process for listening for EIP changes and acting upon those changes
func (c *EipRuleController) Run(stop <-chan struct{}) {

	defer logrus.Errorln("Stop EipRuleController")

	logrus.Infoln("Start EipRuleController")

	// Execute go function
	c.Informer.Run(stop)

	// Wait till we receive a stop signal
	<-stop
}

// sync the rule status from CR
// if there are different, update the rule status in db
// if the CR does not exist, update deleted status
func syncEipRuleCR() error {
	defer logrus.Infoln("finish eip dnat rule sync between db and cr")

	logrus.Infoln("Start eip dnat rule sync between db and cr")
	// get all the records in db, update from the status of cr
	dnatDatas, err := dao.GetAllDnatRules(resourceprovider.BosonProvider.Engine, true)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	ruleCrs, err := resourceprovider.BosonProvider.BosonNetClient.Dnats().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	makeCrState := func(crs *netv1.DnatList) *(map[string]string) {
		m := make(map[string]string)
		for _, cr := range crs.Items {
			m[cr.Name] = cr.Status.State
		}
		return &m
	}

	ruleCrsState := makeCrState(ruleCrs)
	//logrus.Infof("eip rule cr status: %v", *ruleCrsState)

	for _, dnatData := range dnatDatas {
		state, ok := (*ruleCrsState)[dnatData.Name]
		if !ok {
			state = network.DNATRule_DELETED.String()
		}
		if dnatData.State != state {
			err = dao.SetDnatRuleState(resourceprovider.BosonProvider.Engine, dnatData.DnatRuleID, state)
			if err != nil {
				logrus.Errorln(err)
				return err
			}
			logrus.Infof("sync EIP dnat rule %v status from %v to %v\n", dnatData.Name, dnatData.State, state)
			resourceprovider.BosonProvider.Processor.StateMessageSend(dnatData.ID, state)
		}
	}
	return nil
}

// EipRuleController 初始化 EIP rule controller EventHandler
func (c *EipRuleController) NewEipRuleController() {

	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalln(err)
	}

	cs, err := netclientset.NewForConfig(cfg)
	if err != nil {
		// to do
		logrus.Fatalln(err)
	}

	informer := netinformer.NewDnatInformer(cs, time.Hour*24, nil)

	c.ClientSet = cs
	c.Informer = informer

	c.Informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.createEipRule,
		UpdateFunc: c.updateEipRule,
		DeleteFunc: c.deleteEipRule,
	})
}

func (c *EipRuleController) createEipRule(obj interface{}) {

}

func (c *EipRuleController) bindEipRuleCr(name string) error {
	// update CR
	return retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
		k8sRTMetrics := exporter.InitK8sRTMetrics("GetDnat")
		drule, err := resourceprovider.BosonProvider.BosonNetClient.Dnats().Get(context.Background(), name, metav1.GetOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil {
			logrus.Errorln(err)
			return err
		}

		// annotation&labels
		if drule.ObjectMeta.Annotations == nil {
			drule.ObjectMeta.Annotations = make(map[string]string)
		}
		drule.ObjectMeta.Annotations["boson.sensetime.com/action"] = "bind"

		k8sRTMetrics = exporter.InitK8sRTMetrics("BindDnat")

		_, err = resourceprovider.BosonProvider.BosonNetClient.Dnats().Update(context.Background(), drule, metav1.UpdateOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil {
			logrus.Errorln(err)
			return err
		}
		return nil
	})
}

func (c *EipRuleController) updateEipRule(oldObj interface{}, obj interface{}) {
	o1 := oldObj.(*netv1.Dnat)
	o2 := obj.(*netv1.Dnat)

	// update the state via to the State
	if o2.Status.State != o1.Status.State {
		state := ""
		// to do, replaced with machine translate
		//State define == State define ???
		//how to do if some state change msg dropped??
		switch o2.Status.State {
		case network.DNATRule_State_name[int32(network.DNATRule_CREATING)]:
			state = network.DNATRule_State_name[int32(network.DNATRule_CREATING)]

		// CREATING-CREATED
		case network.DNATRule_State_name[int32(network.DNATRule_CREATED)]:
			state = network.DNATRule_State_name[int32(network.DNATRule_CREATED)]

		// CREATED - BINDING
		case network.DNATRule_State_name[int32(network.DNATRule_BINDING)]:
			state = network.DNATRule_State_name[int32(network.DNATRule_BINDING)]

		// BINDING - ACTIVE
		case network.DNATRule_State_name[int32(network.DNATRule_ACTIVE)]:
			state = network.DNATRule_State_name[int32(network.DNATRule_ACTIVE)]

		// ACTIVE - UNBINDING
		case network.DNATRule_State_name[int32(network.DNATRule_UNBINDING)]:
			state = network.DNATRule_State_name[int32(network.DNATRule_UNBINDING)]

		// UNBINDING - CREATED
		case network.DNATRule_State_name[int32(network.DNATRule_CREATED)]:
			state = network.DNATRule_State_name[int32(network.DNATRule_CREATED)]

		// CREATED - DELETING
		case network.DNATRule_State_name[int32(network.DNATRule_DELETING)]:
			state = network.DNATRule_State_name[int32(network.DNATRule_DELETING)]

		// DELETING - DELETED
		case network.DNATRule_State_name[int32(network.DNATRule_DELETED)]:
			state = network.DNATRule_State_name[int32(network.DNATRule_DELETED)]

		case network.DNATRule_State_name[int32(network.DNATRule_FAILED)]:
			state = network.DNATRule_State_name[int32(network.DNATRule_FAILED)]

		case network.DNATRule_State_name[int32(network.DNATRule_UPDATING)]:
			state = network.DNATRule_State_name[int32(network.DNATRule_UPDATING)]

		default:
			logrus.Errorf("Unknown EIP Dnat Rule[%v] ctrlStatus translate case from %v to %v\n", o1.Name, o1.Status.State, o2.Status.State)
		}

		if state != "" {
			logrus.Infof("Update EIP Dnat Rule[%v] status from %v to %v\n", o1.Name, o1.Status.State, o2.Status.State)
			if state == network.DNATRule_State_name[int32(network.DNATRule_DELETING)] ||
				state == network.DNATRule_State_name[int32(network.DNATRule_CREATING)] {
				return
			}

			needCleanUp := false
			if o1.Status.State == network.DNATRule_UNBINDING.String() && state == network.DNATRule_CREATED.String() {
				needCleanUp = true
			}

			if o1.Status.State == network.DNATRule_CREATING.String() && state == network.DNATRule_CREATED.String() &&
				o2.Labels != nil && o2.Labels["lepton.sensetime.com/instance-type"] != "" && o2.Spec.InnerPorts != "" &&
				(o2.Labels["lepton.sensetime.com/instance-name"] != "" || o2.Spec.InternalIP != "") {

				// update CR
				if err := c.bindEipRuleCr(o2.Name); err != nil {
					// to do, not rollback for debug trace
					logrus.Errorln(err)
					return
				}
			}

			dbRTMetrics := exporter.InitDBRTMetrics("UpdateDNATRule")

			deleted := state == network.DNATRule_State_name[int32(network.DNATRule_DELETED)]
			var err error
			if needCleanUp {
				// FIXME: multi-cluster not considered
				logrus.Infof("Clear the inner ip and inner ports for the dnat rule %s", o2.Name)
				typeName := eip.DNATRuleProperties_InstanceType_name[int32(eip.DNATRuleProperties_UNSPECIFIED)]
				_, err = resourceprovider.BosonProvider.Engine.Exec(
					`update dnat_rules set inner_ip = ?,
						inner_port_min = ?,
						inner_port_max = ?,
						internal_instance_type = ?,
						internal_instance_name = ?,
						state = ?,
						deleted = ?
					where name = ?`,
					"", 0, 0, typeName, "", state, deleted, o2.Name)
			} else {
				// FIXME: multi-cluster not considered
				logrus.Infof("Update the state to %s for the dnat rule %s", state, o2.Name)
				_, err = resourceprovider.BosonProvider.Engine.Exec("update dnat_rules set state = ?, deleted = ? where name = ?",
					state, deleted, o2.Name)
			}

			dbRTMetrics.ObserveDurationAndLog()

			if err != nil {
				logrus.Errorln(err)
				return
			}
			/*
				dbRTMetrics = exporter.InitDBRTMetrics("GetDNATRule")

				eipRuledata := db.DnatRules{}
				if _, err = resourceprovider.BosonProvider.Engine.Where("name = ?", o2.Name).Get(&eipRuledata); err != nil {
					logrus.Errorln(err)
					dbRTMetrics.ObserveDurationAndLog()
					return
				}
				dbRTMetrics.ObserveDurationAndLog()

				//resourceprovider.BosonProvider.Processor.StateMessageSend(eipRuledata.DnatRuleID, state)
			*/
		}

	}
}

func (c *EipRuleController) deleteEipRule(obj interface{}) {
	o := obj.(*netv1.Dnat)
	logrus.Infof("delete EIP Dnat Rule[%v] with status from %v \n", o.Name, o.Status.State)

	dbRTMetrics := exporter.InitDBRTMetrics("UpdateDNATRule")

	// FIXME: multi-cluster not considered
	_, err := resourceprovider.BosonProvider.Engine.Exec("update dnat_rules set state = ?, deleted = true where name = ?",
		network.DNATRule_DELETED.String(), o.Name)

	dbRTMetrics.ObserveDurationAndLog()

	if err != nil {
		logrus.Errorln(err)
		return
	}
}
