package informer

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/cache"

	kubeovnv1 "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netclientset "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned"
	netinformer "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/informers/externalversions/network/v1"
	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

// EIPController k8s controller
type EIPController struct {
	EIPClientSet netclientset.Interface
	EIPInformer  cache.SharedIndexInformer
}

// sync the eip status from CR
// if there are different, update the eip status in db
// if the CR does not exist, ignore this now
func syncEipCR() error {
	defer logrus.Infoln("finish eip sync between db and cr")

	logrus.Infoln("Start eip sync between db and cr")
	// get all the records in db, update from the status of cr
	eipDatas, err := dao.GetAllEIPs(resourceprovider.BosonProvider.Engine, true)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	eipCrs, err := resourceprovider.BosonProvider.BosonNetClient.EIPs().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logrus.Errorln(err)
		return err
	}
	makeCrState := func(crs *netv1.EIPList) *(map[string]string) {
		m := make(map[string]string)
		for _, cr := range crs.Items {
			m[cr.Name] = cr.Status.State
		}
		return &m
	}

	eipCrsState := makeCrState(eipCrs)
	//logrus.Infof("eip cr status: %v", *eipCrsState)

	for _, eipData := range eipDatas {
		state, ok := (*eipCrsState)[eipData.Name]
		if !ok {
			state = network.EIP_DELETED.String()
		}
		if eipData.State != state {
			err = dao.SetEipState(resourceprovider.BosonProvider.Engine, eipData.EIPID, state)
			if err != nil {
				logrus.Errorf("sync EIP %v err: %v", eipData.Name, err)
				return err
			}
			logrus.Infof("sync EIP %v status from %v to %v\n", eipData.Name, eipData.State, state)
			resourceprovider.BosonProvider.Processor.StateMessageSend(eipData.ID, state)
		}
	}
	return nil
}

// Run starts the process for listening for EIP changes and acting upon those changes
func (c *EIPController) Run(stopEIP <-chan struct{}) {

	defer logrus.Errorln("Stop EIPController")

	logrus.Infoln("Start EIPController")

	// Execute go function
	c.EIPInformer.Run(stopEIP)

	// Wait till we receive a stop signal
	<-stopEIP
}

// EIPController 初始化 EIP controller EventHandler
func (c *EIPController) NewEIPController() {
	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalln(err)
	}

	cs, err := netclientset.NewForConfig(cfg)
	if err != nil {
		logrus.Fatalln(err)
	}

	informer := netinformer.NewEIPInformer(cs, time.Hour*24, nil)

	c.EIPClientSet = cs
	c.EIPInformer = informer

	c.EIPInformer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.createEIP,
		UpdateFunc: c.updateEIP,
		DeleteFunc: c.deleteEIP,
	})
}

func (c *EIPController) createEIP(obj interface{}) {

}

func (c *EIPController) updateEIP(oldObj interface{}, obj interface{}) {
	o1 := oldObj.(*netv1.EIP)
	o2 := obj.(*netv1.EIP)

	if o2.Status.State != o1.Status.State {
		logrus.Infof("Update EIP %v status from %v to %v\n", o1.Name, o1.Status.State, o2.Status.State)
		if o2.Status.State != network.EIP_State_name[int32(network.EIP_CREATING)] &&
			o2.Status.State != network.EIP_State_name[int32(network.EIP_DELETING)] {

			// FIXME: multi-cluster not considered
			eipData, err := dao.GetEipByNameRetry(resourceprovider.BosonProvider.Engine, o2.Name)
			if err != nil {
				logrus.Errorln(err)
				return
			}

			err = dao.SetEipState(resourceprovider.BosonProvider.Engine, eipData.EIPID, o2.Status.State)
			if err != nil {
				logrus.Errorln(err)
				return
			}

			if o2.Status.State == network.EIP_State_name[int32(network.EIP_DELETED)] {
				err = dao.SetEipPoolAllocated(resourceprovider.BosonProvider.Engine, []string{eipData.EIPPoolID}, false)
				if err != nil {
					logrus.Errorln(err)
					return
				}
				logrus.Infof("Recycle allocated eip ip=%s success", eipData.EIPIP)

				// for internal eip, need to gc local ip and peer ip
				if eipData.InternalEIP {
					eips, err := getInternalEIPs(o2)
					if err != nil {
						logrus.Errorln(err)
						return
					}

					if len(eips) > 0 {
						err = dao.SetEipPoolAllocatedByEIPs(resourceprovider.BosonProvider.Engine, eips, false)
						if err != nil {
							logrus.Errorf("Recycle internal local and peer eip failed, eips: %v, err: %v", eips, err)
							return
						}
					}
					logrus.Infof("Recycle internal local and peer eip success, eips=%v", eips)
				}
			}

			if o1.Status.State == network.EIP_State_name[int32(network.EIP_CREATING)] && o2.Status.State == network.EIP_State_name[int32(network.EIP_ACTIVE)] {
				resourceprovider.BosonProvider.Processor.SuccessActionMessageSend(eipData.EIPID, eipData.ResourceType, o2.Annotations["action"], types.CallbackData{
					OrderID:  o2.Annotations["orderid"],
					TenantID: o2.Annotations["tenantid"],
					UserID:   o2.Annotations["userid"],
				})
			}

			resourceprovider.BosonProvider.Processor.StateMessageSend(eipData.ID, o2.Status.State)
		}
	}
}

func (c *EIPController) deleteEIP(obj interface{}) {

}

func getInternalEIPs(eipCR *netv1.EIP) ([]string, error) {
	eipInternalConfAnno := eipCR.ObjectMeta.Annotations[resourceprovider.AnnoEIPInternalConf]
	if len(eipInternalConfAnno) > 0 {
		var eipElasticNicConf *kubeovnv1.ElasticNicConfElement
		err := json.Unmarshal([]byte(eipInternalConfAnno), &eipElasticNicConf)
		if err != nil {
			err = fmt.Errorf("failed to Unmarshal eipInternalConfAnnotation, eip name=%s", eipCR.Name)
			return nil, err
		}

		eips := []string{
			strings.Split(eipElasticNicConf.LocalIp, "/")[0],
			strings.Split(eipElasticNicConf.PeerIp, "/")[0],
		}
		return eips, nil
	}

	return nil, nil
}
