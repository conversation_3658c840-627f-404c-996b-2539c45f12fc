package informer

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netclientset "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned"
	netinformer "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/informers/externalversions/network/v1"
	slbv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/slb/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/server"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/cache"
)

// SLBController k8s controller
type SLBController struct {
	SLBClientSet netclientset.Interface
	SLBInformer  cache.SharedIndexInformer
}

// SLBController 初始化 SLB controller EventHandler
func (c *SLBController) NewSLBController() {
	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalln(err)
	}

	cs, err := netclientset.NewForConfig(cfg)
	if err != nil {
		logrus.Fatalln(err)
	}

	informer := netinformer.NewSLBInformer(cs, time.Hour*24, nil)

	c.SLBClientSet = cs
	c.SLBInformer = informer

	c.SLBInformer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.createSLB,
		UpdateFunc: c.updateSLB,
		DeleteFunc: c.deleteSLB,
	})
}

// Run starts the process for listening for SLB changes and acting upon those changes
func (c *SLBController) Run(stopSLB <-chan struct{}) {
	defer logrus.Errorln("Stop SLBController")

	logrus.Infoln("Start SLBController")

	// Execute go function
	c.SLBInformer.Run(stopSLB)

	// Wait till we receive a stop signal
	<-stopSLB
}

// sync the slb status from CR
// if there are different, update the slb status in db
// if the CR does not exist, ignore this now
func syncSlbCR() error {
	defer logrus.Infoln("finish slb sync between db and cr")
	logrus.Infoln("Start slb sync between db and cr")

	engine := resourceprovider.BosonProvider.Engine

	slbRecords, err := dao.GetSlbsAll(engine, true)
	if err != nil {
		logrus.Errorf("Sync slb CR fail. get all slbs db err:%s", err)
		return err
	}

	slbCrClient := resourceprovider.BosonProvider.BosonNetClient.SLBs()
	slbCrs, err := slbCrClient.List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logrus.Errorf("Sync slb CR fail. get all slbs cr err:%s", err)
		return err
	}

	slbCrsMap := slbCrsMap(slbCrs)

	for _, slbRecord := range slbRecords {
		err := syncSlbCrOne(slbCrsMap, slbRecord)
		if err != nil {
			logrus.Errorf("Sync slb CR fail. sync slb:%s err:%s", slbRecord.Name, err)
			continue
		}
	}

	return nil
}

func (c *SLBController) createSLB(obj interface{}) {
	// nothing
}

func (c *SLBController) updateSLB(oldObj interface{}, obj interface{}) {
	oldSlb := oldObj.(*netv1.SLB)
	newSlb := obj.(*netv1.SLB)
	logrus.Info("informer update slb start", "old", oldSlb.Name, "new", newSlb.Name)
	defer logrus.Info("informer update slb end", "old", oldSlb.Name, "new", newSlb.Name)

	// update DB
	engine := resourceprovider.BosonProvider.Engine
	slbRecord, err := dao.GetSLBByName(engine, newSlb.Name, true)
	if err != nil {
		logrus.Errorf("update slb:%s status fail. get slb db err:%s", newSlb.Name, err.Error())
		return
	}

	if !c.slbStatusValid(newSlb) {
		logrus.Info(fmt.Sprintf("slb status not stable, skip this: \n%+v", newSlb))
		return
	}

	c.updateSlbInstance(slbRecord, oldSlb, newSlb)
	c.updateSlbListener(slbRecord, newSlb)
	c.updateSlbTargetGroup(slbRecord, newSlb)
}

func (c *SLBController) deleteSLB(obj interface{}) {
	// nothing
}

var slbStatusHandlers = map[slbv1.SLB_State]slbStatusHandler{
	slbv1.SLB_CREATING:       slbStatusHandlerCreating,
	slbv1.SLB_UPDATING:       slbStatusHandlerUpdating,
	slbv1.SLB_ACTIVE:         slbStatusHandlerActive,
	slbv1.SLB_DELETING:       slbStatusHandlerDeleting,
	slbv1.SLB_DELETED:        slbStatusHandlerDeleted,
	slbv1.SLB_FAILED:         slbStatusHandlerFailed,
	slbv1.SLB_EXPIRESTOPPING: slbStatusHandlerExpireStopping,
	slbv1.SLB_EXPIRESTOPPED:  slbStatusHandlerExpireStopped,
	slbv1.SLB_RENEWSTARTING:  slbStatusHandlerRenewStarting,
}

type slbStatusHandler func(c *SLBController, id string, slb *netv1.SLB)

func slbStatusHandlerCreating(c *SLBController, id string, slb *netv1.SLB) {
	resourceprovider.BosonProvider.Processor.StateMessageSend(id, slb.Status.InstanceStatus.State)
}

func slbStatusHandlerUpdating(c *SLBController, id string, slb *netv1.SLB) {
	resourceprovider.BosonProvider.Processor.StateMessageSend(id, slb.Status.InstanceStatus.State)
}

func slbStatusHandlerActive(c *SLBController, id string, slb *netv1.SLB) {
	resourceprovider.BosonProvider.Processor.StateMessageSend(id, slb.Status.InstanceStatus.State)
}

func slbStatusHandlerDeleting(c *SLBController, id string, slb *netv1.SLB) {
	resourceprovider.BosonProvider.Processor.StateMessageSend(id, slb.Status.InstanceStatus.State)
}

func slbStatusHandlerDeleted(c *SLBController, id string, slb *netv1.SLB) {
	if err := resourceprovider.SlbReleaseResource(slb.Name); err != nil {
		logrus.Errorln("SLB deleted status fail.", "Release resource error", err.Error())
	}

	resourceprovider.BosonProvider.Processor.StateMessageSend(id, slb.Status.InstanceStatus.State)
}

func slbStatusHandlerFailed(c *SLBController, id string, slb *netv1.SLB) {
	resourceprovider.BosonProvider.Processor.StateMessageSend(id, slb.Status.InstanceStatus.State)
}

func slbStatusHandlerExpireStopping(c *SLBController, id string, slb *netv1.SLB) {
	resourceprovider.BosonProvider.Processor.StateMessageSend(id, slb.Status.InstanceStatus.State)
}

func slbStatusHandlerExpireStopped(c *SLBController, id string, slb *netv1.SLB) {
	resourceprovider.BosonProvider.Processor.StateMessageSend(id, slb.Status.InstanceStatus.State)
}

func slbStatusHandlerRenewStarting(c *SLBController, id string, slb *netv1.SLB) {
	resourceprovider.BosonProvider.Processor.StateMessageSend(id, slb.Status.InstanceStatus.State)
}

func slbStateType(state string) slbv1.SLB_State {
	return slbv1.SLB_State(slbv1.SLB_State_value[state])
}

func (c *SLBController) updateSlbInstance(slbRecord *db.Slbs, oldSlb *netv1.SLB, newSlb *netv1.SLB) {
	if len(newSlb.Status.InstanceStatus.State) == 0 {
		return
	}

	if oldSlb.Status.InstanceStatus.State == newSlb.Status.InstanceStatus.State {
		return
	}

	logrus.Infof(
		"Update SLB %s status from %s to %s\n",
		oldSlb.Name, oldSlb.Status.InstanceStatus.State, newSlb.Status.InstanceStatus.State,
	)

	// status 处理
	h, ok := slbStatusHandlers[slbStateType(newSlb.Status.InstanceStatus.State)]
	if !ok {
		err := fmt.Errorf(
			"update slb:%s status fail. status:%s handler not exist\n",
			newSlb.Name, newSlb.Status.InstanceStatus.State,
		)
		logrus.Errorln(err.Error())
		return
	}
	if h != nil {
		h(c, slbRecord.ID, newSlb)
	}

	engine := resourceprovider.BosonProvider.Engine
	err := dao.SetSLBState(engine, slbRecord.SlbID, newSlb.Status.InstanceStatus.State)
	if err != nil {
		logrus.Errorf("update slb:%s status fail. set slb state err:%s", newSlb.Name, err.Error())
		return
	}
	logrus.Infof(
		"Update SLB %s status from %s to %s success\n",
		oldSlb.Name, oldSlb.Status.InstanceStatus.State, newSlb.Status.InstanceStatus.State,
	)
}

func (c *SLBController) updateSlbListener(slbRecord *db.Slbs, newSlb *netv1.SLB) {
	engine := resourceprovider.BosonProvider.Engine
	listenersRecord, err := dao.GetSLBListenersBySlbID(engine, slbRecord.SlbID, false)
	if err != nil {
		logrus.Errorf("update slb:%s listeners status fail. get listener list err:%s", slbRecord.SlbID, err)
		return
	}

	if len(listenersRecord) == 0 {
		return
	}

	statusMap := generateListenerStatusMap(newSlb)

	updatedRecords := []db.SlbListeners{}
	newStateFn := func(statusMap map[string]string, record *db.SlbListeners) string {
		state := ""
		listenerState, ok := statusMap[record.ListenerID]
		if !ok {
			if record.State == listenerStateName(slbv1.Listener_DELETING) {
				state = listenerStateName(slbv1.Listener_DELETED)
			} else {
				state = record.State
			}
		} else {
			state = listenerState
			if record.State == listenerStateName(slbv1.Listener_STOPPING) {
				if state != listenerStateName(slbv1.Listener_FAILED) &&
					state != listenerStateName(slbv1.Listener_STOPPED) {
					// 从 stopping 只能切换到 fail 或者 stopped，其余的状态认为是临时中间态，不care
					state = listenerStateName(slbv1.Listener_STOPPING)
				}
			}

			if record.State == listenerStateName(slbv1.Listener_STARTING) {
				if state != listenerStateName(slbv1.Listener_FAILED) &&
					state != listenerStateName(slbv1.Listener_ACTIVE) {
					// 从 starting 只能切换到 fail 或者 active，其余的状态认为是临时中间态，不care
					state = listenerStateName(slbv1.Listener_STARTING)
				}
			}
		}
		return state
	}
	for _, record := range listenersRecord {
		state := newStateFn(statusMap, record)
		if state == "" {
			logrus.Infof("update slb:%s listener:%s status skip. new status is nil\n", newSlb.Name, record.Name)
			return
		}

		if record.State == state {
			continue
		}

		logrus.Infof(
			"Update SLB %s listener:%s status from %s to %s\n",
			newSlb.Name, record.Name, record.State, state,
		)

		recordNew := *record
		recordNew.State = state
		updatedRecords = append(updatedRecords, recordNew)

		// 处理状态
		h, ok := listenerStatusHandlers[listenerStateType(recordNew.State)]
		if !ok {
			logrus.Infof("Update SLB %s listener:%s status skip, handler is nil\n", newSlb.Name, record.Name)
			continue
		}
		if h != nil {
			h(c, newSlb, slbRecord, recordNew.ListenerID)
		}
	}

	if len(updatedRecords) == 0 {
		return
	}

	// fixme 批处理
	for _, record := range updatedRecords {
		err := dao.SetListenerState(engine, record.SlbID, record.ListenerID, record.State)
		if err != nil {
			logrus.Errorf(
				"Update SLB %s listener:%s status fail. set state:%s err:%s",
				record.SlbID, record.ListenerID, record.State, err,
			)
			continue
		}
		logrus.Infof("SLB %s listener:%s change to %s", record.SlbID, record.ListenerID, record.State)
	}
}

func (c *SLBController) updateSlbTargetGroup(slbRecord *db.Slbs, newSlb *netv1.SLB) {
	engine := resourceprovider.BosonProvider.Engine
	targetGroupsRecord, err := dao.GetSLBTargetGroupsBySlbID(engine, slbRecord.SlbID, false)
	if err != nil {
		logrus.Errorf("update slb:%s targetGroup status fail. get targetGroup list err:%s", slbRecord.SlbID, err)
		return
	}

	if len(targetGroupsRecord) == 0 {
		return
	}

	statusMap := generateTargetGroupStatusMap(newSlb)
	for _, record := range targetGroupsRecord {
		// .1 targets
		c.updateSlbTarget(record, statusMap, slbRecord, newSlb)

		// .2 targetGroups
		// 先处理子资源，再处理自己
		c.updateSlbTargetGroupOne(record, statusMap, slbRecord, newSlb)
	}
}

func (c *SLBController) slbStatusValid(newSlb *netv1.SLB) bool {
	if newSlb == nil {
		return false
	}

	// listeners
	{
		specListenerMap := make(map[string]bool)
		statusListenerMap := make(map[string]bool)
		for _, specListener := range newSlb.Spec.Listeners {
			specListenerMap[specListener.ListenerID] = true
		}
		for _, statusListener := range newSlb.Status.ListenerStatus {
			statusListenerMap[statusListener.Spec.ListenerID] = true
		}
		if !mapKeyEqual(specListenerMap, statusListenerMap) {
			return false
		}
	}

	// targetGroups
	specTgMapForT := make(map[string]*netv1.TargetGroup)
	statusTgMapForT := make(map[string]*netv1.TargetGroupStatus)
	{
		specTgMap := make(map[string]bool)
		statusTgMap := make(map[string]bool)
		for i, specTg := range newSlb.Spec.TargetGroups {
			specTgMapForT[specTg.TargetGroupID] = &newSlb.Spec.TargetGroups[i]
			specTgMap[specTg.TargetGroupID] = true
		}
		for i, statusTg := range newSlb.Status.TargetGroupStatus {
			statusTgMapForT[statusTg.Spec.TargetGroupID] = &newSlb.Status.TargetGroupStatus[i]
			statusTgMap[statusTg.Spec.TargetGroupID] = true
		}
		if !mapKeyEqual(specTgMap, statusTgMap) {
			return false
		}
	}

	// targets
	{
		for _, specTg := range newSlb.Spec.TargetGroups {
			statusTg := statusTgMapForT[specTg.TargetGroupID]

			specTargetMap := make(map[string]bool)
			statusTargetTMap := make(map[string]bool)
			for _, specT := range specTg.Targets {
				if specT != nil {
					specTargetMap[specT.TargetID] = true
				}
			}
			for _, statusT := range statusTg.TargetStatus {
				statusTargetTMap[statusT.Spec.TargetID] = true
			}

			if !mapKeyEqual(specTargetMap, statusTargetTMap) {
				return false
			}
		}
	}

	return true
}

func mapKeyEqual(m1 map[string]bool, m2 map[string]bool) bool {
	if len(m1) != len(m2) {
		return false
	}

	for k := range m1 {
		if _, ok := m2[k]; !ok {
			return false
		}
	}

	return true
}

func (c *SLBController) updateSlbTarget(
	targetGroupRecord *db.SlbTargetGroups, targetGroupStatusMap map[string]*netv1.TargetGroupStatus,
	slbRecord *db.Slbs, newSlb *netv1.SLB,
) {
	engine := resourceprovider.BosonProvider.Engine
	records, err := dao.GetSLBTargetsByTargetGroupID(
		engine, targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID,
	)
	if err != nil {
		logrus.Errorf(
			"update slb:%s targetGroup:%s targets status fail. get target list err:%s",
			targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, err,
		)
	}

	if len(records) == 0 {
		logrus.Infof(
			"update slb:%s targetGroup:%s targets status skip. get target list is nil",
			targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID,
		)
		return
	}

	statusSlice := []netv1.TargetStatus{}
	_, ok := targetGroupStatusMap[targetGroupRecord.TargetGroupID]
	if ok &&
		targetGroupStatusMap[targetGroupRecord.TargetGroupID] != nil &&
		targetGroupStatusMap[targetGroupRecord.TargetGroupID].TargetStatus != nil {
		statusSlice = targetGroupStatusMap[targetGroupRecord.TargetGroupID].TargetStatus
	}

	statusMap := generateTargetStatusMap(statusSlice)
	newStateFn := func(statusMap map[string]*netv1.TargetStatus, record *db.SlbTargets) string {
		state := ""
		targetStatus, ok := statusMap[record.TargetID]
		if !ok {
			if record.State == targetStateName(slbv1.Target_REMOVING) {
				state = targetStateName(slbv1.Target_REMOVED)
			} else {
				// 删除 targetGroup 的时候联动处理
				targetGroupStatus, ok := targetGroupStatusMap[targetGroupRecord.TargetGroupID]
				if !ok || targetGroupStatus == nil {
					// 说明 targetGroup 已经删除，那么 target 也设置为 Target_REMOVED
					state = targetStateName(slbv1.Target_REMOVED)
				} else {
					state = record.State
				}
			}
		} else {
			state = targetStatus.State
		}
		return state
	}
	for _, record := range records {
		state := newStateFn(statusMap, record)
		if state == "" {
			logrus.Infof(
				"update slb:%s targetGroup:%s target:%s status skip. new status is nil\n",
				targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, record.TargetID,
			)
			continue
		}

		if state == record.State {
			continue
		}

		logrus.Infof(
			"update slb:%s targetGroup:%s target:%s status from %s to %s\n",
			targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, record.TargetID,
			record.State, state,
		)

		// handler
		h, ok := targetStatusHandlers[targetStateType(state)]
		if !ok {
			logrus.Infof(
				"update slb:%s targetGroup:%s target:%s handler skip. state:%s handler is nil\n",
				targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, record.TargetID, state,
			)
			continue
		}
		if h != nil {
			h(c, newSlb, slbRecord, targetGroupRecord, record.TargetID)
		}

		err := dao.SetTargetState(engine, record.SlbID, record.TargetGroupID, record.TargetID, state)
		if err != nil {
			logrus.Errorf(
				"update slb:%s targetGroup:%s target:%s status skip. set state:%s err:%s\n",
				targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, record.TargetID,
				state, err,
			)
			return
		}
	}
}

var listenerStatusHandlers = map[slbv1.Listener_State]listenerStatusHandler{
	slbv1.Listener_CREATING: nil, // nothing
	slbv1.Listener_UPDATING: nil, // nothing
	slbv1.Listener_ACTIVE:   nil, // nothing
	slbv1.Listener_DELETING: nil, // nothing
	slbv1.Listener_DELETED:  listenerStatusHandlerDeleted,
	slbv1.Listener_FAILED:   nil, // nothing
	slbv1.Listener_STOPPING: nil, // nothing
	slbv1.Listener_STOPPED:  nil, // nothing
	slbv1.Listener_STARTING: nil, // nothing
}

type listenerStatusHandler func(c *SLBController, slb *netv1.SLB, slbRecord *db.Slbs, listenerID string)

func listenerStatusHandlerDeleted(c *SLBController, slb *netv1.SLB, slbRecord *db.Slbs, listenerID string) {
	err := server.ReleaseListener(slbRecord, listenerID)
	if err != nil {
		logrus.Errorf("Slb:%s listener:%s deleted fail. release listener err:%s", slbRecord.SlbID, listenerID, err)
		return
	}
}

func listenerStateType(state string) slbv1.Listener_State {
	return slbv1.Listener_State(slbv1.Listener_State_value[state])
}

func listenerStateName(state slbv1.Listener_State) string {
	return slbv1.Listener_State_name[int32(state)]
}

var targetGroupStatusHandlers = map[slbv1.TargetGroup_State]targetGroupStatusHandler{
	slbv1.TargetGroup_CREATING: nil, // nothing
	slbv1.TargetGroup_UPDATING: nil, // nothing
	slbv1.TargetGroup_ACTIVE:   nil, // nothing
	slbv1.TargetGroup_DELETING: nil, // nothing
	slbv1.TargetGroup_DELETED:  targetGroupStatusHandlerDeleted,
	slbv1.TargetGroup_FAILED:   nil, // nothing
}

type targetGroupStatusHandler func(c *SLBController, slb *netv1.SLB, slbRecord *db.Slbs, targetGroupID string)

func targetGroupStatusHandlerDeleted(c *SLBController, slb *netv1.SLB, slbRecord *db.Slbs, targetGroupID string) {
	err := server.ReleaseTargetGroup(slbRecord, targetGroupID)
	if err != nil {
		logrus.Errorf("Slb:%s targetGroup:%s deleted fail. release targetGroup err:%s", slbRecord.SlbID, targetGroupID, err)
		return
	}
}

func targetGroupStateType(state string) slbv1.TargetGroup_State {
	return slbv1.TargetGroup_State(slbv1.TargetGroup_State_value[state])
}

func targetGroupStateName(state slbv1.TargetGroup_State) string {
	return slbv1.TargetGroup_State_name[int32(state)]
}

func (c *SLBController) updateSlbTargetGroupOne(
	record *db.SlbTargetGroups, targetGroupStatusMap map[string]*netv1.TargetGroupStatus,
	slbRecord *db.Slbs, newSlb *netv1.SLB,
) {
	newStateFn := func(statusMap map[string]*netv1.TargetGroupStatus, record *db.SlbTargetGroups) string {
		state := ""
		listenerStatus, ok := statusMap[record.TargetGroupID]
		if !ok {
			if record.State == targetGroupStateName(slbv1.TargetGroup_DELETING) {
				state = targetGroupStateName(slbv1.TargetGroup_DELETED)
			} else {
				state = record.State
			}
		} else {
			if listenerStatus == nil {
				state = targetGroupStateName(slbv1.TargetGroup_DELETED)
			} else {
				state = listenerStatus.State
			}
		}
		return state
	}

	state := newStateFn(targetGroupStatusMap, record)

	if state == "" {
		logrus.Infof(
			"update slb:%s targetGroup:%s status skip. new status is nil\n", newSlb.Name, record.Name,
		)
		return
	}

	if state == record.State {
		return
	}

	logrus.Infof(
		"Update SLB %s targetGroup:%s status from %s to %s\n",
		newSlb.Name, record.Name, record.State, state,
	)

	// 处理状态
	h, ok := targetGroupStatusHandlers[targetGroupStateType(state)]
	if !ok {
		logrus.Infof("Update SLB %s targetGroup:%s status skip, handler is nil\n", newSlb.Name, record.Name)
		return
	}
	if h != nil {
		h(c, newSlb, slbRecord, record.TargetGroupID)
	}

	engine := resourceprovider.BosonProvider.Engine
	err := dao.SetTargetGroupState(engine, record.SlbID, record.TargetGroupID, state)
	if err != nil {
		logrus.Errorf(
			"Update SLB %s targetGroup:%s status fail. set state:%s err:%s",
			record.SlbID, record.TargetGroupID, record.State, err,
		)
		return
	}
	logrus.Infof(
		"Update SLB %s targetGroup:%s status from %s to %s success\n",
		newSlb.Name, record.Name, record.State, state,
	)
}

var targetStatusHandlers = map[slbv1.Target_State]targetStatusHandler{
	slbv1.Target_ADDING:   nil,
	slbv1.Target_UPDATING: nil,
	slbv1.Target_ACTIVE:   nil,
	slbv1.Target_REMOVING: nil,
	slbv1.Target_REMOVED:  targetStatusHandlerRemoved,
	slbv1.Target_FAILED:   nil,
}

type targetStatusHandler func(c *SLBController, slb *netv1.SLB, slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups, targetID string)

func targetStateType(state string) slbv1.Target_State {
	return slbv1.Target_State(slbv1.Target_State_value[state])
}

func targetStateName(state slbv1.Target_State) string {
	return slbv1.Target_State_name[int32(state)]
}

func targetStatusHandlerRemoved(
	c *SLBController, slb *netv1.SLB, slbRecord *db.Slbs, targetGroupRecord *db.SlbTargetGroups, targetID string,
) {
	err := server.ReleaseTarget(slbRecord, targetGroupRecord, targetID)
	if err != nil {
		logrus.Errorf(
			"Slb:%s targetGroup:%s target:%s removed fail. release target err:%s",
			targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, targetID, err,
		)
		return
	}
}

func syncSlbCrOne(crsMap map[string]*netv1.SLB, slbRecord *db.Slbs) error {
	state := slbv1.SLB_DELETED.String()
	_, ok := crsMap[slbRecord.Name]
	if ok {
		state = crsMap[slbRecord.Name].Status.InstanceStatus.State
	}

	// SLB
	if slbRecord.State != state {
		engine := resourceprovider.BosonProvider.Engine
		if state == slbv1.SLB_DELETED.String() {
			err := dao.DeleteSLB(engine, slbRecord.SlbID, state)
			if err != nil {
				logrus.Errorf("Sync slb fail. delete slb:%s state:%s error:%s", slbRecord.Name, state, err)
				return err
			}
		} else {
			err := dao.SetSLBState(engine, slbRecord.SlbID, state)
			if err != nil {
				logrus.Errorf("Sync slb fail. set slb:%s state:%s error:%s", slbRecord.Name, state, err)
				return err
			}
		}

		logrus.Infof("Sync slb:%s state from %s to %s", slbRecord.Name, slbRecord.State, state)
		resourceprovider.BosonProvider.Processor.StateMessageSend(slbRecord.ID, state)
	}

	if !ok {
		return nil
	}

	// 监听器
	err := syncSlbListener(crsMap[slbRecord.Name], slbRecord)
	if err != nil {
		logrus.Errorf("Sync slb fail. sync slb:%s listeners error:%s", slbRecord.Name, err)
	}

	// 后端组&后端
	err = syncSlbTargetGroup(crsMap[slbRecord.Name], slbRecord)
	if err != nil {
		logrus.Errorf("Sync slb fail. sync slb:%s target groups error:%s", slbRecord.Name, err)
	}

	return nil
}

func syncSlbListener(slbCR *netv1.SLB, slbRecord *db.Slbs) error {
	listenersMap := slbCrListenersMap(slbCR)
	engine := resourceprovider.BosonProvider.Engine
	listenerRecords, err := dao.GetSLBListenersBySlbID(engine, slbRecord.SlbID, true)
	if err != nil {
		logrus.Errorf("Sync slb listener fail. get slb:%s listeners error:%s", slbRecord.Name, err)
		return err
	}

	for _, listenerRecord := range listenerRecords {
		state := slbv1.Listener_DELETED.String()
		stateCr, ok := listenersMap[listenerRecord.ListenerID]
		if ok {
			state = stateCr
		}
		if listenerRecord.State == state {
			continue
		}

		err := dao.SetListenerState(engine, slbRecord.SlbID, listenerRecord.ListenerID, state)
		if err != nil {
			logrus.Errorf(
				"Sync slb listener fail. set slb:%s listener:%s state:%s error:%s",
				slbRecord.Name, listenerRecord.ListenerID, state, err,
			)
			continue
		}
		logrus.Infof(
			"Sync slb:%s listener:%s state from %s to %s",
			slbRecord.Name, listenerRecord.ListenerID, listenerRecord.State, state,
		)
	}

	return nil
}

func syncSlbTargetGroup(slbCR *netv1.SLB, slbRecord *db.Slbs) error {
	targetGroupsCrMap := slbCrTargetGroupsMap(slbCR)
	engine := resourceprovider.BosonProvider.Engine
	targetGroupRecords, err := dao.GetSLBTargetGroupsBySlbID(engine, slbRecord.SlbID, true)
	if err != nil {
		logrus.Errorf("Sync slb targetGroup fail. get slb:%s  error:%s", slbRecord.Name, err)
		return err
	}

	for _, targetGroupRecord := range targetGroupRecords {
		err = syncSlbTargetGroupOne(targetGroupsCrMap, targetGroupRecord)
		if err != nil {
			logrus.Errorf(
				"Sync slb targetGroup fail. sync slb:%s  targetGroup:%s error:%s",
				slbRecord.Name, targetGroupRecord.Name, err,
			)
			continue
		}
	}

	return nil
}

func syncSlbTargetGroupOne(
	targetGroupsCrMap map[string]*netv1.TargetGroupStatus, targetGroupRecord *db.SlbTargetGroups,
) error {
	state := slbv1.TargetGroup_DELETED.String()
	_, ok := targetGroupsCrMap[targetGroupRecord.TargetGroupID]
	if ok {
		state = targetGroupsCrMap[targetGroupRecord.TargetGroupID].State
	}

	// 后端组
	if targetGroupRecord.State != state {
		engine := resourceprovider.BosonProvider.Engine
		err := dao.SetTargetGroupState(engine, targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, state)
		if err != nil {
			logrus.Errorf(
				"Sync slb fail. set slb:%s targetGroup:%s state:%s error:%s",
				targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, state, err,
			)
			return err
		}
		logrus.Infof(
			"Sync slb:%s targetGroup:%s state form %s to %s",
			targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, targetGroupRecord.State, state,
		)
	}

	if !ok {
		return nil
	}

	// 后端
	err := syncSlbTargets(targetGroupsCrMap[targetGroupRecord.TargetGroupID], targetGroupRecord)
	if err != nil {
		logrus.Errorf(
			"Sync slb fail. sync slb:%s targetGroup:%s targets error:%s",
			targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, err,
		)
		return err
	}

	return nil
}

func syncSlbTargets(targetGroupStatus *netv1.TargetGroupStatus, targetGroupRecord *db.SlbTargetGroups) error {
	targetsMap := slbCrTargetsMap(targetGroupStatus)
	engine := resourceprovider.BosonProvider.Engine
	targetRecords, err := dao.GetSLBTargetsByTargetGroupID(
		engine, targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID,
	)
	if err != nil {
		logrus.Errorf(
			"Sync slb targets fail. get slb:%s targetGroups:%s targets err:%s",
			targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, err,
		)
		return err
	}

	for _, targetRecord := range targetRecords {
		state := slbv1.Target_REMOVED.String()
		stateCr, ok := targetsMap[targetRecord.TargetID]
		if ok {
			state = stateCr
		}

		if targetRecord.State == state {
			continue
		}

		err = dao.SetTargetState(
			engine, targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, targetRecord.TargetID, state,
		)
		if err != nil {
			logrus.Errorf(
				"Sync slb targets fail. set slb:%s targetGroups:%s target:%s state:%s err:%s",
				targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, targetRecord.TargetID, state, err,
			)
			continue
		}
		logrus.Infof(
			"Sync slb:%s targetGroups:%s target:%s state from %s to %s",
			targetGroupRecord.SlbID, targetGroupRecord.TargetGroupID, targetRecord.TargetID, targetRecord.State, state,
		)
	}

	return nil
}

// key:slbName
func slbCrsMap(crs *netv1.SLBList) map[string]*netv1.SLB {
	crsMap := make(map[string]*netv1.SLB)
	for index := range crs.Items {
		crsMap[crs.Items[index].Name] = &crs.Items[index]
	}

	return crsMap
}

// key:listenerID, value:state
func slbCrListenersMap(slbCR *netv1.SLB) map[string]string {
	listenersMap := make(map[string]string)
	if slbCR.Status.ListenerStatus == nil {
		return listenersMap
	}
	for _, listenerStatus := range slbCR.Status.ListenerStatus {
		listenersMap[listenerStatus.Spec.ListenerID] = listenerStatus.State
	}
	return listenersMap
}

// key:targetGroupID
func slbCrTargetGroupsMap(slbCR *netv1.SLB) map[string]*netv1.TargetGroupStatus {
	targetGroupsMap := make(map[string]*netv1.TargetGroupStatus)
	if slbCR.Status.TargetGroupStatus == nil {
		return targetGroupsMap
	}
	for index, targetGroupStatus := range slbCR.Status.TargetGroupStatus {
		targetGroupsMap[targetGroupStatus.Spec.TargetGroupID] = &slbCR.Status.TargetGroupStatus[index]
	}
	return targetGroupsMap
}

// key:targetID, values:state
func slbCrTargetsMap(targetGroupStatus *netv1.TargetGroupStatus) map[string]string {
	targetsMap := make(map[string]string)
	if targetGroupStatus.TargetStatus == nil {
		return targetsMap
	}
	for _, targetStatus := range targetGroupStatus.TargetStatus {
		targetsMap[targetStatus.Spec.TargetID] = targetStatus.State
	}
	return targetsMap
}

func generateListenerStatusMap(newSlb *netv1.SLB) map[string]string {
	statusMap := make(map[string]string)
	specMap := make(map[string]bool)

	if newSlb.Spec.Listeners != nil {
		for _, spec := range newSlb.Spec.Listeners {
			specMap[spec.ListenerID] = true
		}
	}

	if newSlb.Status.ListenerStatus != nil {
		for _, status := range newSlb.Status.ListenerStatus {
			if _, ok := specMap[status.Spec.ListenerID]; ok {
				statusMap[status.Spec.ListenerID] = status.State
			} else {
				statusMap[status.Spec.ListenerID] = listenerStateName(slbv1.Listener_DELETED)
			}
		}
	}

	return statusMap
}

func generateTargetGroupStatusMap(newSlb *netv1.SLB) map[string]*netv1.TargetGroupStatus {
	statusMap := make(map[string]*netv1.TargetGroupStatus)
	specMap := make(map[string]bool)

	if newSlb.Spec.TargetGroups != nil {
		for _, spec := range newSlb.Spec.TargetGroups {
			specMap[spec.TargetGroupID] = true
		}
	}

	if newSlb.Status.TargetGroupStatus != nil {
		for index, status := range newSlb.Status.TargetGroupStatus {
			if _, ok := specMap[status.Spec.TargetGroupID]; ok {
				statusMap[status.Spec.TargetGroupID] = &newSlb.Status.TargetGroupStatus[index]
			} else {
				statusMap[status.Spec.TargetGroupID] = nil
			}
		}
	}

	return statusMap
}

func generateTargetStatusMap(statusSlice []netv1.TargetStatus) map[string]*netv1.TargetStatus {
	statusMap := make(map[string]*netv1.TargetStatus)
	if statusSlice == nil {
		return statusMap
	}

	for index, status := range statusSlice {
		statusMap[status.Spec.TargetID] = &statusSlice[index]
	}
	return statusMap
}
