package informer

import (
	"context"
	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/server"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/util/retry"
	"reflect"
	"time"

	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netclientset "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned"
	netinformer "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/informers/externalversions/network/v1"
	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/dc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

// DCVCController k8s controller
type DCVCController struct {
	DCVCClientSet netclientset.Interface
	DCVCInformer  cache.SharedIndexInformer
}

// sync the dcvc status from CR
// if there are different, update the dcvc status in db
// if the CR does not exist, ignore this now
func syncDcvcCR() error {
	defer logrus.Infoln("finish dcvc sync between db and cr")

	logrus.Infoln("Start dcvc sync between db and cr")
	// get all the records in db, update from the status of cr
	dcvcDatas, err := dao.GetAllDcvcs(resourceprovider.BosonProvider.Engine, true)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	dcvcCrs, err := resourceprovider.BosonProvider.BosonNetClient.DcVcs().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logrus.Errorln(err)
		return err
	}
	makeCrState := func(crs *netv1.DcVcList) *(map[string]string) {
		m := make(map[string]string)
		for _, cr := range crs.Items {
			m[cr.Name] = cr.Status.State
		}
		return &m
	}

	dcvcCrsState := makeCrState(dcvcCrs)
	//logrus.Infof("dcvc cr status: %v", *dcvcCrsState)

	for _, dcvcData := range dcvcDatas {
		state, ok := (*dcvcCrsState)[dcvcData.Name]
		if !ok {
			state = network.DCVC_DELETED.String()
		}
		if dcvcData.State != state {
			err = dao.SetDcvcState(resourceprovider.BosonProvider.Engine, dcvcData.Name, state)
			if err != nil {
				logrus.Errorf("sync dcvc %v err: %v", dcvcData.Name, err)
				return err
			}
			logrus.Infof("sync dcvc %v status from %v to %v\n", dcvcData.Name, dcvcData.State, state)
		}
	}
	return nil
}

// Run starts the process for listening for DCVC changes and acting upon those changes
func (c *DCVCController) Run(stopDCVC <-chan struct{}) {

	defer logrus.Errorln("Stop DCVCController")

	logrus.Infoln("Start DCVCController")

	// Execute go function
	c.DCVCInformer.Run(stopDCVC)

	// Wait till we receive a stop signal
	<-stopDCVC
}

// DCVC controller EventHandler
func (c *DCVCController) NewDCVCController() {
	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalln(err)
	}

	cs, err := netclientset.NewForConfig(cfg)
	if err != nil {
		logrus.Fatalln(err)
	}

	informer := netinformer.NewDcVcInformer(cs, time.Hour*24, nil)

	c.DCVCClientSet = cs
	c.DCVCInformer = informer

	c.DCVCInformer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.createDCVC,
		UpdateFunc: c.updateDCVC,
		DeleteFunc: c.deleteDCVC,
	})
}

func (c *DCVCController) createDCVC(obj interface{}) {

}

func (c *DCVCController) updateDCVC(oldObj interface{}, obj interface{}) {
	o1 := oldObj.(*netv1.DcVc)
	o2 := obj.(*netv1.DcVc)

	if o2.Status.State != o1.Status.State {
		logrus.Infof("Update dcvc %v status from %v to %v\n", o1.Name, o1.Status.State, o2.Status.State)
		if o2.Status.State != network.DCVC_State_name[int32(network.DCVC_CREATING)] &&
			o2.Status.State != network.DCVC_State_name[int32(network.DCVC_DELETING)] {

			var dcvcData *db.Dcvcs
			if err := retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
				dcvcData, err = dao.GetDcvcByName(resourceprovider.BosonProvider.Engine, o2.Name)
				if err != nil {
					return err
				}
				return nil
			}); err != nil {
				logrus.Errorln(err)
				return
			}

			if err := dao.SetDcvcState(resourceprovider.BosonProvider.Engine, dcvcData.Name, o2.Status.State); err != nil {
				logrus.Errorln(err)
				return
			}
		}
	}

	if !reflect.DeepEqual(o1.Status.HealthStatus, o2.Status.HealthStatus) {
		if !o1.Status.HealthStatus.RunTime.Time.Before(o2.Status.HealthStatus.RunTime.Time) {
			logrus.Infof("Update dcvc %v healthStatus from %v to %v suppressed\n", o1.Name, o1.Status.HealthStatus, o2.Status.HealthStatus)
			return
		}
		logrus.Infof("Update dcvc %v healthStatus from %v to %v\n", o1.Name, o1.Status.HealthStatus, o2.Status.HealthStatus)
		var dcvcData *db.Dcvcs
		if err := retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
			dcvcData, err = dao.GetDcvcByName(resourceprovider.BosonProvider.Engine, o2.Name)
			if err != nil {
				return err
			}
			return nil
		}); err != nil {
			logrus.Errorln(err)
			return
		}
		if err := dao.SetDchcRunResult(resourceprovider.BosonProvider.Engine, o2.Name, o2.Status.HealthStatus.RunStatus, o2.Status.HealthStatus.RunTime.Time); err != nil {
			logrus.Errorln(err)
			return
		}

		if o1.Status.HealthStatus.RunStatus == server.FAIL && o2.Status.HealthStatus.RunStatus == server.SUCCESS {
			logrus.Infof("Notice dcvc %v healthStatus from %v to %v\n", o1.Name, o1.Status.HealthStatus, o2.Status.HealthStatus)
			resourceprovider.BosonProvider.Processor.DcvcNoticeMessageSend("DC", "FAIL_2_SUCCESS", dcvcData.Name, dcvcData.DisplayName, dcvcData.TenantID, []string{dcvcData.OwnerID})
		}
		if o1.Status.HealthStatus.RunStatus == server.SUCCESS && o2.Status.HealthStatus.RunStatus == server.FAIL {
			logrus.Infof("Notice dcvc %v healthStatus from %v to %v\n", o1.Name, o1.Status.HealthStatus, o2.Status.HealthStatus)
			resourceprovider.BosonProvider.Processor.DcvcNoticeMessageSend("DC", "SUCCESS_2_FAIL", dcvcData.Name, dcvcData.DisplayName, dcvcData.TenantID, []string{dcvcData.OwnerID})
		}
	}
}

func (c *DCVCController) deleteDCVC(obj interface{}) {

}
