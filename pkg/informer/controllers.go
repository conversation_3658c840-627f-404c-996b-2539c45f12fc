package informer

import (
	"sync"

	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
)

var statusSync sync.Once

func Controllers(stopCh <-chan struct{}) {
	// rdma node controller no need leader election
	go rdmaNodeControllers(stopCh)

	resourceprovider.BosonProvider.LeaderElection()
	go statusSync.Do(statusSyncCR)
	go vpcControllers(stopCh)
	go dgwControllers(stopCh)
	go eipControllers(stopCh)
	go dnatRuleControllers(stopCh)
	go aclControllers(stopCh)
	go vpcAclControllers(stopCh)
	go ipaControllers(stopCh)
	if resourceprovider.BosonProvider.RpConfig.Boson.SlbEnable {
		go slbControllers(stopCh)
	}
	go haNatGwControllers(stopCh)
	go VpcPeeringControllers(stopCh)
	go dcvcControllers(stopCh)
	go bosonServiceControllers(stopCh)
	go configmapControllers(stopCh)
}

func statusSyncCR() {
	if err := syncIpaCR(); err != nil {
		logrus.Errorf("Sync the ipa status between provider and controller err[%v]", err)
	}
	if err := syncEipCR(); err != nil {
		logrus.Errorf("sync the eip status between provider and controller err[%v]", err)
	}
	if err := syncEipRuleCR(); err != nil {
		logrus.Errorf("Sync the eip dnat rules status between provider and controller err[%v]", err)
	}
	if err := syncAclCR(); err != nil {
		logrus.Errorf("Sync the eip acl status between provider and controller err[%v]", err)
	}
	if err := syncVpcAclCR(); err != nil {
		logrus.Errorf("Sync the vpc acl status between provider and controller err[%v]", err)
	}
	if err := syncDgwCR(); err != nil {
		logrus.Errorf("Sync the dgw status between provider and controller err[%v]", err)
	}
	if resourceprovider.BosonProvider.RpConfig.Boson.SlbEnable {
		if err := syncSlbCR(); err != nil {
			logrus.Errorf("Sync the slb status between provider and controller err[%v]", err)
		}
	}
	if err := syncBosonServiceCR(); err != nil {
		logrus.Errorf("Sync the boson service status between provider and controller err[%v]", err)
	}
	if err := syncDcvcCR(); err != nil {
		logrus.Errorf("sync the dcvc status between provider and controller err[%v]", err)
	}
}

func vpcControllers(stopCh <-chan struct{}) {
	vpcController := VPCController{}
	vpcController.NewVPCController()
	vpcController.Run(stopCh)
}

func dgwControllers(stopCh <-chan struct{}) {
	dgwController := DgwController{}
	dgwController.NewDgwController()
	dgwController.Run(stopCh)
}

func eipControllers(stopCh <-chan struct{}) {
	eipController := EIPController{}
	eipController.NewEIPController()
	eipController.Run(stopCh)
}

func dnatRuleControllers(stopCh <-chan struct{}) {
	dnatRuleController := EipRuleController{}
	dnatRuleController.NewEipRuleController()
	dnatRuleController.Run(stopCh)
}

func ipaControllers(stopCh <-chan struct{}) {
	ipaController := IpaController{}
	ipaController.NewIpaController()
	ipaController.Run(stopCh)
}

func aclControllers(stopCh <-chan struct{}) {
	aclController := ACLController{}
	aclController.NewACLController()
	aclController.Run(stopCh)
}

func vpcAclControllers(stopCh <-chan struct{}) {
	vpcAclController := VpcAclController{}
	vpcAclController.NewVpcAclController()
	vpcAclController.Run(stopCh)
}

func slbControllers(stopCh <-chan struct{}) {
	slbController := SLBController{}
	slbController.NewSLBController()
	slbController.Run(stopCh)
}

func haNatGwControllers(stopCh <-chan struct{}) {
	haNatGwController := HaNatGwController{}
	haNatGwController.NewHaNatGwController()
	haNatGwController.Run(stopCh)
}

func VpcPeeringControllers(stopCh <-chan struct{}) {
	vpcPeeringController := VpcPeeringController{}
	vpcPeeringController.NewVpcPeeringController()
	vpcPeeringController.Run(stopCh)
}

func dcvcControllers(stopCh <-chan struct{}) {
	dcvcController := DCVCController{}
	dcvcController.NewDCVCController()
	dcvcController.Run(stopCh)
}

func bosonServiceControllers(stopCh <-chan struct{}) {
	bosonServiceController := BosonServiceController{}
	bosonServiceController.NewBosonServiceController()
	bosonServiceController.Run(stopCh)
}

func configmapControllers(stopCh <-chan struct{}) {
	configmapController := ConfigmapController{}
	configmapController.NewConfigmapController()
	configmapController.Run(stopCh)
}

func rdmaNodeControllers(stopCh <-chan struct{}) {
	rdmaNodeController := NewRDMANodeController()
	rdmaNodeController.Run(stopCh)
}
