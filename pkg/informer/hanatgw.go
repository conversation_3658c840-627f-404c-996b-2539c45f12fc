package informer

import (
	"time"

	"github.com/sirupsen/logrus"
	"k8s.io/client-go/tools/cache"

	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"

	netclientset "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned"
	netinformer "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/informers/externalversions/network/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

var haNatGwStatus []string = []string{string(netv1.StateCreating), string(netv1.StateActive), string(netv1.StateDeleting), string(netv1.StateDeleted)}

// EipRuleController k8s controller
type HaNatGwController struct {
	ClientSet netclientset.Interface
	Informer  cache.SharedIndexInformer
}

// Run starts the process for listening for HaNatGw status changes and acting upon those changes
func (c *HaNatGwController) Run(stop <-chan struct{}) {

	defer logrus.Errorln("Stop HaNatGwController")

	logrus.Infoln("Start HaNatGwController")

	// Execute go function
	c.Informer.Run(stop)

	// Wait till we receive a stop signal
	<-stop
}

// EipRuleController 初始化 EIP rule controller EventHandler
func (c *HaNatGwController) NewHaNatGwController() {

	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalln(err)
	}

	cs, err := netclientset.NewForConfig(cfg)
	if err != nil {
		// to do
		logrus.Fatalln(err)
	}

	informer := netinformer.NewHaNatGwInformer(cs, time.Hour*24, nil)

	c.ClientSet = cs
	c.Informer = informer

	c.Informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.createHaNatGw,
		UpdateFunc: c.updaetHaNatGw,
		DeleteFunc: c.deleteHaNatGw,
	})
}

func (c *HaNatGwController) createHaNatGw(obj interface{}) {

}

func (c *HaNatGwController) updaetHaNatGw(oldObj interface{}, newObj interface{}) {
	o1 := oldObj.(*netv1.HaNatGw)
	o2 := newObj.(*netv1.HaNatGw)

	if o2.Status.State != o1.Status.State {
		logrus.Infof("hanatgw status change from %s to %s", o1.Status.State, o2.Status.State)
		haNatGw, has, err := dao.GetHaNatGatewayByName(resourceprovider.BosonProvider.Engine, o2.Name)
		if err != nil {
			logrus.Errorf("get hanatgw %s failed %s", o2.Name, err)
			return
		}
		if !has {
			logrus.Errorf("hanatgw %s not exists in db", o2.Name)
			return
		}

		if utils.ContainString(haNatGwStatus, o2.Status.State) {
			if err := dao.SetHaNatGatewayState(resourceprovider.BosonProvider.Engine, haNatGw.HaNatGatewayId, o2.Status.State); err != nil {
				logrus.Errorf("hanatgw %s update status error %s", o2.Name, err)
				return
			}
		} else {
			logrus.Errorf("hanat gw %s status is not support here %s", o2.Name, o2.Status.State)
			return
		}
	}
}

func (c *HaNatGwController) deleteHaNatGw(obj interface{}) {
	o := obj.(*netv1.HaNatGw)
	logrus.Infof("delete haNatGw [%s] with status from %s \n", o.Name, o.Status.State)

	err := dao.DeleteHaNatGatewayByName(resourceprovider.BosonProvider.Engine, o.Name)
	if err != nil {
		logrus.Errorln(err)
		return
	}
}
