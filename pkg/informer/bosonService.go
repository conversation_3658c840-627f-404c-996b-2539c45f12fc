package informer

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
	netControllerV1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netclientset "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned"
	netinformer "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/informers/externalversions/network/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/server"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/cache"
)

// BosonServiceController k8s controller
type BosonServiceController struct {
	ClientSet netclientset.Interface
	Informer  cache.SharedIndexInformer
}

func (c *BosonServiceController) NewBosonServiceController() {
	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalln(err)
	}

	cs, err := netclientset.NewForConfig(cfg)
	if err != nil {
		logrus.Fatalln(err)
	}
	informer := netinformer.NewBosonServiceInformer(cs, time.Hour*24, nil)

	c.ClientSet = cs
	c.Informer = informer
	c.Informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.createBosonService,
		UpdateFunc: c.updateBosonService,
		DeleteFunc: c.deleteBosonService,
	})
}

func (c *BosonServiceController) createBosonService(obj interface{}) {
	// nothing
}

func (c *BosonServiceController) updateBosonService(oldObj interface{}, obj interface{}) {
	logrus.Info("informer update boson service start", "boson service", obj.(*netv1.BosonService).Name)
	defer logrus.Info("informer update boson service end", "boson service", obj.(*netv1.BosonService).Name)

	if err := updateBosonServiceOne(obj.(*netv1.BosonService)); err != nil {
		return
	}
}

func (c *BosonServiceController) deleteBosonService(obj interface{}) {
	logrus.Info("informer delete boson service start", "boson service", obj.(*netv1.BosonService).Name)
	defer logrus.Info("informer delete boson service end", "boson service", obj.(*netv1.BosonService).Name)

	service := obj.(*netv1.BosonService)
	engine := resourceprovider.BosonProvider.Engine
	serviceDB, err := dao.GetBosonService(engine, service.Name)
	if err != nil {
		logrus.Error("delete boson service fail, get boson service db error", "service", service.Name, "error", err)
		return
	}
	if serviceDB == nil {
		return
	}

	err = dao.ClearBosonServiceEndpoints(engine, serviceDB.ServiceName)
	if err != nil {
		logrus.Error("delete boson service fail, clear boson service db error", "service", service.Name, "error", err)
		return
	}

	err = dao.DeleteBosonServiceByServiceName(engine, serviceDB.ServiceName)
	if err != nil {
		logrus.Error("delete boson service fail, delete db record error", "service", service.Name, "error", err)
		return
	}
}

func updateBosonServiceCci(serviceName string) error {
	err := server.BosonServiceEndpointsUpdateForCci(serviceName)
	if err != nil {
		logrus.Error("update boson service cci fail", "service", serviceName)
		return err
	}
	return nil
}

func updateBosonServiceOne(service *netv1.BosonService) error {
	logrus.Info("boson service update,", "service:", service.Name, "status:", service.Status)

	// 更新service后端列表
	engine := resourceprovider.BosonProvider.Engine
	serviceDB, err := dao.GetBosonService(engine, service.Name)
	if err != nil {
		logrus.Error("update boson service fail, get boson service db error,", "service:", service.Name, "error:", err)
		return err
	}
	if serviceDB == nil {
		logrus.Info("boson service update, service db not exist, skip.", "service:", service.Name)
		return err
	}

	endpointsOld, err := dao.ListBosonServiceEndpoints(engine, service.Name)
	if err != nil {
		logrus.Error("update boson service fail, get old boson service endpoints error,", "service:", service.Name, "error:", err)
		return err
	}
	endpoints := []db.BosonServiceEndpoints{}
	for _, e := range service.Status.Endpoints {
		endpoints = append(endpoints, db.BosonServiceEndpoints{
			ServiceName: serviceDB.ServiceName,
			Name:        e.Name,
			Ip:          e.Ip,
			Deleted:     false,
		})
	}
	adds, dels, updates := endpointsDiff(endpoints, endpointsOld)
	delIDs := []int64{}
	for _, e := range dels {
		delIDs = append(delIDs, e.ID)
	}

	logrus.Info(
		"boson service update,",
		"service:", service.Name,
		"old endpoints:", endpointsOld,
		"new endpoints:", endpoints,
		"add:", adds, "dels:", dels, "updates:", updates,
	)

	if len(delIDs) != 0 {
		err = dao.DeleteBosonServiceEndpoints(engine, delIDs)
		if err != nil {
			logrus.Error("update boson service fail, clear boson service endpoints error", "service", service.Name, "error", err)
			return err
		}
	}
	if len(adds) != 0 {
		err = dao.InsertBosonServiceEndpoints(engine, adds)
		if err != nil {
			logrus.Error("update boson service fail, insert boson service endpoints list error", "service", service.Name, "error", err)
			return err
		}
	}
	if len(updates) != 0 {
		err = dao.UpdateBosonServiceEndpoints(engine, updates)
		if err != nil {
			logrus.Error("update boson service fail, insert boson service endpoints list error", "service", service.Name, "error", err)
			return err
		}
	}

	// 更新可能存在的 SLB的TargetGroup的Targets
	if serviceDB.EndpointKind == string(netControllerV1.BosonServiceKind_CCI) {
		err = updateBosonServiceCci(serviceDB.ServiceName)
		if err != nil {
			return err
		}
	}

	return nil
}

func endpointsDiff(
	new []db.BosonServiceEndpoints, old []*db.BosonServiceEndpoints,
) ([]*db.BosonServiceEndpoints, []db.BosonServiceEndpoints, []db.BosonServiceEndpoints) {
	newMap := make(map[string]db.BosonServiceEndpoints)
	oldMap := make(map[string]*db.BosonServiceEndpoints)

	for index := range new {
		newMap[new[index].Name] = new[index]
	}
	for index := range old {
		oldMap[old[index].Name] = old[index]
	}

	adds := []*db.BosonServiceEndpoints{}
	for index, e := range new {
		if _, ok := oldMap[e.Name]; !ok {
			adds = append(adds, &new[index])
		}
	}

	dels := []db.BosonServiceEndpoints{}
	for index, e := range old {
		if _, ok := newMap[e.Name]; !ok {
			dels = append(dels, *old[index])
		}
	}

	updates := []db.BosonServiceEndpoints{}
	for _, e := range old {
		if eNew, ok := newMap[e.Name]; ok {
			if e.Ip != eNew.Ip {
				updates = append(updates, newMap[e.Name])
			}
		}
	}

	return adds, dels, updates
}

func (c *BosonServiceController) Run(stopCh <-chan struct{}) {
	defer logrus.Errorln("Stop BosonServiceController")

	logrus.Infoln("Start BosonServiceController")

	// Execute go function
	c.Informer.Run(stopCh)

	// Wait till we receive a stop signal
	<-stopCh
}

func syncBosonServiceCR() error {
	defer logrus.Infoln("finish boson service sync between db and cr")
	logrus.Infoln("Start boson service sync between db and cr")

	engine := resourceprovider.BosonProvider.Engine
	servicesDB, err := dao.ListBosonServices(engine)
	if err != nil {
		logrus.Errorf("sync boson service CR fail, list boson services DB fail, error:%s", err)
		return err
	}

	servicesCR, err := resourceprovider.BosonProvider.BosonNetClient.BosonServices().List(context.Background(), metav1.ListOptions{})
	if err != nil {
		logrus.Errorf("sync boson service CR fail, list boson services CR fail, error:%s", err)
		return err
	}

	servicesDBMap := make(map[string]*db.BosonServices)
	for index, serviceDB := range servicesDB {
		servicesDBMap[serviceDB.ServiceName] = servicesDB[index]
	}
	for index, serviceCR := range servicesCR.Items {
		serviceDB, ok := servicesDBMap[serviceCR.Name]
		if !ok {
			continue
		}

		err = syncBosonServiceCROne(serviceDB, &servicesCR.Items[index])
		if err != nil {
			logrus.Errorf("sync boson service CR fail, sync one fail, error:%s service:%s", err, serviceCR.Name)
			continue
		}
	}

	return nil
}

func syncBosonServiceCROne(serviceBD *db.BosonServices, serviceCR *netControllerV1.BosonService) error {
	return updateBosonServiceOne(serviceCR)
}
