package informer

import (
	"context"
	"time"

	"github.com/sirupsen/logrus"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/client-go/tools/cache"

	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	netclientset "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned"
	netinformer "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/informers/externalversions/network/v1"
	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/ipa/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

// sync the rule status from CR
// if there are different, update the rule status in db
// if f the CR does not exist, update the rule status deleted in db
// if the CR exists and no record, ingore this now
func syncIpaCR() error {
	defer logrus.Infoln("finish ipa sync between db and cr")

	logrus.Infoln("Start ipa sync between db and cr")
	// get all the records in db, update from the status of cr
	ipaDatas, err := dao.GetAllIPAs(resourceprovider.BosonProvider.Engine, true)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	ruleCrs, err := resourceprovider.BosonProvider.BosonNetClient.IPAs().List(context.TODO(), metav1.ListOptions{})
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	makeCrState := func(crs *netv1.IPAList) *(map[string]string) {
		m := make(map[string]string)
		for _, cr := range crs.Items {
			m[cr.Name] = cr.Status.State
		}
		return &m
	}

	ruleCrsState := makeCrState(ruleCrs)
	//logrus.Infof("ipa cr status: %v", *ruleCrsState)

	for _, ipaData := range ipaDatas {
		state, ok := (*ruleCrsState)[ipaData.Name]
		if !ok {
			state = network.IPA_DELETED.String()
		}
		if ipaData.State != state {
			err = dao.SetIPAState(resourceprovider.BosonProvider.Engine, ipaData.IpaID, state)
			if err != nil {
				logrus.Errorln(err)
				return err
			}
			logrus.Infof("sync ipa %v status from %v to %v\n", ipaData.Name, ipaData.State, state)
			resourceprovider.BosonProvider.Processor.StateMessageSend(ipaData.ID, state)
		}
	}
	return nil
}

// IpaController k8s controller
type IpaController struct {
	ClientSet netclientset.Interface
	Informer  cache.SharedIndexInformer
}

// Run starts the process for listening for ipa changes and acting upon those changes
func (c *IpaController) Run(stop <-chan struct{}) {

	defer logrus.Errorln("Stop IpaController")

	logrus.Infoln("Start IpaController")

	// Execute go function
	c.Informer.Run(stop)

	// Wait till we receive a stop signal
	<-stop
}

// IpaController 初始化 ipa controller EventHandler
func (c *IpaController) NewIpaController() {
	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalln(err)
	}

	utils.AddRateLimit(cfg)
	logrus.Infof("increate ipa informers ratelimit")

	cs, err := netclientset.NewForConfig(cfg)
	if err != nil {
		// to do
		logrus.Fatalln(err)
	}

	informer := netinformer.NewIPAInformer(cs, time.Hour*24, nil)

	c.ClientSet = cs
	c.Informer = informer

	c.Informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc:    c.createIpa,
		UpdateFunc: c.updateIpa,
		DeleteFunc: c.deleteIpa,
	})
}

func (c *IpaController) createIpa(obj interface{}) {

}

func (c *IpaController) updateIpa(oldObj interface{}, obj interface{}) {
	o1 := oldObj.(*netv1.IPA)
	o2 := obj.(*netv1.IPA)

	// update the db state via to the State
	if o2.Status.State != o1.Status.State {
		state := ""
		switch o2.Status.State {
		case network.IPA_DELETED.String():
			state = o2.Status.State

		// CREATED - BINDING
		case network.IPA_BINDING.String():
			state = o2.Status.State
		case network.IPA_PROVISION.String():
			state = o2.Status.State
		// UPDATING - ACTIVE
		// BINDING - ACTIVE
		case network.IPA_ACTIVE.String():
			state = o2.Status.State
		// ACTIVE - UNBINDING
		case network.IPA_UNBINDING.String():
			state = o2.Status.State
		// UNBINDING - CREATED
		case network.IPA_CREATED.String():
			state = o2.Status.State
		// ACTIVE - UPDATING
		case network.IPA_UPDATING.String():
			state = o2.Status.State
		case network.IPA_FAILED.String():
			state = o2.Status.State
		default:
			logrus.Errorf("others IPA[%v] status translate case from %v to %v\n", o1.Name, o1.Status.State, o2.Status.State)
		}

		if state != "" {
			logrus.Infof("Update IPA[%v] status from %v to %v\n", o1.Name, o1.Status.State, o2.Status.State)

			dbRTMetrics := exporter.InitDBRTMetrics("UpdateIPA")

			_, err := resourceprovider.BosonProvider.Engine.Exec("update ipas set state = ?, deleted = ? where name = ? and zone = ?",
				state, state == network.IPA_DELETED.String(), o2.Name, db.AZ)

			dbRTMetrics.ObserveDurationAndLog()

			if err != nil {
				logrus.Errorln(err)
				return
			}
		}

	}
}

func (c *IpaController) deleteIpa(obj interface{}) {

}
