package config

import (
	"os"

	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"github.com/sirupsen/logrus"
	"gopkg.in/yaml.v2"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

// EnvValueConfigFilePath 全配置参数文件
var EnvValueConfigFilePath = utils.GetEnv("CONFIG", "./boson-provider.yaml")

// Config 全配置文件对应结构体
type Config struct {
	Env      string           `yaml:"env"`
	MQ       RocketMQConfig   `yaml:"rocketmq"`
	PG       PostgresConfig   `yaml:"pg"`
	K8s      K8sConfig        `yaml:"k8s"`
	Boson    BosonDefault     `yaml:"boson_default"`
	InitPool map[string]*Pool `yaml:"init_pool"`
}

// RocketMQConfig 全配置文件中MQ对应结构体
type RocketMQConfig struct {
	Default    RocketMQConfigDefault    `yaml:"default"`
	CloudAudit RocketMQConfigCloudAudit `yaml:"cloudAudit"`
}

type RocketMQConfigDefault struct {
	NameServer                    []string `yaml:"nameServer"`
	Broker                        string   `yaml:"broker"`
	BrokerConsumerGroupName       string   `yaml:"brokerConsumerGroupName"`
	EIPConsumerGroupName          string   `yaml:"eip_consumer_group_name"`
	BrokerRMProducerGroupName     string   `yaml:"brokerRMProducerGroupName"`
	BrokerBossProducerGroupName   string   `yaml:"brokerBossProducerGroupName"`
	BrokerNoticeProducerGroupName string   `yaml:"brokerNoticeProducerGroupName"`
	RetryInterval                 int      `yaml:"retryInterval"`
	Topic                         string   `yaml:"topic"`
	EIPTopic                      string   `yaml:"eip_topic"`
	NoticeTopic                   string   `yaml:"notice_topic"`
	RMAckTopic                    string   `yaml:"rmAckTopic"`
	BossAckTopic                  string   `yaml:"bossAckTopic"`
	InstanceName                  string   `yaml:"instanceName"`
	AccessKey                     string   `yaml:"accessKey"`
	SecretKey                     string   `yaml:"secretKey"`

	SLBTopic             string `yaml:"slb_topic"`
	SLBConsumerGroupName string `yaml:"slb_consumer_group_name"`
}

type RocketMQConfigCloudAudit struct {
	NameServer   []string                      `yaml:"nameServer"`
	InstanceName string                        `yaml:"instanceName"`
	Topics       RocketMQConfigCloudAuditTopic `yaml:"topics"`
}

type RocketMQConfigCloudAuditTopic struct {
	Eip RocketMQConfigCloudAuditTopicInfo `yaml:"eip"`
	Vpc RocketMQConfigCloudAuditTopicInfo `yaml:"vpc"`
	Dc  RocketMQConfigCloudAuditTopicInfo `yaml:"dc"`
	Slb RocketMQConfigCloudAuditTopicInfo `yaml:"slb"`
}

type RocketMQConfigCloudAuditTopicInfo struct {
	Topic             string `yaml:"topic"`
	ProducerGroupName string `yaml:"producerGroupName"`
	AccessKey         string `yaml:"accessKey"`
	SecretKey         string `yaml:"secretKey"`
}

// PostgresConfig 全配置文件中PG对应结构体
type PostgresConfig struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	DB       string `yaml:"db"`
	ShowSQL  bool   `yaml:"show_sql"`
}

// K8sConfig 全配置文件中K8s对应结构体
type K8sConfig struct {
	Master     string `yaml:"master"`
	Kubeconfig string `yaml:"kubeconfig"`
}

// BosonDefault 默认 boson provider 相关配置
type BosonDefault struct {
	KKRegistryURL                      string
	HTTPEndPoint                       string
	GRPCEndpoint                       string
	VPCCIDR                            string                             `yaml:"vpc_cidr"`
	GeneveSubnetCIDR                   string                             `yaml:"geneve_subnet_cidr"`
	VlanSubnetCIDR                     string                             `yaml:"vlan_subent_cidr"`
	DcVxlanCIDR                        string                             `yaml:"dc_vxlan_cidr"`
	DcConsoleCIDR                      string                             `yaml:"dc_console_cidr"`
	VPCDefaultAZ                       string                             `yaml:"vpc_default_az"`
	VPCDefaultRegion                   string                             `yaml:"vpc_default_region"`
	GRPCPort                           int                                `yaml:"grpc_port"`
	HTTPPort                           int                                `yaml:"http_port"`
	MetricsPort                        int                                `yaml:"metrics_port"`
	PRP                                string                             `yaml:"prp"`
	BosonDefaultDgw                    BosonDefaultDgw                    `yaml:"dgw"`
	BosonDefaultQuota                  BosonDefaultQuota                  `yaml:"quota"`
	SlowOpsThresholdMilliSecondsConfig SlowOpsThresholdMilliSecondsConfig `yaml:"slow_ops_threshold_milli_seconds_config"`
	TrainingNetworks                   TrainingNetworks                   `yaml:"training_networks"`
	CloudAuditEnable                   bool                               `yaml:"cloud_audit_enable"`
	VPCDefaulAcls                      []ovn.Acl                          `yaml:"vpc_default_acls"`
	VPCAcls                            []VpcAcl                           `yaml:"vpc_acls"`
	DcPgLockTimeOut                    int                                `yaml:"dc_pg_lock_time_out"`
	IPAPgLockTimeOut                   int                                `yaml:"ipa_pg_lock_timeout"`
	DnatPgLockTimeOut                  int                                `yaml:"dnat_pg_lock_timeout"`
	VPCPgLockTimeOut                   int                                `yaml:"vpc_pg_lock_timeout"`
	EIPPgLockTimeOut                   int                                `yaml:"eip_pg_lock_timeout"`
	SLBPgLockTimeOut                   int                                `yaml:"slb_pg_lock_timeout"`
	SlbEnable                          bool                               `yaml:"slb_enable"`
	SlbReplica                         int                                `yaml:"slb_replica"`
	SlbExposeBasicNetworkVip           bool                               `yaml:"slb_expose_basic_network_vip"`
	SlbDataplane                       SlbDataplane                       `yaml:"slb_dataplane"`
	SlbExposeOverlayVip                bool                               `yaml:"slb_expose_overlay_vip"`
	BgpEnable                          bool                               `yaml:"bgp_enable"`
	BmsMasterNic                       string                             `yaml:"bms_master_nic"`
	TSGatewayNodes                     []string                           `yaml:"ts_gateway_nodes"`
}

type VpcAcl struct {
	SrcIp       string `yaml:"src_ip,omitempty"`
	SrcPort     string `yaml:"src_port,omitempty"`
	DestIp      string `yaml:"dest_ip,omitempty"`
	DestPort    string `yaml:"dest_port,omitempty"`
	Protocol    string `yaml:"protocol,omitempty"`
	Priority    int    `yaml:"priority,omitempty"`
	Action      string `yaml:"action,omitempty"`
	Description string `yaml:"description,omitempty"`
}

type SlbDataplane struct {
	Host     string `yaml:"host"`
	Port     string `yaml:"port"`
	User     string `yaml:"user"`
	Password string `yaml:"password"`
	DB       string `yaml:"db"`
}

type TrainingNetworks struct {
	Vlan_acl_nic_max_count int                      `yaml:"vlan_acl_nic_max_count"`
	KubeOvn                []TrainingNetworkConfig  `yaml:"kube_ovn"`
	Bms                    TrainingNetworkBmsConfig `yaml:"bms"`
}

type TrainingNetworkBmsConfig struct {
	Gws []string `yaml:"gws"`
}

// TrainingNetworkConfig 训练网网卡信息配置
type TrainingNetworkConfig struct {
	IfID string `yaml:"if_id"`
	Gw   string `yaml:"gw"`
}

// Pool natgw,eip,cidr 等pool init
type Pool struct {
	CIDR    string     `yaml:"cidr"`
	Gateway string     `yaml:"gateway"`
	Sku     string     `yaml:"sku"`
	IPs     []string   `yaml:"ips"`
	IB      []IBCIDR   `yaml:"ib"`
	Vlan    []VlanCIDR `yaml:"vlan"`
	Line    string     `yaml:"line"`
}

// IB_CIDR
type IBCIDR struct {
	CIDR     string `yaml:"cidr"`
	Gateway  string `yaml:"gateway"`
	ZonePRP  string `yaml:"zone_prp"`
	VNI      int    `yaml:"vni"`
	Scope    string `yaml:"scope"`
	Reserved string `yaml:"reserved"`
}

// VlanCIDR
type VlanCIDR struct {
	CIDR     string `yaml:"cidr"`
	Gateway  string `yaml:"gateway"`
	ZonePRP  string `yaml:"zone_prp"`
	VNI      int    `yaml:"vni"`
	Scope    string `yaml:"scope"`
	Reserved string `yaml:"reserved"`
}

// BosonDefaultDgw 默认 dgw config
// enable, **********/16, 10.118.164/24,10.118.165/24,plat-boson-infra
type BosonDefaultDgw struct {
	Enable         bool   `yaml:"enable"`
	ExternalSubnet string `yaml:"external_subnet"`
	Reserved       string `yaml:"resered"`
	PolicyCIDR     string `yaml:"policy_cidr"`
	InfraNs        string `yaml:"infra_ns"`
}

// BosonDefaultQuota 默认 quota
type BosonDefaultQuota struct {
	VPCCIDR              string `yaml:"vpc_cidr"`
	VPCsPerTenant        int64  `yaml:"vpcs_per_tenant"`
	GeneveSubnetsPerVPC  int64  `yaml:"geneve_subnets_per_vpc"`
	VlanSubnetsPerVPC    int64  `yaml:"vlan_subnets_per_vpc"`
	IBSubnetsPerVPC      int64  `yaml:"ib_subnets_per_vpc"`
	RoceSubnetsPerVPC    int64  `yaml:"roce_subnets_per_vpc"`
	EIPsPerVPC           int64  `yaml:"eips_per_vpc"`
	NATGatewaysPerVPC    int64  `yaml:"nat_gateways_per_vpc"`
	DnatRulesPerEip      int64  `yaml:"dnat_rules_per_eip"`
	SlbPerVpc            int64  `yaml:"slb_per_vpc"`
	ListenerPerSlb       int64  `yaml:"listener_per_slb"`
	RulePerSlb           int64  `yaml:"rule_per_slb"`
	TargetGroupPerSlb    int64  `yaml:"target_group_per_slb"`
	TargetPerTargetGroup int64  `yaml:"target_per_target_group"`
}

// SlowOpsThresholdMilliSecondsConfig slowOps 判断阈值
type SlowOpsThresholdMilliSecondsConfig struct {
	HTTPRequestRT int64 `yaml:"http_request_rt"`
	MQRequestRT   int64 `yaml:"mq_request_rt"`
	DBRequestRT   int64 `yaml:"db_request_rt"`
	K8sRequestRT  int64 `yaml:"k8s_request_rt"`
}

// SlowOpsThresholdMilliSecondsConfigInit 初始化
func (s *SlowOpsThresholdMilliSecondsConfig) SlowOpsThresholdMilliSecondsConfigInit() {

	if s.HTTPRequestRT == 0 {
		s.HTTPRequestRT = 1000
	}

	if s.MQRequestRT == 0 {
		s.MQRequestRT = 1000
	}

	if s.DBRequestRT == 0 {
		s.DBRequestRT = 1000
	}

	if s.K8sRequestRT == 0 {
		s.K8sRequestRT = 1000
	}
}

// NewResourceProviderConfig 参数特殊处理
func NewResourceProviderConfig(rawConfig *Config) *Config {
	c := rawConfig

	if c.K8s.Kubeconfig != "" && c.K8s.Master != "" {
		utils.MasterURL = c.K8s.Master
		utils.Kubeconfig = c.K8s.Kubeconfig
		logrus.Infoln("Connect remote k8s:", c.K8s.Master)
	}

	return c
}

// InitRawConfig 读参数文件
func InitRawConfig(configPath *string) *Config {
	c := Config{}

	b, err := os.ReadFile(*configPath)
	if err != nil {
		logrus.Fatalf("Failed to read config file: %v, %v", *configPath, err)
	}

	err = yaml.Unmarshal(b, &c)
	if err != nil {
		logrus.Fatalf("Failed to unmarshal yaml %v to Object: %v", string(b), err)
	}

	if c.Boson.BosonDefaultDgw.PolicyCIDR == "" {
		logrus.Fatalf("Invalid dgw config  %v", c.Boson.BosonDefaultDgw)
	}

	if c.Boson.IPAPgLockTimeOut == 0 {
		c.Boson.IPAPgLockTimeOut = 30
		logrus.Infof("set ipa pglock timeout %d", c.Boson.IPAPgLockTimeOut)
	}

	if c.Boson.DnatPgLockTimeOut == 0 {
		c.Boson.DnatPgLockTimeOut = 30
		logrus.Infof("set dnat pglock timeout %d", c.Boson.DnatPgLockTimeOut)
	}

	if c.Boson.DcPgLockTimeOut == 0 {
		c.Boson.DcPgLockTimeOut = 30
		logrus.Infof("set dc pglock timeout %d", c.Boson.DcPgLockTimeOut)
	}

	if c.Boson.VPCPgLockTimeOut == 0 {
		c.Boson.VPCPgLockTimeOut = 30
		logrus.Infof("set vpc pglock timeout %d", c.Boson.VPCPgLockTimeOut)
	}

	if c.Boson.EIPPgLockTimeOut == 0 {
		c.Boson.EIPPgLockTimeOut = 30
		logrus.Infof("set eip pglock timeout %d", c.Boson.EIPPgLockTimeOut)
	}

	if c.Boson.SLBPgLockTimeOut == 0 {
		c.Boson.SLBPgLockTimeOut = 30
		logrus.Infof("set slb pglock timeout %d", c.Boson.SLBPgLockTimeOut)
	}

	return &c
}
