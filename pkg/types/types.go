package types

import (
	"encoding/json"
	"fmt"
	"sync"

	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
)

// ActionResultFailed 消息执行失败
const ActionResultFailed = "failed"

// ActionResultSucceeded 消息执行成功
const ActionResultSucceeded = "success"

// ActionResultSucceeded 消息任务暂未实现
const ActionResultNotImplemented = "not_implemented"

// StateActive 服务状态 ACTIVE
const StateActive = "ACTIVE"
const StateDeleted = "DELETED"

// ExpireAction 仅适用于过期续订释放场景
var ExpireActionToMethod = map[string]string{"expireStop": "ExpireStop", "delete": "ForceDelete", "renewStart": "RenewStart", "resize": "Resize", "resizeCancel": "ResizeCancel"}

// NoticeType
var NoticeType = map[string]string{
	"expireStop":     "EIP_0001",
	"forceDelete":    "EIP_0002",
	"FAIL_2_SUCCESS": "DC_0001", // 虚拟通道健康状态从失败变为健康
	"SUCCESS_2_FAIL": "DC_0002", // 虚拟通道健康检查从健康变为失败
}

// RMBody 接收的消息体
type RMBody struct {
	Package      string       `json:"package"`
	Service      string       `json:"service"`
	Method       string       `json:"method"`
	Action       string       `json:"action"`
	TenantCode   string       `json:"tenant_code"`
	CallbackData CallbackData `json:"callback_data"`
	Data         interface{}  `json:"data"`

	// for ExpireAction
	ResourceID string `json:"resource_id"`
	TenantID   string `json:"tenant_id"`
}

// CallbackData 任务执行结果反馈信息
type CallbackData struct {
	OrderID  string `json:"order_id"`
	UserID   string `json:"user_id"`
	TenantID string `json:"tenant_id"`
}

func (d *CallbackData) String() string {
	data, _ := json.Marshal(d)
	return string(data)
}

// StateBody 服务状态消息体
type StateBody struct {
	ID    string `json:"id"`
	State string `json:"state"`
}

// ActionBody 任务状态消息体
type ActionBody struct {
	ResourceID   string       `json:"resource_id"`
	ResourceType string       `json:"resource_type"`
	Result       string       `json:"result"`
	Reason       string       `json:"reason"`
	Action       string       `json:"action"`
	CallbackData CallbackData `json:"callback_data"`
}

// NoticeBody
type NoticeBody struct {
	SpecVersion string         `json:"specversion"`
	Type        string         `json:"type"`
	Source      string         `json:"source"`
	ID          string         `json:"id"`
	Time        string         `json:"time"`
	Data        NoticeBodyData `json:"data"`
}

type NoticeBodyData struct {
	Receiver NoticeBodyDataReceiver `json:"receiver"`
	Payload  NoticeBodyDataPayload  `json:"payload"`
}

type NoticeBodyDataReceiver struct {
	TenantID string   `json:"tenant_id"`
	UserIDs  []string `json:"user_ids"`
}

type NoticeBodyDataPayload struct {
	IP          string `json:"ip_address"`
	Name        string `json:"dcvc_name"`
	DisplayName string `json:"dcvc_display_name"`
}

// CloudAuditBody 发送给云审计的消息结构
// 数据结构的设计、类型的定义参考：https://ones.ainewera.com/wiki/#/team/JNwe8qUX/share/89bs63gX/page/8v8chxBi
type CloudAuditBody struct {
	Time          string      `json:"time"`
	User          interface{} `json:"user"`
	ServiceType   string      `json:"service_type"`
	OperationType string      `json:"operation_type"`
	ResourceType  string      `json:"resource_type"`
	EventID       string      `json:"event_id"`
	SourceIP      string      `json:"source_ip"`
	EventRating   string      `json:"event_rating"`
	EventType     string      `json:"event_type"`
	Request       interface{} `json:"request"`
	Response      interface{} `json:"response"`
	ResourceName  string      `json:"resource_name"`
	ResourceID    string      `json:"resource_id"`
	APIVersion    string      `json:"api_version"`
	Message       interface{} `json:"message"`
	Code          int32       `json:"code"`
	RequestID     string      `json:"request_id"`
	LocationInfo  interface{} `json:"location_info"`
	Endpoint      string      `json:"endpoint"`
	ResourceURL   string      `json:"resource_url"`
}

type CloudAuditBodyUser struct {
	UserID     string `json:"user_id"`
	UserName   string `json:"user_name"`
	TenantID   string `json:"tenant_id"`
	TenantCode string `json:"tenant_code"`
}

type CloudAuditEventType string

// 事件类型，如下：
// ConsoleAction表示通过管理界面或控制台执行的操作
// ApiCall表示API调用触发的操作，当前版本不涉及程序化API大量调用的操作
// SystemAction表示系统内部触发或间接调用的操作，当前不涉及
// ServiceEvent平台执行的管控事件，当前不涉及
// https://ones.ainewera.com/wiki/#/team/JNwe8qUX/share/89bs63gX/page/8v8chxBi
const (
	CloudAuditEventTypeConsoleAction CloudAuditEventType = "ConsoleAction"
	CloudAuditEventTypeApiCall       CloudAuditEventType = "ApiCall"
	CloudAuditEventTypeSystemAction  CloudAuditEventType = "SystemAction"
	CloudAuditEventTypeServiceEvent  CloudAuditEventType = "ServiceEvent"
)

type CloudAuditEventRating string

// 事件等级，分为normal（正常）、warning（警告）和incident（事故）。
// normal：代表本次操作成功。
// warning：代表本次操作失败。
// incident：代表本次操作引起了比失败更严重的后果，比如会造成节点故障或用户业务故障等情况。
// https://ones.ainewera.com/wiki/#/team/JNwe8qUX/share/89bs63gX/page/8v8chxBi
const (
	CloudAuditEventRatingNormal   CloudAuditEventRating = "normal"
	CloudAuditEventRatingWarning  CloudAuditEventRating = "warning"
	CloudAuditEventRatingIncident CloudAuditEventRating = "incident"
)

type CloudAuditServiceType string

// 服务类型，与云服务英文简称保持一致
// https://ones.ainewera.com/wiki/#/team/JNwe8qUX/space/F5zdhken/page/4SKmdyFm
const (
	CloudAuditServiceTypeVPC CloudAuditServiceType = "VPC"
	CloudAuditServiceTypeEIP CloudAuditServiceType = "EIP"
	CloudAuditServiceTypeDC  CloudAuditServiceType = "DC"
	CloudAuditServiceTypeSLB CloudAuditServiceType = "SLB"
)

type CloudAuditOperationType string

// 操作类型，与操作类型英文名称保持一致
// https://ones.ainewera.com/wiki/#/team/JNwe8qUX/space/F5zdhken/page/4SKmdyFm
const (
	CloudAuditOperationTypeCreateDnatRules      CloudAuditOperationType = "createDnatRules"
	CloudAuditOperationTypeUpdateDnatRules      CloudAuditOperationType = "updateDnatRules"
	CloudAuditOperationTypeDeleteDnatRules      CloudAuditOperationType = "deleteDnatRules"
	CloudAuditOperationTypeBindDnatRules        CloudAuditOperationType = "bindDnatRules"
	CloudAuditOperationTypeUnbindDnatRules      CloudAuditOperationType = "unbindDnatRules"
	CloudAuditOperationTypeBatchUnbindDnatRules CloudAuditOperationType = "batchUnbindDnatRules"

	CloudAuditOperationTypeCreateAccessControl CloudAuditOperationType = "createAccessControl"
	CloudAuditOperationTypeUpdateAccessControl CloudAuditOperationType = "updateAccessControl"
	CloudAuditOperationTypeDeleteAccessControl CloudAuditOperationType = "deleteAccessControl"

	CloudAuditOperationTypeCreateDcgws CloudAuditOperationType = "createDcgw"
	CloudAuditOperationTypeUpdateDcgws CloudAuditOperationType = "updateDcgw"
	CloudAuditOperationTypeDeleteDcgws CloudAuditOperationType = "deleteDcgw"

	CloudAuditOperationTypeCreateDcvcs CloudAuditOperationType = "createDcvc"
	CloudAuditOperationTypeUpdateDcvcs CloudAuditOperationType = "updateDcvc"
	CloudAuditOperationTypeDeleteDcvcs CloudAuditOperationType = "deleteDcvc"

	CloudAuditOperationTypeCreateSlbListener    CloudAuditOperationType = "createListener"
	CloudAuditOperationTypeUpdateSlbListener    CloudAuditOperationType = "updateListener"
	CloudAuditOperationTypeDeleteSlbListener    CloudAuditOperationType = "deleteListener"
	CloudAuditOperationTypeCreateSlbTargetGroup CloudAuditOperationType = "createTargetGroup"
	CloudAuditOperationTypeUpdateSlbTargetGroup CloudAuditOperationType = "updateTargetGroup"
	CloudAuditOperationTypeDeleteSlbTargetGroup CloudAuditOperationType = "deleteTargetGroup"
	CloudAuditOperationTypeSlbAddTarget         CloudAuditOperationType = "addTarget"
	CloudAuditOperationTypeSlbRemoveTarget      CloudAuditOperationType = "removeTarget"
	CloudAuditOperationTypeSlbUpdateTarget      CloudAuditOperationType = "updateTarget"

	CloudAuditOperationTypeCreateEips    CloudAuditOperationType = "createEips"
	CloudAuditOperationTypeUpdateEips    CloudAuditOperationType = "updateEips"
	CloudAuditOperationTypeDeleteEips    CloudAuditOperationType = "deleteEips"
	CloudAuditOperationTypeSuspendEips   CloudAuditOperationType = "suspendEips"
	CloudAuditOperationTypeResumeEips    CloudAuditOperationType = "resumeEips"
	CloudAuditOperationTypeReleaseEips   CloudAuditOperationType = "releaseEips"
	CloudAuditOperationTypeResizeEips    CloudAuditOperationType = "resizeEips"
	CloudAuditOperationTypeUpdateDstCidr CloudAuditOperationType = "updateDstCIDR"
)

type CloudAuditResourceType string

// 操作类型，与操作类型英文名称保持一致
// https://ones.ainewera.com/wiki/#/team/JNwe8qUX/share/FahcyQHL/page/HvTVoywg
const (
	CloudAuditResourceTypeVpc              CloudAuditResourceType = "network.vpc.v1.vpc" // 暂未使用
	CloudAuditResourceTypeEip              CloudAuditResourceType = "network.eip.v1.eip" // 暂未使用
	CloudAuditResourceTypeEipDnat          CloudAuditResourceType = "network.eip.v1.dnat"
	CloudAuditResourceTypeEipAccessControl CloudAuditResourceType = "network.eip.v1.accessControl"
	CloudAuditResourceTypeDcgw             CloudAuditResourceType = "network.dc.v1.Dcgw"
	CloudAuditResourceTypeDcvc             CloudAuditResourceType = "network.dc.v1.Dcvc"
	CloudAuditResourceTypeSlb              CloudAuditResourceType = "network.slb.v1.slb"
	CloudAuditResourceTypeSlbListener      CloudAuditResourceType = "network.slb.v1.listener"
	CloudAuditResourceTypeSlbTargetGroup   CloudAuditResourceType = "network.slb.v1.targetGroup"
	CloudAuditResourceTypeSlbTarget        CloudAuditResourceType = "network.slb.v1.target"
	CloudAuditResourceTypeEipDstCidr       CloudAuditResourceType = "network.eip.v1.dstCIDR"
)

const CloudAuditAPIVersion = "v1"

type CloudEventType string

const (
	SLB_RESOURCE_TYPE                = "sensetime.core.network.slb"
	SLB_API_VERSION                  = "v1"
	SLB_CREATE_ACTION                = "create"
	SLB_UPDATE_ACTION                = "update"
	SLB_DELETE_ACTION                = "delete"
	SLB_CREATE        CloudEventType = SLB_RESOURCE_TYPE + "." + SLB_CREATE_ACTION + "." + SLB_API_VERSION
	SLB_UPDATE        CloudEventType = SLB_RESOURCE_TYPE + "." + SLB_UPDATE_ACTION + "." + SLB_API_VERSION
	SLB_DELETE        CloudEventType = SLB_RESOURCE_TYPE + "." + SLB_DELETE_ACTION + "." + SLB_API_VERSION

	RESOURCE_MSG_TYPE                             = "sensetime.core.higgs.msg"
	RESOURCE_MSG_API_VERSION                      = "v1"
	RESOURCE_MSG_EXPIRESTOP_ACTION                = "expireStop"
	RESOURCE_MSG_RENEWSTART_ACTION                = "renewStart"
	RESOURCE_MSG_DELETE_ACTION                    = "delete"
	RESOURCE_MSG_EXPIRESTOP        CloudEventType = RESOURCE_MSG_TYPE + "." + RESOURCE_MSG_EXPIRESTOP_ACTION + "." + RESOURCE_MSG_API_VERSION
	RESOURCE_MSG_RENEWSTART        CloudEventType = RESOURCE_MSG_TYPE + "." + RESOURCE_MSG_RENEWSTART_ACTION + "." + RESOURCE_MSG_API_VERSION
	RESOURCE_MSG_DELETE            CloudEventType = RESOURCE_MSG_TYPE + "." + RESOURCE_MSG_DELETE_ACTION + "." + RESOURCE_MSG_API_VERSION
)

type CloudEventMsg struct {
	SpecVersion     *string       `json:"specversion"`
	ID              *string       `json:"id"`
	Source          *string       `json:"source"`
	Type            *string       `json:"type"`
	Time            *string       `json:"time"`
	DataContentType *string       `json:"datacontenttype"`
	Data            interface{}   `json:"data"`
	CallbackData    *CallbackData `json:"callback_data"`
}

func (m *CloudEventMsg) String() string {
	data, err := json.Marshal(m)
	if err != nil {
		return fmt.Sprintf("m marshal err:%s\n", err.Error())
	}
	return fmt.Sprintf("%s\n", string(data))
}

func (m *CloudEventMsg) GetData() ([]byte, error) {
	data, err := json.Marshal(m.Data)
	if err != nil {
		logrus.Error("CloudEventMsg get data fail.", "error", err.Error())
		return nil, err
	}
	return data, nil
}

type RDMAClusterCache struct {
	Data  *vpc.VPCRDMAClustersStatus
	Mutex sync.RWMutex
}

var RDMACache = &RDMAClusterCache{
	Data: &vpc.VPCRDMAClustersStatus{},
}
