package resourceprovider

import (
	"encoding/json"
	"fmt"

	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/types/known/timestamppb"

	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
)

// SubnetKind crd kind
const SubnetKind = "Subnet"

func subnetServices(b *types.RMBody) {
	switch b.Method {
	case "ListSubnets":
		listSubnets(b)
	case "GetSubnet":
		getSubnet(b)
	case "CreateSubnet":
		createSubnet(b)
	case "UpdateSubnet":
		updateSubnet(b)
	case "DeleteSubnet":
		deleteSubnet(b)
	default:
		reason := fmt.Sprintf("Not an avaliable %v service method: %v", b.Service, b.Method)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
	}
}

func createSubnet(b *types.RMBody) {

	var err error = nil
	reason := ""

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "Subnet", "CreateSubnet")

	defer func() {
		if err == nil && reason == "" {
			mqRTMetrics.Result = types.ActionResultSucceeded
		}
		mqRTMetrics.ObserveDurationAndLog()
	}()

	request := network.CreateSubnetRequest{}

	js, _ := json.Marshal(b.Data)

	err = json.Unmarshal(js, &request)
	if err != nil {
		logrus.Errorln(err)
		reason = fmt.Sprintf("Not an avaliable %v service %v method body for CreateSubnetRequest: %+v", b.Service, b.Method, b.Data)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
		return
	}

	if request.Zone != db.Region {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.Region)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_FAILED.String())
		return
	}

	if request.Subnet.CreateTime == nil {
		request.Subnet.CreateTime = timestamppb.Now()
	}

	if request.Subnet.UpdateTime == nil {
		request.Subnet.UpdateTime = request.Subnet.CreateTime
	}

	has, err := dao.CheckSubnet(BosonProvider.Engine, request.SubnetName, request.Zone)
	if err != nil {
		logrus.Errorln(has, err)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}

	if has {
		reason = fmt.Sprintf("Subnet %v already exist", request.SubnetName)
		logrus.Errorln(reason)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, reason, b.CallbackData)
		return
	}

	if request.Subnet.Properties.Scope == network.SubnetProperties_SERVICE &&
		request.Subnet.Properties.NetworkType == network.SubnetProperties_GENEVE {
		reason, err = createGeneveSubnet(b, &request)
		return
	}

	reason = fmt.Sprintf("Invalid subnet scope: %v", request.Subnet.Properties.Scope)
	logrus.Errorln(reason)
	BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, reason, b.CallbackData)
}

func updateSubnet(b *types.RMBody) {

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "Subnet", "UpdateSubnet")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()

}

func getSubnet(b *types.RMBody) {

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "Subnet", "GetSubnet")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()

}

func listSubnets(b *types.RMBody) {

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "Subnet", "ListSubnets")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()

}

func deleteSubnet(b *types.RMBody) {

	var err error = nil
	reason := ""

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "Subnet", "DeleteSubnet")
	defer func() {
		if err == nil && reason == "" {
			mqRTMetrics.Result = types.ActionResultSucceeded
		}
		mqRTMetrics.ObserveDurationAndLog()
	}()

	request := network.DeleteSubnetRequest{}

	js, _ := json.Marshal(b.Data)

	err = json.Unmarshal(js, &request)
	if err != nil {
		logrus.Errorln(err)
		reason = fmt.Sprintf("Not an avaliable %v service %v method body for DeleteSubnetRequest: %+v", b.Service, b.Method, b.Data)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
		return
	}

	if request.Zone != db.Region {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.Region)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, err.Error(), b.CallbackData)
		return
	}

	subnetData, err := dao.GetSubnetByName(BosonProvider.Engine, request.SubnetName, request.Zone)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, err.Error(), b.CallbackData)
		return
	}

	if subnetData.Scope == network.SubnetProperties_SERVICE.String() &&
		subnetData.NetworkType == network.SubnetProperties_GENEVE.String() {
		reason, err = deleteGeneveSubnet(b, &request, subnetData)
		return
	}

	// switch network.SubnetProperties_Scope(subnetData.Scope) {
	// case network.SubnetProperties_geneve:
	// 	deleteGeneveSubnet(b, &request, &subnetData)
	// case network.SubnetProperties_Vlan:
	// 	//deleteVlanSubnet(b, &request)
	// case network.SubnetProperties_IB:
	// 	//deleteIBSubnet(b, &request)
	// case network.SubnetProperties_Roce:
	// 	//deleteRoceSubnet(b, &request)
	// default:
	reason = fmt.Sprintf("Invalid subnet scope: %v", subnetData.Scope)
	logrus.Errorln(reason)
	BosonProvider.Processor.FailedActionMessageSend(subnetData.SubnetID, subnetData.ResourceType, b.Action, reason, b.CallbackData)
	// }
}
