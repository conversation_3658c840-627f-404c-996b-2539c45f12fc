package resourceprovider

import (
	"fmt"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
)

func snatRuleServices(b *types.RMBody) {
	switch b.Method {
	case "ListSNATRules":
		listSNATRules(b)
	case "GetSNATRule":
		getSNATRule(b)
	case "CreateSNATRule":
		createSNATRule(b)
	case "UpdateSNATRule":
		updateSNATRule(b)
	case "DeleteSNATRule":
		deleteSNATRule(b)
	default:
		reason := fmt.Sprintf("Not an avaliable %v service method: %v", b.Service, b.Method)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
	}
}

func createSNATRule(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.<PERSON>, "SNATRule", "CreateSNATRule")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}

func updateSNATRule(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "SNATRule", "UpdateSNATRule")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}

func getSNATRule(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "SNATRule", "GetSNATRule")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}

func listSNATRules(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "SNATRule", "ListSNATRules")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}

func deleteSNATRule(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "SNATRule", "DeleteSNATRule")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}
