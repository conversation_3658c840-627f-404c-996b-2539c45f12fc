package resourceprovider

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"reflect"
	"sort"
	"strings"
	"time"

	"github.com/allisson/go-pglock/v3"
	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"github.com/sirupsen/logrus"
	netControllerV1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	eipv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	slbv1 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/slb/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	higgsMsgv1 "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/msg/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

func slbService(eventType types.CloudEventType, msg *types.CloudEventMsg) {
	logrus.Info("slb process start", "type", eventType, "id", msg.ID)
	defer logrus.Info("slb process end", "type", eventType, "id", msg.ID)

	switch eventType {
	case types.SLB_CREATE:
		slbCreate(msg, string(eventType), types.SLB_CREATE_ACTION)
	case types.SLB_UPDATE:
		slbUpdate(msg, string(eventType), types.SLB_UPDATE_ACTION)
	case types.SLB_DELETE:
		slbDelete(msg, string(eventType), types.SLB_DELETE_ACTION)
	case types.RESOURCE_MSG_EXPIRESTOP:
		slbExpireStop(msg, string(eventType), types.RESOURCE_MSG_EXPIRESTOP_ACTION)
	case types.RESOURCE_MSG_RENEWSTART:
		slbRenewStart(msg, string(eventType), types.RESOURCE_MSG_RENEWSTART_ACTION)
	case types.RESOURCE_MSG_DELETE:
		slbNotifyDelete(msg, string(eventType), types.RESOURCE_MSG_DELETE_ACTION)
	default:
		errMsg := fmt.Sprintf("slb service CloudEvent type:%s not support, event:%s\n", eventType, msg.String())
		logrus.Info(errMsg)
		BosonProvider.Processor.ErrorActionMessageSend(string(eventType), errMsg, types.CallbackData{})
	}
}

func slbCreate(msg *types.CloudEventMsg, method string, action string) {
	var err error = nil
	var createSLBMessage *slbv1.CreateSLBMessage = nil
	var callbackData *types.CallbackData = nil
	var isActionError bool = false
	var resource *slbResource = nil
	var eip *db.Eips = nil
	defer func() {
		slbCreateDefer(
			err, isActionError, msg, method, action, createSLBMessage, callbackData, resource, eip,
		)
	}()

	// .1 反序列化 RM 消息
	if createSLBMessage, callbackData, err = generateCreateSLBMessage(msg); err != nil {
		isActionError = true
		return
	}

	// .1.1 add pg lock here
	seed := fmt.Sprintf("%s-%s", db.AZ, PGLOCK_SLB)
	pglockId := utils.HashStringToInt32(seed)
	logrus.Infof("slb: %s,init PG Lock ID %d for %s (region-slbname)", createSLBMessage.SlbName, pglockId, seed)

	var dlock pglock.Lock
	ctx := context.Background()
	timeout := BosonProvider.RpConfig.Boson.SLBPgLockTimeOut
	childCtx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Second)
	dbLockRTMetrics := exporter.InitDBRTMetrics("PgLockWhenCreateSLB")
	err = func() error {
		defer cancel()
		dlock, err = pglock.NewLock(childCtx, int64(pglockId), BosonProvider.Engine.DB().DB)
		if err != nil {
			logrus.Errorf("slb: %s, new pglock for pgLockID %d failed, error: %s", createSLBMessage.SlbName, pglockId, err.Error())
			return err
		}
		logrus.Infof("slb: %s, new pglock for pgLockID %d success", createSLBMessage.SlbName, pglockId)

		err = dlock.WaitAndLock(childCtx)
		if err != nil {
			logrus.Errorf("slb: %s, get pglock for pgLockID %d failed, error %s", createSLBMessage.SlbName, pglockId, err.Error())
			return err
		}
		return nil
	}()
	dbLockRTMetrics.ObserveDurationAndLog()
	defer dlock.Close()

	defer func() {
		_ = dlock.Unlock(ctx) // protect end here
		logrus.Infof("slb: %s, unlock pglock for pgLockID %d", createSLBMessage.SlbName, pglockId)
	}()

	if err != nil {
		return
	}

	// .2 check
	vpc, err := slbCreateCheck(createSLBMessage)
	if err != nil {
		return
	}

	// .3 allocate resource
	resource, err = slbAllocateResource(vpc, createSLBMessage.Slb.Properties.Resources.CapacityLimitations)
	if err != nil {
		return
	}

	// .4 EIP update
	eip, err = slbAssociateEIPonCreate(createSLBMessage, resource)
	if err != nil {
		return
	}

	// .5 CR create
	if err = slbCRCreate(createSLBMessage, method, resource, callbackData, eip); err != nil {
		return
	}

	// .6 save DB
	if err = slbDBCreate(createSLBMessage, method, resource); err != nil {
		return
	}
}

func slbCreateDefer(
	err error, isActionError bool,
	msg *types.CloudEventMsg, method string, action string,
	createSLBMessage *slbv1.CreateSLBMessage, callbackData *types.CallbackData,
	resource *slbResource, eip *db.Eips,
) {
	var resourceID = ""
	var resourceUID = ""
	var resourceType = ""
	var mqRTMetrics *exporter.MQRTMetrics = nil
	if createSLBMessage != nil {
		resourceID = createSLBMessage.Slb.Id
		resourceUID = createSLBMessage.Slb.Uid
		resourceType = createSLBMessage.Slb.ResourceType
		mqRTMetrics = exporter.InitMQRTMetrics(createSLBMessage.TenantCode, "SLB", method)
	}
	cbData := callbackData
	if cbData == nil {
		cbData = &types.CallbackData{}
	}

	// .1 RM
	if err != nil {
		if isActionError { // 参数错误
			BosonProvider.Processor.ErrorActionMessageSend(
				action, err.Error(), *cbData,
			)
		} else { // 参数正确，处理失败
			BosonProvider.Processor.FailedActionMessageSend(
				resourceUID, resourceType, action, err.Error(), *cbData,
			)
		}
		if resourceID != "" {
			BosonProvider.Processor.StateMessageSend(resourceID, slbStateTypeName(slbv1.SLB_FAILED))
		}
	} else {
		BosonProvider.Processor.SuccessActionMessageSend(
			resourceUID, resourceType, action, *cbData,
		)
	}

	// .2 Metrics
	if mqRTMetrics == nil || createSLBMessage == nil || cbData == nil {
		errMsg := fmt.Sprintf(
			"SLB create fail, mqRTMetrics:%p, createSLBMessage:%p, callbackData:%p",
			mqRTMetrics, createSLBMessage, cbData,
		)
		logrus.Errorln(errMsg)
		return
	}

	if err == nil {
		mqRTMetrics.Result = types.ActionResultSucceeded
	}

	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": cbData.TenantID,
		"user_id":   cbData.UserID,
		"service":   SLBServiceName,
		"method":    method,
		"action":    action,
		"data":      msg,
	}).Infof("SLB create %s result %s", resourceID, mqRTMetrics.Result)
	mqRTMetrics.ObserveDurationAndLog()

	// .3 ip resource
	if err != nil {
		if createSLBMessage != nil && resource != nil {
			logrus.Errorln(fmt.Sprintf("SLB create fail, release resource:%s\n", resource.String()))
			slbReleaseResource(resource)
		}
	}

	// .4 eip
	// nothing

	// .5 cr
	if err != nil {
		if createSLBMessage != nil {
			logrus.Errorln(fmt.Sprintf("SLB create fail, release slb CR:%s\n", createSLBMessage.Slb.Name))
			errNew := slbCRDelete(createSLBMessage.Slb.Name)
			if errNew != nil {
				logrus.Errorln(fmt.Sprintf("SLB create fail, release slb CR:%s err:%s\n", createSLBMessage.Slb.Name, errNew.Error()))
			}
		}
	}
}

func slbUpdate(msg *types.CloudEventMsg, method string, action string) {
	var err error = nil
	var updateSLBMessage *slbv1.UpdateSLBMessage = nil
	var isActionError bool = false
	var slbRecord *db.Slbs = nil

	defer func() {
		slbUpdateDefer(err, isActionError, msg, method, action, updateSLBMessage, slbRecord)
	}()

	// .1 反序列化 RM 消息
	if updateSLBMessage, err = generateUpdateSLBMessage(msg); err != nil {
		isActionError = true
		return
	}

	// .2 check
	var vpc *db.Vpcs
	slbRecord, vpc, err = slbUpdateCheck(updateSLBMessage)
	if err != nil {
		return
	}
	var resource *slbResource
	resource, err = slbFetchResource(slbRecord, updateSLBMessage, vpc)
	if err != nil {
		return
	}

	// .3 EIP update
	eipCurr, err := slbAssociateEIPonUpdate(updateSLBMessage, slbRecord, resource)
	if err != nil {
		return
	}

	// .4 通用字段
	// a. DisplayName、Description 直接更新 DB 无业务逻辑
	// pass

	// .4 CR update
	if err = slbCRUpdate(updateSLBMessage, method, eipCurr, slbRecord); err != nil {
		return
	}

	// .5 update DB
	if err = slbDBUpdate(updateSLBMessage, method, slbRecord); err != nil {
		return
	}
}

func slbUpdateDefer(
	err error, isActionError bool,
	msg *types.CloudEventMsg, method string, action string,
	updateSLBMessage *slbv1.UpdateSLBMessage, slbRecord *db.Slbs,
) {
	var resourceID = ""
	var mqRTMetrics *exporter.MQRTMetrics = nil
	if slbRecord != nil {
		resourceID = slbRecord.ID
		mqRTMetrics = exporter.InitMQRTMetrics(slbRecord.TenantID, "SLB", method)
	}

	// .1 RM
	if err != nil {
		if resourceID != "" {
			BosonProvider.Processor.StateMessageSend(resourceID, slbStateTypeName(slbv1.SLB_FAILED))
		}
	}

	// .2 Metrics
	if mqRTMetrics == nil {
		errMsg := fmt.Sprintf(
			"SLB update fail, mqRTMetrics:%p, updateSLBMessage:%p",
			mqRTMetrics, updateSLBMessage,
		)
		logrus.Info(errMsg)
		return
	}
	if err == nil {
		mqRTMetrics.Result = types.ActionResultSucceeded
	}
	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": "",
		"user_id":   "",
		"service":   SLBServiceName,
		"method":    method,
		"action":    action,
		"data":      msg,
	}).Infof("SLB update %s result %s", resourceID, mqRTMetrics.Result)
	mqRTMetrics.ObserveDurationAndLog()
}

func slbDelete(msg *types.CloudEventMsg, method string, action string) {
	// .1 反序列化 RM 消息
	deleteSLBMessage, err := generateDeleteSLBMessage(msg)
	if err != nil {
		logrus.Error("Slb delete fail, err", err.Error())
		return
	}
	slbResourceId := dao.SlbResourceId(deleteSLBMessage.SubscriptionName, deleteSLBMessage.ResourceGroupName, deleteSLBMessage.Zone, deleteSLBMessage.SlbName)
	slbRecord, err := dao.GetSLBByResourceID(BosonProvider.Engine, slbResourceId, true)
	if err != nil {
		logrus.Errorln("Slb delete fail.", "GetSLBByNameRetry err:", err.Error())
		return
	}

	err = slbDeleteProcess(msg, slbRecord, method, action, deleteSLBMessage.TenantCode)
	if err != nil {
		logrus.Errorln("Slb delete fail.", "delete process err:", err.Error())
		return
	}
}

func slbExpireStop(msg *types.CloudEventMsg, method string, action string) {
	var err error = nil
	var message *higgsMsgv1.Notification = nil
	var isActionError bool = false
	var slbRecord *db.Slbs = nil
	defer func() {
		slbExpireStopDefer(err, msg, message, isActionError, slbRecord, method, action)
	}()

	message, err = generateNotifyMessage(msg)
	if err != nil {
		isActionError = true
		return
	}

	slbRecord, err = dao.GetSLBByResourceID(BosonProvider.Engine, message.Id, true)
	if err != nil {
		logrus.Errorln("Slb expire stop fail.", "GetSLBByNameRetry err:", err.Error())
		return
	}

	err = slbCRExpireStop(slbRecord)
	if err != nil {
		logrus.Errorln("Slb expire stop fail.", "SlbCRExpireStopping err:", err.Error())
		return
	}
}

func slbExpireStopDefer(
	err error, msg *types.CloudEventMsg,
	message *higgsMsgv1.Notification, isActionError bool,
	slbRecord *db.Slbs, method string, action string,
) {
	var resourceID = ""
	var mqRTMetrics *exporter.MQRTMetrics = exporter.InitMQRTMetrics("", "SLB", method)
	if slbRecord != nil {
		resourceID = slbRecord.ID
	}

	// .1 RM
	if err != nil {
		if resourceID != "" {
			BosonProvider.Processor.StateMessageSend(resourceID, slbStateTypeName(slbv1.SLB_FAILED))
		}
	}

	// .2 Metrics
	if mqRTMetrics == nil {
		errMsg := fmt.Sprintf(
			"SLB expire stop fail, mqRTMetrics:%p",
			mqRTMetrics,
		)
		logrus.Errorln(errMsg)
		return
	}
	if err == nil {
		mqRTMetrics.Result = types.ActionResultSucceeded
	}
	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": "",
		"user_id":   "",
		"service":   SLBServiceName,
		"method":    method,
		"action":    action,
		"data":      msg,
	}).Infof("SLB expire stop %s result %s", resourceID, mqRTMetrics.Result)
	mqRTMetrics.ObserveDurationAndLog()
}

func slbRenewStart(msg *types.CloudEventMsg, method string, action string) {
	var err error = nil
	var message *higgsMsgv1.Notification = nil
	var isActionError bool = false
	var slbRecord *db.Slbs = nil
	defer func() {
		slbRenewStartDefer(err, msg, message, isActionError, slbRecord, method, action)
	}()
	message, err = generateNotifyMessage(msg)
	if err != nil {
		isActionError = true
		return
	}

	slbRecord, err = dao.GetSLBByResourceID(BosonProvider.Engine, message.Id, true)
	if err != nil {
		logrus.Errorln("Slb renew start fail.", "GetSLBByNameRetry err:", err.Error())
		return
	}

	err = slbCRRenewStart(slbRecord)
	if err != nil {
		logrus.Errorln("Slb renew start fail.", "SlbCRRenewStart err:", err.Error())
		return
	}
}

func slbRenewStartDefer(
	err error, msg *types.CloudEventMsg,
	message *higgsMsgv1.Notification, isActionError bool,
	slbRecord *db.Slbs, method string, action string,
) {
	var resourceID = ""
	var mqRTMetrics *exporter.MQRTMetrics = exporter.InitMQRTMetrics("", "SLB", method)
	if slbRecord != nil {
		resourceID = slbRecord.ID
	}

	// .1 RM
	if err != nil {
		if resourceID != "" {
			BosonProvider.Processor.StateMessageSend(resourceID, slbStateTypeName(slbv1.SLB_FAILED))
		}
	}

	// .2 Metrics
	if mqRTMetrics == nil {
		errMsg := fmt.Sprintf("SLB expire stop fail, mqRTMetrics:%p", mqRTMetrics)
		logrus.Errorln(errMsg)
		return
	}
	if err == nil {
		mqRTMetrics.Result = types.ActionResultSucceeded
	}
	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": "",
		"user_id":   "",
		"service":   SLBServiceName,
		"method":    method,
		"action":    action,
		"data":      msg,
	}).Infof("SLB expire stop %s result %s", resourceID, mqRTMetrics.Result)
	mqRTMetrics.ObserveDurationAndLog()
}

func slbNotifyDelete(msg *types.CloudEventMsg, method string, action string) {
	message, err := generateNotifyMessage(msg)
	if err != nil {
		logrus.Error("Slb notify delete fail, err", err.Error())
		return
	}

	slbRecord, err := dao.GetSLBByResourceID(BosonProvider.Engine, message.Id, true)
	if err != nil {
		logrus.Errorln("Slb notify delete fail.", "GetSLBByNameRetry err:", err.Error())
		return
	}

	tenantCode := ""
	err = slbDeleteProcess(msg, slbRecord, method, action, tenantCode)
	if err != nil {
		logrus.Errorln("Slb notify delete fail.", "delete process err:", err.Error())
		return
	}
}

func slbUpdateCheck(updateSLBMessage *slbv1.UpdateSLBMessage) (*db.Slbs, *db.Vpcs, error) {
	// slb实例本身，是控制面资源，和云管侧已经对齐，需要每次传递的都是全量信息，因此不做 updateMask 字段

	engine := BosonProvider.Engine
	slbResourceId := dao.SlbResourceId(updateSLBMessage.SubscriptionName, updateSLBMessage.ResourceGroupName, updateSLBMessage.Zone, updateSLBMessage.SlbName)

	// .1 slb DB exist
	slbRecord, err := dao.GetSLBByResourceID(engine, slbResourceId, false)
	if err != nil {
		err := fmt.Errorf("Slb update check fail. GetSLBByResourceID err:%s\n", err.Error())
		logrus.Errorln(err.Error())
		return nil, nil, err
	}

	// .2 vpc db
	vpc, err := dao.GetVpc(engine, slbRecord.VpcID)
	if err != nil {
		logrus.Errorln(err.Error())
		return nil, nil, err
	}

	// .3 IPVersion support
	if updateSLBMessage.Slb.IpVersion != slbv1.SLBIPVersion_IPV4 {
		err := fmt.Errorf(
			"Slb update check fail. IP version:%s not support\n",
			slbv1.SLBIPVersion_name[int32(updateSLBMessage.Slb.IpVersion)],
		)
		logrus.Errorln(err.Error())
		return nil, nil, err
	}

	// .4 eip check
	if updateSLBMessage.Slb.Type == slbv1.LoadBalancerType_INTERNET {
		if len(updateSLBMessage.Slb.EipId) != 0 {
			eipID := updateSLBMessage.Slb.EipId
			eipData, err := dao.GetEipByID(BosonProvider.Engine, eipID, true)
			if err != nil {
				logrus.Errorln("Slb update check fail.", "GetEipByID error", err.Error())
				return nil, nil, err
			}
			if eipData.AssociationType != eipv1.EIPProperties_AType_name[int32(eipv1.EIPProperties_SLB)] {
				logrus.Errorln("Slb update check fail.", "Eip type error", eipData.AssociationType)
				return nil, nil, err
			}

			slbRecordTmp, err := dao.GetSlbWithEip(BosonProvider.Engine, eipID)
			if err != nil {
				logrus.Errorln("Slb update check fail.", "Get slb with eipID fail", err)
				return nil, nil, err
			}
			if slbRecordTmp != nil {
				err := fmt.Errorf(
					"Slb:%s update check fail. EIP:%s already associate SLB:%s\n",
					slbRecord.SlbID, eipID, slbRecordTmp.SlbID,
				)
				logrus.Errorln(err)
				return nil, nil, err
			}
		}
	}

	// .5 INTERNET <-> INTRANET check
	if slbRecord.LoadBalancerType == slbTypeName(slbv1.LoadBalancerType_INTERNET) {
		if updateSLBMessage.Slb.Type == slbv1.LoadBalancerType_INTRANET {
			if len(slbRecord.EipID) != 0 {
				err := fmt.Errorf("Slb update check fail. Slb already associate eip:%s, need clear it", slbRecord.EipID)
				logrus.Errorln(err.Error())
				return nil, nil, err
			}
		}
	}

	// .Zone
	if slbRecord.Zone != updateSLBMessage.Zone {
		err := fmt.Errorf("Slb update check fail. .Zone:%s if invalid", updateSLBMessage.Zone)
		logrus.Errorln(err.Error())
		return nil, nil, err
	}

	return slbRecord, vpc, nil
}

func slbCRUpdate(updateSLBMessage *slbv1.UpdateSLBMessage, method string, eipCurr *db.Eips, slbDBOld *db.Slbs) error {
	// 仅支持更新 InstanceType、InternetVIP
	ctx := context.Background()

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSLB")
	slbOld, err := BosonProvider.BosonNetClient.SLBs().Get(ctx, SlbCRName(slbDBOld.Name), metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Slb CR update fail.", "GetSLB err:", err.Error())
		return err
	}

	slbNew := slbOld.DeepCopy()
	// .InstanceType
	slbNew.Spec.InstanceType = slbTypeName(updateSLBMessage.Slb.Type)
	// .InternetVIP
	if updateSLBMessage.Slb.Type == slbv1.LoadBalancerType_INTERNET {
		if eipCurr != nil {
			if slbNew.Spec.IPs.InternetVIP != eipCurr.EIPIP {
				slbNew.Spec.IPs.InternetVIP = eipCurr.EIPIP
			}
		} else {
			slbNew.Spec.IPs.InternetVIP = ""
		}
	} else {
		slbNew.Spec.IPs.InternetVIP = ""
	}

	return updateSlbSpec(slbOld, slbNew)
}

func slbDBUpdate(
	updateSLBMessage *slbv1.UpdateSLBMessage, method string, slbDBOld *db.Slbs,
) error {
	// 可更新的字段
	// .Description
	slbDBOld.Description = updateSLBMessage.Slb.Description
	// .DisplayName
	slbDBOld.DisplayName = updateSLBMessage.Slb.GetDisplayName()
	// .LoadBalancerType
	slbDBOld.LoadBalancerType = slbTypeName(updateSLBMessage.Slb.Type)
	// .EipID
	slbDBOld.EipID = updateSLBMessage.Slb.EipId

	engine := BosonProvider.Engine
	err := dao.UpdateSLB(engine, *slbDBOld)
	if err != nil {
		logrus.Errorln("SLB DB update fail.", "DB update err:", err.Error())
		return err
	}
	return nil
}

func slbAssociateEIPonUpdate(
	updateSLBMessage *slbv1.UpdateSLBMessage, slbRecord *db.Slbs, resource *slbResource,
) (*db.Eips, error) {
	unsetOldEIP := false
	setNewEIP := false

	if slbRecord.LoadBalancerType == slbTypeName(slbv1.LoadBalancerType_INTRANET) {
		if updateSLBMessage.Slb.Type == slbv1.LoadBalancerType_INTERNET {
			// .1 intranet --> internet
			if len(updateSLBMessage.Slb.EipId) != 0 {
				// a. 切换为公网，且设置了 EIP
				// set new EIP
				setNewEIP = true
			} else {
				// b. 切换为公网，没有设置 EIP
				// nothing, pass
				return nil, nil
			}
		} else {
			// .2 intranet --> intranet
			// nothing, pass
			return nil, nil
		}
	} else {
		if updateSLBMessage.Slb.Type == slbv1.LoadBalancerType_INTERNET {
			// .3 internet --> internet
			if len(slbRecord.EipID) != 0 {
				if len(updateSLBMessage.Slb.EipId) != 0 {
					if slbRecord.EipID != updateSLBMessage.Slb.EipId {
						// a. EIP 切换
						// a.1 unset old EIP
						unsetOldEIP = true
						// a.2 set new EIP
						setNewEIP = true
					} else {
						// b. EIP 不变
						// nothing, pass
						eipCurr, err := dao.GetEipByID(BosonProvider.Engine, slbRecord.EipID, true)
						if err != nil {
							logrus.Errorln(
								"Slb associate EIP onUpdate fail. get current eip error",
								"slb", slbRecord.SlbID, "eip", slbRecord.EipID,
							)
							return nil, err
						}
						return eipCurr, nil
					}
				} else {
					// c. 解绑 EIP
					// unset old EIP
					unsetOldEIP = true
				}
			} else {
				if len(updateSLBMessage.Slb.EipId) != 0 {
					// d. 设置新的 EIP
					// set new EIP
					setNewEIP = true
				} else {
					// e. 未绑定 EIP
					// nothing, pass
					return nil, nil
				}
			}
		} else {
			// .4 internet --> intranet
			if len(slbRecord.EipID) != 0 {
				// a. 公网切换为内网，存在已经绑定的EIP
				// unset old eip
				unsetOldEIP = true
			} else {
				// b. 公网切换为内网，不存在绑定的EIP
				// nothing, pass
				return nil, nil
			}
		}
	}

	if unsetOldEIP {
		if len(slbRecord.EipID) != 0 {
			err := unsetEip(slbRecord.EipID)
			if err != nil {
				logrus.Errorln(
					"Slb associate EIP onUpdate fail. unsetEIP error",
					"slb", slbRecord.SlbID, "eip", slbRecord.EipID,
				)
				return nil, err
			}
		}

	}

	if setNewEIP {
		if len(updateSLBMessage.Slb.EipId) != 0 {
			if resource == nil || resource.BasicNetworkVIP == nil {
				err := fmt.Errorf(
					"Slb associate EIP onUpdate fail. setEIP error, resource is nil, slb:%s eip:%s\n",
					slbRecord.SlbID, slbRecord.EipID,
				)
				logrus.Errorln(err.Error())
				return nil, err
			}

			eipNew, err := setEip(
				updateSLBMessage.Slb.EipId, resource.BasicNetworkVIP.Ip.SlbExternalIp,
			)
			if err != nil {
				logrus.Errorln(err.Error())
				return nil, err
			}
			return eipNew, nil
		}
	}

	return nil, nil
}

func slbDeleteProcess(msg *types.CloudEventMsg, slbRecord *db.Slbs, method string, action string, tenantCode string) error {
	var err error = nil
	var isDeleted bool = false
	var isActionError bool = false
	defer func() {
		slbDeleteDefer(err, isActionError, msg, method, action, slbRecord, isDeleted, tenantCode)
	}()

	if slbRecord.Deleted {
		logrus.Info("slb already deleted, slb ", slbRecord)
		isDeleted = true
		return nil
	}

	// .2 EIP update
	err = slbDissociateEIPonDelete(slbRecord)
	if err != nil {
		return err
	}

	// .3 资源释放
	// 资源释放在 slbStatusHandlerDeleting 中
	// pass

	// .4 CR delete
	err = slbCRDelete(slbRecord.Name)
	if err != nil {
		return err
	}

	// .5 db deleting
	engine := BosonProvider.Engine
	err = dao.SetSLBState(engine, slbRecord.SlbID, slbStateTypeName(slbv1.SLB_DELETING))
	if err != nil {
		logrus.Errorf(
			"Slb:%s delete fail. set db deleting err:%s",
			slbRecord.SlbID, slbStateTypeName(slbv1.SLB_DELETING),
		)
		return err
	}
	logrus.Infof("Slb :%s set status:%s", slbRecord.SlbID, slbStateTypeName(slbv1.SLB_DELETING))

	return nil
}

func slbDeleteDefer(
	err error, isActionError bool,
	msg *types.CloudEventMsg, method string, action string, slbRecord *db.Slbs, isDeleted bool,
	tenantCode string,
) {
	var resourceID = ""
	var mqRTMetrics *exporter.MQRTMetrics = exporter.InitMQRTMetrics(tenantCode, "SLB", method)
	if slbRecord != nil {
		resourceID = slbRecord.ID
	}

	// .1 RM
	if err != nil {
		if resourceID != "" {
			BosonProvider.Processor.StateMessageSend(resourceID, slbStateTypeName(slbv1.SLB_FAILED))
		}
	} else {
		if isDeleted {
			if resourceID != "" {
				BosonProvider.Processor.StateMessageSend(resourceID, slbStateTypeName(slbv1.SLB_FAILED))
			}
		}
	}

	// .2 Metrics
	if mqRTMetrics == nil {
		errMsg := fmt.Sprintf(
			"SLB delete fail, mqRTMetrics:%p",
			mqRTMetrics,
		)
		logrus.Errorln(errMsg)
		return
	}
	if err == nil {
		mqRTMetrics.Result = types.ActionResultSucceeded
	}
	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": "",
		"user_id":   "",
		"service":   SLBServiceName,
		"method":    method,
		"action":    action,
		"data":      msg,
	}).Infof("SLB delete %s result %s", resourceID, mqRTMetrics.Result)
	mqRTMetrics.ObserveDurationAndLog()
}

func slbDissociateEIPonDelete(slbRecord *db.Slbs) error {
	if len(slbRecord.EipID) != 0 {
		_, err := updateEipSourceIP(slbRecord.EipID, "")
		return err
	}

	return nil
}

func slbCRDelete(slbName string) error {
	ctx := context.Background()

	k8sRTMetrics := exporter.InitK8sRTMetrics("DeleteSLB")
	err := BosonProvider.BosonNetClient.SLBs().Delete(ctx, SlbCRName(slbName), metav1.DeleteOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Slb CR delete fail.", "DeleteSLB err:", err.Error())
		return err
	}

	return nil
}

func slbCRExpireStop(slbRecord *db.Slbs) error {
	ctx := context.Background()

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSLB")
	slbOld, err := BosonProvider.BosonNetClient.SLBs().Get(ctx, SlbCRName(slbRecord.Name), metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Slb CR expire stopping fail.", "GetSLB err:", err.Error())
		return err
	}

	slbNew := slbOld.DeepCopy()
	slbNew.Spec.OrderOpt = netControllerV1.OrderOptExpireStop

	return updateSlbSpec(slbOld, slbNew)
}

func slbCRRenewStart(slbRecord *db.Slbs) error {
	ctx := context.Background()

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSLB")
	slbOld, err := BosonProvider.BosonNetClient.SLBs().Get(ctx, SlbCRName(slbRecord.Name), metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Slb CR expire stopping fail.", "GetSLB err:", err.Error())
		return err
	}

	slbNew := slbOld.DeepCopy()
	slbNew.Spec.OrderOpt = netControllerV1.OrderOptRenewStart

	return updateSlbSpec(slbOld, slbNew)
}

func generateNotifyMessage(msg *types.CloudEventMsg) (*higgsMsgv1.Notification, error) {
	var message higgsMsgv1.Notification
	data, err := msg.GetData()
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(data, &message); err != nil {
		err := fmt.Errorf("slb service parse Notification error, err:%s", err.Error())
		logrus.Info(err.Error())
		return nil, err
	}

	return &message, nil
}

func generateCreateSLBMessage(msg *types.CloudEventMsg) (*slbv1.CreateSLBMessage, *types.CallbackData, error) {
	data, err := msg.GetData()
	if err != nil {
		return nil, nil, err
	}
	var createSLBMessage slbv1.CreateSLBMessage
	if err := json.Unmarshal(data, &createSLBMessage); err != nil {
		err := fmt.Errorf("slb service parse CreateSLBMessage error, err:%s", err.Error())
		logrus.Error(err)
		return nil, nil, err
	}

	// createSLBMessage 里面的 CallbackData 无效，使用 msg 的
	// fixme 需要删掉
	if msg.CallbackData == nil {
		err := fmt.Errorf("slb service parse CreateSLBMessage error, callbackData is nil")
		logrus.Error(err)
		return nil, nil, err

	}

	callbackData := &types.CallbackData{
		OrderID:  msg.CallbackData.OrderID,
		UserID:   msg.CallbackData.UserID,
		TenantID: msg.CallbackData.TenantID,
	}

	return &createSLBMessage, callbackData, nil
}

func slbCreateCheck(createSLBMessage *slbv1.CreateSLBMessage) (*db.Vpcs, error) {
	engine := BosonProvider.Engine

	// args
	// .id
	slbResourceID := dao.SlbResourceId(createSLBMessage.SubscriptionName, createSLBMessage.ResourceGroupName, createSLBMessage.Zone, createSLBMessage.SlbName)
	if slbResourceID != createSLBMessage.Slb.Id {
		err := fmt.Errorf(
			"Slb create check fail, create message id error, subscriptionName:%s, resourceGroupName:%s, zone:%s, slbName:%s, id:%s",
			createSLBMessage.SubscriptionName, createSLBMessage.ResourceGroupName, createSLBMessage.Zone, createSLBMessage.SlbName,
			createSLBMessage.Slb.Id,
		)
		logrus.Errorln("Slb create check fail.", "CheckSLBByID err:", err.Error())
		return nil, err
	}

	// quota
	err := slbCreateQuotaCheck(createSLBMessage)
	if err != nil {
		logrus.Errorln("Slb create check fail.", "quota err:", err.Error())
		return nil, err
	}

	// .1 slb DB not exist
	has, err := dao.CheckSLBByResourceId(engine, createSLBMessage.Slb.Id)
	if err != nil {
		logrus.Errorln("Slb create check fail.", "CheckSLBByID err:", err.Error())
		return nil, err
	}
	if has {
		err = fmt.Errorf(
			"Slb create check fail.Slb exist, id:%s, name:%s", createSLBMessage.Slb.Uid, createSLBMessage.Slb.Name,
		)
		logrus.Errorln(err.Error())
		return nil, err
	}

	// .2 vpc DB exist
	vpc, err := dao.GetVpc(engine, createSLBMessage.Slb.Properties.VpcId)
	if err != nil {
		logrus.Errorln("Slb create check fail.", "GetVpc err:", err.Error())
		return nil, err
	}

	// .3 IPVersion support
	if createSLBMessage.Slb.Properties.IpVersion != slbv1.SLBIPVersion_IPV4 {
		err := fmt.Errorf(
			"Slb create check fail. IP version:%s not support",
			slbv1.SLBIPVersion_name[int32(createSLBMessage.Slb.Properties.IpVersion)],
		)
		logrus.Errorln(err.Error())
		return nil, err
	}

	// .4 eip check
	if createSLBMessage.Slb.Properties.Type == slbv1.LoadBalancerType_INTERNET {
		if len(createSLBMessage.Slb.Properties.EipId) != 0 {
			eipID := createSLBMessage.Slb.Properties.EipId
			eipData, err := dao.GetEipByID(BosonProvider.Engine, eipID, true)
			if err != nil {
				logrus.Errorln("Slb create check fail.", "GetEipByID error", err.Error())
				return nil, err
			}
			if eipData.AssociationType != eipv1.EIPProperties_AType_name[int32(eipv1.EIPProperties_SLB)] {
				err := fmt.Errorf("Slb create check fail. Eip type:%s error", eipData.AssociationType)
				logrus.Errorln(err)
				return nil, err
			}

			slbRecordTmp, err := dao.GetSlbWithEip(BosonProvider.Engine, eipID)
			if err != nil {
				logrus.Errorln(err)
				return nil, err
			}
			if slbRecordTmp != nil {
				err := fmt.Errorf(
					"Slb create check fail. EIP:%s already associate SLB:%s\n",
					eipID, slbRecordTmp.SlbID,
				)
				logrus.Errorln(err)
				return nil, err
			}
		}
	}

	return vpc, nil
}

func slbAllocateNetworkIp(ipType string) (*slbExternalIpInfo, error) {
	engine := BosonProvider.Engine

	basicNetworkIP, err := dao.AllocateSLBNetworkIP(engine, ipType)
	if err != nil {
		logrus.Errorln("Slb allocate resource fail.", "Allocate basic network ip err:", err.Error(), "ip type", ipType)
		return nil, err
	}

	cidr, err := dao.GetCidrPoolByCidrID(engine, basicNetworkIP.CidrID)
	if err != nil {
		logrus.Errorln("Slb allocate resource fail.", "GetCidrPoolByCidrID err:", err.Error(), "basicNetworkIP", basicNetworkIP, "ip type", ipType)
		return nil, err
	}
	return &slbExternalIpInfo{
		Ip:   basicNetworkIP,
		Cidr: cidr,
	}, nil
}

func slbAllocateResource(vpc *db.Vpcs, caps *slbv1.CapacityLimitations) (*slbResource, error) {
	var err error = nil
	var basicNetworkIPsInfo = []*slbExternalIpInfo{}
	var basicNetworkVIPInfo *slbExternalIpInfo = nil
	var intranetVIP = ""
	defer func() {
		if err != nil {
			slbAllocateResourceDefer(err, basicNetworkIPsInfo, basicNetworkVIPInfo, intranetVIP)
		}
	}()

	// BasicNetworkIP
	replica := slbGetReplica(caps)
	for i := 0; i < replica; i++ {
		ip, err := slbAllocateNetworkIp(string(db.Communication))
		if err != nil {
			return nil, err
		}

		basicNetworkIPsInfo = append(basicNetworkIPsInfo, ip)
	}

	// BasicNetworkVIP
	basicNetworkVIPInfo, err = slbAllocateNetworkIp(string(db.VirtualIP))
	if err != nil {
		return nil, err
	}

	// IntranetVIP
	// 在 boson-net-controller 里面分配
	intranetVIP, err = slbAllocateIntranetVIP()
	if err != nil {
		logrus.Errorln("Slb allocate resource fail.", "allocate intranet vip err:", err.Error())
		return nil, err
	}

	// Subnet
	subnetName, err := getVpcOvnServiceSubnetByVpcID(vpc.VPCID)
	if err != nil {
		return nil, err
	}

	resource := &slbResource{
		BasicNetworkIPs: basicNetworkIPsInfo,
		BasicNetworkVIP: basicNetworkVIPInfo,
		Subnet:          subnetName,
	}

	logrus.Info("Slb allocate resource success. ", "resource", resource.String())

	return resource, nil
}

func slbAllocateResourceDefer(
	err error,
	basicNetworkIPs []*slbExternalIpInfo, basicNetworkVIP *slbExternalIpInfo, intranetVIP string,
) {
	if err == nil {
		return
	}
	engine := BosonProvider.Engine
	if len(basicNetworkIPs) != 0 {
		for _, basicNetworkIP := range basicNetworkIPs {
			logrus.Infoln("Slb allocate resource fail.", "release basicNetworkIP", basicNetworkIP.Ip.SlbExternalIp)
			if errNew := dao.ReleaseSLBNetworkIP(engine, basicNetworkIP.Ip.SlbExternalIpID); errNew != nil {
				logrus.Errorln("Slb allocate resource fail.", "rollback fail.", "ReleaseSLBNetworkIP")
			}
		}
	}
	if basicNetworkVIP != nil {
		logrus.Infoln("Slb allocate resource fail.", "release basicNetworkVIP", basicNetworkVIP.Ip.SlbExternalIp)
		if errNew := dao.ReleaseSLBNetworkIP(engine, basicNetworkVIP.Ip.SlbExternalIpID); errNew != nil {
			logrus.Errorln("Slb allocate resource fail.", "rollback fail.", "ReleaseSLBNetworkIP")
		}
	}
	if len(intranetVIP) != 0 {
		logrus.Infoln("Slb allocate resource fail.", "release intranetVIP", intranetVIP)
		if errNew := slbReleaseIntranetVIP(intranetVIP); errNew != nil {
			logrus.Errorln("Slb allocate resource fail.", "rollback fail.", "ReleaseIntranetVIP")
		}
	}
}

func slbFetchResource(
	slbRecord *db.Slbs, updateSLBMessage *slbv1.UpdateSLBMessage, vpc *db.Vpcs,
) (*slbResource, error) {
	resource, err := slbGetResource(slbRecord, vpc)
	if err != nil {
		logrus.Errorln("Slb fetch resource fail.", "[internet --> internet]", "get resource err:", err.Error())
		return nil, err
	}
	return resource, nil
}

func slbGetResource(slb *db.Slbs, vpc *db.Vpcs) (*slbResource, error) {
	resource := &slbResource{}

	if len(slb.BasicNetworkIpIDs) != 0 {
		basicNetworkIPs, err := getBasicNetworkIPs(slb.BasicNetworkIpIDs)
		if err != nil {
			logrus.Errorln("Slb get resource fail.", "GetBasicNetworkIPs basic network ip error,",
				"BasicNetworkIpIDs", slb.BasicNetworkIpIDs, "err", err.Error(),
			)
			return nil, err
		}
		resource.BasicNetworkIPs = basicNetworkIPs
	}

	if len(slb.BasicNetworkVipID) != 0 {
		basicNetworkVIP, err := GetBasicNetworkVip(slb.BasicNetworkVipID)
		if err != nil {
			logrus.Errorln("Slb get resource fail.", "GetSLBNetworkIPByID basic network vip error,",
				"BasicNetworkVipID", slb.BasicNetworkVipID, "err", err.Error(),
			)
			return nil, err
		}
		resource.BasicNetworkVIP = basicNetworkVIP
	}

	subnetName, err := getVpcOvnServiceSubnetByVpcID(vpc.VPCID)
	if err != nil {
		return nil, err
	}
	resource.Subnet = subnetName

	return resource, nil
}

func getVpcOvnServiceSubnetByVpcID(vpcID string) (string, error) {
	engine := BosonProvider.Engine

	sn, err := dao.GetVpcOvnServiceSubnet(engine, vpcID)
	if err != nil {
		logrus.Errorln("Get vpc ovn-service-subnet fail. err:", err.Error())
		return "", err
	}
	return sn.Name, nil
}

func slbAllocateIntranetVIP() (string, error) {
	// 需要单独的 subnet
	// fixme not support
	intranetVIP := "127.0.0.1"
	return intranetVIP, nil
}

func slbReleaseIntranetVIP(ip string) error {
	// fixme not support
	return nil
}

func slbReleaseResource(resource *slbResource) {
	engine := BosonProvider.Engine

	if resource == nil {
		return
	}

	if resource.BasicNetworkIPs != nil && len(resource.BasicNetworkIPs) != 0 {
		for _, basicNetworkIP := range resource.BasicNetworkIPs {
			if err := dao.ReleaseSLBNetworkIP(engine, basicNetworkIP.Ip.SlbExternalIpID); err != nil {
				logrus.Errorln("Slb release resource fail.", "BasicNetworkIP fail.", resource.BasicNetworkIPs)
			}
		}
	}

	if resource.BasicNetworkVIP != nil {
		if err := dao.ReleaseSLBNetworkIP(engine, resource.BasicNetworkVIP.Ip.SlbExternalIpID); err != nil {
			logrus.Errorln("Slb release resource fail.", "BasicNetworkVIP fail.", resource.BasicNetworkVIP.Ip.SlbExternalIpID)
		}
	}
}

func SlbReleaseResource(slbName string) error {
	engine := BosonProvider.Engine

	// .1 网络资源
	slbRecord, err := dao.GetSLBByName(engine, slbName, true)
	if err != nil {
		logrus.Errorln("SLB release resource fail.", "error", err.Error())
		return err
	}

	basicNetworkIPs := []*slbExternalIpInfo{}
	if len(slbRecord.BasicNetworkIpIDs) != 0 {
		basicNetworkIPs, err = getBasicNetworkIPs(slbRecord.BasicNetworkIpIDs)
		if err != nil {
			logrus.Errorln("SLB release resource fail.", slbRecord.BasicNetworkIpIDs, "get basicNetworkIPs error", err.Error())
		}
	}

	basicNetworkVIP, err := GetBasicNetworkVip(slbRecord.BasicNetworkVipID)
	if err != nil {
		logrus.Errorln("SLB release resource fail.", slbRecord.BasicNetworkVipID, "get basicNetworkVIP error", err.Error())
	}

	resource := &slbResource{
		BasicNetworkIPs: basicNetworkIPs,
		BasicNetworkVIP: basicNetworkVIP,
		Subnet:          "", // 不需要
	}

	slbReleaseResource(resource)

	// .2 监听器
	err = dao.ClearSlbListenerBySlbId(engine, slbRecord.SlbID)
	if err != nil {
		logrus.Errorln("SLB release resource fail.", "clear Listener DB err", err.Error())
	}

	// .3 后端
	err = dao.ClearSlbTargetBySlbId(engine, slbRecord.SlbID)
	if err != nil {
		logrus.Errorln("SLB release resource fail.", "clear Targets DB err", err.Error())
	}

	// .4 后端组
	err = dao.ClearSlbTargetGroupBySlbId(engine, slbRecord.SlbID)
	if err != nil {
		logrus.Errorln("SLB release resource fail.", "clear TargetGroups DB err", err.Error())
	}

	// .5 关联关系
	err = dao.ClearSlbAssociationsBySlbId(engine, slbRecord.SlbID)
	if err != nil {
		logrus.Errorln("SLB release resource fail.", "clear SlbAssociations DB err", err.Error())
	}

	// .x self
	err = dao.DeleteSLB(engine, slbRecord.SlbID, slbStateTypeName(slbv1.SLB_DELETED))
	if err != nil {
		logrus.Errorln("SLB release resource fail.", "delete slb DB err", err.Error())
	}
	return nil
}

func slbCRCreate(
	createSLBMessage *slbv1.CreateSLBMessage, method string,
	resource *slbResource, callbackData *types.CallbackData, eip *db.Eips,
) error {
	ctx := context.Background()

	vpc, err := getVpcCR(createSLBMessage.Slb.Properties.VpcId)
	if err != nil {
		return err
	}

	instanceType := slbTypeName(createSLBMessage.Slb.Properties.Type)

	capsCreate := createSLBMessage.Slb.Properties.Resources.CapacityLimitations
	caps := netControllerV1.SLBCapacityLimitations{
		TcpCps:      capsCreate.TcpCps,
		TcpConns:    capsCreate.TcpConns,
		UdpCps:      capsCreate.UdpCps,
		UdpConns:    capsCreate.UdpConns,
		TcpsslCps:   capsCreate.TcpsslCps,
		TcpsslConns: capsCreate.TcpsslConns,
		HttpCps:     capsCreate.HttpCps,
		HttpConns:   capsCreate.HttpConns,
		HttpQps:     capsCreate.HttpQps,
		HttpsCps:    capsCreate.HttpsCps,
		HttpsConns:  capsCreate.HttpsConns,
		HttpsQps:    capsCreate.HttpsQps,
	}

	basicNetworkIpCIRDs, err := generateBasicNetworkIpCIRDs(resource.BasicNetworkIPs)
	if err != nil {
		logrus.Errorln("Slb create CR:SLB fail, GenerateBasicNetworkIpCIRDs err: ", err.Error())
		return err
	}
	basicNetworkIpGw := resource.BasicNetworkIPs[0].Cidr.GatewayIP

	basicNetworkVipCIRD, err := generateBasicNetworkVipCIRD(resource.BasicNetworkVIP)
	if err != nil {
		logrus.Errorln("Slb create CR:SLB fail, GenerateBasicNetworkVipCIRD err: ", err.Error())
		return err
	}
	basicNetworkVipGw := resource.BasicNetworkVIP.Cidr.GatewayIP

	ips := netControllerV1.SLBInstanceIPs{
		InternetVIP:       "",
		BasicNetworkVIP:   basicNetworkVipCIRD,
		BasicNetworkVipGw: basicNetworkVipGw,
		BasicNetworkIPs:   sortBasicNetworkIPs(basicNetworkIpCIRDs),
		BasicNetworkIpGw:  basicNetworkIpGw,
	}
	if instanceType == slbTypeName(slbv1.LoadBalancerType_INTERNET) && eip != nil {
		ips.InternetVIP = eip.EIPIP
	}

	annos := map[string]string{
		AnnoResourceOrderID:  callbackData.OrderID,
		AnnoResourceTenantID: callbackData.TenantID,
		AnnoResourceUserID:   callbackData.UserID,
		AnnoRegion:           db.Region,
		AnnoAz:               db.AZ,
	}

	slb := &netControllerV1.SLB{
		TypeMeta: metav1.TypeMeta{
			Kind:       SlbCRDKindName,
			APIVersion: netControllerV1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: SlbCRName(createSLBMessage.Slb.Name),
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    VPCKind,
				}),
			},
			Annotations: annos,
		},
		Spec: netControllerV1.SLBSpec{
			Name:                SlbCRName(createSLBMessage.Slb.Name),
			InstanceType:        instanceType,
			CapacityLimitations: caps,
			Replica:             len(resource.BasicNetworkIPs),
			OrderOpt:            "", // default,  types.RESOURCE_MSG_XXX 的时候使用
			Subnet:              resource.Subnet,
			Selector:            nil, // default, 赋值需要排序
			TenantID:            createSLBMessage.Slb.TenantId,
			IPs:                 ips,
		},
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics(method)
	_, err = BosonProvider.BosonNetClient.SLBs().Create(ctx, slb, metav1.CreateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Slb create CR:SLB fail, err: ", err.Error())
		return err
	}
	return nil
}

func slbDBCreate(createSLBMessage *slbv1.CreateSLBMessage, method string, resource *slbResource) error {
	engine := BosonProvider.Engine

	slb := db.Slbs{
		// -- 基础信息
		SlbID:        createSLBMessage.Slb.Uid,
		Name:         createSLBMessage.Slb.Name,
		Zone:         createSLBMessage.Slb.Zone,
		State:        "", // 在 infromer 里面赋值
		DisplayName:  createSLBMessage.Slb.DisplayName,
		Description:  createSLBMessage.Slb.Description,
		ResourceType: createSLBMessage.Slb.ResourceType,
		// --
		LoadBalancerType: slbTypeName(createSLBMessage.Slb.Properties.Type),
		VpcID:            createSLBMessage.Slb.Properties.VpcId,
		IPVersion:        slbv1.SLBIPVersion_name[int32(createSLBMessage.Slb.Properties.IpVersion)],
		EipID:            createSLBMessage.Slb.Properties.EipId,
		// -- IP 信息
		BasicNetworkIpIDs: generateBasicNetworkIpIDs(resource.BasicNetworkIPs),
		BasicNetworkVipID: resource.BasicNetworkVIP.Ip.SlbExternalIpID,
		// -- 资源规格属性
		TcpCps:      int64(createSLBMessage.Slb.Properties.Resources.CapacityLimitations.TcpCps),
		TcpConns:    int64(createSLBMessage.Slb.Properties.Resources.CapacityLimitations.TcpConns),
		UdpCps:      int64(createSLBMessage.Slb.Properties.Resources.CapacityLimitations.UdpCps),
		UdpConns:    int64(createSLBMessage.Slb.Properties.Resources.CapacityLimitations.UdpConns),
		TcpsslCps:   int64(createSLBMessage.Slb.Properties.Resources.CapacityLimitations.TcpsslCps),
		TcpsslConns: int64(createSLBMessage.Slb.Properties.Resources.CapacityLimitations.TcpConns),
		HttpCps:     int64(createSLBMessage.Slb.Properties.Resources.CapacityLimitations.HttpCps),
		HttpConns:   int64(createSLBMessage.Slb.Properties.Resources.CapacityLimitations.HttpConns),
		HttpQps:     int64(createSLBMessage.Slb.Properties.Resources.CapacityLimitations.HttpQps),
		HttpsCps:    int64(createSLBMessage.Slb.Properties.Resources.CapacityLimitations.HttpsCps),
		HttpsConns:  int64(createSLBMessage.Slb.Properties.Resources.CapacityLimitations.HttpsConns),
		HttpsQps:    int64(createSLBMessage.Slb.Properties.Resources.CapacityLimitations.HttpsQps),
		// -- 其他公共字段
		ID:         createSLBMessage.Slb.Id,
		CreatorID:  createSLBMessage.Slb.CreatorId,
		OwnerID:    createSLBMessage.Slb.OwnerId,
		TenantID:   createSLBMessage.Slb.TenantId,
		CreateTime: createSLBMessage.Slb.CreateTime.AsTime(),
		UpdateTime: createSLBMessage.Slb.UpdateTime.AsTime(),
		Deleted:    false,
		Tags:       "",
		Sku:        createSLBMessage.Slb.SkuId,
	}
	err := dao.InsertSLB(engine, slb)
	if err != nil {
		logrus.Errorln("Slb DB create fail.", "InsertSLB err:", err.Error())
		return err
	}
	return nil
}

func slbAssociateEIPonCreate(
	createSLBMessage *slbv1.CreateSLBMessage, resource *slbResource,
) (*db.Eips, error) {
	if createSLBMessage.Slb.Properties.Type != slbv1.LoadBalancerType_INTERNET {
		// 非 外网类型，不处理和EIP的关系
		logrus.Info(
			"Slb skip eip process",
			"slb", createSLBMessage.SlbName, "type", createSLBMessage.Slb.Properties.Type.String(),
		)
		return nil, nil
	}

	if len(createSLBMessage.Slb.Properties.EipId) == 0 {
		// 还未绑定 EIP
		logrus.Info("Slb skip eip process. eip is nil", "slb", createSLBMessage.SlbName)
		return nil, nil
	}

	return updateEipSourceIP(
		createSLBMessage.Slb.Properties.EipId,
		resource.BasicNetworkVIP.Ip.SlbExternalIp,
	)
}

func unsetEip(eipID string) error {
	if _, err := updateEipSourceIP(eipID, ""); err != nil {
		logrus.Errorln("Unset EIP err:", err.Error())
		return err
	}

	return nil
}

func setEip(eipID string, basicNetworkVIP string) (*db.Eips, error) {
	return updateEipSourceIP(eipID, basicNetworkVIP)
}

func updateEipSourceIP(eipID string, sourceIP string) (*db.Eips, error) {
	eipData, err := dao.GetEipByID(BosonProvider.Engine, eipID, true)
	if err != nil {
		logrus.Errorln("SLB associate EIP fail.", "Get eip db error", err.Error())
		return nil, err
	}
	if eipData.AssociationType != eipv1.EIPProperties_AType_name[int32(eipv1.EIPProperties_SLB)] {
		logrus.Errorln("SLB associate EIP fail.", "Eip type error", eipData.AssociationType)
		return nil, err
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetEIP")
	eipOld, err := BosonProvider.BosonNetClient.EIPs().Get(
		context.Background(), SlbCRName(eipData.Name), metav1.GetOptions{},
	)
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("SLB associate EIP fail.", "GetEIP err:", err.Error())
		return nil, err
	}
	eipNew := eipOld.DeepCopy()
	eipNew.Spec.SourceIP = sourceIP
	if !reflect.DeepEqual(eipOld.Spec, eipNew.Spec) {
		_, err = BosonProvider.BosonNetClient.EIPs().Update(
			context.Background(), eipNew, metav1.UpdateOptions{},
		)
		if err != nil {
			logrus.Errorln("SLB associate EIP fail.", "Update eip cr error", err.Error())
			return nil, err
		}
	}

	return eipData, nil
}

func generateUpdateSLBMessage(msg *types.CloudEventMsg) (*slbv1.UpdateSLBMessage, error) {
	var updateSLBMessage slbv1.UpdateSLBMessage
	data, err := msg.GetData()
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(data, &updateSLBMessage); err != nil {
		err := fmt.Errorf("slb service parse UpdateSLBMessage error, err:%s", err.Error())
		logrus.Info(err)
		return nil, err
	}

	return &updateSLBMessage, nil
}

func generateDeleteSLBMessage(msg *types.CloudEventMsg) (*slbv1.DeleteSLBMessage, error) {
	var deleteSLBMessage slbv1.DeleteSLBMessage
	data, err := msg.GetData()
	if err != nil {
		return nil, err
	}
	if err := json.Unmarshal(data, &deleteSLBMessage); err != nil {
		errMsg := fmt.Sprintf("slb service parse DeleteSLBMessage error, err:%s", err.Error())
		logrus.Info(errMsg)
		return nil, errors.New(errMsg)
	}

	return &deleteSLBMessage, nil
}

func eipDissociateSlb(slbResourceID string, eipID string) error {
	// .1 check
	engine := BosonProvider.Engine
	slbRecord, err := dao.GetSLBByResourceID(engine, slbResourceID, false)
	if err != nil {
		logrus.Errorln("SLB unset eip fail. GetSLBByID err:", err.Error())
		return err
	}

	if slbRecord.LoadBalancerType != slbTypeName(slbv1.LoadBalancerType_INTERNET) {
		return nil
	}

	if len(slbRecord.EipID) == 0 {
		return nil
	}

	// .2 update slb cr
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetSLB")
	slbOld, err := BosonProvider.BosonNetClient.SLBs().Get(
		context.Background(), SlbCRName(slbRecord.Name), metav1.GetOptions{},
	)
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Slb CR update fail.", "GetSLB err:", err.Error())
		return err
	}
	slbNew := slbOld.DeepCopy()
	slbNew.Spec.IPs.InternetVIP = ""
	err = updateSlbSpec(slbOld, slbNew)
	if err != nil {
		logrus.Errorln("Slb CR update fail.", "update fail", err.Error())
		return err
	}

	// .3 update slb db
	slbRecord.EipID = ""
	err = dao.UpdateSLB(BosonProvider.Engine, *slbRecord)
	if err != nil {
		logrus.Errorln("Slb unset eip fail.", "update db record error", err.Error())
		return err
	}

	return nil
}

func updateSlbSpec(old *netControllerV1.SLB, new *netControllerV1.SLB) error {
	if reflect.DeepEqual(old.Spec, new.Spec) {
		return nil
	}
	k8sRTMetrics := exporter.InitK8sRTMetrics("UpdateSLB")
	_, err := BosonProvider.BosonNetClient.SLBs().Update(context.Background(), new, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Slb update CR:SLB fail, err: ", err.Error())
		return err
	}
	return nil
}

const (
	SLBServiceName = "SLBs"
	SlbCRDKindName = "slbs"
)

type slbResource struct {
	BasicNetworkIPs []*slbExternalIpInfo `json:"basicNetworkIPs"`
	BasicNetworkVIP *slbExternalIpInfo   `json:"basicNetworkVIP"`
	IntranetVIP     string               `json:"intranetVIP"` // 【废弃】，仅保留字段
	Subnet          string               `json:"subnet"`
}

type slbExternalIpInfo struct {
	Ip   *db.SlbExternalIpPools `json:"ip"`
	Cidr *db.CidrPools          `json:"cidr"`
}

func (r *slbResource) String() string {
	data, _ := json.Marshal(r)
	return string(data)
}

func getVpcCR(vpcID string) (*ovn.Vpc, error) {
	vpcData, err := dao.GetVpc(BosonProvider.Engine, vpcID)
	if err != nil {
		logrus.Errorln("Get VPC CR fail.", "DB GetVpc err:", err.Error())
		return nil, err
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetVPC")
	vpc, err := BosonProvider.KubeovnClient.Vpcs().Get(context.Background(), vpcData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln("Get VPC CR fail.", "CR ovn Get err:", err.Error())
		return nil, err
	}

	return vpc, nil
}

func SlbCRName(slbName string) string {
	return slbName
}

func slbTypeName(slbType slbv1.LoadBalancerType) string {
	return slbv1.LoadBalancerType_name[int32(slbType)]
}

func slbStateTypeName(slbStateType slbv1.SLB_State) string {
	return slbv1.SLB_State_name[int32(slbStateType)]
}

func slbGetReplica(caps *slbv1.CapacityLimitations) int {
	var replica int
	if !BosonProvider.RpConfig.Boson.BgpEnable {
		replica = 1
	} else {
		replica = BosonProvider.RpConfig.Boson.SlbReplica
	}

	return replica
}

const basicNetworkIpIDSeparator = ";"

func parseBasicNetworkIpIDs(basicNetworkIpIDs string) []string {
	return strings.Split(basicNetworkIpIDs, basicNetworkIpIDSeparator)
}

func generateBasicNetworkIpIDs(basicNetworkIPs []*slbExternalIpInfo) string {
	ids := []string{}
	for _, basicNetworkIP := range basicNetworkIPs {
		ids = append(ids, basicNetworkIP.Ip.SlbExternalIpID)
	}

	return strings.Join(ids, basicNetworkIpIDSeparator)
}

func generateBasicNetworkIpCIRDs(basicNetworkIPs []*slbExternalIpInfo) ([]string, error) {
	if len(basicNetworkIPs) == 0 {
		err := fmt.Errorf("generate basic network ip cird list fail, resource is nil")
		return nil, err
	}
	ipCIRDs := []string{}

	for _, basicNetworkIP := range basicNetworkIPs {
		natGwEIPAddr, err := utils.MergeIPToCIDR(basicNetworkIP.Ip.SlbExternalIp, basicNetworkIP.Cidr.CIDR)
		if err != nil {
			logrus.Errorln("GenerateBasicNetworkIpCIRDs fail.", "MergeIPToCIDR err:", err.Error())
			return []string{}, nil
		}

		ipCIRDs = append(ipCIRDs, natGwEIPAddr)
	}

	return ipCIRDs, nil
}

func generateBasicNetworkVipCIRD(basicNetworkVIP *slbExternalIpInfo) (string, error) {
	natGwEIPAddr, err := utils.MergeIPToCIDR(basicNetworkVIP.Ip.SlbExternalIp, basicNetworkVIP.Cidr.CIDR)
	if err != nil {
		logrus.Errorln("generateBasicNetworkVipCIRD fail.", "MergeIPToCIDR err:", err.Error())
		return "", nil
	}

	return natGwEIPAddr, nil
}

func GetBasicNetworkVip(basicNetworkVipID string) (*slbExternalIpInfo, error) {
	return getSlbExternalIpInfo(basicNetworkVipID)
}

func getSlbExternalIpInfo(ipID string) (*slbExternalIpInfo, error) {
	engine := BosonProvider.Engine

	ip, err := dao.GetSLBNetworkIPByID(engine, ipID)
	if err != nil {
		logrus.Errorln("Slb get resource fail.", "GetSLBNetworkIPByID basic network vip error,",
			"ipID", ipID, "err", err.Error(),
		)
		return nil, err
	}
	cidr, err := dao.GetCidrPoolByCidrID(engine, ip.CidrID)
	if err != nil {
		logrus.Errorln("Slb get resource fail.", "GetCidrPoolByCidrID basic network ip error,",
			"ipID", ipID, "CidrID", ip.CidrID, "err", err.Error(),
		)
		return nil, err
	}
	return &slbExternalIpInfo{
		Ip: ip, Cidr: cidr,
	}, nil
}

func getBasicNetworkIPs(basicNetworkIpIDs string) ([]*slbExternalIpInfo, error) {
	if len(basicNetworkIpIDs) == 0 {
		return []*slbExternalIpInfo{}, nil
	}

	basicNetworkIPs := []*slbExternalIpInfo{}
	for _, basicNetworkIpID := range parseBasicNetworkIpIDs(basicNetworkIpIDs) {
		ipInfo, err := getSlbExternalIpInfo(basicNetworkIpID)
		if err != nil {
			logrus.Errorln("Slb get resource fail.", "getSlbExternalIpInfo basic network ip error,",
				"BasicNetworkIpIDs", basicNetworkIpIDs, "basicNetworkIpID", basicNetworkIpID, "err", err.Error(),
			)
			return nil, err
		}

		basicNetworkIPs = append(basicNetworkIPs, ipInfo)
	}

	return basicNetworkIPs, nil
}

func slbCreateQuotaCheck(createSLBMessage *slbv1.CreateSLBMessage) error {
	engine := BosonProvider.Engine
	vpcID := createSLBMessage.Slb.Properties.VpcId
	vpcQuota, err := dao.GetVpcQuotaByVpcID(engine, vpcID)
	if err != nil {
		logrus.Errorf("vpc:%s slb:%s create fail, get quota err:%s", vpcID, createSLBMessage.SlbName, err)
		return err
	}

	slbsRecord, err := dao.ListSLBsByVpcId(engine, vpcID)
	if err != nil {
		logrus.Errorf("vpc:%s slb:%s create fail, get slb list err:%s", vpcID, createSLBMessage.SlbName, err)
		return err
	}
	count := len(slbsRecord)
	if count >= int(vpcQuota.SlbPerVpc) {
		err = fmt.Errorf(
			"vpc:%s slb:%s create fail, limit exceeded, quota:%d current count:%d",
			vpcID, createSLBMessage.SlbName, vpcQuota.SlbPerVpc, count,
		)
		logrus.Errorln(err)
		return err
	}

	return nil
}

func sortBasicNetworkIPs(ips []string) []string {
	sortedSlice := sort.StringSlice(ips)
	sortedSlice.Sort()
	return sortedSlice
}
