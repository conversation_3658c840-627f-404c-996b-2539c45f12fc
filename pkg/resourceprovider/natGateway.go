package resourceprovider

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"

	kubeovnv1 "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/types/known/timestamppb"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"

	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/natGateway/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

const NatGatewayKind = "VpcNatGateway"

func natGatewayServices(b *types.RMBody) {
	switch b.Method {
	case "ListNATGateways":
		listNATGateways(b)
	case "GetNATGateway":
		getNATGateway(b)
	case "CreateNATGateway":
		createNATGateway(b)
	case "UpdateNATGateway":
		updateNATGateway(b)
	case "DeleteNATGateway":
		deleteNATGateway(b)
	default:
		reason := fmt.Sprintf("Not an avaliable %v service method: %v", b.Service, b.Method)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
	}
}

func createNATGateway(b *types.RMBody) {

	var err error = nil
	reason := ""

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "NATGateway", "CreateNATGateway")
	defer func() {
		if err == nil && reason == "" {
			mqRTMetrics.Result = types.ActionResultSucceeded
		}
		mqRTMetrics.ObserveDurationAndLog()
	}()

	request := network.CreateNATGatewayRequest{}

	js, _ := json.Marshal(b.Data)

	err = json.Unmarshal(js, &request)
	if err != nil {
		logrus.Errorln(err)
		reason = fmt.Sprintf("Not an avaliable %v service %v method body for CreateNATGatewayRequest: %+v", b.Service, b.Method, b.Data)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
		return
	}

	if request.Zone != db.Region {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.Region)
		logrus.Errorln(err)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, err.Error(), b.CallbackData)
		return
	}

	if request.NatGateway.CreateTime == nil {
		request.NatGateway.CreateTime = timestamppb.Now()
	}

	if request.NatGateway.UpdateTime == nil {
		request.NatGateway.UpdateTime = request.NatGateway.CreateTime
	}

	has, err := dao.CheckNatGateway(BosonProvider.Engine, request.NatGatewayName)
	if err != nil {
		logrus.Errorln(has, err)
		BosonProvider.Processor.FailedActionMessageSend(request.NatGateway.Uid, request.NatGateway.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.NatGateway.Id, network.NATGateway_State_name[int32(network.NATGateway_FAILED)])
		return
	}

	if has {
		reason = fmt.Sprintf("NatGateway %v already exist", request.NatGatewayName)
		logrus.Errorln(reason)
		BosonProvider.Processor.FailedActionMessageSend(request.NatGateway.Uid, request.NatGateway.ResourceType, b.Action, reason, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.NatGateway.Id, network.NATGateway_State_name[int32(network.NATGateway_FAILED)])
		return
	}

	subnetData, err := dao.GetSubnet(BosonProvider.Engine, request.NatGateway.Properties.SubnetId)
	if err != nil {
		logrus.Errorln(has, err)
		BosonProvider.Processor.FailedActionMessageSend(request.NatGateway.Uid, request.NatGateway.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.NatGateway.Id, network.NATGateway_State_name[int32(network.NATGateway_FAILED)])
		return
	}

	vpcData, err := dao.GetVpc(BosonProvider.Engine, request.NatGateway.Properties.VpcId)
	if err != nil {
		logrus.Errorln(has, err)
		BosonProvider.Processor.FailedActionMessageSend(request.NatGateway.Uid, request.NatGateway.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.NatGateway.Id, network.NATGateway_State_name[int32(network.NATGateway_FAILED)])
		return
	}

	vpcQuota, err := dao.GetVpcQuotaByVpcID(BosonProvider.Engine, request.NatGateway.Properties.VpcId)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.NatGateway.Uid, request.NatGateway.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.NatGateway.Id, network.NATGateway_State_name[int32(network.NATGateway_FAILED)])
		return
	}

	hisGw, err := dao.ListNatGateways(BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.NatGateway.Uid, request.NatGateway.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.NatGateway.Id, network.NATGateway_State_name[int32(network.NATGateway_FAILED)])
		return
	}

	count := int64(len(hisGw))

	if vpcQuota.NatGatewayCount <= count {
		reason = fmt.Sprintf("NATGateway quota for VPC %v is used up! Quota is %v, current existed nat gateway count is %v ", vpcData.Name, vpcQuota.NatGatewayCount, count)
		logrus.Errorln(reason)
		BosonProvider.Processor.FailedActionMessageSend(request.NatGateway.Uid, request.NatGateway.ResourceType, b.Action, reason, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.NatGateway.Id, network.NATGateway_State_name[int32(network.NATGateway_FAILED)])
		return
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetVPC")

	vpc, err := BosonProvider.KubeovnClient.Vpcs().Get(context.Background(), vpcData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.NatGateway.Uid, request.NatGateway.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.NatGateway.Id, network.NATGateway_State_name[int32(network.NATGateway_FAILED)])
		return
	}

	natGwEip, err := dao.FetchNatGatewayEip(BosonProvider.Engine)
	if err != nil {
		logrus.Errorln(has, err)
		BosonProvider.Processor.FailedActionMessageSend(request.NatGateway.Uid, request.NatGateway.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.NatGateway.Id, network.NATGateway_State_name[int32(network.NATGateway_FAILED)])
		return
	}

	natGwEipCidr, err := dao.GetCidrPoolByCidrID(BosonProvider.Engine, natGwEip.CIDRID)
	if err != nil {
		logrus.Errorln(has, err)
		BosonProvider.Processor.FailedActionMessageSend(request.NatGateway.Uid, request.NatGateway.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.NatGateway.Id, network.NATGateway_State_name[int32(network.NATGateway_FAILED)])
		return
	}

	natGwEIPAddr, err := utils.MergeIPToCIDR(natGwEip.NatGwExternalIP, natGwEipCidr.CIDR)
	if err != nil {
		logrus.Errorln(has, err)
		BosonProvider.Processor.FailedActionMessageSend(request.NatGateway.Uid, request.NatGateway.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.NatGateway.Id, network.NATGateway_State_name[int32(network.NATGateway_FAILED)])
		return
	}

	err = dao.SetNatGwEipPoolAllocated(BosonProvider.Engine, request.NatGateway.Uid, natGwEip.NatGwExternalIPID)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.NatGateway.Uid, request.NatGateway.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.NatGateway.Id, network.NATGateway_State_name[int32(network.NATGateway_FAILED)])
		return
	}

	cidrs := strings.Split(BosonProvider.RpConfig.Boson.VPCCIDR, ",")
	snatRules := make([]*kubeovnv1.SnatRule, len(cidrs))
	for index, cidr := range cidrs {
		snatRules[index] = &ovn.SnatRule{
			Eip:          natGwEip.NatGwExternalIP,
			InternalCIDR: cidr,
		}
	}

	natGw := &ovn.VpcNatGateway{
		TypeMeta: metav1.TypeMeta{
			Kind:       NatGatewayKind,
			APIVersion: ovn.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: request.NatGatewayName,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    VPCKind,
				}),
			},
		},
		Spec: ovn.VpcNatSpec{
			Subnet: subnetData.Name,
			Vpc:    vpc.Name,
			LanIp:  request.NatGateway.Properties.InternalIp,
			Eips: []*ovn.Eip{
				&ovn.Eip{
					EipCIDR: natGwEIPAddr,
					Gateway: natGwEipCidr.GatewayIP,
				},
			},
			SnatRules: snatRules,
		},
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("CreateVPCNATGateway")

	_, err = BosonProvider.KubeovnClient.VpcNatGateways().Create(context.Background(), natGw, metav1.CreateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.NatGateway.Uid, request.NatGateway.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.NatGateway.Id, network.NATGateway_State_name[int32(network.NATGateway_FAILED)])
		return
	}

	natGwData := db.NatGateways{
		NATGatewayID:      request.NatGateway.Uid,
		VPCID:             request.NatGateway.Properties.VpcId,
		SubnetID:          request.NatGateway.Properties.SubnetId,
		DisplayName:       utils.EmptyToBackup(request.NatGateway.DisplayName, request.NatGateway.Name),
		NatGwExternalIPID: natGwEip.NatGwExternalIPID,
		Description:       request.NatGateway.Description,
		InternalIP:        request.NatGateway.Properties.InternalIp,
		ID:                request.NatGateway.Id,
		Name:              natGw.Name,
		ResourceType:      request.NatGateway.ResourceType,
		Zone:              request.NatGateway.Zone,
		State:             types.StateActive,
		CreatorID:         request.NatGateway.CreatorId,
		OwnerID:           request.NatGateway.OwnerId,
		TenantID:          request.NatGateway.TenantId,
		CreateTime:        request.NatGateway.CreateTime.AsTime(),
		UpdateTime:        request.NatGateway.UpdateTime.AsTime(),
		Deleted:           false,
	}

	err = dao.AddNatGateway(BosonProvider.Engine, &natGwData)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.NatGateway.Uid, request.NatGateway.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.NatGateway.Id, network.NATGateway_State_name[int32(network.NATGateway_FAILED)])
		return
	}

	logrus.Infoln("Created NATGateway: ", request.NatGatewayName)
	BosonProvider.Processor.SuccessActionMessageSend(request.NatGateway.Uid, request.NatGateway.ResourceType, b.Action, b.CallbackData)
	BosonProvider.Processor.StateMessageSend(request.NatGateway.Id, types.StateActive)
}

func updateNATGateway(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "NATGateway", "UpdateNATGateway")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}

func getNATGateway(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "NATGateway", "GetNATGateway")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}

func listNATGateways(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "NATGateway", "ListNATGateways")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}

func deleteNATGateway(b *types.RMBody) {

	var err error = nil
	reason := ""

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "NATGateway", "DeleteNATGateway")

	defer func() {
		if err == nil && reason == "" {
			mqRTMetrics.Result = types.ActionResultSucceeded
		}
		mqRTMetrics.ObserveDurationAndLog()
	}()

	request := network.DeleteNATGatewayRequest{}

	js, _ := json.Marshal(b.Data)

	err = json.Unmarshal(js, &request)
	if err != nil {
		logrus.Errorln(err)
		reason = fmt.Sprintf("Not an avaliable %v service %v method body for DeleteNATGatewayRequest: %+v", b.Service, b.Method, b.Data)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
		return
	}

	if request.Zone != db.Region {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.Region)
		logrus.Errorln(err)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, err.Error(), b.CallbackData)
		return
	}

	natGwData, has, err := dao.GetNATGatewayByName(BosonProvider.Engine, request.NatGatewayName)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, err.Error(), b.CallbackData)
		return
	}
	if !has {
		reason = fmt.Sprintf("NATGateway %v not exist", request.NatGatewayName)
		logrus.Errorln(reason)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
		return
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("DeleteVPCNATGateway")

	err = BosonProvider.KubeovnClient.VpcNatGateways().Delete(context.Background(), request.NatGatewayName, metav1.DeleteOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(natGwData.NATGatewayID, natGwData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}

	err = dao.DeleteNatGateway(BosonProvider.Engine, natGwData.NATGatewayID)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(natGwData.NATGatewayID, natGwData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}

	err = dao.ReleaseNatGatewayExternalIP(BosonProvider.Engine, natGwData.NatGwExternalIPID)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(natGwData.NATGatewayID, natGwData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}

	logrus.Infoln("Deleted NATGateway: ", request.NatGatewayName)
	BosonProvider.Processor.SuccessActionMessageSend(natGwData.NATGatewayID, natGwData.ResourceType, b.Action, b.CallbackData)
	BosonProvider.Processor.StateMessageSend(natGwData.ID, network.NATGateway_State_name[int32(network.NATGateway_DELETED)])
}
