package resourceprovider

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	"github.com/sirupsen/logrus"
	"google.golang.org/grpc/metadata"
	"google.golang.org/grpc/peer"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/sender"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

// Processor 消息生产者
type Processor struct {
	RMSender            *sender.Sender
	BossSender          *sender.Sender
	NoticeSender        *sender.Sender
	CloudAuditEipSender *sender.Sender
	CloudAuditVpcSender *sender.Sender
	CloudAuditDcSender  *sender.Sender
	CloudAuditSlbSender *sender.Sender
	Config              *config.Config
}

// NewProcessor 消息生产者初始化
func NewProcessor(rs *sender.Sender, bs *sender.Sender, ns *sender.Sender, caEip *sender.Sender, caVpc *sender.Sender, caDc *sender.Sender, caSlb *sender.Sender, c *config.Config) *Processor {
	p := &Processor{
		RMSender:            rs,
		BossSender:          bs,
		NoticeSender:        ns,
		CloudAuditEipSender: caEip,
		CloudAuditVpcSender: caVpc,
		CloudAuditDcSender:  caDc,
		CloudAuditSlbSender: caSlb,
		Config:              c,
	}
	return p
}

func (p *Processor) makeService(topic string) string {
	mq := p.Config.MQ.Default
	service := ""
	switch topic {
	case mq.Topic:
		service = "VPCs"
	case mq.EIPTopic:
		service = "EIPs"
	default:
		logrus.Errorf("Not an available service from topic: %v", topic)
	}

	return service
}

// Start 消息任务分发
func (p *Processor) Start(b *types.RMBody, topic string) {

	logrus.Infof("Received a message: %+v", b)

	//由系统触发的删除消息，在资源创建失败，或者资源在20分钟之内没有回执创建成功或失败消息，或者资源进入冻结期，
	//这三种场景下会触发这类消息。
	if types.ExpireActionToMethod[b.Action] != "" && b.Method == "" {
		b.Service = p.makeService(topic)
		b.Method = types.ExpireActionToMethod[b.Action]
	}

	switch b.Service {
	case "VPCs":
		vpcServices(b)
	case "Subnets":
		subnetServices(b)
	case "NATGateways":
		natGatewayServices(b)
	case "EIPs":
		eipServices(b)
	case "SNATRules":
		snatRuleServices(b)
	case "DNATRules":
		dnatRuleServices(b)
	case "ACLs":
		aclServices(b)
	default:
		reason := fmt.Sprintf("Not an available service: %v", b.Service)
		p.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
	}
}

// Start 消息任务分发
func (p *Processor) StartCloudEvent(msg *types.CloudEventMsg) {
	eventType := types.CloudEventType(*msg.Type)

	switch eventType {
	case types.SLB_CREATE, types.SLB_UPDATE, types.SLB_DELETE,
		types.RESOURCE_MSG_EXPIRESTOP, types.RESOURCE_MSG_RENEWSTART, types.RESOURCE_MSG_DELETE:
		slbService(eventType, msg)
	default:
		reason := fmt.Sprintf("Not an available CloudEvent type: %s", eventType)
		p.ErrorActionMessageSend(string(eventType), reason, types.CallbackData{})
	}
}

func (p *Processor) sendStateMessage(body types.StateBody) (string, error) {
	b, err := json.Marshal(body)
	if err != nil {
		return "", err
	}

	return p.RMSender.Send(b, body.ID)
}

func (p *Processor) sendActionMessage(body types.ActionBody) (string, error) {
	b, err := json.Marshal(body)
	if err != nil {
		return "", err
	}

	return p.BossSender.Send(b, body.ResourceID)
}

func (p *Processor) sendNoticeMessage(body types.NoticeBody) (string, error) {
	b, err := json.Marshal(body)
	if err != nil {
		return "", err
	}

	return p.NoticeSender.Send(b, body.ID)
}

// ErrorActionMessageSend 非正常执行消息发送
func (p *Processor) ErrorActionMessageSend(action, reason string, data types.CallbackData) {
	body := types.ActionBody{
		Action:       action,
		CallbackData: data,
		Result:       types.ActionResultFailed,
		Reason:       reason,
	}
	logrus.Errorln(reason)
	res, err := p.sendActionMessage(body)
	if err != nil {
		logrus.Errorln(res, err)
	}
}

// FailedActionMessageSend 执行失败的消息发送
func (p *Processor) FailedActionMessageSend(resourceID, resourceType, action, reason string, data types.CallbackData) {
	body := types.ActionBody{
		ResourceID:   resourceID,
		ResourceType: resourceType,
		Action:       action,
		CallbackData: data,
		Result:       types.ActionResultFailed,
		Reason:       reason,
	}
	logrus.Errorln(reason)
	res, err := p.sendActionMessage(body)
	if err != nil {
		logrus.Errorln(res, err)
	}
}

// SuccessActionMessageSend 执行成功的消息发送
func (p *Processor) SuccessActionMessageSend(resourceID, resourceType, action string, data types.CallbackData) {
	body := types.ActionBody{
		ResourceID:   resourceID,
		ResourceType: resourceType,
		Action:       action,
		CallbackData: data,
		Result:       types.ActionResultSucceeded,
	}
	res, err := p.sendActionMessage(body)
	if err != nil {
		logrus.Errorln(res, err)
	}
}

// DoneActionMessageSend 不带 id 的 任务执行完成消息发送
func (p *Processor) DoneActionMessageSend(action string, data types.CallbackData) {
	body := types.ActionBody{
		Action:       action,
		CallbackData: data,
		Result:       types.ActionResultSucceeded,
	}
	res, err := p.sendActionMessage(body)
	if err != nil {
		logrus.Errorln(res, err)
	}
}

// StateMessageSend 状态消息发送
func (p *Processor) StateMessageSend(id, state string) {
	body := types.StateBody{
		ID:    id,
		State: state,
	}
	res, err := p.sendStateMessage(body)
	if err != nil {
		logrus.Errorln(res, err)
	}
}

// NoticeMessageSend 状态消息发送
func (p *Processor) NoticeMessageSend(source, msgType, ip, tenantID string, userIDs []string) {
	body := types.NoticeBody{
		SpecVersion: "1.0",
		Type:        types.NoticeType[msgType],
		Source:      source,
		ID:          utils.UUIDGen(),
		Time:        utils.TimeFormat(time.Now()),
		Data: types.NoticeBodyData{
			Receiver: types.NoticeBodyDataReceiver{
				TenantID: tenantID,
				UserIDs:  userIDs,
			},
			Payload: types.NoticeBodyDataPayload{
				IP: ip,
			},
		},
	}
	res, err := p.sendNoticeMessage(body)
	if err != nil {
		logrus.Errorln(res, err)
	}
}

// DcvcNoticeMessageSend 状态消息发送
func (p *Processor) DcvcNoticeMessageSend(source, msgType, name, displayName, tenantID string, userIDs []string) {
	body := types.NoticeBody{
		SpecVersion: "1.0",
		Type:        types.NoticeType[msgType],
		Source:      source,
		ID:          utils.UUIDGen(),
		Time:        utils.TimeFormat(time.Now()),
		Data: types.NoticeBodyData{
			Receiver: types.NoticeBodyDataReceiver{
				TenantID: tenantID,
				UserIDs:  userIDs,
			},
			Payload: types.NoticeBodyDataPayload{
				Name:        name,
				DisplayName: displayName,
			},
		},
	}
	res, err := p.sendNoticeMessage(body)
	if err != nil {
		logrus.Errorln(res, err)
	}
}

type CloudAuditSendParam struct {
	// -- start 必选字段 --
	// UserID 用户 ID
	UserID string
	// UserName 用户 name
	UserName string
	// TenantID 租户 ID
	TenantID string
	// TenantName 租户 name
	TenantName string
	// ServiceType 服务类型，与云服务英文简称保持一致
	ServiceType types.CloudAuditServiceType
	// OperatorType 操作类型，与操作类型英文名称保持一致
	OperationType types.CloudAuditOperationType
	// ResourceType 操作类型，操作类型，与操作类型英文名称保持一致
	ResourceType types.CloudAuditResourceType
	// UserIP 发起本次操作的用户的IP。仅event_type为SystemAction时可为空
	UserIP string
	// -- end 必选字段 --

	// EventRating 事件等级
	EventRating types.CloudAuditEventRating
	// EventType 事件类型
	EventType types.CloudAuditEventType
	// RequestID 记录本次请求的request id。异步请求时必须
	RequestID string
	// Request 操作的请求内容，必须是结构体形式传入，要求结构体能够进行 json 序列化（json.Marshal(request)）
	Request interface{}
	// Response 操作的响应内容，必须是结构体形式传入，要求结构体能够进行 json 序列化（json.Marshal(request)）
	// 操作的响应内容。异步请求操作时，请求和响应分为两个事件传输和存储，以request_id对应
	Response interface{}
	// Code 操作的http返回码
	Code int

	// ResourceName 资源名称
	ResourceName string
	// ResourceID 资源唯一标识
	ResourceID string
	// Msg 备注信息，用以描述本操作额外的信息
	Msg string
	// LocationInfo 记录本次操作出错后，定位问题所需要的辅助信息，必须是结构体形式传入，要求结构体能够进行 json 序列化（json.Marshal(request)）
	LocationInfo interface{}
	// Endpoint 记录本次操作设计云资源的详情页面的 endpoint
	Endpoint string
	// ResourceUrl 记录本次操作涉及云资源的详情页面的访问链接（不包含 endpoint）
	ResourceUrl string
}

// cloudAuditSend 状态消息发送
func (p *Processor) cloudAuditSend(
	param *CloudAuditSendParam, cloudAuditSender *sender.Sender,
) (string, error) {
	if !sender.CloudAuditConfig[cloudAuditSender.Topic].Enable {
		return "", nil
	}
	user := types.CloudAuditBodyUser{
		UserID:     param.UserID,
		UserName:   param.UserName,
		TenantID:   param.TenantID,
		TenantCode: param.TenantName,
	}

	body := types.CloudAuditBody{
		Time:          time.Now().Format(time.RFC3339),
		User:          user,
		ServiceType:   string(param.ServiceType),
		OperationType: string(param.OperationType),
		ResourceType:  string(param.ResourceType),
		EventID:       utils.UUIDGen(),
		SourceIP:      param.UserIP,
		EventRating:   string(param.EventRating),
		EventType:     string(param.EventType),
		Request:       param.Request,
		Response:      param.Response,
		ResourceName:  param.ResourceName,
		ResourceID:    param.ResourceID,
		APIVersion:    types.CloudAuditAPIVersion,
		Message:       param.Msg,
		Code:          int32(param.Code),
		RequestID:     param.RequestID,
		LocationInfo:  param.LocationInfo,
		Endpoint:      param.Endpoint,
		ResourceURL:   param.ResourceUrl,
	}

	bodyMsg, err := json.Marshal(body)
	if err != nil {
		logrus.Error(err)
		return "", err
	}

	return cloudAuditSender.Send(bodyMsg, body.EventID)
}

type UserInfo struct {
	UserID     string
	UserName   string
	TenantID   string
	TenantCode string
	Source     string
}

func GetUserInfoFromHeader(ctx context.Context) UserInfo {
	userInfo := UserInfo{}
	md, err := metadata.FromIncomingContext(ctx)
	if !err {
		return userInfo
	}

	if len(md.Get("x-user-id")) > 0 {
		userInfo.UserID = md.Get("x-user-id")[0]
	}

	if len(md.Get("x-user-name")) > 0 {
		userInfo.UserName = md.Get("x-user-name")[0]
	}

	if len(md.Get("x-tenant-id")) > 0 {
		userInfo.TenantID = md.Get("x-tenant-id")[0]
	}

	if len(md.Get("x-tenant-code")) > 0 {
		userInfo.TenantCode = md.Get("x-tenant-code")[0]
	}

	userInfo.Source = getClientIP(ctx, md)

	return userInfo
}

func getClientIP(ctx context.Context, md metadata.MD) string {
	xForwardedFors := md.Get("x-forwarded-for")
	if len(xForwardedFors) > 0 {
		ip := strings.TrimSpace(strings.Split(xForwardedFors[0], ",")[0])
		if ip != "" {
			return ip
		}
	}

	xRealIPs := md.Get("x-real-ip")
	if len(xRealIPs) > 0 {
		ip := xRealIPs[0]
		if ip != "" {
			return ip
		}
	}

	peerInfo, ok := peer.FromContext(ctx)
	if !ok {
		return "0.0.0.0"
	}

	ipStr := strings.Split(peerInfo.Addr.String(), ":")
	if len(ipStr) == 2 {
		ip := ipStr[0]
		if ip != "" {
			return ip
		}
	}

	return "0.0.0.0"
}

func (p *Processor) CloudAuditSendEipDnat(
	ctx context.Context,
	operationType types.CloudAuditOperationType,
	eventRating types.CloudAuditEventRating,
	request interface{}, response interface{}, httpCode int,
	resourceID, resourceName string,
	locationInfo interface{},
) {
	userInfo := GetUserInfoFromHeader(ctx)
	param := CloudAuditSendParam{
		UserID:        userInfo.UserID,
		UserName:      userInfo.UserName,
		TenantID:      userInfo.TenantID,
		TenantName:    userInfo.TenantCode,
		ServiceType:   types.CloudAuditServiceTypeEIP,
		OperationType: operationType,
		ResourceType:  types.CloudAuditResourceTypeEipDnat,
		UserIP:        userInfo.Source,
		EventRating:   eventRating,
		EventType:     types.CloudAuditEventTypeConsoleAction,
		RequestID:     "", // 都是同步，不需要
		Request:       request,
		Response:      response,
		Code:          httpCode,
		ResourceName:  resourceName,
		ResourceID:    resourceID,
		Msg:           "", // 暂不需要
		LocationInfo:  locationInfo,
		Endpoint:      "", // 暂不需要
		ResourceUrl:   "", // 暂不需要
	}
	if !BosonProvider.RpConfig.Boson.CloudAuditEnable {
		logrus.Info(fmt.Sprintf("cloud audit disabled, eip report info: param:%#v", param))
		return
	}
	res, err := p.cloudAuditSend(
		&param,
		p.CloudAuditEipSender,
	)
	if err != nil {
		logrus.Errorln(res, err)
	}
	logrus.Info(res)
}

func (p *Processor) CloudAuditSendACL(
	ctx context.Context,
	operationType types.CloudAuditOperationType,
	eventRating types.CloudAuditEventRating,
	request interface{}, response interface{}, httpCode int,
	resourceID, resourceName string,
	locationInfo interface{},
) {
	userInfo := GetUserInfoFromHeader(ctx)
	param := CloudAuditSendParam{
		UserID:        userInfo.UserID,
		UserName:      userInfo.UserName,
		TenantID:      userInfo.TenantID,
		TenantName:    userInfo.TenantCode,
		ServiceType:   types.CloudAuditServiceTypeEIP,
		OperationType: operationType,
		ResourceType:  types.CloudAuditResourceTypeEipAccessControl,
		UserIP:        userInfo.Source,
		EventRating:   eventRating,
		EventType:     types.CloudAuditEventTypeConsoleAction,
		RequestID:     "", // 都是同步，不需要
		Request:       request,
		Response:      response,
		Code:          httpCode,
		ResourceName:  resourceName,
		ResourceID:    resourceID,
		Msg:           "", // 暂不需要
		LocationInfo:  locationInfo,
		Endpoint:      "", // 暂不需要
		ResourceUrl:   "", // 暂不需要
	}
	if !BosonProvider.RpConfig.Boson.CloudAuditEnable {
		logrus.Info(fmt.Sprintf("cloud audit disabled, eip report info: param:%#v", param))
		return
	}
	res, err := p.cloudAuditSend(
		&param,
		p.CloudAuditEipSender,
	)
	if err != nil {
		logrus.Errorln(res, err)
	}
	logrus.Info(res)
}

func (p *Processor) CloudAuditSendDCGW(
	ctx context.Context,
	operationType types.CloudAuditOperationType,
	eventRating types.CloudAuditEventRating,
	request interface{}, response interface{}, httpCode int,
	resourceID, resourceName string,
	locationInfo interface{},
) {
	userInfo := GetUserInfoFromHeader(ctx)
	param := CloudAuditSendParam{
		UserID:        userInfo.UserID,
		UserName:      userInfo.UserName,
		TenantID:      userInfo.TenantID,
		TenantName:    userInfo.TenantCode,
		ServiceType:   types.CloudAuditServiceTypeDC,
		OperationType: operationType,
		ResourceType:  types.CloudAuditResourceTypeDcgw,
		UserIP:        userInfo.Source,
		EventRating:   eventRating,
		EventType:     types.CloudAuditEventTypeConsoleAction,
		RequestID:     "", // 都是同步，不需要
		Request:       request,
		Response:      response,
		Code:          httpCode,
		ResourceName:  resourceName,
		ResourceID:    resourceID,
		Msg:           "", // 暂不需要
		LocationInfo:  locationInfo,
		Endpoint:      "", // 暂不需要
		ResourceUrl:   "", // 暂不需要
	}
	if !BosonProvider.RpConfig.Boson.CloudAuditEnable {
		logrus.Info(fmt.Sprintf("cloud audit disabled, dc report info: param:%#v", param))
		return
	}
	res, err := p.cloudAuditSend(
		&param,
		p.CloudAuditDcSender,
	)
	if err != nil {
		logrus.Errorln(res, err)
	}
	logrus.Info(res)
}

func (p *Processor) CloudAuditSendDCVC(
	ctx context.Context,
	operationType types.CloudAuditOperationType,
	eventRating types.CloudAuditEventRating,
	request interface{}, response interface{}, httpCode int,
	resourceID, resourceName string,
	locationInfo interface{},
) {
	userInfo := GetUserInfoFromHeader(ctx)
	param := CloudAuditSendParam{
		UserID:        userInfo.UserID,
		UserName:      userInfo.UserName,
		TenantID:      userInfo.TenantID,
		TenantName:    userInfo.TenantCode,
		ServiceType:   types.CloudAuditServiceTypeDC,
		OperationType: operationType,
		ResourceType:  types.CloudAuditResourceTypeDcvc,
		UserIP:        userInfo.Source,
		EventRating:   eventRating,
		EventType:     types.CloudAuditEventTypeConsoleAction,
		RequestID:     "", // 都是同步，不需要
		Request:       request,
		Response:      response,
		Code:          httpCode,
		ResourceName:  resourceName,
		ResourceID:    resourceID,
		Msg:           "", // 暂不需要
		LocationInfo:  locationInfo,
		Endpoint:      "", // 暂不需要
		ResourceUrl:   "", // 暂不需要
	}
	if !BosonProvider.RpConfig.Boson.CloudAuditEnable {
		logrus.Info(fmt.Sprintf("cloud audit disabled, dc report info: param:%#v", param))
		return
	}
	res, err := p.cloudAuditSend(
		&param,
		p.CloudAuditDcSender,
	)
	if err != nil {
		logrus.Errorln(res, err)
	}
	logrus.Info(res)
}

func (p *Processor) CloudAuditSendSlb(
	ctx context.Context,
	resourceType types.CloudAuditResourceType,
	operationType types.CloudAuditOperationType,
	eventRating types.CloudAuditEventRating,
	request interface{}, response interface{}, httpCode int,
	resourceID, resourceName string,
	locationInfo interface{},
) {
	userInfo := GetUserInfoFromHeader(ctx)
	param := CloudAuditSendParam{
		UserID:        userInfo.UserID,
		UserName:      userInfo.UserName,
		TenantID:      userInfo.TenantID,
		TenantName:    userInfo.TenantCode,
		ServiceType:   types.CloudAuditServiceTypeSLB,
		OperationType: operationType,
		ResourceType:  resourceType,
		UserIP:        userInfo.Source,
		EventRating:   eventRating,
		EventType:     types.CloudAuditEventTypeConsoleAction,
		RequestID:     "", // 都是同步，不需要
		Request:       request,
		Response:      response,
		Code:          httpCode,
		ResourceName:  resourceName,
		ResourceID:    resourceID,
		Msg:           "", // 暂不需要
		LocationInfo:  locationInfo,
		Endpoint:      "", // 暂不需要
		ResourceUrl:   "", // 暂不需要
	}
	if !BosonProvider.RpConfig.Boson.CloudAuditEnable {
		logrus.Info(fmt.Sprintf("cloud audit disabled, slb report info: param:%#v", param))
		return
	}
	res, err := p.cloudAuditSend(
		&param,
		p.CloudAuditSlbSender,
	)
	if err != nil {
		logrus.Errorln(res, err)
	}
	logrus.Info(res)
}

func (p *Processor) CloudAuditSendEIP(
	ctx context.Context,
	operationType types.CloudAuditOperationType,
	resourceType types.CloudAuditResourceType,
	eventRating types.CloudAuditEventRating,
	request interface{}, response interface{}, httpCode int,
	resourceID, resourceName string,
	locationInfo interface{},
) {
	userInfo := GetUserInfoFromHeader(ctx)
	param := CloudAuditSendParam{
		UserID:        userInfo.UserID,
		UserName:      userInfo.UserName,
		TenantID:      userInfo.TenantID,
		TenantName:    userInfo.TenantCode,
		ServiceType:   types.CloudAuditServiceTypeEIP,
		OperationType: operationType,
		ResourceType:  resourceType,
		UserIP:        userInfo.Source,
		EventRating:   eventRating,
		EventType:     types.CloudAuditEventTypeConsoleAction,
		RequestID:     "", // 都是同步，不需要
		Request:       request,
		Response:      response,
		Code:          httpCode,
		ResourceName:  resourceName,
		ResourceID:    resourceID,
		Msg:           "", // 暂不需要
		LocationInfo:  locationInfo,
		Endpoint:      "", // 暂不需要
		ResourceUrl:   "", // 暂不需要
	}
	if !BosonProvider.RpConfig.Boson.CloudAuditEnable {
		logrus.Info(fmt.Sprintf("cloud audit disabled, eip report info: param:%#v", param))
		return
	}
	res, err := p.cloudAuditSend(
		&param,
		p.CloudAuditEipSender,
	)
	if err != nil {
		logrus.Errorln(res, err)
	}
	logrus.Info(res)
}

func (p *Processor) CloudAuditSendMsg(msg string) struct {
	Msg string `json:"msg"`
} {
	return struct {
		Msg string `json:"msg"`
	}{Msg: msg}
}
