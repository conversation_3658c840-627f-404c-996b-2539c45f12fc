package resourceprovider

import (
	"context"
	"fmt"
	"os"
	"reflect"
	"strings"
	"time"

	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"gopkg.in/yaml.v2"
	"k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/util/wait"
	kubeinformers "k8s.io/client-go/informers"
	listersv1 "k8s.io/client-go/listers/core/v1"

	nadv1 "github.com/k8snetworkplumbingwg/network-attachment-definition-client/pkg/client/clientset/versioned/typed/k8s.cni.cncf.io/v1"
	v1 "github.com/kubeovn/kube-ovn/pkg/client/clientset/versioned/typed/kubeovn/v1"
	netv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned/typed/network/v1"

	// pg 初始化依赖包
	_ "github.com/lib/pq"
	"github.com/sirupsen/logrus"
	"k8s.io/apimachinery/pkg/util/runtime"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/tools/leaderelection"
	"xorm.io/xorm"

	kubeovnclientset "github.com/kubeovn/kube-ovn/pkg/client/clientset/versioned"
	slbSdk "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/controllers/slb/sdk"
	networkclientset "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/generated/clientset/versioned"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	db2 "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/migrate"
	pa "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/parser"
	rv "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/receiver"
	sd "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/sender"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

const (
	componentName = "boson-provider"
)

// BosonProvider 全局资源实体
var BosonProvider *ResourceProvider
var BosonProviderConfig config.Config
var LastVPCDefaulAcls []ovn.Acl

// ResourceProvider 全局资源实体定义
type ResourceProvider struct {
	RpConfig            *config.Config
	RMSender            *sd.Sender
	BossSender          *sd.Sender
	NoticeSender        *sd.Sender
	CloudAuditEipSender *sd.Sender
	CloudAuditVpcSender *sd.Sender
	CloudAuditDcSender  *sd.Sender
	CloudAuditSlbSender *sd.Sender
	Receiver            *rv.Receiver
	EIPReceiver         *rv.Receiver
	SLBReceiver         *rv.Receiver
	Parser              *pa.Parser
	ParserCloudEvent    *pa.Parser
	Processor           *Processor
	Engine              *db2.EngineWrapper
	K8sClient           *kubernetes.Clientset
	KubeovnClient       *v1.KubeovnV1Client
	KubeovnClientset    *kubeovnclientset.Clientset
	BosonNetClient      *netv1.NetworkV1Client
	NetworkClientSet    *networkclientset.Clientset
	NADClient           *nadv1.K8sCniCncfIoV1Client
	Elector             *leaderelection.LeaderElector
	configMapsLister    listersv1.ConfigMapLister
	cmInformerFactory   kubeinformers.SharedInformerFactory
	SlbDataplane        *slbSdk.SdkClient
}

// NewResourceProvider ResourceProvider 初始化
func NewResourceProvider() *ResourceProvider {
	// default config filepath ./boson-provider.yaml
	// provider目前仅提供配置文件方式做参数注入
	// config 字段样式在 pkg/config 中定义
	rpConfig := config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))

	// Sender 是处理完消息的结果生产者，回写MQ
	// Receiver 是从MQ接收来自R.M.的消息，是处理流程的入口
	// Processor 是处理逻辑的实际落脚点，由开发者编写
	// Parser 是对接收消息的解析器
	rmSender, err := sd.NewRMSender(rpConfig)
	if err != nil {
		logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
	}

	bossSender, err := sd.NewBossSender(rpConfig)
	if err != nil {
		logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
	}

	noticeSender, err := sd.NewNoticeSender(rpConfig)
	if err != nil {
		logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
	}

	receiver, err := rv.NewReceiver(rpConfig, rpConfig.MQ.Default.BrokerConsumerGroupName)
	if err != nil {
		logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
	}

	eipReceiver, err := rv.NewReceiver(rpConfig, rpConfig.MQ.Default.EIPConsumerGroupName)
	if err != nil {
		logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
	}

	var slbReceiver *rv.Receiver = nil
	if rpConfig.Boson.SlbEnable {
		slbReceiver, err = rv.NewReceiver(rpConfig, rpConfig.MQ.Default.SLBConsumerGroupName)
		if err != nil {
			logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
		}
	} else {
		logrus.Infoln("SLB disabled")
	}

	var cloudAuditEipSender, cloudAuditVpcSender, cloudAuditDcSender, cloudAuditSlbSender *sd.Sender = nil, nil, nil, nil
	if rpConfig.Boson.CloudAuditEnable {
		cloudAuditEipSender, err = sd.NewCloudAuditSenderEip(rpConfig)
		if err != nil {
			logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
		}

		cloudAuditVpcSender, err = sd.NewCloudAuditSenderVpc(rpConfig)
		if err != nil {
			logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
		}

		cloudAuditDcSender, err = sd.NewCloudAuditSenderDc(rpConfig)
		if err != nil {
			logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
		} else {
			logrus.Infoln("cloud audit dc enabled")
		}
		cloudAuditSlbSender, err = sd.NewCloudAuditSenderSlb(rpConfig)
		if err != nil {
			logrus.Fatalf("MQ producer connection failed: %v, error: %v", rpConfig.MQ, err.Error())
		} else {
			logrus.Infoln("cloud audit dc enabled")
		}
	} else {
		logrus.Infoln("cloud audit disabled")
	}

	processor := NewProcessor(rmSender, bossSender, noticeSender, cloudAuditEipSender, cloudAuditVpcSender, cloudAuditDcSender, cloudAuditSlbSender, rpConfig)
	parser := pa.NewParser(processor.Start)
	parserCloudEvent := pa.NewParserCloudEvent(processor.StartCloudEvent)

	pgInfo := fmt.Sprintf("host=%s port=%s user=%s "+
		"password=%s dbname=%s sslmode=disable",
		rpConfig.PG.Host, rpConfig.PG.Port, rpConfig.PG.User, rpConfig.PG.Password, rpConfig.PG.DB)

	engine, err := xorm.NewEngine("postgres", pgInfo)
	if err != nil {
		logrus.Fatalf("PG connection failed: %v, error: %v", pgInfo, err.Error())
	}

	if rpConfig.PG.ShowSQL {
		engine.ShowSQL(true)
	}

	for _, v := range db2.BosonTables {
		err = engine.Sync(v)
		if err != nil {
			logrus.Fatalf("PG table sync failed: %v, error: %v", pgInfo, err.Error())
		}
	}

	migrate.AlterVpcAclColumnLength(engine)

	k8sclient, err := utils.GetClientSet()
	if err != nil {
		logrus.Fatalf("K8s connection failed: %v", err.Error())
	}

	kubeovnclientSet, err := utils.GetKubeovnClientSet()
	if err != nil {
		logrus.Fatalf("K8s connection failed: %v", err.Error())
	}

	networkclientSet, err := utils.GetNetworkClientSet()
	if err != nil {
		logrus.Fatalf("K8s connection failed: %v", err.Error())
	}

	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalf("K8s config error: %v", err.Error())
	}

	kubeovnClient, err := v1.NewForConfig(cfg)
	if err != nil {
		logrus.Fatalf("Kubeovn connection failed: %v", err.Error())
	}

	nadClient, err := nadv1.NewForConfig(cfg)
	if err != nil {
		logrus.Fatalf("NAD connection failed: %v", err.Error())
	}

	// (NOTE: litianqing)
	// here we increase the rateLimit only for net client
	utils.AddRateLimit(cfg)
	bosonNetClient, err := netv1.NewForConfig(cfg)
	if err != nil {
		logrus.Fatalf("Boson net connection failed: %v", err.Error())
	}

	cmInformerFactory := kubeinformers.NewSharedInformerFactoryWithOptions(k8sclient, 0,
		kubeinformers.WithTweakListOptions(func(listOption *metav1.ListOptions) {
			listOption.AllowWatchBookmarks = true
		}), kubeinformers.WithNamespace(os.Getenv("BOSON_PROVIDER_POD_NAMESPACE")))
	configMapInformer := cmInformerFactory.Core().V1().ConfigMaps()

	db2.SetAZ(rpConfig.Boson.VPCDefaultAZ)
	db2.SetRegion(rpConfig.Boson.VPCDefaultRegion)

	slbDp, err := newSlbDataplane(&rpConfig.Boson)
	if err != nil {
		logrus.Fatalf("new slb dataplane client fail, error:%s", err)
	}

	rp := &ResourceProvider{
		RpConfig:            rpConfig,
		Parser:              parser,
		ParserCloudEvent:    parserCloudEvent,
		Processor:           processor,
		Receiver:            receiver,
		EIPReceiver:         eipReceiver,
		SLBReceiver:         slbReceiver,
		RMSender:            rmSender,
		BossSender:          bossSender,
		NoticeSender:        noticeSender,
		CloudAuditEipSender: cloudAuditEipSender,
		CloudAuditVpcSender: cloudAuditVpcSender,
		CloudAuditDcSender:  cloudAuditDcSender,
		CloudAuditSlbSender: cloudAuditSlbSender,
		Engine:              db2.NewEngineWrapper(engine),
		K8sClient:           k8sclient,
		KubeovnClient:       kubeovnClient,
		KubeovnClientset:    kubeovnclientSet,
		NetworkClientSet:    networkclientSet,
		BosonNetClient:      bosonNetClient,
		NADClient:           nadClient,
		configMapsLister:    configMapInformer.Lister(),
		cmInformerFactory:   cmInformerFactory,
		SlbDataplane:        slbDp,
	}

	return rp
}

// Run 开始订阅消息并监听k8s相关资源并处理
func (r *ResourceProvider) Run(stopCh <-chan struct{}) {

	r.cmInformerFactory.Start(stopCh)
	go wait.Until(r.ResyncBosonProviderConfig, time.Second, stopCh)

	closeProvider := func() {
		err := r.Receiver.ShutDown()
		if err != nil {
			logrus.Errorln(err)
		}
		err = r.RMSender.ShutDown()
		if err != nil {
			logrus.Errorln(err)
		}
		err = r.BossSender.ShutDown()
		if err != nil {
			logrus.Errorln(err)
		}

		err = r.NoticeSender.ShutDown()
		if err != nil {
			logrus.Errorln(err)
		}

		if BosonProvider.RpConfig.Boson.CloudAuditEnable {
			err = r.CloudAuditEipSender.ShutDown()
			if err != nil {
				logrus.Errorln(err)
			}

			err = r.CloudAuditVpcSender.ShutDown()
			if err != nil {
				logrus.Errorln(err)
			}

			err = r.CloudAuditDcSender.ShutDown()
			if err != nil {
				logrus.Errorln(err)
			}

			err = r.CloudAuditSlbSender.ShutDown()
			if err != nil {
				logrus.Errorln(err)
			}
		}

		if BosonProvider.RpConfig.Boson.SlbEnable {
			err = r.SLBReceiver.ShutDown()
			if err != nil {
				logrus.Errorln(err)
			}
		}

		err = r.Engine.Close()
		if err != nil {
			logrus.Errorln(err)
		}

		runtime.HandleCrash()

		logrus.Errorf("Stopped " + componentName)
	}

	defer closeProvider()

	logrus.Infof("Recovering " + componentName)

	if !cache.WaitForCacheSync(
		stopCh) {
		logrus.Fatalln("Failed to WaitForCacheSync")
	}

	logrus.Infof("Running " + componentName)

	err := r.RMSender.Start()
	if err != nil {
		logrus.Fatalln(err)
	}
	err = r.BossSender.Start()
	if err != nil {
		logrus.Fatalln(err)
	}
	err = r.NoticeSender.Start()
	if err != nil {
		logrus.Fatalln(err)
	}

	if BosonProvider.RpConfig.Boson.CloudAuditEnable {
		err = r.CloudAuditEipSender.Start()
		if err != nil {
			logrus.Fatalln(err)
		}
		err = r.CloudAuditVpcSender.Start()
		if err != nil {
			logrus.Fatalln(err)
		}
		err = r.CloudAuditDcSender.Start()
		if err != nil {
			logrus.Fatalln(err)
		} else {
			logrus.Infoln("cloud audit dc started")
		}
		err = r.CloudAuditSlbSender.Start()
		if err != nil {
			logrus.Fatalln(err)
		} else {
			logrus.Infoln("cloud audit slb started")
		}
	}

	// Receiver 启动会连带调用Parser，Parser包含了Processor启动逻辑
	err = r.Receiver.Start(r.RpConfig.MQ.Default.Topic, r.Parser.MessageSelector, r.Parser.Starter)
	if err != nil {
		logrus.Fatalln(err)
	}

	err = r.EIPReceiver.Start(r.RpConfig.MQ.Default.EIPTopic, r.Parser.MessageSelector, r.Parser.Starter)
	if err != nil {
		logrus.Fatalln(err)
	}

	if BosonProvider.RpConfig.Boson.SlbEnable {
		err = r.SLBReceiver.Start(r.RpConfig.MQ.Default.SLBTopic, r.ParserCloudEvent.MessageSelector, r.ParserCloudEvent.Starter)
		if err != nil {
			logrus.Fatalln(err)
		}
	}

	<-stopCh
}

func (c *ResourceProvider) ResyncBosonProviderConfig() {
	cm, err := c.configMapsLister.ConfigMaps(os.Getenv("BOSON_PROVIDER_POD_NAMESPACE")).Get("boson-provider-config")
	if err != nil {
		logrus.Errorf("failed to get boson-provider-config, %v", err)
		return
	}

	if cm.Data["boson-provider.yaml"] != "" {
		err = yaml.Unmarshal([]byte(cm.Data["boson-provider.yaml"]), &BosonProviderConfig)
		if err != nil {
			logrus.Errorf("error boson-provider.yaml format %v in boson-provider-config, %v", cm.Data["boson-provider.yaml"], err)
			return
		}

		VPCDefaulAcls := BosonProviderConfig.Boson.VPCDefaulAcls

		if reflect.DeepEqual(VPCDefaulAcls, LastVPCDefaulAcls) {
			return
		}
		logrus.Infoln("last vpc_default_acls: ", LastVPCDefaulAcls)
		logrus.Infoln("new vpc_default_acls: ", VPCDefaulAcls)

		// sync vpc_default_acls
		result := c.SyncVPCDefaulAcls(VPCDefaulAcls)
		if result {
			LastVPCDefaulAcls = VPCDefaulAcls
			BosonProvider.RpConfig.Boson.VPCDefaulAcls = VPCDefaulAcls
		}
		logrus.Infoln("sync vpc_default_acls result: ", result)

		return
	}
}

func (c *ResourceProvider) SyncVPCDefaulAcls(vpcDefaulAcls []ovn.Acl) bool {

	vpcsCli := c.KubeovnClient.Vpcs()
	snCli := c.KubeovnClient.Subnets()

	for _, acl := range vpcDefaulAcls {
		// ![SYSTEM_ACL_LOW, SYSTEM_ACL_HIGH]
		if acl.Priority < SYSTEM_ACL_LOW_PRIORITY_MIN || acl.Priority > SYSTEM_ACL_LOW_PRIORITY_MAX &&
			acl.Priority < SYSTEM_ACL_HIGH_PRIORITY_MIN || acl.Priority > SYSTEM_ACL_HIGH_PRIORITY_MAX {
			logrus.Errorf("cannot assign requested acl priority, acl: %+v", acl)
			return false
		}

		// direction
		if acl.Direction != AclIngressDirection && acl.Direction != AclEgressDirection {
			logrus.Errorf("unsupported acl direction, acl: %+v", acl)
			return false
		}

		// action
		if acl.Action != AclAllowAction && acl.Action != AclAllowRelatedAction && acl.Action != AclDropAction &&
			acl.Action != AclRejectAction && acl.Action != AclAllowStatelessAction && acl.Action != AclPassAction {
			logrus.Errorf("unsupported acl action, acl: %+v", acl)
			return false
		}
	}
	logrus.Infof("patch vpc defaul acls: %+v", vpcDefaulAcls)

	var vpcList, err = vpcsCli.List(context.Background(), metav1.ListOptions{})
	if err != nil {
		panic(err)
	}
	var vpcNames []string
	var vpcs []ovn.Vpc
	for _, vpc := range vpcList.Items {
		// get subnet
		_, err := GetServiceSubnet(vpc.Status.Subnets)
		if err != nil {
			continue

		}
		vpcNames = append(vpcNames, vpc.Name)
		vpcs = append(vpcs, vpc)
	}
	logrus.Infof("patch vpcs total: %d, vpcs list: %+v", len(vpcNames), vpcNames)

	count := 0
	skip := 0
	for _, vpc := range vpcs {

		// get subnet
		snName, err := GetServiceSubnet(vpc.Status.Subnets)
		if err != nil {
			logrus.Errorf("get service subnet error %v in the vpc %s", err, vpc.Name)
			continue

		}
		logrus.Infof("handle vpc: %s subnet: %s", vpc.Name, snName)

		sn, err := snCli.Get(context.Background(), snName, metav1.GetOptions{})

		if err != nil {
			if errors.IsNotFound(err) {
				continue
			}
			logrus.Errorf("get subnet error %v in the vpc %s", err, vpc.Name)
		}

		if sn.Labels != nil && sn.Labels["boson.sensetime.com/vpc-default-acl-skip"] == "true" {
			logrus.Infof("skip vpc: %s subnet: %s", vpc.Name, snName)
			skip++
			continue
		}

		var snAcls []ovn.Acl
		for _, acl := range sn.Spec.Acls {
			// ![SYSTEM_ACL_LOW, SYSTEM_ACL_HIGH]
			if acl.Priority < SYSTEM_ACL_LOW_PRIORITY_MIN || acl.Priority > SYSTEM_ACL_LOW_PRIORITY_MAX &&
				acl.Priority < SYSTEM_ACL_HIGH_PRIORITY_MIN || acl.Priority > SYSTEM_ACL_HIGH_PRIORITY_MAX {
				snAcls = append(snAcls, acl)
			}
		}
		sn.Spec.Acls = append(vpcDefaulAcls, snAcls...)

		_, err = snCli.Update(context.Background(), sn, metav1.UpdateOptions{})
		if err != nil {
			logrus.Errorf("error vpc: %s subnet: %s, %v", vpc.Name, snName, err)
			continue
		}
		count++
	}

	logrus.Infof("vpc total: %d, skipped: %d updated: %d", len(vpcNames), skip, count)
	return len(vpcNames)-skip == count
}

func GetServiceSubnet(subnets []string) (string, error) {
	for _, name := range subnets {
		if strings.HasPrefix(name, "sn-") {
			return name, nil
		}
	}

	return "", fmt.Errorf("No service subnet in the  %+v'", subnets)
}

func newSlbDataplane(bosonConfig *config.BosonDefault) (*slbSdk.SdkClient, error) {
	clientConfig := &slbSdk.Config{
		Region: bosonConfig.VPCDefaultRegion,
		Zone:   bosonConfig.VPCDefaultAZ,
		DB: &slbSdk.DbConfig{
			Host:     bosonConfig.SlbDataplane.Host,
			Port:     bosonConfig.SlbDataplane.Port,
			User:     bosonConfig.SlbDataplane.User,
			Password: bosonConfig.SlbDataplane.Password,
			DB:       bosonConfig.SlbDataplane.DB,
		},
	}
	client, err := slbSdk.NewSdkClient(clientConfig)
	if err != nil {
		logrus.Errorf("new slb dataplane fail, error:%s", err)
		return nil, err
	}
	return client, nil
}

// NewResourceProvider ResourceProvider wiht init pool client
func NewInitPoolProvider() *ResourceProvider {
	// default config filepath ./boson-provider.yaml
	// provider目前仅提供配置文件方式做参数注入
	// config 字段样式在 pkg/config 中定义
	rpConfig := config.NewResourceProviderConfig(config.InitRawConfig(&config.EnvValueConfigFilePath))

	pgInfo := fmt.Sprintf("host=%s port=%s user=%s "+
		"password=%s dbname=%s sslmode=disable",
		rpConfig.PG.Host, rpConfig.PG.Port, rpConfig.PG.User, rpConfig.PG.Password, rpConfig.PG.DB)
	logrus.Infof("db pg ifno %s", pgInfo)
	engine, err := xorm.NewEngine("postgres", pgInfo)
	if err != nil {
		logrus.Fatalf("PG connection failed: %v, error: %v", pgInfo, err.Error())
	}

	if rpConfig.PG.ShowSQL {
		engine.ShowSQL(true)
	}

	for _, v := range db2.BosonTables {
		err = engine.Sync(v)
		if err != nil {
			logrus.Fatalf("PG table sync failed: %v, error: %v", pgInfo, err.Error())
		}
	}

	// change vpc_acls table column length
	migrate.AlterVpcAclColumnLength(engine)

	k8sclient, err := utils.GetClientSet()
	if err != nil {
		logrus.Fatalf("K8s connection failed: %v", err.Error())
	}

	kubeovnclientSet, err := utils.GetKubeovnClientSet()
	if err != nil {
		logrus.Fatalf("K8s connection failed: %v", err.Error())
	}

	networkclientSet, err := utils.GetNetworkClientSet()
	if err != nil {
		logrus.Fatalf("K8s connection failed: %v", err.Error())
	}

	cfg, err := utils.GetRestConfig()
	if err != nil {
		logrus.Fatalf("K8s config error: %v", err.Error())
	}

	kubeovnClient, err := v1.NewForConfig(cfg)
	if err != nil {
		logrus.Fatalf("Kubeovn connection failed: %v", err.Error())
	}

	nadClient, err := nadv1.NewForConfig(cfg)
	if err != nil {
		logrus.Fatalf("NAD connection failed: %v", err.Error())
	}

	// (NOTE: litianqing)
	// here we increase the rateLimit only for net client
	utils.AddRateLimit(cfg)
	bosonNetClient, err := netv1.NewForConfig(cfg)
	if err != nil {
		logrus.Fatalf("Boson net connection failed: %v", err.Error())
	}

	cmInformerFactory := kubeinformers.NewSharedInformerFactoryWithOptions(k8sclient, 0,
		kubeinformers.WithTweakListOptions(func(listOption *metav1.ListOptions) {
			listOption.AllowWatchBookmarks = true
		}), kubeinformers.WithNamespace(os.Getenv("BOSON_PROVIDER_POD_NAMESPACE")))
	configMapInformer := cmInformerFactory.Core().V1().ConfigMaps()

	db2.SetAZ(rpConfig.Boson.VPCDefaultAZ)
	db2.SetRegion(rpConfig.Boson.VPCDefaultRegion)

	rp := &ResourceProvider{
		RpConfig:          rpConfig,
		Engine:            db2.NewEngineWrapper(engine),
		K8sClient:         k8sclient,
		KubeovnClient:     kubeovnClient,
		KubeovnClientset:  kubeovnclientSet,
		NetworkClientSet:  networkclientSet,
		BosonNetClient:    bosonNetClient,
		NADClient:         nadClient,
		configMapsLister:  configMapInformer.Lister(),
		cmInformerFactory: cmInformerFactory,
	}

	return rp
}
