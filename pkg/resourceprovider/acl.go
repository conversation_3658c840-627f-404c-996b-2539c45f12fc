package resourceprovider

import (
	"fmt"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
)

const ACLKind = "ACL"

func aclServices(b *types.RMBody) {
	switch b.Method {
	case "ListACLs":
		listACLs(b)
	case "GetACL":
		getACL(b)
	case "CreateACL":
		createACL(b)
	case "UpdateACL":
		updateACL(b)
	case "DeleteACL":
		deleteACL(b)
	default:
		reason := fmt.Sprintf("Not an avaliable %v service method: %v", b.Service, b.Method)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
	}
}

func listACLs(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "ACL", "ListACLs")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}

func getACL(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "ACL", "GetACL")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}

func createACL(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "ACL", "CreateACL")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}

func updateACL(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "ACL", "UpdateACL")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}

func deleteACL(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "ACL", "DeleteACL")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}
