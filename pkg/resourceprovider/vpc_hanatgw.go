package resourceprovider

import (
	"context"
	"encoding/json"
	"fmt"
	"strconv"
	"strings"
	"sync"

	nadv1 "github.com/k8snetworkplumbingwg/network-attachment-definition-client/pkg/apis/k8s.cni.cncf.io/v1"
	"k8s.io/apimachinery/pkg/api/errors"

	kubeovnv1 "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"github.com/sirupsen/logrus"
	v1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

const (
	BmsNADName   = "nad-vpcbms-network"
	BmsNADConfig = `{"cniVersion": "0.3.0","type": "macvlan","master": "<bms_master_nic>","mode": "bridge"}`
)

type HaNatGwBuilder struct {
	HaInstance     byte     // 目前ha实例为固定的2个，不支持随意改动。
	LanIps         []string // 有序的，一般情况下，0为254，1为253，2为252
	bmsGwIps       []string // 有序的，一般情况下，0为254，1为253，2为252
	NatGwEips      []db.NatGatewayExternalIpPools
	NatGwEipsMap   sync.Map // for eips, k:v => eipCIDR:gateway
	HaHeartBeatNic string
	HaMode         string
	HaMonitorIp    string
	HaGwKind       string
	HaNatGwId      string
	HaNatGwName    string
	lock           sync.Locker
	request        *network.CreateVPCRequest
	session        *db.SessionWrapper
}

func NewHaNatGwBuilder(session *db.SessionWrapper) *HaNatGwBuilder {
	return &HaNatGwBuilder{
		HaInstance:     2, // 不要改，否则后果自负。
		HaHeartBeatNic: "net1",
		HaMode:         "master-standby",
		HaGwKind:       "HaNatGw",
		lock:           &sync.Mutex{},
		session:        session,
	}
}

func (h *HaNatGwBuilder) Prepare(b *types.RMBody, r *network.CreateVPCRequest) (err error) {
	h.lock.Lock()
	defer h.lock.Unlock()

	if err := h.session.Begin(); err != nil {
		logrus.Errorf("session begin failed %s for vpc %s", err, r.VpcName)
		return err
	}

	defer func() {
		if err != nil {
			if err := h.session.Rollback(); err != nil {
				logrus.Errorf("session rollback failed %s for vpc %s", err, r.VpcName)
			}
			logrus.Infof("all done right, no need rollback session here for vpc %s", r.VpcName)
		}
	}()

	// 有序，勿动。
	h.prepareRequest(r)
	if err := h.prepareLanIP(); err != nil {
		logrus.Errorf("prepare LanIp failed: %s", err)
		return err
	}
	if err := h.prepareGwEips(); err != nil {
		logrus.Errorf("prepare gw eips failed: %s", err)
		return err
	}
	h.parepareHaGwName(b)
	if err := h.prepareGwSnat(); err != nil {
		logrus.Errorf("prepare gw snat failed: %s", err)
		return err
	}
	return nil
}

func (h *HaNatGwBuilder) prepareRequest(r *network.CreateVPCRequest) {
	h.request = r
	logrus.Infof("prepare request for vpc %s done", r.VpcName)
}

func (h *HaNatGwBuilder) parepareHaGwName(b *types.RMBody) {
	h.HaNatGwId = utils.UUIDGen()
	h.HaNatGwName = "hagw-" + utils.Substring(b.TenantCode, 0, 20) + "-" + h.HaNatGwId[:8]
	logrus.Infof("prepare hanatgw name %s done", h.HaNatGwName)
}

func (h *HaNatGwBuilder) prepareLanIP() error {
	vpcNetxtHopIp, err := utils.CalcTheLastXIPFromCidr(BosonProvider.RpConfig.Boson.GeneveSubnetCIDR, 1)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	h.LanIps = append(h.LanIps, vpcNetxtHopIp)
	logrus.Infof("prepare vpc nextHopIp %s", vpcNetxtHopIp)

	for i := byte(2); i <= h.HaInstance+1; i++ {
		ip, err := utils.CalcTheLastXIPFromCidr(BosonProvider.RpConfig.Boson.GeneveSubnetCIDR, i)
		if err != nil {
			logrus.Errorln(err)
			return err
		}
		h.LanIps = append(h.LanIps, ip)
	}

	logrus.Infof("get multi lan ip(%s) for ha nat gw", strings.Join(h.LanIps, ","))
	return nil
}

func (h *HaNatGwBuilder) prepareBmsGwIp(bmsSubnet *db.Subnets) error {
	bmsGwVip, err := utils.CalcTheLastXIPFromCidr(bmsSubnet.CIDR, 1)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	h.bmsGwIps = append(h.bmsGwIps, bmsGwVip)
	logrus.Infof("prepare bms gw vip %s", bmsGwVip)

	for i := byte(2); i <= h.HaInstance+1; i++ {
		ip, err := utils.CalcTheLastXIPFromCidr(bmsSubnet.CIDR, i)
		if err != nil {
			logrus.Errorln(err)
			return err
		}
		h.bmsGwIps = append(h.bmsGwIps, ip)
	}

	logrus.Infof("get multi bms gw ip(%s) for ha nat gw", strings.Join(h.bmsGwIps, ","))
	return nil
}

func (h *HaNatGwBuilder) GetNextHopIp() string {
	return h.LanIps[0]
}

func (h *HaNatGwBuilder) prepareGwSnat() error {
	// use vip as snat ip, and allocated rip
	err := dao.SetMultiNatGwEipPoolAllocated(h.session, h.HaNatGwId,
		[]string{h.NatGwEips[0].NatGwExternalIPID, h.NatGwEips[1].NatGwExternalIPID, h.NatGwEips[2].NatGwExternalIPID})
	if err != nil {
		if err := h.session.Rollback(); err != nil {
			logrus.Errorf("session rollback failed %s for vpc %s", err, h.request.VpcName)
		}
		logrus.Errorln(err)
		return err
	}
	var ips []string
	for _, gws := range h.NatGwEips {
		ips = append(ips, gws.NatGwExternalIP)
	}
	logrus.Infof("set allocated in db for natgweip %s for hanatgws %s done", strings.Join(ips, ","), h.HaNatGwName)
	return nil
}

func (h *HaNatGwBuilder) prepareGwEips() error {
	// vip,rip1,rip2 in order
	var err error
	h.NatGwEips, err = dao.FetchMultiNatGatewayEip(h.session, h.HaInstance+1)
	if err != nil {
		if err := h.session.Rollback(); err != nil {
			logrus.Errorf("session rollback failed %s for vpc %s", err, h.request.VpcName)
		}
		errMsg := fmt.Sprintf("vpc %s prepare gw eips error %s", h.request.VpcName, err)
		logrus.Errorln(errMsg)
		return err
	}

	natGwEipCidr, has, err := dao.GetCidrPoolByIDInSession(h.session, h.NatGwEips[0].CIDRID)
	if err != nil {
		if err := h.session.Rollback(); err != nil {
			logrus.Errorf("session rollback failed %s for vpc %s", err, h.request.VpcName)
		}
		logrus.Errorln(has, err)
		return err
	}
	if !has {
		err := fmt.Errorf("allocated NAT Gateway ExternalIP %v has no matched cidr record", h.NatGwEips[0].NatGwExternalIPID)
		logrus.Errorln(err.Error())
		return err
	}
	// monitor ip 为net1的网关地址
	h.HaMonitorIp = natGwEipCidr.GatewayIP

	for _, natGwEip := range h.NatGwEips {
		// 这里的值会填写在eips: eipCIDR字段下，因为其为 eipCIDR: *************/24的形式
		// 所以这里封装了ip+cidr
		natGwEIPAddr, err := utils.MergeIPToCIDR(natGwEip.NatGwExternalIP, natGwEipCidr.CIDR)
		if err != nil {
			logrus.Errorln(err)
			return err
		}
		tmpSlice := []string{natGwEIPAddr, natGwEipCidr.GatewayIP}
		h.NatGwEipsMap.Store(natGwEip.NatGwExternalIP, tmpSlice)
		logrus.Infof("store %s,%s into NatGwEips", natGwEip.NatGwExternalIP, tmpSlice)
	}
	logrus.Infof("prepare gweip done for hanatgws %s", h.HaNatGwName)
	return nil
}

func (h *HaNatGwBuilder) assembleExtendInfo(bmsSubnet *db.Subnets) []*v1.ExtendInfo {
	var extendInfos []*v1.ExtendInfo
	var containerRealIp []string
	var providerIp []string
	var bmsGwIps []string
	for i := byte(1); i <= h.HaInstance; i++ {
		containerRealIp = append(containerRealIp, h.LanIps[i])
		providerIp = append(providerIp, h.NatGwEips[i].NatGwExternalIP)
	}
	if BosonProviderConfig.Boson.BmsMasterNic != "" && bmsSubnet.CIDR != "" {
		for i := byte(1); i <= h.HaInstance; i++ {
			bmsGwIps = append(bmsGwIps, h.bmsGwIps[i])
		}
	}

	// for container
	extendInfos = append(extendInfos, &v1.ExtendInfo{
		Vip:     h.LanIps[0],
		RealIps: containerRealIp,
		Scope:   "c",
	}, &v1.ExtendInfo{
		Vip:     h.NatGwEips[0].NatGwExternalIP,
		RealIps: providerIp,
		Scope:   "p",
	})

	if BosonProviderConfig.Boson.BmsMasterNic != "" && bmsSubnet.CIDR != "" {
		extendInfos = append(extendInfos, &v1.ExtendInfo{
			Vip:     h.bmsGwIps[0],
			RealIps: bmsGwIps,
			Scope:   "b",
		})
	}

	return extendInfos
}

func generateBmsNadCR(bmsMasterNic string) *nadv1.NetworkAttachmentDefinition {

	return &nadv1.NetworkAttachmentDefinition{
		TypeMeta: metav1.TypeMeta{
			Kind:       "NetworkAttachmentDefinition",
			APIVersion: nadv1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      BmsNADName,
			Namespace: NADNamespace,
		},
		Spec: nadv1.NetworkAttachmentDefinitionSpec{
			Config: strings.ReplaceAll(BmsNADConfig, "<bms_master_nic>", bmsMasterNic),
		},
	}
}

func MakeEniAnnotation(vip string, subnet *db.Subnets, haNatGw *v1.HaNatGw) (string, error) {
	var elasticNicConf []*ovn.ElasticNicConfElement

	if elasticNicConfAnnotation, ok := haNatGw.Annotations[AnnoElasticNicConf]; ok {
		if err := json.Unmarshal([]byte(elasticNicConfAnnotation), &elasticNicConf); err != nil {
			logrus.Error(err, "failed to Unmarshal elasticNicConfAnnotation", "hanatgw", haNatGw.Name)
			return "", err
		}
	}
	if BosonProviderConfig.Boson.BmsMasterNic != "" && subnet.CIDR != "" {
		bmsMasterNic := "bms0"
		// TODO mzy move to pre job
		_, err := BosonProvider.NADClient.NetworkAttachmentDefinitions(NADNamespace).Get(context.Background(), BmsNADName, metav1.GetOptions{})
		if err != nil {
			if errors.IsNotFound(err) {
				nad := generateBmsNadCR(BosonProviderConfig.Boson.BmsMasterNic)
				_, err = BosonProvider.NADClient.NetworkAttachmentDefinitions(NADNamespace).Create(context.Background(), nad, metav1.CreateOptions{})
				if err != nil {
					logrus.Errorln(err)
					return "", err
				}
			} else {
				logrus.Errorln(err)
				return "", err
			}
		}

		bmsElasticNicConf := &ovn.ElasticNicConfElement{
			Vip:    vip,
			Nic:    fmt.Sprintf("%s/%s@%s", NADNamespace, BmsNADName, bmsMasterNic),
			Cidr:   subnet.CIDR,
			GwIp:   subnet.GatewayIP,
			Subnet: subnet.Name,
			Scope:  "b",
			Info:   map[string]string{"bms_vlan_id": strconv.Itoa(subnet.VNI), "bms_master_nic": bmsMasterNic},
		}

		for i, v := range elasticNicConf {
			if v.Vip == bmsElasticNicConf.Vip {
				elasticNicConf = append(elasticNicConf[:i], elasticNicConf[i+1:]...)
			}
		}
		elasticNicConf = append([]*ovn.ElasticNicConfElement{bmsElasticNicConf}, elasticNicConf...)
	}

	elasticNicConfBytes, err := json.Marshal(elasticNicConf)
	if err != nil {
		logrus.Errorln(err)
		return "", err
	}

	return string(elasticNicConfBytes), nil
}

func (h *HaNatGwBuilder) CreateInCR(vpc *ovn.Vpc, subnet *ovn.Subnet, bmsSubnet *db.Subnets) error {
	EipInfo, ok := h.NatGwEipsMap.Load(h.NatGwEips[0].NatGwExternalIP)
	if !ok {
		err := fmt.Errorf("can not load eip info for %s", h.NatGwEips[0].NatGwExternalIP)
		logrus.Errorln(err)
		return err
	}

	EipInfoSclie, ok := EipInfo.([]string)
	if !ok {
		err := fmt.Errorf("can not get right type for eip info, gw Eip %s", h.NatGwEips[0].NatGwExternalIP)
		logrus.Errorln(err)
		return err
	}

	cidrs := strings.Split(BosonProvider.RpConfig.Boson.VPCCIDR, ",")
	snatRules := make([]*kubeovnv1.SnatRule, len(cidrs))
	for index, cidr := range cidrs {
		snatRules[index] = &ovn.SnatRule{
			Eip:          h.NatGwEips[0].NatGwExternalIP,
			InternalCIDR: cidr,
		}
	}

	haNatGw := &v1.HaNatGw{
		TypeMeta: metav1.TypeMeta{
			Kind:       h.HaGwKind,
			APIVersion: v1.SchemeGroupVersion.Version,
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: h.HaNatGwName,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    VPCKind,
				}),
			},
			Annotations: map[string]string{
				AnnoRegion: db.Region,
				AnnoAz:     db.AZ,
			},
		},
		Spec: v1.HaNatGwSpec{
			HaMode:         h.HaMode,
			HaHeartBeatNic: h.HaHeartBeatNic,
			HaVip:          h.NatGwEips[0].NatGwExternalIP,
			HaMonitor:      h.HaMonitorIp,
			ExtendInfos:    h.assembleExtendInfo(bmsSubnet),
			VpcNatSpec: kubeovnv1.VpcNatSpec{
				Subnet: subnet.Name,
				Vpc:    vpc.Name,
				Selector: []string{
					"kubernetes.io/os: linux",
				},
				LanIp: h.LanIps[0],
				Eips: []*ovn.Eip{
					&ovn.Eip{
						EipCIDR: EipInfoSclie[0],
						Gateway: EipInfoSclie[1],
					},
				},
				SnatRules: snatRules,
			},
		},
	}

	if BosonProviderConfig.Boson.BmsMasterNic != "" && bmsSubnet.CIDR != "" {
		haNatGw.ObjectMeta.Annotations[AnnoElasticNicConf], _ = MakeEniAnnotation(h.bmsGwIps[0], bmsSubnet, haNatGw)
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("CreateVPCHaNATGateway")
	_, err := BosonProvider.BosonNetClient.HaNatGws().Create(context.Background(), haNatGw, metav1.CreateOptions{})
	if err != nil {
		logrus.Errorln(err)
		return err
	}
	k8sRTMetrics.ObserveDurationAndLog()

	return nil

}

func (h *HaNatGwBuilder) CreateInDB(subnetID string, bmsSubnet *db.Subnets) (err error) {
	defer func() {
		if err != nil {
			if err := h.session.Rollback(); err != nil {
				logrus.Errorf("session rollback failed %s when create ha-nat-gateway %s", err, h.HaNatGwName)
			}
		}
	}()

	has, err := dao.CheckHaNatGateway(h.session, h.HaNatGwName)
	if err != nil {
		logrus.Errorf("check hanatgw %s failed %s", h.HaNatGwName, err)
		return err
	}

	if has {
		err := fmt.Errorf("ha-nat-gateway %s is already in db, please clean, and then create", h.HaNatGwName)
		logrus.Errorln(err)
		return err
	}

	haNatGwData := db.HaNatGateways{
		HaNatGatewayId:    h.HaNatGwId,
		HaMde:             h.HaMode,
		DisplayName:       h.HaNatGwName,
		Description:       h.request.Vpc.Description,
		ID:                utils.GenRMID(h.request.SubscriptionName, h.request.ResourceGroupName, h.request.Zone, "natGateways", h.HaNatGwName),
		Name:              h.HaNatGwName,
		NatGwExternalIPID: h.NatGwEips[0].NatGwExternalIPID,
		InternalIP:        h.LanIps[0],
		MonitorIp:         h.HaMonitorIp,
		HeartbeatListen:   h.HaHeartBeatNic,
		ResourceType:      "network.natGateway.v1.natGateway",
		VrrpVip:           h.NatGwEips[0].NatGwExternalIP,
		VpcID:             h.request.Vpc.Uid,
		SubnetID:          subnetID,
		Zone:              BosonProvider.RpConfig.Boson.VPCDefaultAZ,
		State:             network.VPC_State_name[int32(network.VPC_CREATING)],
		CreatorID:         h.request.Vpc.CreatorId,
		OwnerID:           h.request.Vpc.OwnerId,
		TenantID:          h.request.Vpc.TenantId,
		CreateTime:        h.request.Vpc.CreateTime.AsTime(),
		UpdateTime:        h.request.Vpc.UpdateTime.AsTime(),
		Deleted:           false,
	}
	if BosonProviderConfig.Boson.BmsMasterNic != "" && bmsSubnet.CIDR != "" {
		haNatGwData.BmsGwVip = h.bmsGwIps[0]
	}

	if err := dao.AddHaNatGateway(h.session, &haNatGwData); err != nil {
		logrus.Errorln(err)
		return err
	}

	return nil
}

func (h *HaNatGwBuilder) BuildAll(vpc *ovn.Vpc, subnet *ovn.Subnet, subnet_id string, bmsSubnet *db.Subnets) error {
	if err := h.CreateInDB(subnet_id, bmsSubnet); err != nil {
		logrus.Errorf("create db met error %s, for vpc %s", err, vpc.Name)
		return err
	}
	if err := h.session.Commit(); err != nil {
		logrus.Errorf("session commit error %s, for vpc %s", err, vpc.Name)
	}

	logrus.Infof("hanatgw db record build success for vpc %s", vpc.Name)

	if err := h.CreateInCR(vpc, subnet, bmsSubnet); err != nil {
		logrus.Errorf("create cr met error %s, for vpc %s", err, vpc.Name)
		return err
	}

	logrus.Infof("hanatgw cr(%s) build success for vpc %s", h.HaNatGwName, vpc.Name)
	return nil
}
