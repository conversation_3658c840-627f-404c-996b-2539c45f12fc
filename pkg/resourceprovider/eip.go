package resourceprovider

import (
	"context"
	"encoding/json"
	"fmt"
	"math/rand"
	"time"

	"github.com/allisson/go-pglock/v3"
	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/types/known/timestamppb"
	k8s_errors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"

	eipv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	subnet "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

const EIPKind = "EIP"

const (
	AnnoEipDefaultSnat         = "boson.sensetime.com/eip-default-snat"
	AnnoEipDefaultSnatEnabled  = "enabled"
	AnnoEipDefaultSnatDisabled = "disabled"
	AnnoEipInternalType        = "boson.sensetime.com/eip-internal-type"
	HaNatGwNameAnnotation      = "boson.sensetime.com/hanatgw_name"

	OvnVpcExternalNetwork = "kube-system/ovn-vpc-external-network"

	InternalEIPDstCidrQuota = 50
)

func eipServices(b *types.RMBody) {
	switch b.Method {
	case "ListEIPs":
		listEIPs(b)
	case "GetEIP":
		getEIP(b)
	case "CreateEIP":
		createEIP(b)
	case "UpdateEIP":
		updateEIP(b)
	case "DeleteEIP":
		forceDelete(b)
	case "ExpireStop":
		expireStop(b)
	case "RenewStart":
		renewStart(b)
	case "ReleaseEIP":
		forceDelete(b)
	case "ForceDelete":
		forceDelete(b)
	case "Resize":
		resize(b)
	case "ResizeCancel":
		resizeCancel(b)
	default:
		reason := fmt.Sprintf("Not an avaliable %v service method: %v", b.Service, b.Method)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("invalid method for EIP: %s", b.ResourceID)
	}
}

func getBmsSubnet(vpcID, scope, tenantCode string) (*db.Subnets, error) {
	return dao.GetBmsSubnet(BosonProvider.Engine, scope, vpcID)
}

// miaozhanyong todo , 所有的出错处理都没有回滚，需要用go 多层defer 来处理异常时的回滚
func createEIP(b *types.RMBody) {

	var err error = nil
	reason := ""

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "EIP", "CreateEIP")

	defer func() {
		if err == nil && reason == "" {
			mqRTMetrics.Result = types.ActionResultSucceeded
		}

		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("create EIP %s result %s", b.ResourceID, mqRTMetrics.Result)

		mqRTMetrics.ObserveDurationAndLog()
	}()

	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": b.TenantID,
		"user_id":   b.CallbackData.UserID,
		"service":   b.Service,
		"method":    b.Method,
		"action":    b.Action,
		"data":      b.Data,
	}).Infof("create EIP %s starting...", b.ResourceID)

	request := network.CreateEIPRequest{}

	js, _ := json.Marshal(b.Data)

	err = json.Unmarshal(js, &request)
	if err != nil {
		logrus.Errorln(err)
		reason = fmt.Sprintf("Not an avaliable %v service %v method body for CreateEIPRequest: %+v", b.Service, b.Method, b.Data)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, err.Error(), b.CallbackData)
		return
	}

	if request.Eip == nil || request.Eip.Properties == nil {
		reason = fmt.Sprintf("Invalid Parameter for the %v service %v method body for CreateEIPRequest: %+v", b.Service, b.Method, b.Data)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
		//BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	if request.Eip.CreateTime == nil {
		request.Eip.CreateTime = timestamppb.Now()
	}

	if request.Eip.UpdateTime == nil {
		request.Eip.UpdateTime = request.Eip.CreateTime
	}

	has, err := dao.CheckEipByName(BosonProvider.Engine, request.EipName)
	if err != nil {
		logrus.Errorln(has, err)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	if has {
		reason = fmt.Sprintf("EIP %v already exist", request.EipName)
		logrus.Errorln(reason)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, reason, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	seed := fmt.Sprintf("%s-%s", db.AZ, PGLOCK_EIP)
	pglockId := utils.HashStringToInt32(seed)
	logrus.Infof("eip: %s,init PG Lock ID %d for %s (region-eipname)", request.EipName, pglockId, seed)

	var dlock pglock.Lock
	ctx := context.Background()
	timeout := BosonProvider.RpConfig.Boson.EIPPgLockTimeOut
	childCtx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Second)
	dbLockRTMetrics := exporter.InitDBRTMetrics("PgLockWhenCreateEIP")
	err = func() error {
		defer cancel()
		dlock, err = pglock.NewLock(childCtx, int64(pglockId), BosonProvider.Engine.DB().DB)
		if err != nil {
			logrus.Errorf("eip: %s, new pglock for pgLockID %d failed, error: %s", request.EipName, pglockId, err.Error())
			return err
		}
		logrus.Infof("eip: %s, new pglock for pgLockID %d success", request.EipName, pglockId)

		err = dlock.WaitAndLock(childCtx)
		if err != nil {
			logrus.Errorf("eip: %s, get pglock for pgLockID %d failed, error %s", request.EipName, pglockId, err.Error())
			return err
		}
		return nil
	}()
	dbLockRTMetrics.ObserveDurationAndLog()
	defer dlock.Close()

	defer func() {
		_ = dlock.Unlock(ctx) // protect end here
		logrus.Infof("eip: %s, unlock pglock for pgLockID %d", request.EipName, pglockId)
	}()

	if err != nil {
		logrus.Errorln(has, err)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	vpcData, err := dao.GetVpc(BosonProvider.Engine, request.Eip.Properties.VpcId)
	if err != nil {
		logrus.Errorln(has, err)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	vpcQuota, err := dao.GetVpcQuotaByVpcID(BosonProvider.Engine, request.Eip.Properties.VpcId)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	hisEips, err := dao.ListEips(BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetVPC")

	vpc, err := BosonProvider.KubeovnClient.Vpcs().Get(context.Background(), vpcData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	count := int64(len(hisEips))

	if vpcQuota.EipCount <= count {
		reason = fmt.Sprintf("EIP quota for VPC %v is used up! Quota is %v, current existed vpc count is %v ", vpcData.Name, vpcQuota.EipCount, count)
		logrus.Errorln(reason)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, reason, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	if request.Eip.Properties.InternalEip {
		count = 0
		for _, eip := range hisEips {
			if eip.InternalEIP {
				count = count + 1
			}
		}
		if count > 0 {
			reason = fmt.Sprintf("Enterprise EIP quota for VPC %v is used up! Quota is 1, current existed vpc count is %v ", vpcData.Name, count)
			logrus.Errorln(reason)
			BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, reason, b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
			return
		}
	}

	// for internal eip, need alloc 3 eip, 1 used as vip, the other 2 used as rs ip by hagw
	var needAllocEipNum uint32 = 1
	if request.Eip.Properties.InternalEip {
		needAllocEipNum = 3
	}
	logrus.Infof("Start alloc %d eips for eip_name=%s, sku=%s", needAllocEipNum, request.EipName, request.Eip.Properties.Sku)

	eipPoolData, err := allocEipLine(BosonProvider.Engine, request.Eip.TenantId, request.Eip.Properties.Sku, needAllocEipNum)
	if err != nil {
		logrus.Errorln(has, err)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}
	logrus.Infof("Allocted eips: %+v", eipPoolData)

	// 支持绑定： NATGW_AND_BM | NATGW | SLB
	if !eipTypeValueSupport(request.Eip.Properties.AssociationType) {
		reason = fmt.Sprintf("AssociationType %v is not supported now", request.Eip.Properties.AssociationType)
		logrus.Errorln(reason)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, reason, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	if request.Eip.Properties.AssociationType == network.EIPProperties_SLB {
		err = createSlbEip(b, &request, vpc, &((*eipPoolData)[0]))
		if err != nil {
			reason = err.Error()
		}
		return
	}

	natGwData, has, err := dao.GetHaNatGateway(BosonProvider.Engine, request.Eip.Properties.AssociationId)
	if err != nil {
		logrus.Errorln(has, err)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}
	if !has {
		reason = fmt.Sprintf("EIP %v rely on a non-existent resource: AssociationType %v AssociationID %v", request.EipName, request.Eip.Properties.AssociationType, request.Eip.Properties.AssociationId)
		logrus.Errorln(reason)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, reason, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	bmsSubnet, err := getBmsSubnet(vpcData.VPCID, subnet.SubnetProperties_SERVICE.String(), b.TenantCode)
	if err != nil {
		if vpcData.SubnetType == subnet.SubnetType_POD_BMS_SERVICE.String() || vpcData.SubnetType == subnet.SubnetType_POD_BMS_SERVICE_TRAINING.String() || vpcData.SubnetType == subnet.SubnetType_POD_SERVICE_BMS.String() {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
			return
		}
	}

	natGwIPData, has, err := dao.GetNatGatewayExternalIpPool(BosonProvider.Engine, natGwData.NatGwExternalIPID)
	if err != nil {
		logrus.Errorln(has, err)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	if !has {
		reason = fmt.Sprintf("EIP %v rely on a non-existent resource IP: AssociationType %v AssociationID %v", request.EipName, request.Eip.Properties.AssociationType, request.Eip.Properties.AssociationId)
		logrus.Errorln(reason)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, reason, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	if request.Eip.Properties.Resources == nil || request.Eip.Properties.Resources.BillingItems == nil {
		reason = fmt.Sprintf("EIP %s Resources.BillingItems.Bw could not be nil", request.EipName)
		logrus.Errorln(reason)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, reason, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	bw := request.Eip.Properties.Resources.BillingItems.Bw
	if bw > 200*1024 {
		reason = fmt.Sprintf("EIP %v BW[%v] only support no more than 200G bandwidth", request.EipName, request.Eip.Properties.Resources.BillingItems.Bw)
		logrus.Errorln(reason)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, reason, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	if request.Eip.Properties.Resources.BillingItems.Bw == 0 {
		request.Eip.Properties.Resources.BillingItems.Bw = 10
	}

	ruleName := "snat-" + utils.Substring(utils.NameToTenantCode(request.EipName), 0, 20) + "-" + request.Eip.Uid[:8]
	snatRule := db.SnatRules{
		SnatRuleID:   utils.UUIDGen(),
		DisplayName:  ruleName,
		NatGatewayID: natGwData.HaNatGatewayId,
		Protocol:     "ANY",
		OuterIP:      (*eipPoolData)[0].EIPIP,
		OuterPortMin: "0",
		OuterPortMax: "0",
		InnerPortMin: "0",
		InnerPortMax: "0",
		InnerIP:      "0.0.0.0/0",
		VPCID:        vpcData.VPCID,
		EIPID:        request.Eip.Uid,
		ID:           utils.GenRMID(request.SubscriptionName, request.ResourceGroupName, request.Zone, "snats", ruleName),
		Name:         ruleName,
		ResourceType: "snats",
		Zone:         request.Zone,
		State:        types.StateActive,
		CreatorID:    request.Eip.CreatorId,
		OwnerID:      request.Eip.OwnerId,
		TenantID:     request.Eip.TenantId,
		CreateTime:   request.Eip.CreateTime.AsTime(),
		UpdateTime:   request.Eip.UpdateTime.AsTime(),
		Deleted:      false,
	}

	defaultSnat := AnnoEipDefaultSnatDisabled
	snatRules := []*db.SnatRules{}
	if request.Eip.Properties.DefaultSnat && !request.Eip.Properties.InternalEip {
		defaultSnat = AnnoEipDefaultSnatEnabled
		// 这里是获取默认snat规则
		snatRules, err = dao.ListDefaultSnatRules(BosonProvider.Engine, vpcData.VPCID)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
			return
		}

		// 多eip的时候，如果新建的eip被设置为主eip，即设置新建的eip为默认snat源ip，那么替换掉之前的snat规则
		// .1 add new
		if err = dao.AddSnatRule(BosonProvider.Engine, &snatRule); err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
			return
		}

		// .2 del old
		// 找到 snatRules 对应的 CR:eip，设置 defaultSnat 为 false
		// 此处先找 eip，后面通过修改对应的eip，触发net-controller的修改，在net-controller里面，会处理snat规则和db数据
		// miaozhanyong snat db 是北向数据库，controller不能修改，最多只能使用，在这里del eip snat record
		/*for _, rule := range snatRules {
			eipUIDsNeedUnsetSnat = append(eipUIDsNeedUnsetSnat, rule.EIPID)
		}*/
	}

	eip := &eipv1.EIP{
		TypeMeta: metav1.TypeMeta{
			Kind:       EIPKind,
			APIVersion: eipv1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: request.EipName,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    VPCKind,
				}),
			},
			Annotations: map[string]string{
				"action":            b.Action,
				"orderid":           b.CallbackData.OrderID,
				"tenantid":          b.CallbackData.TenantID,
				"userid":            b.CallbackData.UserID,
				AnnoEipDefaultSnat:  defaultSnat,
				AnnoRegion:          db.Region,
				AnnoAz:              db.AZ,
				AnnoEipInternalType: "false",
			},
		},
		Spec: eipv1.EIPSpec{
			IP:                     (*eipPoolData)[0].EIPIP,
			Allocated:              true,
			TenantID:               request.Eip.TenantId,
			SourceIP:               natGwIPData.NatGwExternalIP,
			UpStreamMaxBandwidth:   fmt.Sprintf("%d", request.Eip.Properties.Resources.BillingItems.Bw),
			DownStreamMaxBandwidth: fmt.Sprintf("%d", request.Eip.Properties.Resources.BillingItems.Bw),
			AssociationType:        request.Eip.Properties.AssociationType.String(),
			Subnet:                 bmsSubnet.Name,
		},
	}

	if request.Eip.Properties.InternalEip {
		if err = dao.AddSnatRule(BosonProvider.Engine, &snatRule); err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
			return
		}

		eipNicName, err := utils.GenEipNicName(request.EipName)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
			return
		}
		localIP, err := utils.ConvertIPWithCIDR((*eipPoolData)[1].EIPIP, (*eipPoolData)[1].CIDR)
		if err != nil {
			logrus.Errorf("convert local ip=%s with cidr failed, err=%v", (*eipPoolData)[1].EIPIP, err)
			BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
			return
		}
		peerIP, err := utils.ConvertIPWithCIDR((*eipPoolData)[2].EIPIP, (*eipPoolData)[2].CIDR)
		if err != nil {
			logrus.Errorf("convert peer ip=%s with cidr failed, err=%v", (*eipPoolData)[2].EIPIP, err)
			BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
			return
		}
		eipElasticNicConf := ovn.ElasticNicConfElement{
			Vip:     (*eipPoolData)[0].EIPIP,
			Nic:     fmt.Sprintf("%s@%s", OvnVpcExternalNetwork, eipNicName),
			Cidr:    (*eipPoolData)[0].CIDR,
			GwIp:    (*eipPoolData)[0].Gateway,
			LocalIp: localIP,
			PeerIp:  peerIP,
			Scope:   "p",
			Info: map[string]string{
				"eip_nic": eipNicName,
				// when create internal eip, snat_dst_cidr is empty
			},
		}

		eipENCB, err := json.Marshal(eipElasticNicConf)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
			return
		}
		logrus.Infof("Generate eip internal conf: %s", string(eipENCB))

		// append annotations
		eip.ObjectMeta.Annotations[AnnoEipInternalType] = "true"
		eip.ObjectMeta.Annotations[HaNatGwNameAnnotation] = natGwData.Name
		eip.ObjectMeta.Annotations[AnnoEIPInternalConf] = string(eipENCB)
	}

	var eipPoolIDs []string
	for _, ep := range *eipPoolData {
		eipPoolIDs = append(eipPoolIDs, ep.EIPPoolID)
	}
	err = dao.SetEipPoolAllocated(BosonProvider.Engine, eipPoolIDs, true)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	// for internal eip, we only insert the vip(eipPoolData[0].EIP will as vip) to eips
	eipData := db.Eips{
		EIPID:           request.Eip.Uid,
		VPCID:           request.Eip.Properties.VpcId,
		AssociationType: network.EIPProperties_AType_name[int32(request.Eip.Properties.AssociationType)],
		AssociationID:   request.Eip.Properties.AssociationId,
		DisplayName:     utils.EmptyToBackup(request.Eip.DisplayName, request.Eip.Name),
		EIPPoolID:       (*eipPoolData)[0].EIPPoolID,
		Description:     request.Eip.Description,
		EIPIP:           (*eipPoolData)[0].EIPIP,
		BW:              request.Eip.Properties.Resources.BillingItems.Bw,
		UpStreamBW:      request.Eip.Properties.Resources.BillingItems.Bw,
		DownStreamBW:    request.Eip.Properties.Resources.BillingItems.Bw,
		//MaxConn:         0,
		ID:                 request.Eip.Id,
		Name:               eip.Name,
		ResourceType:       request.Eip.ResourceType,
		Zone:               request.Eip.Zone,
		State:              network.EIP_State_name[int32(network.EIP_CREATING)],
		CreatorID:          request.Eip.CreatorId,
		OwnerID:            request.Eip.OwnerId,
		TenantID:           request.Eip.TenantId,
		CreateTime:         request.Eip.CreateTime.AsTime(),
		UpdateTime:         request.Eip.UpdateTime.AsTime(),
		Deleted:            false,
		SnatDstCidrEnabled: request.Eip.Properties.SnatDstCidrEnabled,
		InternalEIP:        request.Eip.Properties.InternalEip,
	}

	if err = dao.AddEip(BosonProvider.Engine, &eipData); err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("CreateEIP")
	_, err = BosonProvider.BosonNetClient.EIPs().Create(context.Background(), eip, metav1.CreateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
		return
	}

	// 更新老的 eip
	logrus.Info(fmt.Sprintf("update old eip:%+v", snatRules))
	for _, sr := range snatRules {
		if err = setEipDefaultSnat(sr, false); err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)])
			return
		}
	}

	logrus.Infoln("Created EIP: ", request.EipName)
	// BosonProvider.Processor.SuccessActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, b.CallbackData)
	// for eip create, the init state in rm is creating, no need to send state message again to rm
	// BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_CREATING)])

}

func createSlbEip(
	b *types.RMBody, request *network.CreateEIPRequest, vpc *ovn.Vpc, eipPoolData *db.EipPools,
) error {
	var err error = nil
	defer func() {
		if err != nil {
			BosonProvider.Processor.FailedActionMessageSend(
				request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData,
			)
			BosonProvider.Processor.StateMessageSend(
				request.Eip.Id, network.EIP_State_name[int32(network.EIP_FAILED)],
			)
		}
	}()

	if request.Eip.Properties.Resources == nil || request.Eip.Properties.Resources.BillingItems == nil {
		err = fmt.Errorf("EIP %s Resources.BillingItems.Bw could not be nil", request.EipName)
		logrus.Errorln(err.Error())
		return err
	}

	bw := request.Eip.Properties.Resources.BillingItems.Bw
	if bw > 200*1024 {
		err = fmt.Errorf("EIP %v BW[%v] only support no more than 200G bandwidth",
			request.EipName, request.Eip.Properties.Resources.BillingItems.Bw,
		)
		logrus.Errorln(err.Error())
		return err
	}

	if request.Eip.Properties.Resources.BillingItems.Bw == 0 {
		request.Eip.Properties.Resources.BillingItems.Bw = 10
	}

	// 创建的时候，设置为对应的 vpcgw 的 ip，确保net-controller 的正常，绑定slb后，则切换为slb的eip
	sourceIP, err := getVpcGwExternalIP(request.Eip.Properties.VpcId)
	if err != nil {
		return err
	}

	eip := &eipv1.EIP{
		TypeMeta: metav1.TypeMeta{
			Kind:       EIPKind,
			APIVersion: eipv1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: request.EipName,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    VPCKind,
				}),
			},
			Annotations: map[string]string{
				"action":           b.Action,
				"orderid":          b.CallbackData.OrderID,
				"tenantid":         b.CallbackData.TenantID,
				"userid":           b.CallbackData.UserID,
				AnnoEipDefaultSnat: AnnoEipDefaultSnatDisabled,
				AnnoRegion:         db.Region,
				AnnoAz:             db.AZ,
			},
		},
		Spec: eipv1.EIPSpec{
			IP:                     eipPoolData.EIPIP,
			Allocated:              true,
			TenantID:               request.Eip.TenantId,
			SourceIP:               sourceIP,
			UpStreamMaxBandwidth:   fmt.Sprintf("%d", request.Eip.Properties.Resources.BillingItems.Bw),
			DownStreamMaxBandwidth: fmt.Sprintf("%d", request.Eip.Properties.Resources.BillingItems.Bw),
			AssociationType:        request.Eip.Properties.AssociationType.String(),
			Subnet:                 "", // SLB 不需要
		},
	}
	k8sRTMetrics := exporter.InitK8sRTMetrics("CreateEIP")

	_, err = BosonProvider.BosonNetClient.EIPs().Create(context.Background(), eip, metav1.CreateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err.Error())
		return err
	}
	err = dao.SetEipPoolAllocated(BosonProvider.Engine, []string{eipPoolData.EIPPoolID}, true)
	if err != nil {
		logrus.Errorln(err.Error())
		return err
	}

	eipData := db.Eips{
		EIPID:           request.Eip.Uid,
		VPCID:           request.Eip.Properties.VpcId,
		AssociationType: network.EIPProperties_AType_name[int32(request.Eip.Properties.AssociationType)],
		AssociationID:   "", // 【SLB 类型的EIP，这个ID字段没有使用】，本质是这里设计的有问题，EIP和其他资源之间是同等阶的，需要单独的数据表来记录引用关系，不然处理起来很容易出现两边同时更新的问题，很麻烦
		DisplayName:     utils.EmptyToBackup(request.Eip.DisplayName, request.Eip.Name),
		EIPPoolID:       eipPoolData.EIPPoolID,
		Description:     request.Eip.Description,
		EIPIP:           eipPoolData.EIPIP,
		BW:              request.Eip.Properties.Resources.BillingItems.Bw,
		UpStreamBW:      request.Eip.Properties.Resources.BillingItems.Bw,
		DownStreamBW:    request.Eip.Properties.Resources.BillingItems.Bw,
		//MaxConn:         0,
		ID:           request.Eip.Id,
		Name:         eip.Name,
		ResourceType: request.Eip.ResourceType,
		Zone:         request.Eip.Zone,
		State:        network.EIP_State_name[int32(network.EIP_CREATING)],
		CreatorID:    request.Eip.CreatorId,
		OwnerID:      request.Eip.OwnerId,
		TenantID:     request.Eip.TenantId,
		CreateTime:   request.Eip.CreateTime.AsTime(),
		UpdateTime:   request.Eip.UpdateTime.AsTime(),
		Deleted:      false,
	}

	if err := dao.AddEip(BosonProvider.Engine, &eipData); err != nil {
		logrus.Errorln(err.Error())
		return err
	}

	logrus.Infoln("Created EIP: ", request.EipName)
	// BosonProvider.Processor.SuccessActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, b.CallbackData)
	BosonProvider.Processor.StateMessageSend(request.Eip.Id, network.EIP_State_name[int32(network.EIP_CREATING)])
	return nil
}

func getVpcGwExternalIP(vpcId string) (string, error) {
	hagws, err := dao.ListHaNatGateways(BosonProvider.Engine, vpcId)
	if err != nil {
		logrus.Errorln(err)
		return "", err
	}
	if len(hagws) == 0 {
		err = fmt.Errorf("get vpc:%s gw fail, can not find anyone", vpcId)
		return "", err
	}

	haGwVip, has, err := dao.GetNatGatewayExternalIpPool(BosonProvider.Engine, hagws[0].NatGwExternalIPID) //:= db.NatGatewayExternalIpPools{}
	if err != nil {
		return "", err
	}
	if !has {
		err = fmt.Errorf("create eip fail, get vpc nat wh external ip pool fail, not exist, eip type:SLB")
		return "", err
	}

	return haGwVip.NatGwExternalIP, nil
}

func updateEIP(b *types.RMBody) {
	var err error = nil
	reason := ""
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "EIP", "UpdateEIP")
	defer func() {
		if err == nil && reason == "" {
			mqRTMetrics.Result = types.ActionResultSucceeded
		}
		mqRTMetrics.ObserveDurationAndLog()
		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("update EIP %s result %s", b.ResourceID, mqRTMetrics.Result)
	}()

	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": b.TenantID,
		"user_id":   b.CallbackData.UserID,
		"service":   b.Service,
		"method":    b.Method,
		"action":    b.Action,
		"data":      b.Data,
	}).Infof("update EIP %s starting...", b.ResourceID)

	request := network.UpdateEIPRequest{}
	js, _ := json.Marshal(b.Data)

	err = json.Unmarshal(js, &request)
	if err != nil {
		logrus.Errorln(err)
		reason = fmt.Sprintf("Not an avaliable %v service %v method body for UpdateEIPRequest: %+v", b.Service, b.Method, b.Data)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
		return
	}

	if request.Zone != db.AZ {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.AZ)
		logrus.Errorln(err)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, err.Error(), b.CallbackData)
		return
	}

	if request.Eip.DisplayName == "" && request.Eip.Description == "" && request.Eip.Properties == nil {
		reason = "just support to update DisplayName、Description、Eip.Properties.DefaultSnat"
		logrus.Errorln(reason)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, reason, b.CallbackData)
		return
	}

	id := utils.GenEipResourceID(request.SubscriptionName, request.ResourceGroupName, request.Zone, request.EipName)
	eipData, err := dao.GetEipByResourceID(BosonProvider.Engine, id)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}

	if request.Eip.Properties != nil && utils.In("properties.default_snat", request.UpdateMask.Paths) {
		if err = updateEIPDefaultSnat(&request, eipData); err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(
				request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData,
			)
			return
		}
	}

	if request.Eip.DisplayName != "" {
		eipData.DisplayName = request.Eip.DisplayName
	}

	if request.Eip.Description != "" {
		eipData.Description = request.Eip.Description
	}

	// eip limit rate
	if request.Eip != nil && request.Eip.Properties != nil && request.Eip.Properties.Resources != nil && request.Eip.Properties.Resources.LimitRateItems != nil {
		callbackData := types.RMBody{}
		upStreamBw := request.Eip.Properties.Resources.LimitRateItems.UpStreamBw
		downStreamBw := request.Eip.Properties.Resources.LimitRateItems.DownStreamBw

		// need upStreamBw and downStreamBw
		if upStreamBw < 1 || upStreamBw > eipData.BW {
			logrus.Errorln("Invalid Parameter UpStreamBw: ", upStreamBw)
			reason = fmt.Sprintf("Invalid Parameter for the %v service %v method body for UpdateEIPRequest: %+v", b.Service, b.Method, b.Data)
			BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, callbackData.CallbackData)
			return
		}
		if downStreamBw < 1 || downStreamBw > eipData.BW {
			logrus.Errorln("Invalid Parameter DownStreamBw: ", downStreamBw)
			reason = fmt.Sprintf("Invalid Parameter for the %v service %v method body for UpdateEIPRequest: %+v", b.Service, b.Method, b.Data)
			BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, callbackData.CallbackData)
			return
		}
		eipData.UpStreamBW = upStreamBw
		eipData.DownStreamBW = downStreamBw

		k8sRTMetrics := exporter.InitK8sRTMetrics("GetEIP")
		eipCr, err := BosonProvider.BosonNetClient.EIPs().Get(context.TODO(), eipData.Name, metav1.GetOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), callbackData.CallbackData)
			return
		}

		eipCr.Spec.UpStreamMaxBandwidth = fmt.Sprintf("%d", upStreamBw)
		eipCr.Spec.DownStreamMaxBandwidth = fmt.Sprintf("%d", downStreamBw)

		k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateEIP")

		_, err = BosonProvider.BosonNetClient.EIPs().Update(context.TODO(), eipCr, metav1.UpdateOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), callbackData.CallbackData)
			return
		}
	}

	// slb
	if eipData.AssociationType == network.EIPProperties_AType_name[int32(network.EIPProperties_SLB)] {
		callbackData := types.RMBody{}
		err = eipUpdateSlb(eipData, &request)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), callbackData.CallbackData)
		}
	}

	eipData.UpdateTime = timestamppb.Now().AsTime()
	if err := dao.UpdateEip(BosonProvider.Engine, eipData); err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}

	logrus.Infoln("updated EIP: ", eipData.Name)

	BosonProvider.Processor.SuccessActionMessageSend(request.Eip.Uid, request.Eip.ResourceType, b.Action, b.CallbackData)
}

func updateEIPDefaultSnat(request *network.UpdateEIPRequest, eipData *db.Eips) error {
	// miaozhanyong get update eip snat db record,
	// get  eip nat record
	snatRule, err := dao.GetDefaultSnatRuleByName(BosonProvider.Engine, eipData.EIPID)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	if !request.Eip.Properties.DefaultSnat && snatRule == nil {
		logrus.Infof("ignore for eip: %s", eipData.Name)
		return nil
	}

	if request.Eip.Properties.DefaultSnat {
		// enable new eip eipData.Name snat
		if snatRule == nil {

			ruleName := "snat-" + utils.Substring(utils.NameToTenantCode(eipData.Name), 0, 20) + "-" + eipData.EIPID[:8]
			// add new record
			snatRule = &db.SnatRules{
				SnatRuleID:   utils.UUIDGen(),
				DisplayName:  ruleName,
				NatGatewayID: eipData.AssociationID, // TODO 是否合理？
				Protocol:     "ANY",
				OuterIP:      eipData.EIPIP,
				OuterPortMin: "0",
				OuterPortMax: "0",
				InnerPortMin: "0",
				InnerPortMax: "0",
				InnerIP:      "0.0.0.0/0",
				VPCID:        eipData.VPCID,
				EIPID:        eipData.EIPID,
				ID:           utils.GenRMID(request.SubscriptionName, request.ResourceGroupName, request.Zone, "snats", ruleName),
				Name:         ruleName,
				ResourceType: "snats",
				Zone:         request.Zone,
				State:        types.StateActive,
				CreatorID:    request.Eip.CreatorId,
				OwnerID:      request.Eip.OwnerId,
				TenantID:     request.Eip.TenantId,
				CreateTime:   request.Eip.CreateTime.AsTime(),
				UpdateTime:   request.Eip.UpdateTime.AsTime(),
				Deleted:      false,
			}
		}
	}

	// update eip
	if err := setEipDefaultSnat(snatRule, request.Eip.Properties.DefaultSnat); err != nil {
		logrus.Errorln(err)
		return err
	}

	// update old eip
	if request.Eip.Properties.DefaultSnat {
		// disable old eip snat, todo miaozhanyong
		oldSnats, err := dao.ListDefaultSnatRules(BosonProvider.Engine, eipData.VPCID)
		if err != nil {
			logrus.Errorln(err)
			return err
		}

		for _, snat := range oldSnats {
			if snat.SnatRuleID == snatRule.SnatRuleID {
				continue
			}

			if err := setEipDefaultSnat(snat, false); err != nil {
				logrus.Errorln(err)
				return err
			}
		}
	}

	return nil
}

func setEipCrDefaultStauts(eip *eipv1.EIP, status string) error {
	eip.Annotations[AnnoEipDefaultSnat] = status

	k8sRTMetrics := exporter.InitK8sRTMetrics("UpdateEIP")
	_, err := BosonProvider.BosonNetClient.EIPs().Update(context.TODO(), eip, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return err
	}
	return nil
}

func setEipDefaultSnat(snatRule *db.SnatRules, defaultSnat bool) error {
	//default true: update cr, add db
	// false, update cr, delete db
	defaultSnatAnno := AnnoEipDefaultSnatDisabled
	if defaultSnat {
		defaultSnatAnno = AnnoEipDefaultSnatEnabled
	}

	eip, err := dao.GetEipByID(BosonProvider.Engine, snatRule.EIPID, false)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	if eip.State == network.EIP_DELETED.String() || eip.State == network.EIP_DELETING.String() {
		logrus.Infof("eip %s state %s, ignore to update snat config", eip.Name, eip.State)
		// fix abnormal case, delete snat rule
		if !defaultSnat {
			// it's default snat eip, disable it
			if err := dao.DeleteSnatRule(BosonProvider.Engine, snatRule.SnatRuleID); err != nil {
				logrus.Errorln(err)
				return err
			}
		}
		return nil
	}

	// .1 get eip cr
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetEIP")
	eipCr, err := BosonProvider.BosonNetClient.EIPs().Get(context.TODO(), eip.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	// .2 if changed, update
	logrus.Infof("eip %s state %s, default snat: %s, set: %s", eip.Name, eip.State, eipCr.Annotations[AnnoEipDefaultSnat], defaultSnatAnno)

	if eipCr.Annotations[AnnoEipDefaultSnat] != defaultSnatAnno {
		// update cr
		if err := setEipCrDefaultStauts(eipCr, defaultSnatAnno); err != nil {
			logrus.Errorln(err)
			return err
		}
	}

	if defaultSnat {
		if err := dao.AddSnatRule(BosonProvider.Engine, snatRule); err != nil {
			logrus.Errorln(err)
			return err
		}

	} else {
		// disable new eip eipData.Name

		if snatRule != nil {
			// it's default snat eip, disable it
			if err := dao.DeleteSnatRule(BosonProvider.Engine, snatRule.SnatRuleID); err != nil {
				logrus.Errorln(err)
				return err
			}
		}
	}

	return nil
}

func getEIP(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "EIP", "UpdateEIP")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("get EIP %s result %s", b.ResourceID, mqRTMetrics.Result)
	}()
	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": b.TenantID,
		"user_id":   b.CallbackData.UserID,
		"service":   b.Service,
		"method":    b.Method,
		"action":    b.Action,
		"data":      b.Data,
	}).Infof("get EIP %s starting...", b.ResourceID)
}

func listEIPs(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "EIP", "UpdateEIP")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("list EIP %s result %s", b.ResourceID, mqRTMetrics.Result)
	}()
	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": b.TenantID,
		"user_id":   b.CallbackData.UserID,
		"service":   b.Service,
		"method":    b.Method,
		"action":    b.Action,
		"data":      b.Data,
	}).Infof("list EIP %s starting...", b.ResourceID)
}

func expireStop(b *types.RMBody) {

	var err error = nil
	reason := ""

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "EIP", "ExpireStopEIP")

	defer func() {
		if err == nil && reason == "" {
			mqRTMetrics.Result = types.ActionResultSucceeded
		}
		mqRTMetrics.ObserveDurationAndLog()
		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("expire EIP %s result %s", b.ResourceID, mqRTMetrics.Result)
	}()

	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": b.TenantID,
		"user_id":   b.CallbackData.UserID,
		"service":   b.Service,
		"method":    b.Method,
		"action":    b.Action,
		"data":      b.Data,
	}).Infof("expire EIP %s starting...", b.ResourceID)

	eipData, err := dao.GetEipByID(BosonProvider.Engine, b.ResourceID, false)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(b.ResourceID, "", b.Action, err.Error(), b.CallbackData)
		return
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetEIP")

	eipCr, err := BosonProvider.BosonNetClient.EIPs().Get(context.TODO(), eipData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}

	err = dao.SetEipState(BosonProvider.Engine, eipData.EIPID, network.EIP_State_name[int32(network.EIP_EXPIRESTOPPING)])
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}
	eipCr.Status.State = network.EIP_State_name[int32(network.EIP_EXPIRESTOPPING)]

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateEIP")

	_, err = BosonProvider.BosonNetClient.EIPs().UpdateStatus(context.TODO(), eipCr, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}

	// err = dao.SetEipState(BosonProvider.Engine, eipData.EIPID, network.EIP_State_name[int32(network.EIP_EXPIRESTOPPING)])
	// if err != nil {
	// 	logrus.Errorln(err)
	// 	BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
	// 	return
	// }

	if eipData.AssociationType == network.EIPProperties_AType_name[int32(network.EIPProperties_SLB)] {
		err = eipDissociateSlbProcess(eipData)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
			return
		}
	}

	logrus.Infoln("Expire stop EIP: ", eipData.Name)
	BosonProvider.Processor.SuccessActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, b.CallbackData)
	// send mg in eip informer
	//BosonProvider.Processor.StateMessageSend(eipData.ID, network.EIP_State_name[int32(network.EIP_EXPIRESTOPPING)])
	BosonProvider.Processor.NoticeMessageSend("EIP", "expireStop", eipData.EIPIP, eipData.TenantID, []string{})
}

func renewStart(b *types.RMBody) {

	var err error = nil
	reason := ""

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "EIP", "RenewStartEIP")

	defer func() {
		if err == nil && reason == "" {
			mqRTMetrics.Result = types.ActionResultSucceeded
		}
		mqRTMetrics.ObserveDurationAndLog()
		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("renew EIP %s result %s", b.ResourceID, mqRTMetrics.Result)
	}()
	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": b.TenantID,
		"user_id":   b.CallbackData.UserID,
		"service":   b.Service,
		"method":    b.Method,
		"action":    b.Action,
		"data":      b.Data,
	}).Infof("renew EIP %s starting...", b.ResourceID)

	eipData, err := dao.GetEipByID(BosonProvider.Engine, b.ResourceID, false)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(b.ResourceID, "", b.Action, err.Error(), b.CallbackData)
		return
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetEIP")
	eipCr, err := BosonProvider.BosonNetClient.EIPs().Get(context.TODO(), eipData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}

	err = dao.SetEipState(BosonProvider.Engine, eipData.EIPID, network.EIP_State_name[int32(network.EIP_RENEWSTARTING)])
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}

	eipCr.Status.State = network.EIP_State_name[int32(network.EIP_RENEWSTARTING)]

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateEIPStatus")

	_, err = BosonProvider.BosonNetClient.EIPs().UpdateStatus(context.TODO(), eipCr, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}

	// err = dao.SetEipState(BosonProvider.Engine, eipData.EIPID, network.EIP_State_name[int32(network.EIP_RENEWSTARTING)])
	// if err != nil {
	// 	logrus.Errorln(err)
	// 	BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
	// 	return
	// }

	logrus.Infoln("Renew start EIP: ", eipData.Name)
	BosonProvider.Processor.SuccessActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, b.CallbackData)
	// sned msg in eip infromer
	//BosonProvider.Processor.StateMessageSend(eipData.ID, network.EIP_State_name[int32(network.EIP_RENEWSTARTING)])
}

func forceDelete(b *types.RMBody) {

	var err error = nil
	reason := ""

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "EIP", "ForceDeleteEIP")

	defer func() {
		if err == nil && reason == "" {
			mqRTMetrics.Result = types.ActionResultSucceeded
		}
		mqRTMetrics.ObserveDurationAndLog()
		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("delete EIP %s result %s", b.ResourceID, mqRTMetrics.Result)
	}()
	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": b.TenantID,
		"user_id":   b.CallbackData.UserID,
		"service":   b.Service,
		"method":    b.Method,
		"action":    b.Action,
		"data":      b.Data,
	}).Infof("delete EIP %s starting...", b.ResourceID)

	var eipData *db.Eips

	if b.Method != "ForceDelete" {

		request := network.DeleteEIPRequest{}

		js, _ := json.Marshal(b.Data)

		err = json.Unmarshal(js, &request)
		if err != nil {
			logrus.Errorln(err)
			reason = fmt.Sprintf("Not an avaliable %v service %v method body for DeleteEIPRequest: %+v", b.Service, b.Method, b.Data)
			BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
			return
		}

		if eipData, err = dao.GetEipByName(BosonProvider.Engine, request.EipName); err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(b.ResourceID, "", b.Action, err.Error(), b.CallbackData)
			return
		}

		b.ResourceID = eipData.EIPID

	} else {
		if eipData, err = dao.GetEipByID(BosonProvider.Engine, b.ResourceID, false); err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(b.ResourceID, "", b.Action, err.Error(), b.CallbackData)
			return
		}
	}

	if eipData.Deleted {
		BosonProvider.Processor.SuccessActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(eipData.ID, network.EIP_State_name[int32(network.EIP_DELETED)])
		return
	}

	if b.Method == "DeleteEIP" {
		// 手动释放，需要做关联资源检查
		err = eipDeleteCheck(eipData)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
			return
		}
	}

	dnatDatas, err := dao.ListDnatRules(BosonProvider.Engine, eipData.EIPID)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}

	aclDatas, err := dao.ListACLs(BosonProvider.Engine, eipData.EIPID)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}

	for _, dnatData := range dnatDatas {
		k8sRTMetrics := exporter.InitK8sRTMetrics("DeleteDNAT")
		err = BosonProvider.BosonNetClient.Dnats().Delete(context.TODO(), dnatData.Name, metav1.DeleteOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil && !k8s_errors.IsNotFound(err) {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
			return
		} else if k8s_errors.IsNotFound(err) {
			state := network.DNATRule_State_name[int32(network.DNATRule_DELETED)]
			if err = dao.SetDnatRuleState(BosonProvider.Engine, dnatData.DnatRuleID, state); err != nil {
				logrus.Errorln(err)
				BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
				return
			}
		}
	}

	for _, aclData := range aclDatas {
		k8sRTMetrics := exporter.InitK8sRTMetrics("DeleteACL")
		err = BosonProvider.BosonNetClient.ACLs(BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs).Delete(context.TODO(), aclData.Name, metav1.DeleteOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil && !k8s_errors.IsNotFound(err) {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
			return
		} else if k8s_errors.IsNotFound(err) {
			state := network.ACL_State_name[int32(network.ACL_DELETED)]
			if err = dao.SetACLStateByName(BosonProvider.Engine, aclData.Name, state); err != nil {
				logrus.Errorln(err)
				BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
				return
			}
		}
	}

	// for internal eip, we did not insert into db, so need to delete acl cr manually
	if eipData.InternalEIP {
		aclName := utils.GenAclNameForInternalEip(eipData.Name)
		k8sRTMetrics := exporter.InitK8sRTMetrics("DeleteACL")
		err = BosonProvider.BosonNetClient.ACLs(BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs).Delete(context.TODO(), aclName, metav1.DeleteOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil && !k8s_errors.IsNotFound(err) {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
		}
	}

	// snat delete directly
	if err = dao.SetSnatRulesState(BosonProvider.Engine, eipData.EIPID, network.SNATRule_DELETED.String()); err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}

	state := network.EIP_State_name[int32(network.EIP_DELETING)]
	k8sRTMetrics := exporter.InitK8sRTMetrics("DeleteEIP")
	err = BosonProvider.BosonNetClient.EIPs().Delete(context.TODO(), eipData.Name, metav1.DeleteOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil && !k8s_errors.IsNotFound(err) {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	} else {
		if k8s_errors.IsNotFound(err) {
			state = network.EIP_State_name[int32(network.EIP_DELETED)]
		}
		if err = dao.SetEipState(BosonProvider.Engine, eipData.EIPID, state); err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
			return
		}
	}

	if eipData.AssociationType == network.EIPProperties_AType_name[int32(network.EIPProperties_SLB)] {
		err = eipDissociateSlbProcess(eipData)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), b.CallbackData)
			return
		}
	}

	logrus.Infoln("Force deleting EIP: ", eipData.Name)
	BosonProvider.Processor.SuccessActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, b.CallbackData)
	BosonProvider.Processor.StateMessageSend(eipData.ID, state)

	if b.Method == "ForceDelete" {
		logrus.Infoln("Force deleting EIP notice: ", b.Action, b.Method, b.ResourceID)
		BosonProvider.Processor.NoticeMessageSend("EIP", "forceDelete", eipData.EIPIP, eipData.TenantID, []string{})
	}
}

func eipDissociateSlbProcess(eipData *db.Eips) error {
	slbRecordTmp, err := dao.GetSlbWithEip(BosonProvider.Engine, eipData.EIPID)
	if err != nil {
		logrus.Errorln("Eip dissociate slb fail", "Get slb fail", err)
		return err
	}
	if slbRecordTmp == nil {
		return nil
	}

	err = eipDissociateSlb(slbRecordTmp.ID, eipData.EIPID)
	if err != nil {
		logrus.Errorln("Eip dissociate slb fail.", "slbUnsetEip err:", err.Error())
		return nil
	}

	return nil
}

func eipDeleteCheck(eipData *db.Eips) error {
	// .1 关联的 slb，如果存在，则不能删除
	slbRecordTmp, err := dao.GetSlbWithEip(BosonProvider.Engine, eipData.EIPID)
	if err != nil {
		logrus.Errorln("Eip delete check fail", "Get slb fail", err)
		return err
	}
	if slbRecordTmp != nil {
		err = fmt.Errorf("Eip delete fail, already associate slb:%s/%s", slbRecordTmp.Name, slbRecordTmp.DisplayName)
		// fixme: 此处应当返回 error，但是因为暂未完整的与云管对接，所以先静默处理
		logrus.Errorln(err)
		return nil
	}

	return nil
}

func resize(b *types.RMBody) {
	var err error = nil
	reason := ""

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "EIP", "ResizeEIP")

	defer func() {
		if err == nil && reason == "" {
			mqRTMetrics.Result = types.ActionResultSucceeded
		}

		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("resize EIP %s result %s", b.ResourceID, mqRTMetrics.Result)

		mqRTMetrics.ObserveDurationAndLog()
	}()

	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": b.TenantID,
		"user_id":   b.CallbackData.UserID,
		"service":   b.Service,
		"method":    b.Method,
		"action":    b.Action,
		"data":      b.Data,
	}).Infof("resize EIP %s starting...", b.ResourceID)

	request := network.EIPResize{}

	js, _ := json.Marshal(b.Data)

	callbackData := types.RMBody{}
	err = json.Unmarshal(js, &callbackData)
	if err != nil {
		logrus.Errorln(err)
		reason = fmt.Sprintf("Not an avaliable %v service %v method body for CallbackData: %+v", b.Service, b.Method, b.Data)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, callbackData.CallbackData)
		return
	}

	err = json.Unmarshal(js, &request)
	if err != nil {
		logrus.Errorln(err)
		reason = fmt.Sprintf("Not an avaliable %v service %v method body for ResizeEIPRequest: %+v", b.Service, b.Method, b.Data)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, callbackData.CallbackData)
		return
	}

	if request.ResourceId == "" || request.Properties == nil {
		reason = fmt.Sprintf("Invalid Parameter for the %v service %v method body for ResizeEIPRequest: %+v", b.Service, b.Method, b.Data)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, callbackData.CallbackData)
		return
	}

	eipData, err := dao.GetEipByID(BosonProvider.Engine, request.ResourceId, false)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.ResourceId, "", b.Action, err.Error(), callbackData.CallbackData)
		return
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetEIP")

	// TODO(myl): use informer to get EIP
	eipCr, err := BosonProvider.BosonNetClient.EIPs().Get(context.TODO(), eipData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), callbackData.CallbackData)
		return
	}

	bw := request.Properties.Resources.BillingItems.Bw

	if bw > 200*1024 {
		reason = fmt.Sprintf("EIP %v BW[%v] only support no more than 200G bandwidth", eipData.Name, bw)
		logrus.Errorln(reason)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, reason, callbackData.CallbackData)
		return
	}

	if bw == 0 {
		bw = 10
	}

	eipCr.Spec.UpStreamMaxBandwidth = fmt.Sprintf("%d", bw)
	eipCr.Spec.DownStreamMaxBandwidth = fmt.Sprintf("%d", bw)

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateEIP")

	_, err = BosonProvider.BosonNetClient.EIPs().Update(context.TODO(), eipCr, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), callbackData.CallbackData)
		return
	}

	err = dao.UpdateEipBw(BosonProvider.Engine, eipData.EIPID, bw)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), callbackData.CallbackData)
		return
	}

	logrus.Infoln("Resize EIP: ", eipData.Name)
	BosonProvider.Processor.SuccessActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, callbackData.CallbackData)
}

func resizeCancel(b *types.RMBody) {
	var err error = nil
	reason := ""

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "EIP", "ResizeCancelEIP")

	defer func() {
		if err == nil && reason == "" {
			mqRTMetrics.Result = types.ActionResultSucceeded
		}

		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("resize cancel EIP %s result %s", b.ResourceID, mqRTMetrics.Result)

		mqRTMetrics.ObserveDurationAndLog()
	}()

	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": b.TenantID,
		"user_id":   b.CallbackData.UserID,
		"service":   b.Service,
		"method":    b.Method,
		"action":    b.Action,
		"data":      b.Data,
	}).Infof("resize cancel EIP %s starting...", b.ResourceID)

	request := network.EIPResize{}

	js, _ := json.Marshal(b.Data)

	callbackData := types.RMBody{}
	err = json.Unmarshal(js, &callbackData)
	if err != nil {
		logrus.Errorln(err)
		reason = fmt.Sprintf("Not an avaliable %v service %v method body for CallbackData: %+v", b.Service, b.Method, b.Data)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, callbackData.CallbackData)
		return
	}

	err = json.Unmarshal(js, &request)
	if err != nil {
		logrus.Errorln(err)
		reason = fmt.Sprintf("Not an avaliable %v service %v method body for ResizeCancelEIPRequest: %+v", b.Service, b.Method, b.Data)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, callbackData.CallbackData)
		return
	}

	if request.ResourceId == "" || request.Properties == nil {
		reason = fmt.Sprintf("Invalid Parameter for the %v service %v method body for ResizeCancelEIPRequest: %+v", b.Service, b.Method, b.Data)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, callbackData.CallbackData)
		return
	}

	eipData, err := dao.GetEipByID(BosonProvider.Engine, request.ResourceId, false)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.ResourceId, "", b.Action, err.Error(), callbackData.CallbackData)
		return
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetEIP")

	// TODO(myl): use informer to get EIP
	eipCr, err := BosonProvider.BosonNetClient.EIPs().Get(context.TODO(), eipData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), callbackData.CallbackData)
		return
	}

	bw := request.Properties.Resources.BillingItems.Bw

	if bw == 0 {
		bw = 10
	}

	eipCr.Spec.UpStreamMaxBandwidth = fmt.Sprintf("%d", bw)
	eipCr.Spec.DownStreamMaxBandwidth = fmt.Sprintf("%d", bw)

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateEIP")

	_, err = BosonProvider.BosonNetClient.EIPs().Update(context.TODO(), eipCr, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), callbackData.CallbackData)
		return
	}

	err = dao.UpdateEipBw(BosonProvider.Engine, eipData.EIPID, bw)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, err.Error(), callbackData.CallbackData)
		return
	}

	logrus.Infoln("Resize Cancel EIP: ", eipData.Name)
	BosonProvider.Processor.SuccessActionMessageSend(eipData.EIPID, eipData.ResourceType, b.Action, callbackData.CallbackData)
}

func eipTypeNameIsDnat(typeName string) bool {
	if typeName == network.EIPProperties_AType_name[int32(network.EIPProperties_NATGW)] ||
		typeName == network.EIPProperties_AType_name[int32(network.EIPProperties_NATGW_AND_BM)] {
		return true
	}
	return false
}

func eipTypeValueSupport(typeValue network.EIPProperties_AType) bool {
	if typeValue == network.EIPProperties_NATGW_AND_BM ||
		typeValue == network.EIPProperties_NATGW ||
		typeValue == network.EIPProperties_SLB {
		return true
	}
	return false
}

func eipUpdateSlb(eipData *db.Eips, request *network.UpdateEIPRequest) error {
	// nothing, reserve hook point
	return nil
}

func allocEipLine(e *db.EngineWrapper, tenantID, sku string, num uint32) (*[]db.EipPools, error) {
	// tenant if alreay bind eip line
	tenantLine, err := dao.GetTenantEipLine(e, tenantID)
	if err != nil {
		return nil, err
	}

	if tenantLine != nil {
		eipPoolData, err := dao.AllocEip(BosonProvider.Engine, tenantLine.Line, num)
		if err != nil {
			err = fmt.Errorf("allocate eip for tenant=%s failed, err: %v", tenantID, err)
			return nil, err
		}
		return eipPoolData, nil
	}

	// tenant not bind eip sku line, alloc by line weight
	skuLines, err := dao.GetEipSkuLineMeta(e)
	if err != nil {
		return nil, err
	}

	// 当没有配置skuLineMeta时, 此时sku被当做线路，以兼容老逻辑
	lines, ok := skuLines[sku]
	if !ok || len(lines) == 0 {
		logrus.Warnf("not found sku=%s's lines, use sku as line", sku)
		eipPoolData, err := dao.AllocEip(BosonProvider.Engine, sku, num)
		if err != nil {
			return nil, err
		}
		return eipPoolData, nil
	}

	for {
		var totalWeight int32 = 0
		for _, line := range lines {
			totalWeight += int32(line.Weight)
		}

		if totalWeight <= 0 {
			eipPoolData, err := dao.AllocEip(BosonProvider.Engine, sku, num)
			if err != nil {
				return nil, err
			}
			return eipPoolData, nil
		}

		r := rand.New(rand.NewSource(time.Now().UnixNano()))
		randNumber := r.Int31n(totalWeight) + 1

		var currentWeight int32 = 0
		for index, line := range lines {
			currentWeight += int32(line.Weight)
			if currentWeight >= randNumber {
				logrus.Infof("rand number: %d, total weight: %d, choosed line: %s", randNumber, totalWeight, line.Line)
				eipPoolData, err := dao.AllocEip(BosonProvider.Engine, line.Line, num)
				if err != nil {
					logrus.Warnf("alloc eip for line=%s failed, err: %v. skip this line.", line.Line, err)
					lines = append(lines[:index], lines[index+1:]...)
					break
				}
				return eipPoolData, nil
			}
		}

		// maybe all lines weight is 0, use sku as line
		if currentWeight < randNumber {
			logrus.Warnf("rand Numeber: %d, all line current weight: %d, not valid, use sku as line", randNumber, currentWeight)
			eipPoolData, err := dao.AllocEip(BosonProvider.Engine, sku, num)
			if err != nil {
				return nil, err
			}
			return eipPoolData, nil
		}
	}
}
