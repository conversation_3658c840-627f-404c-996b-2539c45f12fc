package resourceprovider

import (
	"context"
	"fmt"
	"strings"

	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"github.com/sirupsen/logrus"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"

	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

func createGeneveSubnet(b *types.RMBody, request *network.CreateSubnetRequest) (reason string, err error) {

	vpcData, err := dao.GetVpc(BosonProvider.Engine, request.Subnet.Properties.VpcId)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_State_name[int32(network.VPC_FAILED)])
		return
	}

	vpcQuota, err := dao.GetVpcQuotaByVpcID(BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_State_name[int32(network.VPC_FAILED)])
		return
	}

	hisSubnet, err := dao.ListSubnets(BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_State_name[int32(network.VPC_FAILED)])
		return
	}

	count := int64(len(hisSubnet))

	if vpcQuota.GeneveSubnetCount <= count {
		reason = fmt.Sprintf("Geneve subnet quota for VPC %v is used up! Quota is %v, current existed subnets count is %v ", vpcData.Name, vpcQuota.GeneveSubnetCount, count)
		logrus.Errorln(reason)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, reason, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_State_name[int32(network.VPC_FAILED)])
		return
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetVPC")

	vpc, err := BosonProvider.KubeovnClient.Vpcs().Get(context.Background(), vpcData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_State_name[int32(network.VPC_FAILED)])
		return
	}

	nsUID := utils.UUIDGen()
	ns := "ns-" + utils.Substring(b.TenantCode, 0, 20) + "-" + nsUID[:8]

	cidr := BosonProvider.RpConfig.Boson.GeneveSubnetCIDR
	if request.Subnet.Properties.Cidr != "" {
		cidr = request.Subnet.Properties.Cidr
		// TODO(lijianhong): hardcode just use the first cidr for check.
		cidrs := strings.Split(vpcData.CIDR, ",")
		if len(cidrs) > 0 && !utils.CheckSubCIDR(cidrs[0], cidr) {
			reason = fmt.Sprintf("Subnet CIDR %v is not a subset of VPC CIDR %v", cidr, vpcData.CIDR)
			logrus.Errorln(reason)
			BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, reason, b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_State_name[int32(network.VPC_FAILED)])
			return
		}
	}

	gatewayIP, err := utils.GetTheFirstAddrFromCIDR(cidr)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_State_name[int32(network.VPC_FAILED)])
		return
	}

	exPre9, err := utils.GetFirstxAddrsFromCIDR(9, cidr)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_State_name[int32(network.VPC_FAILED)])
		return
	}

	exSuf5, err := utils.GetLastxAddrsFromCIDR(5, cidr)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_State_name[int32(network.VPC_FAILED)])
		return
	}

	subnet := &ovn.Subnet{
		TypeMeta: metav1.TypeMeta{
			Kind:       SubnetKind,
			APIVersion: ovn.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: request.SubnetName,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    VPCKind,
				}),
			},
		},
		Spec: ovn.SubnetSpec{
			Namespaces:  []string{ns},
			Vpc:         vpc.Name,
			CIDRBlock:   cidr,
			NatOutgoing: false,
			Default:     false,
			ExcludeIps:  []string{exPre9, exSuf5},
		},
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("CreateOVNSubnet")

	subnetRes, err := BosonProvider.KubeovnClient.Subnets().Create(context.Background(), subnet, metav1.CreateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(subnetRes, err)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_State_name[int32(network.VPC_FAILED)])
		return
	}

	err = createNS(ns, vpc, vpcData.Zone, vpcData.TenantID, vpcData.VPCID, vpcData.Name, request.Subnet.Uid, request.SubnetName)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_State_name[int32(network.VPC_FAILED)])
		return
	}

	vpc.Spec.Namespaces = append(vpc.Spec.Namespaces, ns)

	k8sRTMetrics = exporter.InitK8sRTMetrics("CreateVPC")

	_, err = BosonProvider.KubeovnClient.Vpcs().Update(context.Background(), vpc, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_State_name[int32(network.VPC_FAILED)])
		return
	}

	subnetData := db.Subnets{
		SubnetID:     request.Subnet.Uid,
		VPCID:        vpcData.VPCID,
		DisplayName:  utils.EmptyToBackup(request.Subnet.DisplayName, request.Subnet.Name),
		CIDR:         cidr,
		Description:  request.Subnet.Description,
		IsDefault:    request.Subnet.Properties.IsDefault,
		GatewayIP:    gatewayIP,
		ID:           request.Subnet.Id,
		Name:         subnet.Name,
		ResourceType: request.Subnet.ResourceType,
		Zone:         request.Subnet.Zone,
		State:        network.VPC_State_name[int32(network.VPC_ACTIVE)],
		CreatorID:    request.Subnet.CreatorId,
		OwnerID:      request.Subnet.OwnerId,
		TenantID:     request.Subnet.TenantId,
		CreateTime:   request.Subnet.CreateTime.AsTime(),
		UpdateTime:   request.Subnet.UpdateTime.AsTime(),
		Deleted:      false,
	}

	err = dao.AddSubnet(BosonProvider.Engine, &subnetData)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_State_name[int32(network.VPC_FAILED)])
		return
	}

	k8sNs := db.K8sNamespaces{
		NamespaceID: nsUID,
		VPCID:       vpcData.VPCID,
		SubnetID:    subnetData.SubnetID,
		Name:        ns,
	}

	err = dao.AddNamespace(BosonProvider.Engine, &k8sNs)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_State_name[int32(network.VPC_FAILED)])
		return
	}

	logrus.Infoln("Created Subnet: ", subnet.Name)
	BosonProvider.Processor.SuccessActionMessageSend(request.Subnet.Uid, request.Subnet.ResourceType, b.Action, b.CallbackData)
	BosonProvider.Processor.StateMessageSend(request.Subnet.Id, network.VPC_State_name[int32(network.VPC_ACTIVE)])

	return
}

func deleteGeneveSubnet(b *types.RMBody, request *network.DeleteSubnetRequest, subnetData *db.Subnets) (reason string, err error) {

	nss, err := dao.ListNamespacesBySubnet(BosonProvider.Engine, subnetData.SubnetID)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(subnetData.SubnetID, subnetData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return reason, err
	}

	for _, ns := range nss {
		k8sRTMetrics := exporter.InitK8sRTMetrics("ListPods")
		pods, err := BosonProvider.K8sClient.CoreV1().Pods(ns.Name).List(context.Background(), metav1.ListOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(subnetData.SubnetID, subnetData.ResourceType, b.Action, err.Error(), b.CallbackData)
			return reason, err
		}
		if len(pods.Items) > 0 {
			reason = fmt.Sprintf("Also exist pods in Subnet %v", request.SubnetName)
			logrus.Errorln(reason)
			BosonProvider.Processor.FailedActionMessageSend(subnetData.SubnetID, subnetData.ResourceType, b.Action, reason, b.CallbackData)
			return reason, err
		}
	}

	natGws, err := dao.ListNatGatewaysBySubnetID(BosonProvider.Engine, subnetData.SubnetID)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(subnetData.SubnetID, subnetData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return reason, err
	}

	for _, natGw := range natGws {
		gw, err := BosonProvider.KubeovnClient.VpcNatGateways().Get(context.Background(), natGw.Name, metav1.GetOptions{})
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(subnetData.SubnetID, subnetData.ResourceType, b.Action, err.Error(), b.CallbackData)
			return reason, err
		}
		if gw.Spec.Subnet == subnetData.Name {
			reason = fmt.Sprintf("Also exist NatGateway %v in Subnet %v", gw.Name, subnetData.Name)
			logrus.Errorln(reason)
			BosonProvider.Processor.FailedActionMessageSend(subnetData.SubnetID, subnetData.ResourceType, b.Action, reason, b.CallbackData)
			return reason, err
		}
	}

	vpcData, err := dao.GetVpc(BosonProvider.Engine, subnetData.SubnetID)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(subnetData.SubnetID, subnetData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return reason, err
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetVPC")

	vpc, err := BosonProvider.KubeovnClient.Vpcs().Get(context.Background(), vpcData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(subnetData.SubnetID, subnetData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return reason, err
	}

	tmpVpcns := []string{}
	for _, vpcns := range vpc.Spec.Namespaces {
		flag := true
		for _, subnetns := range nss {
			if subnetns.Name == vpcns {
				flag = false
				break
			}
		}
		if flag {
			tmpVpcns = append(tmpVpcns, vpcns)
		}
	}

	vpc.Spec.Namespaces = tmpVpcns

	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateVPC")

	_, err = BosonProvider.KubeovnClient.Vpcs().Update(context.Background(), vpc, metav1.UpdateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(subnetData.SubnetID, subnetData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return reason, err
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("DeleteSubnet")

	err = BosonProvider.KubeovnClient.Subnets().Delete(context.Background(), request.SubnetName, metav1.DeleteOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(subnetData.SubnetID, subnetData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return reason, err
	}

	for _, subnetns := range nss {

		k8sRTMetrics = exporter.InitK8sRTMetrics("DeleteNamespace")

		err = BosonProvider.K8sClient.CoreV1().Namespaces().Delete(context.Background(), subnetns.Name, metav1.DeleteOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(subnetData.SubnetID, subnetData.ResourceType, b.Action, err.Error(), b.CallbackData)
			return reason, err
		}
	}

	s := BosonProvider.Engine.NewSession()
	_ = dao.DeleteSubnet(s, subnetData.SubnetID)
	err = s.Commit()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(subnetData.SubnetID, subnetData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return reason, err
	}

	err = dao.DeleteNamespaceBySubnet(BosonProvider.Engine, subnetData.SubnetID)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(vpcData.VPCID, vpcData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return reason, err
	}

	logrus.Infoln("Deleted Subnet: ", request.SubnetName)
	BosonProvider.Processor.SuccessActionMessageSend(subnetData.SubnetID, subnetData.ResourceType, b.Action, b.CallbackData)
	BosonProvider.Processor.StateMessageSend(subnetData.ID, network.VPC_State_name[int32(network.VPC_DELETED)])
	return reason, err
}
