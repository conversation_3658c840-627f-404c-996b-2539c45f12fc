package resourceprovider

import (
	"context"
	"os"
	"time"

	"github.com/go-resty/resty/v2"
	"github.com/sirupsen/logrus"
	apiv1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	clientset "k8s.io/client-go/kubernetes"
	"k8s.io/client-go/kubernetes/scheme"
	"k8s.io/client-go/tools/leaderelection"
	"k8s.io/client-go/tools/leaderelection/resourcelock"
	"k8s.io/client-go/tools/record"
)

const ovnLeaderElector = "boson-provider-leader-elector"

type leaderElectionConfig struct {
	PodName      string
	PodNamespace string

	Client clientset.Interface

	ElectionID string
	WasLeader  bool

	OnStartedLeading func(chan struct{})
	OnStoppedLeading func()
	OnNewLeader      func(identity string)
}

type RegistryInfo struct {
	AZ           string `json:"az"`
	HttpEndpoint string `json:"http_endpoint"`
	GrpcEndpoint string `json:"grpc_endpoint"`
}

func (r *ResourceProvider) isLeader() bool {
	return r.Elector.IsLeader()
}

func (r *ResourceProvider) registryToKK(chan struct{}) {
	go func() {
		url := BosonProviderConfig.Boson.KKRegistryURL
		httpEp := BosonProviderConfig.Boson.HTTPEndPoint
		grpcEp := BosonProviderConfig.Boson.GRPCEndpoint
		az := BosonProviderConfig.Boson.VPCDefaultAZ

		if url == "" {
			return
		}

		client := resty.New()

		ri := RegistryInfo{
			AZ:           az,
			HttpEndpoint: httpEp,
			GrpcEndpoint: grpcEp,
		}

		for {
			_, err := client.R().
				SetBody(ri).
				Post(url)

			if err != nil {
				logrus.Errorf("failed to register: %s", err.Error())
				time.Sleep(5 * time.Second)
			} else {
				logrus.Infof("registry to kk with info: %v", ri)
				return
			}
		}
	}()
}

func (r *ResourceProvider) LeaderElection() {
	config := &leaderElectionConfig{
		Client:           r.K8sClient,
		ElectionID:       "boson-provider",
		PodName:          os.Getenv("BOSON_PROVIDER_POD_NAME"),
		PodNamespace:     os.Getenv("BOSON_PROVIDER_POD_NAMESPACE"),
		OnStartedLeading: r.registryToKK,
	}

	r.Elector = setupLeaderElection(config)
	for {
		if r.isLeader() {
			return
		}
		time.Sleep(5 * time.Second)
	}
}

func setupLeaderElection(config *leaderElectionConfig) *leaderelection.LeaderElector {
	var stopCh chan struct{}
	callbacks := leaderelection.LeaderCallbacks{
		OnStartedLeading: func(ctx context.Context) {
			logrus.Infoln("I am the new leader")
			stopCh = make(chan struct{})
			config.WasLeader = true

			if config.OnStartedLeading != nil {
				config.OnStartedLeading(stopCh)
			}
		},
		OnStoppedLeading: func() {
			logrus.Infoln("I am not leader anymore")
			close(stopCh)

			if config.OnStoppedLeading != nil {
				config.OnStoppedLeading()
			}
			logrus.Fatalf("leaderelection lost")
		},
		OnNewLeader: func(identity string) {
			logrus.Infof("new leader elected: %v", identity)
			if config.WasLeader && identity != config.PodName {
				logrus.Fatal("I am not leader anymore")
			}
			if config.OnNewLeader != nil {
				config.OnNewLeader(identity)
			}
		},
	}

	broadcaster := record.NewBroadcaster()
	hostname := os.Getenv("BOSON_PROVIDER_NODE_NAME")
	recorder := broadcaster.NewRecorder(scheme.Scheme, apiv1.EventSource{
		Component: ovnLeaderElector,
		Host:      hostname,
	})
	lock := &resourcelock.LeaseLock{
		LeaseMeta: metav1.ObjectMeta{Namespace: config.PodNamespace, Name: config.ElectionID},
		Client:    config.Client.CoordinationV1(),
		LockConfig: resourcelock.ResourceLockConfig{
			Identity:      config.PodName,
			EventRecorder: recorder,
		},
	}
	elector, err := leaderelection.NewLeaderElector(leaderelection.LeaderElectionConfig{
		Lock:          lock,
		LeaseDuration: 15 * time.Second,
		RenewDeadline: 10 * time.Second,
		RetryPeriod:   2 * time.Second,
		Callbacks:     callbacks,
	})
	if err != nil {
		logrus.Fatalf("unexpected error starting leader election: %v", err)
	}

	go elector.Run(context.Background())
	return elector
}
