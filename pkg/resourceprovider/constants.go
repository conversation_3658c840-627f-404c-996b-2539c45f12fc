package resourceprovider

import "errors"

var ErrDnatRuleExist = errors.New("EipDnatRuleExist")
var ErrNgwIdNotSpecify = errors.New("NgwIdNotSpecify")
var ErrInvalidDnatProperties = errors.New("invalidDnatProperties")
var ErrEipNotSpecify = errors.New("EipNotSpecify")
var ErrEipNotActivated = errors.New("EipNotActivated")
var ErrEipNotExisted = errors.New("ErrEipNotExisted")
var ErrInternalServerError = errors.New("InternalServerError")
var ErrDnatQuotaUsedUp = errors.New("EipDnatRuleQuotaUsedUp")

// const data define for dgw
const AnnoDgwVirtualPodName = "boson.sensetime.com/dgw-virtual-pod-name"
const AnnoDgwLpCidr = "boson.sensetime.com/dgw-localport-cidr"
const AnnoDgwLpIpAddress = "boson.sensetime.com/dgw-localport-ipaddress"
const AnnoDgwLpMacAddress = "boson.sensetime.com/dgw-localport-macaddress"
const AnnoDgwLpName = "boson.sensetime.com/dgw-localport-name"
const AnnoDgwLpStime = "boson.sensetime.com/dgw-localport-stime"
const AnnoDgwLpEtime = "boson.sensetime.com/dgw-localport-etime"
const AnnoRegion = "boson.sensetime.com/region"
const AnnoAz = "boson.sensetime.com/az"
const AnnoResourceAction = "action"
const AnnoResourceOrderID = "orderid"
const AnnoResourceTenantID = "tenantid"
const AnnoResourceUserID = "userid"
const AnnoBindingIPAMode = "boson.sensetime.com/mode"
const AnnoElasticNicConf = "boson.sensetime.com/elastic-nic-conf"
const AnnoEIPInternalConf = "boson.sensetime.com/eip-internal-conf"
const PGLOCK_VPC = "vpc-pg-lock"
const PGLOCK_EIP = "eip-pg-lock"
const PGLOCK_SLB = "slb-pg-lock"
const PGLOCK_DCVC = "dcvc-pg-lock"

// bms
const (
	BmsMasterNicAnnotation = "boson.sensetime.com/bms_master_nic"
	BmsVlanIdAnnotation    = "boson.sensetime.com/bms_vlan_id"
	BmsGwIpAnnotation      = "boson.sensetime.com/bms_gw_ip"
	BmsGwVipAnnotation     = "boson.sensetime.com/bms_gw_vip"
	BmsCidrAnnotation      = "boson.sensetime.com/bms_cidr"
)

// acl priority
const (
	SYSTEM_ACL_LOW_PRIORITY_MIN      = 10000
	SYSTEM_ACL_LOW_PRIORITY_MAX      = 15000
	CUSTOM_ACL_MID_PRIORITY_MIN      = 20000
	CUSTOM_ACL_MID_PRIORITY_MAX      = 29900
	SYSTEM_ACL_HIGH_PRIORITY_MIN     = 30000
	SYSTEM_ACL_HIGH_PRIORITY_MAX     = 32000
	BMS_2_POD_ACL_PRIORITY           = 32001
	SYSTEM_ACL_RESERVED_PRIORITY_MIN = 32100
	SYSTEM_ACL_RESERVED_PRIORITY_MAX = 32500
	SYSTEM_ACL_GATEWAY_SERVICE_MIN   = 32600
	SYSTEM_ACL_GATEWAY_SERVICE_MAX   = 32900
	SYSTEM_ACL_MAX                   = 40000 // for ct
)

const (
	AclIngressDirection = "to-lport"
	AclEgressDirection  = "from-lport"
)

const (
	AclAllowAction          = "allow"
	AclAllowRelatedAction   = "allow-related"
	AclAllowStatelessAction = "allow-stateless"
	AclDropAction           = "drop"
	AclRejectAction         = "reject"
	AclPassAction           = "pass"
	AclDenyAction           = "deny"
)

const (
	ProtocolICMP = "icmp"
	ProtocolUDP  = "udp"
	ProtocolTCP  = "tcp"
	ProtocolALL  = "all"
)
