package resourceprovider

import (
	"fmt"
	"strconv"
	"strings"

	"github.com/sirupsen/logrus"

	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	vpc "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	utilnet "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils/network"
)

const DNATKind = "Dnat"

func dnatRuleServices(b *types.RMBody) {
	switch b.Method {
	case "ListDNATRules":
		listDNATRules(b)
	case "GetDNATRule":
		getDNATRule(b)
	case "CreateDNATRule":
		createDNATRule(b)
	case "UpdateDNATRule":
		updateDNATRule(b)
	case "DeleteDNATRule":
		deleteDNATRule(b)
	default:
		reason := fmt.Sprintf("Not an avaliable %v service method: %v", b.Service, b.Method)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
	}
}

// for http
func ValidateParametersForCreateDNATRule(request *network.CreateDNATRuleRequest) (string, error) {
	return validateParametersForCreateDNATRule(request)
}

/*
* 1. check parameter validation:
*  quota <= 50
*  rule name union
*  ports not overlap,inner ports count same outer ports count
*  port&protocol union
*  intancetype&(name, IP)
 */
func validateParametersForCreateDNATRule(request *network.CreateDNATRuleRequest) (string, error) {

	has, err := dao.ExistDnatRule(BosonProvider.Engine, request.DnatRuleName)
	if err != nil {
		logrus.Errorln(has, err)
		return err.Error(), ErrInternalServerError
	}

	if has {
		reason := fmt.Sprintf("EIP dnat rule: %v already exist", request.DnatRuleName)
		logrus.Errorln(reason)
		return reason, ErrDnatRuleExist
	}

	//NatGatewayID
	if request.DnatRule.Properties.NatGatewayId == "" {
		reason := fmt.Sprintf("EIP Dnat rule: %v NatGatewayId should be specified", request.DnatRuleName)
		return reason, ErrNgwIdNotSpecify
	}

	_, has, err = dao.GetHaNatGateway(BosonProvider.Engine, request.DnatRule.Properties.NatGatewayId)

	if err != nil {
		logrus.Errorln(has, err)
		return err.Error(), ErrInternalServerError
	}

	if !has {
		reason := fmt.Sprintf("EIP dnat rule:%v NatGatewayId: %v is invalid", request.DnatRuleName, request.DnatRule.Properties.NatGatewayId)
		logrus.Errorln(reason)
		return reason, ErrInvalidDnatProperties
	}

	//eip id, ExternalIp
	if request.DnatRule.Properties.EipId == "" && request.DnatRule.Properties.ExternalIp == "" {
		reason := fmt.Sprintf("EIP Dnat rule: %v Eip id and externalIp should be specified", request.DnatRuleName)
		return reason, ErrInvalidDnatProperties
	}

	eip := db.Eips{}
	if request.DnatRule.Properties.EipId != "" {
		eipTmp, err := dao.GetEipByID(BosonProvider.Engine, request.DnatRule.Properties.EipId, true)
		if err != nil {
			logrus.Errorln(has, err)
			return err.Error(), ErrInternalServerError
		}
		if eipTmp == nil {
			reason := fmt.Sprintf("EIP dnat rule: %v EipId: %v is invalid", request.DnatRuleName, request.DnatRule.Properties.EipId)
			logrus.Errorln(reason)
			return reason, ErrEipNotExisted
		}
		eip = *eipTmp

		if request.DnatRule.Properties.ExternalIp != "" && !strings.EqualFold(eip.EIPIP, request.DnatRule.Properties.ExternalIp) {
			reason := fmt.Sprintf("EIP Dnat rule: %v Eip ip:%v and externalIp:%v are not equal",
				request.DnatRuleName, eip.EIPIP, request.DnatRule.Properties.ExternalIp)
			return reason, ErrInvalidDnatProperties
		}

		request.DnatRule.Properties.ExternalIp = eip.EIPIP
	} else {
		// eip id: null, ExternalIp not null, to, do
		reason := "not support"
		return reason, ErrInvalidDnatProperties
	}

	if !eipTypeNameIsDnat(eip.AssociationType) {
		reason := fmt.Sprintf("EIP type is %s, not support dnat\n", eip.AssociationType)
		return reason, ErrInvalidDnatProperties
	}

	if eip.State != network.EIP_ACTIVE.String() {
		reason := fmt.Sprintf("no permit to operate dnat rule under the eip state %v", eip.State)
		logrus.Errorln(reason)
		return reason, ErrEipNotActivated
	}

	if !strings.EqualFold(request.DnatRule.Properties.Protocol, "TCP") && !strings.EqualFold(request.DnatRule.Properties.Protocol, "UDP") {
		reason := fmt.Sprintf("EIP Dnat rule: %v Protocol[%v] must be TCP or UDP",
			request.DnatRuleName, request.DnatRule.Properties.Protocol)
		return reason, ErrInvalidDnatProperties
	}

	//port, only support one port in a rule
	// not same port in db
	// innerr & outer port
	ePort := strings.Split(request.DnatRule.Properties.ExternalPort, ",")
	if len(ePort) != 1 {
		reason := fmt.Sprintf("EIP Dnat rule: %v ExternalPort invalided: %v", request.DnatRuleName, request.DnatRule.Properties.ExternalPort)
		return reason, ErrInvalidDnatProperties
	}

	if port, err := strconv.Atoi(ePort[0]); err != nil || port < 1 || port > 65535 {
		reason := fmt.Sprintf("EIP Dnat rule: %v ExternalPort invalided: %v", request.DnatRuleName, request.DnatRule.Properties.ExternalPort)
		return reason, ErrInvalidDnatProperties
	}

	if request.DnatRule.Properties.InternalPort != "" {
		iPort := strings.Split(request.DnatRule.Properties.InternalPort, ",")
		if len(iPort) != 1 {
			reason := fmt.Sprintf("EIP Dnat rule: %v InternalPort invalided: %v", request.DnatRuleName, request.DnatRule.Properties.InternalPort)
			return reason, ErrInvalidDnatProperties
		}

		if port, err := strconv.Atoi(iPort[0]); err != nil || port < 1 || port > 65535 {
			reason := fmt.Sprintf("EIP Dnat rule: %v InternalPort invalided: %v", request.DnatRuleName, request.DnatRule.Properties.InternalPort)
			return reason, ErrInvalidDnatProperties
		}
	}

	// port can repeat in different eip
	has, err = dao.CheckDnatRule(BosonProvider.Engine, ePort[0], request.DnatRule.Properties.Protocol, eip.AssociationID, eip.EIPID)
	if err != nil {
		logrus.Errorln(has, err)
		return err.Error(), ErrInternalServerError
	}
	if has {
		reason := fmt.Sprintf("EIP dnat rule: %v port: %v already exists", request.DnatRuleName, ePort[0])
		logrus.Errorln(reason)
		return reason, ErrDnatRuleExist
	}

	// inner instance type & name/ip
	if request.DnatRule.Properties.InternalInstanceType != network.DNATRuleProperties_UNSPECIFIED {
		if request.DnatRule.Properties.InternalInstanceName == "" && request.DnatRule.Properties.InternalIp == "" {
			reason := fmt.Sprintf("EIP dnat rule %s must specify instanceName or internalIp", request.DnatRuleName)
			logrus.Errorln(reason)
			return reason, ErrInvalidDnatProperties
		}

		if request.DnatRule.Properties.InternalPort == "" {
			reason := fmt.Sprintf("EIP dnat rule %s must specify internalPort", request.DnatRuleName)
			logrus.Errorln(reason)
			return reason, ErrInvalidDnatProperties
		}

		// not support service bms instance
		if request.DnatRule.Properties.InternalInstanceType == network.DNATRuleProperties_BMS && eip.InternalEIP {
			subnets, err := dao.ListSubnets(BosonProvider.Engine, eip.VPCID)
			if err != nil {
				logrus.Errorln(err)
				return err.Error(), ErrInternalServerError
			}
			dataInstance := false
			for _, subnet := range subnets {
				if subnet.Provider == vpc.SubnetProperties_CONTROLLER.String() && subnet.Scope == vpc.SubnetProperties_DATA.String() {
					if utilnet.CIDRContainIP(subnet.CIDR, request.DnatRule.Properties.InternalIp) {
						dataInstance = true
						break
					}
				}
			}
			if !dataInstance {
				reason := fmt.Sprintf("EIP dnat rule %s only support data bms instance", request.DnatRuleName)
				logrus.Errorln(reason)
				return reason, ErrInvalidDnatProperties
			}
		}
	}

	//quota
	vpcQuota, err := dao.GetVpcQuotaByVpcID(BosonProvider.Engine, eip.VPCID)
	if err != nil {
		return err.Error(), ErrInternalServerError
	}

	// enterprise eip
	dnatQuota := vpcQuota.EipDnatRuleCount
	if eip.InternalEIP {
		dnatQuota = dnatQuota * 10
	}

	if count, err := dao.GetEipRuleCount(BosonProvider.Engine, eip.EIPID); err != nil || count >= dnatQuota {

		reason := fmt.Sprintf("dnat rule quota for EIP %v is used up! Quota is %v, current existed count is %v ",
			eip.Name, dnatQuota, count)
		logrus.Errorln(reason)

		return reason, ErrDnatQuotaUsedUp
	}

	return "", nil
}

// port allocate
func AllocateNatGatewayPort(protocol, natgwId string) (int32, error) {
	var portNumber int32 = 60000
	for portNumber < 65535 {
		portNumber++
		ok, err := dao.ExistDnatRuleByNatGatewayPort(BosonProvider.Engine, portNumber, protocol, natgwId)
		if err != nil {
			logrus.Errorln(err)
			return 0, err
		}
		if !ok {
			return portNumber, nil
		}
	}
	return 0, fmt.Errorf("no free natgw [%s] port for protocol %s", natgwId, protocol)
}

func listDNATRules(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "DNATRule", "ListDNATRules")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}

func getDNATRule(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "DNATRule", "GetDNATRule")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}

func createDNATRule(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "DNATRule", "CreateDNATRule")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}

func updateDNATRule(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "DNATRule", "UpdateDNATRule")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}

func deleteDNATRule(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "DNATRule", "DeleteDNATRule")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
	}()
}
