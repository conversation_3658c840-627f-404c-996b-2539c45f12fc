package resourceprovider

import (
	"context"

	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

func createNS(ns string, vpc *ovn.Vpc, zone, tenantID, vpcID, vpcName, subnetID, subnetName string) error {
	k8sns := &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: ns,
			Labels: map[string]string{
				"creator": "boson-provider",
				//"zone":        zone,
				"tenant_id":   tenantID,
				"vpc_id":      vpcID,
				"vpc_name":    vpcName,
				"subnet_id":   subnetID,
				"subnet_name": subnetName,
			},
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    VPCKind,
				}),
			},
		},
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("CreateNamespace")
	_, err := BosonProvider.K8sClient.CoreV1().Namespaces().Create(context.Background(), k8sns, metav1.CreateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	return err
}
