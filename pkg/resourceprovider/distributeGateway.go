package resourceprovider

import (
	"context"
	"fmt"
	"net"
	"strings"

	kubeovnv1 "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"github.com/sirupsen/logrus"
	dgwv1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	utilnet "gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils/network"
	"google.golang.org/protobuf/types/known/timestamppb"
	k8s_errors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"
)

const DGWKind = "DGW"
const PolicyRoutingPriority = 4000

func allocateIPv4Address(cidr string, reservedIPs []string) (string, error) {
	dbRTMetrics := exporter.InitDBRTMetrics("ListDGWIPs")
	gws, err := dao.ListGatewayIPs(BosonProvider.Engine)
	dbRTMetrics.ObserveDurationAndLog()
	if err != nil {
		err := fmt.Errorf("List gwIPs from the dgateway error: %s", err)
		return "", err
	}

	m := map[string]bool{}
	for _, v := range gws {
		m[v] = true
	}

	firstIP, err := utilnet.FirstIP(cidr)
	if err != nil {
		return "", err
	}
	lastIP, err := utilnet.LastIP(cidr)
	if err != nil {
		return "", err
	}
	logrus.Infof("CIDR %s firstIP: %s lastIP: %s", cidr, firstIP, lastIP)

	for ip := firstIP; utilnet.CIDRContainIP(cidr, ip) && ip != string(utilnet.IP(lastIP).Add(1)); ip = string(utilnet.IP(ip).Add(1)) {
		if isReservedIP(ip, reservedIPs) {
			continue
		}
		if _, ok := m[ip]; !ok {
			return ip, nil
		}
	}

	return "", nil
}

func isReservedIP(ip string, reservedIPs []string) bool {
	for _, ex := range reservedIPs {
		if utilnet.ContainsIPs(ex, ip) {
			return true
		}
	}

	return false
}

/*
alloc dgw ip
create dgw cr
insert dgw db record
*/
/*
cases := map[string]string{
                "***********/24": "ip4.dst==***********/24",
                "raw:(ip4.dst!=***********/24) || ip4.dst==***********/24 || (ip4.dst!=***********/24)": "(ip4.dst!=***********/24) || ip4.dst==***********/24 || (ip4.dst!=***********/24)",
}
for in,re := range cases {
	if re == makeMatchValue(in) {
			fmt.Printf("succ: %v %v\n", in, re)
	} else {
			fmt.Printf("fail: %v %v %v\n", in, re, makeMatchValue(in))
	}
}
*/

func makeMatchValue(subnet string) (match string) {
	// subnet format:xxx,yyyy,... or
	// format: raw: vpc route policy match string, such as ip4.dst == xxx || ip4.dst == yyy ...
	// return : raw string or ip4.dst == xxx || ip4.dst == yyy ...

	if strings.HasPrefix(subnet, "raw") {
		match = strings.TrimSpace(subnet[3:])
	} else {
		match = fmt.Sprintf("ip4.dst==%s", strings.ReplaceAll(strings.TrimSpace(subnet), ",", " || ip4.dst=="))
	}

	return match
}

func makeDgwPolicy(subnet, nh string) []kubeovnv1.PolicyRoute {
	return []kubeovnv1.PolicyRoute{
		kubeovnv1.PolicyRoute{
			Priority:  PolicyRoutingPriority,
			Match:     makeMatchValue(subnet),
			Action:    "reroute",
			NextHopIP: nh,
		},
	}
}

func CreateDgw(vpcName, dgwName string, admin bool, exsubnet, lanIP, gwPolicy string) error {
	reservedIPs := utilnet.ExpandExcludeIPs(strings.Split(BosonProvider.RpConfig.Boson.BosonDefaultDgw.Reserved, ","), exsubnet)
	v4ExcludeIPs, _ := utilnet.SplitIpsByProtocol(reservedIPs)
	logrus.Infof("The IPv4 exclude IPs %s", v4ExcludeIPs)
	exIP, err := allocateIPv4Address(exsubnet, v4ExcludeIPs)
	if err != nil {
		return fmt.Errorf("VPC %v not exist", vpcName)
	}

	exprefix := strings.Split(exsubnet, "/")
	_, cidr, err := net.ParseCIDR(exsubnet)
	if err != nil {
		reason := fmt.Errorf("invalid parameter exsubnet %v for dgw %v", exsubnet, dgwName)
		logrus.Errorln(reason)
		return reason
	}
	vpcData, err := dao.GetVpcByName(BosonProvider.Engine, vpcName)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	gwID := utils.UUIDGen()
	description := "dgw for vpc: " + vpcData.VPCID
	adminState := network.DGWGatewayStatus_DISABLE.String()
	if admin {
		adminState = network.DGWGatewayStatus_ENABLE.String()
	}

	dgw := &db.DistributeGateways{
		DistributeGatewayID: gwID,
		DisplayName:         dgwName,
		Description:         description,
		GwExternalIP:        exIP,
		InternalIP:          lanIP,
		GwPolicy:            gwPolicy,
		VPCID:               vpcData.VPCID,
		GwExternalCIDR:      exsubnet,
		ResourceType:        "network.vpc.v1.dgw",
		ID:                  fmt.Sprintf("%s/dgw/%s", vpcData.ID, dgwName),
		Name:                dgwName,
		Zone:                BosonProvider.RpConfig.Boson.VPCDefaultAZ,
		State:               network.DGWGateway_CREATING.String(),
		AdminState:          adminState,
		CreatorID:           vpcData.CreatorID,
		OwnerID:             vpcData.OwnerID,
		TenantID:            vpcData.TenantID,
		CreateTime:          timestamppb.Now().AsTime(),
		UpdateTime:          timestamppb.Now().AsTime(),
		Deleted:             false,
	}

	if err := dao.AddDGateway(BosonProvider.Engine, dgw); err != nil {
		return err
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetVPC")

	vpc, err := BosonProvider.KubeovnClient.Vpcs().Get(context.Background(), vpcData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	dgwCR := &dgwv1.DGW{
		TypeMeta: metav1.TypeMeta{
			Kind:       DGWKind,
			APIVersion: dgwv1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: dgwName,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    VPCKind,
				}),
			},
		},
		Spec: dgwv1.DGWSpec{
			Enabled:        admin,
			VPC:            vpcName,
			GwExternalIP:   fmt.Sprintf("%s/%s", exIP, exprefix[1]),
			GwExternalCIDR: fmt.Sprintf("%s/%s", cidr.IP.String(), exprefix[1]),
			BrDataIP:       exsubnet,
			PolicyRoutes:   makeDgwPolicy(gwPolicy, lanIP),
		},
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("CreateNamespace")
	_, err = BosonProvider.BosonNetClient.DGWs(BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs).Create(context.Background(), dgwCR, metav1.CreateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		// rollback
		dbRTMetrics := exporter.InitDBRTMetrics("deleteDGW")
		if err := dao.DeleteDGateways(BosonProvider.Engine, vpcData.VPCID); err != nil {
			dbRTMetrics.ObserveDurationAndLog()
			logrus.Errorf("rollback to delete dgw %s error: %s", dgw.DistributeGatewayID, err)
		}
		dbRTMetrics.ObserveDurationAndLog()
	}

	return err
}

func UpdateDgw(dgw *db.DistributeGateways) error {

	logrus.Infof("start to update DGW %v with %+v", dgw.Name, dgw)
	if err := dao.UpdateDGateway(BosonProvider.Engine, dgw); err != nil {
		logrus.Errorln(err)
		return err
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetDGW")

	// update CR
	dgwCR, err := BosonProvider.BosonNetClient.DGWs(BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs).Get(context.Background(), dgw.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		return err
	}
	dgwCR.Spec.Enabled = dgw.AdminState == network.DGWGatewayStatus_ENABLE.String()
	dgwCR.Spec.PolicyRoutes = makeDgwPolicy(dgw.GwPolicy, dgw.InternalIP)
	k8sRTMetrics = exporter.InitK8sRTMetrics("UpdateDGW")
	if _, err := BosonProvider.BosonNetClient.DGWs(BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs).Update(context.Background(), dgwCR, metav1.UpdateOptions{}); err != nil {
		k8sRTMetrics.ObserveDurationAndLog()
		logrus.Errorln(err)
		return err
	}
	k8sRTMetrics.ObserveDurationAndLog()
	logrus.Infof("finish update DGW %v with %+v", dgw.Name, dgw)

	return nil
}

func DeleteDgw(dgwName string) error {
	logrus.Infof("Delete DGW with %v", dgwName)

	dgw, has, err := dao.GetDGatewayByName(BosonProvider.Engine, dgwName)
	if err != nil {
		logrus.Errorf("Delete DGW %s error: %s", dgwName, err)
		return err
	}

	if !has {
		logrus.Infof("DGW %v is not exist", dgwName)
		return nil
	}

	if dgw.State == network.DGWGateway_DELETING.String() || dgw.State == network.DGWGateway_DELETED.String() {
		logrus.Infof("DGW %v is deleting", dgwName)
		return nil
	}

	if err := dao.SetDGatewayState(BosonProvider.Engine, dgw.DistributeGatewayID, network.DGWGatewayStatus_DELETING.String()); err != nil {
		logrus.Errorln(err)
		return err
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("DeleteDGW")

	// delete CR
	if err := BosonProvider.BosonNetClient.DGWs(BosonProvider.RpConfig.Boson.BosonDefaultDgw.InfraNs).Delete(context.Background(), dgw.Name, metav1.DeleteOptions{}); err != nil {
		k8sRTMetrics.ObserveDurationAndLog()
		if k8s_errors.IsNotFound(err) {
			if err := dao.SetDGatewayState(BosonProvider.Engine, dgw.DistributeGatewayID, network.DGWGatewayStatus_DELETED.String()); err != nil {
				logrus.Errorln(err)
				return err
			}
		} else {
			logrus.Errorln(err)
			return err
		}
	}
	k8sRTMetrics.ObserveDurationAndLog()

	return nil
}
