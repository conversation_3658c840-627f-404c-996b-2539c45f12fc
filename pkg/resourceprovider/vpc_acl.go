package resourceprovider

import (
	"context"
	"fmt"
	"strings"

	kubeovnv1 "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"github.com/sirupsen/logrus"
	v1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/utils/ptr"
)

var (
	VpcAclNamespace     = "plat-boson-infra"
	VpcNameLabel        = "boson.sensetime.com/vpc-name"
	VpcAclResourceLabel = "boson.sensetime.com/vpc-acl-source"
	VpcAclResourceValue = "configmap"

	ConfigMapName   = "boson-provider-config"
	ConfigMapAclKey = "boson-provider.yaml"
	OvnVpc          = "ovn-cluster"
)

func SyncVPCAcls(vpcAcls []config.VpcAcl, vpc *kubeovnv1.Vpc) {

	vpcAclsCli := BosonProvider.BosonNetClient.VpcAcls(VpcAclNamespace)

	// skip the ovn vpc acl config
	if vpc.Name == OvnVpc {
		return
	}

	labelSelector := fmt.Sprintf("%s=%s,%s=%s", VpcAclResourceLabel, VpcAclResourceValue, VpcNameLabel, vpc.Name)
	acls, err := vpcAclsCli.List(context.Background(), metav1.ListOptions{LabelSelector: labelSelector})
	if err != nil {
		logrus.Warningf("can not list acl in vpc: %s in k8s, err: %s", vpc.Name, err)
		return
	}

	toCreate, toUpdate, toDelete := filterAcls(vpc, vpcAcls, acls)

	for _, acl := range toCreate {
		logrus.Infof("intend to create vpc acl with vpc [%s], acl-name [%s], acl-priority[%d]", vpc.Name, acl.Name, acl.Spec.Priority)
		_, err = vpcAclsCli.Create(context.Background(), &acl, metav1.CreateOptions{})
		if err != nil {
			logrus.Warningf("can not create vpc acl with vpc [%s], acl-name [%s], acl-priority[%d], with err: %s", vpc.Name, acl.Name, acl.Spec.Priority, err)
		}
	}
	for _, acl := range toUpdate {
		logrus.Infof("intend to update vpc acl with vpc [%s], acl-name [%s], acl-priority[%d]", vpc.Name, acl.Name, acl.Spec.Priority)
		_, err = vpcAclsCli.Update(context.Background(), &acl, metav1.UpdateOptions{})
		if err != nil {
			logrus.Warningf("can not update vpc acl with vpc [%s], acl-name [%s], acl-priority[%d], with err: %s", vpc.Name, acl.Name, acl.Spec.Priority, err)
		}
	}
	for _, acl := range toDelete {
		logrus.Infof("intend to delete vpc acl with vpc [%s], acl-name [%s], acl-priority[%d]", vpc.Name, acl.Name, acl.Spec.Priority)
		err = vpcAclsCli.Delete(context.Background(), acl.Name, metav1.DeleteOptions{})
		if err != nil {
			logrus.Warningf("can not delete vpc acl with vpc [%s], acl-name [%s], acl-priority[%d], with err: %s", vpc.Name, acl.Name, acl.Spec.Priority, err)
		}
	}

}

func filterAcls(vpc *kubeovnv1.Vpc, vpcAcls []config.VpcAcl, acls *v1.VpcAclList) ([]v1.VpcAcl, []v1.VpcAcl, []v1.VpcAcl) {
	toCreate := make([]v1.VpcAcl, 0)
	toUpdate := make([]v1.VpcAcl, 0)
	toDelete := make([]v1.VpcAcl, 0)

	for _, vpcAcl := range vpcAcls {
		found := false
		var acl v1.VpcAcl
		for _, acl = range acls.Items {
			spec := acl.Spec
			if vpcAcl.Priority == spec.Priority {
				found = true
				if vpcAcl.SrcIp == spec.Src && vpcAcl.SrcPort == spec.SrcPort && vpcAcl.DestIp == spec.Dest && vpcAcl.DestPort == spec.DestPort && vpcAcl.Action == spec.Action && vpcAcl.Protocol == spec.Protocol {
					break
				}
				acl.Spec.Src = vpcAcl.SrcIp
				acl.Spec.SrcPort = vpcAcl.SrcPort
				acl.Spec.Dest = vpcAcl.DestIp
				acl.Spec.DestPort = vpcAcl.DestPort
				acl.Spec.Action = vpcAcl.Action
				acl.Spec.Protocol = vpcAcl.Protocol
				toUpdate = append(toUpdate, acl)
			}
		}
		if !found {
			name := fmt.Sprintf("acl-%s-%s", strings.TrimPrefix(vpc.Name, "vpc-"), utils.Rand8())

			ownerReference := metav1.OwnerReference{
				APIVersion:         ovn.SchemeGroupVersion.String(),
				Controller:         ptr.To[bool](true),
				BlockOwnerDeletion: ptr.To[bool](true),
				Kind:               VPCKind,
				Name:               vpc.Name,
				UID:                vpc.UID,
			}
			acl := v1.VpcAcl{
				TypeMeta: metav1.TypeMeta{
					Kind:       "VpcAcl",
					APIVersion: v1.SchemeGroupVersion.String(),
				},
				ObjectMeta: metav1.ObjectMeta{
					Name:      name,
					Namespace: VpcAclNamespace,
					Labels: map[string]string{
						VpcNameLabel:        vpc.Name,
						VpcAclResourceLabel: VpcAclResourceValue,
					},
					OwnerReferences: []metav1.OwnerReference{
						ownerReference,
					},
				},
				Spec: v1.VpcAclSpec{
					Src:      vpcAcl.SrcIp,
					SrcPort:  vpcAcl.SrcPort,
					Dest:     vpcAcl.DestIp,
					DestPort: vpcAcl.DestPort,
					AclID:    name,
					Vpc:      vpc.Name,
					Action:   vpcAcl.Action,
					Protocol: vpcAcl.Protocol,
					Priority: vpcAcl.Priority,
				},
			}
			toCreate = append(toCreate, acl)
		}
	}
	for _, acl := range acls.Items {
		found := false
		var vpcAcl config.VpcAcl
		for _, vpcAcl = range vpcAcls {
			if acl.Spec.Priority == vpcAcl.Priority {
				found = true
				break
			}
		}
		if !found {
			toDelete = append(toDelete, acl)
		}
	}

	return toCreate, toUpdate, toDelete
}
