package resourceprovider

import (
	"context"
	"encoding/json"
	"errors"
	"fmt"
	"strings"
	"time"

	"k8s.io/client-go/util/retry"

	"github.com/allisson/go-pglock/v3"
	nadv1 "github.com/k8snetworkplumbingwg/network-attachment-definition-client/pkg/apis/k8s.cni.cncf.io/v1"
	ovn "github.com/kubeovn/kube-ovn/pkg/apis/kubeovn/v1"
	"github.com/sirupsen/logrus"
	"google.golang.org/protobuf/types/known/timestamppb"
	k8s_errors "k8s.io/apimachinery/pkg/api/errors"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime/schema"

	v1 "gitlab.bj.sensetime.com/elementary/boson/boson-net-controller/apis/network/v1"
	network "gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/db/dao"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/types"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

// VPCKind crd kind
const VPCKind = "Vpc"
const NADNamespace = "plat-boson-service"
const NADConfig = `{
      "cniVersion": "0.3.1",
      "plugins": [
      {
        "type": "roce-host-device",
		"ipam": {
			"type": "kube-ovn",
			"server_socket": "/run/openvswitch/kube-ovn-daemon.sock",
			"provider": "<nad_name>.plat-boson-service"
		},
        "if_id": "<roce_id>",
        "server_socket": "/run/roce-cni/daemon.sock"
      }
      ]
    }`
const ResourceName = "rdma-training/roce"

func vpcServices(b *types.RMBody) {
	switch b.Method {
	case "ListVPCs":
		listVPCs(b)
	case "GetVPC":
		getVPC(b)
	case "CreateVPC":
		createVPC(b)
	case "UpdateVPC":
		updateVPC(b)
	case "DeleteVPC":
		deleteVPC(b)
	case "ForceDelete":
		deleteVPC(b)
	default:
		reason := fmt.Sprintf("Not an avaliable %v service method: %v", b.Service, b.Method)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("invalid method for VPC: %s", b.ResourceID)
	}
}

func createVPC(b *types.RMBody) {

	var err error = nil
	reason := ""

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "VPC", "CreateVPC")
	defer func() {
		if err == nil && reason == "" {
			mqRTMetrics.Result = types.ActionResultSucceeded
		}
		mqRTMetrics.ObserveDurationAndLog()
		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("create VPC %s result %s", b.ResourceID, mqRTMetrics.Result)
	}()

	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": b.CallbackData.TenantID,
		"user_id":   b.CallbackData.UserID,
		"service":   b.Service,
		"method":    b.Method,
		"action":    b.Action,
		"data":      b.Data}).Infof("create VPC %s starting...", b.ResourceID)

	request := network.CreateVPCRequest{}
	js, _ := json.Marshal(b.Data)
	err = json.Unmarshal(js, &request)
	if err != nil {
		logrus.Errorln(err)
		reason = fmt.Sprintf("Not an avaliable %v service %v method body for CreateVPCRequest: %+v", b.Service, b.Method, b.Data)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}

	if request.Zone != db.Region {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.Region)
		logrus.Errorln(err)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, err.Error(), b.CallbackData)
		return
	}

	if request.Vpc.CreateTime == nil {
		request.Vpc.CreateTime = timestamppb.Now()
	}

	if request.Vpc.UpdateTime == nil {
		request.Vpc.UpdateTime = request.Vpc.CreateTime
	}

	has, err := dao.CheckVpcExists(BosonProvider.Engine, request.VpcName)
	if err != nil {
		logrus.Errorln(has, err)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}
	if has {
		reason = fmt.Sprintf("VPC %v already exist", request.VpcName)
		logrus.Errorln(reason)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, reason, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}
	if request.Vpc.Properties == nil {
		reason = "request.Vpc.Properties is nil"
		logrus.Errorln(reason)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}
	logrus.Infof("request: %v to create vpc %s\n", request.Vpc.Properties.SubnetType, request.VpcName)

	if request.Vpc.Properties.SubnetType == network.SubnetType_NULL {
		reason = "SubnetType NULL reserved"
		logrus.Errorln(reason)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, reason, b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}

	seed := fmt.Sprintf("%s-%s", db.AZ, PGLOCK_VPC)
	pglockId := utils.HashStringToInt32(seed)
	logrus.Infof("vpc: %s,init PG Lock ID %d for %s (region-vpcname)", request.VpcName, pglockId, seed)

	var dlock pglock.Lock
	ctx := context.Background()
	timeout := BosonProvider.RpConfig.Boson.VPCPgLockTimeOut
	childCtx, cancel := context.WithTimeout(ctx, time.Duration(timeout)*time.Second)
	dbLockRTMetrics := exporter.InitDBRTMetrics("PgLockWhenCreateVPC")
	err = func() error {
		defer cancel()
		dlock, err = pglock.NewLock(childCtx, int64(pglockId), BosonProvider.Engine.DB().DB)
		if err != nil {
			logrus.Errorf("vpc: %s, new pglock for pgLockID %d failed, error: %s", request.VpcName, pglockId, err.Error())
			return err
		}
		logrus.Infof("vpc: %s, new pglock for pgLockID %d success", request.VpcName, pglockId)

		err = dlock.WaitAndLock(childCtx)
		if err != nil {
			logrus.Errorf("vpc: %s, get pglock for pgLockID %d failed, error %s", request.VpcName, pglockId, err.Error())
			return err
		}
		return nil
	}()
	dbLockRTMetrics.ObserveDurationAndLog()
	defer dlock.Close()

	defer func() {
		_ = dlock.Unlock(ctx) // protect end here
		logrus.Infof("vpc: %s, unlock pglock for pgLockID %d", request.VpcName, pglockId)
	}()

	if err != nil { // 可能锁超时，或者其它不可预知的问题
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}

	nsUID := utils.UUIDGen()
	ns := "ns-" + utils.Substring(b.TenantCode, 0, 20) + "-" + nsUID[:8]

	subnetGwIP, err := utils.GetTheFirstAddrFromCIDR(BosonProvider.RpConfig.Boson.GeneveSubnetCIDR)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}

	exPre9, err := utils.GetFirstxAddrsFromCIDR(9, BosonProvider.RpConfig.Boson.GeneveSubnetCIDR)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}

	exSuf5, err := utils.GetLastxAddrsFromCIDR(5, BosonProvider.RpConfig.Boson.GeneveSubnetCIDR)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}

	localIp, err := utils.CalcTheLastXIPFromCidr(BosonProvider.RpConfig.Boson.GeneveSubnetCIDR, 5)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}

	haNatGwSession := BosonProvider.Engine.NewSession()
	defer haNatGwSession.Close()

	vpcHaNatGwBuilder := NewHaNatGwBuilder(haNatGwSession)
	err = vpcHaNatGwBuilder.Prepare(b, &request)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}
	logrus.Infof("prepare for hanatgw done for vpc %s", request.VpcName)

	vpcQuota := db.VpcQuota{
		VPCID:                request.Vpc.Uid,
		GeneveSubnetCount:    BosonProvider.RpConfig.Boson.BosonDefaultQuota.GeneveSubnetsPerVPC,
		VlanSubnetCount:      BosonProvider.RpConfig.Boson.BosonDefaultQuota.VlanSubnetsPerVPC,
		IBSubnetCount:        BosonProvider.RpConfig.Boson.BosonDefaultQuota.IBSubnetsPerVPC,
		RoceSubnetCount:      BosonProvider.RpConfig.Boson.BosonDefaultQuota.RoceSubnetsPerVPC,
		EipCount:             BosonProvider.RpConfig.Boson.BosonDefaultQuota.EIPsPerVPC,
		NatGatewayCount:      BosonProvider.RpConfig.Boson.BosonDefaultQuota.NATGatewaysPerVPC,
		EipDnatRuleCount:     BosonProvider.RpConfig.Boson.BosonDefaultQuota.DnatRulesPerEip,
		SlbPerVpc:            BosonProvider.RpConfig.Boson.BosonDefaultQuota.SlbPerVpc,
		ListenerPerSlb:       BosonProvider.RpConfig.Boson.BosonDefaultQuota.ListenerPerSlb,
		RulePerSlb:           BosonProvider.RpConfig.Boson.BosonDefaultQuota.RulePerSlb,
		TargetGroupPerSlb:    BosonProvider.RpConfig.Boson.BosonDefaultQuota.TargetGroupPerSlb,
		TargetPerTargetGroup: BosonProvider.RpConfig.Boson.BosonDefaultQuota.TargetPerTargetGroup,
		PeerCount:            10,
	}

	err = dao.AddVPCQuota(BosonProvider.Engine, &vpcQuota)
	if err != nil {
		logrus.Errorln(err)
		logrus.Infof("Create vpcQuota %v fail in the vpc %s\n", vpcQuota, request.VpcName)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}

	vpcData := db.Vpcs{
		VPCID:        request.Vpc.Uid,
		DisplayName:  utils.EmptyToBackup(request.Vpc.DisplayName, request.Vpc.Name),
		CIDR:         BosonProvider.RpConfig.Boson.VPCCIDR,
		Description:  request.Vpc.Description,
		IsDefault:    false,
		ID:           request.Vpc.Id,
		Name:         request.Vpc.Name,
		ResourceType: request.Vpc.ResourceType,
		Zone:         BosonProvider.RpConfig.Boson.VPCDefaultAZ,
		State:        network.VPC_State_name[int32(network.VPC_ACTIVE)],
		CreatorID:    request.Vpc.CreatorId,
		OwnerID:      request.Vpc.OwnerId,
		TenantID:     request.Vpc.TenantId,
		SubnetType:   request.Vpc.Properties.SubnetType.String(),
		CreateTime:   request.Vpc.CreateTime.AsTime(),
		UpdateTime:   request.Vpc.UpdateTime.AsTime(),
		Deleted:      false,
	}

	err = dao.AddVPC(BosonProvider.Engine, &vpcData)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}
	vpc := &ovn.Vpc{
		TypeMeta: metav1.TypeMeta{
			Kind:       VPCKind,
			APIVersion: ovn.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: request.VpcName,
			Annotations: map[string]string{
				"action":           b.Action,
				"orderid":          b.CallbackData.OrderID,
				"tenantid":         b.CallbackData.TenantID,
				"userid":           b.CallbackData.UserID,
				AnnoDgwLpStime:     request.Vpc.CreateTime.AsTime().Format(time.RFC3339),
				AnnoDgwLpIpAddress: localIp,
				AnnoDgwLpCidr:      BosonProvider.RpConfig.Boson.GeneveSubnetCIDR,
				AnnoRegion:         db.Region,
				AnnoAz:             db.AZ,
			},
		},
		Spec: ovn.VpcSpec{
			Namespaces: []string{ns},
			StaticRoutes: []*ovn.StaticRoute{
				&ovn.StaticRoute{
					CIDR:      subnetGwIP + "/0",
					NextHopIP: vpcHaNatGwBuilder.GetNextHopIp(),
					Policy:    ovn.PolicyDst,
				},
			},
		},
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("CreateVPC")
	var res *ovn.Vpc
	res, err = BosonProvider.KubeovnClient.Vpcs().Create(context.Background(), vpc, metav1.CreateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(res, err)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("GetVPC")
	vpc, err = BosonProvider.KubeovnClient.Vpcs().Get(context.Background(), vpc.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	for err != nil {
		logrus.Errorln(err)
		k8sRTMetrics = exporter.InitK8sRTMetrics("GetVPC")
		vpc, err = BosonProvider.KubeovnClient.Vpcs().Get(context.Background(), vpc.Name, metav1.GetOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
	}

	if request.Vpc.Properties.SubnetType == network.SubnetType_POD_SERVICE_TRAINING || request.Vpc.Properties.SubnetType == network.SubnetType_POD_BMS_SERVICE {
		// Create ovn Training subnets
		err = addTraniningSubnets(&request, vpc, b.TenantCode)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(
				request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
			return
		}
	}

	if request.Vpc.Properties.SubnetType == network.SubnetType_POD_BMS_SERVICE {
		// Create BMS_SERVICE subnets
		logrus.Infof("add BMS_SERVICE subnets in vpc %s\n", vpc.Name)
		err = addBmsServiceDataSubnets(&request, vpc, b.TenantCode)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
			return
		}
	}

	if request.Vpc.Properties.SubnetType == network.SubnetType_POD_BMS_SERVICE_TRAINING {
		// Create ovn Training subnets
		err = addTraniningSubnets(&request, vpc, b.TenantCode)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(
				request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
			return
		}

		// Create BMS subnets
		logrus.Infof("add bmsSubnets in vpc %s\n", vpc.Name)
		err = addBmsSubnets(&request, vpc, b.TenantCode)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
			return
		}
	}

	if request.Vpc.Properties.SubnetType == network.SubnetType_POD_SERVICE_BMS {
		// Create BMS subnets
		logrus.Infof("add bms Subnets in vpc %s\n", vpc.Name)
		err = addBmsSubnets(&request, vpc, b.TenantCode)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
			return
		}
	}

	// Get BMS data network
	logrus.Infof("get bmsSubnet with scope %s in vpc_id %s\n", network.SubnetProperties_DATA.String(), request.Vpc.Uid)
	bmsSubnet, _ := dao.GetBmsSubnet(BosonProvider.Engine, network.SubnetProperties_DATA.String(), request.Vpc.Uid)

	// 临时创建方案，能连上 RM 之后，此处逻辑调整为调用 RM API 去创建 Subnet
	subnetID := utils.UUIDGen()
	subnetName := "sn-" + utils.Substring(b.TenantCode, 0, 20) + "-" + subnetID[:8]
	subnet := &ovn.Subnet{
		TypeMeta: metav1.TypeMeta{
			Kind:       SubnetKind,
			APIVersion: ovn.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: subnetName,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    VPCKind,
				}),
			},
			Labels: map[string]string{
				"creator":                     "boson-provider",
				"kubernetes.io/metadata.name": subnetName,
				"subnet_id":                   subnetID,
				"subnet_scope":                network.SubnetProperties_SERVICE.String(),
				"subnet_type":                 network.SubnetProperties_GENEVE.String(),
				"tenant_id":                   vpcData.TenantID,
				"vni":                         fmt.Sprintf("%v", 0),
				"vpc_id":                      vpcData.VPCID,
				"vpc.Name":                    vpcData.Name,
			},
			Annotations: map[string]string{
				AnnoDgwLpIpAddress: localIp,
				AnnoRegion:         db.Region,
				AnnoAz:             db.AZ,
			},
		},
		Spec: ovn.SubnetSpec{
			Namespaces:  []string{ns},
			Vpc:         request.VpcName,
			CIDRBlock:   BosonProvider.RpConfig.Boson.GeneveSubnetCIDR,
			Protocol:    ovn.ProtocolIPv4,
			NatOutgoing: false,
			Default:     false,
			ExcludeIps:  []string{exPre9, exSuf5},
			Acls:        BosonProvider.RpConfig.Boson.VPCDefaulAcls,
		},
	}
	logrus.Infof("create vpc: %s subnet: %s defaul acls: %+v", vpc.Name, subnetName, BosonProvider.RpConfig.Boson.VPCDefaulAcls)

	if BosonProviderConfig.Boson.BmsMasterNic != "" && bmsSubnet.CIDR != "" {
		bmsAcl := ovn.Acl{
			Direction: AclEgressDirection,
			Priority:  BMS_2_POD_ACL_PRIORITY,
			Match:     "ip4.dst==" + bmsSubnet.CIDR,
			Action:    AclAllowAction,
		}
		logrus.Infof("create vpc: %s subnet: %s bms acls: %+v", vpc.Name, subnetName, bmsAcl)
		subnet.Spec.Acls = append(subnet.Spec.Acls, bmsAcl)
	}

	k8sRTMetrics = exporter.InitK8sRTMetrics("CreateSubnet")
	var subnetRes *ovn.Subnet
	subnetRes, err = BosonProvider.KubeovnClient.Subnets().Create(context.Background(), subnet, metav1.CreateOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(subnetRes, err)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}

	// create vpcacls
	SyncVPCAcls(BosonProvider.RpConfig.Boson.VPCAcls, vpc)

	err = createNS(ns, vpc, request.Zone, request.Vpc.TenantId, request.Vpc.Uid, request.Vpc.Name, subnetID, subnetName)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}

	if BosonProviderConfig.Boson.BmsMasterNic != "" && bmsSubnet.CIDR != "" {
		if err = vpcHaNatGwBuilder.prepareBmsGwIp(bmsSubnet); err != nil {
			logrus.Errorf("prepare bmsGwIp failed: %s", err)
			BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
			BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
			return
		}
	}

	// create vpc ha nat gw
	err = vpcHaNatGwBuilder.BuildAll(vpc, subnet, subnetID, bmsSubnet)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}
	logrus.Infof("hanatgw build done for vpc %s", vpc.Name)

	subnetData := db.Subnets{
		SubnetID:     subnetID,
		VPCID:        request.Vpc.Uid,
		DisplayName:  subnet.Name,
		CIDR:         subnet.Spec.CIDRBlock,
		ReservedIPs:  fmt.Sprintf("%s,%s", exPre9, exSuf5),
		Description:  request.Vpc.Description,
		IsDefault:    true,
		GatewayIP:    subnetGwIP,
		Scope:        network.SubnetProperties_SERVICE.String(),
		Provider:     network.SubnetProperties_OVN.String(),
		NetworkType:  network.SubnetProperties_GENEVE.String(),
		ID:           utils.GenRMID(request.SubscriptionName, request.ResourceGroupName, request.Zone, "subnets", subnet.Name),
		Name:         subnet.Name,
		ResourceType: "network.vpc.v1.subnet",
		Zone:         BosonProvider.RpConfig.Boson.VPCDefaultAZ,
		State:        network.VPC_State_name[int32(network.VPC_ACTIVE)],
		CreatorID:    request.Vpc.CreatorId,
		OwnerID:      request.Vpc.OwnerId,
		TenantID:     request.Vpc.TenantId,
		CreateTime:   request.Vpc.CreateTime.AsTime(),
		UpdateTime:   request.Vpc.UpdateTime.AsTime(),
		Deleted:      false,
	}

	err = dao.AddSubnet(BosonProvider.Engine, &subnetData)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}

	k8sNs := db.K8sNamespaces{
		NamespaceID: nsUID,
		VPCID:       request.Vpc.Uid,
		SubnetID:    subnetData.SubnetID,
		Name:        ns,
	}

	err = dao.AddNamespace(BosonProvider.Engine, &k8sNs)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_FAILED.String())
		return
	}

	logrus.Infoln("Created VPC: ", vpc.Name)

	BosonProvider.Processor.SuccessActionMessageSend(request.Vpc.Uid, request.Vpc.ResourceType, b.Action, b.CallbackData)
	BosonProvider.Processor.StateMessageSend(request.Vpc.Id, network.VPC_ACTIVE.String())
}

func updateVPC(b *types.RMBody) {
	var err error = nil
	reason := ""

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "VPC", "UpdateVPC")
	defer func() {
		if err == nil && reason == "" {
			mqRTMetrics.Result = types.ActionResultSucceeded
		}
		mqRTMetrics.ObserveDurationAndLog()
		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("update VPC %s result %s", b.ResourceID, mqRTMetrics.Result)
	}()

	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": b.TenantID,
		"user_id":   b.CallbackData.UserID,
		"service":   b.Service,
		"method":    b.Method,
		"action":    b.Action,
		"data":      b.Data,
	}).Infof("update VPC %s starting...", b.ResourceID)

	request := network.UpdateVPCRequest{}
	js, _ := json.Marshal(b.Data)

	err = json.Unmarshal(js, &request)
	if err != nil {
		logrus.Errorln(err)
		reason = fmt.Sprintf("Not an avaliable %v service %v method body for UpdateVPCRequest: %+v", b.Service, b.Method, b.Data)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
		return
	}

	if request.Zone != db.Region {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.Region)
		logrus.Errorln(err)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, err.Error(), b.CallbackData)
		return
	}

	if request.Vpc.GetDisplayName() == "" && request.Vpc.Description == "" && request.Vpc.SubnetType == network.SubnetType_NULL {
		reason = "just support to update DisplayName & Description & SubnetType"
		logrus.Errorln(reason)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
		return
	}

	var vpcData *db.Vpcs
	vpcData, err = dao.GetVpcByName(BosonProvider.Engine, request.VpcName)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, err.Error(), b.CallbackData)
		return
	}
	logrus.Infof("updated VPC %s, SubnetType %s\n", vpcData.Name, request.Vpc.SubnetType.String())

	if request.Vpc.GetDisplayName() != "" {
		vpcData.DisplayName = request.Vpc.GetDisplayName()
	}

	if request.Vpc.Description != "" {
		vpcData.Description = request.Vpc.Description
	}

	if vpcData.SubnetType == network.SubnetType_POD_BMS_SERVICE_TRAINING.String() {
		reason = "not support to update SubnetType BMS_TRAINING"
		logrus.Errorln(reason)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
	}

	UpdateTime := timestamppb.Now()
	vpcData.UpdateTime = UpdateTime.AsTime()

	createVPCRequest := network.CreateVPCRequest{
		Vpc: &network.VPC{
			TenantId:    vpcData.TenantID,
			Uid:         vpcData.VPCID,
			Description: vpcData.Description,
			CreatorId:   vpcData.CreatorID,
			OwnerId:     vpcData.OwnerID,
			CreateTime:  UpdateTime,
			UpdateTime:  UpdateTime,
		},
		SubscriptionName:  request.SubscriptionName,
		ResourceGroupName: request.ResourceGroupName,
		Zone:              request.Zone,
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("GetVPC")
	var vpc *ovn.Vpc
	vpc, err = BosonProvider.KubeovnClient.Vpcs().Get(context.Background(), vpcData.Name, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.FailedActionMessageSend(vpcData.VPCID, vpcData.ResourceType, b.Action, err.Error(), b.CallbackData)
		return
	}

	if vpcData.SubnetType == network.SubnetType_POD_SERVICE.String() && request.Vpc.SubnetType == network.SubnetType_POD_SERVICE_TRAINING ||
		vpcData.SubnetType == network.SubnetType_POD_SERVICE.String() && request.Vpc.SubnetType == network.SubnetType_POD_BMS_SERVICE_TRAINING ||
		vpcData.SubnetType == network.SubnetType_POD_SERVICE_BMS.String() && request.Vpc.SubnetType == network.SubnetType_POD_BMS_SERVICE_TRAINING {
		// Create ovn Training subnets
		err = addTraniningSubnets(&createVPCRequest, vpc, b.TenantCode)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(vpcData.VPCID, vpcData.ResourceType, b.Action, err.Error(), b.CallbackData)
			return
		}
	}

	if vpcData.SubnetType == network.SubnetType_POD_SERVICE.String() || vpcData.SubnetType == network.SubnetType_POD_SERVICE_TRAINING.String() {
		if request.Vpc.SubnetType == network.SubnetType_POD_BMS_SERVICE || request.Vpc.SubnetType == network.SubnetType_POD_BMS_SERVICE_TRAINING {
			// Create BMS_SERVICE subnets
			logrus.Infof("add BMS_SERVICE subnets in vpc %s\n", vpcData.Name)
			err = addBmsServiceDataSubnets(&createVPCRequest, vpc, b.TenantCode)
			if err != nil {
				logrus.Errorln(err)
				BosonProvider.Processor.FailedActionMessageSend(vpcData.VPCID, vpcData.ResourceType, b.Action, err.Error(), b.CallbackData)
				return
			}

			if BosonProviderConfig.Boson.BmsMasterNic != "" {
				err = AddBms2Pod(vpcData, vpc)
				if err != nil {
					logrus.Errorln(err)
					BosonProvider.Processor.FailedActionMessageSend(vpcData.VPCID, vpcData.ResourceType, b.Action, err.Error(), b.CallbackData)
					return
				}
			}

			err = updateEipSpecSubnet(vpcData)
			if err != nil {
				logrus.Errorln(err)
				BosonProvider.Processor.FailedActionMessageSend(vpcData.VPCID, vpcData.ResourceType, b.Action, err.Error(), b.CallbackData)
				return
			}
		}
	}

	if vpcData.SubnetType == network.SubnetType_POD_SERVICE.String() || vpcData.SubnetType == network.SubnetType_POD_SERVICE_TRAINING.String() || vpcData.SubnetType == network.SubnetType_POD_BMS_SERVICE.String() {
		if request.Vpc.SubnetType == network.SubnetType_POD_BMS_SERVICE_TRAINING {
			// Create BMS subnets
			logrus.Infof("add bms Subnets in vpc %s\n", vpcData.Name)
			err = addBmsTrainingSubnets(&createVPCRequest, vpc, b.TenantCode)
			if err != nil {
				logrus.Errorln(err)
				BosonProvider.Processor.FailedActionMessageSend(vpcData.VPCID, vpcData.ResourceType, b.Action, err.Error(), b.CallbackData)
				return
			}
		}
	}

	if vpcData.SubnetType == network.SubnetType_POD_SERVICE.String() && request.Vpc.SubnetType == network.SubnetType_POD_SERVICE_BMS {
		// Create BMS subnets
		err = addBmsSubnets(&createVPCRequest, vpc, b.TenantCode)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(vpcData.VPCID, vpcData.ResourceType, b.Action, err.Error(), b.CallbackData)
			return
		}

		if BosonProviderConfig.Boson.BmsMasterNic != "" {
			err = AddBms2Pod(vpcData, vpc)
			if err != nil {
				logrus.Errorln(err)
				BosonProvider.Processor.FailedActionMessageSend(vpcData.VPCID, vpcData.ResourceType, b.Action, err.Error(), b.CallbackData)
				return
			}
		}

		err = updateEipSpecSubnet(vpcData)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(vpcData.VPCID, vpcData.ResourceType, b.Action, err.Error(), b.CallbackData)
			return
		}
	}

	if request.Vpc.SubnetType.String() != vpcData.SubnetType {
		vpcData.SubnetType = request.Vpc.SubnetType.String()
	}

	err = dao.UpdateVPC(BosonProvider.Engine, vpcData)
	if err != nil {
		logrus.Errorln(err)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, err.Error(), b.CallbackData)
		return
	}

	BosonProvider.Processor.SuccessActionMessageSend(vpcData.VPCID, vpcData.ResourceType, b.Action, b.CallbackData)
}

func getVPC(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "VPC", "GetVPC")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("get VPC %s result %s", b.ResourceID, mqRTMetrics.Result)
	}()
	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": b.TenantID,
		"user_id":   b.CallbackData.UserID,
		"service":   b.Service,
		"method":    b.Method,
		"action":    b.Action,
		"data":      b.Data,
	}).Infof("get VPC %s starting...", b.ResourceID)
}

func listVPCs(b *types.RMBody) {
	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "VPC", "ListVPCs")
	defer func() {
		mqRTMetrics.Result = types.ActionResultNotImplemented
		mqRTMetrics.ObserveDurationAndLog()
		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("list VPC %s result %s", b.ResourceID, mqRTMetrics.Result)
	}()
	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": b.TenantID,
		"user_id":   b.CallbackData.UserID,
		"service":   b.Service,
		"method":    b.Method,
		"action":    b.Action,
		"data":      b.Data,
	}).Infof("list VPC %s starting...", b.ResourceID)
}

func deleteVPC(b *types.RMBody) {

	var err error = nil
	reason := ""

	mqRTMetrics := exporter.InitMQRTMetrics(b.TenantCode, "VPC", "DeleteVPC")
	defer func() {
		if err == nil && reason == "" {
			mqRTMetrics.Result = types.ActionResultSucceeded
		}
		mqRTMetrics.ObserveDurationAndLog()
		logrus.WithFields(logrus.Fields{
			"audit":     true,
			"tenant_id": b.TenantID,
			"user_id":   b.CallbackData.UserID,
			"service":   b.Service,
			"method":    b.Method,
			"action":    b.Action,
			"data":      b.Data,
		}).Infof("delete VPC %s result %s", b.ResourceID, mqRTMetrics.Result)
	}()
	logrus.WithFields(logrus.Fields{
		"audit":     true,
		"tenant_id": b.TenantID,
		"user_id":   b.CallbackData.UserID,
		"service":   b.Service,
		"method":    b.Method,
		"action":    b.Action,
		"data":      b.Data,
	}).Infof("delete VPC %s starting...", b.ResourceID)

	request := network.DeleteVPCRequest{}

	if b.Data != "" {
		js, _ := json.Marshal(b.Data)

		err = json.Unmarshal(js, &request)
		if err != nil {
			logrus.Errorln(err)
			reason = fmt.Sprintf("Not an avaliable %v service %v method body for DeleteVPCRequest: %+v", b.Service, b.Method, b.Data)
			BosonProvider.Processor.ErrorActionMessageSend(b.Action, reason, b.CallbackData)
			return
		}
	} else {
		vpcData, err := dao.GetVpc(BosonProvider.Engine, b.ResourceID)
		if err != nil {
			logrus.Errorln(err)
			BosonProvider.Processor.FailedActionMessageSend(b.ResourceID, "", b.Action, err.Error(), b.CallbackData)
			return
		}
		request.Zone = vpcData.Zone
		request.VpcName = vpcData.Name
	}

	if request.Zone != db.Region {
		err = fmt.Errorf("request zone %s not match provider zone %s", request.Zone, db.Region)
		logrus.Errorln(err)
		BosonProvider.Processor.ErrorActionMessageSend(b.Action, err.Error(), b.CallbackData)
		return
	}

	vpc, err := removeVpc(request.Zone, request.VpcName)
	if err != nil {
		logrus.Errorf("Delete vpc[%s] error: %+v", request.VpcName, err)
		if vpc == nil {
			BosonProvider.Processor.ErrorActionMessageSend(b.Action, err.Error(), b.CallbackData)
		} else {
			BosonProvider.Processor.FailedActionMessageSend(vpc.VPCID, vpc.ResourceType, b.Action, err.Error(), b.CallbackData)
		}
		return
	}

	BosonProvider.Processor.SuccessActionMessageSend(vpc.VPCID, vpc.ResourceType, b.Action, b.CallbackData)
	BosonProvider.Processor.StateMessageSend(vpc.ID, network.VPC_DELETED.String())
}

func removeVpc(zone, vpcName string) (*db.Vpcs, error) {

	dbRTMetrics := exporter.InitDBRTMetrics("GetVpc")

	vpc, err := dao.GetVpcByName(BosonProvider.Engine, vpcName)
	dbRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("Get vpc %s error: %s", vpcName, err)
		return nil, err
	}

	dbRTMetrics = exporter.InitDBRTMetrics("ListSubnets")

	sns, err := dao.ListSubnets(BosonProvider.Engine, vpc.VPCID)
	dbRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("List vpc %s(%s) subnets error: %s", vpcName, vpc.VPCID, err)
		return vpc, err
	}

	nss, err := dao.ListNamespaces(BosonProvider.Engine, vpc.VPCID)
	if err != nil {
		logrus.Errorf("List vpc %s(%s) namespaces error: %s", vpcName, vpc.VPCID, err)
		return vpc, err
	}

	// Check pods in the namespaces
	for _, ns := range nss {
		k8sRTMetrics := exporter.InitK8sRTMetrics("ListPods")
		pods, err := BosonProvider.K8sClient.CoreV1().Pods(ns.Name).List(context.Background(), metav1.ListOptions{})
		k8sRTMetrics.ObserveDurationAndLog()
		if err != nil {
			logrus.Errorln(err)
			return vpc, err
		}
		if len(pods.Items) > 0 {
			err := fmt.Errorf("Also exist pods in VPC %v", vpcName)
			logrus.Errorln(err)
			return vpc, err
		}
	}

	// check eip
	{
		has, err := dao.CheckVpcExists(BosonProvider.Engine, vpc.VPCID)
		if err != nil {
			logrus.Errorln(err)
			return vpc, err
		}

		if has {
			err := fmt.Errorf("Also exist eip resource in VPC %v", vpcName)
			logrus.Errorln(err)
			return vpc, err
		}
	}

	// check ipa
	for _, sn := range sns {
		ipas, err := dao.ListIPAs(BosonProvider.Engine, sn.SubnetID)
		if err != nil {
			logrus.Errorln(err)
			return vpc, err
		}

		if len(ipas) > 0 {
			err := fmt.Errorf("Also exist ip addresses resource from subnet %v in VPC %v", sn.Name, vpcName)
			logrus.Errorln(err)
			return vpc, err
		}
	}

	// check dcgw
	{
		has, err := dao.CheckDcgwExistsByVpcId(BosonProvider.Engine, vpc.VPCID)
		if err != nil {
			logrus.Errorln(err)
			return vpc, err
		}

		if has {
			err := fmt.Errorf("Also exist dcgw resource in VPC %v", vpcName)
			logrus.Errorln(err)
			return vpc, err
		}
	}

	// delete CR (namespaces, subnets, vpcNatGateway, VPC)
	for _, ns := range nss {
		k8sRTMetrics := exporter.InitK8sRTMetrics("DeleteNamespace")
		if err := BosonProvider.K8sClient.CoreV1().Namespaces().Delete(context.Background(), ns.Name, metav1.DeleteOptions{}); err != nil && !k8s_errors.IsNotFound(err) {
			logrus.Errorf("Delete CR namespace %s error: %s", ns.Name, err)
			k8sRTMetrics.ObserveDurationAndLog()
			return vpc, err
		}
		k8sRTMetrics.ObserveDurationAndLog()
	}

	// delete localport for dgw
	var ipName string
	k8sRTMetrics := exporter.InitK8sRTMetrics("GetVPC")
	vpcCr, err := BosonProvider.KubeovnClient.Vpcs().Get(context.Background(), vpcName, metav1.GetOptions{})
	k8sRTMetrics.ObserveDurationAndLog()
	if err != nil && !k8s_errors.IsNotFound(err) {
		logrus.Errorln(err)
		return vpc, err
	}

	ipName = fmt.Sprintf("%s.%s", vpcCr.Annotations[AnnoDgwVirtualPodName], "kube-system")

	k8sRTMetrics = exporter.InitK8sRTMetrics("DeleteLP")
	if err := BosonProvider.KubeovnClient.IPs().Delete(context.Background(), ipName, metav1.DeleteOptions{}); err != nil && !k8s_errors.IsNotFound(err) {
		logrus.Errorf("Delete dgw lp %s error: %s", ipName, err)
		k8sRTMetrics.ObserveDurationAndLog()
		return vpc, err
	}
	k8sRTMetrics.ObserveDurationAndLog()

	dbRTMetrics = exporter.InitDBRTMetrics("CleanVPCSession")

	s := BosonProvider.Engine.NewSession()
	defer s.Close()

	for _, sn := range sns {
		if sn.Provider == network.SubnetProperties_CONTROLLER.String() {
			k8sRTMetrics := exporter.InitK8sRTMetrics("DeleteBosonSubnet")
			if err := BosonProvider.BosonNetClient.Subnets().Delete(context.Background(), sn.Name, metav1.DeleteOptions{}); err != nil && !k8s_errors.IsNotFound(err) {
				logrus.Errorf("Delete controller CR subnet %s error: %s", sn.Name, err)
				k8sRTMetrics.ObserveDurationAndLog()
				return vpc, err
			}
			k8sRTMetrics.ObserveDurationAndLog()
		}

		if sn.Provider == network.SubnetProperties_OVN.String() {
			k8sRTMetrics := exporter.InitK8sRTMetrics("DeleteOVNSubnet")
			if err := BosonProvider.KubeovnClient.Subnets().Delete(context.Background(), sn.Name, metav1.DeleteOptions{}); err != nil && !k8s_errors.IsNotFound(err) {
				logrus.Errorf("Delete CR subnet %s error: %s", sn.Name, err)
				k8sRTMetrics.ObserveDurationAndLog()
				return vpc, err
			}
			k8sRTMetrics.ObserveDurationAndLog()
		}

		// Release CIDR pool
		if len(sn.CIDRPoolID) > 0 {
			if err := dao.ReleaseCidrPool(s, sn.CIDRPoolID); err != nil {
				logrus.Errorf("Release cidr pool %s error: %s", sn.CIDRPoolID, err)
				return vpc, err
			}
		}
	}

	haNgws, err := dao.ListHaNatGateways(BosonProvider.Engine, vpc.VPCID)
	if err != nil {
		logrus.Errorf("List vpc %s(%s) ha nat gateways error: %s", vpcName, vpc.VPCID, err)
		return vpc, err
	}

	for _, haNgw := range haNgws {
		if err := BosonProvider.BosonNetClient.HaNatGws().Delete(context.Background(), haNgw.Name, metav1.DeleteOptions{}); err != nil && !k8s_errors.IsNotFound(err) {
			logrus.Errorf("Delete CR HaNatGateway %s error: %s", haNgw.Name, err)
			return vpc, err
		}
		// release all external ip for this ha nat gw
		if err := dao.ReleaseNatGatewayExternalIPByHaGWID(BosonProvider.Engine, haNgw.HaNatGatewayId); err != nil {
			logrus.Errorf("Release nat gateway external IP for ha nat gw id: %s error: %s", haNgw.HaNatGatewayId, err)
			return vpc, err
		}
	}

	if err := BosonProvider.KubeovnClient.Vpcs().Delete(context.Background(), vpcName, metav1.DeleteOptions{}); err != nil && !k8s_errors.IsNotFound(err) {
		logrus.Errorf("Delete CR Vpc %s error: %s", vpcName, err)
		return vpc, err
	}

	if err := dao.DeleteNamespaces(s, vpc.VPCID); err != nil {
		logrus.Errorf("Delete namespaces for vpc %s error: %s", vpcName, err)
		return vpc, err
	}
	if err := dao.DeleteSubnets(s, vpc.VPCID); err != nil {
		logrus.Errorf("Delete subnets for vpc %s error: %s", vpcName, err)
		return vpc, err
	}
	if err := dao.DeleteHaNatGateways(s, vpc.VPCID); err != nil {
		logrus.Errorf("Delete ha nat gateways for vpc %s error: %s", vpcName, err)
		return vpc, err
	}
	if err := dao.DeleteVpc(s, vpc.VPCID); err != nil {
		logrus.Errorf("Delete vpc %s error: %s", vpcName, err)
		return vpc, err
	}
	if err := dao.DeleteVPCQuota(s, vpc.VPCID); err != nil {
		logrus.Errorf("delete vpc %s quota error: %s", vpcName, err)
		return vpc, err
	}

	if err := s.Commit(); err != nil {
		logrus.Errorf("Delete vpc %s error: %s", vpcName, err)
		dbRTMetrics.ObserveDurationAndLog()
		return vpc, err
	}

	dbRTMetrics.ObserveDurationAndLog()

	logrus.Infof("Deleted vpc: %s", vpcName)
	return vpc, nil
}

// addBmsServiceDataSubnets add BMS service/data
func addBmsServiceDataSubnets(request *network.CreateVPCRequest, vpc *ovn.Vpc, tenantCode string) error {
	// BMS service network, Vlan
	if err := addOneSubnet(request, vpc, tenantCode, network.SubnetProperties_SERVICE.String(),
		network.SubnetProperties_CONTROLLER.String(), network.SubnetProperties_VLAN.String()); err != nil {
		logrus.Errorf("Add subnet error: %s", err)
		return err
	}

	// BMS data network, Vlan
	if err := addOneSubnet(request, vpc, tenantCode, network.SubnetProperties_DATA.String(),
		network.SubnetProperties_CONTROLLER.String(), network.SubnetProperties_VLAN.String()); err != nil {
		logrus.Errorf("Add subnet error: %s", err)
		return err
	}

	return nil
}

// addBmsTrainingSubnets add BMS training subnets
func addBmsTrainingSubnets(request *network.CreateVPCRequest, vpc *ovn.Vpc, tenantCode string) error {
	// BMS training network, IB
	if err := addOneSubnet(request, vpc, tenantCode, network.SubnetProperties_TRAINING.String(),
		network.SubnetProperties_CONTROLLER.String(), network.SubnetProperties_IB.String()); err != nil {
		logrus.Errorf("failed to add bms IB training subnet error: %s", err)
		return err
	}

	// BMS training network, RoCE Vlan, pre-create subnets
	if vlanAclTrainingSubnetEnable() {
		if err := createVlanAclTrainingSubnets(request, vpc, tenantCode, network.SubnetProperties_CONTROLLER.String()); err != nil {
			logrus.Errorf("failed to add bms vlan-acl RoCE training subnet error: %s", err)
			return err
		}
	} else if bmsTrainingSubnetEnable() {
		if err := createBMSTrainingSubnet(request, vpc, tenantCode); err != nil {
			logrus.Errorf("failed to add bms RoCE training subnet error: %s", err)
			return err
		}
	} else {
		logrus.Infof("no bms RoCE training Subnets config, just create IB training subnet")
	}

	return nil
}

// addBmsSubnets add BMS service/data/training subnets
func addBmsSubnets(request *network.CreateVPCRequest, vpc *ovn.Vpc, tenantCode string) error {
	logrus.Infof("add bmsSubnets in vpc %s\n", vpc.Name)

	// BMS service network, Vlan
	if err := addOneSubnet(request, vpc, tenantCode, network.SubnetProperties_SERVICE.String(),
		network.SubnetProperties_CONTROLLER.String(), network.SubnetProperties_VLAN.String()); err != nil {
		logrus.Errorf("Add subnet error: %s", err)
		return err
	}

	// BMS data network, Vlan
	if err := addOneSubnet(request, vpc, tenantCode, network.SubnetProperties_DATA.String(),
		network.SubnetProperties_CONTROLLER.String(), network.SubnetProperties_VLAN.String()); err != nil {
		logrus.Errorf("Add subnet error: %s", err)
		return err
	}

	// BMS training network
	if err := addBmsTrainingSubnets(request, vpc, tenantCode); err != nil {
		logrus.Errorf("Add subnet error: %s", err)
		return err
	}

	return nil
}

// addOneSubnet add a subnet
func addOneSubnet(request *network.CreateVPCRequest, vpc *ovn.Vpc, tenantCode, scope, provider, networkType string) error {

	dbRTMetrics := exporter.InitDBRTMetrics("UpdateCidrPools")

	pool, err := dao.AllocateCidrPool(BosonProvider.Engine, scope, networkType)
	dbRTMetrics.ObserveDurationAndLog()
	if err != nil {
		logrus.Errorf("Add subnet failed, allocate cidr pool error: %s", err)
		return err
	}
	logrus.Infof("Allocate CIDR: %v", pool)

	return createBMSOneSubnet(pool, request, vpc, tenantCode, scope, provider, networkType)
}

type OvnTrainingCidrInfo struct {
	If_id string
	Cidr  db.CidrPools
}

// pkey allocate, no multi-proesses
const (
	pkey_min = 12500
	pkey_max = 14900
)

func allocateIbPkey() (int, error) {
	key := pkey_min
	if sn, err := dao.GetOvnMaxVniIBSubnet(BosonProvider.Engine); err == nil {
		if key < sn.VNI {
			key = sn.VNI
		}
	}

	lenth := pkey_max - pkey_min
	for i := 1; i <= lenth; i++ {
		ok, err := dao.CheckOvnIBSubnet(BosonProvider.Engine, (key-pkey_min+i)%lenth+pkey_min)
		if err != nil {
			logrus.Errorln(err)
			return 0, err
		}
		if !ok {
			return (key-pkey_min+i)%lenth + pkey_min, nil
		}
	}
	return 0, errors.New("no free IB pkey ")
}

// overlay subnet, no cidr pool, no ns means no pod to use it
func createIBSubnet(
	request *network.CreateVPCRequest, vpc *ovn.Vpc,
) (*db.Subnets, error) {
	// 1 get free vni
	vni, err := allocateIbPkey()
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}

	// 2 generate
	rand8 := utils.Rand8()
	snName := fmt.Sprintf("sn-%s-%s", utils.NameToTenantCode(vpc.Name), rand8)
	subnetGwIP, err := utils.GetTheFirstAddrFromCIDR(BosonProvider.RpConfig.Boson.GeneveSubnetCIDR)
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}

	subnetData := db.Subnets{
		SubnetID:     utils.UUIDGen(),
		VPCID:        request.Vpc.Uid,
		DisplayName:  snName,
		CIDR:         BosonProvider.RpConfig.Boson.GeneveSubnetCIDR,
		Description:  "ib training subnet for ovn",
		IsDefault:    true,
		GatewayIP:    subnetGwIP,
		Scope:        network.SubnetProperties_TRAINING.String(),
		Provider:     network.SubnetProperties_OVN.String(),
		NetworkType:  network.SubnetProperties_IB.String(),
		VNI:          vni,
		ID:           utils.GenRMID(request.SubscriptionName, request.ResourceGroupName, request.Zone, "subnets", snName),
		Name:         snName,
		ResourceType: "network.vpc.v1.subnet",
		Zone:         BosonProvider.RpConfig.Boson.VPCDefaultAZ,
		State:        network.VPC_State_name[int32(network.VPC_ACTIVE)],
		CreatorID:    request.Vpc.CreatorId,
		OwnerID:      request.Vpc.OwnerId,
		TenantID:     request.Vpc.TenantId,
		CreateTime:   request.Vpc.CreateTime.AsTime(),
		UpdateTime:   request.Vpc.UpdateTime.AsTime(),
		Deleted:      false,
	}

	err = dao.AddSubnet(BosonProvider.Engine, &subnetData)
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}

	return &subnetData, nil
}

func vlanAclTrainingSubnetEnable() bool {
	// 0 means: vlan maping vxlan
	return BosonProvider.RpConfig.Boson.TrainingNetworks.Vlan_acl_nic_max_count > 0
}

func ovnTrainingSubnetEnable() bool {
	// 0 means: no ovn training config
	return len(BosonProvider.RpConfig.Boson.TrainingNetworks.KubeOvn) > 0
}

func bmsTrainingSubnetEnable() bool {
	// 0 means: no bms training config
	return len(BosonProvider.RpConfig.Boson.TrainingNetworks.Bms.Gws) > 0
}

func createVlanAclTrainingSubnets(
	request *network.CreateVPCRequest, vpc *ovn.Vpc, tenantCode, provider string,
) (err error) {
	// check
	trainingNetworkCnt := BosonProvider.RpConfig.Boson.TrainingNetworks.Vlan_acl_nic_max_count
	if trainingNetworkCnt == 0 {
		err := errors.New("boson training network config error")
		logrus.Errorln(err)
		return err
	}

	session := BosonProvider.Engine.NewSession()
	defer session.Close()

	// 1. get all cidrs with free vni and order by  cidr
	var cidrPools []db.CidrPools
	dbRTMetrics := exporter.InitDBRTMetrics("GetCidrBySQL")
	if err := session.Where("scope = ? AND network_type = ? AND allocated = false AND zone_prp = ? AND vni NOT IN (SELECT vni FROM cidr_pools WHERE scope = ? AND network_type = ? AND allocated = true AND zone_prp = ?)", "TRAINING", "VLAN", BosonProvider.RpConfig.Boson.VPCDefaultAZ, "TRAINING", "VLAN", BosonProvider.RpConfig.Boson.VPCDefaultAZ).
		OrderBy("vni").OrderBy("inet(cidr)").Limit(trainingNetworkCnt).Find(&cidrPools); err != nil {
		dbRTMetrics.ObserveDurationAndLog()
		if err1 := session.Rollback(); err1 != nil {
			logrus.Errorf("Failed to rollback transaction: %v", err1)
		}
		logrus.Errorf("failed to create  training error :%s", err.Error())
		return err
	}
	dbRTMetrics.ObserveDurationAndLog()

	if len(cidrPools) < trainingNetworkCnt {
		if err := session.Rollback(); err != nil {
			logrus.Errorf("Failed to rollback transaction: %v", err)
		}
		err := fmt.Errorf("failed to allocate  training subnets fail, allocated count %d, request count%d", len(cidrPools), trainingNetworkCnt)
		logrus.Errorln(err)
		return err
	}

	logrus.Infoln("vpc allocate cidrs", "cidrs", cidrPools, "vpc", vpc, "count", trainingNetworkCnt)

	// .3 create resource
	for index, cidrPoolData := range cidrPools {
		logrus.Infoln("vpc allocate cidrs", "cidr pool", cidrPoolData, "vpc", vpc, "index", index)
		if provider == network.SubnetProperties_CONTROLLER.String() {
			// 3.1 update db allocated
			if err := dao.SetCidrAllocatedByID(session, cidrPoolData.CIDRPoolID, true); err != nil {
				if err1 := session.Rollback(); err1 != nil {
					logrus.Errorf("Failed to rollback transaction: %v", err1)
				}
				logrus.Errorf("failed to update cidr %s allocated error :%s", cidrPoolData.CIDRPoolID, err.Error())
				return err
			}
			// 3.2 create bms subnet
			err = createBMSOneSubnet(&cidrPoolData, request, vpc, tenantCode,
				network.SubnetProperties_TRAINING.String(),
				network.SubnetProperties_CONTROLLER.String(),
				network.SubnetProperties_VLAN.String())
		}

		if provider == network.SubnetProperties_OVN.String() {
			ifId := fmt.Sprintf("roce_%d", index)
			err = createOneOvnTraningSubnet(session, &cidrPoolData, ifId, vpc, request)
		}

		if err != nil {
			if err1 := session.Rollback(); err1 != nil {
				logrus.Errorf("Failer to rollback transaction: %v", err1)
			}
			logrus.Errorf("failed to create  training error :%s", err.Error())
			return err
		}
	}

	// 提交事务
	if err := session.Commit(); err != nil {
		if err1 := session.Rollback(); err1 != nil {
			logrus.Errorf("Failed to rollback transaction: %v", err1)
		}
		logrus.Errorln(err)
		return err
	}

	logrus.Infof("traning subnet %d success in vpc: %s", len(cidrPools), vpc.Name)

	return nil
}

func createOneOvnTraningSubnet(
	session *db.SessionWrapper, cidrPoolData *db.CidrPools, ifId string, vpc *ovn.Vpc, request *network.CreateVPCRequest,
) error {
	logrus.Infoln("createOneOvnTraningSubnet", "vni", cidrPoolData.VNI, "if_id", ifId, "vpc", vpc)

	// 1 update db allocated
	if err := dao.SetCidrAllocatedByID(session, cidrPoolData.CIDRPoolID, true); err != nil {
		logrus.Errorf("failed to update cidr %s allocated error :%s", cidrPoolData.CIDRPoolID, err.Error())
		return err
	}

	// 2 generate
	rand8 := utils.Rand8()
	sntName := fmt.Sprintf("snt-%s-%s", utils.NameToTenantCode(vpc.Name), rand8)
	nadName := fmt.Sprintf("nad-%s-%s", utils.NameToTenantCode(vpc.Name), rand8)
	sntID := utils.UUIDGen()

	nad := generateNadCR(nadName, ifId, vpc)
	snt := generateSubnet(sntID, sntName, nadName, ifId, vpc, cidrPoolData, request)
	sntData := generateSubnetData(sntID, sntName, vpc, cidrPoolData, request, snt.Name)

	if _, err := session.InsertOne(sntData); err != nil {
		logrus.Errorf("failed to create training subnet in vpc %s error :%s", vpc.Name, err.Error())
		return err
	}

	// 3 create
	k8sRTMetrics := exporter.InitK8sRTMetrics("CreateNADForTrainingSubnet")
	if _, err := BosonProvider.NADClient.NetworkAttachmentDefinitions(NADNamespace).Create(
		context.Background(), nad, metav1.CreateOptions{}); err != nil {
		k8sRTMetrics.ObserveDurationAndLog()
		logrus.Errorf("failed to create training subnet nad %v in vpc %s error :%s", nad, vpc.Name, err.Error())
		return err
	}
	k8sRTMetrics.ObserveDurationAndLog()

	k8sRTMetrics = exporter.InitK8sRTMetrics("CreateTrainingSubnet")
	if _, err := BosonProvider.KubeovnClient.Subnets().Create(context.Background(), snt, metav1.CreateOptions{}); err != nil {
		k8sRTMetrics.ObserveDurationAndLog()
		logrus.Errorf("failed to create training subnet cr %v in vpc %s error :%s", snt, vpc.Name, err.Error())
		return err
	}
	k8sRTMetrics.ObserveDurationAndLog()

	logrus.Infof("traning subnet %v success in vpc: %s", sntData, vpc.Name)
	return nil
}

func createTrainingSubnets(
	request *network.CreateVPCRequest, vpc *ovn.Vpc) error {
	// check
	trainingNetworkCnt := len(BosonProvider.RpConfig.Boson.TrainingNetworks.KubeOvn)
	if trainingNetworkCnt == 0 {
		err := errors.New("boson training network config error")
		logrus.Errorln(err)
		return err
	}

	// .1 vni
	vni, err := AllocateTrainingVni()
	if err != nil {
		logrus.Errorf("failed to allocate training subnet vni in vpc %s error: %s", vpc.Name, err.Error())
		return err
	}

	// .2 get all with vni with check
	cidrPoolDatas, err := GetAllTrainingCidrDatasWithVni(trainingNetworkCnt, vni)
	if err != nil {
		logrus.Errorf("failed to allocate training cidr pool %d with vni %d in vpc %s error: %s", trainingNetworkCnt, vni, vpc.Name, err.Error())
		return err
	}

	logrus.Infoln("vpc allocate cidrs", "vni", vni, "vpc", vpc, "count", trainingNetworkCnt)

	session := BosonProvider.Engine.NewSession()
	defer session.Close()

	// .3 create resource
	for _, cidrPoolData := range cidrPoolDatas {
		if err := createOneOvnTraningSubnet(session, &cidrPoolData.Cidr, cidrPoolData.If_id, vpc, request); err != nil {
			if err1 := session.Rollback(); err1 != nil {
				logrus.Errorf("Failed to rollback transaction: %v", err1)
			}
			logrus.Errorf("failed to create one ovn training subnet in vpc %s error: %s", vpc.Name, err.Error())
			return err
		}
	}

	// 提交事务
	if err := session.Commit(); err != nil {
		if err1 := session.Rollback(); err1 != nil {
			logrus.Errorf("Failed to rollback transaction: %v", err1)
		}
		logrus.Errorln(err)
		return err
	}

	logrus.Infoln("vpc create training subnet success", "vni", vni, "vpc", vpc, "count", trainingNetworkCnt)
	return nil
}

func addTraniningSubnets(request *network.CreateVPCRequest, vpc *ovn.Vpc, tenantCode string) error {
	// Create ovn IB Training subnets
	if _, err := createIBSubnet(request, vpc); err != nil {
		logrus.Errorln(err)
		return err
	}

	// Create ovn RoCE Training subnets
	if vlanAclTrainingSubnetEnable() {
		if err := createVlanAclTrainingSubnets(request, vpc, tenantCode, network.SubnetProperties_OVN.String()); err != nil {
			logrus.Errorf("failed to add ovn vlan-acl RoCE training subnet error: %s", err)
			return err
		}
	} else if ovnTrainingSubnetEnable() {
		if err := createTrainingSubnets(request, vpc); err != nil {
			logrus.Errorf("failed to add ovn RoCE training subnet error: %s", err)
			return err
		}
	} else {
		logrus.Infof("no ovn RoCE training Subnets config, just create IB training subnet")
	}

	return nil
}

func generateNadCR(nadName string, if_id string, vpc *ovn.Vpc) *nadv1.NetworkAttachmentDefinition {
	return &nadv1.NetworkAttachmentDefinition{
		TypeMeta: metav1.TypeMeta{
			Kind:       "NetworkAttachmentDefinition",
			APIVersion: nadv1.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name:      nadName,
			Namespace: NADNamespace,
			Annotations: map[string]string{
				"k8s.v1.cni.cncf.io/resourceName": ResourceName,
			},
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    VPCKind,
				}),
			},
		},
		Spec: nadv1.NetworkAttachmentDefinitionSpec{
			Config: strings.ReplaceAll(strings.ReplaceAll(NADConfig, "<nad_name>", nadName), "<roce_id>", if_id),
		},
	}
}

func generateSubnet(
	sntID string, sntName string,
	nadName string, if_id string, vpc *ovn.Vpc, cidrData *db.CidrPools, request *network.CreateVPCRequest,
) *ovn.Subnet {
	return &ovn.Subnet{
		TypeMeta: metav1.TypeMeta{
			Kind:       SubnetKind,
			APIVersion: ovn.SchemeGroupVersion.String(),
		},
		ObjectMeta: metav1.ObjectMeta{
			Name: sntName,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    VPCKind,
				}),
			},
			Labels: map[string]string{
				"creator":                     "boson-provider",
				"kubernetes.io/metadata.name": sntName,
				"subnet_id":                   sntID,
				"subnet_scope":                cidrData.Scope,
				"subnet_type":                 cidrData.NetworkType,
				"tenant_id":                   request.Vpc.TenantId,
				"vni":                         fmt.Sprintf("%v", cidrData.VNI),
				"vpc_id":                      request.Vpc.Uid,
				"vpc.Name":                    vpc.Name,
			},
			Annotations: map[string]string{
				"k8s.v1.cni.cncf.io/networks":     fmt.Sprintf("%s/%s", NADNamespace, nadName),
				"k8s.v1.cni.cncf.io/resourceName": ResourceName,
				"roce_id":                         GetAnnosRoceID(if_id),
			},
		},
		Spec: ovn.SubnetSpec{
			Vpc:         vpc.Name,
			CIDRBlock:   cidrData.CIDR,
			Gateway:     cidrData.GatewayIP,
			Protocol:    ovn.ProtocolIPv4,
			NatOutgoing: false,
			Default:     false,
			ExcludeIps:  strings.Split(cidrData.Reserved, ","),
			Provider:    fmt.Sprintf("%s.%s", nadName, NADNamespace),
		},
	}
}

func generateSubnetData(
	sntID string, sntName string,
	vpc *ovn.Vpc, cidrData *db.CidrPools, request *network.CreateVPCRequest, vpcSubnetName string,
) *db.Subnets {
	return &db.Subnets{
		SubnetID:     sntID,
		VPCID:        request.Vpc.Uid,
		DisplayName:  sntName,
		CIDR:         cidrData.CIDR,
		IsDefault:    false,
		GatewayIP:    cidrData.GatewayIP,
		Scope:        cidrData.Scope,
		Provider:     "OVN",
		NetworkType:  cidrData.NetworkType,
		ID:           utils.GenRMID(request.SubscriptionName, request.ResourceGroupName, request.Zone, "subnets", vpcSubnetName),
		Name:         sntName,
		ResourceType: "network.vpc.v1.subnet",
		Zone:         request.Vpc.Zone,
		State:        network.Subnet_State_name[int32(network.Subnet_ACTIVE)],
		CreatorID:    request.Vpc.CreatorId,
		OwnerID:      request.Vpc.OwnerId,
		TenantID:     request.Vpc.TenantId,
		CreateTime:   request.Vpc.CreateTime.AsTime(),
		UpdateTime:   request.Vpc.UpdateTime.AsTime(),
		Deleted:      false,
		CIDRPoolID:   cidrData.CIDRPoolID,
		VNI:          cidrData.VNI,
	}
}

func formatTrainingNetworkGwSql() []string {
	gws := []string{}
	for _, info := range BosonProvider.RpConfig.Boson.TrainingNetworks.KubeOvn {
		gws = append(gws, info.Gw)
	}

	return gws
}

func GenerateBmsTrainingCidrInfo(cidrPoolsDB []*db.CidrPools) map[string]db.CidrPools {
	info := make(map[string]db.CidrPools)
	for _, pool := range cidrPoolsDB {
		info[pool.GatewayIP] = *pool
	}
	return info
}

func createBMSTrainingSubnet(request *network.CreateVPCRequest, vpc *ovn.Vpc, tenantCode string) error {
	trainingVni, err := AllocateBmsTrainingVni()
	if err != nil {
		logrus.Errorf("failed to allocate bms traning subnet vni error: %s", err)
		return err
	}

	cidrPools, err := dao.FetchCIDRPoolsWithGws(
		BosonProvider.Engine,
		network.SubnetProperties_TRAINING.String(),
		network.SubnetProperties_VLAN.String(),
		BosonProvider.RpConfig.Boson.TrainingNetworks.Bms.Gws,
		trainingVni)
	if err != nil {
		logrus.Errorf("fetch bms training error: %s", err.Error())
		return err
	}
	if len(cidrPools) < len(BosonProvider.RpConfig.Boson.TrainingNetworks.Bms.Gws) {
		err := fmt.Errorf("fetch bms training fail, need:%d, get:%d",
			len(BosonProvider.RpConfig.Boson.TrainingNetworks.Bms.Gws), len(cidrPools))
		return err
	}
	logrus.Infof("fetch bms training CIDR: %v", cidrPools)

	info := GenerateBmsTrainingCidrInfo(cidrPools)
	for _, cidr := range BosonProvider.RpConfig.Boson.TrainingNetworks.Bms.Gws {
		pool, ok := info[cidr]
		if !ok {
			err = fmt.Errorf("create bms training allocated pool fail, cidr:%s info:%v", cidr, info)
			return err
		}

		err := dao.SetCidrAllocatedByPoolID(BosonProvider.Engine, pool.CIDRPoolID, true)
		if err != nil {
			logrus.Errorf("create bms training allocated pool error :%s", err.Error())
			return err
		}

		err = createBMSOneSubnet(
			&pool, request, vpc, tenantCode,
			network.SubnetProperties_TRAINING.String(),
			network.SubnetProperties_CONTROLLER.String(),
			network.SubnetProperties_VLAN.String())
		if err != nil {
			logrus.Errorf("create bms training error :%s", err.Error())
			return err
		}
	}

	return nil
}

func createBMSOneSubnet(
	pool *db.CidrPools, request *network.CreateVPCRequest,
	vpc *ovn.Vpc, tenantCode, scope, provider, networkType string,
) error {
	//miaozhanyong to do for prp, request.ResourceGroupName
	subnetID := utils.UUIDGen()
	subnetName := "sn-" + utils.Substring(tenantCode, 0, 20) + "-" + subnetID[:8]
	sn := db.Subnets{
		SubnetID:     subnetID,
		VPCID:        request.Vpc.Uid,
		DisplayName:  subnetName,
		CIDR:         pool.CIDR,
		Description:  request.Vpc.Description,
		IsDefault:    true,
		GatewayIP:    pool.GatewayIP,
		Scope:        scope,
		Provider:     provider,
		NetworkType:  networkType,
		ReservedIPs:  pool.Reserved,
		VNI:          pool.VNI,
		CIDRPoolID:   pool.CIDRPoolID,
		ID:           utils.GenRMID(request.SubscriptionName, request.ResourceGroupName, request.Zone, "subnets", subnetName),
		Name:         subnetName,
		ResourceType: "network.vpc.v1.subnet",
		Zone:         BosonProvider.RpConfig.Boson.VPCDefaultAZ,
		State:        network.Subnet_State_name[int32(network.Subnet_ACTIVE)],
		CreatorID:    request.Vpc.CreatorId,
		OwnerID:      request.Vpc.OwnerId,
		TenantID:     request.Vpc.TenantId,
		CreateTime:   request.Vpc.CreateTime.AsTime(),
		UpdateTime:   request.Vpc.UpdateTime.AsTime(),
		Deleted:      false,
	}

	if err := dao.AddSubnet(BosonProvider.Engine, &sn); err != nil {
		logrus.Errorf("Add subnet %v error: %s", sn, err)
		return err
	}

	// TODO: Create subnet CR
	subnetCR := &v1.Subnet{
		ObjectMeta: metav1.ObjectMeta{
			Name: sn.Name,
			OwnerReferences: []metav1.OwnerReference{
				*metav1.NewControllerRef(vpc, schema.GroupVersionKind{
					Group:   ovn.SchemeGroupVersion.Group,
					Version: ovn.SchemeGroupVersion.Version,
					Kind:    VPCKind,
				}),
			},
		},
		Spec: v1.SubnetSpec{
			CIDR:        sn.CIDR,
			GwIP:        sn.GatewayIP,
			VPC:         vpc.Name,
			Scope:       sn.Scope,
			Provider:    sn.Provider,
			NetworkType: sn.NetworkType,
			VNI:         sn.VNI,
			IsDefault:   sn.IsDefault,
			PoolID:      sn.CIDRPoolID,
		},
	}

	if scope == network.SubnetProperties_TRAINING.String() && networkType == network.SubnetProperties_VLAN.String() {
		subnetCR.ObjectMeta.Annotations = map[string]string{
			"k8s.v1.cni.cncf.io/resourceName": "rdma-training/roce", // tag anno for multi-roce NIC subnets
		}
	}

	k8sRTMetrics := exporter.InitK8sRTMetrics("CreateBosonSubnet")

	if _, err := BosonProvider.BosonNetClient.Subnets().Create(context.Background(), subnetCR, metav1.CreateOptions{}); err != nil {
		logrus.Errorf("Create CR subnet %v error: %s", subnetCR, err)
		k8sRTMetrics.ObserveDurationAndLog()
		return err
	}

	k8sRTMetrics.ObserveDurationAndLog()

	/* don't create ns for bms subnet
	// Add namespace for subnet
	nsName := "ns-" +  utils.Substring(tenantCode, 0, 20)  + "-" + subnetID[:8]
	ns := db.K8sNamespaces{
		NamespaceID: subnetID,
		VPCID:       request.Vpc.Uid,
		SubnetID:    subnetID,
		Name:        nsName,
	}

	if err := dao.AddNamespace(BosonProvider.Engine, &ns); err != nil {
		logrus.Errorf("Add namespace %v error: %s", ns, err)
		return err
	}

	err = createNS(nsName, vpc, request.Zone, request.Vpc.TenantId, request.Vpc.Uid, request.Vpc.Name, subnetID, subnetName)
	if err != nil {
		logrus.Errorf("Create namespace %s error: %s", ns, err)
		return err
	}
	*/
	return nil
}

// if_id="roce_X", return "X"
func GetAnnosRoceID(if_id string) string {
	return strings.TrimPrefix(if_id, "roce_")
}

func AllocateTrainingVni() (int, error) {
	trainingNetwork := BosonProvider.RpConfig.Boson.TrainingNetworks.KubeOvn[0]
	cidrData, err := dao.FetchCidrPool(BosonProvider.Engine, "TRAINING", "VLAN", trainingNetwork.Gw, 0)
	if err != nil {
		logrus.Errorln(err)
		return 0, err
	}

	return cidrData.VNI, nil
}

func AllocateBmsTrainingVni() (int, error) {
	trainingNetwork := BosonProvider.RpConfig.Boson.TrainingNetworks.Bms
	cidrData, err := dao.FetchCidrPool(BosonProvider.Engine, "TRAINING", "VLAN", trainingNetwork.Gws[0], 0)
	if err != nil {
		logrus.Errorln(err)
		return 0, err
	}

	return cidrData.VNI, nil
}

func GetAllTrainingCidrDatasWithVni(trainingNetworkCnt int, vni int) ([]OvnTrainingCidrInfo, error) {
	cidrPools, err := dao.FetchCIDRPoolsWithGws(
		BosonProvider.Engine, "TRAINING", "VLAN", formatTrainingNetworkGwSql(), vni)
	if err != nil {
		logrus.Errorln(err)
		return nil, err
	}
	if len(cidrPools) < trainingNetworkCnt {
		err = fmt.Errorf(
			"allocate all training network fail, resources are not enough, vni:%d, needCnd:%d, getCnt:%d, allocate:%v, gws:%s",
			vni, trainingNetworkCnt, len(cidrPools), cidrPools, strings.Join(formatTrainingNetworkGwSql(), ","))
		logrus.Errorln(err)
		return nil, err
	}

	// 确保 gw 是都有 cidr 可以使用
	cidrPoolsMapTmp := map[string]db.CidrPools{}
	for _, cidr := range cidrPools {
		cidrPoolsMapTmp[cidr.GatewayIP] = *cidr
	}
	cidrPoolDatas := []OvnTrainingCidrInfo{}
	for _, info := range BosonProvider.RpConfig.Boson.TrainingNetworks.KubeOvn {
		cidr, ok := cidrPoolsMapTmp[info.Gw]
		if !ok {
			err := fmt.Errorf("no allocatable training roce cidr found, gw:%s vni:%d", info.Gw, vni)
			logrus.Errorln(err)
			return nil, err
		}
		cidrPoolDatas = append(cidrPoolDatas, OvnTrainingCidrInfo{
			If_id: info.IfID,
			Cidr:  cidr,
		})
	}

	return cidrPoolDatas, nil
}

func AddBms2Pod(vpcData *db.Vpcs, vpc *ovn.Vpc) error {

	// Get BMS data network
	logrus.Infof("get bmsSubnet with scope %s in vpc_id %s\n", network.SubnetProperties_DATA.String(), vpcData.VPCID)
	bmsSubnet, err := dao.GetBmsSubnet(BosonProvider.Engine, network.SubnetProperties_DATA.String(), vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	// Get HaNatGateway data
	haNatGwData, has, err := dao.GetHaNatGatewayByVpcId(BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln(err)
		return err
	}
	if !has {
		logrus.Errorf("GetHaNatGatewayByVpcId %s not exists in db", vpcData.VPCID)
		return err
	}

	haNatGw, err := BosonProvider.BosonNetClient.HaNatGws().Get(context.Background(), haNatGwData.Name, metav1.GetOptions{})
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	// prepareBmsGwIps
	bmsGwVip, err := utils.CalcTheLastXIPFromCidr(bmsSubnet.CIDR, 1)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	var bmsLanIp []string
	bmsGwBackupIp, err := utils.CalcTheLastXIPFromCidr(bmsSubnet.CIDR, 2)
	if err != nil {
		logrus.Errorln(err)
		return err
	}
	bmsGwMasterIp, err := utils.CalcTheLastXIPFromCidr(bmsSubnet.CIDR, 3)
	if err != nil {
		logrus.Errorln(err)
		return err
	}
	bmsLanIp = append(bmsLanIp, bmsGwBackupIp, bmsGwMasterIp)

	// handle ha nat-gw
	bmsExtendInfo := &v1.ExtendInfo{
		Vip:     bmsGwVip,
		RealIps: bmsLanIp,
		Scope:   "b",
	}
	m, _ := utils.ToMapSetE(haNatGw.Spec.ExtendInfos)
	if _, ok := m[bmsExtendInfo]; !ok {
		haNatGw.Spec.ExtendInfos = append(haNatGw.Spec.ExtendInfos, bmsExtendInfo)
	}

	if haNatGw.ObjectMeta.Annotations == nil {
		haNatGw.ObjectMeta.Annotations = make(map[string]string)
	}

	haNatGw.ObjectMeta.Annotations[AnnoElasticNicConf], _ = MakeEniAnnotation(bmsGwVip, bmsSubnet, haNatGw)

	logrus.Infof("handle haNatGw: %s", haNatGwData.Name)
	_, err = BosonProvider.BosonNetClient.HaNatGws().Update(context.Background(), haNatGw, metav1.UpdateOptions{})
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	// handle db
	logrus.Infof("handle db ha_nat_gateways bms_gw_vip: %s where ha_nat_gateway_id = %s", bmsGwVip, haNatGwData.HaNatGatewayId)
	err = dao.SetHaNatGatewayBmsGwVip(BosonProvider.Engine, haNatGwData.HaNatGatewayId, bmsGwVip)
	if err != nil {
		logrus.Errorln(err)
		return err
	}

	// handle subnet acl
	logrus.Infof("get vpc ovn-service-subnet in vpc_id %s\n", vpcData.VPCID)
	snData, err := dao.GetVpcOvnServiceSubnet(BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		logrus.Errorln("get vpc ovn-service-subnet fail. err:", err.Error())
		return err
	}
	sn, err := BosonProvider.KubeovnClient.Subnets().Get(context.Background(), snData.Name, metav1.GetOptions{})
	if err != nil {
		logrus.Errorln(err)
		return err
	}
	bmsAcl := ovn.Acl{
		Direction: AclEgressDirection,
		Priority:  BMS_2_POD_ACL_PRIORITY,
		Match:     "ip4.dst==" + bmsSubnet.CIDR,
		Action:    AclAllowAction,
	}
	mAcl, _ := utils.ToMapSetE(sn.Spec.Acls)
	if _, ok := mAcl[bmsAcl]; !ok {
		sn.Spec.Acls = append(sn.Spec.Acls, bmsAcl)
	}
	logrus.Infof("handle vpc: %s subnet: %s bms acl: %v", vpcData.Name, snData.Name, bmsAcl)
	_, err = BosonProvider.KubeovnClient.Subnets().Update(context.Background(), sn, metav1.UpdateOptions{})
	if err != nil {
		logrus.Errorln(err)
		return err
	}
	return nil
}

func updateEipSpecSubnet(vpcData *db.Vpcs) error {
	// Get BMS SERVICE network
	logrus.Infof("get bmsSubnet with scope %s in vpc_id %s\n", network.SubnetProperties_SERVICE.String(), vpcData.VPCID)
	bmsSubnet, err := dao.GetBmsSubnet(BosonProvider.Engine, network.SubnetProperties_SERVICE.String(), vpcData.VPCID)
	if err != nil {
		return err
	}

	eips, err := dao.ListEips(BosonProvider.Engine, vpcData.VPCID)
	if err != nil {
		return err
	}

	for _, eipData := range eips {
		eipCr, err := BosonProvider.BosonNetClient.EIPs().Get(context.TODO(), eipData.Name, metav1.GetOptions{})
		if err != nil {
			return err
		}
		eipCr.Spec.Subnet = bmsSubnet.Name

		err = retry.RetryOnConflict(retry.DefaultBackoff, func() (err error) {
			_, err = BosonProvider.BosonNetClient.EIPs().Update(context.TODO(), eipCr, metav1.UpdateOptions{})
			if err != nil {
				return err
			}
			return nil
		})
		if err != nil {
			return err
		}
	}
	return nil
}
