package sender

import (
	"context"
	"fmt"
	"time"

	"github.com/apache/rocketmq-client-go/v2"
	"github.com/apache/rocketmq-client-go/v2/primitive"
	"github.com/apache/rocketmq-client-go/v2/producer"

	"github.com/sirupsen/logrus"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/config"
)

// Sender 消息生产者
type Sender struct {
	Producer rocketmq.Producer
	Topic    string
}

func newSenderDefault(rpconf *config.Config, topic, group string) (*Sender, error) {
	p, err := rocketmq.NewProducer(
		producer.WithNameServer(rpconf.MQ.Default.NameServer),
		producer.WithRetry(rpconf.MQ.Default.RetryInterval),
		producer.WithGroupName(group),
		producer.WithQueueSelector(producer.NewHashQueueSelector()),
		producer.WithInstanceName(rpconf.MQ.Default.InstanceName),
		producer.WithCredentials(primitive.Credentials{
			AccessKey: rpconf.MQ.Default.AccessKey,
			SecretKey: rpconf.MQ.Default.SecretKey,
		}),
	)

	s := &Sender{
		Producer: p,
		Topic:    topic,
	}

	return s, err
}

var (
	CloudAuditConfig = map[string]struct{ Enable bool }{}
)

func newSenderCloudAudit(rpconf *config.Config, topicInfo *config.RocketMQConfigCloudAuditTopicInfo) (*Sender, error) {
	p, err := rocketmq.NewProducer(
		producer.WithNameServer(rpconf.MQ.CloudAudit.NameServer),
		producer.WithRetry(rpconf.MQ.Default.RetryInterval),
		producer.WithGroupName(topicInfo.ProducerGroupName),
		producer.WithQueueSelector(producer.NewHashQueueSelector()),
		producer.WithInstanceName(fmt.Sprintf("%s#%d", rpconf.MQ.CloudAudit.InstanceName, time.Now().UnixNano())),
		producer.WithCredentials(primitive.Credentials{
			AccessKey: topicInfo.AccessKey,
			SecretKey: topicInfo.SecretKey,
		}),
	)

	s := &Sender{
		Producer: p,
		Topic:    topicInfo.Topic,
	}

	CloudAuditConfig[topicInfo.Topic] = struct{ Enable bool }{Enable: true}

	return s, err
}

// NewRMSender 初始化 rm topic 生产者
func NewRMSender(rpconf *config.Config) (*Sender, error) {
	return newSenderDefault(rpconf, rpconf.MQ.Default.RMAckTopic, rpconf.MQ.Default.BrokerRMProducerGroupName)
}

// NewBossSender 初始化 boss topic 生产者
func NewBossSender(rpconf *config.Config) (*Sender, error) {
	return newSenderDefault(rpconf, rpconf.MQ.Default.BossAckTopic, rpconf.MQ.Default.BrokerBossProducerGroupName)
}

// NewNoticeSender 初始化 notice topic 生产者
func NewNoticeSender(rpconf *config.Config) (*Sender, error) {
	return newSenderDefault(rpconf, rpconf.MQ.Default.NoticeTopic, rpconf.MQ.Default.BrokerNoticeProducerGroupName)
}

// NewCloudAuditSenderEip 初始化 云审计 eip topic 生产者
func NewCloudAuditSenderEip(rpconf *config.Config) (*Sender, error) {
	return newSenderCloudAudit(rpconf, &rpconf.MQ.CloudAudit.Topics.Eip)
}

// NewCloudAuditSenderVpc 初始化 云审计 vpc topic 生产者
func NewCloudAuditSenderVpc(rpconf *config.Config) (*Sender, error) {
	return newSenderCloudAudit(rpconf, &rpconf.MQ.CloudAudit.Topics.Vpc)
}

// NewCloudAuditSenderVpc 初始化 云审计 dc topic 生产者
func NewCloudAuditSenderDc(rpconf *config.Config) (*Sender, error) {
	return newSenderCloudAudit(rpconf, &rpconf.MQ.CloudAudit.Topics.Dc)
}

// NewCloudAuditSenderVpc 初始化 云审计 dc topic 生产者
func NewCloudAuditSenderSlb(rpconf *config.Config) (*Sender, error) {
	return newSenderCloudAudit(rpconf, &rpconf.MQ.CloudAudit.Topics.Slb)
}

// Start 开启生产者连接
func (s *Sender) Start() error {
	return s.Producer.Start()
}

// ShutDown 关闭生产者连接
func (s *Sender) ShutDown() error {
	return s.Producer.Shutdown()
}

// Send 生产消息
func (s *Sender) Send(bytesMessage []byte, key string) (string, error) {
	msg := &primitive.Message{
		Topic: s.Topic,
		Body:  bytesMessage,
	}

	logrus.Infof("Send Message to topic %v key %v: %v", s.Topic, key, string(bytesMessage))

	msg.WithKeys([]string{key})
	msg.WithShardingKey(key)

	res, err := s.Producer.SendSync(context.Background(), msg)
	if err != nil {
		return "", err
	}

	if res != nil && res.MessageQueue != nil {
		logrus.Infoln(res.MessageQueue.Topic, res.MsgID)
	}
	return res.String(), err
}
