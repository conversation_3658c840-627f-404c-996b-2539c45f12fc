package metrics

import (
	"strings"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/eip/v1"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/api/network/vpc/v1"
)

func VPCMetrics(instances, exceptions, subnets int32, lang string) *vpc.VPCMetrics {
	if strings.HasPrefix(lang, "en") {
		return VPCMetricsEN(instances, exceptions, subnets)
	}
	return VPCMetricsCN(instances, exceptions, subnets)
}

func EIPMetrics(instances, exceptions, subnets int32, lang string) *eip.EIPMetrics {
	if strings.HasPrefix(lang, "en") {
		return EIPMetricsEN(instances, exceptions, subnets)
	}
	return EIPMetricsCN(instances, exceptions, subnets)
}

func VPCMetricsEN(instances, exceptions, subnets int32) *vpc.VPCMetrics {
	return &vpc.VPCMetrics{
		ResourceMetrics: []*vpc.ResourceMetrics{
			&vpc.ResourceMetrics{
				Name:  "VPCs",
				Value: instances,
				Unit:  "pcs",
			},
			&vpc.ResourceMetrics{
				Name:  "Error VPCs",
				Value: exceptions,
				Unit:  "pcs",
				Color: "#ff0000",
			},
			&vpc.ResourceMetrics{
				Name:  "Subnets",
				Value: subnets,
				Unit:  "pcs",
			},
		},
	}
}

func EIPMetricsEN(instances, exceptions, subnets int32) *eip.EIPMetrics {
	return &eip.EIPMetrics{
		ResourceMetrics: []*eip.ResourceMetrics{
			&eip.ResourceMetrics{
				Name:  "EIPs",
				Value: instances,
				Unit:  "pcs",
			},
			&eip.ResourceMetrics{
				Name:  "Error EIPs",
				Value: exceptions,
				Unit:  "pcs",
				Color: "#ff0000",
			},
			&eip.ResourceMetrics{
				Name:  "Bandwidth",
				Value: subnets,
				Unit:  "Mbps",
			},
		},
	}
}

func VPCMetricsCN(instances, exceptions, subnets int32) *vpc.VPCMetrics {
	return &vpc.VPCMetrics{
		ResourceMetrics: []*vpc.ResourceMetrics{
			&vpc.ResourceMetrics{
				Name:  "全部实例",
				Value: instances,
				Unit:  "个",
			},
			&vpc.ResourceMetrics{
				Name:  "异常",
				Value: exceptions,
				Unit:  "个",
				Color: "#ff0000",
			},
			&vpc.ResourceMetrics{
				Name:  "子网",
				Value: subnets,
				Unit:  "个",
			},
		},
	}
}

func EIPMetricsCN(instances, exceptions, subnets int32) *eip.EIPMetrics {
	return &eip.EIPMetrics{
		ResourceMetrics: []*eip.ResourceMetrics{
			&eip.ResourceMetrics{
				Name:  "全部实例",
				Value: instances,
				Unit:  "个",
			},
			&eip.ResourceMetrics{
				Name:  "异常",
				Value: exceptions,
				Unit:  "个",
				Color: "#ff0000",
			},
			&eip.ResourceMetrics{
				Name:  "带宽",
				Value: subnets,
				Unit:  "Mbps",
			},
		},
	}
}
