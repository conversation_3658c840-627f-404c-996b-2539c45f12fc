variables:
  REPO_NAME: gitlab.bj.sensetime.com/elementary/boson/boson-provider
  GOPATH: /go
  GIT_LFS_SKIP_SMUDGE: "1"

before_script:
  - mkdir -p $GOPATH/src/$(dirname $REPO_NAME)
  - ln -svf $CI_PROJECT_DIR $GOPATH/src/$REPO_NAME
  - cd $GOPATH/src/$REPO_NAME
  - |
    echo "
    machine gitlab.bj.sensetime.com
    login sco.ain.boson.ci
    password $CI_JOB_TOKEN
    " > ~/.netrc
  - |
    echo "
    HOST *
    StrictHostKeyChecking no
    " > ~/.ssh/config

stages:
- lint
- build
- code_scan

code_scan:
  image: registry.sensetime.com/security/codescan:latest
  stage: code_scan
  script:
  - sonar_full_scan.sh
  tags:
  - k8s

code_lint:
  image: registry.sensetime.com/sensecore-boson/golangci-lint:v1.48
  variables:
    GO111MODULE: "on"
    GOPROXY: "https://goproxy.cn"
    GONOPROXY: "**gitlab.bj.sensetime.com**"
    GONOSUMDB: "**gitlab.bj.sensetime.com**"
    GOPRIVATE: "**gitlab.bj.sensetime.com**"
  stage: lint
  script:
  - git config -l
  - make lint
  when: manual

code_build:
  image: registry.sensetime.com/sensecore-boson/golangci-lint:v1.48
  variables:
    GO111MODULE: "on"
    GOPROXY: "https://goproxy.cn"
    GONOPROXY: "**gitlab.bj.sensetime.com**"
    GONOSUMDB: "**gitlab.bj.sensetime.com**"
    GOPRIVATE: "**gitlab.bj.sensetime.com**"
  stage: build
  script:
  - make build
  when: manual
