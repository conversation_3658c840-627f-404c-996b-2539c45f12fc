package main

import (
	"runtime"

	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/exporter"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/informer"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/resourceprovider"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/server"
	"gitlab.bj.sensetime.com/elementary/boson/boson-provider/pkg/utils"
)

func init() {
	runtime.SetBlockProfileRate(1)
	utils.InitAll()
}

func main() {
	ch := utils.NewStopChannel()

	resourceprovider.BosonProvider = resourceprovider.NewResourceProvider()
	exporter.Exporter = exporter.NewExporter(resourceprovider.BosonProvider.RpConfig)

	exporter.Exporter.Serve(ch)
	go informer.Controllers(ch)
	server.Serve(resourceprovider.BosonProvider.RpConfig.Boson.GRPCPort, resourceprovider.BosonProvider.RpConfig.Boson.HTTPPort, ch)

	resourceprovider.BosonProvider.Run(ch)

}
