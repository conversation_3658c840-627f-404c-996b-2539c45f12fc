create table slb_health_checks(
    target_group_id     varchar(128) PRIMARY KEY NOT NULL,
    enable              boolean NOT NULL,
    type                varchar(64),
    timeout             int default 0,
    interval            int default 0,
    health_threshold    int default 0,
    unhealth_threshold  int default 0,
    health_check_config text,
    deleted             boolean NOT NULL
);

create table slb_health_check_results(
    target_id     varchar(128) PRIMARY KEY NOT NULL,
    target_group_id     varchar(128) NOT NULL,    
    enable              boolean NOT NULL,
    status              varchar(64),
    reason              varchar(64),
    deleted             boolean NOT NULL
);
