create table subnet(
   subnet_id       varchar(64) PRIMARY KEY NOT NULL,
   display_name    varchar(64) NOT NULL,
   cidr            varchar(64) NOT NULL,
   description     varchar(256),
   is_default      boolean NOT NULL,
   scope           varchar(64) NOT NULL,
   gateway_ip      varchar(64) NOT NULL,
   id              varchar(1024) NOT NULL,
   name            varchar(64) NOT NULL,
   resourceType    varchar(64) NOT NULL,
   zone            varchar(64) NOT NULL,
   state           varchar(64) NOT NULL,
   creator_id      varchar(64) NOT NULL,
   owner_id        varchar(64) NOT NULL,
   tenant_id       varchar(64) NOT NULL,
   create_time     timestamp NOT NULL,
   update_time     timestamp NOT NULL,
   deleted         boolean NOT NULL,
   tags            text
);
