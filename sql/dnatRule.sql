

create table dnat_rules(
   dnat_rule_id    varchar(64) PRIMARY KEY NOT NULL,
   description     varchar(256),
   protocol        varchar(64) NOT NULL,
   outer_ip        varchar(64) NOT NULL,
   outer_port_min   int,
   outer_port_max   int,
   inner_port_min   int,
   inner_port_max   int,
   inner_ip         varchar(64),

   nat_gateway_id   varchar(64) NOT NULL,
   eip_id           varchar(64) NOT NULL,
   priority         int default 0,
   id              varchar(1024) NOT NULL,
   name            varchar(64) NOT NULL,
   resource_type    varchar(64) NOT NULL,
   zone            varchar(64) NOT NULL,
   state           varchar(64) NOT NULL,
   creator_id      varchar(64) NOT NULL,
   owner_id        varchar(64) NOT NULL,
   tenant_id       varchar(64) NOT NULL,
   create_time     timestamp NOT NULL,
   update_time     timestamp NOT NULL,
   deleted         boolean NOT NULL,
   tags            text
);
