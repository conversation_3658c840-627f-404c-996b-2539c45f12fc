# Boson-Provider 模块设计

## 功能定位

1. 部署在 AZ 的 k8s 中
2. 通过 Regional k8s RocketMQ 与 RM 交互，给 RM 提供 proto API
3. 使用 Regional k8s PG 存储相关 vpc, subnet, eip 等网络相关配置数据
4. 通过 Internal API 提供 AZ 集群内子产品 Provider 间服务访问，查询 vpc/namespace 关联信息等
5. 使用 k8s cr 下发网络配置，具体任务实施由 k8s controller 实现
6. 监听 k8s cr 状态更新 PG，通过 MQ 反馈状态到 RM

## 模块设计

### 参数加载模块

所有 Provider 依赖的参数，都配置到 ConfigMap 挂载到 Provider 容器中写到 `boson-provider.yaml` 文件，在 Provider 启动时统一加载

### MQ 模块

与 Regional k8s 的 RocketMQ 交互，接收的消息体为 RM 基于 API 模块的 proto 文件生成的 api 相关信息，返回的消息体为 RM 定义的标准消息体格式，follow [mq消息体](./mq/mq.md)

实现：

1. 在 MQ 中预创建好 Topic
2. 在 Provider 启动时加载配置文件 `boson-provider.yaml`，获取 MQ 的 地址，Topic 等信息（暂无认证，AccessKey 和 SecretKey 暂无）
3. Provider 根据配置，使用 `rocketmq-client-go` 连接上 MQ，分别建立 Producer 和 Consumer 连接到不同的 Topic
4. 消息体通过 parser 进行 go struct <-> mq message 的相互转换

### DB 模块

与 Postgres 交互，存储记录当前 AZ 下的相关网络资源

[暂定设计](https://gitlab.bj.sensetime.com/elementary/boson/boson-docs/-/blob/ywh-dev-draft/design/diagrams/boson-erd.png)

实现：

1. 在 PG 中与创建好上述设计的数据表，并提前初始化 natGateway ip 段等资源
2. 在 Provider 启动时加载配置文件 `boson-provider.yaml`，获取 PG 的 地址，用户名，密码，DB 等信息
3. Provider 根据配置，使用 DB 通用框架 `go-xorm/xorm ` 连接 PG 并就行读写

### 任务分发模块

根据 MQ 消息体中的 method，将任务分发到对应的逻辑处理方法，并根据任务的的同步或异步，返回 MQ

实现：

1. 基于 MQ 模块转换的消息体对应 Proto API，若消息为异步执行任务，通过 DB 模块更新 DB 并通过 MQ 模块返回状态到 MQ ，然后转发任务到实际逻辑处理模块
2. 基于 MQ 模块转换的消息体对应 Proto API，若消息为同步执行任务，直接转发任务到实际逻辑处理模块

### 逻辑处理模块

根据 MQ 消息及当前 PG 中的相关网络资源，提取相关信息和资源状态，执行相应逻辑，并创建 k8s CR 资源触发实际的网络变更

实现：

1. 针对 Proto 定义的 API 实现相关实际任务逻辑
2. 接收任务分发模块传递的任务及消息体，结合 PG 数据，处理网络任务，生成对应的 k8s cr 资源的创建/更新


### 资源监听模块

监听 k8s 的相关 CR 资源，更新 DB 数据及返回 MQ 消息

实现：

1. 使用 k8s informer watch 相关 cr 资源
2. 根据 cr 资源的状态，实时更新 DB 并通过 MQ 反馈到 RM

### Internal API

提供 AZ 内其他 Provider 调用

TBD
