# 简单介绍 toolbox 下工具的使用

使用 toolbox 工具前提为配置好 boson-provider.yaml 中的相关参数


1. syncTables 创建所有依赖的数据表

- 无参数
- 需要配置好 pg 相关信息


2. truncTables 清空所有数据表数据

- 无参数
- 需要配置好 pg 相关信息


3. initGwIPs 初始化 nat_gateway_external_ip_pools, cidr_pools 表

- 第一个参数为申请到的 IP 对应的 CIDR
- 第二个参数为 CIDR 的网关
- 第三个参数为英文逗号分隔的 IPs
- Sample: ./initGwIPs ***********/22 *********** ***********07,*************,************
- 需要配置好 pg 相关信息


4. createTopics 创建 boson-provider.yaml 中的 topic，rmAckTopic，bossAckTopic

- 无参数
- 需要配置好 rocketmq 相关信息


5. consume 模拟 RM 监听 provider 返回的消息

- 无参数
- 需要配置好 rocketmq 相关信息


6. produce 模拟 RM 消息发送

- 参数为消息体文件路径
- Sample: ./produce sample_massages/CreateVPC.json
- 需要配置好 rocketmq 相关信息
