# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: SLBDataService API
    description: "[zh] 负载均衡服务\r\n [en] SLB Data Service."
    version: 0.0.1
paths:
    /network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs:
        get:
            tags:
                - SLBDataService
            description: "[zh] 列举符合条件的负载均衡状态列表\r\n [en] List  details of SLBs resources status."
            operationId: SLBDataService_ListSLBsStatus
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: filter
                  in: query
                  description: "[zh] slb过滤条件【暂不支持】\r [en] List filter."
                  schema:
                    type: string
                - name: order_by
                  in: query
                  description: "[zh] slb资源排序规则【暂不支持】\r [en] Sort resoults."
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: "[zh] 分页大小【暂不支持】\r [en] The maximum number of items to return."
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: "[zh] 从上一个List请求返回的next_page_token值(如果有的话)【暂不支持】\r [en] The next_page_token value returned from a previous List request, if any."
                  schema:
                    type: string
                - name: slb_names
                  in: query
                  description: "[zh] slb的name列表\r [en] The slb name list"
                  schema:
                    type: array
                    items:
                        type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListSLBsStatusResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/listeners
    :   get:
            tags:
                - SLBDataService
            description: "[zh] 获取符合请求的所有监听器.\r\n [en] List requested listener."
            operationId: SLBDataService_ListListeners
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡名称\r [en] SLB name"
                  required: true
                  schema:
                    type: string
                - name: filter
                  in: query
                  description: "[zh] 过滤字段\r [en] List filter."
                  schema:
                    type: string
                - name: order_by
                  in: query
                  description: "[zh] 排序字段\r [en] Sort resoults."
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: "[zh] 分页大小\r [en] The maximum number of items to return."
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: "[zh] 分页token\r [en] The next_page_token value returned from a previous List request, if any."
                  schema:
                    type: string
                - name: tenant_id
                  in: query
                  description: "[zh] 租户ID\r [en] tenant_id."
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListListenersResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/listeners/{listener_name}
    :   get:
            tags:
                - SLBDataService
            description: "[zh] 获取符合请求的一个监听器.\r\n [en] Get a requested listener."
            operationId: SLBDataService_GetListener
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: listener_name
                  in: path
                  description: "[zh] 监听器名称\r [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Listener'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - SLBDataService
            description: "[zh] 创建一个监听器.\r\n [en] Create a Listener."
            operationId: SLBDataService_CreateListener
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: listener_name
                  in: path
                  description: "[zh] 监听器名称\r [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Listener'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Listener'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - SLBDataService
            description: "[zh] 删除一个监听器\r\n [en] Delete a listener."
            operationId: SLBDataService_DeleteListener
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: listener_name
                  in: path
                  description: "[zh] 监听器名称\r [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - SLBDataService
            description: "[zh] 更新监听器的可编辑字段.\r\n [en] Update listener editable properties."
            operationId: SLBDataService_UpdateListener
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: listener_name
                  in: path
                  description: "[zh] 监听器名称\r [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: update_mask
                  in: query
                  description: "[zh] 更新标记.\r [en] The resource update mask."
                  schema:
                    type: string
                    format: field-mask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ListenerUpdate'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Listener'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/listeners/{listener_name}/status
    :   get:
            tags:
                - SLBDataService
            description: "[zh] 获取符合请求的一个监听器的相关属性.\r\n [en] Get a requested listener status."
            operationId: SLBDataService_GetListenerStatus
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: listener_name
                  in: path
                  description: "[zh] 监听器名称\r [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListenerStatus'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/listeners/{listener_name}:start
    :   post:
            tags:
                - SLBDataService
            description: "[zh] 启动一个监听器.\r\n [en] Start a Listener."
            operationId: SLBDataService_StartListener
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: listener_name
                  in: path
                  description: "[zh] 监听器名称\r [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/StartListenerRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/listeners/{listener_name}:stop
    :   post:
            tags:
                - SLBDataService
            description: "[zh] 停止一个监听器.\r\n [en] Stop a Listener."
            operationId: SLBDataService_StopListener
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: listener_name
                  in: path
                  description: "[zh] 监听器名称\r [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/StopListenerRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/status:
        get:
            tags:
                - SLBDataService
            description: "[zh] 查看负载均衡资源后端状态.\r\n [en] Gets details of a SLB resources status."
            operationId: SLBDataService_GetSLBStatus
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/SLBStatus'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups
    :   get:
            tags:
                - SLBDataService
            description: "[zh] 列举符合请求的后端组\r\n [en] List requested TargetGroup"
            operationId: SLBDataService_ListTargetGroups
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡名称\r [en] SLB name"
                  required: true
                  schema:
                    type: string
                - name: filter
                  in: query
                  description: "[zh] 过滤字段\r [en] List filter."
                  schema:
                    type: string
                - name: order_by
                  in: query
                  description: "[zh] 排序字段\r [en] Sort resoults."
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: "[zh] 分页大小\r [en] The maximum number of items to return."
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: "[zh] 分页token\r [en] The next_page_token value returned from a previous List request, if any."
                  schema:
                    type: string
                - name: tenant_id
                  in: query
                  description: "[zh] 租户ID\r [en] tenant_id."
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListTargetGroupsResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}
    :   get:
            tags:
                - SLBDataService
            description: "[zh] 获取符合请求的后端组\r\n [en] Get requested TargetGroup"
            operationId: SLBDataService_GetTargetGroup
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: target_group_name
                  in: path
                  description: "[zh] 后端组名称\r [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/TargetGroup'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - SLBDataService
            description: "[zh] 创建一个后端组\r\n [en] Create one TargetGroup"
            operationId: SLBDataService_CreateTargetGroup
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: target_group_name
                  in: path
                  description: "[zh] 后端组名称\r [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/TargetGroup'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/TargetGroup'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - SLBDataService
            description: "[zh] 删除一个后端组\r\n [en] Delete one TargetGroup"
            operationId: SLBDataService_DeleteTargetGroup
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: target_group_name
                  in: path
                  description: "[zh] 后端组名称\r [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - SLBDataService
            description: "[zh] 更新后端组的可编辑字段\r\n [en] Update TargetGroup editable properties."
            operationId: SLBDataService_UpdateTargetGroup
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: target_group_name
                  in: path
                  description: "[zh] 后端组名称\r [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: update_mask
                  in: query
                  description: "[zh] 更新标记.\r [en] The resource update mask."
                  schema:
                    type: string
                    format: field-mask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/TargetGroupUpdate'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/TargetGroup'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}/status
    :   get:
            tags:
                - SLBDataService
            description: '[zh] 获取符合请求的后端组的状态'
            operationId: SLBDataService_GetTargetGroupStatus
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: target_group_name
                  in: path
                  description: "[zh] 后端组名称\r [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/TargetGroupStatus'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}/targets
    :   get:
            tags:
                - SLBDataService
            description: "[zh] 查询某个后端组绑定的后端列表\r\n [en] Get requested TargetGroup targets"
            operationId: SLBDataService_ListTargets
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡名称\r [en] SLB name"
                  required: true
                  schema:
                    type: string
                - name: target_group_name
                  in: path
                  description: "[zh] 后端组名称\r [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: filter
                  in: query
                  description: "[zh] 过滤字段\r [en] List filter."
                  schema:
                    type: string
                - name: order_by
                  in: query
                  description: "[zh] 排序字段\r [en] Sort resoults."
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: "[zh] 分页大小\r [en] The maximum number of items to return."
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: "[zh] 分页token\r [en] The next_page_token value returned from a previous List request, if any."
                  schema:
                    type: string
                - name: tenant_id
                  in: query
                  description: "[zh] 租户ID\r [en] tenant_id."
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListTargetsResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - SLBDataService
            description: "[zh] 修改某个后端组的一台或者多台后端的属性.\r\n [en] Update the editable properties of one or more backends of a TargetGroup."
            operationId: SLBDataService_TargetsUpdate
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: target_group_name
                  in: path
                  description: "[zh] 后端组名称\r [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/TargetsUpdateRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/TargetGroupTargets'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}/targets/{target_name}
    :   get:
            tags:
                - SLBDataService
            description: "[zh] 查询某个后端组绑定的某个后端\r\n [en] Get requested TargetGroup targets"
            operationId: SLBDataService_GetTarget
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: target_group_name
                  in: path
                  description: "[zh] 后端组名称\r [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: target_name
                  in: path
                  description: "[zh] 后端名称\r [en] The Target resource name"
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Target'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}:targetsAdd
    :   post:
            tags:
                - SLBDataService
            description: "[zh] 添加一个或多个后端到某个后端组.\r\n [en] Add one or more backend targets to a TargetGroup."
            operationId: SLBDataService_TargetGroupAddTargets
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: target_group_name
                  in: path
                  description: "[zh] 后端组名称\r [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/TargetGroupAddTargetsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/TargetGroupAddTargetsResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/slb/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}:targetsRemove
    :   post:
            tags:
                - SLBDataService
            description: "[zh] 从某个后端组删除一个或多个后端.\r\n [en] Remove one or more backend targets from a TargetGroup."
            operationId: SLBDataService_TargetGroupRemoveTargets
            parameters:
                - name: subscription_name
                  in: path
                  description: "[zh] 订阅.\r [en] Subscription."
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: "[zh] 资源组.\r [en] Resource group."
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: "[zh] 可用区.\r [en] Available zone."
                  required: true
                  schema:
                    type: string
                - name: slb_name
                  in: path
                  description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
                - name: target_group_name
                  in: path
                  description: "[zh] 后端组名称\r [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/TargetGroupRemoveTargetsRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/TargetGroupRemoveTargetsResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        CapacityLimitations:
            type: object
            properties:
                tcp_cps:
                    type: integer
                    description: "[zh] TCP 新建连接数：每秒处理的新建TCP连接的数量，单位:秒\r [en] CPS: the number of new TCP connections processed per second; Unit:Second;"
                    format: int32
                tcp_conns:
                    type: integer
                    description: "[zh] TCP 并发连接数：每分钟内并发TCP连接的数量，单位：分钟\r [en] CONNS: the number of concurrent TCP connections per minute; Unit:Minute"
                    format: int32
                udp_cps:
                    type: integer
                    description: "[zh] UDP 新建连接数：每秒处理的新建UDP连接的数量，单位：秒\r [en] The number of new UDP connections processed per second; Unit: Second"
                    format: int32
                udp_conns:
                    type: integer
                    description: "[zh] UDP 并发连接数：每分钟内并发UDP连接的数量，单位：分钟\r [en] The number of concurrent UDP connections per minute; Unit: Second"
                    format: int32
                tcpssl_cps:
                    readOnly: true
                    type: integer
                    description: "[zh] TCPSSL 新建连接数：每秒处理的新建TCPSSL连接的数量，单位：秒\r [en] The number of new TCPSSL connections processed per second; Unit: Second"
                    format: int32
                tcpssl_conns:
                    readOnly: true
                    type: integer
                    description: "[zh] TCPSSL 并发连接数：每分钟内并发TCPSSL连接的数量，单位：分钟\r [en] The number of concurrent TCPSSL connections per minute; Unit: Second"
                    format: int32
                http_cps:
                    readOnly: true
                    type: integer
                    description: "[zh] HTTP/ 新建连接数：每秒处理的新建HTTP/连接的数量，单位：秒\r [en] The number of new HTTP/ connections processed per second; Unit: Second"
                    format: int32
                http_conns:
                    readOnly: true
                    type: integer
                    description: "[zh] HTTP/ 并发连接数：每分钟内并发HTTP/连接的数量，单位：分钟\r [en] The number of concurrent HTTP/ connections per minute; Unit: Second"
                    format: int32
                http_qps:
                    readOnly: true
                    type: integer
                    description: "[zh] HTTP/ 每秒查询数：每秒可以完成的HTTP或的查询（请求）的数量，单位：秒\r [en] QPS: The number of HTTP/ queries (requests) that can be processed per second; Unit: Second"
                    format: int32
                https_cps:
                    readOnly: true
                    type: integer
                    description: "[zh] HTTPS 新建连接数：每秒处理的新建HTTPS连接的数量，单位：秒\r [en] The number of new HTTPS connections processed per second; Unit: Second"
                    format: int32
                https_conns:
                    readOnly: true
                    type: integer
                    description: "[zh] HTTPS 并发连接数：每分钟内并发HTTPS连接的数量，单位：分钟\r [en] The number of concurrent HTTPS connections per minute; Unit: Second"
                    format: int32
                https_qps:
                    readOnly: true
                    type: integer
                    description: "[zh] HTTPS 每秒查询数：每秒可以完成的HTTPS的查询（请求）的数量，单位：秒\r [en] QPS: The number of HTTPS queries (requests) that can be processed per second; Unit: Second"
                    format: int32
            description: "[zh] 资源的描述属性\r [en] Description attribute"
        CciDeploymentInstanceInfo:
            type: object
            properties:
                name:
                    type: string
                    description: "[zh] instance name\r instance name"
                display_name:
                    type: string
                    description: "[zh] instance display name\r [en] instance display name"
                id:
                    type: string
                    description: "[zh] instance id\r [en] instance id"
                uid:
                    type: string
                    description: "[zh] instance uid\r [en] instance uid"
                port:
                    type: integer
                    description: "[zh] instance port\r [en] instance port"
                    format: int32
            description: "[zh] 容器应用部署信息\r [en] CCI deployment instance info"
        EIPInfo:
            type: object
            properties:
                id:
                    type: string
                    description: "[zh] ID\r [en] ID"
                uid:
                    type: string
                    description: "[zh] UID\r [en] UID"
                name:
                    type: string
                    description: "[zh] name\r [en] name"
                display_name:
                    type: string
                    description: "[zh] display_name\r [en] display_name"
            description: "[zh] EIP 信息\r [en] EIP info"
        ForwardAction:
            type: object
            properties:
                type:
                    type: integer
                    description: "[zh] 转发动作类型\r [en] Forward action type"
                    format: enum
                forward_target_group_config:
                    $ref: '#/components/schemas/ForwardTargetGroupConfig'
            description: "[zh] 转发动作\r [en] Forward action"
        ForwardTargetGroupConfig:
            type: object
            properties:
                target_groups:
                    type: array
                    items:
                        $ref: '#/components/schemas/TargetGroupInfo'
                    description: "[zh] 后端组列表\r [en] TargetGroup list"
            description: "[zh] 转发到目的后端的配置\r [en] ForwardTarget config"
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        HTTPHealthCheckConfig:
            type: object
            properties:
                host:
                    type: string
                    description: "[zh] HTTP 检查的host\r [en] HTTP health check host"
                host_type:
                    type: integer
                    description: "[zh] host类型\r [en] host type"
                    format: enum
                path:
                    type: string
                    description: "[zh] HTTP 检查的path\r [en] HTTP health check path"
                method:
                    type: integer
                    description: "[zh] HTTP 检查的方式\r [en] HTTP health check host"
                    format: enum
                version:
                    type: integer
                    description: "[zh] HTTP 检查的版本\r [en] HTTP health check version"
                    format: enum
                return_code:
                    type: array
                    items:
                        type: integer
                        format: enum
                    description: "[zh] HTTP返回码\r [ch] HTTP return code"
            description: "[zh] HTTP 检查检查配置\r [en] HTTP health check config"
        HealthCheck:
            type: object
            properties:
                enable:
                    type: boolean
                    description: "[zh] 是否开启健康检查\r [en] Health check switch"
                type:
                    type: integer
                    description: "[zh] 健康检查类型\r [en] Healcheck type"
                    format: enum
                timeout:
                    type: integer
                    description: "[zh] 等待健康检查的超时时间，单位: 毫秒\r [en] The time to wait for a health check response, unit: ms."
                    format: int32
                interval:
                    type: integer
                    description: "[zh] 检查间隔，单位: 秒\r [en] The interval between health checks."
                    format: int32
                health_threshold:
                    type: integer
                    description: "[zh] 健康状态阈值，主机被标记为健康之前所需的健康健康检查次数。启动的时候，只需要一次，即可立即进入健康状态。\r [en] The number of healthy health checks required before a host is marked healthy"
                    format: int32
                unhealth_threshold:
                    type: integer
                    description: "[zh] 不健康状态阈值，主机被标记为不健康之前所需的不健康健康检查次数。\r [en] The number of unhealthy health checks required before a host is marked unhealthy"
                    format: int32
                tcp_health_check_config:
                    $ref: '#/components/schemas/TCPHealthCheckConfig'
                http_health_check_config:
                    $ref: '#/components/schemas/HTTPHealthCheckConfig'
            description: "[zh] 健康检查配置\r [en] Health check config"
        ListListenersResponse:
            type: object
            properties:
                listener_status_infos:
                    type: array
                    items:
                        $ref: '#/components/schemas/ListenerStatusInfo'
                    description: "[zh] Listener 列表\r [en] Listener list"
                next_page_token:
                    type: string
                    description: "[zh] 下一个页面的 token，如果没有更多数据则为空.\r [en] Token to retrieve the next page of results, or empty if there are no more results in the list."
                total_size:
                    type: integer
                    description: "[zh] 总数量\r [en] total size"
                    format: int32
            description: "[zh] 列举 Listeners 的响应\r [en] Response to list Listeners"
        ListSLBsStatusResponse:
            type: object
            properties:
                slbs:
                    type: array
                    items:
                        $ref: '#/components/schemas/SLBStatus'
                    description: "[zh] SLB状态列表\r [en] SLB status list"
                next_page_token:
                    type: string
                    description: "[zh] 下一个页面的 token，如果没有更多数据则为空.\r [en] Token to retrieve the next page of results, or empty if there are no more results in the list."
                total_size:
                    type: integer
                    description: "[zh] 返回的slb实例总数\r [en] total size"
                    format: int32
            description: "[zh] 列举符合条件的负载均衡状态列表\r [en] List  details of SLBs resources status."
        ListTargetGroupsResponse:
            type: object
            properties:
                target_groups:
                    type: array
                    items:
                        $ref: '#/components/schemas/TargetGroupStatus'
                    description: "[zh] TargetGroup 列表\r [en] TargetGroup list"
                next_page_token:
                    type: string
                    description: "[zh] 下一个页面的 token，如果没有更多数据则为空.\r [en] Token to retrieve the next page of results, or empty if there are no more results in the list."
                total_size:
                    type: integer
                    description: "[zh] 总数量\r [en] total size"
                    format: int32
            description: "[zh] 列举符合请求的后端组\r [en] Response to list TargetGroup"
        ListTargetsResponse:
            type: object
            properties:
                target_infos:
                    type: array
                    items:
                        $ref: '#/components/schemas/TargetInfo'
                    description: "[zh] Target 列表\r [en] Target list"
                next_page_token:
                    type: string
                    description: "[zh] 下一个页面的 token，如果没有更多数据则为空.\r [en] Token to retrieve the next page of results, or empty if there are no more results in the list."
                total_size:
                    type: integer
                    description: "[zh] 总数量\r [en] total size"
                    format: int32
            description: "[zh] 查询某个后端组绑定的后端列表\r [en] Response to list TargetGroup targets"
        Listener:
            required:
                - display_name
            type: object
            properties:
                id:
                    readOnly: true
                    type: string
                    description: "[zh] 资源id.\r [en] The Listener resource id using the form:\r     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/slbs/{slb_name}/listeners/{name}`."
                uid:
                    readOnly: true
                    type: string
                    description: "[zh] 资源的uuid.\r [en] The Listener resource uuid."
                name:
                    type: string
                    description: "[zh] 资源标识.\r [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                display_name:
                    type: string
                    description: "[zh] 资源名称.\r [en] The Listener resource display name"
                description:
                    type: string
                    description: "[zh] 资源描述.\r [en] The Listener resource description."
                resource_type:
                    type: string
                    description: "[zh] 资源类型.\r [en] The Listener resource type."
                creator_id:
                    readOnly: true
                    type: string
                    description: "[zh] 创建者id.\r [en] The id of the user who created the Listener resource."
                owner_id:
                    readOnly: true
                    type: string
                    description: "[zh] 拥有者id.\r [en] The id of the user who owns the Listener resource."
                tenant_id:
                    readOnly: true
                    type: string
                    description: "[zh] 租户id\r [en] Tenant id."
                zone:
                    readOnly: true
                    type: string
                    description: "[zh] 可用区.\r [en] Available zone."
                state:
                    readOnly: true
                    type: integer
                    description: "[zh] 资源状态.\r [en] The current state of the Listener resource."
                    format: enum
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: "[zh] 监听器资源的标签.\r [en] Tags attached to the VirtualMachine resource."
                properties:
                    $ref: '#/components/schemas/ListenerProperties'
                deleted:
                    readOnly: true
                    type: boolean
                    description: "[zh] 资源是否已删除.\r [en] Indicates whether the Listener resource is deleted or not."
                create_time:
                    readOnly: true
                    type: string
                    description: "[zh] 资源创建时间.\r [en] The time when the Listener resource was created."
                    format: date-time
                update_time:
                    readOnly: true
                    type: string
                    description: "[zh] 资源更新时间.\r [en] The time when the Listener resource was last updated."
                    format: date-time
            description: "[zh] 监听器资源.\r [en] The Listener resource."
        ListenerHealthCheckStatus:
            type: object
            properties:
                state:
                    type: integer
                    description: "[zh] 后端健康检查状态\r [en] health check status"
                    format: enum
            description: "[zh] 健康检查结果\r [en] Health check status"
        ListenerInfo:
            type: object
            properties:
                id:
                    type: string
                    description: "[zh] ID\r [en] EIP ID"
                uid:
                    type: string
                    description: "[zh] UID\r [en] UID"
                name:
                    type: string
                    description: "[zh] name\r [en] name"
                display_name:
                    type: string
                    description: "[zh] display_name\r [en] display_name"
                properties:
                    $ref: '#/components/schemas/ListenerProperties'
            description: "[zh] 监听器信息\r [en] Listener info"
        ListenerProperties:
            type: object
            properties:
                protocol:
                    type: integer
                    description: "[zh] 监听的协议\r [en] Listener protocol"
                    format: enum
                port:
                    type: integer
                    description: "[zh] 监听的端口\r [en] Listener port"
                    format: int32
                default_forward_action:
                    $ref: '#/components/schemas/ForwardAction'
                original_client_ip_address:
                    $ref: '#/components/schemas/OriginalClientIPAddress'
            description: "[zh] Listener 资源实际属性\r [en] Real resource properties of Listener"
        ListenerStatus:
            type: object
            properties:
                health_check_status:
                    $ref: '#/components/schemas/ListenerHealthCheckStatus'
            description: "[zh] Listener 的状态\r [en] Listener status"
        ListenerStatusInfo:
            type: object
            properties:
                listener:
                    $ref: '#/components/schemas/Listener'
                status:
                    $ref: '#/components/schemas/ListenerStatus'
            description: "[zh] Listener 的信息\r [en] Listener info"
        ListenerUpdate:
            required:
                - display_name
            type: object
            properties:
                display_name:
                    type: string
                    description: "[zh] 资源名称.\r [en] The Listener resource display name"
                description:
                    type: string
                    description: "[zh] 资源描述.\r [en] The Listener resource description."
                properties:
                    $ref: '#/components/schemas/ListenerProperties'
            description: "[zh] 监听器可更新资源.\r [en] The Listener update-resource."
        OrderInfo:
            type: object
            properties:
                billing_cycle_number:
                    type: integer
                    description: '[zh] 购买时长. [en] length of purchase.'
                    format: int32
                auto_renew:
                    type: boolean
                    description: '[zh] 自动续费. [en] Automatic renewal.'
                currency_code:
                    type: string
                    description: '[zh] 货币代码. [en] currency code.'
                payment_channel:
                    type: integer
                    description: '[zh] 支付方式. [en] payment method.'
                    format: enum
                note:
                    type: string
                    description: '[zh] 订单备注. [en] order notes'
                order_type:
                    type: integer
                    description: '[zh] 订单类型. [en] Order Type.'
                    format: enum
                order_id:
                    type: string
                    description: '[zh] 订单id. [en] order id.'
                start_time:
                    type: string
                    description: '[zh] 订单生效日期. [en] Order effective date.'
                    format: date-time
                payment_model:
                    type: integer
                    description: '[zh] 付费类型. [en] payment type.'
                    format: enum
                billing_model:
                    type: integer
                    description: '[zh] 计费类型. [en] billing type.'
                    format: enum
                original_id:
                    type: string
                    description: '[zh] 合同包ID. [en] Contract package ID. original id-- OT_ORIGINAL: original spu_id/package_id; OT_RENEW/OT_UPGRADED/OT_DOWNGRADED: original/ order_id; OT_CONTRACT: original contract_id'
                end_time:
                    type: string
                    description: '[zh] 订单结束时间. [en] Order end time.'
                    format: date-time
            description: '[zh] 订单信息. [en] Order Infomation.'
        OriginalClientIPAddress:
            type: object
            properties:
                original_client_ip_address_enable:
                    type: boolean
                    description: "[zh] 是否开启源地址保持，默认关闭\r [en] Original client IP address retaine enable"
                proxy_protocol_enable:
                    type: boolean
                    description: "[zh] 四层 Proxy Protocol 配置，当 源地址保持 开启，且监听协议为四层协议的时候，此字段生效\r [en] The configuration of Proxy Protocol"
            description: "[zh] 源地址保持配置\r [en] Config of retaine original client IP address"
        Resources:
            type: object
            properties:
                capacity_limitations:
                    $ref: '#/components/schemas/CapacityLimitations'
            description: "[zh] 资源规格属性\r [en] Resource Specification Properties."
        SLB:
            required:
                - display_name
                - sku_id
            type: object
            properties:
                id:
                    readOnly: true
                    type: string
                    description: "[zh] 资源id.\r [en] The SLB resource id using the form:\r     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/slbs/{name}`."
                uid:
                    readOnly: true
                    type: string
                    description: "[zh] 资源的uuid.\r [en] The SLB resource uuid."
                name:
                    type: string
                    description: "[zh] 资源标识.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                display_name:
                    type: string
                    description: "[zh] 资源名称.\r [en] The SLB resource display name"
                description:
                    type: string
                    description: "[zh] 资源描述.\r [en] The SLB resource description."
                resource_type:
                    type: string
                    description: "[zh] 资源类型.\r [en] The SLB resource type."
                creator_id:
                    readOnly: true
                    type: string
                    description: "[zh] 创建者id.\r [en] The id of the user who created the SLB resource."
                owner_id:
                    readOnly: true
                    type: string
                    description: "[zh] 拥有者id.\r [en] The id of the user who owns the SLB resource."
                tenant_id:
                    readOnly: true
                    type: string
                    description: "[zh] 租户id\r [en] Tenant id."
                zone:
                    readOnly: true
                    type: string
                    description: "[zh] 可用区.\r [en] Available zone."
                state:
                    readOnly: true
                    type: integer
                    description: "[zh] 资源状态.\r [en] The current state of the SLB resource."
                    format: enum
                sku_id:
                    type: string
                    description: "[zh] 最小库存单元id.\r [en] SKU id."
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: "[zh] 负载均衡资源的标签.\r [en] Tags attached to the VirtualMachine resource."
                properties:
                    $ref: '#/components/schemas/SLBProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
                deleted:
                    readOnly: true
                    type: boolean
                    description: "[zh] 资源是否已删除.\r [en] Indicates whether the SLB resource is deleted or not."
                create_time:
                    readOnly: true
                    type: string
                    description: "[zh] 资源创建时间.\r [en] The time when the SLB resource was created."
                    format: date-time
                update_time:
                    readOnly: true
                    type: string
                    description: "[zh] 资源更新时间.\r [en] The time when the SLB resource was last updated."
                    format: date-time
            description: "[zh] 负载均衡资源.\r [en] The SLB resource."
        SLBProperties:
            type: object
            properties:
                resources:
                    $ref: '#/components/schemas/Resources'
                type:
                    type: integer
                    description: "[zh] SLB 类型\r [en] SLB loadbalancer type"
                    format: enum
                vpc_id:
                    type: string
                    description: "[zh] 所属 VPC uid\r [en] VPC uid"
                ip_version:
                    type: integer
                    description: "[zh] IP 协议栈版本\r [en] IP protocol version"
                    format: enum
                eip_id:
                    type: string
                    description: "[zh] 所关联的 EIP uid.\r [en] EIP uid."
            description: "[zh] 资源属性.\r [en] The SLBProperties."
        SLBStatus:
            type: object
            properties:
                slb:
                    $ref: '#/components/schemas/SLB'
                internet_vip:
                    type: string
                    description: "[zh] 公网VIP\r [en] internet vip"
                intranet_vip:
                    type: string
                    description: "[zh] 内网VIP\r [en] intranet vip"
                basic_network_vip:
                    type: string
                    description: "[zh] 基础网络VIP\r [en] basic network vip"
                expose_basic_network_vip:
                    type: boolean
                    description: "[zh] 是否暴露基础网络VIP\r [en] expose basic network vip"
                vpc:
                    $ref: '#/components/schemas/VPCInfo'
                eip:
                    $ref: '#/components/schemas/EIPInfo'
                listeners:
                    type: array
                    items:
                        $ref: '#/components/schemas/ListenerInfo'
                    description: "[zh] 监听器信息\r [en] listener info"
            description: "[zh] SLB 的后端状态\r [en] SLB status"
        StartListenerRequest:
            required:
                - subscription_name
                - resource_group_name
                - zone
                - slb_name
                - listener_name
            type: object
            properties:
                subscription_name:
                    type: string
                    description: "[zh] 订阅.\r [en] Subscription."
                resource_group_name:
                    type: string
                    description: "[zh] 资源组.\r [en] Resource group."
                zone:
                    type: string
                    description: "[zh] 可用区.\r [en] Available zone."
                slb_name:
                    type: string
                    description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                listener_name:
                    type: string
                    description: "[zh] 监听器名称\r [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
            description: "[zh] 启动一个监听器的请求\r [en] Request to start a Listener"
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        StopListenerRequest:
            required:
                - subscription_name
                - resource_group_name
                - zone
                - slb_name
                - listener_name
            type: object
            properties:
                subscription_name:
                    type: string
                    description: "[zh] 订阅.\r [en] Subscription."
                resource_group_name:
                    type: string
                    description: "[zh] 资源组.\r [en] Resource group."
                zone:
                    type: string
                    description: "[zh] 可用区.\r [en] Available zone."
                slb_name:
                    type: string
                    description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                listener_name:
                    type: string
                    description: "[zh] 监听器名称\r [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
            description: "[zh] 停止一个监听器的请求\r [en] Request to stop a Listener"
        TCPHealthCheckConfig:
            type: object
            properties:
                port:
                    type: integer
                    description: "[zh] 健康检查端口，发送SYN握手报文来检测服务器端口是否存活\r [en] Health check port, send SYN handshake packets to detect if the server port is alive."
                    format: int32
            description: "[zh] TCP 检查检查配置\r [en] TCP health check config"
        Target:
            type: object
            properties:
                id:
                    type: string
                    description: "[zh] 资源id\r [en] The Listener resouce id using the form:\r     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}/targets/{name}`."
                uid:
                    type: string
                    description: "[zh] 资源uuid\r [en] The Target uuid"
                name:
                    type: string
                    description: "[zh] 资源标识\r [en] The Target resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                display_name:
                    type: string
                    description: "[zh] 资源名称\r [en] The Target resource display name"
                description:
                    type: string
                    description: "[zh] 资源描述\r [en] The Target resource description."
                resource_type:
                    type: string
                    description: "[zh] 资源类型\r [en] The Target resource type."
                creator_id:
                    type: string
                    description: "[zh] 创建者id\r [en] The id of the user who created the Target resource."
                owner_id:
                    type: string
                    description: "[zh] 拥有者id\r [en] The id of the user who owns the Target resource."
                tenant_id:
                    type: string
                    description: "[zh] 租户id\r [en] Tenant id."
                zone:
                    type: string
                    description: "[zh] 可用区\r [en] Available zone."
                state:
                    type: integer
                    description: "[zh] 资源状态\r [en] The current state of the Target resource."
                    format: enum
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: "[zh] 资源标签\r [en] Tags attached to the Target resource."
                properties:
                    $ref: '#/components/schemas/TargetProperties'
                deleted:
                    type: boolean
                    description: "[zh] 是否删除\r [en] Indicates whether the Target resource is deleted or not."
                create_time:
                    type: string
                    description: "[zh] 创建时间\r [en] The time when the Target resource was created."
                    format: date-time
                update_time:
                    type: string
                    description: "[zh] 更新时间\r [en] The time when the Target resource was last updated."
                    format: date-time
            description: "[zh] Target 实体结构\r [en] Target entity"
        TargetGroup:
            type: object
            properties:
                id:
                    type: string
                    description: "[zh] 资源ID\r [en] The Listener resouce id using the form:\r     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{name}`."
                uid:
                    type: string
                    description: "[zh] 资源UUID\r [en] The TargetGroup uuid"
                name:
                    type: string
                    description: "[zh] 资源标识.\r [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                display_name:
                    type: string
                    description: "[zh] 资源名称.\r [en] The TargetGroup resource display name"
                description:
                    type: string
                    description: "[zh] 资源描述.\r [en] The TargetGroup resource description."
                resource_type:
                    type: string
                    description: "[zh] 资源类型.\r [en] The TargetGroup resource type."
                creator_id:
                    type: string
                    description: "[zh] 创建者id.\r [en] The id of the user who created the TargetGroup resource."
                owner_id:
                    type: string
                    description: "[zh] 拥有者id.\r [en] The id of the user who owns the TargetGroup resource."
                tenant_id:
                    type: string
                    description: "[zh] 租户id\r [en] Tenant id."
                zone:
                    type: string
                    description: "[zh] 可用区.\r [en] Available zone."
                state:
                    type: integer
                    description: "[zh] 资源状态.\r [en] The current state of the TargetGroup resource."
                    format: enum
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: "[zh] 资源的标签.\r [en] Tags attached to the TargetGroup resource."
                properties:
                    $ref: '#/components/schemas/TargetGroupProperties'
                deleted:
                    type: boolean
                    description: "[zh] 是否删除\r [en] Indicates whether the TargetGroup resource is deleted or not."
                create_time:
                    type: string
                    description: "[zh] 创建时间\r [en] The time when the TargetGroup resource was created."
                    format: date-time
                update_time:
                    type: string
                    description: "[zh] 更新时间\r [en] The time when the TargetGroup resource was last updated."
                    format: date-time
            description: "[zh] 后端组\r [en] TargetGroup"
        TargetGroupAddTargetsRequest:
            required:
                - subscription_name
                - resource_group_name
                - zone
                - slb_name
                - target_group_name
            type: object
            properties:
                subscription_name:
                    type: string
                    description: "[zh] 订阅.\r [en] Subscription."
                resource_group_name:
                    type: string
                    description: "[zh] 资源组.\r [en] Resource group."
                zone:
                    type: string
                    description: "[zh] 可用区.\r [en] Available zone."
                slb_name:
                    type: string
                    description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                target_group_name:
                    type: string
                    description: "[zh] 后端组名称\r [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/Target'
                    description: "[zh] 后端\r [en] The TargetGroup resource to create"
            description: "[zh] 添加一个或多个后端到某个后端组.\r [en] Add one or more backend targets to a TargetGroup."
        TargetGroupAddTargetsResponse:
            type: object
            properties:
                curr_targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/Target'
                    description: "[zh] 当前后端组包含的后端列表\r [en] List of backends contained in the current backend group"
            description: "[zh] 添加一个或多个后端到某个后端组\r [en] Response to add targets"
        TargetGroupInfo:
            type: object
            properties:
                id:
                    type: string
                    description: "[zh] ID\r [en] EIP ID"
                uid:
                    type: string
                    description: "[zh] UID\r [en] UID"
                name:
                    type: string
                    description: "[zh] name\r [en] name"
                display_name:
                    type: string
                    description: "[zh] display_name\r [en] display_name"
            description: "[zh] 后端组信息\r [en] target group info"
        TargetGroupProperties:
            type: object
            properties:
                type:
                    type: integer
                    description: "[zh] 后端组的类型\r [en] TargetGroup type"
                    format: enum
                scheduler:
                    type: integer
                    description: "[zh] 负载算法\r [en] The scheduling algorithm"
                    format: enum
                protocol:
                    type: integer
                    description: "[zh] 后端使用的协议，默认与监听协议保持一致\r [en] The protocol used by the Target"
                    format: enum
                health_check:
                    $ref: '#/components/schemas/HealthCheck'
                weight:
                    type: integer
                    description: "[zh] 后端组权重，范围 0~100，默认100，如果为0，则不转发请求到这个后端组\r [en] The weight of the TargetGroup. Valid values: 0 to 100. Default value: 100. If the weight of a TargetGroup is set to 0, no requests are forwarded to the TargetGroup."
                    format: int32
                cci_info:
                    $ref: '#/components/schemas/CciDeploymentInstanceInfo'
            description: "[zh] 后端组资源实际属性\r [en] Real resource properties of TargetGroup."
        TargetGroupRemoveTargetsRequest:
            required:
                - subscription_name
                - resource_group_name
                - zone
                - slb_name
                - target_group_name
            type: object
            properties:
                subscription_name:
                    type: string
                    description: "[zh] 订阅.\r [en] Subscription."
                resource_group_name:
                    type: string
                    description: "[zh] 资源组.\r [en] Resource group."
                zone:
                    type: string
                    description: "[zh] 可用区.\r [en] Available zone."
                slb_name:
                    type: string
                    description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                target_group_name:
                    type: string
                    description: "[zh] 后端组名称\r [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                target_ids:
                    type: array
                    items:
                        type: string
                    description: "[zh] 后端ID列表\r [en] The Target uids"
            description: "[zh] 从某个后端组删除一个或多个后端.\r [en] Remove one or more backend targets from a TargetGroup."
        TargetGroupRemoveTargetsResponse:
            type: object
            properties:
                curr_targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/Target'
                    description: "[zh] 后端组包含的后端列表\r [en] List of backends contained in the current backend group"
            description: "[zh] 从某个后端组删除一个或多个后端.\r [en] Response to remove targets"
        TargetGroupStatus:
            type: object
            properties:
                target_group:
                    $ref: '#/components/schemas/TargetGroup'
                listeners:
                    type: array
                    items:
                        $ref: '#/components/schemas/ListenerInfo'
                    description: "[zh] 关联的监听器信息\r [en] Associated Listener Information"
                vpc:
                    $ref: '#/components/schemas/VPCInfo'
            description: "[zh] TargetGroup 的状态\r [en] TargetGroup status`"
        TargetGroupTargets:
            type: object
            properties:
                curr_targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/Target'
                    description: "[zh] 后端组包含的后端列表\r [en] List of backends contained in the current backend group"
            description: "[zh] 修改某个后端组的后端属性的响应\r [en] Response to modifying the backend properties of a backend group"
        TargetGroupUpdate:
            type: object
            properties:
                display_name:
                    type: string
                    description: "[zh] 资源名称.\r [en] The TargetGroup resource display name"
                description:
                    type: string
                    description: "[zh] 资源描述.\r [en] The TargetGroup resource description."
                properties:
                    $ref: '#/components/schemas/TargetGroupProperties'
            description: "[zh] 后端组可更新资源.\r [en] The TargetGroup update-resource."
        TargetHealthCheckStatus:
            type: object
            properties:
                state:
                    type: integer
                    description: "[zh] 健康检查状态\r [en] Health check status"
                    format: enum
            description: "[zh] 后端的健康检查结果\r [en] Target health check status"
        TargetInfo:
            type: object
            properties:
                target:
                    $ref: '#/components/schemas/Target'
                status:
                    $ref: '#/components/schemas/TargetStatus'
            description: "[zh] Target 实体结构\r [en] Target entity"
        TargetProperties:
            type: object
            properties:
                ipv4_address:
                    type: string
                    description: "[zh] IPv4地址\r [en] ipv4 address"
                port:
                    type: integer
                    description: "[zh] 后端使用的端口\r [en] The port used by the Target."
                    format: int32
                weight:
                    type: integer
                    description: "[zh] 后端权重，范围 0~100，默认100，如果为0，则不转发请求到这个后端\r [en] The weight of the Target. Valid values: 0 to 100. Default value: 100. If the weight of a target is set to 0, no requests are forwarded to the target."
                    format: int32
                type:
                    type: integer
                    description: "[zh] 后端类型\r [en] Target type"
                    format: enum
                instance_name:
                    type: string
                    description: "[zh] 实例名称\r [en] instanceName"
            description: "[zh] Target 资源实际属性\r [en] Real resource properties of Target"
        TargetStatus:
            type: object
            properties:
                health_check:
                    $ref: '#/components/schemas/TargetHealthCheckStatus'
            description: "[zh] Target 实体结构\r [en] Target entity"
        TargetUpdate:
            type: object
            properties:
                uid:
                    type: string
                    description: "[zh] 资源uuid\r [en] The Target uuid"
                display_name:
                    type: string
                    description: "[zh] 资源名称\r [en] The Target resource display name"
                description:
                    type: string
                    description: "[zh] 资源描述\r [en] The Target resource description."
                properties:
                    $ref: '#/components/schemas/TargetProperties'
            description: "[zh] 后端可更新资源.\r [en] The Target update-resource."
        TargetsUpdateRequest:
            required:
                - subscription_name
                - resource_group_name
                - zone
                - slb_name
                - target_group_name
            type: object
            properties:
                subscription_name:
                    type: string
                    description: "[zh] 订阅.\r [en] Subscription."
                resource_group_name:
                    type: string
                    description: "[zh] 资源组.\r [en] Resource group."
                zone:
                    type: string
                    description: "[zh] 可用区.\r [en] Available zone."
                slb_name:
                    type: string
                    description: "[zh] 负载均衡资源名称.\r [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                target_group_name:
                    type: string
                    description: "[zh] 后端组名称\r [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`."
                target_group_targets:
                    type: array
                    items:
                        $ref: '#/components/schemas/TargetUpdate'
                    description: '[en] The TargetGroup resource to update'
                update_mask:
                    readOnly: true
                    type: string
                    description: "[zh] 更新标记.\r [en] update_mask"
                    format: field-mask
            description: "[zh] 修改某个后端组的一台或者多台后端的属性.\r [en] Update the editable properties of one or more backends of a TargetGroup."
        VPCInfo:
            type: object
            properties:
                id:
                    type: string
                    description: "[zh] ID\r [en] ID"
                uid:
                    type: string
                    description: "[zh] UID\r [en] UID"
                name:
                    type: string
                    description: "[zh] name\r [en] name"
                display_name:
                    type: string
                    description: "[zh] display_name\r [en] display_name"
            description: "[zh] VPC 信息\r [en] VPC info"
tags:
    - name: SLBDataService
