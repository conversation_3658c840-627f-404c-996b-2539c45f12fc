# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: EIPs API
    description: |-
        [zh] EIP 服务，管理 AZ 内租户 EIP 相关网络资源.
         [en] Service of EIP, for managing EIP related network resources for tenants in AZ.
    version: 0.0.1
paths:
    /network/eip/data/v1/eips/metrics:
        get:
            tags:
                - EIPs
            description: |-
                [zh] EIP metrics.
                 [en] EIP metrics.
            operationId: EIPs_GetEIPMetrics
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/EIPMetrics'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/eip/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}/status:
        get:
            tags:
                - EIPs
            description: |-
                [zh] 查看 EIP 状态信息.
                 [en] Get a requested EIP status.
            operationId: EIPs_GetEIPStatus
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅 [en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组 [en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区 [en] Available zone'
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: '[zh] eip名 [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/EIPStatus'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips:
        get:
            tags:
                - EIPs
            description: |-
                [zh] 获取 EIP 实例列表.
                 [en] List requested EIPs.
            operationId: EIPs_ListEIPs
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅 [en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组 [en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区 [en] Available zone'
                  required: true
                  schema:
                    type: string
                - name: filter
                  in: query
                  description: '[zh] eip过滤条件 [en] List filter.'
                  schema:
                    type: string
                - name: order_by
                  in: query
                  description: '[zh] eip资源排序规则 [en] Sort resoults.'
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: '[zh] 分页大小 [en] The maximum number of items to return.'
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: '[zh] 从上一个List请求返回的next_page_token值(如果有的话) [en] The next_page_token value returned from a previous List request, if any.'
                  schema:
                    type: string
                - name: tenant_id
                  in: query
                  description: '[zh] 租户id [en] tenant_id.'
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListEIPsResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}:
        get:
            tags:
                - EIPs
            description: |-
                [zh] 查看 EIP 实例详情.
                 [en] Get a requested EIP.
            operationId: EIPs_GetEIP
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅 [en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组 [en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区 [en] Available zone'
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: '[zh] eip名 [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/EIP'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - EIPs
            description: |-
                [zh] 创建一个 EIP.
                 [en] Create a EIP.
            operationId: EIPs_CreateEIP
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅 [en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组 [en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区 [en] Available zone, todo: add validation'
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: '[zh] eip名 [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/EIP'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/EIP'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - EIPs
            description: |-
                [zh] 删除一个 EIP.
                 [en] Delete a EIP.
            operationId: EIPs_DeleteEIP
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅 [en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组 [en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区 [en] Available zone'
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: '[zh] eip名 [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - EIPs
            description: |-
                [zh] 更新 EIP 可编辑字段.
                 [en] Update EIP editable properties.
            operationId: EIPs_UpdateEIP
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅 [en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组 [en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区 [en] Available zone'
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: '[zh] eip名 [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
                - name: update_mask
                  in: query
                  description: '[zh] 要更新的eip字段掩码 [en] update_mask'
                  schema:
                    type: string
                    format: field-mask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/EIP'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/EIP'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}:expireStop:
        post:
            tags:
                - EIPs
            description: |-
                [zh] 过期停服一个 EIP.
                 [en] ExpireStop a EIP.
            operationId: EIPs_ExpireStopEIP
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅 [en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组 [en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区 [en] Available zone'
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: '[zh] eip名 [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}:forceDelete:
        post:
            tags:
                - EIPs
            description: |-
                [zh] 强制删除一个 EIP.
                 [en] Force Delete a EIP.
            operationId: EIPs_ForceDeleteEIP
            parameters:
                - name: subscription_name
                  in: path
                  description: '[en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[en] Available zone'
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: '[en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}:release:
        post:
            tags:
                - EIPs
            description: |-
                [zh] 在保留期释放一个 EIP.
                 [en] release a EIP.
            operationId: EIPs_ReleaseEIP
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅 [en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组 [en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区 [en] Available zone'
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: '[zh] eip名 [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}:renewStart:
        post:
            tags:
                - EIPs
            description: |-
                [zh] 恢复一个 EIP.
                 [en] Resume a EIP.
            operationId: EIPs_RenewStartEIP
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅 [en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组 [en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区 [en] Available zone'
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: '[zh] eip名 [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/eip/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}:resize:
        post:
            tags:
                - EIPs
            description: |-
                [zh] EIP 扩缩容.
                 [en] EIP resize.
            operationId: EIPs_ResizeEIP
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅. [en] Subscription.'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组. [en] Resource group.'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区. [en] Available zone.'
                  required: true
                  schema:
                    type: string
                - name: eip_name
                  in: path
                  description: '[zh] EIP 名称，需要严格满足正则 `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$` [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/EIPResize'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/EIP'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        ACLProperties:
            type: object
            properties:
                action:
                    type: integer
                    description: '[zh] ACL动作. [en] ACL action.'
                    format: enum
                src:
                    type: string
                    description: '[zh] ACL源地址. [en] ACL source addresses, support multi IPs,IP range or Subnet: ********,********,*********-*********,********/27.'
                dest:
                    type: string
                    description: '[zh] ACL目的地址. [en] ACL destination addresses, reserved now.'
                dest_port:
                    type: string
                    description: '[zh] ACL目的端口，目前保留. [en] ACL destination port, reserved now.'
                protocol:
                    type: string
                    description: '[zh] 对外暴露的协议，目前保留. [en] External protocol, reserved now.'
                priority:
                    type: integer
                    description: '[zh] ACL规则优先级，目前保留. [en] ACL rule priority.'
                    format: int32
            description: '[zh] 资源实际属性. [en] Real resource properties.'
        BillingItems:
            type: object
            properties:
                bw:
                    type: integer
                    description: '[zh] EIP 带宽限制，单位: M/s. [en] eip bandwidth, unit: M/s'
                    format: int32
            description: '[zh] 计费项 [en] billing items'
        EIP:
            type: object
            properties:
                id:
                    type: string
                    description: '[zh] EIP id [en] The EIP resource id using the form:     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}`.'
                name:
                    type: string
                    description: '[zh] EIP名 [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                display_name:
                    type: string
                    description: '[zh] EIP前端展示名 [en] eip resource display name'
                description:
                    type: string
                    description: '[zh] EIP说明 [en] eip resource description'
                uid:
                    type: string
                    description: '[zh] EIP uuid [en] The EIP resource uuid.'
                resource_type:
                    type: string
                    description: '[zh] EIP资源类型 [en] The EIP resource type.'
                creator_id:
                    type: string
                    description: '[zh] 创建该EIP的用户id [en] The id of the user who created the EIP resource.'
                owner_id:
                    type: string
                    description: '[zh] 拥有该EIP资源的用户id [en] The id of the user who owns the EIP resource.'
                tenant_id:
                    type: string
                    description: '[zh] 租户id [en] Tenant id.'
                zone:
                    type: string
                    description: '[zh] 可用区 [en] Available zone.'
                state:
                    type: integer
                    description: '[zh] 当前EIP资源的状态 [en] The current state of the EIP resource.'
                    format: enum
                sku_id:
                    type: string
                    description: '[zh] 最小库存单元id [en] Sku id.'
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: '[zh] EIP资源标签 [en] Tags attached to the EIP resource.'
                properties:
                    $ref: '#/components/schemas/EIPProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
                deleted:
                    type: boolean
                    description: '[zh] EIP是否已删除 [en] Indicates whether the EIP resource is deleted or not.'
                create_time:
                    type: string
                    description: '[zh] EIP资源的创建时间 [en] The time when the EIP resource was created.'
                    format: date-time
                update_time:
                    type: string
                    description: '[zh] EIP资源的更新时间 [en] The time when the EIP resource was last updated.'
                    format: date-time
            description: '[zh] EIP 实例结构体. [en] EIP entity.'
        EIPACL:
            type: object
            properties:
                acl_name:
                    type: string
                    description: '[zh] ACL名称. [en] The name of the ACL.'
                acl_properties:
                    $ref: '#/components/schemas/ACLProperties'
                acl_state:
                    type: string
                    description: '[zh] ACL状态. [en] The state of the ACL.'
            description: '[zh] EIP ACL规则 [en] EIP ACL'
        EIPMetrics:
            type: object
            properties:
                resource_metrics:
                    type: array
                    items:
                        $ref: '#/components/schemas/ResourceMetrics'
                    description: resource_metrics.
            description: EIP 相关 metrics. [en] EIP metrics.
        EIPProperties:
            type: object
            properties:
                vpc_id:
                    type: string
                    description: '[zh] VPC的uuid. [en] VPC uid.'
                association_id:
                    type: string
                    description: '[zh] 关联的设备或组件 name, like vpc nat gateway id. [en] Related device or item id, like vpc nat gateway id.'
                association_type:
                    type: integer
                    description: '[zh] 关联的设备或组件类型，NATGW,POD,SLB,BM. [en] Related device type，NATGW,POD,SLB,BM.'
                    format: enum
                resources:
                    $ref: '#/components/schemas/Resources'
                sku:
                    type: string
                    description: '[zh] EIP 类型. [en] EIP sku.'
                default_snat:
                    type: boolean
                    description: '[zh] 是否作为默认 vpc 流量出口. [en] The vpc default snat.'
                acl_enabled:
                    type: boolean
                    description: '[zh] 是否启用黑白名单. [en] EIP acl enabled.'
                acls:
                    type: array
                    items:
                        $ref: '#/components/schemas/EIPACL'
                    description: '[zh] EIP黑白名单规则列表. [en] EIP acl rule list.'
                internal_eip:
                    type: boolean
                    description: '[zh] 是否是企业网EIP. [en] EIP internal flag.'
                snat_dst_cidr_enabled:
                    type: boolean
                    description: '[zh] 是否启用基于目的网段的snat规则. [en] EIP snat enable while access dst cidr.'
            description: '[zh] 资源实际属性. [en] Real resource properties.'
        EIPResize:
            required:
                - resource_id
                - sku_id
                - operator_id
            type: object
            properties:
                resource_id:
                    type: string
                    description: '[zh] EIP 资源uuid. [en] EIP resource uuid.'
                sku_id:
                    type: string
                    description: '[zh] 最小库存单元id. [en] EIP resource sku_id.'
                operator_id:
                    type: string
                    description: '[zh] 变更操作者id. [en] Operator id.'
                properties:
                    $ref: '#/components/schemas/EIPProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
            description: '[zh] 变更 EIP 资源. [en] Resize EIP body.'
        EIPStatus:
            type: object
            properties:
                eip_info:
                    $ref: '#/components/schemas/EIP'
                eip_ip:
                    type: string
                    description: Resource ip
                vpc_info:
                    $ref: '#/components/schemas/VPCStatus'
                snat_count:
                    type: integer
                    description: snat 规则数量 [en] snat count.
                    format: int32
                dnat_count:
                    type: integer
                    description: dnat 规则数量 [en] dnat count.
                    format: int32
                snat_info:
                    type: array
                    items:
                        $ref: '#/components/schemas/SnatStatus'
                    description: '[zh] snat规则信息 [en] snat status info'
            description: 获取 EIP 属性的响应. [en] Response to get EIP status.
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        LimitRateItems:
            type: object
            properties:
                up_stream_bw:
                    type: integer
                    description: '[zh] EIP 上行带宽限制，单位: M/s. [en] eip upstream bandwidth limit, unit: M/s'
                    format: int32
                down_stream_bw:
                    type: integer
                    description: '[zh] EIP 下行带宽限制，单位: M/s [en] EIP downstream bandwidth limit, unit: M/s'
                    format: int32
            description: '[zh] 限速项 [en] rate limit items'
        ListEIPsResponse:
            type: object
            properties:
                eips:
                    type: array
                    items:
                        $ref: '#/components/schemas/EIP'
                    description: '[zh] EIP 列表. [en] EIP list.'
                next_page_token:
                    type: string
                    description: '[zh] 下一个页面的 token，如果没有更多数据则为空. [en] Token to retrieve the next page of results, or empty if there are no more results in the list.'
                total_size:
                    type: integer
                    description: '[zh] 返回的eip实例总数 [en] total size'
                    format: int32
            description: '[zh] 列举 EIPs 的响应. [en] Response to list EIPs.'
        OrderInfo:
            type: object
            properties:
                billing_cycle_number:
                    type: integer
                    description: '[zh] 购买时长. [en] length of purchase.'
                    format: int32
                auto_renew:
                    type: boolean
                    description: '[zh] 自动续费. [en] Automatic renewal.'
                currency_code:
                    type: string
                    description: '[zh] 货币代码. [en] currency code.'
                payment_channel:
                    type: integer
                    description: '[zh] 支付方式. [en] payment method.'
                    format: enum
                note:
                    type: string
                    description: '[zh] 订单备注. [en] order notes'
                order_type:
                    type: integer
                    description: '[zh] 订单类型. [en] Order Type.'
                    format: enum
                order_id:
                    type: string
                    description: '[zh] 订单id. [en] order id.'
                start_time:
                    type: string
                    description: '[zh] 订单生效日期. [en] Order effective date.'
                    format: date-time
                payment_model:
                    type: integer
                    description: '[zh] 付费类型. [en] payment type.'
                    format: enum
                billing_model:
                    type: integer
                    description: '[zh] 计费类型. [en] billing type.'
                    format: enum
                original_id:
                    type: string
                    description: '[zh] 合同包ID. [en] Contract package ID. original id-- OT_ORIGINAL: original spu_id/package_id; OT_RENEW/OT_UPGRADED/OT_DOWNGRADED: original/ order_id; OT_CONTRACT: original contract_id'
                end_time:
                    type: string
                    description: '[zh] 订单结束时间. [en] Order end time.'
                    format: date-time
            description: '[zh] 订单信息. [en] Order Infomation.'
        ResourceMetrics:
            type: object
            properties:
                name:
                    type: string
                    description: name.
                value:
                    type: integer
                    description: value.
                    format: int32
                unit:
                    type: string
                    description: unit.
                color:
                    type: string
                    description: color.
            description: ResourceMetrics
        Resources:
            type: object
            properties:
                billing_items:
                    $ref: '#/components/schemas/BillingItems'
                limit_rate_items:
                    $ref: '#/components/schemas/LimitRateItems'
            description: '[zh] 资源规格属性 [en] EIP Resources'
        SnatStatus:
            type: object
            properties:
                name:
                    type: string
                    description: '[zh] snat 名字 [en] snat name'
                inner_ip:
                    type: string
                    description: '[zh] snat 源地址cidr [en] snat inner cidr'
                outer_ip:
                    type: string
                    description: '[zh] snat 目的cidr [en] snat outer cidr'
                policy:
                    type: string
                    description: '[zh] snat policy策略, src/dst/all [en] snat policy'
                policy_value:
                    type: string
                    description: '[zh] snat policy值 [en] snat policy value'
            description: '[zh] snat status 信息 [en] snat status info'
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        VPCStatus:
            type: object
            properties:
                name:
                    type: string
                    description: vpc name.
                display_name:
                    type: string
                    description: vpc display name
                id:
                    type: string
                    description: vpc uid.
            description: vpc info.
tags:
    - name: EIPs
