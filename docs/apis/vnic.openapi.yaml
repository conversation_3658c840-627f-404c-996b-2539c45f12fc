# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: VNics API
    description: |-
        [zh] 虚拟网卡服务
         [en] VNics Service.
    version: 0.0.1
paths:
    /network/vnic/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/subnets/{subnet_name}:
        get:
            tags:
                - VNics
            description: |-
                [zh] 列举虚拟网卡资源详情.
                 [en] List details of VNic resources.
            operationId: VNics_ListVNics
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅. [en] Subscription.'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组. [en] Resource group.'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区. [en] Available zone.'
                  required: true
                  schema:
                    type: string
                - name: subnet_name
                  in: path
                  description: '[zh] 子网名. [en] The name of the subnet.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListVNicsResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/vnic/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vnics/{vnic_name}:
        get:
            tags:
                - VNics
            description: |-
                [zh] 查看虚拟网卡资源详情.
                 [en] Gets details of a VNic resource.
            operationId: VNics_GetVNic
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅. [en] Subscription.'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组. [en] Resource group.'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区. [en] Available zone.'
                  required: true
                  schema:
                    type: string
                - name: vnic_name
                  in: path
                  description: '[zh] 虚拟网卡资源名称. [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
                - name: subnet
                  in: query
                  description: '[zh] 子网名. [en] The name of the subnet.'
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VNic'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - VNics
            description: |-
                [zh] 创建虚拟网卡资源.
                 [en] Creates VNic resources.
            operationId: VNics_CreateVNic
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅. [en] Subscription.'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组. [en] Resource group.'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区. [en] Available zone.'
                  required: true
                  schema:
                    type: string
                - name: vnic_name
                  in: path
                  description: '[zh] 虚拟网卡资源名称. [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/VNicCreate'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VNic'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - VNics
            description: |-
                [zh] 删除虚拟网卡资源.
                 [en] Delete the VNic resources.
            operationId: VNics_DeleteVNic
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅. [en] Subscription.'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组. [en] Resource group.'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区. [en] Available zone.'
                  required: true
                  schema:
                    type: string
                - name: vnic_name
                  in: path
                  description: '[zh] 虚拟网卡资源名称. [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
                - name: subnet
                  in: query
                  description: '[zh] vnic 的 subnet 名. [en] The name of the subnet.'
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - VNics
            description: |-
                [zh] 更新虚拟网卡资源.
                 [en] Update the VNic resources.
            operationId: VNics_UpdateVNic
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅. [en] The resource subscription.'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组. [en] The resource group.'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区. [en] The resource available zone.'
                  required: true
                  schema:
                    type: string
                - name: vnic_name
                  in: path
                  description: '[zh] 资源名称. [en] The resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
                - name: update_mask
                  in: query
                  description: '[zh] 更新标记. [en] The resource update mask.'
                  schema:
                    type: string
                    format: field-mask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/VNicUpdate'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VNic'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/vnic/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vnics/{vnic_name}:release
    :   post:
            tags:
                - VNics
            description: |-
                [zh] 释放 (保留期释放).
                 [en] The Release of a VNic resources.
            operationId: VNics_ReleaseVNic
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅名. [en] Subscription.'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组. [en] Resource group.'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区. [en] Available zone.'
                  required: true
                  schema:
                    type: string
                - name: vnic_name
                  in: path
                  description: '[zh] 虚拟网卡资源名称. [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ReleaseVNicRequest'
                required: true
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/vnic/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vnics/{vnic_name}:resize
    :   post:
            tags:
                - VNics
            description: |-
                [zh] 扩缩容虚拟网卡资源.
                 [en] The Resize of a VNic resources.
            operationId: VNics_ResizeVNic
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅名. [en] Subscription.'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组. [en] Resource group.'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区. [en] Available zone.'
                  required: true
                  schema:
                    type: string
                - name: vnic_name
                  in: path
                  description: '[zh] 虚拟网卡资源名称. [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/VNicResize'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VNic'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        BillingItems:
            type: object
            properties: {}
            description: '[zh] 计费项 [en] The BillingItems in VNicProperties Resources.'
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        ListVNicsResponse:
            type: object
            properties:
                vnics:
                    type: array
                    items:
                        $ref: '#/components/schemas/VNic'
                    description: VPC 列表. [EN] VPC list.
                total_size:
                    type: integer
                    description: total size
                    format: int32
            description: 列举 VNIC 的响应. [EN] Response to list VNICs.
        OrderInfo:
            type: object
            properties:
                billing_cycle_number:
                    type: integer
                    description: '[zh] 购买时长. [en] length of purchase.'
                    format: int32
                auto_renew:
                    type: boolean
                    description: '[zh] 自动续费. [en] Automatic renewal.'
                currency_code:
                    type: string
                    description: '[zh] 货币代码. [en] currency code.'
                payment_channel:
                    type: integer
                    description: '[zh] 支付方式. [en] payment method.'
                    format: enum
                note:
                    type: string
                    description: '[zh] 订单备注. [en] order notes'
                order_type:
                    type: integer
                    description: '[zh] 订单类型. [en] Order Type.'
                    format: enum
                order_id:
                    type: string
                    description: '[zh] 订单id. [en] order id.'
                start_time:
                    type: string
                    description: '[zh] 订单生效日期. [en] Order effective date.'
                    format: date-time
                payment_model:
                    type: integer
                    description: '[zh] 付费类型. [en] payment type.'
                    format: enum
                billing_model:
                    type: integer
                    description: '[zh] 计费类型. [en] billing type.'
                    format: enum
                original_id:
                    type: string
                    description: '[zh] 合同包ID. [en] Contract package ID. original id-- OT_ORIGINAL: original spu_id/package_id; OT_RENEW/OT_UPGRADED/OT_DOWNGRADED: original/ order_id; OT_CONTRACT: original contract_id'
                end_time:
                    type: string
                    description: '[zh] 订单结束时间. [en] Order end time.'
                    format: date-time
            description: '[zh] 订单信息. [en] Order Infomation.'
        ReleaseVNicRequest:
            required:
                - subscription_name
                - resource_group_name
                - zone
                - vnic_name
            type: object
            properties:
                subscription_name:
                    type: string
                    description: '[zh] 订阅名. [en] Subscription.'
                resource_group_name:
                    type: string
                    description: '[zh] 资源组. [en] Resource group.'
                zone:
                    type: string
                    description: '[zh] 可用区. [en] Available zone.'
                vnic_name:
                    type: string
                    description: '[zh] 虚拟网卡资源名称. [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
            description: '[zh] 释放虚拟网卡资源. [en] Release VNic resource.'
        Resources:
            type: object
            properties:
                billing_items:
                    $ref: '#/components/schemas/BillingItems'
            description: '[zh] 资源规格属性 [en] Resource Specification Properties.'
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        VNic:
            required:
                - display_name
                - sku_id
            type: object
            properties:
                id:
                    readOnly: true
                    type: string
                    description: '[zh] 资源id. [en] The VNic resource id using the form:     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/vnics/{name}`.'
                uid:
                    readOnly: true
                    type: string
                    description: '[zh] 资源的uuid. [en] The VNic resource uuid.'
                name:
                    readOnly: true
                    type: string
                    description: '[zh] 资源标识. [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                display_name:
                    type: string
                    description: '[zh] 资源名称. [en] The VNic resource display name'
                description:
                    type: string
                    description: '[zh] 资源描述. [en] The VNic resource description.'
                resource_type:
                    type: string
                    description: '[zh] 资源类型. [en] The VNic resource type.'
                creator_id:
                    readOnly: true
                    type: string
                    description: '[zh] 创建者id. [en] The id of the user who created the VNic resource.'
                owner_id:
                    readOnly: true
                    type: string
                    description: '[zh] 拥有者id. [en] The id of the user who owns the VNic resource.'
                tenant_id:
                    readOnly: true
                    type: string
                    description: '[zh] 租户id [en] Tenant id.'
                zone:
                    readOnly: true
                    type: string
                    description: '[zh] 可用区. [en] Available zone.'
                state:
                    readOnly: true
                    type: integer
                    description: '[zh] 资源状态. [en] The current state of the VNic resource.'
                    format: enum
                sku_id:
                    type: string
                    description: '[zh] 最小库存单元id. [en] SKU id.'
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: '[zh] 虚拟网卡资源的标签. [en] Tags attached to the VirtualMachine resource.'
                properties:
                    $ref: '#/components/schemas/VNicProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
                deleted:
                    readOnly: true
                    type: boolean
                    description: '[zh] 资源是否已删除. [en] Indicates whether the VNic resource is deleted or not.'
                create_time:
                    readOnly: true
                    type: string
                    description: '[zh] 资源创建时间. [en] The time when the VNic resource was created.'
                    format: date-time
                update_time:
                    readOnly: true
                    type: string
                    description: '[zh] 资源更新时间. [en] The time when the VNic resource was last updated.'
                    format: date-time
            description: '[zh] 虚拟网卡资源. [en] The VNic resource.'
        VNicCreate:
            required:
                - subnet
            type: object
            properties:
                id:
                    readOnly: true
                    type: string
                    description: '[zh] 资源id. [en] The VNic resource id using the form:     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/vnics/{name}`.'
                subnet:
                    type: string
                    description: '[zh] vnic 的 subnet 名. [en] The name of the subnet.'
                name:
                    readOnly: true
                    type: string
                    description: '[zh] 资源标识. [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                description:
                    type: string
                    description: '[zh] 资源描述. [en] The VNic resource description.'
                resource_type:
                    type: string
                    description: '[zh] 资源类型. [en] The VNic resource type.'
                sku_id:
                    type: string
                    description: '[zh] 最小库存单元id. [en] SKU id.'
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: '[zh] 虚拟网卡资源的标签. [en] Tags attached to the VNic resource.'
                properties:
                    $ref: '#/components/schemas/VNicProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
            description: '[zh] 虚拟网卡资源. [en] The VNic resource.'
        VNicProperties:
            type: object
            properties:
                resources:
                    $ref: '#/components/schemas/Resources'
                vnic_id:
                    type: string
                    description: ID of this VNic
                name:
                    type: string
                    description: 'The VIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                ip_addr_v4:
                    type: string
                    description: The IPv4 address
                mac_addr:
                    type: string
                    description: The Mac address
                subnet:
                    type: string
                    description: The name of vnic subnet
                vpc:
                    type: string
                    description: The name of vip vpc
            description: '[zh] 资源属性. [en] The VNicProperties.'
        VNicResize:
            required:
                - resource_id
                - sku_id
                - operator_id
            type: object
            properties:
                resource_id:
                    type: string
                    description: '[zh] 虚拟网卡资源uuid. [en] VNic resource uuid.'
                sku_id:
                    type: string
                    description: '[zh] 虚拟网卡资源sku_id. [en] VNic resource sku_id.'
                operator_id:
                    type: string
                    description: '[zh] 变更操作者id. [en] operator id.'
                properties:
                    $ref: '#/components/schemas/VNicProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
            description: '[zh] 变更虚拟网卡资源. [en] Resize VNic resource.'
        VNicUpdate:
            required:
                - display_name
            type: object
            properties:
                display_name:
                    type: string
                    description: '[zh] 资源名称. [en] The resource display name with the restriction: `^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]?([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_]{0,62})$`.'
                description:
                    type: string
                    description: '[zh] 资源描述. [en] The resource Description.'
            description: '[zh] 更新虚拟网卡资源. [en] Update VNic body.'
tags:
    - name: VNics
