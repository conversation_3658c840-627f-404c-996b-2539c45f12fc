# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: QuotaService API
    description: |-
        [zh] Quota 服务
         [en] Service of Quota
    version: 0.0.1
paths:
    /network/quota/data/v1/quotas:
        get:
            tags:
                - QuotaService
            description: |-
                [zh] 列举产品的所有 Quotas.
                 [en] List requested Quotas.
            operationId: QuotaService_ListQuotas
            parameters:
                - name: filter
                  in: query
                  description: '[zh] 过滤条件 [en] List filter.'
                  schema:
                    type: string
                - name: order_by
                  in: query
                  description: '[zh] 排序规则 [en] Sort rules.'
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: '[zh] 分页大小 [en] The maximum number of items to return.'
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: '[zh] 从上一个List请求返回的next_page_token值(如果有的话) [en] The next_page_token value returned from a previous List request, if any.'
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListQuotasResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        ListQuotasResponse:
            type: object
            properties:
                Quotas:
                    type: object
                    additionalProperties:
                        $ref: '#/components/schemas/ListQuotasResponse_QuotaValue'
                    description: '[zh] Quotas 列表. [en] Quotas list.'
                next_page_token:
                    type: string
                    description: '[zh] 下一个页面的 token，如果没有更多数据则为空. [en] Token to retrieve the next page of results, or empty if there are no more results in the list.'
                total_size:
                    type: integer
                    description: '[zh] Quotas 总数 [en] total size'
                    format: int32
            description: '[zh] 列举 Quotas 的响应. [en] Response to list Quotas.'
        ListQuotasResponse_QuotaValue:
            type: object
            properties:
                values:
                    type: object
                    additionalProperties:
                        type: integer
                        format: int64
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
tags:
    - name: QuotaService
