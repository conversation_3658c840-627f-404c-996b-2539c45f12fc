# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: ACLs API
    description: |-
        [zh] ACL 服务，管理 AZ 内租户 ACL 相关网络资源.
         [en] Service of ACLs, for managing ACLs related network resources for tenants in AZ.
    version: 0.0.1
paths:
    /network/vpc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}/acls:
        get:
            tags:
                - ACLs
            description: |-
                [zh]列举VPC下所有 ACLs.
                 [en] List VPC ACLs.
            operationId: ACLs_ListACLs
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订购 [en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组 [en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区 [en] Available zone'
                  required: true
                  schema:
                    type: string
                - name: vpc_name
                  in: path
                  description: '[zh] vpc名称 [en] vpc name'
                  required: true
                  schema:
                    type: string
                - name: filter
                  in: query
                  description: '[zh] 过滤条件 [en] List filter.'
                  schema:
                    type: string
                - name: order_by
                  in: query
                  description: '[zh] 排序字段 [en] Sort results.'
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: '[zh] 分页大小 [en] The maximum number of items to return.'
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: '[zh] 分页标记 [en] The next_page_token value returned from a previous List request, if any.'
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListACLsResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/vpc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}/acls/{acl_name}
    :   get:
            tags:
                - ACLs
            description: |-
                [zh]获取符合请求的一个 ACL.
                 [en] Get an ACL.
            operationId: ACLs_GetACL
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订购 [en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组 [en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区 [en] Available zone'
                  required: true
                  schema:
                    type: string
                - name: vpc_name
                  in: path
                  description: '[zh] vpc名称 [en] vpc name'
                  required: true
                  schema:
                    type: string
                - name: acl_name
                  in: path
                  description: '[zh] ACL资源名称 [en] The ACL resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ACL'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - ACLs
            description: |-
                [zh]创建一个 ACL.
                 [en] Create an ACL.
            operationId: ACLs_CreateACL
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订购 [en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组 [en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区 [en] Available zone'
                  required: true
                  schema:
                    type: string
                - name: vpc_name
                  in: path
                  description: '[zh] vpc名称 [en] vpc name'
                  required: true
                  schema:
                    type: string
                - name: acl_name
                  in: path
                  description: '[zh] ACL资源名称 [en] The ACL resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ACL'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ACL'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - ACLs
            description: |-
                [zh]删除一个 ACL.
                 [en] Delete a ACL.
            operationId: ACLs_DeleteACL
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订购 [en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组 [en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区 [en] Available zone'
                  required: true
                  schema:
                    type: string
                - name: vpc_name
                  in: path
                  description: '[zh] vpc名称 [en] vpc name'
                  required: true
                  schema:
                    type: string
                - name: acl_name
                  in: path
                  description: '[zh] ACL资源名称 [en] The ACL resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - ACLs
            description: |-
                [zh]更新一个 ACL.
                 [en] update the ACL.
            operationId: ACLs_UpdateACL
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订购 [en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组 [en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区 [en] Available zone'
                  required: true
                  schema:
                    type: string
                - name: vpc_name
                  in: path
                  description: '[zh] vpc名称 [en] vpc name'
                  required: true
                  schema:
                    type: string
                - name: acl_name
                  in: path
                  description: '[zh] ACL资源名称 [en] The ACL resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
                - name: update_mask
                  in: query
                  description: update_mask
                  schema:
                    type: string
                    format: field-mask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/ACLUpdateProperties'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ACL'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        ACL:
            required:
                - id
                - zone
            type: object
            properties:
                id:
                    type: string
                    description: '[zh] ACL资源ID [en] The ACL resource id using the form: `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/acls/{acl_name}`.'
                name:
                    type: string
                    description: '[zh] ACL资源名称 [en] The ACL resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                display_name:
                    type: string
                    description: '[zh] ACL资源显示名称 [en] ContainerInstance resource display name'
                description:
                    type: string
                    description: '[zh] 资源描述信息 [en] ContainerInstance resource description'
                uid:
                    type: string
                    description: '[zh] ACL资源唯一标识 [en] The ACL resource uuid.'
                resource_type:
                    type: string
                    description: '[zh] ACL资源类型 [en] The ACL resource type.'
                creator_id:
                    type: string
                    description: '[zh] 创建者ID [en] The id of the user who created the ACL resource.'
                owner_id:
                    type: string
                    description: '[zh] 拥有者ID [en] The id of the user who owns the ACL resource.'
                tenant_id:
                    type: string
                    description: '[zh] 租户ID [en] Tenant id.'
                zone:
                    type: string
                    description: '[zh] 可用区 [en] Available zone.'
                state:
                    type: integer
                    description: '[zh] ACL资源当前状态 [en] The current state of the ACL resource.'
                    format: enum
                sku_id:
                    type: string
                    description: '[zh] sku标识 [en] Sku id.'
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: '[zh] 标签 [en] Tags attached to the ACL resource.'
                properties:
                    $ref: '#/components/schemas/ACLProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
                deleted:
                    type: boolean
                    description: '[zh] ACL资源是否已删除 [en] Indicates whether the ACL resource is deleted or not.'
                create_time:
                    type: string
                    description: '[zh] 创建时间 [en] The time when the ACL resource was created.'
                    format: date-time
                update_time:
                    type: string
                    description: '[zh] 更新时间 [en] The time when the ACL resource was last updated.'
                    format: date-time
            description: '[zh] ACL 实例结构体. [en] ACL entity.'
        ACLProperties:
            type: object
            properties:
                action:
                    type: integer
                    description: '[zh] ACL动作. [en] ACL action.'
                    format: enum
                src:
                    type: string
                    description: '[zh] ACL源地址. [en] ACL source addresses, support multi IPs,IP range or Subnet: ********,********,*********-*********,********/27.'
                src_port:
                    type: string
                    description: '[zh] ACL源端口. [en] ACL source port, support multi ports: 80,443,8080.'
                dest:
                    type: string
                    description: '[zh] ACL目的地址. [en] ACL destination addresses.'
                dest_port:
                    type: string
                    description: '[zh] ACL目的端口. [en] ACL destination port.'
                protocol:
                    type: string
                    description: '[zh] 对外暴露的协议. [en] External protocol, tcp，udp，icmp，all，default all.'
                priority:
                    type: integer
                    description: '[zh] ACL规则优先级. [en] ACL rule priority.'
                    format: int32
                type:
                    type: integer
                    description: '[zh] ACL类型. [en] ACL type.'
                    format: enum
                extra_data:
                    type: string
                    description: '[zh] ACL规则其他属性，目前保留. [en] ACL rule extra_data , reserved now.'
            description: '[zh] 资源实际属性. [en] Real resource properties.'
        ACLUpdateProperties:
            type: object
            properties:
                display_name:
                    type: string
                    description: '[zh] ACL资源显示名称 [en] ContainerInstance resource display name'
                description:
                    type: string
                    description: '[zh] 资源描述信息 [en] ContainerInstance resource description'
            description: '[zh] 资源可更新属性. [en] resource properties to update.'
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        ListACLsResponse:
            type: object
            properties:
                acls:
                    type: array
                    items:
                        $ref: '#/components/schemas/ACL'
                    description: '[zh] ACL 列表. [en] ACL list.'
                next_page_token:
                    type: string
                    description: '[zh] 下一个页面的 token，如果没有更多数据则为空. [en] Token to retrieve the next page of results, or empty if there are no more results in the list.'
                total_size:
                    type: integer
                    description: '[zh] 分页大小 [en] total size'
                    format: int32
            description: '[zh] 列举 ACLs 的响应. [en] Response to list ACLs.'
        OrderInfo:
            type: object
            properties:
                billing_cycle_number:
                    type: integer
                    description: '[zh] 购买时长. [en] length of purchase.'
                    format: int32
                auto_renew:
                    type: boolean
                    description: '[zh] 自动续费. [en] Automatic renewal.'
                currency_code:
                    type: string
                    description: '[zh] 货币代码. [en] currency code.'
                payment_channel:
                    type: integer
                    description: '[zh] 支付方式. [en] payment method.'
                    format: enum
                note:
                    type: string
                    description: '[zh] 订单备注. [en] order notes'
                order_type:
                    type: integer
                    description: '[zh] 订单类型. [en] Order Type.'
                    format: enum
                order_id:
                    type: string
                    description: '[zh] 订单id. [en] order id.'
                start_time:
                    type: string
                    description: '[zh] 订单生效日期. [en] Order effective date.'
                    format: date-time
                payment_model:
                    type: integer
                    description: '[zh] 付费类型. [en] payment type.'
                    format: enum
                billing_model:
                    type: integer
                    description: '[zh] 计费类型. [en] billing type.'
                    format: enum
                original_id:
                    type: string
                    description: '[zh] 合同包ID. [en] Contract package ID. original id-- OT_ORIGINAL: original spu_id/package_id; OT_RENEW/OT_UPGRADED/OT_DOWNGRADED: original/ order_id; OT_CONTRACT: original contract_id'
                end_time:
                    type: string
                    description: '[zh] 订单结束时间. [en] Order end time.'
                    format: date-time
            description: '[zh] 订单信息. [en] Order Infomation.'
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
tags:
    - name: ACLs
