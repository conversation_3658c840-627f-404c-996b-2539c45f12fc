# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: Subnets API
    description: |-
        Subnet 服务，管理 AZ 内租户 Subnet 相关网络资源.
         [EN] Service of Subnet, for managing Subnet related network resources for tenants in AZ.
    version: 0.0.1
paths:
    /network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/subnets:
        get:
            tags:
                - Subnets
            description: |-
                列举符合请求的所有 Subnets.
                 [EN] List requested Subnets.
            operationId: Subnets_ListSubnets
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: filter
                  in: query
                  description: List filter.
                  schema:
                    type: string
                - name: order_by
                  in: query
                  description: Sort resoults.
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: The maximum number of items to return.
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: The next_page_token value returned from a previous List request, if any.
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListSubnetsResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/subnets/{subnet_name}:
        get:
            tags:
                - Subnets
            description: |-
                获取符合请求的一个 Subnet.
                 [EN] Get a requested Subnet.
            operationId: Subnets_GetSubnet
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: subnet_name
                  in: path
                  description: 'The Subnet resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Subnet'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - Subnets
            description: |-
                创建一个 Subnet.
                 [EN] Create a Subnet.
            operationId: Subnets_CreateSubnet
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: 'Available zone, todo: add validation'
                  required: true
                  schema:
                    type: string
                - name: subnet_name
                  in: path
                  description: 'The Subnet resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Subnet'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Subnet'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - Subnets
            description: |-
                删除一个 Subnet.
                 [EN] Delete a Subnet.
            operationId: Subnets_DeleteSubnet
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: subnet_name
                  in: path
                  description: 'The Subnet resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - Subnets
            description: |-
                更新 Subnet 可编辑字段.
                 [EN] Update Subnet editable properties.
            operationId: Subnets_UpdateSubnet
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: subnet_name
                  in: path
                  description: 'The Subnet resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/Subnet'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Subnet'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        ListSubnetsResponse:
            type: object
            properties:
                Subnets:
                    type: array
                    items:
                        $ref: '#/components/schemas/Subnet'
                    description: Subnet 列表. [EN] Subnet list.
                next_page_token:
                    type: string
                    description: 下一个页面的 token，如果没有更多数据则为空. [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
                total_size:
                    type: integer
                    description: total size
                    format: int32
            description: 列举 Subnets 的响应. [EN] Response to list Subnets.
        OrderInfo:
            type: object
            properties:
                billing_cycle_number:
                    type: integer
                    description: '[zh] 购买时长. [en] length of purchase.'
                    format: int32
                auto_renew:
                    type: boolean
                    description: '[zh] 自动续费. [en] Automatic renewal.'
                currency_code:
                    type: string
                    description: '[zh] 货币代码. [en] currency code.'
                payment_channel:
                    type: integer
                    description: '[zh] 支付方式. [en] payment method.'
                    format: enum
                note:
                    type: string
                    description: '[zh] 订单备注. [en] order notes'
                order_type:
                    type: integer
                    description: '[zh] 订单类型. [en] Order Type.'
                    format: enum
                order_id:
                    type: string
                    description: '[zh] 订单id. [en] order id.'
                start_time:
                    type: string
                    description: '[zh] 订单生效日期. [en] Order effective date.'
                    format: date-time
                payment_model:
                    type: integer
                    description: '[zh] 付费类型. [en] payment type.'
                    format: enum
                billing_model:
                    type: integer
                    description: '[zh] 计费类型. [en] billing type.'
                    format: enum
                original_id:
                    type: string
                    description: '[zh] 合同包ID. [en] Contract package ID. original id-- OT_ORIGINAL: original spu_id/package_id; OT_RENEW/OT_UPGRADED/OT_DOWNGRADED: original/ order_id; OT_CONTRACT: original contract_id'
                end_time:
                    type: string
                    description: '[zh] 订单结束时间. [en] Order end time.'
                    format: date-time
            description: '[zh] 订单信息. [en] Order Infomation.'
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        Subnet:
            type: object
            properties:
                id:
                    type: string
                    description: 'The Subnet resource id using the form:     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/subnets/{subnet_name}`.'
                name:
                    type: string
                    description: 'The Subnet resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                display_name:
                    type: string
                    description: ContainerInstance resource display name.
                description:
                    type: string
                    description: ContainerInstance resource description.
                uid:
                    type: string
                    description: The Subnet resource uuid.
                resource_type:
                    type: string
                    description: The Subnet resource type.
                creator_id:
                    type: string
                    description: The id of the user who created the Subnet resource.
                owner_id:
                    type: string
                    description: The id of the user who owns the Subnet resource.
                tenant_id:
                    type: string
                    description: Tenant id.
                zone:
                    type: string
                    description: Available zone.
                state:
                    type: integer
                    description: The current state of the Subnet resource.
                    format: enum
                sku_id:
                    type: string
                    description: Sku id.
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: Tags attached to the Subnet resource.
                properties:
                    $ref: '#/components/schemas/SubnetProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
                deleted:
                    type: boolean
                    description: Indicates whether the Subnet resource is deleted or not.
                create_time:
                    type: string
                    description: The time when the Subnet resource was created.
                    format: date-time
                update_time:
                    type: string
                    description: The time when the Subnet resource was last updated.
                    format: date-time
            description: Subnet 实例结构体. [EN] Subnet entity.
        SubnetProperties:
            type: object
            properties:
                scope:
                    type: integer
                    description: 当前子网范围 [EN] Subnet scope.
                    format: enum
                provider:
                    type: integer
                    description: 当前子网提供者 [EN] Subnet provider.
                    format: enum
                network_type:
                    type: integer
                    description: 当前子网的网络类型 [EN] Subnet network type.
                    format: enum
                vpc_id:
                    type: string
                    description: 关联的 VPC uid [EN] Subnet VPC uid.
                cidr:
                    type: string
                    description: 当前子网的CIDR [EN] Subnet CIDR.
                reserved_ips:
                    type: array
                    items:
                        type: string
                    description: 当前子网预留不能使用的IP地址，x.x.x.1..x.x.x.9, x.x.x.250..x.x.x.255 地址默认不做分配 [EN] Subnet reserved IPs.
                is_default:
                    type: boolean
                    description: 是否默认子网 [EN] Is default subnet.
                gateway_ip:
                    type: string
                    description: 关联的网关IP地址 [EN] Subnet gateway IP.
                cidr_pool_id:
                    type: string
                    description: 关联的CIDR资源池 [EN] CIDR pool related to the subnet.
            description: 资源实际属性. [EN] Real resource properties.
tags:
    - name: Subnets
