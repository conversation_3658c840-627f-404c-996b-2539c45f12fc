# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: VPCs API
    description: |-
        VPC 服务，管理 AZ 内租户 VPC 相关网络资源.
         [EN] Service of VPC, for managing VPC related network resources for tenants in AZ.
    version: 0.0.1
paths:
    ? /network/vpc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}/rdma_clusters
    :   get:
            tags:
                - VPCs
            description: |-
                [zh] 获取VPC内RDMA集群信息.
                 [en] get rdma cluster infor in vpc.
            operationId: VPCs_GetVPCRDMAStatus
            parameters:
                - name: subscription_name
                  in: path
                  description: '[zh] 订阅 [en] Subscription'
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: '[zh] 资源组 [en] Resource group'
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: '[zh] 可用区 [en] Available zone'
                  required: true
                  schema:
                    type: string
                - name: vpc_name
                  in: path
                  description: '[zh] vpc名 [en] The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VPCRDMAClustersStatus'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/vpc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}/status:
        get:
            tags:
                - VPCs
            description: |-
                获取符合请求的一个 VPC 相关属性.
                 [EN] Get a requested VPC status.
            operationId: VPCs_GetVPCDataStatus
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: vpc_name
                  in: path
                  description: 'The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VPCStatus'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/vpc/data/v1/vpcs/metrics:
        get:
            tags:
                - VPCs
            description: |-
                VPC metrics.
                 [EN] VPC metrics.
            operationId: VPCs_GetVPCMetrics
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VPCMetrics'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs:
        get:
            tags:
                - VPCs
            description: |-
                列举符合请求的所有 VPCs.
                 [EN] List requested VPCs.
            operationId: VPCs_ListVPCs
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: filter
                  in: query
                  description: List filter.
                  schema:
                    type: string
                - name: order_by
                  in: query
                  description: Sort resoults.
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: The maximum number of items to return.
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: The next_page_token value returned from a previous List request, if any.
                  schema:
                    type: string
                - name: tenant_id
                  in: query
                  description: tenant_id.
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListVPCsResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}:
        get:
            tags:
                - VPCs
            description: |-
                获取符合请求的一个 VPC.
                 [EN] Get a requested VPC.
            operationId: VPCs_GetVPC
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: vpc_name
                  in: path
                  description: 'The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VPC'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - VPCs
            description: |-
                创建一个 VPC.
                 [EN] Create a VPC.
            operationId: VPCs_CreateVPC
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: 'Available zone, todo: add validation'
                  required: true
                  schema:
                    type: string
                - name: vpc_name
                  in: path
                  description: 'The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/VPC'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VPC'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - VPCs
            description: |-
                删除一个 VPC.
                 [EN] Delete a VPC.
            operationId: VPCs_DeleteVPC
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: vpc_name
                  in: path
                  description: 'The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - VPCs
            description: |-
                更新 VPC 可编辑字段.
                 [EN] Update VPC editable properties.
            operationId: VPCs_UpdateVPC
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: vpc_name
                  in: path
                  description: 'The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
                - name: update_mask
                  in: query
                  description: update_mask
                  schema:
                    type: string
                    format: field-mask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/VPCUpdateProperties'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VPC'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    ? /network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}/dgws/{dgw_name}
    :   patch:
            tags:
                - VPCs
            description: |-
                更新 VPCDGW 可编辑字段.
                 [EN] Update VPCDGW editable properties.
            operationId: VPCs_UpdateVPCDgw
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: vpc_name
                  in: path
                  description: 'The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
                - name: dgw_name
                  in: path
                  description: 'The dgw resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
                - name: update_mask
                  in: query
                  description: update_mask
                  schema:
                    type: string
                    format: field-mask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DGWGateway'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DGWGateway'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/vpc/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}/status:
        get:
            tags:
                - VPCs
            description: |-
                获取符合请求的一个 VPC 相关属性.
                 [EN] Get a requested VPC status.
            operationId: VPCs_GetVPCStatus
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Available zone
                  required: true
                  schema:
                    type: string
                - name: vpc_name
                  in: path
                  description: 'The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/VPCStatus'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        ClusterStatus:
            type: object
            properties:
                name:
                    type: string
                    description: name.
                protocol:
                    type: integer
                    description: protocol. IB，RoCE，None
                    format: enum
                networkInfos:
                    type: array
                    items:
                        $ref: '#/components/schemas/NetworkInfo'
                    description: network detail information
                properties:
                    type: object
                    additionalProperties:
                        type: string
                    description: extend config, such as "sharp_enable":"true"
            description: cluster_status
        DGWGateway:
            type: object
            properties:
                id:
                    type: string
                    description: 'The dgw resource id using the form:     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}/dgws/{name}`.'
                name:
                    type: string
                    description: 'The dgw resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                display_name:
                    type: string
                    description: ContainerInstance resource display name
                description:
                    type: string
                    description: ContainerInstance resource description
                uid:
                    type: string
                    description: The dgw resource uuid.
                resource_type:
                    type: string
                    description: The dgw resource type.
                creator_id:
                    type: string
                    description: The id of the user who created the VPC dgw resource.
                owner_id:
                    type: string
                    description: The id of the user who owns the VPC dgw resource.
                tenant_id:
                    type: string
                    description: Tenant id.
                zone:
                    type: string
                    description: Available zone.
                state:
                    type: integer
                    description: The current state of the VPC dgw resource.
                    format: enum
                admin_state:
                    type: integer
                    description: the current admin status
                    format: enum
                gw_external_ip:
                    type: string
                    description: cidr/prefix模式,200.64.xx.xx
                gw_policy:
                    type: string
                    description: 以，为连接的目标网段列表
                internal_ip:
                    type: string
                    description: vpc overlay ip, 10.119.xxx.xxx
                deleted:
                    type: boolean
                    description: Indicates whether the VPC resource is deleted or not.
                create_time:
                    type: string
                    description: The time when the VPC resource was created.
                    format: date-time
                update_time:
                    type: string
                    description: The time when the VPC resource was last updated.
                    format: date-time
            description: DGWGateway 实例结构体. [EN] DGWGateway entity.
        DGWGatewayStatus:
            type: object
            properties:
                name:
                    type: string
                    description: Gateway name.
                admin_state:
                    type: integer
                    description: admin enable
                    format: enum
                ip:
                    type: string
                    description: Gateway ip.
                policy:
                    type: string
                    description: dest subnet
                id:
                    type: string
                    description: Gateway uid.
                state:
                    type: integer
                    description: Gateway state
                    format: enum
            description: vpc status distribute Gateway info.
        EIPStatus:
            type: object
            properties:
                name:
                    type: string
                    description: eip name.
                ip:
                    type: string
                    description: eip ip.
                id:
                    type: string
                    description: eip uid.
                default_snat:
                    type: boolean
                    description: The default snat.
                association_type:
                    type: integer
                    description: Related device type，NATGW,POD,SLB,BM.
                    format: enum
                association_id:
                    type: string
                    description: Related device or item id, like vpc nat gateway id.
            description: vpc status eip info.
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        ListVPCsResponse:
            type: object
            properties:
                vpcs:
                    type: array
                    items:
                        $ref: '#/components/schemas/VPC'
                    description: VPC 列表. [EN] VPC list.
                next_page_token:
                    type: string
                    description: 下一个页面的 token，如果没有更多数据则为空. [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
                total_size:
                    type: integer
                    description: total size
                    format: int32
            description: 列举 VPCs 的响应. [EN] Response to list VPCs.
        NATGatewayStatus:
            type: object
            properties:
                name:
                    type: string
                    description: natGateway name.
                ip:
                    type: string
                    description: natGateway ip.
                id:
                    type: string
                    description: natGateway uid.
            description: vpc status natGateway info.
        NamespaceStatus:
            type: object
            properties:
                name:
                    type: string
                    description: namespace name.
                subnet:
                    type: string
                    description: subnet name
            description: vpc status namespace info.
        NetworkInfo:
            type: object
            properties:
                nic_count:
                    type: integer
                    description: nic_count. such as 1,2,4,8,16
                    format: int32
                bandwidth:
                    type: string
                    description: bandwidth.
                node_count:
                    type: integer
                    description: nodes count
                    format: int32
        OrderInfo:
            type: object
            properties:
                billing_cycle_number:
                    type: integer
                    description: '[zh] 购买时长. [en] length of purchase.'
                    format: int32
                auto_renew:
                    type: boolean
                    description: '[zh] 自动续费. [en] Automatic renewal.'
                currency_code:
                    type: string
                    description: '[zh] 货币代码. [en] currency code.'
                payment_channel:
                    type: integer
                    description: '[zh] 支付方式. [en] payment method.'
                    format: enum
                note:
                    type: string
                    description: '[zh] 订单备注. [en] order notes'
                order_type:
                    type: integer
                    description: '[zh] 订单类型. [en] Order Type.'
                    format: enum
                order_id:
                    type: string
                    description: '[zh] 订单id. [en] order id.'
                start_time:
                    type: string
                    description: '[zh] 订单生效日期. [en] Order effective date.'
                    format: date-time
                payment_model:
                    type: integer
                    description: '[zh] 付费类型. [en] payment type.'
                    format: enum
                billing_model:
                    type: integer
                    description: '[zh] 计费类型. [en] billing type.'
                    format: enum
                original_id:
                    type: string
                    description: '[zh] 合同包ID. [en] Contract package ID. original id-- OT_ORIGINAL: original spu_id/package_id; OT_RENEW/OT_UPGRADED/OT_DOWNGRADED: original/ order_id; OT_CONTRACT: original contract_id'
                end_time:
                    type: string
                    description: '[zh] 订单结束时间. [en] Order end time.'
                    format: date-time
            description: '[zh] 订单信息. [en] Order Infomation.'
        ResourceMetrics:
            type: object
            properties:
                name:
                    type: string
                    description: name.
                value:
                    type: integer
                    description: value.
                    format: int32
                unit:
                    type: string
                    description: unit.
                color:
                    type: string
                    description: color.
            description: ResourceMetrics
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
        SubnetStatus:
            type: object
            properties:
                name:
                    type: string
                    description: subnet name.
                cidr:
                    type: string
                    description: subnet cidr.
                id:
                    type: string
                    description: subnet uid.
                scope:
                    type: string
                    description: subnet scope.
                provider:
                    type: string
                    description: subnet provider.
                network_type:
                    type: string
                    description: subnet network type.
            description: vpc status subnet info.
        VPC:
            type: object
            properties:
                id:
                    type: string
                    description: 'The VPC resource id using the form:     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}`.'
                name:
                    type: string
                    description: 'The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                display_name:
                    type: string
                    description: ContainerInstance resource display name
                description:
                    type: string
                    description: ContainerInstance resource description
                uid:
                    type: string
                    description: The VPC resource uuid.
                resource_type:
                    type: string
                    description: The VPC resource type.
                creator_id:
                    type: string
                    description: The id of the user who created the VPC resource.
                owner_id:
                    type: string
                    description: The id of the user who owns the VPC resource.
                tenant_id:
                    type: string
                    description: Tenant id.
                zone:
                    type: string
                    description: Available zone.
                state:
                    type: integer
                    description: The current state of the VPC resource.
                    format: enum
                sku_id:
                    type: string
                    description: Sku id.
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: Tags attached to the VPC resource.
                properties:
                    $ref: '#/components/schemas/VPCProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
                deleted:
                    type: boolean
                    description: Indicates whether the VPC resource is deleted or not.
                create_time:
                    type: string
                    description: The time when the VPC resource was created.
                    format: date-time
                update_time:
                    type: string
                    description: The time when the VPC resource was last updated.
                    format: date-time
            description: VPC 实例结构体. [EN] VPC entity.
        VPCMetrics:
            type: object
            properties:
                resource_metrics:
                    type: array
                    items:
                        $ref: '#/components/schemas/ResourceMetrics'
                    description: resource_metrics.
            description: VPC 相关 metrics. [EN] VPC metrics.
        VPCProperties:
            type: object
            properties:
                cidr:
                    type: string
                    description: VPC 级别最大的 cidr, subnet 的 cidr 应该为 VPC cidr 的子集. [EN] VPC level cidr, subnet cidrs should be the subset of the VPC cidr.
                is_default:
                    type: boolean
                    description: 默认 VPC. [EN] Default VPC or not.
                subnet_type:
                    type: integer
                    description: the VPC resource subnets' type
                    format: enum
            description: 资源实际属性. [EN] Real resource properties.
        VPCRDMAClustersStatus:
            type: object
            properties:
                clusters_status:
                    type: array
                    items:
                        $ref: '#/components/schemas/ClusterStatus'
                    description: cluster_status
        VPCStatus:
            type: object
            properties:
                cidr:
                    type: string
                    description: vpc cidr.
                namespaces:
                    type: array
                    items:
                        $ref: '#/components/schemas/NamespaceStatus'
                    description: namespaces info
                subnets:
                    type: array
                    items:
                        $ref: '#/components/schemas/SubnetStatus'
                    description: subnets info
                nat_gateways:
                    type: array
                    items:
                        $ref: '#/components/schemas/NATGatewayStatus'
                    description: natGateway info
                eips:
                    type: array
                    items:
                        $ref: '#/components/schemas/EIPStatus'
                    description: eip info
                dgw_gateways:
                    type: array
                    items:
                        $ref: '#/components/schemas/DGWGatewayStatus'
                    description: distribute Gateway info
            description: 返回 VPC 相关属性的响应. [EN] Response to vpc status.
        VPCUpdateProperties:
            type: object
            properties:
                display_name:
                    type: string
                    description: ContainerInstance resource display name
                description:
                    type: string
                    description: ContainerInstance resource description
                subnet_type:
                    type: integer
                    description: the VPC resource subnets' type
                    format: enum
tags:
    - name: VPCs
