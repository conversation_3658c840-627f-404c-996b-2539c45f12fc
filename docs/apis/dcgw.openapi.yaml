# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: DCGWs API
    description: |-
        DCGW 服务，管理 AZ 内租户 DCGW 相关网络资源.
         [EN] Service of DCGW, for managing DCGW related network resources for tenants in AZ.
    version: 0.0.1
paths:
    /network/dc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcgws:
        get:
            tags:
                - DCGWs
            description: |-
                列举符合请求的所有 DCGWs.
                 [EN] List requested DCGWs.
            operationId: DCGWs_ListDCGWs
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Zone
                  required: true
                  schema:
                    type: string
                - name: filter
                  in: query
                  description: List filter.
                  schema:
                    type: string
                - name: order_by
                  in: query
                  description: Sort resoults.
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: The maximum number of items to return.
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: The next_page_token value returned from a previous List request, if any.
                  schema:
                    type: string
                - name: tenant_id
                  in: query
                  description: tenant_id.
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListDCGWsResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/dc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcgws/{dcgw_name}:
        get:
            tags:
                - DCGWs
            description: |-
                获取符合请求的一个 DCGW.
                 [EN] Get a requested DCGW.
            operationId: DCGWs_GetDCGW
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Zone
                  required: true
                  schema:
                    type: string
                - name: dcgw_name
                  in: path
                  description: 'The DCGW resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DCGW'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - DCGWs
            description: |-
                创建一个 DCGW.
                 [EN] Create a DCGW.a
            operationId: DCGWs_CreateDCGW
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: 'Zone, todo: add validation'
                  required: true
                  schema:
                    type: string
                - name: dcgw_name
                  in: path
                  description: 'The DCGW resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DCGW'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DCGW'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - DCGWs
            description: |-
                删除一个 DCGW.
                 [EN] Delete a DCGW.
            operationId: DCGWs_DeleteDCGW
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Zone
                  required: true
                  schema:
                    type: string
                - name: dcgw_name
                  in: path
                  description: 'The DCGW resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - DCGWs
            description: |-
                更新 DCGW 可编辑字段.
                 [EN] Update DCGW editable properties.
            operationId: DCGWs_UpdateDCGW
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Zone
                  required: true
                  schema:
                    type: string
                - name: dcgw_name
                  in: path
                  description: 'The DCGW resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
                - name: update_mask
                  in: query
                  description: update_mask
                  schema:
                    type: string
                    format: field-mask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DCGWUpdateProperties'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DCGW'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        DCGW:
            type: object
            properties:
                id:
                    type: string
                    description: The DCGW resource id
                name:
                    type: string
                    description: 'The DCGW resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                display_name:
                    type: string
                    description: resource display name
                description:
                    type: string
                    description: ContainerInstance resource description
                uid:
                    type: string
                    description: The DCGW resource uuid.
                resource_type:
                    type: string
                    description: The DCGW resource type.
                creator_id:
                    type: string
                    description: The id of the user who created the DCGW resource.
                owner_id:
                    type: string
                    description: The id of the user who owns the DCGW resource.
                tenant_id:
                    type: string
                    description: Tenant id.
                zone:
                    type: string
                    description: Zone.
                state:
                    type: integer
                    description: The current state of the DCGW resource.
                    format: enum
                sku_id:
                    type: string
                    description: Sku id.
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: Tags attached to the DCGW resource.
                properties:
                    $ref: '#/components/schemas/DCGWProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
                deleted:
                    type: boolean
                    description: Indicates whether the DCGW resource is deleted or not.
                create_time:
                    type: string
                    description: The time when the DCGW resource was created.
                    format: date-time
                update_time:
                    type: string
                    description: The time when the DCGW resource was last updated.
                    format: date-time
            description: DCGW 实例结构体. [EN] DCGW entity.
        DCGWProperties:
            type: object
            properties:
                vpc_id:
                    type: string
                    description: VPC uid. [EN] VPC uid.
                ha_mode:
                    type: string
                    description: '[zh] 专线网关高可用模式. [en] ha mode of dcgw'
                deploy_mode:
                    type: string
                    description: '[zh] 专线网关部署模式. [en] deploy mode of dcgw.'
                internal_vip:
                    type: string
                    description: '[zh] 专线网关内部的 vip. [en] internal vip of dcgw.'
                monitor_ip:
                    type: string
                    description: '[zh] monitor 监听地址 [en] monitor ip of dcgw.'
                heartbeat_nic:
                    type: string
                    description: '[zh] heartbeat 监听在哪个网卡 [en] heartbeat nic ip of dcgw.'
                external_vip:
                    type: string
                    description: '[zh] 专线网关外部的 vip. [en] external vip of dcgw'
                local_cidr:
                    type: array
                    items:
                        type: string
                    description: '[zh]  专线网关本端的 CIDR. [en] local cidr of dcgw.'
                remote_cidr:
                    type: array
                    items:
                        type: string
                    description: '[zh] 专线网关远端的 CIDR. [en] remote cidr of dcgw.'
                dcvcs:
                    type: array
                    items:
                        $ref: '#/components/schemas/DCVCElement'
                    description: '[zh] 专线网关关联的虚拟通道. [en] dcvcs of dcgw.'
                subscription_name:
                    readOnly: true
                    type: string
                    description: '[zh] 专线网关的 subscription name. [en] subscription name of dcgw'
                resource_group_name:
                    readOnly: true
                    type: string
                    description: '[zh] 专线网关的 resource group name. [en] resource group name of dcgw'
            description: 资源实际属性. [EN] Real resource properties.
        DCGWUpdateProperties:
            type: object
            properties:
                display_name:
                    type: string
                    description: resource display name
                description:
                    type: string
                    description: ContainerInstance resource description
                properties:
                    $ref: '#/components/schemas/DCGWProperties'
            description: '[zh] 更新DCGW资源. [en] Update DCGW properties.'
        DCVCElement:
            type: object
            properties:
                name:
                    type: string
                    description: 'The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                display_name:
                    type: string
                    description: resource display name
            description: 虚拟通道. [EN] DCVC Element.
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        ListDCGWsResponse:
            type: object
            properties:
                dcgws:
                    type: array
                    items:
                        $ref: '#/components/schemas/DCGW'
                    description: DCGW 列表. [EN] DCGW list.
                next_page_token:
                    type: string
                    description: 下一个页面的 token，如果没有更多数据则为空. [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
                total_size:
                    type: integer
                    description: total size
                    format: int32
            description: 列举 DCGWs 的响应. [EN] Response to list DCGWs.
        OrderInfo:
            type: object
            properties:
                billing_cycle_number:
                    type: integer
                    description: '[zh] 购买时长. [en] length of purchase.'
                    format: int32
                auto_renew:
                    type: boolean
                    description: '[zh] 自动续费. [en] Automatic renewal.'
                currency_code:
                    type: string
                    description: '[zh] 货币代码. [en] currency code.'
                payment_channel:
                    type: integer
                    description: '[zh] 支付方式. [en] payment method.'
                    format: enum
                note:
                    type: string
                    description: '[zh] 订单备注. [en] order notes'
                order_type:
                    type: integer
                    description: '[zh] 订单类型. [en] Order Type.'
                    format: enum
                order_id:
                    type: string
                    description: '[zh] 订单id. [en] order id.'
                start_time:
                    type: string
                    description: '[zh] 订单生效日期. [en] Order effective date.'
                    format: date-time
                payment_model:
                    type: integer
                    description: '[zh] 付费类型. [en] payment type.'
                    format: enum
                billing_model:
                    type: integer
                    description: '[zh] 计费类型. [en] billing type.'
                    format: enum
                original_id:
                    type: string
                    description: '[zh] 合同包ID. [en] Contract package ID. original id-- OT_ORIGINAL: original spu_id/package_id; OT_RENEW/OT_UPGRADED/OT_DOWNGRADED: original/ order_id; OT_CONTRACT: original contract_id'
                end_time:
                    type: string
                    description: '[zh] 订单结束时间. [en] Order end time.'
                    format: date-time
            description: '[zh] 订单信息. [en] Order Infomation.'
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
tags:
    - name: DCGWs
