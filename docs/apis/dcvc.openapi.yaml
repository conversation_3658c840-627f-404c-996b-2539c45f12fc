# Generated with protoc-gen-openapi
# https://github.com/google/gnostic/tree/master/cmd/protoc-gen-openapi

openapi: 3.0.3
info:
    title: DCVCs API
    description: |-
        DCVC 服务，管理 AZ 内租户 DCVC 相关网络资源.
         [EN] Service of DCVC, for managing DCVC related network resources for tenants in AZ.
    version: 0.0.1
paths:
    /network/dc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcvcs:
        get:
            tags:
                - DCVCs
            description: |-
                列举符合请求的所有 DCVCs.
                 [EN] List requested DCVCs.
            operationId: DCVCs_ListDCVCs
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Zone
                  required: true
                  schema:
                    type: string
                - name: filter
                  in: query
                  description: List filter.
                  schema:
                    type: string
                - name: order_by
                  in: query
                  description: Sort resoults.
                  schema:
                    type: string
                - name: page_size
                  in: query
                  description: The maximum number of items to return.
                  schema:
                    type: integer
                    format: int32
                - name: page_token
                  in: query
                  description: The next_page_token value returned from a previous List request, if any.
                  schema:
                    type: string
                - name: tenant_id
                  in: query
                  description: tenant_id.
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/ListDCVCsResponse'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
    /network/dc/data/v1/subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/dcvcs/{dcvc_name}:
        get:
            tags:
                - DCVCs
            description: |-
                获取符合请求的一个 DCVC.
                 [EN] Get a requested DCVC.
            operationId: DCVCs_GetDCVC
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Zone
                  required: true
                  schema:
                    type: string
                - name: dcvc_name
                  in: path
                  description: 'The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DCVC'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        post:
            tags:
                - DCVCs
            description: |-
                创建一个 DCVC.
                 [EN] Create a DCVC.a
            operationId: DCVCs_CreateDCVC
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: 'Zone, todo: add validation'
                  required: true
                  schema:
                    type: string
                - name: dcvc_name
                  in: path
                  description: 'The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DCVC'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DCVC'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        delete:
            tags:
                - DCVCs
            description: |-
                删除一个 DCVC.
                 [EN] Delete a DCVC.
            operationId: DCVCs_DeleteDCVC
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Zone
                  required: true
                  schema:
                    type: string
                - name: dcvc_name
                  in: path
                  description: 'The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
            responses:
                "200":
                    description: OK
                    content: {}
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
        patch:
            tags:
                - DCVCs
            description: |-
                更新 DCVC 可编辑字段.
                 [EN] Update DCVC editable properties.
            operationId: DCVCs_UpdateDCVC
            parameters:
                - name: subscription_name
                  in: path
                  description: Subscription
                  required: true
                  schema:
                    type: string
                - name: resource_group_name
                  in: path
                  description: Resource group
                  required: true
                  schema:
                    type: string
                - name: zone
                  in: path
                  description: Zone
                  required: true
                  schema:
                    type: string
                - name: dcvc_name
                  in: path
                  description: 'The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                  required: true
                  schema:
                    type: string
                - name: update_mask
                  in: query
                  description: update_mask
                  schema:
                    type: string
                    format: field-mask
            requestBody:
                content:
                    application/json:
                        schema:
                            $ref: '#/components/schemas/DCVCUpdateProperties'
                required: true
            responses:
                "200":
                    description: OK
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/DCVC'
                default:
                    description: Default error response
                    content:
                        application/json:
                            schema:
                                $ref: '#/components/schemas/Status'
components:
    schemas:
        DCVC:
            type: object
            properties:
                id:
                    type: string
                    description: The DCVC resource id
                name:
                    type: string
                    description: 'The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.'
                display_name:
                    type: string
                    description: resource display name
                description:
                    type: string
                    description: ContainerInstance resource description
                uid:
                    type: string
                    description: The DCVC resource uuid.
                resource_type:
                    type: string
                    description: The DCVC resource type.
                creator_id:
                    type: string
                    description: The id of the user who created the DCVC resource.
                owner_id:
                    type: string
                    description: The id of the user who owns the DCVC resource.
                tenant_id:
                    type: string
                    description: Tenant id.
                zone:
                    type: string
                    description: Zone.
                state:
                    type: integer
                    description: The current state of the DCVC resource.
                    format: enum
                sku_id:
                    type: string
                    description: Sku id.
                tags:
                    type: object
                    additionalProperties:
                        type: string
                    description: Tags attached to the DCVC resource.
                properties:
                    $ref: '#/components/schemas/DCVCProperties'
                order_info:
                    $ref: '#/components/schemas/OrderInfo'
                deleted:
                    type: boolean
                    description: Indicates whether the DCVC resource is deleted or not.
                create_time:
                    type: string
                    description: The time when the DCVC resource was created.
                    format: date-time
                update_time:
                    type: string
                    description: The time when the DCVC resource was last updated.
                    format: date-time
            description: DCVC 实例结构体. [EN] DCVC entity.
        DCVCProperties:
            type: object
            properties:
                route_type:
                    type: string
                    description: '[zh] 路由类型. [en] route type.'
                limit_rate_items:
                    $ref: '#/components/schemas/LimitRateItems'
                dcpl_id:
                    type: string
                    description: '[zh] 物理线路 id. [en] dcpl id.'
                dcgw_name:
                    type: string
                    description: '[zh] 专线网关 name. [en] dcgw name.'
                vlan_id:
                    type: string
                    description: '[zh] 虚拟通道 vlan id. [en] vlan id'
                customer_connect_ip:
                    type: string
                    description: '[zh]  客户IDC侧互联ip. [en]  connect ip of customer.'
                sensecore_connect_ip:
                    type: string
                    description: '[zh]  大装置侧互联ip. [en]  connect ip of sensecore.'
                vxlan_name:
                    readOnly: true
                    type: string
                    description: '[zh] 专线网关的 vxlan name. [en] vxlan name of dcgw'
                vxlan_id:
                    readOnly: true
                    type: string
                    description: '[zh] 专线网关的 vxlan id. [en] vxlan id of dcgw'
                local_vtep_ip:
                    readOnly: true
                    type: string
                    description: '[zh] 专线网关本端的 vtep ip. [en] local vtep ip of dcgw.'
                remote_vtep_ip:
                    readOnly: true
                    type: string
                    description: '[zh] 专线网关远端的 vtep ip. [en] remote vtep ip of dcgw.'
                local_vxlan_ip:
                    readOnly: true
                    type: string
                    description: '[zh] 专线网关本端的 vxlan ip. [en] local vxlan ip of dcgw'
                remote_vxlan_ip:
                    readOnly: true
                    type: string
                    description: '[zh] 专线网关远端的 vxlan ip. [en] local dc asw vxlan ip.'
                local_cidr:
                    type: array
                    items:
                        type: string
                    description: '[zh]  专线网关本端的 CIDR. [en] local cidr of dcgw.'
                remote_cidr:
                    type: array
                    items:
                        type: string
                    description: '[zh] 专线网关远端的 CIDR. [en] remote cidr of dcgw.'
                subscription_name:
                    readOnly: true
                    type: string
                    description: '[zh] 专线网关的 subscription name. [en] subscription name of dcgw'
                resource_group_name:
                    readOnly: true
                    type: string
                    description: '[zh] 专线网关的 resource group name. [en] resource group name of dcgw'
                health_check:
                    $ref: '#/components/schemas/HealtCheck'
            description: 资源实际属性. [EN] Real resource properties.
        DCVCUpdateProperties:
            type: object
            properties:
                display_name:
                    type: string
                    description: resource display name
                description:
                    type: string
                    description: ContainerInstance resource description
                properties:
                    $ref: '#/components/schemas/DCVCProperties'
            description: '[zh] 更新DCVC资源. [en] Update DCVC properties.'
        GoogleProtobufAny:
            type: object
            properties:
                '@type':
                    type: string
                    description: The type of the serialized message.
            additionalProperties: true
            description: Contains an arbitrary serialized message along with a @type that describes the type of the serialized message.
        HealtCheck:
            type: object
            properties:
                enable:
                    type: boolean
                    description: '[zh] 开启/关闭健康检查标志。默认为：关闭。 [en] enable, default false'
                target_ip:
                    type: string
                    description: '[zh] 健康检查的目标IP地址。默认值为空字符串，空字符串表示使用此虚拟通道的互联IP（IDC侧），即：CustomerIDCConnectIP。 [en] target ip, default customer_connect_ip'
                interval:
                    type: integer
                    description: '[zh] 健康检查的间隔时间，单位：秒。默认值为2秒。支持[2,5]内整数. [en] health check interval, default 2s, effective value 2 ~ 5'
                    format: int32
                counter:
                    type: integer
                    description: '[zh] 健康检查成功/失败判定次数。单位：次。默认值为5次。有效值为3 ~ 8次。 [en] health check counter, default 5, effective value 3 ~ 8'
                    format: int32
                run_status:
                    type: string
                    description: '[zh] 健康检查的结果，"SUCCESS" --成功，"FAIL"--失败，"CHECKING" --- 检测中 （虚拟通道尚未完成完整的1轮检测，无法给出成功/失败结果。默认情况下2x5=10秒） [en] run status'
                run_time:
                    type: string
                    description: '[zh]  健康检查结果最后变化时间。表示最近从“成功”到“失败”，或者“失败”到“成功”，或者“检测中”到“成功”/“失败”的切换时刻。从这个时间至今，都是没有状态变化。 [en] run time'
                    format: date-time
            description: HealtCheck
        LimitRateItems:
            type: object
            properties:
                up_stream_bw:
                    type: integer
                    description: 'DCGW 上行带宽限制，单位: M/s.'
                    format: int32
                down_stream_bw:
                    type: integer
                    description: 'DCGW 下行带宽限制，单位: M/s'
                    format: int32
            description: 限速项
        ListDCVCsResponse:
            type: object
            properties:
                dcvcs:
                    type: array
                    items:
                        $ref: '#/components/schemas/DCVC'
                    description: DCVC 列表. [EN] DCVC list.
                next_page_token:
                    type: string
                    description: 下一个页面的 token，如果没有更多数据则为空. [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
                total_size:
                    type: integer
                    description: total size
                    format: int32
            description: 列举 DCVCs 的响应. [EN] Response to list DCVCs.
        OrderInfo:
            type: object
            properties:
                billing_cycle_number:
                    type: integer
                    description: '[zh] 购买时长. [en] length of purchase.'
                    format: int32
                auto_renew:
                    type: boolean
                    description: '[zh] 自动续费. [en] Automatic renewal.'
                currency_code:
                    type: string
                    description: '[zh] 货币代码. [en] currency code.'
                payment_channel:
                    type: integer
                    description: '[zh] 支付方式. [en] payment method.'
                    format: enum
                note:
                    type: string
                    description: '[zh] 订单备注. [en] order notes'
                order_type:
                    type: integer
                    description: '[zh] 订单类型. [en] Order Type.'
                    format: enum
                order_id:
                    type: string
                    description: '[zh] 订单id. [en] order id.'
                start_time:
                    type: string
                    description: '[zh] 订单生效日期. [en] Order effective date.'
                    format: date-time
                payment_model:
                    type: integer
                    description: '[zh] 付费类型. [en] payment type.'
                    format: enum
                billing_model:
                    type: integer
                    description: '[zh] 计费类型. [en] billing type.'
                    format: enum
                original_id:
                    type: string
                    description: '[zh] 合同包ID. [en] Contract package ID. original id-- OT_ORIGINAL: original spu_id/package_id; OT_RENEW/OT_UPGRADED/OT_DOWNGRADED: original/ order_id; OT_CONTRACT: original contract_id'
                end_time:
                    type: string
                    description: '[zh] 订单结束时间. [en] Order end time.'
                    format: date-time
            description: '[zh] 订单信息. [en] Order Infomation.'
        Status:
            type: object
            properties:
                code:
                    type: integer
                    description: The status code, which should be an enum value of [google.rpc.Code][google.rpc.Code].
                    format: int32
                message:
                    type: string
                    description: A developer-facing error message, which should be in English. Any user-facing error message should be localized and sent in the [google.rpc.Status.details][google.rpc.Status.details] field, or localized by the client.
                details:
                    type: array
                    items:
                        $ref: '#/components/schemas/GoogleProtobufAny'
                    description: A list of messages that carry the error details.  There is a common set of message types for APIs to use.
            description: 'The `Status` type defines a logical error model that is suitable for different programming environments, including REST APIs and RPC APIs. It is used by [gRPC](https://github.com/grpc). Each `Status` message contains three pieces of data: error code, error message, and error details. You can find out more about this error model and how to work with it in the [API Design Guide](https://cloud.google.com/apis/design/errors).'
tags:
    - name: DCVCs
