# Boson Provider 对接 RM 的消息体设计

Boson Provider 将会通过 RocketMQ 接收 RM 发送的 MQ 请求并通过 MQ 进行状态反馈。

## MQ 相关

- 收来自RM消息的 Topic 命名
    topic: "sensecore-network-v1-az1"
- 发给 RM 返回资源状态的 Topic 命名
    rmAckTopic: "sensecore-rm-resource-update"
- 发给 BOSS action 处理结果消息的 Topic 命名
    bossAckTopic: "sensecore-boss-resource-update"
- 收来自 RM 消息的消费者组名
    brokerConsumerGroupName: "sensecore-network-v1-az1-consumer"
- 发给 RM 的处理结果生产者组名
    brokerProducerGroupName: "sensecore-network-v1-az1-rm-producer"
- 发给 BOSS action 的处理结果生产者组名
    brokerProducerGroupName: "sensecore-network-v1-az1-boss-producer"

## RM 的 MQ 消息体

RM 的消息体是根据 [pkg/types/pb](../../pkg/types/pb) 中的 proto 文件定义，加上相关配置参数组成的，消息体结构如下：

```
{​​

    "package": "proto package",​​

    "service": "proto service",​​

    "method": "proto method",​​

    "action": "create",

    "callback_data": {
        "order_id": "order_id"
    }

    "data": {}​​

}​​
```
​
说明：以 [pkg/types/pb/vpc.proto](../../pkg/types/pb/vpc.proto) 为例

1. package: 请求 API 对应的 proto 文件中的 package，例：sensecore.network.v1
2. service: 请求 API 对应的 proto 文件中的 service，例：VPCs
3. method: 请求 API 对应的 proto 文件中的 service 下的方法名，例：ListVPCs
4. data: 请求 API 对应的 proto 文件中的 service 下方法里的请求体，例：ListVPCsRequest

## 给 RM 响应的消息体

给 RM 响应的消息体结构如下：

```
{​​

    "id": "resource id",​​

    "state": "resource state",​​

}​
```

说明：以 [pb/network/v1/vpc.proto](../../pb/network/v1/vpc.proto) 为例

1. id：资源在 RM 中的唯一标识，例：subscriptions/sid1/resourceGroups/rgid1/zones/zoneid1/vpcs/vpcid1
2. state: 对应各 proto 中资源实体的 state 字段，返回 RM 当前资源状态，例：Active

state 可选值:

- ACCEPTED​
- ACTIVE​
- CANCELLED​
- DELETED​
- FAILED​
- SUCCEEDED​
- SUSPENDED​
- VERIFIED​
- DELETING
- PENDING
- REPAIRING
- RUNNING
- SUSPENDING

## 给 BOS 响应的消息体

给 BOSS 响应的消息体结构如下：

```
{​​

    "resource_id": "resource id",​​

    "resource_type": "resource type",

    "action": "create",

    "result": "success or failed"​,

    "reason": "failed reason"​,

    "callback_data": {
        "order_id": "order_id"
    }

}​
```
