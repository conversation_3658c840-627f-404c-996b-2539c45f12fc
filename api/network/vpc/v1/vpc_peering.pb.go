// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/vpc/v1/vpc_peering.proto

package vpc

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/api/annotations"
	v1 "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// [zh] 资源状态.
// [en] Represents the different states of a VpcPeering.
type State int32

const (
	// [zh] 创建中.
	// [en] The VpcPeering resource is being created.
	State_CREATING State = 0
	// [zh] 更新中.
	// [en] The VpcPeering resource is being updated.
	State_UPDATING State = 1
	// [zh] 已激活.
	// [en] The VpcPeering resource has been active.
	State_ACTIVE State = 2
	// [zh] 删除中.
	// [en] TThe VpcPeering resource is being deleted.
	State_DELETING State = 3
	// [zh] 已删除.
	// [en] The VpcPeering resource has been deleted.
	State_DELETED State = 4
	// [zh] 操作失败.
	// [en] The VpcPeering resource has failed.
	State_FAILED State = 5
	// [zh] 到期降级中
	// [en]  The VpcPeering resource is being expireDowngrading.
	State_EXPIREDOWNGRADING State = 6
	// [zh] 到期已降级
	// [en]   The VpcPeering resource has been expireDowngrade.
	State_EXPIREDOWNGRADED State = 7
	// [zh] 续订升级中
	// [en]   The VpcPeering resource is being renewUpgrading.
	State_RENEWUPGRADING State = 8
	// [zh] 到期停服中.
	// [en] The VpcPeering resource is being disabled.
	State_EXPIRESTOPPING State = 9
	// [zh] 到期已停服.
	// [en] The VpcPeering resource has been disabled.
	State_EXPIRESTOPPED State = 10
	// [zh] 续订恢复中.
	// [en] The VpcPeering resource is being enabled.
	State_RENEWSTARTING State = 11
	// [zh] 服务降级中【瞬时】.
	// [en] Default, the VpcPeering is being downgrading.
	State_DOWNGRADING State = 12
	// [zh] 服务降级.
	// [en] Default, the VpcPeering is being downgraded.
	State_DOWNGRADE State = 13
	// [zh] 服务降级恢复中【瞬时】.
	// [en] Default, the VpcPeering is being restoring.
	State_RESTORING State = 14
	// [zh] 欠费停服中【瞬时】.
	// [en] Default, the VpcPeering is being arrearStopping.
	State_ARREARSTOPPING State = 15
	// [zh] 欠费停服
	// [en] Default, the VpcPeering is being arrearStopped.
	State_ARREARSTOPPED State = 16
	// [zh] 充值恢复中【瞬时】.
	// [en] Default, the VpcPeering is being rechargeStarting.
	State_RECHARGESTARTING State = 17
	// [zh] 资源变更中【瞬时】.
	// [en] Default, the VpcPeering is being resizing.
	State_RESIZING State = 18
	// [zh] 取消资源变更【瞬时】.
	// [en] Default, the aoss pack is being resizeCanceling.
	State_RESIZECANCELING State = 19
)

// Enum value maps for State.
var (
	State_name = map[int32]string{
		0:  "CREATING",
		1:  "UPDATING",
		2:  "ACTIVE",
		3:  "DELETING",
		4:  "DELETED",
		5:  "FAILED",
		6:  "EXPIREDOWNGRADING",
		7:  "EXPIREDOWNGRADED",
		8:  "RENEWUPGRADING",
		9:  "EXPIRESTOPPING",
		10: "EXPIRESTOPPED",
		11: "RENEWSTARTING",
		12: "DOWNGRADING",
		13: "DOWNGRADE",
		14: "RESTORING",
		15: "ARREARSTOPPING",
		16: "ARREARSTOPPED",
		17: "RECHARGESTARTING",
		18: "RESIZING",
		19: "RESIZECANCELING",
	}
	State_value = map[string]int32{
		"CREATING":          0,
		"UPDATING":          1,
		"ACTIVE":            2,
		"DELETING":          3,
		"DELETED":           4,
		"FAILED":            5,
		"EXPIREDOWNGRADING": 6,
		"EXPIREDOWNGRADED":  7,
		"RENEWUPGRADING":    8,
		"EXPIRESTOPPING":    9,
		"EXPIRESTOPPED":     10,
		"RENEWSTARTING":     11,
		"DOWNGRADING":       12,
		"DOWNGRADE":         13,
		"RESTORING":         14,
		"ARREARSTOPPING":    15,
		"ARREARSTOPPED":     16,
		"RECHARGESTARTING":  17,
		"RESIZING":          18,
		"RESIZECANCELING":   19,
	}
)

func (x State) Enum() *State {
	p := new(State)
	*p = x
	return p
}

func (x State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_vpc_v1_vpc_peering_proto_enumTypes[0].Descriptor()
}

func (State) Type() protoreflect.EnumType {
	return &file_network_vpc_v1_vpc_peering_proto_enumTypes[0]
}

func (x State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use State.Descriptor instead.
func (State) EnumDescriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{0}
}

// [zh] 创建对等连接资源请求.
// [en] CreateVpcPeeringRequest.
type CreateVpcPeeringRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 对等连接资源名称.
	// [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VpcPeeringName string `protobuf:"bytes,4,opt,name=vpc_peering_name,json=vpcPeeringName,proto3" json:"vpc_peering_name,omitempty"`
	// [zh] 创建对等连接资源.
	// [en] The VpcPeering resource to create.
	VpcPeering *VpcPeeringCreate `protobuf:"bytes,5,opt,name=vpc_peering,json=vpcPeering,proto3" json:"vpc_peering,omitempty"` // [(validate.rules).message.required = true];
}

func (x *CreateVpcPeeringRequest) Reset() {
	*x = CreateVpcPeeringRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateVpcPeeringRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVpcPeeringRequest) ProtoMessage() {}

func (x *CreateVpcPeeringRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVpcPeeringRequest.ProtoReflect.Descriptor instead.
func (*CreateVpcPeeringRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{0}
}

func (x *CreateVpcPeeringRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *CreateVpcPeeringRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *CreateVpcPeeringRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateVpcPeeringRequest) GetVpcPeeringName() string {
	if x != nil {
		return x.VpcPeeringName
	}
	return ""
}

func (x *CreateVpcPeeringRequest) GetVpcPeering() *VpcPeeringCreate {
	if x != nil {
		return x.VpcPeering
	}
	return nil
}

// [zh] 对等连接资源.
// [en] The VpcPeering resource.
type VpcPeeringCreate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源id.
	// [en] The VpcPeering resource id using the form:
	//     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/vpc_peerings/{name}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// [zh] 资源的uuid.
	// [en] The VpcPeering resource uuid.
	Uid string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// [zh] 资源标识.
	// [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] 资源名称.
	// [en] The VpcPeering resource display name
	DisplayName string `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// [zh] 资源描述.
	// [en] The VpcPeering resource description.
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// [zh] 资源类型.
	// [en] The VpcPeering resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// [zh] 创建者id.
	// [en] The id of the user who created the VpcPeering resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// [zh] 拥有者id.
	// [en] The id of the user who owns the VpcPeering resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// [zh] 租户id
	// [en] Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 资源状态.
	// [en] The current state of the VpcPeering resource.
	State State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.vpc.v1.State" json:"state,omitempty"`
	// [zh] 最小库存单元id.
	// [en] SKU id.
	SkuId string `protobuf:"bytes,12,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// [zh] 对等连接资源的标签.
	// [en] Tags attached to the VpcPeering resource.
	Tags map[string]string `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// [zh] 对等连接资源的属性
	// [en] Properties of the VpcPeering resource.
	Properties *VpcPeeringProperties `protobuf:"bytes,14,opt,name=properties,proto3" json:"properties,omitempty"` // [(validate.rules).message.required = true];
	// [zh] 订单信息.
	// [en] Order information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,15,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"` // [(validate.rules).message.required = true];
	// [zh] 资源是否已删除.
	// [en] Indicates whether the VpcPeering resource is deleted or not.
	Deleted bool `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// [zh] 资源创建时间.
	// [en] The time when the VpcPeering resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// [zh] 资源更新时间.
	// [en] The time when the VpcPeering resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *VpcPeeringCreate) Reset() {
	*x = VpcPeeringCreate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VpcPeeringCreate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VpcPeeringCreate) ProtoMessage() {}

func (x *VpcPeeringCreate) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VpcPeeringCreate.ProtoReflect.Descriptor instead.
func (*VpcPeeringCreate) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{1}
}

func (x *VpcPeeringCreate) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VpcPeeringCreate) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *VpcPeeringCreate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VpcPeeringCreate) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *VpcPeeringCreate) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *VpcPeeringCreate) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *VpcPeeringCreate) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *VpcPeeringCreate) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *VpcPeeringCreate) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *VpcPeeringCreate) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *VpcPeeringCreate) GetState() State {
	if x != nil {
		return x.State
	}
	return State_CREATING
}

func (x *VpcPeeringCreate) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *VpcPeeringCreate) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *VpcPeeringCreate) GetProperties() *VpcPeeringProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *VpcPeeringCreate) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

func (x *VpcPeeringCreate) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *VpcPeeringCreate) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *VpcPeeringCreate) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

type VpcPeeringProperty struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *VpcPeeringProperty) Reset() {
	*x = VpcPeeringProperty{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VpcPeeringProperty) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VpcPeeringProperty) ProtoMessage() {}

func (x *VpcPeeringProperty) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VpcPeeringProperty.ProtoReflect.Descriptor instead.
func (*VpcPeeringProperty) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{2}
}

// [zh] 删除对等连接资源请求.
// [en] DeleteVpcPeeringRequest.
type DeleteVpcPeeringRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 对等连接资源名称.
	// [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VpcPeeringName string `protobuf:"bytes,4,opt,name=vpc_peering_name,json=vpcPeeringName,proto3" json:"vpc_peering_name,omitempty"`
}

func (x *DeleteVpcPeeringRequest) Reset() {
	*x = DeleteVpcPeeringRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteVpcPeeringRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVpcPeeringRequest) ProtoMessage() {}

func (x *DeleteVpcPeeringRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVpcPeeringRequest.ProtoReflect.Descriptor instead.
func (*DeleteVpcPeeringRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteVpcPeeringRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DeleteVpcPeeringRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *DeleteVpcPeeringRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteVpcPeeringRequest) GetVpcPeeringName() string {
	if x != nil {
		return x.VpcPeeringName
	}
	return ""
}

// [zh] 更新对等连接请求.
// [en] Update VpcPeering request.
type UpdateVpcPeeringRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] The resource subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] The resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] The resource available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 资源名称.
	// [en] The resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VpcPeeringName string `protobuf:"bytes,4,opt,name=vpc_peering_name,json=vpcPeeringName,proto3" json:"vpc_peering_name,omitempty"`
	// [zh] 更新资源.
	// [en] The resource to update.
	VpcPeering *VpcPeeringUpdate `protobuf:"bytes,5,opt,name=vpc_peering,json=vpcPeering,proto3" json:"vpc_peering,omitempty"`
	// [zh] 更新标记.
	// [en] The resource update mask.
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,6,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateVpcPeeringRequest) Reset() {
	*x = UpdateVpcPeeringRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVpcPeeringRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVpcPeeringRequest) ProtoMessage() {}

func (x *UpdateVpcPeeringRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVpcPeeringRequest.ProtoReflect.Descriptor instead.
func (*UpdateVpcPeeringRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateVpcPeeringRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *UpdateVpcPeeringRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *UpdateVpcPeeringRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateVpcPeeringRequest) GetVpcPeeringName() string {
	if x != nil {
		return x.VpcPeeringName
	}
	return ""
}

func (x *UpdateVpcPeeringRequest) GetVpcPeering() *VpcPeeringUpdate {
	if x != nil {
		return x.VpcPeering
	}
	return nil
}

func (x *UpdateVpcPeeringRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// [zh] 更新对等连接资源.
// [en] Update VpcPeering body.
type VpcPeeringUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源名称.
	// [en] The resource display name with the restriction: `^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]?([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_]{0,62})$`.
	DisplayName *string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3,oneof" json:"display_name,omitempty"`
	// [zh] 资源描述.
	// [en] The resource Description.
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *VpcPeeringUpdate) Reset() {
	*x = VpcPeeringUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VpcPeeringUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VpcPeeringUpdate) ProtoMessage() {}

func (x *VpcPeeringUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VpcPeeringUpdate.ProtoReflect.Descriptor instead.
func (*VpcPeeringUpdate) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{5}
}

func (x *VpcPeeringUpdate) GetDisplayName() string {
	if x != nil && x.DisplayName != nil {
		return *x.DisplayName
	}
	return ""
}

func (x *VpcPeeringUpdate) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// [zh] 查看对等连接资源详情请求.
// [en] GetVpcPeeringRequest.
type GetVpcPeeringRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 对等连接资源名称.
	// [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VpcPeeringName string `protobuf:"bytes,4,opt,name=vpc_peering_name,json=vpcPeeringName,proto3" json:"vpc_peering_name,omitempty"`
}

func (x *GetVpcPeeringRequest) Reset() {
	*x = GetVpcPeeringRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVpcPeeringRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVpcPeeringRequest) ProtoMessage() {}

func (x *GetVpcPeeringRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVpcPeeringRequest.ProtoReflect.Descriptor instead.
func (*GetVpcPeeringRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{6}
}

func (x *GetVpcPeeringRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetVpcPeeringRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetVpcPeeringRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetVpcPeeringRequest) GetVpcPeeringName() string {
	if x != nil {
		return x.VpcPeeringName
	}
	return ""
}

// 列举 VPC Peering 的请求.
// [EN] Request to list VPC Peerings.
type ListVpcPeeringRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
}

func (x *ListVpcPeeringRequest) Reset() {
	*x = ListVpcPeeringRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListVpcPeeringRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVpcPeeringRequest) ProtoMessage() {}

func (x *ListVpcPeeringRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVpcPeeringRequest.ProtoReflect.Descriptor instead.
func (*ListVpcPeeringRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{7}
}

func (x *ListVpcPeeringRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListVpcPeeringRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListVpcPeeringRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

// 列举 VPC Peering 的响应.
// [EN] Response to list VPC Peerings.
type ListVpcPeeringResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// VPC Peering 列表.
	// [EN] VPC peering list.
	VpcPeerings []*VpcPeering `protobuf:"bytes,1,rep,name=vpc_peerings,json=vpcPeerings,proto3" json:"vpc_peerings,omitempty"`
	// total size
	TotalSize int32 `protobuf:"varint,2,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListVpcPeeringResponse) Reset() {
	*x = ListVpcPeeringResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListVpcPeeringResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVpcPeeringResponse) ProtoMessage() {}

func (x *ListVpcPeeringResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVpcPeeringResponse.ProtoReflect.Descriptor instead.
func (*ListVpcPeeringResponse) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{8}
}

func (x *ListVpcPeeringResponse) GetVpcPeerings() []*VpcPeering {
	if x != nil {
		return x.VpcPeerings
	}
	return nil
}

func (x *ListVpcPeeringResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// [zh] ResizeVpcPeeringRequest.
// [en] 对等连接资源变更请求.
type ResizeVpcPeeringRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅名.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 对等连接资源名称.
	// [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VpcPeeringName string `protobuf:"bytes,4,opt,name=vpc_peering_name,json=vpcPeeringName,proto3" json:"vpc_peering_name,omitempty"`
	// [zh] 变更资源包body参数.
	// [en] Resize VpcPeering resource.
	VpcPeeringResize *VpcPeeringResize `protobuf:"bytes,5,opt,name=vpc_peering_resize,json=vpcPeeringResize,proto3" json:"vpc_peering_resize,omitempty"`
}

func (x *ResizeVpcPeeringRequest) Reset() {
	*x = ResizeVpcPeeringRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResizeVpcPeeringRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResizeVpcPeeringRequest) ProtoMessage() {}

func (x *ResizeVpcPeeringRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResizeVpcPeeringRequest.ProtoReflect.Descriptor instead.
func (*ResizeVpcPeeringRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{9}
}

func (x *ResizeVpcPeeringRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ResizeVpcPeeringRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ResizeVpcPeeringRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ResizeVpcPeeringRequest) GetVpcPeeringName() string {
	if x != nil {
		return x.VpcPeeringName
	}
	return ""
}

func (x *ResizeVpcPeeringRequest) GetVpcPeeringResize() *VpcPeeringResize {
	if x != nil {
		return x.VpcPeeringResize
	}
	return nil
}

// [zh] 变更对等连接资源.
// [en] Resize VpcPeering resource.
type VpcPeeringResize struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 对等连接资源uuid.
	// [en] VpcPeering resource uuid.
	ResourceId string `protobuf:"bytes,1,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`
	// [zh] 对等连接资源sku_id.
	// [en] VpcPeering resource sku_id.
	SkuId string `protobuf:"bytes,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// [zh] 变更操作者id.
	// [en] operator id.
	OperatorId string `protobuf:"bytes,3,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// [zh] 对等连接资源的属性
	// [en] Properties of the VpcPeering resource.
	Properties *VpcPeeringProperties `protobuf:"bytes,4,opt,name=properties,proto3" json:"properties,omitempty"` // [(validate.rules).message.required = true];
	// [zh] 订单信息.
	// [en] Order information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,5,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"` // [(validate.rules).message.required = true];
}

func (x *VpcPeeringResize) Reset() {
	*x = VpcPeeringResize{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VpcPeeringResize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VpcPeeringResize) ProtoMessage() {}

func (x *VpcPeeringResize) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VpcPeeringResize.ProtoReflect.Descriptor instead.
func (*VpcPeeringResize) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{10}
}

func (x *VpcPeeringResize) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *VpcPeeringResize) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *VpcPeeringResize) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

func (x *VpcPeeringResize) GetProperties() *VpcPeeringProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *VpcPeeringResize) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

// [zh] 释放对等连接资源.
// [en] Release VpcPeering resource.
type ReleaseVpcPeeringRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅名.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 对等连接资源名称.
	// [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VpcPeeringName string `protobuf:"bytes,4,opt,name=vpc_peering_name,json=vpcPeeringName,proto3" json:"vpc_peering_name,omitempty"`
}

func (x *ReleaseVpcPeeringRequest) Reset() {
	*x = ReleaseVpcPeeringRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleaseVpcPeeringRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseVpcPeeringRequest) ProtoMessage() {}

func (x *ReleaseVpcPeeringRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseVpcPeeringRequest.ProtoReflect.Descriptor instead.
func (*ReleaseVpcPeeringRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{11}
}

func (x *ReleaseVpcPeeringRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ReleaseVpcPeeringRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ReleaseVpcPeeringRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ReleaseVpcPeeringRequest) GetVpcPeeringName() string {
	if x != nil {
		return x.VpcPeeringName
	}
	return ""
}

// [zh] 对等连接资源.
// [en] The VpcPeering resource.
type VpcPeering struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源id.
	// [en] The VpcPeering resource id using the form:
	//     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/vpc_peerings/{name}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// [zh] 资源的uuid.
	// [en] The VpcPeering resource uuid.
	Uid string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// [zh] 资源标识.
	// [en] The VpcPeering resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] 资源名称.
	// [en] The VpcPeering resource display name
	DisplayName string `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// [zh] 资源描述.
	// [en] The VpcPeering resource description.
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// [zh] 资源类型.
	// [en] The VpcPeering resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// [zh] 创建者id.
	// [en] The id of the user who created the VpcPeering resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// [zh] 拥有者id.
	// [en] The id of the user who owns the VpcPeering resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// [zh] 租户id
	// [en] Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 资源状态.
	// [en] The current state of the VpcPeering resource.
	State State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.vpc.v1.State" json:"state,omitempty"`
	// [zh] 最小库存单元id.
	// [en] SKU id.
	SkuId string `protobuf:"bytes,12,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// [zh] 对等连接资源的标签.
	// [en] Tags attached to the VirtualMachine resource.
	Tags map[string]string `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// [zh] 对等连接资源的属性
	// [en] Properties of the VirtualMachine resource.
	Properties *VpcPeeringProperties `protobuf:"bytes,14,opt,name=properties,proto3" json:"properties,omitempty"` // [(validate.rules).message.required = true];
	// [zh] 订单信息.
	// [en] Order information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,15,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"` // [(validate.rules).message.required = true];
	// [zh] 资源是否已删除.
	// [en] Indicates whether the VpcPeering resource is deleted or not.
	Deleted bool `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// [zh] 资源创建时间.
	// [en] The time when the VpcPeering resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// [zh] 资源更新时间.
	// [en] The time when the VpcPeering resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *VpcPeering) Reset() {
	*x = VpcPeering{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VpcPeering) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VpcPeering) ProtoMessage() {}

func (x *VpcPeering) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VpcPeering.ProtoReflect.Descriptor instead.
func (*VpcPeering) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{12}
}

func (x *VpcPeering) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VpcPeering) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *VpcPeering) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VpcPeering) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *VpcPeering) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *VpcPeering) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *VpcPeering) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *VpcPeering) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *VpcPeering) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *VpcPeering) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *VpcPeering) GetState() State {
	if x != nil {
		return x.State
	}
	return State_CREATING
}

func (x *VpcPeering) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *VpcPeering) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *VpcPeering) GetProperties() *VpcPeeringProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *VpcPeering) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

func (x *VpcPeering) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *VpcPeering) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *VpcPeering) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// [zh] 资源属性.
// [en] The VpcPeeringProperties.
type VpcPeeringProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源规格属性.
	// [en] Resource Specification Properties.
	Resources *Resources `protobuf:"bytes,1,opt,name=resources,proto3" json:"resources,omitempty"`
	// [zh] 本地的 vpc 名.
	// [en] name of local vpc.
	Vpc string `protobuf:"bytes,2,opt,name=vpc,proto3" json:"vpc,omitempty"`
	// [zh] 对等连接的类型.
	// [en] type of vpc peering.
	Type string `protobuf:"bytes,3,opt,name=type,proto3" json:"type,omitempty"`
	// [zh] 对等连接使用的 trainsit switch 名.
	// [en] transit switch of this vpc peering.
	TransitSwitch string `protobuf:"bytes,4,opt,name=transit_switch,json=transitSwitch,proto3" json:"transit_switch,omitempty"`
	// [zh] 本地对等连接在 transit switch 上的 ip.
	// [en] local gateway ip in transit switch.
	LocalGatewayIp string `protobuf:"bytes,5,opt,name=local_gateway_ip,json=localGatewayIp,proto3" json:"local_gateway_ip,omitempty"`
	// [zh] 本地对等连接路由的网关节点.
	// [en] node name of gateway.
	LocalGatewayNodes []string `protobuf:"bytes,6,rep,name=local_gateway_nodes,json=localGatewayNodes,proto3" json:"local_gateway_nodes,omitempty"`
	// [zh] 对等连接远端的 CIDR.
	// [en] remote CIDR of this vpc peering.
	RemoteCidr []string `protobuf:"bytes,7,rep,name=remote_cidr,json=remoteCidr,proto3" json:"remote_cidr,omitempty"`
	// [zh] 对等连接远端的网关ip.
	// [en] local gateway ip in transit switch.
	RemoteGatewayIp string `protobuf:"bytes,8,opt,name=remote_gateway_ip,json=remoteGatewayIp,proto3" json:"remote_gateway_ip,omitempty"`
	// [zh] 对等连接远端的 vpc 名称.
	// [en] name of remote vpc in this peering.
	RemoteVpc string `protobuf:"bytes,9,opt,name=remote_vpc,json=remoteVpc,proto3" json:"remote_vpc,omitempty"`
	// [zh] 对等连接本端的 CIDR.
	// [en] local CIDR of this vpc peering.
	LocalCidr []string `protobuf:"bytes,10,rep,name=local_cidr,json=localCidr,proto3" json:"local_cidr,omitempty"`
	// [zh] 对等连接远端 zone.
	// [en] available zone of remote vpc in this peering.
	RemoteZone string `protobuf:"bytes,11,opt,name=remote_zone,json=remoteZone,proto3" json:"remote_zone,omitempty"`
}

func (x *VpcPeeringProperties) Reset() {
	*x = VpcPeeringProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VpcPeeringProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VpcPeeringProperties) ProtoMessage() {}

func (x *VpcPeeringProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VpcPeeringProperties.ProtoReflect.Descriptor instead.
func (*VpcPeeringProperties) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{13}
}

func (x *VpcPeeringProperties) GetResources() *Resources {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *VpcPeeringProperties) GetVpc() string {
	if x != nil {
		return x.Vpc
	}
	return ""
}

func (x *VpcPeeringProperties) GetType() string {
	if x != nil {
		return x.Type
	}
	return ""
}

func (x *VpcPeeringProperties) GetTransitSwitch() string {
	if x != nil {
		return x.TransitSwitch
	}
	return ""
}

func (x *VpcPeeringProperties) GetLocalGatewayIp() string {
	if x != nil {
		return x.LocalGatewayIp
	}
	return ""
}

func (x *VpcPeeringProperties) GetLocalGatewayNodes() []string {
	if x != nil {
		return x.LocalGatewayNodes
	}
	return nil
}

func (x *VpcPeeringProperties) GetRemoteCidr() []string {
	if x != nil {
		return x.RemoteCidr
	}
	return nil
}

func (x *VpcPeeringProperties) GetRemoteGatewayIp() string {
	if x != nil {
		return x.RemoteGatewayIp
	}
	return ""
}

func (x *VpcPeeringProperties) GetRemoteVpc() string {
	if x != nil {
		return x.RemoteVpc
	}
	return ""
}

func (x *VpcPeeringProperties) GetLocalCidr() []string {
	if x != nil {
		return x.LocalCidr
	}
	return nil
}

func (x *VpcPeeringProperties) GetRemoteZone() string {
	if x != nil {
		return x.RemoteZone
	}
	return ""
}

// [zh] 资源规格属性
// [en] Resource Specification Properties.
type Resources struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 单独购买的计费项
	// [en] Billing Items Purchased Separately.
	BillingItems *BillingItems `protobuf:"bytes,4,opt,name=billing_items,json=billingItems,proto3" json:"billing_items,omitempty"`
}

func (x *Resources) Reset() {
	*x = Resources{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Resources) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Resources) ProtoMessage() {}

func (x *Resources) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Resources.ProtoReflect.Descriptor instead.
func (*Resources) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{14}
}

func (x *Resources) GetBillingItems() *BillingItems {
	if x != nil {
		return x.BillingItems
	}
	return nil
}

// [zh] 计费项
// [en] The BillingItems in VpcPeeringProperties Resources.
type BillingItems struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BillingItems) Reset() {
	*x = BillingItems{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BillingItems) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingItems) ProtoMessage() {}

func (x *BillingItems) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_peering_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingItems.ProtoReflect.Descriptor instead.
func (*BillingItems) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_peering_proto_rawDescGZIP(), []int{15}
}

var File_network_vpc_v1_vpc_peering_proto protoreflect.FileDescriptor

var file_network_vpc_v1_vpc_peering_proto_rawDesc = []byte{
	0x0a, 0x20, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x2f, 0x76, 0x31,
	0x2f, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1d, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76,
	0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c,
	0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x61,
	0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x66,
	0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xb2, 0x02, 0x0a, 0x17, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x54, 0x0a, 0x10, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a,
	0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28,
	0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0e, 0x76, 0x70, 0x63, 0x50,
	0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x50, 0x0a, 0x0b, 0x76, 0x70,
	0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x52, 0x0a, 0x76, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x22, 0xb9, 0x07, 0x0a,
	0x10, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x17, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x73, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x50, 0xe0, 0x41,
	0x02, 0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78,
	0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41,
	0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66,
	0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24, 0x52, 0x0b,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a,
	0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x07, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x08,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x12, 0x3f, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x24, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x1a, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x4d,
	0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x39, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x70, 0x63,
	0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x2e, 0x54, 0x61,
	0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x53, 0x0a,
	0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x33, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f,
	0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2e, 0x63, 0x6f,
	0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x07,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x42, 0x03, 0xe0,
	0x41, 0x03, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x40, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41,
	0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a,
	0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03,
	0xe0, 0x41, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a,
	0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x14, 0x0a, 0x12, 0x56, 0x70, 0x63, 0x50,
	0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x79, 0x22, 0xc8,
	0x01, 0x0a, 0x17, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x2d, 0x0a, 0x10, 0x76, 0x70,
	0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0e, 0x76, 0x70, 0x63, 0x50, 0x65,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x86, 0x03, 0x0a, 0x17, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x57, 0x0a, 0x10, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c,
	0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0e,
	0x76, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x50,
	0x0a, 0x0b, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x76, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73,
	0x6b, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61,
	0x73, 0x6b, 0x22, 0xbf, 0x01, 0x0a, 0x10, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x78, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x50, 0xe0,
	0x41, 0x02, 0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c,
	0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39,
	0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24, 0x48,
	0x00, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01,
	0x01, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0xc5, 0x01, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x56, 0x70, 0x63, 0x50,
	0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a,
	0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x2d, 0x0a,
	0x10, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0e, 0x76, 0x70,
	0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x97, 0x01, 0x0a,
	0x15, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x22, 0x85, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x56,
	0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x4c, 0x0a, 0x0c, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x52, 0x0b, 0x76, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xd6,
	0x02, 0x0a, 0x17, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x57, 0x0a, 0x10, 0x76, 0x70,
	0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f,
	0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x29, 0x3f, 0x24, 0x52, 0x0e, 0x76, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x62, 0x0a, 0x12, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x5f, 0x72, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x76, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x99, 0x02, 0x0a, 0x10, 0x56, 0x70, 0x63, 0x50,
	0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x24, 0x0a, 0x0b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x49, 0x64, 0x12, 0x1a, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x24,
	0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x53, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x68,
	0x69, 0x67, 0x67, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x22, 0xf3, 0x01, 0x0a, 0x18, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x56,
	0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x12, 0x57, 0x0a, 0x10, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa,
	0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0e, 0x76, 0x70, 0x63, 0x50, 0x65,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xad, 0x07, 0x0a, 0x0a, 0x56, 0x70,
	0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52,
	0x03, 0x75, 0x69, 0x64, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x73, 0x0a,
	0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x50, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32,
	0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34,
	0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30,
	0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c,
	0x36, 0x32, 0x7d, 0x29, 0x24, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a,
	0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x03, 0xe0, 0x41, 0x03, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a,
	0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x03, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x3f, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x24, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x42, 0x03, 0xe0,
	0x41, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x06, 0x73, 0x6b, 0x75,
	0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x05,
	0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x47, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0d, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x33, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x54,
	0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x53,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x33, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a,
	0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x42, 0x03,
	0xe0, 0x41, 0x03, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x40, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0,
	0x41, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42,
	0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a,
	0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12,
	0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xb1, 0x03, 0x0a, 0x14, 0x56, 0x70,
	0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x12, 0x46, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76,
	0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x52,
	0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70,
	0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x76, 0x70, 0x63, 0x12, 0x12, 0x0a, 0x04,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65,
	0x12, 0x25, 0x0a, 0x0e, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69, 0x74, 0x5f, 0x73, 0x77, 0x69, 0x74,
	0x63, 0x68, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x74, 0x72, 0x61, 0x6e, 0x73, 0x69,
	0x74, 0x53, 0x77, 0x69, 0x74, 0x63, 0x68, 0x12, 0x28, 0x0a, 0x10, 0x6c, 0x6f, 0x63, 0x61, 0x6c,
	0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0e, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x49,
	0x70, 0x12, 0x2e, 0x0a, 0x13, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x5f, 0x6e, 0x6f, 0x64, 0x65, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x11,
	0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x4e, 0x6f, 0x64, 0x65,
	0x73, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x63, 0x69, 0x64, 0x72,
	0x18, 0x07, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x69,
	0x64, 0x72, 0x12, 0x2a, 0x0a, 0x11, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x67, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x5f, 0x69, 0x70, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0f, 0x72,
	0x65, 0x6d, 0x6f, 0x74, 0x65, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x49, 0x70, 0x12, 0x1d,
	0x0a, 0x0a, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x76, 0x70, 0x63, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x56, 0x70, 0x63, 0x12, 0x1d, 0x0a,
	0x0a, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x63, 0x69, 0x64, 0x72, 0x18, 0x0a, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x43, 0x69, 0x64, 0x72, 0x12, 0x1f, 0x0a, 0x0b,
	0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5a, 0x6f, 0x6e, 0x65, 0x22, 0x5d, 0x0a,
	0x09, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x50, 0x0a, 0x0d, 0x62, 0x69,
	0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x0c,
	0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x0e, 0x0a, 0x0c,
	0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x2a, 0xe0, 0x02, 0x0a,
	0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x44, 0x41, 0x54, 0x49, 0x4e, 0x47,
	0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x0c,
	0x0a, 0x08, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07,
	0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x15, 0x0a, 0x11, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44,
	0x4f, 0x57, 0x4e, 0x47, 0x52, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x06, 0x12, 0x14, 0x0a, 0x10,
	0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x4f, 0x57, 0x4e, 0x47, 0x52, 0x41, 0x44, 0x45, 0x44,
	0x10, 0x07, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x45, 0x4e, 0x45, 0x57, 0x55, 0x50, 0x47, 0x52, 0x41,
	0x44, 0x49, 0x4e, 0x47, 0x10, 0x08, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45,
	0x53, 0x54, 0x4f, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x09, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x58,
	0x50, 0x49, 0x52, 0x45, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x45, 0x44, 0x10, 0x0a, 0x12, 0x11, 0x0a,
	0x0d, 0x52, 0x45, 0x4e, 0x45, 0x57, 0x53, 0x54, 0x41, 0x52, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x0b,
	0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x4f, 0x57, 0x4e, 0x47, 0x52, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x10,
	0x0c, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x4f, 0x57, 0x4e, 0x47, 0x52, 0x41, 0x44, 0x45, 0x10, 0x0d,
	0x12, 0x0d, 0x0a, 0x09, 0x52, 0x45, 0x53, 0x54, 0x4f, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x0e, 0x12,
	0x12, 0x0a, 0x0e, 0x41, 0x52, 0x52, 0x45, 0x41, 0x52, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x49, 0x4e,
	0x47, 0x10, 0x0f, 0x12, 0x11, 0x0a, 0x0d, 0x41, 0x52, 0x52, 0x45, 0x41, 0x52, 0x53, 0x54, 0x4f,
	0x50, 0x50, 0x45, 0x44, 0x10, 0x10, 0x12, 0x14, 0x0a, 0x10, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52,
	0x47, 0x45, 0x53, 0x54, 0x41, 0x52, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x11, 0x12, 0x0c, 0x0a, 0x08,
	0x52, 0x45, 0x53, 0x49, 0x5a, 0x49, 0x4e, 0x47, 0x10, 0x12, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x45,
	0x53, 0x49, 0x5a, 0x45, 0x43, 0x41, 0x4e, 0x43, 0x45, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x13, 0x32,
	0x90, 0x18, 0x0a, 0x0b, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x12,
	0xc2, 0x03, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x12, 0x36, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x70, 0x63, 0x50, 0x65,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x70, 0x63,
	0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x22, 0xca, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xa0,
	0x01, 0x22, 0x90, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63,
	0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a,
	0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x73, 0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x0b, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x96, 0x01, 0x0a, 0x77,
	0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73,
	0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x73, 0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x1b, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65,
	0x72, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x12, 0xa2, 0x03, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56,
	0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x36, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xbd, 0x02, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x93, 0x01, 0x2a, 0x90, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76,
	0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f,
	0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x73, 0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5,
	0x18, 0x96, 0x01, 0x0a, 0x77, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63,
	0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x70,
	0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x1b, 0x76, 0x70,
	0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0xc2, 0x03, 0x0a, 0x10, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x36,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x22, 0xca, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xa0, 0x01, 0x32, 0x90, 0x01, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76,
	0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x7b, 0x76, 0x70, 0x63,
	0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x0b,
	0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x80, 0xb5, 0x18, 0x01, 0x88,
	0xb5, 0x18, 0x00, 0x9a, 0xb5, 0x18, 0x96, 0x01, 0x0a, 0x77, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x7b,
	0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x12, 0x1b, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x69,
	0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xa8,
	0x03, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x12, 0x33, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x47, 0x65, 0x74, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76,
	0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x22, 0xb6, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x93, 0x01, 0x12, 0x90, 0x01, 0x2f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70,
	0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f,
	0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x80, 0xb5, 0x18,
	0x01, 0x9a, 0xb5, 0x18, 0x93, 0x01, 0x0a, 0x77, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f,
	0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x2f, 0x7b, 0x76, 0x70,
	0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12,
	0x18, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x67, 0x65, 0x74, 0x12, 0xa1, 0x03, 0x0a, 0x0e, 0x4c, 0x69,
	0x73, 0x74, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x34, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x35, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xa1, 0x02, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x7f, 0x12, 0x7d, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63,
	0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a,
	0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x73, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x93, 0x01, 0x0a, 0x77, 0x2f, 0x72, 0x6d, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f,
	0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73,
	0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x12, 0x18, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x67, 0x65, 0x74, 0x12, 0xd0, 0x03,
	0x0a, 0x10, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x12, 0x36, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x56, 0x70, 0x63, 0x50, 0x65, 0x65, 0x72,
	0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x70, 0x63, 0x50, 0x65,
	0x65, 0x72, 0x69, 0x6e, 0x67, 0x22, 0xd8, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xae, 0x01, 0x22,
	0x97, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x5f, 0x70,
	0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f,
	0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e,
	0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73, 0x2f,
	0x7b, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x3a, 0x72, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x3a, 0x12, 0x76, 0x70, 0x63, 0x5f, 0x70,
	0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x72, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x88, 0xb5, 0x18,
	0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x96, 0x01, 0x0a, 0x77, 0x2f, 0x72, 0x6d, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f,
	0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x73,
	0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x12, 0x1b, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x72, 0x65, 0x73, 0x69, 0x7a, 0x65,
	0x12, 0xaf, 0x03, 0x0a, 0x11, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x56, 0x70, 0x63, 0x50,
	0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x12, 0x37, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x56, 0x70,
	0x63, 0x50, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xc8, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x9e,
	0x01, 0x22, 0x98, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63,
	0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a,
	0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67,
	0x73, 0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x3a, 0x01, 0x2a, 0x80,
	0xb5, 0x18, 0x01, 0x88, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x96, 0x01, 0x0a, 0x77, 0x2f, 0x72,
	0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b,
	0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e,
	0x67, 0x73, 0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69, 0x6e, 0x67, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x1b, 0x76, 0x70, 0x63, 0x5f, 0x70, 0x65, 0x65, 0x72, 0x69,
	0x6e, 0x67, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x42, 0x50, 0x5a, 0x4e, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x62, 0x6a, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x6c,
	0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x72, 0x79, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2f, 0x62,
	0x6f, 0x73, 0x6f, 0x6e, 0x2d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x70,
	0x69, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x2f, 0x76, 0x31,
	0x3b, 0x76, 0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_network_vpc_v1_vpc_peering_proto_rawDescOnce sync.Once
	file_network_vpc_v1_vpc_peering_proto_rawDescData = file_network_vpc_v1_vpc_peering_proto_rawDesc
)

func file_network_vpc_v1_vpc_peering_proto_rawDescGZIP() []byte {
	file_network_vpc_v1_vpc_peering_proto_rawDescOnce.Do(func() {
		file_network_vpc_v1_vpc_peering_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_vpc_v1_vpc_peering_proto_rawDescData)
	})
	return file_network_vpc_v1_vpc_peering_proto_rawDescData
}

var file_network_vpc_v1_vpc_peering_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_network_vpc_v1_vpc_peering_proto_msgTypes = make([]protoimpl.MessageInfo, 18)
var file_network_vpc_v1_vpc_peering_proto_goTypes = []interface{}{
	(State)(0),                       // 0: sensetime.core.network.vpc.v1.State
	(*CreateVpcPeeringRequest)(nil),  // 1: sensetime.core.network.vpc.v1.CreateVpcPeeringRequest
	(*VpcPeeringCreate)(nil),         // 2: sensetime.core.network.vpc.v1.VpcPeeringCreate
	(*VpcPeeringProperty)(nil),       // 3: sensetime.core.network.vpc.v1.VpcPeeringProperty
	(*DeleteVpcPeeringRequest)(nil),  // 4: sensetime.core.network.vpc.v1.DeleteVpcPeeringRequest
	(*UpdateVpcPeeringRequest)(nil),  // 5: sensetime.core.network.vpc.v1.UpdateVpcPeeringRequest
	(*VpcPeeringUpdate)(nil),         // 6: sensetime.core.network.vpc.v1.VpcPeeringUpdate
	(*GetVpcPeeringRequest)(nil),     // 7: sensetime.core.network.vpc.v1.GetVpcPeeringRequest
	(*ListVpcPeeringRequest)(nil),    // 8: sensetime.core.network.vpc.v1.ListVpcPeeringRequest
	(*ListVpcPeeringResponse)(nil),   // 9: sensetime.core.network.vpc.v1.ListVpcPeeringResponse
	(*ResizeVpcPeeringRequest)(nil),  // 10: sensetime.core.network.vpc.v1.ResizeVpcPeeringRequest
	(*VpcPeeringResize)(nil),         // 11: sensetime.core.network.vpc.v1.VpcPeeringResize
	(*ReleaseVpcPeeringRequest)(nil), // 12: sensetime.core.network.vpc.v1.ReleaseVpcPeeringRequest
	(*VpcPeering)(nil),               // 13: sensetime.core.network.vpc.v1.VpcPeering
	(*VpcPeeringProperties)(nil),     // 14: sensetime.core.network.vpc.v1.VpcPeeringProperties
	(*Resources)(nil),                // 15: sensetime.core.network.vpc.v1.Resources
	(*BillingItems)(nil),             // 16: sensetime.core.network.vpc.v1.BillingItems
	nil,                              // 17: sensetime.core.network.vpc.v1.VpcPeeringCreate.TagsEntry
	nil,                              // 18: sensetime.core.network.vpc.v1.VpcPeering.TagsEntry
	(*v1.OrderInfo)(nil),             // 19: sensetime.core.higgs.common.v1.OrderInfo
	(*timestamppb.Timestamp)(nil),    // 20: google.protobuf.Timestamp
	(*fieldmaskpb.FieldMask)(nil),    // 21: google.protobuf.FieldMask
	(*emptypb.Empty)(nil),            // 22: google.protobuf.Empty
}
var file_network_vpc_v1_vpc_peering_proto_depIdxs = []int32{
	2,  // 0: sensetime.core.network.vpc.v1.CreateVpcPeeringRequest.vpc_peering:type_name -> sensetime.core.network.vpc.v1.VpcPeeringCreate
	0,  // 1: sensetime.core.network.vpc.v1.VpcPeeringCreate.state:type_name -> sensetime.core.network.vpc.v1.State
	17, // 2: sensetime.core.network.vpc.v1.VpcPeeringCreate.tags:type_name -> sensetime.core.network.vpc.v1.VpcPeeringCreate.TagsEntry
	14, // 3: sensetime.core.network.vpc.v1.VpcPeeringCreate.properties:type_name -> sensetime.core.network.vpc.v1.VpcPeeringProperties
	19, // 4: sensetime.core.network.vpc.v1.VpcPeeringCreate.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	20, // 5: sensetime.core.network.vpc.v1.VpcPeeringCreate.create_time:type_name -> google.protobuf.Timestamp
	20, // 6: sensetime.core.network.vpc.v1.VpcPeeringCreate.update_time:type_name -> google.protobuf.Timestamp
	6,  // 7: sensetime.core.network.vpc.v1.UpdateVpcPeeringRequest.vpc_peering:type_name -> sensetime.core.network.vpc.v1.VpcPeeringUpdate
	21, // 8: sensetime.core.network.vpc.v1.UpdateVpcPeeringRequest.update_mask:type_name -> google.protobuf.FieldMask
	13, // 9: sensetime.core.network.vpc.v1.ListVpcPeeringResponse.vpc_peerings:type_name -> sensetime.core.network.vpc.v1.VpcPeering
	11, // 10: sensetime.core.network.vpc.v1.ResizeVpcPeeringRequest.vpc_peering_resize:type_name -> sensetime.core.network.vpc.v1.VpcPeeringResize
	14, // 11: sensetime.core.network.vpc.v1.VpcPeeringResize.properties:type_name -> sensetime.core.network.vpc.v1.VpcPeeringProperties
	19, // 12: sensetime.core.network.vpc.v1.VpcPeeringResize.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	0,  // 13: sensetime.core.network.vpc.v1.VpcPeering.state:type_name -> sensetime.core.network.vpc.v1.State
	18, // 14: sensetime.core.network.vpc.v1.VpcPeering.tags:type_name -> sensetime.core.network.vpc.v1.VpcPeering.TagsEntry
	14, // 15: sensetime.core.network.vpc.v1.VpcPeering.properties:type_name -> sensetime.core.network.vpc.v1.VpcPeeringProperties
	19, // 16: sensetime.core.network.vpc.v1.VpcPeering.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	20, // 17: sensetime.core.network.vpc.v1.VpcPeering.create_time:type_name -> google.protobuf.Timestamp
	20, // 18: sensetime.core.network.vpc.v1.VpcPeering.update_time:type_name -> google.protobuf.Timestamp
	15, // 19: sensetime.core.network.vpc.v1.VpcPeeringProperties.resources:type_name -> sensetime.core.network.vpc.v1.Resources
	16, // 20: sensetime.core.network.vpc.v1.Resources.billing_items:type_name -> sensetime.core.network.vpc.v1.BillingItems
	1,  // 21: sensetime.core.network.vpc.v1.VpcPeerings.CreateVpcPeering:input_type -> sensetime.core.network.vpc.v1.CreateVpcPeeringRequest
	4,  // 22: sensetime.core.network.vpc.v1.VpcPeerings.DeleteVpcPeering:input_type -> sensetime.core.network.vpc.v1.DeleteVpcPeeringRequest
	5,  // 23: sensetime.core.network.vpc.v1.VpcPeerings.UpdateVpcPeering:input_type -> sensetime.core.network.vpc.v1.UpdateVpcPeeringRequest
	7,  // 24: sensetime.core.network.vpc.v1.VpcPeerings.GetVpcPeering:input_type -> sensetime.core.network.vpc.v1.GetVpcPeeringRequest
	8,  // 25: sensetime.core.network.vpc.v1.VpcPeerings.ListVpcPeering:input_type -> sensetime.core.network.vpc.v1.ListVpcPeeringRequest
	10, // 26: sensetime.core.network.vpc.v1.VpcPeerings.ResizeVpcPeering:input_type -> sensetime.core.network.vpc.v1.ResizeVpcPeeringRequest
	12, // 27: sensetime.core.network.vpc.v1.VpcPeerings.ReleaseVpcPeering:input_type -> sensetime.core.network.vpc.v1.ReleaseVpcPeeringRequest
	13, // 28: sensetime.core.network.vpc.v1.VpcPeerings.CreateVpcPeering:output_type -> sensetime.core.network.vpc.v1.VpcPeering
	22, // 29: sensetime.core.network.vpc.v1.VpcPeerings.DeleteVpcPeering:output_type -> google.protobuf.Empty
	13, // 30: sensetime.core.network.vpc.v1.VpcPeerings.UpdateVpcPeering:output_type -> sensetime.core.network.vpc.v1.VpcPeering
	13, // 31: sensetime.core.network.vpc.v1.VpcPeerings.GetVpcPeering:output_type -> sensetime.core.network.vpc.v1.VpcPeering
	9,  // 32: sensetime.core.network.vpc.v1.VpcPeerings.ListVpcPeering:output_type -> sensetime.core.network.vpc.v1.ListVpcPeeringResponse
	13, // 33: sensetime.core.network.vpc.v1.VpcPeerings.ResizeVpcPeering:output_type -> sensetime.core.network.vpc.v1.VpcPeering
	22, // 34: sensetime.core.network.vpc.v1.VpcPeerings.ReleaseVpcPeering:output_type -> google.protobuf.Empty
	28, // [28:35] is the sub-list for method output_type
	21, // [21:28] is the sub-list for method input_type
	21, // [21:21] is the sub-list for extension type_name
	21, // [21:21] is the sub-list for extension extendee
	0,  // [0:21] is the sub-list for field type_name
}

func init() { file_network_vpc_v1_vpc_peering_proto_init() }
func file_network_vpc_v1_vpc_peering_proto_init() {
	if File_network_vpc_v1_vpc_peering_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_network_vpc_v1_vpc_peering_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateVpcPeeringRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_peering_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VpcPeeringCreate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_peering_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VpcPeeringProperty); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_peering_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteVpcPeeringRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_peering_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVpcPeeringRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_peering_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VpcPeeringUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_peering_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVpcPeeringRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_peering_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListVpcPeeringRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_peering_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListVpcPeeringResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_peering_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResizeVpcPeeringRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_peering_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VpcPeeringResize); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_peering_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleaseVpcPeeringRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_peering_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VpcPeering); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_peering_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VpcPeeringProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_peering_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Resources); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_peering_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BillingItems); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_network_vpc_v1_vpc_peering_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_vpc_v1_vpc_peering_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   18,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_network_vpc_v1_vpc_peering_proto_goTypes,
		DependencyIndexes: file_network_vpc_v1_vpc_peering_proto_depIdxs,
		EnumInfos:         file_network_vpc_v1_vpc_peering_proto_enumTypes,
		MessageInfos:      file_network_vpc_v1_vpc_peering_proto_msgTypes,
	}.Build()
	File_network_vpc_v1_vpc_peering_proto = out.File
	file_network_vpc_v1_vpc_peering_proto_rawDesc = nil
	file_network_vpc_v1_vpc_peering_proto_goTypes = nil
	file_network_vpc_v1_vpc_peering_proto_depIdxs = nil
}
