// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/vpc/v1/vpc_peering.proto

package vpc

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateVpcPeeringRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateVpcPeeringRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateVpcPeeringRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateVpcPeeringRequestMultiError, or nil if none found.
func (m *CreateVpcPeeringRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVpcPeeringRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetVpcPeeringName()) > 63 {
		err := CreateVpcPeeringRequestValidationError{
			field:  "VpcPeeringName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateVpcPeeringRequest_VpcPeeringName_Pattern.MatchString(m.GetVpcPeeringName()) {
		err := CreateVpcPeeringRequestValidationError{
			field:  "VpcPeeringName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetVpcPeering()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVpcPeeringRequestValidationError{
					field:  "VpcPeering",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVpcPeeringRequestValidationError{
					field:  "VpcPeering",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVpcPeering()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVpcPeeringRequestValidationError{
				field:  "VpcPeering",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateVpcPeeringRequestMultiError(errors)
	}

	return nil
}

// CreateVpcPeeringRequestMultiError is an error wrapping multiple validation
// errors returned by CreateVpcPeeringRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateVpcPeeringRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVpcPeeringRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVpcPeeringRequestMultiError) AllErrors() []error { return m }

// CreateVpcPeeringRequestValidationError is the validation error returned by
// CreateVpcPeeringRequest.Validate if the designated constraints aren't met.
type CreateVpcPeeringRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVpcPeeringRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateVpcPeeringRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateVpcPeeringRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateVpcPeeringRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateVpcPeeringRequestValidationError) ErrorName() string {
	return "CreateVpcPeeringRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateVpcPeeringRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVpcPeeringRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVpcPeeringRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVpcPeeringRequestValidationError{}

var _CreateVpcPeeringRequest_VpcPeeringName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on VpcPeeringCreate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VpcPeeringCreate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VpcPeeringCreate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VpcPeeringCreateMultiError, or nil if none found.
func (m *VpcPeeringCreate) ValidateAll() error {
	return m.validate(true)
}

func (m *VpcPeeringCreate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Uid

	// no validation rules for Name

	if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
		err := VpcPeeringCreateValidationError{
			field:  "DisplayName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_VpcPeeringCreate_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
		err := VpcPeeringCreateValidationError{
			field:  "DisplayName",
			reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpcPeeringCreateValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpcPeeringCreateValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpcPeeringCreateValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpcPeeringCreateValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpcPeeringCreateValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpcPeeringCreateValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpcPeeringCreateValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpcPeeringCreateValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpcPeeringCreateValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpcPeeringCreateValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpcPeeringCreateValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpcPeeringCreateValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VpcPeeringCreateMultiError(errors)
	}

	return nil
}

// VpcPeeringCreateMultiError is an error wrapping multiple validation errors
// returned by VpcPeeringCreate.ValidateAll() if the designated constraints
// aren't met.
type VpcPeeringCreateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VpcPeeringCreateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VpcPeeringCreateMultiError) AllErrors() []error { return m }

// VpcPeeringCreateValidationError is the validation error returned by
// VpcPeeringCreate.Validate if the designated constraints aren't met.
type VpcPeeringCreateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VpcPeeringCreateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VpcPeeringCreateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VpcPeeringCreateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VpcPeeringCreateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VpcPeeringCreateValidationError) ErrorName() string { return "VpcPeeringCreateValidationError" }

// Error satisfies the builtin error interface
func (e VpcPeeringCreateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVpcPeeringCreate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VpcPeeringCreateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VpcPeeringCreateValidationError{}

var _VpcPeeringCreate_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on VpcPeeringProperty with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VpcPeeringProperty) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VpcPeeringProperty with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VpcPeeringPropertyMultiError, or nil if none found.
func (m *VpcPeeringProperty) ValidateAll() error {
	return m.validate(true)
}

func (m *VpcPeeringProperty) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return VpcPeeringPropertyMultiError(errors)
	}

	return nil
}

// VpcPeeringPropertyMultiError is an error wrapping multiple validation errors
// returned by VpcPeeringProperty.ValidateAll() if the designated constraints
// aren't met.
type VpcPeeringPropertyMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VpcPeeringPropertyMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VpcPeeringPropertyMultiError) AllErrors() []error { return m }

// VpcPeeringPropertyValidationError is the validation error returned by
// VpcPeeringProperty.Validate if the designated constraints aren't met.
type VpcPeeringPropertyValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VpcPeeringPropertyValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VpcPeeringPropertyValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VpcPeeringPropertyValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VpcPeeringPropertyValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VpcPeeringPropertyValidationError) ErrorName() string {
	return "VpcPeeringPropertyValidationError"
}

// Error satisfies the builtin error interface
func (e VpcPeeringPropertyValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVpcPeeringProperty.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VpcPeeringPropertyValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VpcPeeringPropertyValidationError{}

// Validate checks the field values on DeleteVpcPeeringRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteVpcPeeringRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteVpcPeeringRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteVpcPeeringRequestMultiError, or nil if none found.
func (m *DeleteVpcPeeringRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteVpcPeeringRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for VpcPeeringName

	if len(errors) > 0 {
		return DeleteVpcPeeringRequestMultiError(errors)
	}

	return nil
}

// DeleteVpcPeeringRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteVpcPeeringRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteVpcPeeringRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteVpcPeeringRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteVpcPeeringRequestMultiError) AllErrors() []error { return m }

// DeleteVpcPeeringRequestValidationError is the validation error returned by
// DeleteVpcPeeringRequest.Validate if the designated constraints aren't met.
type DeleteVpcPeeringRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteVpcPeeringRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteVpcPeeringRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteVpcPeeringRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteVpcPeeringRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteVpcPeeringRequestValidationError) ErrorName() string {
	return "DeleteVpcPeeringRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteVpcPeeringRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteVpcPeeringRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteVpcPeeringRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteVpcPeeringRequestValidationError{}

// Validate checks the field values on UpdateVpcPeeringRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateVpcPeeringRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateVpcPeeringRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateVpcPeeringRequestMultiError, or nil if none found.
func (m *UpdateVpcPeeringRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateVpcPeeringRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetVpcPeeringName()) > 63 {
		err := UpdateVpcPeeringRequestValidationError{
			field:  "VpcPeeringName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UpdateVpcPeeringRequest_VpcPeeringName_Pattern.MatchString(m.GetVpcPeeringName()) {
		err := UpdateVpcPeeringRequestValidationError{
			field:  "VpcPeeringName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetVpcPeering()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateVpcPeeringRequestValidationError{
					field:  "VpcPeering",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateVpcPeeringRequestValidationError{
					field:  "VpcPeering",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVpcPeering()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateVpcPeeringRequestValidationError{
				field:  "VpcPeering",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateVpcPeeringRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateVpcPeeringRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateVpcPeeringRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateVpcPeeringRequestMultiError(errors)
	}

	return nil
}

// UpdateVpcPeeringRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateVpcPeeringRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateVpcPeeringRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateVpcPeeringRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateVpcPeeringRequestMultiError) AllErrors() []error { return m }

// UpdateVpcPeeringRequestValidationError is the validation error returned by
// UpdateVpcPeeringRequest.Validate if the designated constraints aren't met.
type UpdateVpcPeeringRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateVpcPeeringRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateVpcPeeringRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateVpcPeeringRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateVpcPeeringRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateVpcPeeringRequestValidationError) ErrorName() string {
	return "UpdateVpcPeeringRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateVpcPeeringRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateVpcPeeringRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateVpcPeeringRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateVpcPeeringRequestValidationError{}

var _UpdateVpcPeeringRequest_VpcPeeringName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on VpcPeeringUpdate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VpcPeeringUpdate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VpcPeeringUpdate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VpcPeeringUpdateMultiError, or nil if none found.
func (m *VpcPeeringUpdate) ValidateAll() error {
	return m.validate(true)
}

func (m *VpcPeeringUpdate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Description

	if m.DisplayName != nil {

		if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
			err := VpcPeeringUpdateValidationError{
				field:  "DisplayName",
				reason: "value length must be at most 63 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_VpcPeeringUpdate_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
			err := VpcPeeringUpdateValidationError{
				field:  "DisplayName",
				reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return VpcPeeringUpdateMultiError(errors)
	}

	return nil
}

// VpcPeeringUpdateMultiError is an error wrapping multiple validation errors
// returned by VpcPeeringUpdate.ValidateAll() if the designated constraints
// aren't met.
type VpcPeeringUpdateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VpcPeeringUpdateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VpcPeeringUpdateMultiError) AllErrors() []error { return m }

// VpcPeeringUpdateValidationError is the validation error returned by
// VpcPeeringUpdate.Validate if the designated constraints aren't met.
type VpcPeeringUpdateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VpcPeeringUpdateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VpcPeeringUpdateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VpcPeeringUpdateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VpcPeeringUpdateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VpcPeeringUpdateValidationError) ErrorName() string { return "VpcPeeringUpdateValidationError" }

// Error satisfies the builtin error interface
func (e VpcPeeringUpdateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVpcPeeringUpdate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VpcPeeringUpdateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VpcPeeringUpdateValidationError{}

var _VpcPeeringUpdate_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on GetVpcPeeringRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetVpcPeeringRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVpcPeeringRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVpcPeeringRequestMultiError, or nil if none found.
func (m *GetVpcPeeringRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVpcPeeringRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for VpcPeeringName

	if len(errors) > 0 {
		return GetVpcPeeringRequestMultiError(errors)
	}

	return nil
}

// GetVpcPeeringRequestMultiError is an error wrapping multiple validation
// errors returned by GetVpcPeeringRequest.ValidateAll() if the designated
// constraints aren't met.
type GetVpcPeeringRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVpcPeeringRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVpcPeeringRequestMultiError) AllErrors() []error { return m }

// GetVpcPeeringRequestValidationError is the validation error returned by
// GetVpcPeeringRequest.Validate if the designated constraints aren't met.
type GetVpcPeeringRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVpcPeeringRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVpcPeeringRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVpcPeeringRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVpcPeeringRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVpcPeeringRequestValidationError) ErrorName() string {
	return "GetVpcPeeringRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetVpcPeeringRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVpcPeeringRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVpcPeeringRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVpcPeeringRequestValidationError{}

// Validate checks the field values on ListVpcPeeringRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListVpcPeeringRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListVpcPeeringRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListVpcPeeringRequestMultiError, or nil if none found.
func (m *ListVpcPeeringRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListVpcPeeringRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if len(errors) > 0 {
		return ListVpcPeeringRequestMultiError(errors)
	}

	return nil
}

// ListVpcPeeringRequestMultiError is an error wrapping multiple validation
// errors returned by ListVpcPeeringRequest.ValidateAll() if the designated
// constraints aren't met.
type ListVpcPeeringRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListVpcPeeringRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListVpcPeeringRequestMultiError) AllErrors() []error { return m }

// ListVpcPeeringRequestValidationError is the validation error returned by
// ListVpcPeeringRequest.Validate if the designated constraints aren't met.
type ListVpcPeeringRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListVpcPeeringRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListVpcPeeringRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListVpcPeeringRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListVpcPeeringRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListVpcPeeringRequestValidationError) ErrorName() string {
	return "ListVpcPeeringRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListVpcPeeringRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListVpcPeeringRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListVpcPeeringRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListVpcPeeringRequestValidationError{}

// Validate checks the field values on ListVpcPeeringResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListVpcPeeringResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListVpcPeeringResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListVpcPeeringResponseMultiError, or nil if none found.
func (m *ListVpcPeeringResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListVpcPeeringResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetVpcPeerings() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListVpcPeeringResponseValidationError{
						field:  fmt.Sprintf("VpcPeerings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListVpcPeeringResponseValidationError{
						field:  fmt.Sprintf("VpcPeerings[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListVpcPeeringResponseValidationError{
					field:  fmt.Sprintf("VpcPeerings[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListVpcPeeringResponseMultiError(errors)
	}

	return nil
}

// ListVpcPeeringResponseMultiError is an error wrapping multiple validation
// errors returned by ListVpcPeeringResponse.ValidateAll() if the designated
// constraints aren't met.
type ListVpcPeeringResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListVpcPeeringResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListVpcPeeringResponseMultiError) AllErrors() []error { return m }

// ListVpcPeeringResponseValidationError is the validation error returned by
// ListVpcPeeringResponse.Validate if the designated constraints aren't met.
type ListVpcPeeringResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListVpcPeeringResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListVpcPeeringResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListVpcPeeringResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListVpcPeeringResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListVpcPeeringResponseValidationError) ErrorName() string {
	return "ListVpcPeeringResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListVpcPeeringResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListVpcPeeringResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListVpcPeeringResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListVpcPeeringResponseValidationError{}

// Validate checks the field values on ResizeVpcPeeringRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ResizeVpcPeeringRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResizeVpcPeeringRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResizeVpcPeeringRequestMultiError, or nil if none found.
func (m *ResizeVpcPeeringRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResizeVpcPeeringRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetVpcPeeringName()) > 63 {
		err := ResizeVpcPeeringRequestValidationError{
			field:  "VpcPeeringName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ResizeVpcPeeringRequest_VpcPeeringName_Pattern.MatchString(m.GetVpcPeeringName()) {
		err := ResizeVpcPeeringRequestValidationError{
			field:  "VpcPeeringName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetVpcPeeringResize()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResizeVpcPeeringRequestValidationError{
					field:  "VpcPeeringResize",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResizeVpcPeeringRequestValidationError{
					field:  "VpcPeeringResize",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVpcPeeringResize()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResizeVpcPeeringRequestValidationError{
				field:  "VpcPeeringResize",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResizeVpcPeeringRequestMultiError(errors)
	}

	return nil
}

// ResizeVpcPeeringRequestMultiError is an error wrapping multiple validation
// errors returned by ResizeVpcPeeringRequest.ValidateAll() if the designated
// constraints aren't met.
type ResizeVpcPeeringRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResizeVpcPeeringRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResizeVpcPeeringRequestMultiError) AllErrors() []error { return m }

// ResizeVpcPeeringRequestValidationError is the validation error returned by
// ResizeVpcPeeringRequest.Validate if the designated constraints aren't met.
type ResizeVpcPeeringRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResizeVpcPeeringRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResizeVpcPeeringRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResizeVpcPeeringRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResizeVpcPeeringRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResizeVpcPeeringRequestValidationError) ErrorName() string {
	return "ResizeVpcPeeringRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResizeVpcPeeringRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResizeVpcPeeringRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResizeVpcPeeringRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResizeVpcPeeringRequestValidationError{}

var _ResizeVpcPeeringRequest_VpcPeeringName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on VpcPeeringResize with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *VpcPeeringResize) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VpcPeeringResize with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VpcPeeringResizeMultiError, or nil if none found.
func (m *VpcPeeringResize) ValidateAll() error {
	return m.validate(true)
}

func (m *VpcPeeringResize) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ResourceId

	// no validation rules for SkuId

	// no validation rules for OperatorId

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpcPeeringResizeValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpcPeeringResizeValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpcPeeringResizeValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpcPeeringResizeValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpcPeeringResizeValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpcPeeringResizeValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VpcPeeringResizeMultiError(errors)
	}

	return nil
}

// VpcPeeringResizeMultiError is an error wrapping multiple validation errors
// returned by VpcPeeringResize.ValidateAll() if the designated constraints
// aren't met.
type VpcPeeringResizeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VpcPeeringResizeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VpcPeeringResizeMultiError) AllErrors() []error { return m }

// VpcPeeringResizeValidationError is the validation error returned by
// VpcPeeringResize.Validate if the designated constraints aren't met.
type VpcPeeringResizeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VpcPeeringResizeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VpcPeeringResizeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VpcPeeringResizeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VpcPeeringResizeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VpcPeeringResizeValidationError) ErrorName() string { return "VpcPeeringResizeValidationError" }

// Error satisfies the builtin error interface
func (e VpcPeeringResizeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVpcPeeringResize.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VpcPeeringResizeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VpcPeeringResizeValidationError{}

// Validate checks the field values on ReleaseVpcPeeringRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReleaseVpcPeeringRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReleaseVpcPeeringRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReleaseVpcPeeringRequestMultiError, or nil if none found.
func (m *ReleaseVpcPeeringRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReleaseVpcPeeringRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetVpcPeeringName()) > 63 {
		err := ReleaseVpcPeeringRequestValidationError{
			field:  "VpcPeeringName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ReleaseVpcPeeringRequest_VpcPeeringName_Pattern.MatchString(m.GetVpcPeeringName()) {
		err := ReleaseVpcPeeringRequestValidationError{
			field:  "VpcPeeringName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ReleaseVpcPeeringRequestMultiError(errors)
	}

	return nil
}

// ReleaseVpcPeeringRequestMultiError is an error wrapping multiple validation
// errors returned by ReleaseVpcPeeringRequest.ValidateAll() if the designated
// constraints aren't met.
type ReleaseVpcPeeringRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReleaseVpcPeeringRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReleaseVpcPeeringRequestMultiError) AllErrors() []error { return m }

// ReleaseVpcPeeringRequestValidationError is the validation error returned by
// ReleaseVpcPeeringRequest.Validate if the designated constraints aren't met.
type ReleaseVpcPeeringRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReleaseVpcPeeringRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReleaseVpcPeeringRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReleaseVpcPeeringRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReleaseVpcPeeringRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReleaseVpcPeeringRequestValidationError) ErrorName() string {
	return "ReleaseVpcPeeringRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReleaseVpcPeeringRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReleaseVpcPeeringRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReleaseVpcPeeringRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReleaseVpcPeeringRequestValidationError{}

var _ReleaseVpcPeeringRequest_VpcPeeringName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on VpcPeering with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VpcPeering) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VpcPeering with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VpcPeeringMultiError, or
// nil if none found.
func (m *VpcPeering) ValidateAll() error {
	return m.validate(true)
}

func (m *VpcPeering) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Uid

	// no validation rules for Name

	if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
		err := VpcPeeringValidationError{
			field:  "DisplayName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_VpcPeering_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
		err := VpcPeeringValidationError{
			field:  "DisplayName",
			reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpcPeeringValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpcPeeringValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpcPeeringValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpcPeeringValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpcPeeringValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpcPeeringValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpcPeeringValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpcPeeringValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpcPeeringValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpcPeeringValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpcPeeringValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpcPeeringValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VpcPeeringMultiError(errors)
	}

	return nil
}

// VpcPeeringMultiError is an error wrapping multiple validation errors
// returned by VpcPeering.ValidateAll() if the designated constraints aren't met.
type VpcPeeringMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VpcPeeringMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VpcPeeringMultiError) AllErrors() []error { return m }

// VpcPeeringValidationError is the validation error returned by
// VpcPeering.Validate if the designated constraints aren't met.
type VpcPeeringValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VpcPeeringValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VpcPeeringValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VpcPeeringValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VpcPeeringValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VpcPeeringValidationError) ErrorName() string { return "VpcPeeringValidationError" }

// Error satisfies the builtin error interface
func (e VpcPeeringValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVpcPeering.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VpcPeeringValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VpcPeeringValidationError{}

var _VpcPeering_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on VpcPeeringProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VpcPeeringProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VpcPeeringProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VpcPeeringPropertiesMultiError, or nil if none found.
func (m *VpcPeeringProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *VpcPeeringProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResources()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VpcPeeringPropertiesValidationError{
					field:  "Resources",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VpcPeeringPropertiesValidationError{
					field:  "Resources",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResources()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VpcPeeringPropertiesValidationError{
				field:  "Resources",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Vpc

	// no validation rules for Type

	// no validation rules for TransitSwitch

	// no validation rules for LocalGatewayIp

	// no validation rules for RemoteGatewayIp

	// no validation rules for RemoteVpc

	// no validation rules for RemoteZone

	if len(errors) > 0 {
		return VpcPeeringPropertiesMultiError(errors)
	}

	return nil
}

// VpcPeeringPropertiesMultiError is an error wrapping multiple validation
// errors returned by VpcPeeringProperties.ValidateAll() if the designated
// constraints aren't met.
type VpcPeeringPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VpcPeeringPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VpcPeeringPropertiesMultiError) AllErrors() []error { return m }

// VpcPeeringPropertiesValidationError is the validation error returned by
// VpcPeeringProperties.Validate if the designated constraints aren't met.
type VpcPeeringPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VpcPeeringPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VpcPeeringPropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VpcPeeringPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VpcPeeringPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VpcPeeringPropertiesValidationError) ErrorName() string {
	return "VpcPeeringPropertiesValidationError"
}

// Error satisfies the builtin error interface
func (e VpcPeeringPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVpcPeeringProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VpcPeeringPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VpcPeeringPropertiesValidationError{}

// Validate checks the field values on Resources with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Resources) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Resources with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ResourcesMultiError, or nil
// if none found.
func (m *Resources) ValidateAll() error {
	return m.validate(true)
}

func (m *Resources) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBillingItems()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResourcesValidationError{
					field:  "BillingItems",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResourcesValidationError{
					field:  "BillingItems",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBillingItems()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResourcesValidationError{
				field:  "BillingItems",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResourcesMultiError(errors)
	}

	return nil
}

// ResourcesMultiError is an error wrapping multiple validation errors returned
// by Resources.ValidateAll() if the designated constraints aren't met.
type ResourcesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourcesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourcesMultiError) AllErrors() []error { return m }

// ResourcesValidationError is the validation error returned by
// Resources.Validate if the designated constraints aren't met.
type ResourcesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourcesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourcesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourcesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourcesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourcesValidationError) ErrorName() string { return "ResourcesValidationError" }

// Error satisfies the builtin error interface
func (e ResourcesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResources.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourcesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourcesValidationError{}

// Validate checks the field values on BillingItems with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BillingItems) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BillingItems with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BillingItemsMultiError, or
// nil if none found.
func (m *BillingItems) ValidateAll() error {
	return m.validate(true)
}

func (m *BillingItems) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BillingItemsMultiError(errors)
	}

	return nil
}

// BillingItemsMultiError is an error wrapping multiple validation errors
// returned by BillingItems.ValidateAll() if the designated constraints aren't met.
type BillingItemsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BillingItemsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BillingItemsMultiError) AllErrors() []error { return m }

// BillingItemsValidationError is the validation error returned by
// BillingItems.Validate if the designated constraints aren't met.
type BillingItemsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BillingItemsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BillingItemsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BillingItemsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BillingItemsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BillingItemsValidationError) ErrorName() string { return "BillingItemsValidationError" }

// Error satisfies the builtin error interface
func (e BillingItemsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBillingItems.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BillingItemsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BillingItemsValidationError{}
