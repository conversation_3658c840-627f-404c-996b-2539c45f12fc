// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/vpc/v1/subnet.proto

package vpc

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListSubnetsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSubnetsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSubnetsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSubnetsRequestMultiError, or nil if none found.
func (m *ListSubnetsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSubnetsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	if len(errors) > 0 {
		return ListSubnetsRequestMultiError(errors)
	}

	return nil
}

// ListSubnetsRequestMultiError is an error wrapping multiple validation errors
// returned by ListSubnetsRequest.ValidateAll() if the designated constraints
// aren't met.
type ListSubnetsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSubnetsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSubnetsRequestMultiError) AllErrors() []error { return m }

// ListSubnetsRequestValidationError is the validation error returned by
// ListSubnetsRequest.Validate if the designated constraints aren't met.
type ListSubnetsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSubnetsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSubnetsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSubnetsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSubnetsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSubnetsRequestValidationError) ErrorName() string {
	return "ListSubnetsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListSubnetsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSubnetsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSubnetsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSubnetsRequestValidationError{}

// Validate checks the field values on GetSubnetRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetSubnetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSubnetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSubnetRequestMultiError, or nil if none found.
func (m *GetSubnetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSubnetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for SubnetName

	if len(errors) > 0 {
		return GetSubnetRequestMultiError(errors)
	}

	return nil
}

// GetSubnetRequestMultiError is an error wrapping multiple validation errors
// returned by GetSubnetRequest.ValidateAll() if the designated constraints
// aren't met.
type GetSubnetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSubnetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSubnetRequestMultiError) AllErrors() []error { return m }

// GetSubnetRequestValidationError is the validation error returned by
// GetSubnetRequest.Validate if the designated constraints aren't met.
type GetSubnetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSubnetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSubnetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSubnetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSubnetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSubnetRequestValidationError) ErrorName() string { return "GetSubnetRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetSubnetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSubnetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSubnetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSubnetRequestValidationError{}

// Validate checks the field values on CreateSubnetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSubnetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSubnetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSubnetRequestMultiError, or nil if none found.
func (m *CreateSubnetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSubnetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSubnetName()) > 63 {
		err := CreateSubnetRequestValidationError{
			field:  "SubnetName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateSubnetRequest_SubnetName_Pattern.MatchString(m.GetSubnetName()) {
		err := CreateSubnetRequestValidationError{
			field:  "SubnetName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSubnet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSubnetRequestValidationError{
					field:  "Subnet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSubnetRequestValidationError{
					field:  "Subnet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubnet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSubnetRequestValidationError{
				field:  "Subnet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateSubnetRequestMultiError(errors)
	}

	return nil
}

// CreateSubnetRequestMultiError is an error wrapping multiple validation
// errors returned by CreateSubnetRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateSubnetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSubnetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSubnetRequestMultiError) AllErrors() []error { return m }

// CreateSubnetRequestValidationError is the validation error returned by
// CreateSubnetRequest.Validate if the designated constraints aren't met.
type CreateSubnetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSubnetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSubnetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSubnetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSubnetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSubnetRequestValidationError) ErrorName() string {
	return "CreateSubnetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSubnetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSubnetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSubnetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSubnetRequestValidationError{}

var _CreateSubnetRequest_SubnetName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on UpdateSubnetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateSubnetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSubnetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateSubnetRequestMultiError, or nil if none found.
func (m *UpdateSubnetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSubnetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for SubnetName

	if all {
		switch v := interface{}(m.GetSubnet()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSubnetRequestValidationError{
					field:  "Subnet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSubnetRequestValidationError{
					field:  "Subnet",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSubnet()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSubnetRequestValidationError{
				field:  "Subnet",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateSubnetRequestMultiError(errors)
	}

	return nil
}

// UpdateSubnetRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateSubnetRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateSubnetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSubnetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSubnetRequestMultiError) AllErrors() []error { return m }

// UpdateSubnetRequestValidationError is the validation error returned by
// UpdateSubnetRequest.Validate if the designated constraints aren't met.
type UpdateSubnetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSubnetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSubnetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSubnetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSubnetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSubnetRequestValidationError) ErrorName() string {
	return "UpdateSubnetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateSubnetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSubnetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSubnetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSubnetRequestValidationError{}

// Validate checks the field values on DeleteSubnetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteSubnetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSubnetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteSubnetRequestMultiError, or nil if none found.
func (m *DeleteSubnetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSubnetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for SubnetName

	if len(errors) > 0 {
		return DeleteSubnetRequestMultiError(errors)
	}

	return nil
}

// DeleteSubnetRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteSubnetRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteSubnetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSubnetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSubnetRequestMultiError) AllErrors() []error { return m }

// DeleteSubnetRequestValidationError is the validation error returned by
// DeleteSubnetRequest.Validate if the designated constraints aren't met.
type DeleteSubnetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSubnetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSubnetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSubnetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSubnetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSubnetRequestValidationError) ErrorName() string {
	return "DeleteSubnetRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteSubnetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSubnetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSubnetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSubnetRequestValidationError{}

// Validate checks the field values on ListSubnetsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSubnetsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSubnetsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSubnetsResponseMultiError, or nil if none found.
func (m *ListSubnetsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSubnetsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetSubnets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListSubnetsResponseValidationError{
						field:  fmt.Sprintf("Subnets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListSubnetsResponseValidationError{
						field:  fmt.Sprintf("Subnets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListSubnetsResponseValidationError{
					field:  fmt.Sprintf("Subnets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListSubnetsResponseMultiError(errors)
	}

	return nil
}

// ListSubnetsResponseMultiError is an error wrapping multiple validation
// errors returned by ListSubnetsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListSubnetsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSubnetsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSubnetsResponseMultiError) AllErrors() []error { return m }

// ListSubnetsResponseValidationError is the validation error returned by
// ListSubnetsResponse.Validate if the designated constraints aren't met.
type ListSubnetsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSubnetsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSubnetsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSubnetsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSubnetsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSubnetsResponseValidationError) ErrorName() string {
	return "ListSubnetsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListSubnetsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSubnetsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSubnetsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSubnetsResponseValidationError{}

// Validate checks the field values on Subnet with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Subnet) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Subnet with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SubnetMultiError, or nil if none found.
func (m *Subnet) ValidateAll() error {
	return m.validate(true)
}

func (m *Subnet) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if m.GetDisplayName() != "" {

		if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
			err := SubnetValidationError{
				field:  "DisplayName",
				reason: "value length must be at most 63 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_Subnet_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
			err := SubnetValidationError{
				field:  "DisplayName",
				reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Description

	// no validation rules for Uid

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubnetValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubnetValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubnetValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubnetValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubnetValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubnetValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubnetValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubnetValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubnetValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SubnetValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SubnetValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SubnetValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SubnetMultiError(errors)
	}

	return nil
}

// SubnetMultiError is an error wrapping multiple validation errors returned by
// Subnet.ValidateAll() if the designated constraints aren't met.
type SubnetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubnetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubnetMultiError) AllErrors() []error { return m }

// SubnetValidationError is the validation error returned by Subnet.Validate if
// the designated constraints aren't met.
type SubnetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubnetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubnetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubnetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubnetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubnetValidationError) ErrorName() string { return "SubnetValidationError" }

// Error satisfies the builtin error interface
func (e SubnetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubnet.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubnetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubnetValidationError{}

var _Subnet_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on SubnetProperties with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *SubnetProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubnetProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SubnetPropertiesMultiError, or nil if none found.
func (m *SubnetProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *SubnetProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Scope

	// no validation rules for Provider

	// no validation rules for NetworkType

	// no validation rules for VpcId

	// no validation rules for Cidr

	// no validation rules for IsDefault

	// no validation rules for GatewayIp

	// no validation rules for CidrPoolId

	if len(errors) > 0 {
		return SubnetPropertiesMultiError(errors)
	}

	return nil
}

// SubnetPropertiesMultiError is an error wrapping multiple validation errors
// returned by SubnetProperties.ValidateAll() if the designated constraints
// aren't met.
type SubnetPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubnetPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubnetPropertiesMultiError) AllErrors() []error { return m }

// SubnetPropertiesValidationError is the validation error returned by
// SubnetProperties.Validate if the designated constraints aren't met.
type SubnetPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubnetPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubnetPropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubnetPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubnetPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubnetPropertiesValidationError) ErrorName() string { return "SubnetPropertiesValidationError" }

// Error satisfies the builtin error interface
func (e SubnetPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubnetProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubnetPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubnetPropertiesValidationError{}
