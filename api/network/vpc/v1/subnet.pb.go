// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/vpc/v1/subnet.proto

package vpc

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/api/annotations"
	v1 "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents the different states of a Subnet.
type Subnet_State int32

const (
	// The Subnet resource is being created.
	Subnet_CREATING Subnet_State = 0
	// The Subnet resource is being updated.
	Subnet_UPDATING Subnet_State = 1
	// The Subnet resource has been active.
	Subnet_ACTIVE Subnet_State = 2
	// The Subnet resource is being deleting.
	Subnet_DELETING Subnet_State = 3
	// The Subnet resource has been deleted.
	Subnet_DELETED Subnet_State = 4
	// The Subnet resource has been failed.
	Subnet_FAILED Subnet_State = 5
)

// Enum value maps for Subnet_State.
var (
	Subnet_State_name = map[int32]string{
		0: "CREATING",
		1: "UPDATING",
		2: "ACTIVE",
		3: "DELETING",
		4: "DELETED",
		5: "FAILED",
	}
	Subnet_State_value = map[string]int32{
		"CREATING": 0,
		"UPDATING": 1,
		"ACTIVE":   2,
		"DELETING": 3,
		"DELETED":  4,
		"FAILED":   5,
	}
)

func (x Subnet_State) Enum() *Subnet_State {
	p := new(Subnet_State)
	*p = x
	return p
}

func (x Subnet_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Subnet_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_vpc_v1_subnet_proto_enumTypes[0].Descriptor()
}

func (Subnet_State) Type() protoreflect.EnumType {
	return &file_network_vpc_v1_subnet_proto_enumTypes[0]
}

func (x Subnet_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Subnet_State.Descriptor instead.
func (Subnet_State) EnumDescriptor() ([]byte, []int) {
	return file_network_vpc_v1_subnet_proto_rawDescGZIP(), []int{6, 0}
}

// Subnet network scope.
type SubnetProperties_Scope int32

const (
	// Service network.
	SubnetProperties_SERVICE SubnetProperties_Scope = 0
	// Training network.
	SubnetProperties_TRAINING SubnetProperties_Scope = 1
	// Data network.
	SubnetProperties_DATA SubnetProperties_Scope = 2
	// VPC Gateway network
	SubnetProperties_VPCGW SubnetProperties_Scope = 3
	// Manage network.
	SubnetProperties_MANAGE SubnetProperties_Scope = 4
	// Storage network.
	SubnetProperties_STORAGE SubnetProperties_Scope = 5
)

// Enum value maps for SubnetProperties_Scope.
var (
	SubnetProperties_Scope_name = map[int32]string{
		0: "SERVICE",
		1: "TRAINING",
		2: "DATA",
		3: "VPCGW",
		4: "MANAGE",
		5: "STORAGE",
	}
	SubnetProperties_Scope_value = map[string]int32{
		"SERVICE":  0,
		"TRAINING": 1,
		"DATA":     2,
		"VPCGW":    3,
		"MANAGE":   4,
		"STORAGE":  5,
	}
)

func (x SubnetProperties_Scope) Enum() *SubnetProperties_Scope {
	p := new(SubnetProperties_Scope)
	*p = x
	return p
}

func (x SubnetProperties_Scope) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubnetProperties_Scope) Descriptor() protoreflect.EnumDescriptor {
	return file_network_vpc_v1_subnet_proto_enumTypes[1].Descriptor()
}

func (SubnetProperties_Scope) Type() protoreflect.EnumType {
	return &file_network_vpc_v1_subnet_proto_enumTypes[1]
}

func (x SubnetProperties_Scope) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubnetProperties_Scope.Descriptor instead.
func (SubnetProperties_Scope) EnumDescriptor() ([]byte, []int) {
	return file_network_vpc_v1_subnet_proto_rawDescGZIP(), []int{7, 0}
}

// Subnet provider.
type SubnetProperties_Provider int32

const (
	// Ovn support subnet.
	SubnetProperties_OVN SubnetProperties_Provider = 0
	// Boson controller support subnet.
	SubnetProperties_CONTROLLER SubnetProperties_Provider = 1
)

// Enum value maps for SubnetProperties_Provider.
var (
	SubnetProperties_Provider_name = map[int32]string{
		0: "OVN",
		1: "CONTROLLER",
	}
	SubnetProperties_Provider_value = map[string]int32{
		"OVN":        0,
		"CONTROLLER": 1,
	}
)

func (x SubnetProperties_Provider) Enum() *SubnetProperties_Provider {
	p := new(SubnetProperties_Provider)
	*p = x
	return p
}

func (x SubnetProperties_Provider) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubnetProperties_Provider) Descriptor() protoreflect.EnumDescriptor {
	return file_network_vpc_v1_subnet_proto_enumTypes[2].Descriptor()
}

func (SubnetProperties_Provider) Type() protoreflect.EnumType {
	return &file_network_vpc_v1_subnet_proto_enumTypes[2]
}

func (x SubnetProperties_Provider) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubnetProperties_Provider.Descriptor instead.
func (SubnetProperties_Provider) EnumDescriptor() ([]byte, []int) {
	return file_network_vpc_v1_subnet_proto_rawDescGZIP(), []int{7, 1}
}

// Subnet network type.
type SubnetProperties_NetworkType int32

const (
	// Geneve network.
	SubnetProperties_GENEVE SubnetProperties_NetworkType = 0
	// Vxlan network.
	SubnetProperties_VXLAN SubnetProperties_NetworkType = 1
	// Flat network.
	SubnetProperties_FLAT SubnetProperties_NetworkType = 2
	// Vlan network.
	SubnetProperties_VLAN SubnetProperties_NetworkType = 3
	// IB network.
	SubnetProperties_IB SubnetProperties_NetworkType = 4
)

// Enum value maps for SubnetProperties_NetworkType.
var (
	SubnetProperties_NetworkType_name = map[int32]string{
		0: "GENEVE",
		1: "VXLAN",
		2: "FLAT",
		3: "VLAN",
		4: "IB",
	}
	SubnetProperties_NetworkType_value = map[string]int32{
		"GENEVE": 0,
		"VXLAN":  1,
		"FLAT":   2,
		"VLAN":   3,
		"IB":     4,
	}
)

func (x SubnetProperties_NetworkType) Enum() *SubnetProperties_NetworkType {
	p := new(SubnetProperties_NetworkType)
	*p = x
	return p
}

func (x SubnetProperties_NetworkType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubnetProperties_NetworkType) Descriptor() protoreflect.EnumDescriptor {
	return file_network_vpc_v1_subnet_proto_enumTypes[3].Descriptor()
}

func (SubnetProperties_NetworkType) Type() protoreflect.EnumType {
	return &file_network_vpc_v1_subnet_proto_enumTypes[3]
}

func (x SubnetProperties_NetworkType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubnetProperties_NetworkType.Descriptor instead.
func (SubnetProperties_NetworkType) EnumDescriptor() ([]byte, []int) {
	return file_network_vpc_v1_subnet_proto_rawDescGZIP(), []int{7, 2}
}

// 列举 Subnets 的请求.
// [EN] Request to list Subnets.
type ListSubnetsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// List filter.
	Filter string `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// Sort resoults.
	OrderBy string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// The maximum number of items to return.
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// The next_page_token value returned from a previous List request, if any.
	PageToken string `protobuf:"bytes,7,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
}

func (x *ListSubnetsRequest) Reset() {
	*x = ListSubnetsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_subnet_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSubnetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSubnetsRequest) ProtoMessage() {}

func (x *ListSubnetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_subnet_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSubnetsRequest.ProtoReflect.Descriptor instead.
func (*ListSubnetsRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_subnet_proto_rawDescGZIP(), []int{0}
}

func (x *ListSubnetsRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListSubnetsRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListSubnetsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListSubnetsRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListSubnetsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListSubnetsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListSubnetsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// 获取某一个 Subnet 的请求.
// [EN] Request to get a Subnet.
type GetSubnetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The Subnet resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SubnetName string `protobuf:"bytes,4,opt,name=subnet_name,json=subnetName,proto3" json:"subnet_name,omitempty"`
}

func (x *GetSubnetRequest) Reset() {
	*x = GetSubnetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_subnet_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSubnetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSubnetRequest) ProtoMessage() {}

func (x *GetSubnetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_subnet_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSubnetRequest.ProtoReflect.Descriptor instead.
func (*GetSubnetRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_subnet_proto_rawDescGZIP(), []int{1}
}

func (x *GetSubnetRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetSubnetRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetSubnetRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetSubnetRequest) GetSubnetName() string {
	if x != nil {
		return x.SubnetName
	}
	return ""
}

// CreateSubnetRequest.
type CreateSubnetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone, todo: add validation
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The Subnet resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SubnetName string `protobuf:"bytes,4,opt,name=subnet_name,json=subnetName,proto3" json:"subnet_name,omitempty"`
	// The Subnet resource to create.
	Subnet *Subnet `protobuf:"bytes,5,opt,name=subnet,proto3" json:"subnet,omitempty"`
}

func (x *CreateSubnetRequest) Reset() {
	*x = CreateSubnetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_subnet_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSubnetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSubnetRequest) ProtoMessage() {}

func (x *CreateSubnetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_subnet_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSubnetRequest.ProtoReflect.Descriptor instead.
func (*CreateSubnetRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_subnet_proto_rawDescGZIP(), []int{2}
}

func (x *CreateSubnetRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *CreateSubnetRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *CreateSubnetRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateSubnetRequest) GetSubnetName() string {
	if x != nil {
		return x.SubnetName
	}
	return ""
}

func (x *CreateSubnetRequest) GetSubnet() *Subnet {
	if x != nil {
		return x.Subnet
	}
	return nil
}

// Subnet 可编辑字段.
// [EN] Subnet editable properties.
type UpdateSubnetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The Subnet resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SubnetName string `protobuf:"bytes,4,opt,name=subnet_name,json=subnetName,proto3" json:"subnet_name,omitempty"`
	// The Subnet resource to update.
	Subnet *Subnet `protobuf:"bytes,5,opt,name=subnet,proto3" json:"subnet,omitempty"`
}

func (x *UpdateSubnetRequest) Reset() {
	*x = UpdateSubnetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_subnet_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSubnetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSubnetRequest) ProtoMessage() {}

func (x *UpdateSubnetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_subnet_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSubnetRequest.ProtoReflect.Descriptor instead.
func (*UpdateSubnetRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_subnet_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateSubnetRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *UpdateSubnetRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *UpdateSubnetRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateSubnetRequest) GetSubnetName() string {
	if x != nil {
		return x.SubnetName
	}
	return ""
}

func (x *UpdateSubnetRequest) GetSubnet() *Subnet {
	if x != nil {
		return x.Subnet
	}
	return nil
}

// 删除某一个 Subnet 的请求.
// [EN] Request to delete a Subnet.
type DeleteSubnetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The Subnet resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SubnetName string `protobuf:"bytes,4,opt,name=subnet_name,json=subnetName,proto3" json:"subnet_name,omitempty"`
}

func (x *DeleteSubnetRequest) Reset() {
	*x = DeleteSubnetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_subnet_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSubnetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSubnetRequest) ProtoMessage() {}

func (x *DeleteSubnetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_subnet_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSubnetRequest.ProtoReflect.Descriptor instead.
func (*DeleteSubnetRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_subnet_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteSubnetRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DeleteSubnetRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *DeleteSubnetRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteSubnetRequest) GetSubnetName() string {
	if x != nil {
		return x.SubnetName
	}
	return ""
}

// 列举 Subnets 的响应.
// [EN] Response to list Subnets.
type ListSubnetsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subnet 列表.
	// [EN] Subnet list.
	Subnets []*Subnet `protobuf:"bytes,1,rep,name=Subnets,proto3" json:"Subnets,omitempty"`
	// 下一个页面的 token，如果没有更多数据则为空.
	// [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListSubnetsResponse) Reset() {
	*x = ListSubnetsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_subnet_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSubnetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSubnetsResponse) ProtoMessage() {}

func (x *ListSubnetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_subnet_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSubnetsResponse.ProtoReflect.Descriptor instead.
func (*ListSubnetsResponse) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_subnet_proto_rawDescGZIP(), []int{5}
}

func (x *ListSubnetsResponse) GetSubnets() []*Subnet {
	if x != nil {
		return x.Subnets
	}
	return nil
}

func (x *ListSubnetsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListSubnetsResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// Subnet 实例结构体.
// [EN] Subnet entity.
type Subnet struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The Subnet resource id using the form:
	//     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/subnets/{subnet_name}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The Subnet resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// ContainerInstance resource display name.
	DisplayName string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// ContainerInstance resource description.
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// The Subnet resource uuid.
	Uid string `protobuf:"bytes,5,opt,name=uid,proto3" json:"uid,omitempty"`
	// The Subnet resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// The id of the user who created the Subnet resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// The id of the user who owns the Subnet resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// Available zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// The current state of the Subnet resource.
	State Subnet_State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.vpc.v1.Subnet_State" json:"state,omitempty"`
	// Sku id.
	SkuId string `protobuf:"bytes,12,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// Tags attached to the Subnet resource.
	Tags map[string]string `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Properties of the Subnet resource.
	Properties *SubnetProperties `protobuf:"bytes,14,opt,name=properties,proto3" json:"properties,omitempty"`
	// Payment information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,15,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	// Indicates whether the Subnet resource is deleted or not.
	Deleted bool `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// The time when the Subnet resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The time when the Subnet resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *Subnet) Reset() {
	*x = Subnet{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_subnet_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Subnet) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Subnet) ProtoMessage() {}

func (x *Subnet) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_subnet_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Subnet.ProtoReflect.Descriptor instead.
func (*Subnet) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_subnet_proto_rawDescGZIP(), []int{6}
}

func (x *Subnet) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Subnet) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Subnet) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Subnet) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Subnet) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Subnet) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *Subnet) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *Subnet) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *Subnet) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *Subnet) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *Subnet) GetState() Subnet_State {
	if x != nil {
		return x.State
	}
	return Subnet_CREATING
}

func (x *Subnet) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *Subnet) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Subnet) GetProperties() *SubnetProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *Subnet) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

func (x *Subnet) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *Subnet) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Subnet) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// 资源实际属性.
// [EN] Real resource properties.
type SubnetProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 当前子网范围
	// [EN] Subnet scope.
	Scope SubnetProperties_Scope `protobuf:"varint,1,opt,name=scope,proto3,enum=sensetime.core.network.vpc.v1.SubnetProperties_Scope" json:"scope,omitempty"`
	// 当前子网提供者
	// [EN] Subnet provider.
	Provider SubnetProperties_Provider `protobuf:"varint,2,opt,name=provider,proto3,enum=sensetime.core.network.vpc.v1.SubnetProperties_Provider" json:"provider,omitempty"`
	// 当前子网的网络类型
	// [EN] Subnet network type.
	NetworkType SubnetProperties_NetworkType `protobuf:"varint,3,opt,name=network_type,json=networkType,proto3,enum=sensetime.core.network.vpc.v1.SubnetProperties_NetworkType" json:"network_type,omitempty"`
	// 关联的 VPC uid
	// [EN] Subnet VPC uid.
	VpcId string `protobuf:"bytes,4,opt,name=vpc_id,json=vpcId,proto3" json:"vpc_id,omitempty"`
	// 当前子网的CIDR
	// [EN] Subnet CIDR.
	Cidr string `protobuf:"bytes,5,opt,name=cidr,proto3" json:"cidr,omitempty"`
	// 当前子网预留不能使用的IP地址，x.x.x.1..x.x.x.9, x.x.x.250..x.x.x.255 地址默认不做分配
	// [EN] Subnet reserved IPs.
	ReservedIps []string `protobuf:"bytes,6,rep,name=reserved_ips,json=reservedIps,proto3" json:"reserved_ips,omitempty"`
	// 是否默认子网
	// [EN] Is default subnet.
	IsDefault bool `protobuf:"varint,7,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty"`
	// 关联的网关IP地址
	// [EN] Subnet gateway IP.
	GatewayIp string `protobuf:"bytes,8,opt,name=gateway_ip,json=gatewayIp,proto3" json:"gateway_ip,omitempty"`
	// 关联的CIDR资源池
	// [EN] CIDR pool related to the subnet.
	CidrPoolId string `protobuf:"bytes,9,opt,name=cidr_pool_id,json=cidrPoolId,proto3" json:"cidr_pool_id,omitempty"`
}

func (x *SubnetProperties) Reset() {
	*x = SubnetProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_subnet_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubnetProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubnetProperties) ProtoMessage() {}

func (x *SubnetProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_subnet_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubnetProperties.ProtoReflect.Descriptor instead.
func (*SubnetProperties) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_subnet_proto_rawDescGZIP(), []int{7}
}

func (x *SubnetProperties) GetScope() SubnetProperties_Scope {
	if x != nil {
		return x.Scope
	}
	return SubnetProperties_SERVICE
}

func (x *SubnetProperties) GetProvider() SubnetProperties_Provider {
	if x != nil {
		return x.Provider
	}
	return SubnetProperties_OVN
}

func (x *SubnetProperties) GetNetworkType() SubnetProperties_NetworkType {
	if x != nil {
		return x.NetworkType
	}
	return SubnetProperties_GENEVE
}

func (x *SubnetProperties) GetVpcId() string {
	if x != nil {
		return x.VpcId
	}
	return ""
}

func (x *SubnetProperties) GetCidr() string {
	if x != nil {
		return x.Cidr
	}
	return ""
}

func (x *SubnetProperties) GetReservedIps() []string {
	if x != nil {
		return x.ReservedIps
	}
	return nil
}

func (x *SubnetProperties) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

func (x *SubnetProperties) GetGatewayIp() string {
	if x != nil {
		return x.GatewayIp
	}
	return ""
}

func (x *SubnetProperties) GetCidrPoolId() string {
	if x != nil {
		return x.CidrPoolId
	}
	return ""
}

var File_network_vpc_v1_subnet_proto protoreflect.FileDescriptor

var file_network_vpc_v1_subnet_proto_rawDesc = []byte{
	0x0a, 0x1b, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1b, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xf4, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70,
	0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xa4, 0x01, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x53, 0x75,
	0x62, 0x6e, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x92, 0x02,
	0x0a, 0x13, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x4b, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42, 0x27,
	0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x06, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x52, 0x06, 0x73, 0x75, 0x62, 0x6e,
	0x65, 0x74, 0x22, 0xe6, 0x01, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62,
	0x6e, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73,
	0x75, 0x62, 0x6e, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3d, 0x0a, 0x06,
	0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62,
	0x6e, 0x65, 0x74, 0x52, 0x06, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x22, 0xa7, 0x01, 0x0a, 0x13,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x6e, 0x65,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x9d, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75,
	0x62, 0x6e, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3f, 0x0a,
	0x07, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x75, 0x62, 0x6e, 0x65, 0x74, 0x52, 0x07, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x73, 0x12, 0x26,
	0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xc4, 0x07, 0x0a, 0x06, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74,
	0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x73, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x50, 0xfa, 0x42, 0x4d, 0x72,
	0x4b, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a,
	0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35,
	0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b,
	0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d,
	0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x0b, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x23, 0x0a,
	0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79,
	0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x41, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62,
	0x6e, 0x65, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x15, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x43, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76,
	0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x2e, 0x54, 0x61, 0x67,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x4f, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x48, 0x0a,
	0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e,
	0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x37, 0x0a, 0x09, 0x54,
	0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0x56, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0c, 0x0a,
	0x08, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55,
	0x50, 0x44, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54,
	0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4e,
	0x47, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x04,
	0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x22, 0xfc, 0x04, 0x0a,
	0x10, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x12, 0x4b, 0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x35, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x2e, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x52, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x54,
	0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x38, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x2e, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76,
	0x69, 0x64, 0x65, 0x72, 0x12, 0x5e, 0x0a, 0x0c, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3b, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6e, 0x65,
	0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x4e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0b, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x76, 0x70, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x70, 0x63, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x63,
	0x69, 0x64, 0x72, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x64, 0x72, 0x12,
	0x21, 0x0a, 0x0c, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x5f, 0x69, 0x70, 0x73, 0x18,
	0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0b, 0x72, 0x65, 0x73, 0x65, 0x72, 0x76, 0x65, 0x64, 0x49,
	0x70, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73, 0x44, 0x65, 0x66, 0x61, 0x75, 0x6c,
	0x74, 0x12, 0x1d, 0x0a, 0x0a, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x5f, 0x69, 0x70, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x49, 0x70,
	0x12, 0x20, 0x0a, 0x0c, 0x63, 0x69, 0x64, 0x72, 0x5f, 0x70, 0x6f, 0x6f, 0x6c, 0x5f, 0x69, 0x64,
	0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x63, 0x69, 0x64, 0x72, 0x50, 0x6f, 0x6f, 0x6c,
	0x49, 0x64, 0x22, 0x50, 0x0a, 0x05, 0x53, 0x63, 0x6f, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x53,
	0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x54, 0x52, 0x41, 0x49,
	0x4e, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x41, 0x54, 0x41, 0x10, 0x02,
	0x12, 0x09, 0x0a, 0x05, 0x56, 0x50, 0x43, 0x47, 0x57, 0x10, 0x03, 0x12, 0x0a, 0x0a, 0x06, 0x4d,
	0x41, 0x4e, 0x41, 0x47, 0x45, 0x10, 0x04, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x54, 0x4f, 0x52, 0x41,
	0x47, 0x45, 0x10, 0x05, 0x22, 0x23, 0x0a, 0x08, 0x50, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x12, 0x07, 0x0a, 0x03, 0x4f, 0x56, 0x4e, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x43, 0x4f, 0x4e,
	0x54, 0x52, 0x4f, 0x4c, 0x4c, 0x45, 0x52, 0x10, 0x01, 0x22, 0x40, 0x0a, 0x0b, 0x4e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0a, 0x0a, 0x06, 0x47, 0x45, 0x4e, 0x45,
	0x56, 0x45, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05, 0x56, 0x58, 0x4c, 0x41, 0x4e, 0x10, 0x01, 0x12,
	0x08, 0x0a, 0x04, 0x46, 0x4c, 0x41, 0x54, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x56, 0x4c, 0x41,
	0x4e, 0x10, 0x03, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x42, 0x10, 0x04, 0x32, 0xd2, 0x09, 0x0a, 0x07,
	0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x73, 0x12, 0xed, 0x01, 0x0a, 0x0b, 0x4c, 0x69, 0x73, 0x74,
	0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x73, 0x12, 0x31, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x75, 0x62, 0x6e,
	0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x75, 0x62, 0x6e, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x77,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x6d, 0x12, 0x6b, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2f, 0x76, 0x70, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a,
	0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x75, 0x62, 0x6e,
	0x65, 0x74, 0x73, 0x80, 0xb5, 0x18, 0x01, 0x12, 0xeb, 0x01, 0x0a, 0x09, 0x47, 0x65, 0x74, 0x53,
	0x75, 0x62, 0x6e, 0x65, 0x74, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76,
	0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x22, 0x85, 0x01,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7b, 0x12, 0x79, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2f, 0x76, 0x70, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a,
	0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x75, 0x62, 0x6e,
	0x65, 0x74, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x80, 0xb5, 0x18, 0x01, 0x12, 0xfe, 0x01, 0x0a, 0x0c, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x12, 0x32, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x75, 0x62,
	0x6e, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6e, 0x65,
	0x74, 0x22, 0x92, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x83, 0x01, 0x22, 0x79, 0x2f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d,
	0x2f, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x06, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x88, 0xb5,
	0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x12, 0xfe, 0x01, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x12, 0x32, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x75,
	0x62, 0x6e, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6e,
	0x65, 0x74, 0x22, 0x92, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x83, 0x01, 0x32, 0x79, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x6e, 0x65,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x06, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x88,
	0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x12, 0xe6, 0x01, 0x0a, 0x0c, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x12, 0x32, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53,
	0x75, 0x62, 0x6e, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45,
	0x6d, 0x70, 0x74, 0x79, 0x22, 0x89, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7b, 0x2a, 0x79, 0x2f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f,
	0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e,
	0x65, 0x7d, 0x2f, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x6e,
	0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01,
	0x42, 0x50, 0x5a, 0x4e, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x62, 0x6a, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x61, 0x72, 0x79, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x73,
	0x6f, 0x6e, 0x2d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x76,
	0x70, 0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_network_vpc_v1_subnet_proto_rawDescOnce sync.Once
	file_network_vpc_v1_subnet_proto_rawDescData = file_network_vpc_v1_subnet_proto_rawDesc
)

func file_network_vpc_v1_subnet_proto_rawDescGZIP() []byte {
	file_network_vpc_v1_subnet_proto_rawDescOnce.Do(func() {
		file_network_vpc_v1_subnet_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_vpc_v1_subnet_proto_rawDescData)
	})
	return file_network_vpc_v1_subnet_proto_rawDescData
}

var file_network_vpc_v1_subnet_proto_enumTypes = make([]protoimpl.EnumInfo, 4)
var file_network_vpc_v1_subnet_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_network_vpc_v1_subnet_proto_goTypes = []interface{}{
	(Subnet_State)(0),                 // 0: sensetime.core.network.vpc.v1.Subnet.State
	(SubnetProperties_Scope)(0),       // 1: sensetime.core.network.vpc.v1.SubnetProperties.Scope
	(SubnetProperties_Provider)(0),    // 2: sensetime.core.network.vpc.v1.SubnetProperties.Provider
	(SubnetProperties_NetworkType)(0), // 3: sensetime.core.network.vpc.v1.SubnetProperties.NetworkType
	(*ListSubnetsRequest)(nil),        // 4: sensetime.core.network.vpc.v1.ListSubnetsRequest
	(*GetSubnetRequest)(nil),          // 5: sensetime.core.network.vpc.v1.GetSubnetRequest
	(*CreateSubnetRequest)(nil),       // 6: sensetime.core.network.vpc.v1.CreateSubnetRequest
	(*UpdateSubnetRequest)(nil),       // 7: sensetime.core.network.vpc.v1.UpdateSubnetRequest
	(*DeleteSubnetRequest)(nil),       // 8: sensetime.core.network.vpc.v1.DeleteSubnetRequest
	(*ListSubnetsResponse)(nil),       // 9: sensetime.core.network.vpc.v1.ListSubnetsResponse
	(*Subnet)(nil),                    // 10: sensetime.core.network.vpc.v1.Subnet
	(*SubnetProperties)(nil),          // 11: sensetime.core.network.vpc.v1.SubnetProperties
	nil,                               // 12: sensetime.core.network.vpc.v1.Subnet.TagsEntry
	(*v1.OrderInfo)(nil),              // 13: sensetime.core.higgs.common.v1.OrderInfo
	(*timestamppb.Timestamp)(nil),     // 14: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),             // 15: google.protobuf.Empty
}
var file_network_vpc_v1_subnet_proto_depIdxs = []int32{
	10, // 0: sensetime.core.network.vpc.v1.CreateSubnetRequest.subnet:type_name -> sensetime.core.network.vpc.v1.Subnet
	10, // 1: sensetime.core.network.vpc.v1.UpdateSubnetRequest.subnet:type_name -> sensetime.core.network.vpc.v1.Subnet
	10, // 2: sensetime.core.network.vpc.v1.ListSubnetsResponse.Subnets:type_name -> sensetime.core.network.vpc.v1.Subnet
	0,  // 3: sensetime.core.network.vpc.v1.Subnet.state:type_name -> sensetime.core.network.vpc.v1.Subnet.State
	12, // 4: sensetime.core.network.vpc.v1.Subnet.tags:type_name -> sensetime.core.network.vpc.v1.Subnet.TagsEntry
	11, // 5: sensetime.core.network.vpc.v1.Subnet.properties:type_name -> sensetime.core.network.vpc.v1.SubnetProperties
	13, // 6: sensetime.core.network.vpc.v1.Subnet.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	14, // 7: sensetime.core.network.vpc.v1.Subnet.create_time:type_name -> google.protobuf.Timestamp
	14, // 8: sensetime.core.network.vpc.v1.Subnet.update_time:type_name -> google.protobuf.Timestamp
	1,  // 9: sensetime.core.network.vpc.v1.SubnetProperties.scope:type_name -> sensetime.core.network.vpc.v1.SubnetProperties.Scope
	2,  // 10: sensetime.core.network.vpc.v1.SubnetProperties.provider:type_name -> sensetime.core.network.vpc.v1.SubnetProperties.Provider
	3,  // 11: sensetime.core.network.vpc.v1.SubnetProperties.network_type:type_name -> sensetime.core.network.vpc.v1.SubnetProperties.NetworkType
	4,  // 12: sensetime.core.network.vpc.v1.Subnets.ListSubnets:input_type -> sensetime.core.network.vpc.v1.ListSubnetsRequest
	5,  // 13: sensetime.core.network.vpc.v1.Subnets.GetSubnet:input_type -> sensetime.core.network.vpc.v1.GetSubnetRequest
	6,  // 14: sensetime.core.network.vpc.v1.Subnets.CreateSubnet:input_type -> sensetime.core.network.vpc.v1.CreateSubnetRequest
	7,  // 15: sensetime.core.network.vpc.v1.Subnets.UpdateSubnet:input_type -> sensetime.core.network.vpc.v1.UpdateSubnetRequest
	8,  // 16: sensetime.core.network.vpc.v1.Subnets.DeleteSubnet:input_type -> sensetime.core.network.vpc.v1.DeleteSubnetRequest
	9,  // 17: sensetime.core.network.vpc.v1.Subnets.ListSubnets:output_type -> sensetime.core.network.vpc.v1.ListSubnetsResponse
	10, // 18: sensetime.core.network.vpc.v1.Subnets.GetSubnet:output_type -> sensetime.core.network.vpc.v1.Subnet
	10, // 19: sensetime.core.network.vpc.v1.Subnets.CreateSubnet:output_type -> sensetime.core.network.vpc.v1.Subnet
	10, // 20: sensetime.core.network.vpc.v1.Subnets.UpdateSubnet:output_type -> sensetime.core.network.vpc.v1.Subnet
	15, // 21: sensetime.core.network.vpc.v1.Subnets.DeleteSubnet:output_type -> google.protobuf.Empty
	17, // [17:22] is the sub-list for method output_type
	12, // [12:17] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_network_vpc_v1_subnet_proto_init() }
func file_network_vpc_v1_subnet_proto_init() {
	if File_network_vpc_v1_subnet_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_network_vpc_v1_subnet_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSubnetsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_subnet_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSubnetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_subnet_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSubnetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_subnet_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSubnetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_subnet_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSubnetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_subnet_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSubnetsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_subnet_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Subnet); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_subnet_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubnetProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_vpc_v1_subnet_proto_rawDesc,
			NumEnums:      4,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_network_vpc_v1_subnet_proto_goTypes,
		DependencyIndexes: file_network_vpc_v1_subnet_proto_depIdxs,
		EnumInfos:         file_network_vpc_v1_subnet_proto_enumTypes,
		MessageInfos:      file_network_vpc_v1_subnet_proto_msgTypes,
	}.Build()
	File_network_vpc_v1_subnet_proto = out.File
	file_network_vpc_v1_subnet_proto_rawDesc = nil
	file_network_vpc_v1_subnet_proto_goTypes = nil
	file_network_vpc_v1_subnet_proto_depIdxs = nil
}
