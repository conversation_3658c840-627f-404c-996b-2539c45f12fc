// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/vpc/v1/vpc.proto

package vpc

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/api/annotations"
	v1 "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents the different states of a VPC.
type SubnetType int32

const (
	// only create vpc resource
	SubnetType_NULL SubnetType = 0
	// create vpc resource & default pod service subnet & nat gw
	SubnetType_POD_SERVICE SubnetType = 1
	// create vpc resource & default pod service subnet & nat gw  & training subnets
	SubnetType_POD_SERVICE_TRAINING SubnetType = 2
	// create vpc resource & default pod service subnet & nat gw  & training subnets & bms service service subnet
	SubnetType_POD_BMS_SERVICE SubnetType = 3
	// create vpc resource & default pod service subnet & nat gw  & training subnets & bms service service subnet & bms training sbunet
	SubnetType_POD_BMS_SERVICE_TRAINING SubnetType = 4
	// create vpc resource & default pod service subnet & nat gw  & bms service service subnet & bms training sbunet
	SubnetType_POD_SERVICE_BMS SubnetType = 5
)

// Enum value maps for SubnetType.
var (
	SubnetType_name = map[int32]string{
		0: "NULL",
		1: "POD_SERVICE",
		2: "POD_SERVICE_TRAINING",
		3: "POD_BMS_SERVICE",
		4: "POD_BMS_SERVICE_TRAINING",
		5: "POD_SERVICE_BMS",
	}
	SubnetType_value = map[string]int32{
		"NULL":                     0,
		"POD_SERVICE":              1,
		"POD_SERVICE_TRAINING":     2,
		"POD_BMS_SERVICE":          3,
		"POD_BMS_SERVICE_TRAINING": 4,
		"POD_SERVICE_BMS":          5,
	}
)

func (x SubnetType) Enum() *SubnetType {
	p := new(SubnetType)
	*p = x
	return p
}

func (x SubnetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SubnetType) Descriptor() protoreflect.EnumDescriptor {
	return file_network_vpc_v1_vpc_proto_enumTypes[0].Descriptor()
}

func (SubnetType) Type() protoreflect.EnumType {
	return &file_network_vpc_v1_vpc_proto_enumTypes[0]
}

func (x SubnetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SubnetType.Descriptor instead.
func (SubnetType) EnumDescriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{0}
}

// Represents the different states of a VPC dgw.
type DGWGateway_State int32

const (
	// The VPC dgw resource is being created.
	DGWGateway_CREATING DGWGateway_State = 0
	// The VPC dgw resource is being updated.
	DGWGateway_UPDATING DGWGateway_State = 1
	// The VPC dgw resource has been active.
	DGWGateway_ACTIVE DGWGateway_State = 2
	// The VPC dgw resource is being deleting.
	DGWGateway_DELETING DGWGateway_State = 3
	// The VPC dgw resource has been deleted.
	DGWGateway_DELETED DGWGateway_State = 4
	// The VPC dgw resource has been failed.
	DGWGateway_FAILED DGWGateway_State = 5
	// the VPC dgw resource has been inactive
	DGWGateway_INACTIVE DGWGateway_State = 6
	// the VPC dgw resource has been error
	DGWGateway_ERROR DGWGateway_State = 7
)

// Enum value maps for DGWGateway_State.
var (
	DGWGateway_State_name = map[int32]string{
		0: "CREATING",
		1: "UPDATING",
		2: "ACTIVE",
		3: "DELETING",
		4: "DELETED",
		5: "FAILED",
		6: "INACTIVE",
		7: "ERROR",
	}
	DGWGateway_State_value = map[string]int32{
		"CREATING": 0,
		"UPDATING": 1,
		"ACTIVE":   2,
		"DELETING": 3,
		"DELETED":  4,
		"FAILED":   5,
		"INACTIVE": 6,
		"ERROR":    7,
	}
)

func (x DGWGateway_State) Enum() *DGWGateway_State {
	p := new(DGWGateway_State)
	*p = x
	return p
}

func (x DGWGateway_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DGWGateway_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_vpc_v1_vpc_proto_enumTypes[1].Descriptor()
}

func (DGWGateway_State) Type() protoreflect.EnumType {
	return &file_network_vpc_v1_vpc_proto_enumTypes[1]
}

func (x DGWGateway_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DGWGateway_State.Descriptor instead.
func (DGWGateway_State) EnumDescriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{7, 0}
}

// Represents the different admin states of a dgw.
type DGWGateway_AdminState int32

const (
	// The dgw resource is invalid.
	DGWGateway_INVALID DGWGateway_AdminState = 0
	// The dgw resource is enable.
	DGWGateway_ENABLE DGWGateway_AdminState = 1
	// The dgw resource is disable.
	DGWGateway_DISABLE DGWGateway_AdminState = 2
)

// Enum value maps for DGWGateway_AdminState.
var (
	DGWGateway_AdminState_name = map[int32]string{
		0: "INVALID",
		1: "ENABLE",
		2: "DISABLE",
	}
	DGWGateway_AdminState_value = map[string]int32{
		"INVALID": 0,
		"ENABLE":  1,
		"DISABLE": 2,
	}
)

func (x DGWGateway_AdminState) Enum() *DGWGateway_AdminState {
	p := new(DGWGateway_AdminState)
	*p = x
	return p
}

func (x DGWGateway_AdminState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DGWGateway_AdminState) Descriptor() protoreflect.EnumDescriptor {
	return file_network_vpc_v1_vpc_proto_enumTypes[2].Descriptor()
}

func (DGWGateway_AdminState) Type() protoreflect.EnumType {
	return &file_network_vpc_v1_vpc_proto_enumTypes[2]
}

func (x DGWGateway_AdminState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DGWGateway_AdminState.Descriptor instead.
func (DGWGateway_AdminState) EnumDescriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{7, 1}
}

// Represents the different admin states of a dgw.
type DGWGatewayStatus_AdminState int32

const (
	// The dgw resource is invalid.
	DGWGatewayStatus_INVALID DGWGatewayStatus_AdminState = 0
	// The dgw resource is enable.
	DGWGatewayStatus_ENABLE DGWGatewayStatus_AdminState = 1
	// The dgw resource is disable.
	DGWGatewayStatus_DISABLE DGWGatewayStatus_AdminState = 2
)

// Enum value maps for DGWGatewayStatus_AdminState.
var (
	DGWGatewayStatus_AdminState_name = map[int32]string{
		0: "INVALID",
		1: "ENABLE",
		2: "DISABLE",
	}
	DGWGatewayStatus_AdminState_value = map[string]int32{
		"INVALID": 0,
		"ENABLE":  1,
		"DISABLE": 2,
	}
)

func (x DGWGatewayStatus_AdminState) Enum() *DGWGatewayStatus_AdminState {
	p := new(DGWGatewayStatus_AdminState)
	*p = x
	return p
}

func (x DGWGatewayStatus_AdminState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DGWGatewayStatus_AdminState) Descriptor() protoreflect.EnumDescriptor {
	return file_network_vpc_v1_vpc_proto_enumTypes[3].Descriptor()
}

func (DGWGatewayStatus_AdminState) Type() protoreflect.EnumType {
	return &file_network_vpc_v1_vpc_proto_enumTypes[3]
}

func (x DGWGatewayStatus_AdminState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DGWGatewayStatus_AdminState.Descriptor instead.
func (DGWGatewayStatus_AdminState) EnumDescriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{15, 0}
}

// Represents the different states of a dgw.
type DGWGatewayStatus_State int32

const (
	// The dgw resource is being created.
	DGWGatewayStatus_CREATING DGWGatewayStatus_State = 0
	// The dgw resource is being updated.
	DGWGatewayStatus_UPDATING DGWGatewayStatus_State = 1
	// The dgw resource has been active.
	DGWGatewayStatus_ACTIVE DGWGatewayStatus_State = 2
	// The dgw resource is being deleting.
	DGWGatewayStatus_DELETING DGWGatewayStatus_State = 3
	// The dgw resource has been deleted.
	DGWGatewayStatus_DELETED DGWGatewayStatus_State = 4
	// The dgw resource has been failed.
	DGWGatewayStatus_FAILED DGWGatewayStatus_State = 5
	// the VPC dgw resource has been inactive
	DGWGatewayStatus_INACTIVE DGWGatewayStatus_State = 6
	// the VPC dgw resource has been error
	DGWGatewayStatus_ERROR DGWGatewayStatus_State = 7
)

// Enum value maps for DGWGatewayStatus_State.
var (
	DGWGatewayStatus_State_name = map[int32]string{
		0: "CREATING",
		1: "UPDATING",
		2: "ACTIVE",
		3: "DELETING",
		4: "DELETED",
		5: "FAILED",
		6: "INACTIVE",
		7: "ERROR",
	}
	DGWGatewayStatus_State_value = map[string]int32{
		"CREATING": 0,
		"UPDATING": 1,
		"ACTIVE":   2,
		"DELETING": 3,
		"DELETED":  4,
		"FAILED":   5,
		"INACTIVE": 6,
		"ERROR":    7,
	}
)

func (x DGWGatewayStatus_State) Enum() *DGWGatewayStatus_State {
	p := new(DGWGatewayStatus_State)
	*p = x
	return p
}

func (x DGWGatewayStatus_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DGWGatewayStatus_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_vpc_v1_vpc_proto_enumTypes[4].Descriptor()
}

func (DGWGatewayStatus_State) Type() protoreflect.EnumType {
	return &file_network_vpc_v1_vpc_proto_enumTypes[4]
}

func (x DGWGatewayStatus_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DGWGatewayStatus_State.Descriptor instead.
func (DGWGatewayStatus_State) EnumDescriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{15, 1}
}

// EIP association type.
type EIPStatus_AType int32

const (
	// The EIP resource type is NATGW.
	EIPStatus_NATGW EIPStatus_AType = 0
	// The EIP resource type is POD.
	EIPStatus_POD EIPStatus_AType = 1
	// The EIP resource type is SLB.
	EIPStatus_SLB EIPStatus_AType = 2
	// The EIP resource type is BM.
	EIPStatus_BM EIPStatus_AType = 3
	// The EIP support NATGW and BM at same time, 1030 default.
	EIPStatus_NATGW_AND_BM EIPStatus_AType = 4
)

// Enum value maps for EIPStatus_AType.
var (
	EIPStatus_AType_name = map[int32]string{
		0: "NATGW",
		1: "POD",
		2: "SLB",
		3: "BM",
		4: "NATGW_AND_BM",
	}
	EIPStatus_AType_value = map[string]int32{
		"NATGW":        0,
		"POD":          1,
		"SLB":          2,
		"BM":           3,
		"NATGW_AND_BM": 4,
	}
)

func (x EIPStatus_AType) Enum() *EIPStatus_AType {
	p := new(EIPStatus_AType)
	*p = x
	return p
}

func (x EIPStatus_AType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EIPStatus_AType) Descriptor() protoreflect.EnumDescriptor {
	return file_network_vpc_v1_vpc_proto_enumTypes[5].Descriptor()
}

func (EIPStatus_AType) Type() protoreflect.EnumType {
	return &file_network_vpc_v1_vpc_proto_enumTypes[5]
}

func (x EIPStatus_AType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EIPStatus_AType.Descriptor instead.
func (EIPStatus_AType) EnumDescriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{17, 0}
}

// Represents the different states of a VPC.
type VPC_State int32

const (
	// The VPC resource is being created.
	VPC_CREATING VPC_State = 0
	// The VPC resource is being updated.
	VPC_UPDATING VPC_State = 1
	// The VPC resource has been active.
	VPC_ACTIVE VPC_State = 2
	// The VPC resource is being deleting.
	VPC_DELETING VPC_State = 3
	// The VPC resource has been deleted.
	VPC_DELETED VPC_State = 4
	// The VPC resource has been failed.
	VPC_FAILED VPC_State = 5
)

// Enum value maps for VPC_State.
var (
	VPC_State_name = map[int32]string{
		0: "CREATING",
		1: "UPDATING",
		2: "ACTIVE",
		3: "DELETING",
		4: "DELETED",
		5: "FAILED",
	}
	VPC_State_value = map[string]int32{
		"CREATING": 0,
		"UPDATING": 1,
		"ACTIVE":   2,
		"DELETING": 3,
		"DELETED":  4,
		"FAILED":   5,
	}
)

func (x VPC_State) Enum() *VPC_State {
	p := new(VPC_State)
	*p = x
	return p
}

func (x VPC_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (VPC_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_vpc_v1_vpc_proto_enumTypes[6].Descriptor()
}

func (VPC_State) Type() protoreflect.EnumType {
	return &file_network_vpc_v1_vpc_proto_enumTypes[6]
}

func (x VPC_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use VPC_State.Descriptor instead.
func (VPC_State) EnumDescriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{19, 0}
}

// protocol of cluster
type ClusterStatus_Protocol int32

const (
	// Default value. This value is unused.
	ClusterStatus_PROTOCOL_UNSPECIFIED ClusterStatus_Protocol = 0
	// IB
	ClusterStatus_IB ClusterStatus_Protocol = 1
	// RoCE
	ClusterStatus_RoCE ClusterStatus_Protocol = 2
	// None
	ClusterStatus_None ClusterStatus_Protocol = 3
)

// Enum value maps for ClusterStatus_Protocol.
var (
	ClusterStatus_Protocol_name = map[int32]string{
		0: "PROTOCOL_UNSPECIFIED",
		1: "IB",
		2: "RoCE",
		3: "None",
	}
	ClusterStatus_Protocol_value = map[string]int32{
		"PROTOCOL_UNSPECIFIED": 0,
		"IB":                   1,
		"RoCE":                 2,
		"None":                 3,
	}
)

func (x ClusterStatus_Protocol) Enum() *ClusterStatus_Protocol {
	p := new(ClusterStatus_Protocol)
	*p = x
	return p
}

func (x ClusterStatus_Protocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ClusterStatus_Protocol) Descriptor() protoreflect.EnumDescriptor {
	return file_network_vpc_v1_vpc_proto_enumTypes[7].Descriptor()
}

func (ClusterStatus_Protocol) Type() protoreflect.EnumType {
	return &file_network_vpc_v1_vpc_proto_enumTypes[7]
}

func (x ClusterStatus_Protocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ClusterStatus_Protocol.Descriptor instead.
func (ClusterStatus_Protocol) EnumDescriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{23, 0}
}

// 列举 VPCs 的请求.
// [EN] Request to list VPCs.
type ListVPCsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// List filter.
	Filter string `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// Sort resoults.
	OrderBy string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// The maximum number of items to return.
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// The next_page_token value returned from a previous List request, if any.
	PageToken string `protobuf:"bytes,7,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// tenant_id.
	TenantId string `protobuf:"bytes,8,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
}

func (x *ListVPCsRequest) Reset() {
	*x = ListVPCsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListVPCsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVPCsRequest) ProtoMessage() {}

func (x *ListVPCsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVPCsRequest.ProtoReflect.Descriptor instead.
func (*ListVPCsRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{0}
}

func (x *ListVPCsRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListVPCsRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListVPCsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListVPCsRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListVPCsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListVPCsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListVPCsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListVPCsRequest) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

// 获取某一个 VPC 的请求.
// [EN] Request to get a VPC.
type GetVPCRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VpcName string `protobuf:"bytes,4,opt,name=vpc_name,json=vpcName,proto3" json:"vpc_name,omitempty"`
}

func (x *GetVPCRequest) Reset() {
	*x = GetVPCRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVPCRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVPCRequest) ProtoMessage() {}

func (x *GetVPCRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVPCRequest.ProtoReflect.Descriptor instead.
func (*GetVPCRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{1}
}

func (x *GetVPCRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetVPCRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetVPCRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetVPCRequest) GetVpcName() string {
	if x != nil {
		return x.VpcName
	}
	return ""
}

// 获取符合 VPC 相关属性的请求.
// [EN] Get requested VPC status.
type GetVPCStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VpcName string `protobuf:"bytes,4,opt,name=vpc_name,json=vpcName,proto3" json:"vpc_name,omitempty"`
}

func (x *GetVPCStatusRequest) Reset() {
	*x = GetVPCStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVPCStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVPCStatusRequest) ProtoMessage() {}

func (x *GetVPCStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVPCStatusRequest.ProtoReflect.Descriptor instead.
func (*GetVPCStatusRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{2}
}

func (x *GetVPCStatusRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetVPCStatusRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetVPCStatusRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetVPCStatusRequest) GetVpcName() string {
	if x != nil {
		return x.VpcName
	}
	return ""
}

// VPC 创建字段.
// [EN] VPC creation properties.
type CreateVPCRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone, todo: add validation
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VpcName string `protobuf:"bytes,4,opt,name=vpc_name,json=vpcName,proto3" json:"vpc_name,omitempty"`
	// The VPC resource to create.
	Vpc *VPC `protobuf:"bytes,5,opt,name=vpc,proto3" json:"vpc,omitempty"`
}

func (x *CreateVPCRequest) Reset() {
	*x = CreateVPCRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateVPCRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVPCRequest) ProtoMessage() {}

func (x *CreateVPCRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVPCRequest.ProtoReflect.Descriptor instead.
func (*CreateVPCRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{3}
}

func (x *CreateVPCRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *CreateVPCRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *CreateVPCRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateVPCRequest) GetVpcName() string {
	if x != nil {
		return x.VpcName
	}
	return ""
}

func (x *CreateVPCRequest) GetVpc() *VPC {
	if x != nil {
		return x.Vpc
	}
	return nil
}

// VPC 可编辑字段.
// [EN] VPC editable properties.
type UpdateVPCRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VpcName string `protobuf:"bytes,4,opt,name=vpc_name,json=vpcName,proto3" json:"vpc_name,omitempty"`
	// The VPC resource to update.
	Vpc *VPCUpdateProperties `protobuf:"bytes,5,opt,name=vpc,proto3" json:"vpc,omitempty"`
	// update_mask
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,6,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateVPCRequest) Reset() {
	*x = UpdateVPCRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVPCRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVPCRequest) ProtoMessage() {}

func (x *UpdateVPCRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVPCRequest.ProtoReflect.Descriptor instead.
func (*UpdateVPCRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateVPCRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *UpdateVPCRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *UpdateVPCRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateVPCRequest) GetVpcName() string {
	if x != nil {
		return x.VpcName
	}
	return ""
}

func (x *UpdateVPCRequest) GetVpc() *VPCUpdateProperties {
	if x != nil {
		return x.Vpc
	}
	return nil
}

func (x *UpdateVPCRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

type VPCUpdateProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// ContainerInstance resource display name
	DisplayName *string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3,oneof" json:"display_name,omitempty"`
	// ContainerInstance resource description
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// the VPC resource subnets' type
	SubnetType SubnetType `protobuf:"varint,3,opt,name=subnet_type,json=subnetType,proto3,enum=sensetime.core.network.vpc.v1.SubnetType" json:"subnet_type,omitempty"`
}

func (x *VPCUpdateProperties) Reset() {
	*x = VPCUpdateProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VPCUpdateProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VPCUpdateProperties) ProtoMessage() {}

func (x *VPCUpdateProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VPCUpdateProperties.ProtoReflect.Descriptor instead.
func (*VPCUpdateProperties) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{5}
}

func (x *VPCUpdateProperties) GetDisplayName() string {
	if x != nil && x.DisplayName != nil {
		return *x.DisplayName
	}
	return ""
}

func (x *VPCUpdateProperties) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *VPCUpdateProperties) GetSubnetType() SubnetType {
	if x != nil {
		return x.SubnetType
	}
	return SubnetType_NULL
}

// VPC DGW 可编辑字段.
// [EN] VPC DGW editable properties.
type UpdateVPCDgwRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VpcName string `protobuf:"bytes,4,opt,name=vpc_name,json=vpcName,proto3" json:"vpc_name,omitempty"`
	// The dgw resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	DgwName string `protobuf:"bytes,5,opt,name=dgw_name,json=dgwName,proto3" json:"dgw_name,omitempty"`
	// The DGW resource to update.
	Dgw *DGWGateway `protobuf:"bytes,6,opt,name=dgw,proto3" json:"dgw,omitempty"`
	// update_mask
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,7,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateVPCDgwRequest) Reset() {
	*x = UpdateVPCDgwRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVPCDgwRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVPCDgwRequest) ProtoMessage() {}

func (x *UpdateVPCDgwRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVPCDgwRequest.ProtoReflect.Descriptor instead.
func (*UpdateVPCDgwRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{6}
}

func (x *UpdateVPCDgwRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *UpdateVPCDgwRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *UpdateVPCDgwRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateVPCDgwRequest) GetVpcName() string {
	if x != nil {
		return x.VpcName
	}
	return ""
}

func (x *UpdateVPCDgwRequest) GetDgwName() string {
	if x != nil {
		return x.DgwName
	}
	return ""
}

func (x *UpdateVPCDgwRequest) GetDgw() *DGWGateway {
	if x != nil {
		return x.Dgw
	}
	return nil
}

func (x *UpdateVPCDgwRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// DGWGateway 实例结构体.
// [EN] DGWGateway entity.
type DGWGateway struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The dgw resource id using the form:
	//     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}/dgws/{name}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The dgw resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// ContainerInstance resource display name
	DisplayName string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// ContainerInstance resource description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// The dgw resource uuid.
	Uid string `protobuf:"bytes,5,opt,name=uid,proto3" json:"uid,omitempty"`
	// The dgw resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// The id of the user who created the VPC dgw resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// The id of the user who owns the VPC dgw resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// Available zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// The current state of the VPC dgw resource.
	State DGWGateway_State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.vpc.v1.DGWGateway_State" json:"state,omitempty"`
	// the current admin status
	AdminState DGWGateway_AdminState `protobuf:"varint,12,opt,name=admin_state,json=adminState,proto3,enum=sensetime.core.network.vpc.v1.DGWGateway_AdminState" json:"admin_state,omitempty"`
	// cidr/prefix模式,200.64.xx.xx
	GwExternalIp string `protobuf:"bytes,13,opt,name=gw_external_ip,json=gwExternalIp,proto3" json:"gw_external_ip,omitempty"`
	// 以，为连接的目标网段列表
	GwPolicy string `protobuf:"bytes,14,opt,name=gw_policy,json=gwPolicy,proto3" json:"gw_policy,omitempty"`
	// vpc overlay ip, 10.119.xxx.xxx
	InternalIp string `protobuf:"bytes,15,opt,name=internal_ip,json=internalIp,proto3" json:"internal_ip,omitempty"`
	// Indicates whether the VPC resource is deleted or not.
	Deleted bool `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// The time when the VPC resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The time when the VPC resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *DGWGateway) Reset() {
	*x = DGWGateway{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DGWGateway) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DGWGateway) ProtoMessage() {}

func (x *DGWGateway) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DGWGateway.ProtoReflect.Descriptor instead.
func (*DGWGateway) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{7}
}

func (x *DGWGateway) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DGWGateway) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DGWGateway) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *DGWGateway) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DGWGateway) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *DGWGateway) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *DGWGateway) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *DGWGateway) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *DGWGateway) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *DGWGateway) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DGWGateway) GetState() DGWGateway_State {
	if x != nil {
		return x.State
	}
	return DGWGateway_CREATING
}

func (x *DGWGateway) GetAdminState() DGWGateway_AdminState {
	if x != nil {
		return x.AdminState
	}
	return DGWGateway_INVALID
}

func (x *DGWGateway) GetGwExternalIp() string {
	if x != nil {
		return x.GwExternalIp
	}
	return ""
}

func (x *DGWGateway) GetGwPolicy() string {
	if x != nil {
		return x.GwPolicy
	}
	return ""
}

func (x *DGWGateway) GetInternalIp() string {
	if x != nil {
		return x.InternalIp
	}
	return ""
}

func (x *DGWGateway) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *DGWGateway) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *DGWGateway) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// 删除某一个 VPC 的请求.
// [EN] Request to delete a VPC.
type DeleteVPCRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VpcName string `protobuf:"bytes,4,opt,name=vpc_name,json=vpcName,proto3" json:"vpc_name,omitempty"`
}

func (x *DeleteVPCRequest) Reset() {
	*x = DeleteVPCRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteVPCRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVPCRequest) ProtoMessage() {}

func (x *DeleteVPCRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVPCRequest.ProtoReflect.Descriptor instead.
func (*DeleteVPCRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{8}
}

func (x *DeleteVPCRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DeleteVPCRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *DeleteVPCRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteVPCRequest) GetVpcName() string {
	if x != nil {
		return x.VpcName
	}
	return ""
}

// 列举 VPCs 的响应.
// [EN] Response to list VPCs.
type ListVPCsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// VPC 列表.
	// [EN] VPC list.
	Vpcs []*VPC `protobuf:"bytes,1,rep,name=vpcs,proto3" json:"vpcs,omitempty"`
	// 下一个页面的 token，如果没有更多数据则为空.
	// [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListVPCsResponse) Reset() {
	*x = ListVPCsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListVPCsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVPCsResponse) ProtoMessage() {}

func (x *ListVPCsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVPCsResponse.ProtoReflect.Descriptor instead.
func (*ListVPCsResponse) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{9}
}

func (x *ListVPCsResponse) GetVpcs() []*VPC {
	if x != nil {
		return x.Vpcs
	}
	return nil
}

func (x *ListVPCsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListVPCsResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// 返回 VPC 相关属性的响应.
// [EN] Response to vpc status.
type VPCStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vpc cidr.
	Cidr string `protobuf:"bytes,1,opt,name=cidr,proto3" json:"cidr,omitempty"`
	// namespaces info
	Namespaces []*NamespaceStatus `protobuf:"bytes,2,rep,name=namespaces,proto3" json:"namespaces,omitempty"`
	// subnets info
	Subnets []*SubnetStatus `protobuf:"bytes,3,rep,name=subnets,proto3" json:"subnets,omitempty"`
	// natGateway info
	NatGateways []*NATGatewayStatus `protobuf:"bytes,4,rep,name=nat_gateways,json=natGateways,proto3" json:"nat_gateways,omitempty"`
	// eip info
	Eips []*EIPStatus `protobuf:"bytes,5,rep,name=eips,proto3" json:"eips,omitempty"`
	// distribute Gateway info
	DgwGateways []*DGWGatewayStatus `protobuf:"bytes,6,rep,name=dgw_gateways,json=dgwGateways,proto3" json:"dgw_gateways,omitempty"`
}

func (x *VPCStatus) Reset() {
	*x = VPCStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VPCStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VPCStatus) ProtoMessage() {}

func (x *VPCStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VPCStatus.ProtoReflect.Descriptor instead.
func (*VPCStatus) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{10}
}

func (x *VPCStatus) GetCidr() string {
	if x != nil {
		return x.Cidr
	}
	return ""
}

func (x *VPCStatus) GetNamespaces() []*NamespaceStatus {
	if x != nil {
		return x.Namespaces
	}
	return nil
}

func (x *VPCStatus) GetSubnets() []*SubnetStatus {
	if x != nil {
		return x.Subnets
	}
	return nil
}

func (x *VPCStatus) GetNatGateways() []*NATGatewayStatus {
	if x != nil {
		return x.NatGateways
	}
	return nil
}

func (x *VPCStatus) GetEips() []*EIPStatus {
	if x != nil {
		return x.Eips
	}
	return nil
}

func (x *VPCStatus) GetDgwGateways() []*DGWGatewayStatus {
	if x != nil {
		return x.DgwGateways
	}
	return nil
}

// VPC 相关 metrics.
// [EN] VPC metrics.
type VPCMetrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// resource_metrics.
	ResourceMetrics []*ResourceMetrics `protobuf:"bytes,1,rep,name=resource_metrics,json=resourceMetrics,proto3" json:"resource_metrics,omitempty"`
}

func (x *VPCMetrics) Reset() {
	*x = VPCMetrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VPCMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VPCMetrics) ProtoMessage() {}

func (x *VPCMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VPCMetrics.ProtoReflect.Descriptor instead.
func (*VPCMetrics) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{11}
}

func (x *VPCMetrics) GetResourceMetrics() []*ResourceMetrics {
	if x != nil {
		return x.ResourceMetrics
	}
	return nil
}

// ResourceMetrics
type ResourceMetrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// value.
	Value int32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	// unit.
	Unit string `protobuf:"bytes,3,opt,name=unit,proto3" json:"unit,omitempty"`
	// color.
	Color string `protobuf:"bytes,4,opt,name=color,proto3" json:"color,omitempty"`
}

func (x *ResourceMetrics) Reset() {
	*x = ResourceMetrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceMetrics) ProtoMessage() {}

func (x *ResourceMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceMetrics.ProtoReflect.Descriptor instead.
func (*ResourceMetrics) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{12}
}

func (x *ResourceMetrics) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResourceMetrics) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *ResourceMetrics) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *ResourceMetrics) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

// vpc status namespace info.
type NamespaceStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// namespace name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// subnet name
	Subnet string `protobuf:"bytes,2,opt,name=subnet,proto3" json:"subnet,omitempty"`
}

func (x *NamespaceStatus) Reset() {
	*x = NamespaceStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NamespaceStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NamespaceStatus) ProtoMessage() {}

func (x *NamespaceStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NamespaceStatus.ProtoReflect.Descriptor instead.
func (*NamespaceStatus) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{13}
}

func (x *NamespaceStatus) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NamespaceStatus) GetSubnet() string {
	if x != nil {
		return x.Subnet
	}
	return ""
}

// vpc status subnet info.
type SubnetStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// subnet name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// subnet cidr.
	Cidr string `protobuf:"bytes,2,opt,name=cidr,proto3" json:"cidr,omitempty"`
	// subnet uid.
	Id string `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	// subnet scope.
	Scope string `protobuf:"bytes,4,opt,name=scope,proto3" json:"scope,omitempty"`
	// subnet provider.
	Provider string `protobuf:"bytes,5,opt,name=provider,proto3" json:"provider,omitempty"`
	// subnet network type.
	NetworkType string `protobuf:"bytes,6,opt,name=network_type,json=networkType,proto3" json:"network_type,omitempty"`
}

func (x *SubnetStatus) Reset() {
	*x = SubnetStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SubnetStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SubnetStatus) ProtoMessage() {}

func (x *SubnetStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SubnetStatus.ProtoReflect.Descriptor instead.
func (*SubnetStatus) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{14}
}

func (x *SubnetStatus) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SubnetStatus) GetCidr() string {
	if x != nil {
		return x.Cidr
	}
	return ""
}

func (x *SubnetStatus) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SubnetStatus) GetScope() string {
	if x != nil {
		return x.Scope
	}
	return ""
}

func (x *SubnetStatus) GetProvider() string {
	if x != nil {
		return x.Provider
	}
	return ""
}

func (x *SubnetStatus) GetNetworkType() string {
	if x != nil {
		return x.NetworkType
	}
	return ""
}

// vpc status distribute Gateway info.
type DGWGatewayStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Gateway name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// admin enable
	AdminState DGWGatewayStatus_AdminState `protobuf:"varint,2,opt,name=admin_state,json=adminState,proto3,enum=sensetime.core.network.vpc.v1.DGWGatewayStatus_AdminState" json:"admin_state,omitempty"`
	// Gateway ip.
	Ip string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	// dest subnet
	Policy string `protobuf:"bytes,4,opt,name=policy,proto3" json:"policy,omitempty"`
	// Gateway uid.
	Id string `protobuf:"bytes,5,opt,name=id,proto3" json:"id,omitempty"`
	// Gateway state
	State DGWGatewayStatus_State `protobuf:"varint,6,opt,name=state,proto3,enum=sensetime.core.network.vpc.v1.DGWGatewayStatus_State" json:"state,omitempty"`
}

func (x *DGWGatewayStatus) Reset() {
	*x = DGWGatewayStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DGWGatewayStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DGWGatewayStatus) ProtoMessage() {}

func (x *DGWGatewayStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DGWGatewayStatus.ProtoReflect.Descriptor instead.
func (*DGWGatewayStatus) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{15}
}

func (x *DGWGatewayStatus) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DGWGatewayStatus) GetAdminState() DGWGatewayStatus_AdminState {
	if x != nil {
		return x.AdminState
	}
	return DGWGatewayStatus_INVALID
}

func (x *DGWGatewayStatus) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *DGWGatewayStatus) GetPolicy() string {
	if x != nil {
		return x.Policy
	}
	return ""
}

func (x *DGWGatewayStatus) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DGWGatewayStatus) GetState() DGWGatewayStatus_State {
	if x != nil {
		return x.State
	}
	return DGWGatewayStatus_CREATING
}

// vpc status natGateway info.
type NATGatewayStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// natGateway name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// natGateway ip.
	Ip string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	// natGateway uid.
	Id string `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *NATGatewayStatus) Reset() {
	*x = NATGatewayStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NATGatewayStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NATGatewayStatus) ProtoMessage() {}

func (x *NATGatewayStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NATGatewayStatus.ProtoReflect.Descriptor instead.
func (*NATGatewayStatus) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{16}
}

func (x *NATGatewayStatus) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *NATGatewayStatus) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *NATGatewayStatus) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// vpc status eip info.
type EIPStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// eip name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// eip ip.
	Ip string `protobuf:"bytes,2,opt,name=ip,proto3" json:"ip,omitempty"`
	// eip uid.
	Id string `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	// The default snat.
	DefaultSnat bool `protobuf:"varint,4,opt,name=default_snat,json=defaultSnat,proto3" json:"default_snat,omitempty"`
	// Related device type，NATGW,POD,SLB,BM.
	AssociationType EIPStatus_AType `protobuf:"varint,5,opt,name=association_type,json=associationType,proto3,enum=sensetime.core.network.vpc.v1.EIPStatus_AType" json:"association_type,omitempty"`
	// Related device or item id, like vpc nat gateway id.
	AssociationId string `protobuf:"bytes,6,opt,name=association_id,json=associationId,proto3" json:"association_id,omitempty"`
}

func (x *EIPStatus) Reset() {
	*x = EIPStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EIPStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EIPStatus) ProtoMessage() {}

func (x *EIPStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EIPStatus.ProtoReflect.Descriptor instead.
func (*EIPStatus) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{17}
}

func (x *EIPStatus) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EIPStatus) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *EIPStatus) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EIPStatus) GetDefaultSnat() bool {
	if x != nil {
		return x.DefaultSnat
	}
	return false
}

func (x *EIPStatus) GetAssociationType() EIPStatus_AType {
	if x != nil {
		return x.AssociationType
	}
	return EIPStatus_NATGW
}

func (x *EIPStatus) GetAssociationId() string {
	if x != nil {
		return x.AssociationId
	}
	return ""
}

// GetVPCMetricsRequest
type GetVPCMetricsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetVPCMetricsRequest) Reset() {
	*x = GetVPCMetricsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVPCMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVPCMetricsRequest) ProtoMessage() {}

func (x *GetVPCMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVPCMetricsRequest.ProtoReflect.Descriptor instead.
func (*GetVPCMetricsRequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{18}
}

// VPC 实例结构体.
// [EN] VPC entity.
type VPC struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The VPC resource id using the form:
	//     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/vpcs/{vpc_name}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The VPC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// ContainerInstance resource display name
	DisplayName string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// ContainerInstance resource description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// The VPC resource uuid.
	Uid string `protobuf:"bytes,5,opt,name=uid,proto3" json:"uid,omitempty"`
	// The VPC resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// The id of the user who created the VPC resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// The id of the user who owns the VPC resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// Available zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// The current state of the VPC resource.
	State VPC_State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.vpc.v1.VPC_State" json:"state,omitempty"`
	// Sku id.
	SkuId string `protobuf:"bytes,12,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// Tags attached to the VPC resource.
	Tags map[string]string `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Properties of the VPC resource.
	Properties *VPCProperties `protobuf:"bytes,14,opt,name=properties,proto3" json:"properties,omitempty"`
	// Payment information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,15,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	// Indicates whether the VPC resource is deleted or not.
	Deleted bool `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// The time when the VPC resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The time when the VPC resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *VPC) Reset() {
	*x = VPC{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VPC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VPC) ProtoMessage() {}

func (x *VPC) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VPC.ProtoReflect.Descriptor instead.
func (*VPC) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{19}
}

func (x *VPC) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VPC) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VPC) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *VPC) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *VPC) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *VPC) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *VPC) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *VPC) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *VPC) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *VPC) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *VPC) GetState() VPC_State {
	if x != nil {
		return x.State
	}
	return VPC_CREATING
}

func (x *VPC) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *VPC) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *VPC) GetProperties() *VPCProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *VPC) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

func (x *VPC) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *VPC) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *VPC) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// 资源实际属性.
// [EN] Real resource properties.
type VPCProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// VPC 级别最大的 cidr, subnet 的 cidr 应该为 VPC cidr 的子集.
	// [EN] VPC level cidr, subnet cidrs should be the subset of the VPC cidr.
	Cidr string `protobuf:"bytes,1,opt,name=cidr,proto3" json:"cidr,omitempty"`
	// 默认 VPC.
	// [EN] Default VPC or not.
	IsDefault bool `protobuf:"varint,2,opt,name=is_default,json=isDefault,proto3" json:"is_default,omitempty"`
	// the VPC resource subnets' type
	SubnetType SubnetType `protobuf:"varint,3,opt,name=subnet_type,json=subnetType,proto3,enum=sensetime.core.network.vpc.v1.SubnetType" json:"subnet_type,omitempty"`
}

func (x *VPCProperties) Reset() {
	*x = VPCProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VPCProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VPCProperties) ProtoMessage() {}

func (x *VPCProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VPCProperties.ProtoReflect.Descriptor instead.
func (*VPCProperties) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{20}
}

func (x *VPCProperties) GetCidr() string {
	if x != nil {
		return x.Cidr
	}
	return ""
}

func (x *VPCProperties) GetIsDefault() bool {
	if x != nil {
		return x.IsDefault
	}
	return false
}

func (x *VPCProperties) GetSubnetType() SubnetType {
	if x != nil {
		return x.SubnetType
	}
	return SubnetType_NULL
}

// [zh] 获取符合 VPC RDMA 相关属性的请求.
// [en] Get requested VPC RDMA status.
type GetVPCRDMARequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅
	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区
	// [en] Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] vpc名
	// [en] The VPC resource name with the restriction:
	// `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VpcName string `protobuf:"bytes,4,opt,name=vpc_name,json=vpcName,proto3" json:"vpc_name,omitempty"`
}

func (x *GetVPCRDMARequest) Reset() {
	*x = GetVPCRDMARequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVPCRDMARequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVPCRDMARequest) ProtoMessage() {}

func (x *GetVPCRDMARequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVPCRDMARequest.ProtoReflect.Descriptor instead.
func (*GetVPCRDMARequest) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{21}
}

func (x *GetVPCRDMARequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetVPCRDMARequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetVPCRDMARequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetVPCRDMARequest) GetVpcName() string {
	if x != nil {
		return x.VpcName
	}
	return ""
}

type VPCRDMAClustersStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// cluster_status
	ClustersStatus []*ClusterStatus `protobuf:"bytes,1,rep,name=clusters_status,json=clustersStatus,proto3" json:"clusters_status,omitempty"`
}

func (x *VPCRDMAClustersStatus) Reset() {
	*x = VPCRDMAClustersStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VPCRDMAClustersStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VPCRDMAClustersStatus) ProtoMessage() {}

func (x *VPCRDMAClustersStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VPCRDMAClustersStatus.ProtoReflect.Descriptor instead.
func (*VPCRDMAClustersStatus) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{22}
}

func (x *VPCRDMAClustersStatus) GetClustersStatus() []*ClusterStatus {
	if x != nil {
		return x.ClustersStatus
	}
	return nil
}

// cluster_status
type ClusterStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// protocol. IB，RoCE，None
	Protocol ClusterStatus_Protocol `protobuf:"varint,2,opt,name=protocol,proto3,enum=sensetime.core.network.vpc.v1.ClusterStatus_Protocol" json:"protocol,omitempty"`
	// network detail information
	NetworkInfos []*NetworkInfo `protobuf:"bytes,3,rep,name=networkInfos,proto3" json:"networkInfos,omitempty"`
	// extend config, such as "sharp_enable":"true"
	Properties map[string]string `protobuf:"bytes,4,rep,name=properties,proto3" json:"properties,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
}

func (x *ClusterStatus) Reset() {
	*x = ClusterStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ClusterStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ClusterStatus) ProtoMessage() {}

func (x *ClusterStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ClusterStatus.ProtoReflect.Descriptor instead.
func (*ClusterStatus) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{23}
}

func (x *ClusterStatus) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ClusterStatus) GetProtocol() ClusterStatus_Protocol {
	if x != nil {
		return x.Protocol
	}
	return ClusterStatus_PROTOCOL_UNSPECIFIED
}

func (x *ClusterStatus) GetNetworkInfos() []*NetworkInfo {
	if x != nil {
		return x.NetworkInfos
	}
	return nil
}

func (x *ClusterStatus) GetProperties() map[string]string {
	if x != nil {
		return x.Properties
	}
	return nil
}

type NetworkInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// nic_count. such as 1,2,4,8,16
	NicCount int32 `protobuf:"varint,1,opt,name=nic_count,json=nicCount,proto3" json:"nic_count,omitempty"`
	// bandwidth.
	Bandwidth string `protobuf:"bytes,2,opt,name=bandwidth,proto3" json:"bandwidth,omitempty"`
	// nodes count
	NodeCount int32 `protobuf:"varint,3,opt,name=node_count,json=nodeCount,proto3" json:"node_count,omitempty"`
}

func (x *NetworkInfo) Reset() {
	*x = NetworkInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vpc_v1_vpc_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *NetworkInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*NetworkInfo) ProtoMessage() {}

func (x *NetworkInfo) ProtoReflect() protoreflect.Message {
	mi := &file_network_vpc_v1_vpc_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use NetworkInfo.ProtoReflect.Descriptor instead.
func (*NetworkInfo) Descriptor() ([]byte, []int) {
	return file_network_vpc_v1_vpc_proto_rawDescGZIP(), []int{24}
}

func (x *NetworkInfo) GetNicCount() int32 {
	if x != nil {
		return x.NicCount
	}
	return 0
}

func (x *NetworkInfo) GetBandwidth() string {
	if x != nil {
		return x.Bandwidth
	}
	return ""
}

func (x *NetworkInfo) GetNodeCount() int32 {
	if x != nil {
		return x.NodeCount
	}
	return 0
}

var File_network_vpc_v1_vpc_proto protoreflect.FileDescriptor

var file_network_vpc_v1_vpc_proto_rawDesc = []byte{
	0x0a, 0x18, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x2f, 0x76, 0x31,
	0x2f, 0x76, 0x70, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x68,
	0x69, 0x67, 0x67, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8e,
	0x02, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x50, 0x43, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53,
	0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22,
	0x9b, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x56, 0x50, 0x43, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e,
	0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x70, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x70, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa1, 0x01,
	0x0a, 0x13, 0x47, 0x65, 0x74, 0x56, 0x50, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x70, 0x63, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x70, 0x63, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0x80, 0x02, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x50, 0x43, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x76, 0x70, 0x63, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42, 0x27, 0x72, 0x25,
	0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x76, 0x70, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34,
	0x0a, 0x03, 0x76, 0x70, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x50, 0x43, 0x52,
	0x03, 0x76, 0x70, 0x63, 0x22, 0xa1, 0x02, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56,
	0x50, 0x43, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x70,
	0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x70,
	0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x03, 0x76, 0x70, 0x63, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x56, 0x50, 0x43, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x03, 0x76, 0x70, 0x63, 0x12, 0x3b, 0x0a, 0x0b, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b, 0x22, 0x8b, 0x02, 0x0a, 0x13, 0x56, 0x50, 0x43,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x12, 0x75, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x4d, 0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32,
	0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34,
	0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30,
	0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c,
	0x36, 0x32, 0x7d, 0x29, 0x24, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4a, 0x0a, 0x0b, 0x73, 0x75, 0x62,
	0x6e, 0x65, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x75, 0x62, 0x6e, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x6e, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xb6, 0x02, 0x0a, 0x13, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x56, 0x50, 0x43, 0x44, 0x67, 0x77, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b,
	0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12,
	0x19, 0x0a, 0x08, 0x76, 0x70, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x76, 0x70, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x64, 0x67,
	0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x64, 0x67,
	0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x03, 0x64, 0x67, 0x77, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x47, 0x57, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x52, 0x03, 0x64,
	0x67, 0x77, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x73,
	0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d,
	0x61, 0x73, 0x6b, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b, 0x22,
	0xd0, 0x07, 0x0a, 0x0a, 0x44, 0x47, 0x57, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x3e,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42,
	0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x73,
	0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x50, 0xfa, 0x42, 0x4d, 0x72, 0x4b, 0x18, 0x3f, 0x32, 0x44, 0x5e,
	0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30,
	0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d,
	0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32,
	0x7d, 0x29, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x45, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x47, 0x57, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x55,
	0x0a, 0x0b, 0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x47, 0x57, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x41,
	0x64, 0x6d, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x61, 0x64, 0x6d, 0x69, 0x6e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x67, 0x77, 0x5f, 0x65, 0x78, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x70, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x67,
	0x77, 0x45, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x70, 0x12, 0x1b, 0x0a, 0x09, 0x67,
	0x77, 0x5f, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x67, 0x77, 0x50, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x70, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x70, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65,
	0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18,
	0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d,
	0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x22, 0x6f, 0x0a,
	0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x44, 0x41, 0x54, 0x49, 0x4e, 0x47,
	0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x0c,
	0x0a, 0x08, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07,
	0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49,
	0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x41, 0x43, 0x54, 0x49, 0x56,
	0x45, 0x10, 0x06, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x52, 0x52, 0x4f, 0x52, 0x10, 0x07, 0x22, 0x32,
	0x0a, 0x0a, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0b, 0x0a, 0x07,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x4e, 0x41,
	0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45,
	0x10, 0x02, 0x22, 0x9e, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x50, 0x43,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x70, 0x63, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x70, 0x63, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x91, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x50, 0x43, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x04, 0x76, 0x70, 0x63, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x50, 0x43, 0x52, 0x04, 0x76, 0x70, 0x63, 0x73,
	0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f,
	0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x22, 0x9c, 0x03, 0x0a, 0x09, 0x56, 0x50, 0x43, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x64, 0x72, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x64, 0x72, 0x12, 0x4e, 0x0a, 0x0a, 0x6e, 0x61, 0x6d,
	0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x61,
	0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0a, 0x6e,
	0x61, 0x6d, 0x65, 0x73, 0x70, 0x61, 0x63, 0x65, 0x73, 0x12, 0x45, 0x0a, 0x07, 0x73, 0x75, 0x62,
	0x6e, 0x65, 0x74, 0x73, 0x18, 0x03, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62, 0x6e, 0x65,
	0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x07, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x73,
	0x12, 0x52, 0x0a, 0x0c, 0x6e, 0x61, 0x74, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x73,
	0x18, 0x04, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x41, 0x54, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x6e, 0x61, 0x74, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x73, 0x12, 0x3c, 0x0a, 0x04, 0x65, 0x69, 0x70, 0x73, 0x18, 0x05, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x04, 0x65, 0x69,
	0x70, 0x73, 0x12, 0x52, 0x0a, 0x0c, 0x64, 0x67, 0x77, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x47, 0x57, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x64, 0x67, 0x77, 0x47, 0x61,
	0x74, 0x65, 0x77, 0x61, 0x79, 0x73, 0x22, 0x67, 0x0a, 0x0a, 0x56, 0x50, 0x43, 0x4d, 0x65, 0x74,
	0x72, 0x69, 0x63, 0x73, 0x12, 0x59, 0x0a, 0x10, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x0f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x22,
	0x65, 0x0a, 0x0f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x75, 0x6e, 0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74,
	0x12, 0x14, 0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x3d, 0x0a, 0x0f, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x70,
	0x61, 0x63, 0x65, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x16, 0x0a,
	0x06, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73,
	0x75, 0x62, 0x6e, 0x65, 0x74, 0x22, 0x9b, 0x01, 0x0a, 0x0c, 0x53, 0x75, 0x62, 0x6e, 0x65, 0x74,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69,
	0x64, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x64, 0x72, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x14,
	0x0a, 0x05, 0x73, 0x63, 0x6f, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73,
	0x63, 0x6f, 0x70, 0x65, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72,
	0x12, 0x21, 0x0a, 0x0c, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x54,
	0x79, 0x70, 0x65, 0x22, 0xad, 0x03, 0x0a, 0x10, 0x44, 0x47, 0x57, 0x47, 0x61, 0x74, 0x65, 0x77,
	0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x5b, 0x0a, 0x0b,
	0x61, 0x64, 0x6d, 0x69, 0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x3a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x47, 0x57, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x2e, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x0a, 0x61,
	0x64, 0x6d, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f, 0x6c,
	0x69, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6f, 0x6c, 0x69, 0x63,
	0x79, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69,
	0x64, 0x12, 0x4b, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x35, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x47, 0x57, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x32,
	0x0a, 0x0a, 0x41, 0x64, 0x6d, 0x69, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0b, 0x0a, 0x07,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x45, 0x4e, 0x41,
	0x42, 0x4c, 0x45, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45,
	0x10, 0x02, 0x22, 0x6f, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x43,
	0x52, 0x45, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x44,
	0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56,
	0x45, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x10,
	0x03, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0a,
	0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x06, 0x12, 0x09, 0x0a, 0x05, 0x45, 0x52, 0x52, 0x4f,
	0x52, 0x10, 0x07, 0x22, 0x46, 0x0a, 0x10, 0x4e, 0x41, 0x54, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69,
	0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xa4, 0x02, 0x0a, 0x09,
	0x45, 0x49, 0x50, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12, 0x0e, 0x0a,
	0x02, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a,
	0x0c, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x73, 0x6e, 0x61, 0x74, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x0b, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x6e, 0x61, 0x74,
	0x12, 0x59, 0x0a, 0x10, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2e, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x2e, 0x41, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x61, 0x73, 0x73, 0x6f,
	0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x25, 0x0a, 0x0e, 0x61,
	0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x49, 0x64, 0x22, 0x3e, 0x0a, 0x05, 0x41, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x4e,
	0x41, 0x54, 0x47, 0x57, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x50, 0x4f, 0x44, 0x10, 0x01, 0x12,
	0x07, 0x0a, 0x03, 0x53, 0x4c, 0x42, 0x10, 0x02, 0x12, 0x06, 0x0a, 0x02, 0x42, 0x4d, 0x10, 0x03,
	0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x41, 0x54, 0x47, 0x57, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x42, 0x4d,
	0x10, 0x04, 0x22, 0x16, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x56, 0x50, 0x43, 0x4d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x22, 0xb5, 0x07, 0x0a, 0x03, 0x56,
	0x50, 0x43, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02,
	0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x70, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61,
	0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x4d, 0xfa, 0x42,
	0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41,
	0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66,
	0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c,
	0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d,
	0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24, 0x52, 0x0b, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64,
	0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x3e, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x50, 0x43, 0x2e,
	0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x15, 0x0a, 0x06,
	0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x6b,
	0x75, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28,
	0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x56, 0x50, 0x43, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52,
	0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x4c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x50, 0x43, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a,
	0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x56, 0x0a, 0x05, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10,
	0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x44, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12,
	0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x44,
	0x45, 0x4c, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c,
	0x45, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44,
	0x10, 0x05, 0x22, 0x8e, 0x01, 0x0a, 0x0d, 0x56, 0x50, 0x43, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x64, 0x72, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x64, 0x72, 0x12, 0x1d, 0x0a, 0x0a, 0x69, 0x73, 0x5f, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x09, 0x69, 0x73,
	0x44, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x12, 0x4a, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x6e, 0x65,
	0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x29, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x75, 0x62,
	0x6e, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x54,
	0x79, 0x70, 0x65, 0x22, 0x9f, 0x01, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x56, 0x50, 0x43, 0x52, 0x44,
	0x4d, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x76, 0x70,
	0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x76, 0x70,
	0x63, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x6e, 0x0a, 0x15, 0x56, 0x50, 0x43, 0x52, 0x44, 0x4d, 0x41,
	0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x55,
	0x0a, 0x0f, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0e, 0x63, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xa5, 0x03, 0x0a, 0x0d, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65,
	0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x51, 0x0a, 0x08, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x35, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x6c,
	0x75, 0x73, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x4e,
	0x0a, 0x0c, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x03,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x0c, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x5c,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x3c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x2e, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79,
	0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x1a, 0x3d, 0x0a, 0x0f,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12,
	0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65,
	0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x40, 0x0a, 0x08, 0x50,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x18, 0x0a, 0x14, 0x50, 0x52, 0x4f, 0x54, 0x4f,
	0x43, 0x4f, 0x4c, 0x5f, 0x55, 0x4e, 0x53, 0x50, 0x45, 0x43, 0x49, 0x46, 0x49, 0x45, 0x44, 0x10,
	0x00, 0x12, 0x06, 0x0a, 0x02, 0x49, 0x42, 0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x52, 0x6f, 0x43,
	0x45, 0x10, 0x02, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x6f, 0x6e, 0x65, 0x10, 0x03, 0x22, 0x67, 0x0a,
	0x0b, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1b, 0x0a, 0x09,
	0x6e, 0x69, 0x63, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x6e, 0x69, 0x63, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1c, 0x0a, 0x09, 0x62, 0x61, 0x6e,
	0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x62, 0x61,
	0x6e, 0x64, 0x77, 0x69, 0x64, 0x74, 0x68, 0x12, 0x1d, 0x0a, 0x0a, 0x6e, 0x6f, 0x64, 0x65, 0x5f,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x6e, 0x6f, 0x64,
	0x65, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x2a, 0x89, 0x01, 0x0a, 0x0a, 0x53, 0x75, 0x62, 0x6e, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x55, 0x4c, 0x4c, 0x10, 0x00, 0x12,
	0x0f, 0x0a, 0x0b, 0x50, 0x4f, 0x44, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x01,
	0x12, 0x18, 0x0a, 0x14, 0x50, 0x4f, 0x44, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f,
	0x54, 0x52, 0x41, 0x49, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x13, 0x0a, 0x0f, 0x50, 0x4f,
	0x44, 0x5f, 0x42, 0x4d, 0x53, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x10, 0x03, 0x12,
	0x1c, 0x0a, 0x18, 0x50, 0x4f, 0x44, 0x5f, 0x42, 0x4d, 0x53, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49,
	0x43, 0x45, 0x5f, 0x54, 0x52, 0x41, 0x49, 0x4e, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x13, 0x0a,
	0x0f, 0x50, 0x4f, 0x44, 0x5f, 0x53, 0x45, 0x52, 0x56, 0x49, 0x43, 0x45, 0x5f, 0x42, 0x4d, 0x53,
	0x10, 0x05, 0x32, 0x9a, 0x1b, 0x0a, 0x04, 0x56, 0x50, 0x43, 0x73, 0x12, 0xc0, 0x02, 0x0a, 0x08,
	0x4c, 0x69, 0x73, 0x74, 0x56, 0x50, 0x43, 0x73, 0x12, 0x2e, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x50, 0x43,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x50, 0x43,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xd2, 0x01, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x6a, 0x12, 0x68, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73,
	0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x73, 0x80, 0xb5, 0x18, 0x01,
	0x9a, 0xb5, 0x18, 0x5a, 0x0a, 0x4a, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x12, 0x0c, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x12, 0xb9,
	0x02, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x56, 0x50, 0x43, 0x12, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x50, 0x43,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x50, 0x43, 0x22, 0xdc, 0x01, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x75, 0x12, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76,
	0x70, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e,
	0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x73, 0x2f, 0x7b,
	0x76, 0x70, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18,
	0x59, 0x0a, 0x4a, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0b, 0x76,
	0x70, 0x63, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x67, 0x65, 0x74, 0x12, 0xef, 0x02, 0x0a, 0x0c, 0x47,
	0x65, 0x74, 0x56, 0x50, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56,
	0x50, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x56, 0x50, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x80, 0x02, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x7c, 0x12, 0x7a, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73,
	0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x70,
	0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x80, 0xb5,
	0x18, 0x01, 0x9a, 0xb5, 0x18, 0x76, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f,
	0x76, 0x70, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12,
	0x0b, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x67, 0x65, 0x74, 0x12, 0xf9, 0x02, 0x0a,
	0x10, 0x47, 0x65, 0x74, 0x56, 0x50, 0x43, 0x44, 0x61, 0x74, 0x61, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x32, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x50, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76,
	0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x50, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22,
	0x86, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x81, 0x01, 0x12, 0x7f, 0x2f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f,
	0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5,
	0x18, 0x76, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a,
	0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x73,
	0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0b, 0x76, 0x70, 0x63,
	0x2e, 0x76, 0x70, 0x63, 0x2e, 0x67, 0x65, 0x74, 0x12, 0xcb, 0x02, 0x0a, 0x09, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x56, 0x50, 0x43, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x50, 0x43,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x50, 0x43, 0x22, 0xe8, 0x01, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x7a, 0x22, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76,
	0x70, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e,
	0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x73, 0x2f, 0x7b,
	0x76, 0x70, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x03, 0x76, 0x70, 0x63, 0x88, 0xb5,
	0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x5c, 0x0a, 0x4a, 0x2f, 0x72, 0x6d, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x70, 0x63, 0x2e,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0xe8, 0x02, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x56, 0x50, 0x43, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x50, 0x43, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76,
	0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x50, 0x43, 0x22, 0x85, 0x02, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x7a, 0x32, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73,
	0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x70,
	0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x03, 0x76, 0x70, 0x63, 0x88, 0xb5, 0x18, 0x01,
	0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x76, 0x70, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x12, 0x0e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x87, 0x03, 0x0a, 0x0c, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x50, 0x43, 0x44,
	0x67, 0x77, 0x12, 0x32, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x50, 0x43, 0x44, 0x67, 0x77, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x47, 0x57, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x22, 0x97, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x8b, 0x01, 0x32, 0x83, 0x01, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x76, 0x70, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x64, 0x67, 0x77, 0x73, 0x2f, 0x7b, 0x64, 0x67, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x3a, 0x03, 0x64, 0x67, 0x77, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5,
	0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a,
	0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x73,
	0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x76, 0x70, 0x63,
	0x2e, 0x76, 0x70, 0x63, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xd7, 0x02, 0x0a, 0x09,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x50, 0x43, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x56, 0x50, 0x43, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x80, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x75, 0x2a, 0x73, 0x2f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d,
	0x2f, 0x76, 0x70, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72,
	0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b,
	0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0xfd, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x56, 0x50, 0x43,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x12, 0x33, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x50, 0x43, 0x4d, 0x65,
	0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x50, 0x43,
	0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x22, 0x8b, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23,
	0x12, 0x21, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x2f, 0x64,
	0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x76, 0x70, 0x63, 0x73, 0x2f, 0x6d, 0x65, 0x74, 0x72,
	0x69, 0x63, 0x73, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x5a, 0x0a, 0x4a, 0x2f, 0x72, 0x6d,
	0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0c, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x70, 0x63,
	0x2e, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x87, 0x03, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x56, 0x50, 0x43,
	0x52, 0x44, 0x4d, 0x41, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x30, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x50,
	0x43, 0x52, 0x44, 0x4d, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x50, 0x43,
	0x52, 0x44, 0x4d, 0x41, 0x43, 0x6c, 0x75, 0x73, 0x74, 0x65, 0x72, 0x73, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x22, 0x8a, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x89, 0x01, 0x12, 0x86, 0x01, 0x2f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x2f, 0x64, 0x61, 0x74, 0x61,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73,
	0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x70,
	0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x64, 0x6d, 0x61, 0x5f, 0x63, 0x6c, 0x75,
	0x73, 0x74, 0x65, 0x72, 0x73, 0x9a, 0xb5, 0x18, 0x76, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f,
	0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e,
	0x65, 0x7d, 0x2f, 0x76, 0x70, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x70, 0x63, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x12, 0x0b, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x67, 0x65, 0x74, 0x42,
	0x50, 0x5a, 0x4e, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x62, 0x6a, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x61, 0x72, 0x79, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x73, 0x6f,
	0x6e, 0x2d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x70, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x70,
	0x63, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_network_vpc_v1_vpc_proto_rawDescOnce sync.Once
	file_network_vpc_v1_vpc_proto_rawDescData = file_network_vpc_v1_vpc_proto_rawDesc
)

func file_network_vpc_v1_vpc_proto_rawDescGZIP() []byte {
	file_network_vpc_v1_vpc_proto_rawDescOnce.Do(func() {
		file_network_vpc_v1_vpc_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_vpc_v1_vpc_proto_rawDescData)
	})
	return file_network_vpc_v1_vpc_proto_rawDescData
}

var file_network_vpc_v1_vpc_proto_enumTypes = make([]protoimpl.EnumInfo, 8)
var file_network_vpc_v1_vpc_proto_msgTypes = make([]protoimpl.MessageInfo, 27)
var file_network_vpc_v1_vpc_proto_goTypes = []interface{}{
	(SubnetType)(0),                  // 0: sensetime.core.network.vpc.v1.SubnetType
	(DGWGateway_State)(0),            // 1: sensetime.core.network.vpc.v1.DGWGateway.State
	(DGWGateway_AdminState)(0),       // 2: sensetime.core.network.vpc.v1.DGWGateway.AdminState
	(DGWGatewayStatus_AdminState)(0), // 3: sensetime.core.network.vpc.v1.DGWGatewayStatus.AdminState
	(DGWGatewayStatus_State)(0),      // 4: sensetime.core.network.vpc.v1.DGWGatewayStatus.State
	(EIPStatus_AType)(0),             // 5: sensetime.core.network.vpc.v1.EIPStatus.AType
	(VPC_State)(0),                   // 6: sensetime.core.network.vpc.v1.VPC.State
	(ClusterStatus_Protocol)(0),      // 7: sensetime.core.network.vpc.v1.ClusterStatus.Protocol
	(*ListVPCsRequest)(nil),          // 8: sensetime.core.network.vpc.v1.ListVPCsRequest
	(*GetVPCRequest)(nil),            // 9: sensetime.core.network.vpc.v1.GetVPCRequest
	(*GetVPCStatusRequest)(nil),      // 10: sensetime.core.network.vpc.v1.GetVPCStatusRequest
	(*CreateVPCRequest)(nil),         // 11: sensetime.core.network.vpc.v1.CreateVPCRequest
	(*UpdateVPCRequest)(nil),         // 12: sensetime.core.network.vpc.v1.UpdateVPCRequest
	(*VPCUpdateProperties)(nil),      // 13: sensetime.core.network.vpc.v1.VPCUpdateProperties
	(*UpdateVPCDgwRequest)(nil),      // 14: sensetime.core.network.vpc.v1.UpdateVPCDgwRequest
	(*DGWGateway)(nil),               // 15: sensetime.core.network.vpc.v1.DGWGateway
	(*DeleteVPCRequest)(nil),         // 16: sensetime.core.network.vpc.v1.DeleteVPCRequest
	(*ListVPCsResponse)(nil),         // 17: sensetime.core.network.vpc.v1.ListVPCsResponse
	(*VPCStatus)(nil),                // 18: sensetime.core.network.vpc.v1.VPCStatus
	(*VPCMetrics)(nil),               // 19: sensetime.core.network.vpc.v1.VPCMetrics
	(*ResourceMetrics)(nil),          // 20: sensetime.core.network.vpc.v1.ResourceMetrics
	(*NamespaceStatus)(nil),          // 21: sensetime.core.network.vpc.v1.NamespaceStatus
	(*SubnetStatus)(nil),             // 22: sensetime.core.network.vpc.v1.SubnetStatus
	(*DGWGatewayStatus)(nil),         // 23: sensetime.core.network.vpc.v1.DGWGatewayStatus
	(*NATGatewayStatus)(nil),         // 24: sensetime.core.network.vpc.v1.NATGatewayStatus
	(*EIPStatus)(nil),                // 25: sensetime.core.network.vpc.v1.EIPStatus
	(*GetVPCMetricsRequest)(nil),     // 26: sensetime.core.network.vpc.v1.GetVPCMetricsRequest
	(*VPC)(nil),                      // 27: sensetime.core.network.vpc.v1.VPC
	(*VPCProperties)(nil),            // 28: sensetime.core.network.vpc.v1.VPCProperties
	(*GetVPCRDMARequest)(nil),        // 29: sensetime.core.network.vpc.v1.GetVPCRDMARequest
	(*VPCRDMAClustersStatus)(nil),    // 30: sensetime.core.network.vpc.v1.VPCRDMAClustersStatus
	(*ClusterStatus)(nil),            // 31: sensetime.core.network.vpc.v1.ClusterStatus
	(*NetworkInfo)(nil),              // 32: sensetime.core.network.vpc.v1.NetworkInfo
	nil,                              // 33: sensetime.core.network.vpc.v1.VPC.TagsEntry
	nil,                              // 34: sensetime.core.network.vpc.v1.ClusterStatus.PropertiesEntry
	(*fieldmaskpb.FieldMask)(nil),    // 35: google.protobuf.FieldMask
	(*timestamppb.Timestamp)(nil),    // 36: google.protobuf.Timestamp
	(*v1.OrderInfo)(nil),             // 37: sensetime.core.higgs.common.v1.OrderInfo
	(*emptypb.Empty)(nil),            // 38: google.protobuf.Empty
}
var file_network_vpc_v1_vpc_proto_depIdxs = []int32{
	27, // 0: sensetime.core.network.vpc.v1.CreateVPCRequest.vpc:type_name -> sensetime.core.network.vpc.v1.VPC
	13, // 1: sensetime.core.network.vpc.v1.UpdateVPCRequest.vpc:type_name -> sensetime.core.network.vpc.v1.VPCUpdateProperties
	35, // 2: sensetime.core.network.vpc.v1.UpdateVPCRequest.update_mask:type_name -> google.protobuf.FieldMask
	0,  // 3: sensetime.core.network.vpc.v1.VPCUpdateProperties.subnet_type:type_name -> sensetime.core.network.vpc.v1.SubnetType
	15, // 4: sensetime.core.network.vpc.v1.UpdateVPCDgwRequest.dgw:type_name -> sensetime.core.network.vpc.v1.DGWGateway
	35, // 5: sensetime.core.network.vpc.v1.UpdateVPCDgwRequest.update_mask:type_name -> google.protobuf.FieldMask
	1,  // 6: sensetime.core.network.vpc.v1.DGWGateway.state:type_name -> sensetime.core.network.vpc.v1.DGWGateway.State
	2,  // 7: sensetime.core.network.vpc.v1.DGWGateway.admin_state:type_name -> sensetime.core.network.vpc.v1.DGWGateway.AdminState
	36, // 8: sensetime.core.network.vpc.v1.DGWGateway.create_time:type_name -> google.protobuf.Timestamp
	36, // 9: sensetime.core.network.vpc.v1.DGWGateway.update_time:type_name -> google.protobuf.Timestamp
	27, // 10: sensetime.core.network.vpc.v1.ListVPCsResponse.vpcs:type_name -> sensetime.core.network.vpc.v1.VPC
	21, // 11: sensetime.core.network.vpc.v1.VPCStatus.namespaces:type_name -> sensetime.core.network.vpc.v1.NamespaceStatus
	22, // 12: sensetime.core.network.vpc.v1.VPCStatus.subnets:type_name -> sensetime.core.network.vpc.v1.SubnetStatus
	24, // 13: sensetime.core.network.vpc.v1.VPCStatus.nat_gateways:type_name -> sensetime.core.network.vpc.v1.NATGatewayStatus
	25, // 14: sensetime.core.network.vpc.v1.VPCStatus.eips:type_name -> sensetime.core.network.vpc.v1.EIPStatus
	23, // 15: sensetime.core.network.vpc.v1.VPCStatus.dgw_gateways:type_name -> sensetime.core.network.vpc.v1.DGWGatewayStatus
	20, // 16: sensetime.core.network.vpc.v1.VPCMetrics.resource_metrics:type_name -> sensetime.core.network.vpc.v1.ResourceMetrics
	3,  // 17: sensetime.core.network.vpc.v1.DGWGatewayStatus.admin_state:type_name -> sensetime.core.network.vpc.v1.DGWGatewayStatus.AdminState
	4,  // 18: sensetime.core.network.vpc.v1.DGWGatewayStatus.state:type_name -> sensetime.core.network.vpc.v1.DGWGatewayStatus.State
	5,  // 19: sensetime.core.network.vpc.v1.EIPStatus.association_type:type_name -> sensetime.core.network.vpc.v1.EIPStatus.AType
	6,  // 20: sensetime.core.network.vpc.v1.VPC.state:type_name -> sensetime.core.network.vpc.v1.VPC.State
	33, // 21: sensetime.core.network.vpc.v1.VPC.tags:type_name -> sensetime.core.network.vpc.v1.VPC.TagsEntry
	28, // 22: sensetime.core.network.vpc.v1.VPC.properties:type_name -> sensetime.core.network.vpc.v1.VPCProperties
	37, // 23: sensetime.core.network.vpc.v1.VPC.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	36, // 24: sensetime.core.network.vpc.v1.VPC.create_time:type_name -> google.protobuf.Timestamp
	36, // 25: sensetime.core.network.vpc.v1.VPC.update_time:type_name -> google.protobuf.Timestamp
	0,  // 26: sensetime.core.network.vpc.v1.VPCProperties.subnet_type:type_name -> sensetime.core.network.vpc.v1.SubnetType
	31, // 27: sensetime.core.network.vpc.v1.VPCRDMAClustersStatus.clusters_status:type_name -> sensetime.core.network.vpc.v1.ClusterStatus
	7,  // 28: sensetime.core.network.vpc.v1.ClusterStatus.protocol:type_name -> sensetime.core.network.vpc.v1.ClusterStatus.Protocol
	32, // 29: sensetime.core.network.vpc.v1.ClusterStatus.networkInfos:type_name -> sensetime.core.network.vpc.v1.NetworkInfo
	34, // 30: sensetime.core.network.vpc.v1.ClusterStatus.properties:type_name -> sensetime.core.network.vpc.v1.ClusterStatus.PropertiesEntry
	8,  // 31: sensetime.core.network.vpc.v1.VPCs.ListVPCs:input_type -> sensetime.core.network.vpc.v1.ListVPCsRequest
	9,  // 32: sensetime.core.network.vpc.v1.VPCs.GetVPC:input_type -> sensetime.core.network.vpc.v1.GetVPCRequest
	10, // 33: sensetime.core.network.vpc.v1.VPCs.GetVPCStatus:input_type -> sensetime.core.network.vpc.v1.GetVPCStatusRequest
	10, // 34: sensetime.core.network.vpc.v1.VPCs.GetVPCDataStatus:input_type -> sensetime.core.network.vpc.v1.GetVPCStatusRequest
	11, // 35: sensetime.core.network.vpc.v1.VPCs.CreateVPC:input_type -> sensetime.core.network.vpc.v1.CreateVPCRequest
	12, // 36: sensetime.core.network.vpc.v1.VPCs.UpdateVPC:input_type -> sensetime.core.network.vpc.v1.UpdateVPCRequest
	14, // 37: sensetime.core.network.vpc.v1.VPCs.UpdateVPCDgw:input_type -> sensetime.core.network.vpc.v1.UpdateVPCDgwRequest
	16, // 38: sensetime.core.network.vpc.v1.VPCs.DeleteVPC:input_type -> sensetime.core.network.vpc.v1.DeleteVPCRequest
	26, // 39: sensetime.core.network.vpc.v1.VPCs.GetVPCMetrics:input_type -> sensetime.core.network.vpc.v1.GetVPCMetricsRequest
	29, // 40: sensetime.core.network.vpc.v1.VPCs.GetVPCRDMAStatus:input_type -> sensetime.core.network.vpc.v1.GetVPCRDMARequest
	17, // 41: sensetime.core.network.vpc.v1.VPCs.ListVPCs:output_type -> sensetime.core.network.vpc.v1.ListVPCsResponse
	27, // 42: sensetime.core.network.vpc.v1.VPCs.GetVPC:output_type -> sensetime.core.network.vpc.v1.VPC
	18, // 43: sensetime.core.network.vpc.v1.VPCs.GetVPCStatus:output_type -> sensetime.core.network.vpc.v1.VPCStatus
	18, // 44: sensetime.core.network.vpc.v1.VPCs.GetVPCDataStatus:output_type -> sensetime.core.network.vpc.v1.VPCStatus
	27, // 45: sensetime.core.network.vpc.v1.VPCs.CreateVPC:output_type -> sensetime.core.network.vpc.v1.VPC
	27, // 46: sensetime.core.network.vpc.v1.VPCs.UpdateVPC:output_type -> sensetime.core.network.vpc.v1.VPC
	15, // 47: sensetime.core.network.vpc.v1.VPCs.UpdateVPCDgw:output_type -> sensetime.core.network.vpc.v1.DGWGateway
	38, // 48: sensetime.core.network.vpc.v1.VPCs.DeleteVPC:output_type -> google.protobuf.Empty
	19, // 49: sensetime.core.network.vpc.v1.VPCs.GetVPCMetrics:output_type -> sensetime.core.network.vpc.v1.VPCMetrics
	30, // 50: sensetime.core.network.vpc.v1.VPCs.GetVPCRDMAStatus:output_type -> sensetime.core.network.vpc.v1.VPCRDMAClustersStatus
	41, // [41:51] is the sub-list for method output_type
	31, // [31:41] is the sub-list for method input_type
	31, // [31:31] is the sub-list for extension type_name
	31, // [31:31] is the sub-list for extension extendee
	0,  // [0:31] is the sub-list for field type_name
}

func init() { file_network_vpc_v1_vpc_proto_init() }
func file_network_vpc_v1_vpc_proto_init() {
	if File_network_vpc_v1_vpc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_network_vpc_v1_vpc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListVPCsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVPCRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVPCStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateVPCRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVPCRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VPCUpdateProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVPCDgwRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DGWGateway); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteVPCRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListVPCsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VPCStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VPCMetrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceMetrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NamespaceStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SubnetStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DGWGatewayStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NATGatewayStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EIPStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVPCMetricsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VPC); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VPCProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVPCRDMARequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VPCRDMAClustersStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ClusterStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vpc_v1_vpc_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*NetworkInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_network_vpc_v1_vpc_proto_msgTypes[5].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_vpc_v1_vpc_proto_rawDesc,
			NumEnums:      8,
			NumMessages:   27,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_network_vpc_v1_vpc_proto_goTypes,
		DependencyIndexes: file_network_vpc_v1_vpc_proto_depIdxs,
		EnumInfos:         file_network_vpc_v1_vpc_proto_enumTypes,
		MessageInfos:      file_network_vpc_v1_vpc_proto_msgTypes,
	}.Build()
	File_network_vpc_v1_vpc_proto = out.File
	file_network_vpc_v1_vpc_proto_rawDesc = nil
	file_network_vpc_v1_vpc_proto_goTypes = nil
	file_network_vpc_v1_vpc_proto_depIdxs = nil
}
