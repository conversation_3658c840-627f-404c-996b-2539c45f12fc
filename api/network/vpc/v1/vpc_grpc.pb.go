// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/vpc/v1/vpc.proto

package vpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// VPCsClient is the client API for VPCs service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VPCsClient interface {
	// 列举符合请求的所有 VPCs.
	// [EN] List requested VPCs.
	ListVPCs(ctx context.Context, in *ListVPCsRequest, opts ...grpc.CallOption) (*ListVPCsResponse, error)
	// 获取符合请求的一个 VPC.
	// [EN] Get a requested VPC.
	GetVPC(ctx context.Context, in *GetVPCRequest, opts ...grpc.CallOption) (*VPC, error)
	// 获取符合请求的一个 VPC 相关属性.
	// [EN] Get a requested VPC status.
	GetVPCStatus(ctx context.Context, in *GetVPCStatusRequest, opts ...grpc.CallOption) (*VPCStatus, error)
	// 获取符合请求的一个 VPC 相关属性.
	// [EN] Get a requested VPC status.
	GetVPCDataStatus(ctx context.Context, in *GetVPCStatusRequest, opts ...grpc.CallOption) (*VPCStatus, error)
	// 创建一个 VPC.
	// [EN] Create a VPC.
	CreateVPC(ctx context.Context, in *CreateVPCRequest, opts ...grpc.CallOption) (*VPC, error)
	// 更新 VPC 可编辑字段.
	// [EN] Update VPC editable properties.
	UpdateVPC(ctx context.Context, in *UpdateVPCRequest, opts ...grpc.CallOption) (*VPC, error)
	// 更新 VPCDGW 可编辑字段.
	// [EN] Update VPCDGW editable properties.
	UpdateVPCDgw(ctx context.Context, in *UpdateVPCDgwRequest, opts ...grpc.CallOption) (*DGWGateway, error)
	// 删除一个 VPC.
	// [EN] Delete a VPC.
	DeleteVPC(ctx context.Context, in *DeleteVPCRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// VPC metrics.
	// [EN] VPC metrics.
	GetVPCMetrics(ctx context.Context, in *GetVPCMetricsRequest, opts ...grpc.CallOption) (*VPCMetrics, error)
	// [zh] 获取VPC内RDMA集群信息.
	// [en] get rdma cluster infor in vpc.
	GetVPCRDMAStatus(ctx context.Context, in *GetVPCRDMARequest, opts ...grpc.CallOption) (*VPCRDMAClustersStatus, error)
}

type vPCsClient struct {
	cc grpc.ClientConnInterface
}

func NewVPCsClient(cc grpc.ClientConnInterface) VPCsClient {
	return &vPCsClient{cc}
}

func (c *vPCsClient) ListVPCs(ctx context.Context, in *ListVPCsRequest, opts ...grpc.CallOption) (*ListVPCsResponse, error) {
	out := new(ListVPCsResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VPCs/ListVPCs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vPCsClient) GetVPC(ctx context.Context, in *GetVPCRequest, opts ...grpc.CallOption) (*VPC, error) {
	out := new(VPC)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VPCs/GetVPC", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vPCsClient) GetVPCStatus(ctx context.Context, in *GetVPCStatusRequest, opts ...grpc.CallOption) (*VPCStatus, error) {
	out := new(VPCStatus)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VPCs/GetVPCStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vPCsClient) GetVPCDataStatus(ctx context.Context, in *GetVPCStatusRequest, opts ...grpc.CallOption) (*VPCStatus, error) {
	out := new(VPCStatus)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VPCs/GetVPCDataStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vPCsClient) CreateVPC(ctx context.Context, in *CreateVPCRequest, opts ...grpc.CallOption) (*VPC, error) {
	out := new(VPC)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VPCs/CreateVPC", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vPCsClient) UpdateVPC(ctx context.Context, in *UpdateVPCRequest, opts ...grpc.CallOption) (*VPC, error) {
	out := new(VPC)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VPCs/UpdateVPC", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vPCsClient) UpdateVPCDgw(ctx context.Context, in *UpdateVPCDgwRequest, opts ...grpc.CallOption) (*DGWGateway, error) {
	out := new(DGWGateway)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VPCs/UpdateVPCDgw", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vPCsClient) DeleteVPC(ctx context.Context, in *DeleteVPCRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VPCs/DeleteVPC", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vPCsClient) GetVPCMetrics(ctx context.Context, in *GetVPCMetricsRequest, opts ...grpc.CallOption) (*VPCMetrics, error) {
	out := new(VPCMetrics)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VPCs/GetVPCMetrics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vPCsClient) GetVPCRDMAStatus(ctx context.Context, in *GetVPCRDMARequest, opts ...grpc.CallOption) (*VPCRDMAClustersStatus, error) {
	out := new(VPCRDMAClustersStatus)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VPCs/GetVPCRDMAStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VPCsServer is the server API for VPCs service.
// All implementations must embed UnimplementedVPCsServer
// for forward compatibility
type VPCsServer interface {
	// 列举符合请求的所有 VPCs.
	// [EN] List requested VPCs.
	ListVPCs(context.Context, *ListVPCsRequest) (*ListVPCsResponse, error)
	// 获取符合请求的一个 VPC.
	// [EN] Get a requested VPC.
	GetVPC(context.Context, *GetVPCRequest) (*VPC, error)
	// 获取符合请求的一个 VPC 相关属性.
	// [EN] Get a requested VPC status.
	GetVPCStatus(context.Context, *GetVPCStatusRequest) (*VPCStatus, error)
	// 获取符合请求的一个 VPC 相关属性.
	// [EN] Get a requested VPC status.
	GetVPCDataStatus(context.Context, *GetVPCStatusRequest) (*VPCStatus, error)
	// 创建一个 VPC.
	// [EN] Create a VPC.
	CreateVPC(context.Context, *CreateVPCRequest) (*VPC, error)
	// 更新 VPC 可编辑字段.
	// [EN] Update VPC editable properties.
	UpdateVPC(context.Context, *UpdateVPCRequest) (*VPC, error)
	// 更新 VPCDGW 可编辑字段.
	// [EN] Update VPCDGW editable properties.
	UpdateVPCDgw(context.Context, *UpdateVPCDgwRequest) (*DGWGateway, error)
	// 删除一个 VPC.
	// [EN] Delete a VPC.
	DeleteVPC(context.Context, *DeleteVPCRequest) (*emptypb.Empty, error)
	// VPC metrics.
	// [EN] VPC metrics.
	GetVPCMetrics(context.Context, *GetVPCMetricsRequest) (*VPCMetrics, error)
	// [zh] 获取VPC内RDMA集群信息.
	// [en] get rdma cluster infor in vpc.
	GetVPCRDMAStatus(context.Context, *GetVPCRDMARequest) (*VPCRDMAClustersStatus, error)
	mustEmbedUnimplementedVPCsServer()
}

// UnimplementedVPCsServer must be embedded to have forward compatible implementations.
type UnimplementedVPCsServer struct {
}

func (UnimplementedVPCsServer) ListVPCs(context.Context, *ListVPCsRequest) (*ListVPCsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListVPCs not implemented")
}
func (UnimplementedVPCsServer) GetVPC(context.Context, *GetVPCRequest) (*VPC, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVPC not implemented")
}
func (UnimplementedVPCsServer) GetVPCStatus(context.Context, *GetVPCStatusRequest) (*VPCStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVPCStatus not implemented")
}
func (UnimplementedVPCsServer) GetVPCDataStatus(context.Context, *GetVPCStatusRequest) (*VPCStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVPCDataStatus not implemented")
}
func (UnimplementedVPCsServer) CreateVPC(context.Context, *CreateVPCRequest) (*VPC, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVPC not implemented")
}
func (UnimplementedVPCsServer) UpdateVPC(context.Context, *UpdateVPCRequest) (*VPC, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVPC not implemented")
}
func (UnimplementedVPCsServer) UpdateVPCDgw(context.Context, *UpdateVPCDgwRequest) (*DGWGateway, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVPCDgw not implemented")
}
func (UnimplementedVPCsServer) DeleteVPC(context.Context, *DeleteVPCRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteVPC not implemented")
}
func (UnimplementedVPCsServer) GetVPCMetrics(context.Context, *GetVPCMetricsRequest) (*VPCMetrics, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVPCMetrics not implemented")
}
func (UnimplementedVPCsServer) GetVPCRDMAStatus(context.Context, *GetVPCRDMARequest) (*VPCRDMAClustersStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVPCRDMAStatus not implemented")
}
func (UnimplementedVPCsServer) mustEmbedUnimplementedVPCsServer() {}

// UnsafeVPCsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VPCsServer will
// result in compilation errors.
type UnsafeVPCsServer interface {
	mustEmbedUnimplementedVPCsServer()
}

func RegisterVPCsServer(s grpc.ServiceRegistrar, srv VPCsServer) {
	s.RegisterService(&VPCs_ServiceDesc, srv)
}

func _VPCs_ListVPCs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListVPCsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VPCsServer).ListVPCs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VPCs/ListVPCs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VPCsServer).ListVPCs(ctx, req.(*ListVPCsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VPCs_GetVPC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVPCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VPCsServer).GetVPC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VPCs/GetVPC",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VPCsServer).GetVPC(ctx, req.(*GetVPCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VPCs_GetVPCStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVPCStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VPCsServer).GetVPCStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VPCs/GetVPCStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VPCsServer).GetVPCStatus(ctx, req.(*GetVPCStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VPCs_GetVPCDataStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVPCStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VPCsServer).GetVPCDataStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VPCs/GetVPCDataStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VPCsServer).GetVPCDataStatus(ctx, req.(*GetVPCStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VPCs_CreateVPC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVPCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VPCsServer).CreateVPC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VPCs/CreateVPC",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VPCsServer).CreateVPC(ctx, req.(*CreateVPCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VPCs_UpdateVPC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVPCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VPCsServer).UpdateVPC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VPCs/UpdateVPC",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VPCsServer).UpdateVPC(ctx, req.(*UpdateVPCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VPCs_UpdateVPCDgw_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVPCDgwRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VPCsServer).UpdateVPCDgw(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VPCs/UpdateVPCDgw",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VPCsServer).UpdateVPCDgw(ctx, req.(*UpdateVPCDgwRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VPCs_DeleteVPC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteVPCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VPCsServer).DeleteVPC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VPCs/DeleteVPC",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VPCsServer).DeleteVPC(ctx, req.(*DeleteVPCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VPCs_GetVPCMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVPCMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VPCsServer).GetVPCMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VPCs/GetVPCMetrics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VPCsServer).GetVPCMetrics(ctx, req.(*GetVPCMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VPCs_GetVPCRDMAStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVPCRDMARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VPCsServer).GetVPCRDMAStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VPCs/GetVPCRDMAStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VPCsServer).GetVPCRDMAStatus(ctx, req.(*GetVPCRDMARequest))
	}
	return interceptor(ctx, in, info, handler)
}

// VPCs_ServiceDesc is the grpc.ServiceDesc for VPCs service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VPCs_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.vpc.v1.VPCs",
	HandlerType: (*VPCsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListVPCs",
			Handler:    _VPCs_ListVPCs_Handler,
		},
		{
			MethodName: "GetVPC",
			Handler:    _VPCs_GetVPC_Handler,
		},
		{
			MethodName: "GetVPCStatus",
			Handler:    _VPCs_GetVPCStatus_Handler,
		},
		{
			MethodName: "GetVPCDataStatus",
			Handler:    _VPCs_GetVPCDataStatus_Handler,
		},
		{
			MethodName: "CreateVPC",
			Handler:    _VPCs_CreateVPC_Handler,
		},
		{
			MethodName: "UpdateVPC",
			Handler:    _VPCs_UpdateVPC_Handler,
		},
		{
			MethodName: "UpdateVPCDgw",
			Handler:    _VPCs_UpdateVPCDgw_Handler,
		},
		{
			MethodName: "DeleteVPC",
			Handler:    _VPCs_DeleteVPC_Handler,
		},
		{
			MethodName: "GetVPCMetrics",
			Handler:    _VPCs_GetVPCMetrics_Handler,
		},
		{
			MethodName: "GetVPCRDMAStatus",
			Handler:    _VPCs_GetVPCRDMAStatus_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/vpc/v1/vpc.proto",
}
