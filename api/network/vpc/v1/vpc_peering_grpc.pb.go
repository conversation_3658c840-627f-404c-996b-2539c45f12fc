// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/vpc/v1/vpc_peering.proto

package vpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// VpcPeeringsClient is the client API for VpcPeerings service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VpcPeeringsClient interface {
	// [zh] 创建对等连接资源.
	// [en] Creates VpcPeering resources.
	CreateVpcPeering(ctx context.Context, in *CreateVpcPeeringRequest, opts ...grpc.CallOption) (*VpcPeering, error)
	// [zh] 删除对等连接资源.
	// [en] Delete the VpcPeering resources.
	DeleteVpcPeering(ctx context.Context, in *DeleteVpcPeeringRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// [zh] 更新对等连接资源.
	// [en] Update the VpcPeering resources.
	UpdateVpcPeering(ctx context.Context, in *UpdateVpcPeeringRequest, opts ...grpc.CallOption) (*VpcPeering, error)
	// [zh] 查看对等连接资源详情.
	// [en] Gets details of a VpcPeering resources.
	GetVpcPeering(ctx context.Context, in *GetVpcPeeringRequest, opts ...grpc.CallOption) (*VpcPeering, error)
	// [zh] 查看对等连接资源详情.
	// [en] Gets details of a VpcPeering resources.
	ListVpcPeering(ctx context.Context, in *ListVpcPeeringRequest, opts ...grpc.CallOption) (*ListVpcPeeringResponse, error)
	// [zh] 扩缩容对等连接资源.
	// [en] The Resize of a VpcPeering resources.
	ResizeVpcPeering(ctx context.Context, in *ResizeVpcPeeringRequest, opts ...grpc.CallOption) (*VpcPeering, error)
	// [zh] 释放 (保留期释放).
	// [en] The Release of a VpcPeering resources.
	ReleaseVpcPeering(ctx context.Context, in *ReleaseVpcPeeringRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type vpcPeeringsClient struct {
	cc grpc.ClientConnInterface
}

func NewVpcPeeringsClient(cc grpc.ClientConnInterface) VpcPeeringsClient {
	return &vpcPeeringsClient{cc}
}

func (c *vpcPeeringsClient) CreateVpcPeering(ctx context.Context, in *CreateVpcPeeringRequest, opts ...grpc.CallOption) (*VpcPeering, error) {
	out := new(VpcPeering)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VpcPeerings/CreateVpcPeering", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vpcPeeringsClient) DeleteVpcPeering(ctx context.Context, in *DeleteVpcPeeringRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VpcPeerings/DeleteVpcPeering", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vpcPeeringsClient) UpdateVpcPeering(ctx context.Context, in *UpdateVpcPeeringRequest, opts ...grpc.CallOption) (*VpcPeering, error) {
	out := new(VpcPeering)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VpcPeerings/UpdateVpcPeering", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vpcPeeringsClient) GetVpcPeering(ctx context.Context, in *GetVpcPeeringRequest, opts ...grpc.CallOption) (*VpcPeering, error) {
	out := new(VpcPeering)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VpcPeerings/GetVpcPeering", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vpcPeeringsClient) ListVpcPeering(ctx context.Context, in *ListVpcPeeringRequest, opts ...grpc.CallOption) (*ListVpcPeeringResponse, error) {
	out := new(ListVpcPeeringResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VpcPeerings/ListVpcPeering", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vpcPeeringsClient) ResizeVpcPeering(ctx context.Context, in *ResizeVpcPeeringRequest, opts ...grpc.CallOption) (*VpcPeering, error) {
	out := new(VpcPeering)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VpcPeerings/ResizeVpcPeering", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vpcPeeringsClient) ReleaseVpcPeering(ctx context.Context, in *ReleaseVpcPeeringRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.VpcPeerings/ReleaseVpcPeering", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VpcPeeringsServer is the server API for VpcPeerings service.
// All implementations must embed UnimplementedVpcPeeringsServer
// for forward compatibility
type VpcPeeringsServer interface {
	// [zh] 创建对等连接资源.
	// [en] Creates VpcPeering resources.
	CreateVpcPeering(context.Context, *CreateVpcPeeringRequest) (*VpcPeering, error)
	// [zh] 删除对等连接资源.
	// [en] Delete the VpcPeering resources.
	DeleteVpcPeering(context.Context, *DeleteVpcPeeringRequest) (*emptypb.Empty, error)
	// [zh] 更新对等连接资源.
	// [en] Update the VpcPeering resources.
	UpdateVpcPeering(context.Context, *UpdateVpcPeeringRequest) (*VpcPeering, error)
	// [zh] 查看对等连接资源详情.
	// [en] Gets details of a VpcPeering resources.
	GetVpcPeering(context.Context, *GetVpcPeeringRequest) (*VpcPeering, error)
	// [zh] 查看对等连接资源详情.
	// [en] Gets details of a VpcPeering resources.
	ListVpcPeering(context.Context, *ListVpcPeeringRequest) (*ListVpcPeeringResponse, error)
	// [zh] 扩缩容对等连接资源.
	// [en] The Resize of a VpcPeering resources.
	ResizeVpcPeering(context.Context, *ResizeVpcPeeringRequest) (*VpcPeering, error)
	// [zh] 释放 (保留期释放).
	// [en] The Release of a VpcPeering resources.
	ReleaseVpcPeering(context.Context, *ReleaseVpcPeeringRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedVpcPeeringsServer()
}

// UnimplementedVpcPeeringsServer must be embedded to have forward compatible implementations.
type UnimplementedVpcPeeringsServer struct {
}

func (UnimplementedVpcPeeringsServer) CreateVpcPeering(context.Context, *CreateVpcPeeringRequest) (*VpcPeering, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVpcPeering not implemented")
}
func (UnimplementedVpcPeeringsServer) DeleteVpcPeering(context.Context, *DeleteVpcPeeringRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteVpcPeering not implemented")
}
func (UnimplementedVpcPeeringsServer) UpdateVpcPeering(context.Context, *UpdateVpcPeeringRequest) (*VpcPeering, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVpcPeering not implemented")
}
func (UnimplementedVpcPeeringsServer) GetVpcPeering(context.Context, *GetVpcPeeringRequest) (*VpcPeering, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVpcPeering not implemented")
}
func (UnimplementedVpcPeeringsServer) ListVpcPeering(context.Context, *ListVpcPeeringRequest) (*ListVpcPeeringResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListVpcPeering not implemented")
}
func (UnimplementedVpcPeeringsServer) ResizeVpcPeering(context.Context, *ResizeVpcPeeringRequest) (*VpcPeering, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResizeVpcPeering not implemented")
}
func (UnimplementedVpcPeeringsServer) ReleaseVpcPeering(context.Context, *ReleaseVpcPeeringRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseVpcPeering not implemented")
}
func (UnimplementedVpcPeeringsServer) mustEmbedUnimplementedVpcPeeringsServer() {}

// UnsafeVpcPeeringsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VpcPeeringsServer will
// result in compilation errors.
type UnsafeVpcPeeringsServer interface {
	mustEmbedUnimplementedVpcPeeringsServer()
}

func RegisterVpcPeeringsServer(s grpc.ServiceRegistrar, srv VpcPeeringsServer) {
	s.RegisterService(&VpcPeerings_ServiceDesc, srv)
}

func _VpcPeerings_CreateVpcPeering_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVpcPeeringRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VpcPeeringsServer).CreateVpcPeering(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VpcPeerings/CreateVpcPeering",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VpcPeeringsServer).CreateVpcPeering(ctx, req.(*CreateVpcPeeringRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VpcPeerings_DeleteVpcPeering_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteVpcPeeringRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VpcPeeringsServer).DeleteVpcPeering(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VpcPeerings/DeleteVpcPeering",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VpcPeeringsServer).DeleteVpcPeering(ctx, req.(*DeleteVpcPeeringRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VpcPeerings_UpdateVpcPeering_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVpcPeeringRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VpcPeeringsServer).UpdateVpcPeering(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VpcPeerings/UpdateVpcPeering",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VpcPeeringsServer).UpdateVpcPeering(ctx, req.(*UpdateVpcPeeringRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VpcPeerings_GetVpcPeering_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVpcPeeringRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VpcPeeringsServer).GetVpcPeering(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VpcPeerings/GetVpcPeering",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VpcPeeringsServer).GetVpcPeering(ctx, req.(*GetVpcPeeringRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VpcPeerings_ListVpcPeering_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListVpcPeeringRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VpcPeeringsServer).ListVpcPeering(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VpcPeerings/ListVpcPeering",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VpcPeeringsServer).ListVpcPeering(ctx, req.(*ListVpcPeeringRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VpcPeerings_ResizeVpcPeering_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResizeVpcPeeringRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VpcPeeringsServer).ResizeVpcPeering(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VpcPeerings/ResizeVpcPeering",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VpcPeeringsServer).ResizeVpcPeering(ctx, req.(*ResizeVpcPeeringRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VpcPeerings_ReleaseVpcPeering_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseVpcPeeringRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VpcPeeringsServer).ReleaseVpcPeering(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.VpcPeerings/ReleaseVpcPeering",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VpcPeeringsServer).ReleaseVpcPeering(ctx, req.(*ReleaseVpcPeeringRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// VpcPeerings_ServiceDesc is the grpc.ServiceDesc for VpcPeerings service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VpcPeerings_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.vpc.v1.VpcPeerings",
	HandlerType: (*VpcPeeringsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateVpcPeering",
			Handler:    _VpcPeerings_CreateVpcPeering_Handler,
		},
		{
			MethodName: "DeleteVpcPeering",
			Handler:    _VpcPeerings_DeleteVpcPeering_Handler,
		},
		{
			MethodName: "UpdateVpcPeering",
			Handler:    _VpcPeerings_UpdateVpcPeering_Handler,
		},
		{
			MethodName: "GetVpcPeering",
			Handler:    _VpcPeerings_GetVpcPeering_Handler,
		},
		{
			MethodName: "ListVpcPeering",
			Handler:    _VpcPeerings_ListVpcPeering_Handler,
		},
		{
			MethodName: "ResizeVpcPeering",
			Handler:    _VpcPeerings_ResizeVpcPeering_Handler,
		},
		{
			MethodName: "ReleaseVpcPeering",
			Handler:    _VpcPeerings_ReleaseVpcPeering_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/vpc/v1/vpc_peering.proto",
}
