// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/vpc/v1/acl.proto

package vpc

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListACLsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListACLsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListACLsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListACLsRequestMultiError, or nil if none found.
func (m *ListACLsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListACLsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	// no validation rules for VpcName

	if len(errors) > 0 {
		return ListACLsRequestMultiError(errors)
	}

	return nil
}

// ListACLsRequestMultiError is an error wrapping multiple validation errors
// returned by ListACLsRequest.ValidateAll() if the designated constraints
// aren't met.
type ListACLsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListACLsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListACLsRequestMultiError) AllErrors() []error { return m }

// ListACLsRequestValidationError is the validation error returned by
// ListACLsRequest.Validate if the designated constraints aren't met.
type ListACLsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListACLsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListACLsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListACLsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListACLsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListACLsRequestValidationError) ErrorName() string { return "ListACLsRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListACLsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListACLsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListACLsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListACLsRequestValidationError{}

// Validate checks the field values on GetACLRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetACLRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetACLRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetACLRequestMultiError, or
// nil if none found.
func (m *GetACLRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetACLRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for AclName

	// no validation rules for VpcName

	if len(errors) > 0 {
		return GetACLRequestMultiError(errors)
	}

	return nil
}

// GetACLRequestMultiError is an error wrapping multiple validation errors
// returned by GetACLRequest.ValidateAll() if the designated constraints
// aren't met.
type GetACLRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetACLRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetACLRequestMultiError) AllErrors() []error { return m }

// GetACLRequestValidationError is the validation error returned by
// GetACLRequest.Validate if the designated constraints aren't met.
type GetACLRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetACLRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetACLRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetACLRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetACLRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetACLRequestValidationError) ErrorName() string { return "GetACLRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetACLRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetACLRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetACLRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetACLRequestValidationError{}

// Validate checks the field values on CreateACLRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateACLRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateACLRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateACLRequestMultiError, or nil if none found.
func (m *CreateACLRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateACLRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetAclName()) > 63 {
		err := CreateACLRequestValidationError{
			field:  "AclName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateACLRequest_AclName_Pattern.MatchString(m.GetAclName()) {
		err := CreateACLRequestValidationError{
			field:  "AclName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAcl()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateACLRequestValidationError{
					field:  "Acl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateACLRequestValidationError{
					field:  "Acl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAcl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateACLRequestValidationError{
				field:  "Acl",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VpcName

	if len(errors) > 0 {
		return CreateACLRequestMultiError(errors)
	}

	return nil
}

// CreateACLRequestMultiError is an error wrapping multiple validation errors
// returned by CreateACLRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateACLRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateACLRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateACLRequestMultiError) AllErrors() []error { return m }

// CreateACLRequestValidationError is the validation error returned by
// CreateACLRequest.Validate if the designated constraints aren't met.
type CreateACLRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateACLRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateACLRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateACLRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateACLRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateACLRequestValidationError) ErrorName() string { return "CreateACLRequestValidationError" }

// Error satisfies the builtin error interface
func (e CreateACLRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateACLRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateACLRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateACLRequestValidationError{}

var _CreateACLRequest_AclName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on UpdateACLRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateACLRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateACLRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateACLRequestMultiError, or nil if none found.
func (m *UpdateACLRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateACLRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetAclName()) > 63 {
		err := UpdateACLRequestValidationError{
			field:  "AclName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UpdateACLRequest_AclName_Pattern.MatchString(m.GetAclName()) {
		err := UpdateACLRequestValidationError{
			field:  "AclName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetAcl()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateACLRequestValidationError{
					field:  "Acl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateACLRequestValidationError{
					field:  "Acl",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAcl()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateACLRequestValidationError{
				field:  "Acl",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VpcName

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateACLRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateACLRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateACLRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateACLRequestMultiError(errors)
	}

	return nil
}

// UpdateACLRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateACLRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateACLRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateACLRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateACLRequestMultiError) AllErrors() []error { return m }

// UpdateACLRequestValidationError is the validation error returned by
// UpdateACLRequest.Validate if the designated constraints aren't met.
type UpdateACLRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateACLRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateACLRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateACLRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateACLRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateACLRequestValidationError) ErrorName() string { return "UpdateACLRequestValidationError" }

// Error satisfies the builtin error interface
func (e UpdateACLRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateACLRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateACLRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateACLRequestValidationError{}

var _UpdateACLRequest_AclName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on DeleteACLRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteACLRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteACLRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteACLRequestMultiError, or nil if none found.
func (m *DeleteACLRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteACLRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetAclName()) > 63 {
		err := DeleteACLRequestValidationError{
			field:  "AclName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_DeleteACLRequest_AclName_Pattern.MatchString(m.GetAclName()) {
		err := DeleteACLRequestValidationError{
			field:  "AclName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for VpcName

	if len(errors) > 0 {
		return DeleteACLRequestMultiError(errors)
	}

	return nil
}

// DeleteACLRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteACLRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteACLRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteACLRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteACLRequestMultiError) AllErrors() []error { return m }

// DeleteACLRequestValidationError is the validation error returned by
// DeleteACLRequest.Validate if the designated constraints aren't met.
type DeleteACLRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteACLRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteACLRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteACLRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteACLRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteACLRequestValidationError) ErrorName() string { return "DeleteACLRequestValidationError" }

// Error satisfies the builtin error interface
func (e DeleteACLRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteACLRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteACLRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteACLRequestValidationError{}

var _DeleteACLRequest_AclName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on ListACLsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListACLsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListACLsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListACLsResponseMultiError, or nil if none found.
func (m *ListACLsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListACLsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetAcls() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListACLsResponseValidationError{
						field:  fmt.Sprintf("Acls[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListACLsResponseValidationError{
						field:  fmt.Sprintf("Acls[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListACLsResponseValidationError{
					field:  fmt.Sprintf("Acls[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListACLsResponseMultiError(errors)
	}

	return nil
}

// ListACLsResponseMultiError is an error wrapping multiple validation errors
// returned by ListACLsResponse.ValidateAll() if the designated constraints
// aren't met.
type ListACLsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListACLsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListACLsResponseMultiError) AllErrors() []error { return m }

// ListACLsResponseValidationError is the validation error returned by
// ListACLsResponse.Validate if the designated constraints aren't met.
type ListACLsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListACLsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListACLsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListACLsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListACLsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListACLsResponseValidationError) ErrorName() string { return "ListACLsResponseValidationError" }

// Error satisfies the builtin error interface
func (e ListACLsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListACLsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListACLsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListACLsResponseValidationError{}

// Validate checks the field values on ACL with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *ACL) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ACL with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in ACLMultiError, or nil if none found.
func (m *ACL) ValidateAll() error {
	return m.validate(true)
}

func (m *ACL) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
		err := ACLValidationError{
			field:  "DisplayName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ACL_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
		err := ACLValidationError{
			field:  "DisplayName",
			reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for Uid

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ACLValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ACLValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ACLValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ACLValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ACLValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ACLValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ACLValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ACLValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ACLValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ACLValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ACLValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ACLValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ACLMultiError(errors)
	}

	return nil
}

// ACLMultiError is an error wrapping multiple validation errors returned by
// ACL.ValidateAll() if the designated constraints aren't met.
type ACLMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ACLMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ACLMultiError) AllErrors() []error { return m }

// ACLValidationError is the validation error returned by ACL.Validate if the
// designated constraints aren't met.
type ACLValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ACLValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ACLValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ACLValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ACLValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ACLValidationError) ErrorName() string { return "ACLValidationError" }

// Error satisfies the builtin error interface
func (e ACLValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sACL.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ACLValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ACLValidationError{}

var _ACL_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on ACLUpdateProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ACLUpdateProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ACLUpdateProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ACLUpdatePropertiesMultiError, or nil if none found.
func (m *ACLUpdateProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *ACLUpdateProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Description

	if m.DisplayName != nil {

		if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
			err := ACLUpdatePropertiesValidationError{
				field:  "DisplayName",
				reason: "value length must be at most 63 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_ACLUpdateProperties_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
			err := ACLUpdatePropertiesValidationError{
				field:  "DisplayName",
				reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return ACLUpdatePropertiesMultiError(errors)
	}

	return nil
}

// ACLUpdatePropertiesMultiError is an error wrapping multiple validation
// errors returned by ACLUpdateProperties.ValidateAll() if the designated
// constraints aren't met.
type ACLUpdatePropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ACLUpdatePropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ACLUpdatePropertiesMultiError) AllErrors() []error { return m }

// ACLUpdatePropertiesValidationError is the validation error returned by
// ACLUpdateProperties.Validate if the designated constraints aren't met.
type ACLUpdatePropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ACLUpdatePropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ACLUpdatePropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ACLUpdatePropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ACLUpdatePropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ACLUpdatePropertiesValidationError) ErrorName() string {
	return "ACLUpdatePropertiesValidationError"
}

// Error satisfies the builtin error interface
func (e ACLUpdatePropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sACLUpdateProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ACLUpdatePropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ACLUpdatePropertiesValidationError{}

var _ACLUpdateProperties_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on ACLProperties with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ACLProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ACLProperties with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ACLPropertiesMultiError, or
// nil if none found.
func (m *ACLProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *ACLProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if _, ok := ACLProperties_ACLAction_name[int32(m.GetAction())]; !ok {
		err := ACLPropertiesValidationError{
			field:  "Action",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Src

	// no validation rules for SrcPort

	// no validation rules for Dest

	// no validation rules for DestPort

	// no validation rules for Protocol

	if val := m.GetPriority(); val <= 0 || val >= 400 {
		err := ACLPropertiesValidationError{
			field:  "Priority",
			reason: "value must be inside range (0, 400)",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if _, ok := ACLProperties_ACLType_name[int32(m.GetType())]; !ok {
		err := ACLPropertiesValidationError{
			field:  "Type",
			reason: "value must be one of the defined enum values",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for ExtraData

	if len(errors) > 0 {
		return ACLPropertiesMultiError(errors)
	}

	return nil
}

// ACLPropertiesMultiError is an error wrapping multiple validation errors
// returned by ACLProperties.ValidateAll() if the designated constraints
// aren't met.
type ACLPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ACLPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ACLPropertiesMultiError) AllErrors() []error { return m }

// ACLPropertiesValidationError is the validation error returned by
// ACLProperties.Validate if the designated constraints aren't met.
type ACLPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ACLPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ACLPropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ACLPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ACLPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ACLPropertiesValidationError) ErrorName() string { return "ACLPropertiesValidationError" }

// Error satisfies the builtin error interface
func (e ACLPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sACLProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ACLPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ACLPropertiesValidationError{}
