// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/vpc/v1/vpc.proto

package vpc

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListVPCsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListVPCsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListVPCsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListVPCsRequestMultiError, or nil if none found.
func (m *ListVPCsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListVPCsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	// no validation rules for TenantId

	if len(errors) > 0 {
		return ListVPCsRequestMultiError(errors)
	}

	return nil
}

// ListVPCsRequestMultiError is an error wrapping multiple validation errors
// returned by ListVPCsRequest.ValidateAll() if the designated constraints
// aren't met.
type ListVPCsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListVPCsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListVPCsRequestMultiError) AllErrors() []error { return m }

// ListVPCsRequestValidationError is the validation error returned by
// ListVPCsRequest.Validate if the designated constraints aren't met.
type ListVPCsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListVPCsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListVPCsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListVPCsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListVPCsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListVPCsRequestValidationError) ErrorName() string { return "ListVPCsRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListVPCsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListVPCsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListVPCsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListVPCsRequestValidationError{}

// Validate checks the field values on GetVPCRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetVPCRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVPCRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetVPCRequestMultiError, or
// nil if none found.
func (m *GetVPCRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVPCRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for VpcName

	if len(errors) > 0 {
		return GetVPCRequestMultiError(errors)
	}

	return nil
}

// GetVPCRequestMultiError is an error wrapping multiple validation errors
// returned by GetVPCRequest.ValidateAll() if the designated constraints
// aren't met.
type GetVPCRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVPCRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVPCRequestMultiError) AllErrors() []error { return m }

// GetVPCRequestValidationError is the validation error returned by
// GetVPCRequest.Validate if the designated constraints aren't met.
type GetVPCRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVPCRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVPCRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVPCRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVPCRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVPCRequestValidationError) ErrorName() string { return "GetVPCRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetVPCRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVPCRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVPCRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVPCRequestValidationError{}

// Validate checks the field values on GetVPCStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetVPCStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVPCStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVPCStatusRequestMultiError, or nil if none found.
func (m *GetVPCStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVPCStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for VpcName

	if len(errors) > 0 {
		return GetVPCStatusRequestMultiError(errors)
	}

	return nil
}

// GetVPCStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetVPCStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetVPCStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVPCStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVPCStatusRequestMultiError) AllErrors() []error { return m }

// GetVPCStatusRequestValidationError is the validation error returned by
// GetVPCStatusRequest.Validate if the designated constraints aren't met.
type GetVPCStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVPCStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVPCStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVPCStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVPCStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVPCStatusRequestValidationError) ErrorName() string {
	return "GetVPCStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetVPCStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVPCStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVPCStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVPCStatusRequestValidationError{}

// Validate checks the field values on CreateVPCRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateVPCRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateVPCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateVPCRequestMultiError, or nil if none found.
func (m *CreateVPCRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVPCRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetVpcName()) > 63 {
		err := CreateVPCRequestValidationError{
			field:  "VpcName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateVPCRequest_VpcName_Pattern.MatchString(m.GetVpcName()) {
		err := CreateVPCRequestValidationError{
			field:  "VpcName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetVpc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVPCRequestValidationError{
					field:  "Vpc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVPCRequestValidationError{
					field:  "Vpc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVpc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVPCRequestValidationError{
				field:  "Vpc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateVPCRequestMultiError(errors)
	}

	return nil
}

// CreateVPCRequestMultiError is an error wrapping multiple validation errors
// returned by CreateVPCRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateVPCRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVPCRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVPCRequestMultiError) AllErrors() []error { return m }

// CreateVPCRequestValidationError is the validation error returned by
// CreateVPCRequest.Validate if the designated constraints aren't met.
type CreateVPCRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVPCRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateVPCRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateVPCRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateVPCRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateVPCRequestValidationError) ErrorName() string { return "CreateVPCRequestValidationError" }

// Error satisfies the builtin error interface
func (e CreateVPCRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVPCRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVPCRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVPCRequestValidationError{}

var _CreateVPCRequest_VpcName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on UpdateVPCRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateVPCRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateVPCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateVPCRequestMultiError, or nil if none found.
func (m *UpdateVPCRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateVPCRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for VpcName

	if all {
		switch v := interface{}(m.GetVpc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateVPCRequestValidationError{
					field:  "Vpc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateVPCRequestValidationError{
					field:  "Vpc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVpc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateVPCRequestValidationError{
				field:  "Vpc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateVPCRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateVPCRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateVPCRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateVPCRequestMultiError(errors)
	}

	return nil
}

// UpdateVPCRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateVPCRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateVPCRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateVPCRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateVPCRequestMultiError) AllErrors() []error { return m }

// UpdateVPCRequestValidationError is the validation error returned by
// UpdateVPCRequest.Validate if the designated constraints aren't met.
type UpdateVPCRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateVPCRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateVPCRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateVPCRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateVPCRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateVPCRequestValidationError) ErrorName() string { return "UpdateVPCRequestValidationError" }

// Error satisfies the builtin error interface
func (e UpdateVPCRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateVPCRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateVPCRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateVPCRequestValidationError{}

// Validate checks the field values on VPCUpdateProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VPCUpdateProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VPCUpdateProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VPCUpdatePropertiesMultiError, or nil if none found.
func (m *VPCUpdateProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *VPCUpdateProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Description

	// no validation rules for SubnetType

	if m.DisplayName != nil {

		if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
			err := VPCUpdatePropertiesValidationError{
				field:  "DisplayName",
				reason: "value length must be at most 63 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_VPCUpdateProperties_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
			err := VPCUpdatePropertiesValidationError{
				field:  "DisplayName",
				reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return VPCUpdatePropertiesMultiError(errors)
	}

	return nil
}

// VPCUpdatePropertiesMultiError is an error wrapping multiple validation
// errors returned by VPCUpdateProperties.ValidateAll() if the designated
// constraints aren't met.
type VPCUpdatePropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VPCUpdatePropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VPCUpdatePropertiesMultiError) AllErrors() []error { return m }

// VPCUpdatePropertiesValidationError is the validation error returned by
// VPCUpdateProperties.Validate if the designated constraints aren't met.
type VPCUpdatePropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VPCUpdatePropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VPCUpdatePropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VPCUpdatePropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VPCUpdatePropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VPCUpdatePropertiesValidationError) ErrorName() string {
	return "VPCUpdatePropertiesValidationError"
}

// Error satisfies the builtin error interface
func (e VPCUpdatePropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVPCUpdateProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VPCUpdatePropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VPCUpdatePropertiesValidationError{}

var _VPCUpdateProperties_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on UpdateVPCDgwRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateVPCDgwRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateVPCDgwRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateVPCDgwRequestMultiError, or nil if none found.
func (m *UpdateVPCDgwRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateVPCDgwRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for VpcName

	// no validation rules for DgwName

	if all {
		switch v := interface{}(m.GetDgw()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateVPCDgwRequestValidationError{
					field:  "Dgw",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateVPCDgwRequestValidationError{
					field:  "Dgw",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDgw()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateVPCDgwRequestValidationError{
				field:  "Dgw",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateVPCDgwRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateVPCDgwRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateVPCDgwRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateVPCDgwRequestMultiError(errors)
	}

	return nil
}

// UpdateVPCDgwRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateVPCDgwRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateVPCDgwRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateVPCDgwRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateVPCDgwRequestMultiError) AllErrors() []error { return m }

// UpdateVPCDgwRequestValidationError is the validation error returned by
// UpdateVPCDgwRequest.Validate if the designated constraints aren't met.
type UpdateVPCDgwRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateVPCDgwRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateVPCDgwRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateVPCDgwRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateVPCDgwRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateVPCDgwRequestValidationError) ErrorName() string {
	return "UpdateVPCDgwRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateVPCDgwRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateVPCDgwRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateVPCDgwRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateVPCDgwRequestValidationError{}

// Validate checks the field values on DGWGateway with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DGWGateway) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DGWGateway with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DGWGatewayMultiError, or
// nil if none found.
func (m *DGWGateway) ValidateAll() error {
	return m.validate(true)
}

func (m *DGWGateway) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	if utf8.RuneCountInString(m.GetName()) > 63 {
		err := DGWGatewayValidationError{
			field:  "Name",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_DGWGateway_Name_Pattern.MatchString(m.GetName()) {
		err := DGWGatewayValidationError{
			field:  "Name",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if m.GetDisplayName() != "" {

		if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
			err := DGWGatewayValidationError{
				field:  "DisplayName",
				reason: "value length must be at most 63 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_DGWGateway_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
			err := DGWGatewayValidationError{
				field:  "DisplayName",
				reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Description

	// no validation rules for Uid

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for AdminState

	// no validation rules for GwExternalIp

	// no validation rules for GwPolicy

	// no validation rules for InternalIp

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DGWGatewayValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DGWGatewayValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DGWGatewayValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DGWGatewayValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DGWGatewayValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DGWGatewayValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DGWGatewayMultiError(errors)
	}

	return nil
}

// DGWGatewayMultiError is an error wrapping multiple validation errors
// returned by DGWGateway.ValidateAll() if the designated constraints aren't met.
type DGWGatewayMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DGWGatewayMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DGWGatewayMultiError) AllErrors() []error { return m }

// DGWGatewayValidationError is the validation error returned by
// DGWGateway.Validate if the designated constraints aren't met.
type DGWGatewayValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DGWGatewayValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DGWGatewayValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DGWGatewayValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DGWGatewayValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DGWGatewayValidationError) ErrorName() string { return "DGWGatewayValidationError" }

// Error satisfies the builtin error interface
func (e DGWGatewayValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDGWGateway.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DGWGatewayValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DGWGatewayValidationError{}

var _DGWGateway_Name_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _DGWGateway_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on DeleteVPCRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteVPCRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteVPCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteVPCRequestMultiError, or nil if none found.
func (m *DeleteVPCRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteVPCRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for VpcName

	if len(errors) > 0 {
		return DeleteVPCRequestMultiError(errors)
	}

	return nil
}

// DeleteVPCRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteVPCRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteVPCRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteVPCRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteVPCRequestMultiError) AllErrors() []error { return m }

// DeleteVPCRequestValidationError is the validation error returned by
// DeleteVPCRequest.Validate if the designated constraints aren't met.
type DeleteVPCRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteVPCRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteVPCRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteVPCRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteVPCRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteVPCRequestValidationError) ErrorName() string { return "DeleteVPCRequestValidationError" }

// Error satisfies the builtin error interface
func (e DeleteVPCRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteVPCRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteVPCRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteVPCRequestValidationError{}

// Validate checks the field values on ListVPCsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListVPCsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListVPCsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListVPCsResponseMultiError, or nil if none found.
func (m *ListVPCsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListVPCsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetVpcs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListVPCsResponseValidationError{
						field:  fmt.Sprintf("Vpcs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListVPCsResponseValidationError{
						field:  fmt.Sprintf("Vpcs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListVPCsResponseValidationError{
					field:  fmt.Sprintf("Vpcs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListVPCsResponseMultiError(errors)
	}

	return nil
}

// ListVPCsResponseMultiError is an error wrapping multiple validation errors
// returned by ListVPCsResponse.ValidateAll() if the designated constraints
// aren't met.
type ListVPCsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListVPCsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListVPCsResponseMultiError) AllErrors() []error { return m }

// ListVPCsResponseValidationError is the validation error returned by
// ListVPCsResponse.Validate if the designated constraints aren't met.
type ListVPCsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListVPCsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListVPCsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListVPCsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListVPCsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListVPCsResponseValidationError) ErrorName() string { return "ListVPCsResponseValidationError" }

// Error satisfies the builtin error interface
func (e ListVPCsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListVPCsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListVPCsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListVPCsResponseValidationError{}

// Validate checks the field values on VPCStatus with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VPCStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VPCStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VPCStatusMultiError, or nil
// if none found.
func (m *VPCStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *VPCStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Cidr

	for idx, item := range m.GetNamespaces() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VPCStatusValidationError{
						field:  fmt.Sprintf("Namespaces[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VPCStatusValidationError{
						field:  fmt.Sprintf("Namespaces[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VPCStatusValidationError{
					field:  fmt.Sprintf("Namespaces[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetSubnets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VPCStatusValidationError{
						field:  fmt.Sprintf("Subnets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VPCStatusValidationError{
						field:  fmt.Sprintf("Subnets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VPCStatusValidationError{
					field:  fmt.Sprintf("Subnets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetNatGateways() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VPCStatusValidationError{
						field:  fmt.Sprintf("NatGateways[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VPCStatusValidationError{
						field:  fmt.Sprintf("NatGateways[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VPCStatusValidationError{
					field:  fmt.Sprintf("NatGateways[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetEips() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VPCStatusValidationError{
						field:  fmt.Sprintf("Eips[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VPCStatusValidationError{
						field:  fmt.Sprintf("Eips[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VPCStatusValidationError{
					field:  fmt.Sprintf("Eips[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	for idx, item := range m.GetDgwGateways() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VPCStatusValidationError{
						field:  fmt.Sprintf("DgwGateways[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VPCStatusValidationError{
						field:  fmt.Sprintf("DgwGateways[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VPCStatusValidationError{
					field:  fmt.Sprintf("DgwGateways[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return VPCStatusMultiError(errors)
	}

	return nil
}

// VPCStatusMultiError is an error wrapping multiple validation errors returned
// by VPCStatus.ValidateAll() if the designated constraints aren't met.
type VPCStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VPCStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VPCStatusMultiError) AllErrors() []error { return m }

// VPCStatusValidationError is the validation error returned by
// VPCStatus.Validate if the designated constraints aren't met.
type VPCStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VPCStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VPCStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VPCStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VPCStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VPCStatusValidationError) ErrorName() string { return "VPCStatusValidationError" }

// Error satisfies the builtin error interface
func (e VPCStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVPCStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VPCStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VPCStatusValidationError{}

// Validate checks the field values on VPCMetrics with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VPCMetrics) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VPCMetrics with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VPCMetricsMultiError, or
// nil if none found.
func (m *VPCMetrics) ValidateAll() error {
	return m.validate(true)
}

func (m *VPCMetrics) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetResourceMetrics() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VPCMetricsValidationError{
						field:  fmt.Sprintf("ResourceMetrics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VPCMetricsValidationError{
						field:  fmt.Sprintf("ResourceMetrics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VPCMetricsValidationError{
					field:  fmt.Sprintf("ResourceMetrics[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return VPCMetricsMultiError(errors)
	}

	return nil
}

// VPCMetricsMultiError is an error wrapping multiple validation errors
// returned by VPCMetrics.ValidateAll() if the designated constraints aren't met.
type VPCMetricsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VPCMetricsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VPCMetricsMultiError) AllErrors() []error { return m }

// VPCMetricsValidationError is the validation error returned by
// VPCMetrics.Validate if the designated constraints aren't met.
type VPCMetricsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VPCMetricsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VPCMetricsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VPCMetricsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VPCMetricsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VPCMetricsValidationError) ErrorName() string { return "VPCMetricsValidationError" }

// Error satisfies the builtin error interface
func (e VPCMetricsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVPCMetrics.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VPCMetricsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VPCMetricsValidationError{}

// Validate checks the field values on ResourceMetrics with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ResourceMetrics) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResourceMetrics with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResourceMetricsMultiError, or nil if none found.
func (m *ResourceMetrics) ValidateAll() error {
	return m.validate(true)
}

func (m *ResourceMetrics) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Value

	// no validation rules for Unit

	// no validation rules for Color

	if len(errors) > 0 {
		return ResourceMetricsMultiError(errors)
	}

	return nil
}

// ResourceMetricsMultiError is an error wrapping multiple validation errors
// returned by ResourceMetrics.ValidateAll() if the designated constraints
// aren't met.
type ResourceMetricsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourceMetricsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourceMetricsMultiError) AllErrors() []error { return m }

// ResourceMetricsValidationError is the validation error returned by
// ResourceMetrics.Validate if the designated constraints aren't met.
type ResourceMetricsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourceMetricsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourceMetricsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourceMetricsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourceMetricsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourceMetricsValidationError) ErrorName() string { return "ResourceMetricsValidationError" }

// Error satisfies the builtin error interface
func (e ResourceMetricsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResourceMetrics.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourceMetricsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourceMetricsValidationError{}

// Validate checks the field values on NamespaceStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NamespaceStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NamespaceStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NamespaceStatusMultiError, or nil if none found.
func (m *NamespaceStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *NamespaceStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Subnet

	if len(errors) > 0 {
		return NamespaceStatusMultiError(errors)
	}

	return nil
}

// NamespaceStatusMultiError is an error wrapping multiple validation errors
// returned by NamespaceStatus.ValidateAll() if the designated constraints
// aren't met.
type NamespaceStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NamespaceStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NamespaceStatusMultiError) AllErrors() []error { return m }

// NamespaceStatusValidationError is the validation error returned by
// NamespaceStatus.Validate if the designated constraints aren't met.
type NamespaceStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NamespaceStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NamespaceStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NamespaceStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NamespaceStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NamespaceStatusValidationError) ErrorName() string { return "NamespaceStatusValidationError" }

// Error satisfies the builtin error interface
func (e NamespaceStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNamespaceStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NamespaceStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NamespaceStatusValidationError{}

// Validate checks the field values on SubnetStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SubnetStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SubnetStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SubnetStatusMultiError, or
// nil if none found.
func (m *SubnetStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *SubnetStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Cidr

	// no validation rules for Id

	// no validation rules for Scope

	// no validation rules for Provider

	// no validation rules for NetworkType

	if len(errors) > 0 {
		return SubnetStatusMultiError(errors)
	}

	return nil
}

// SubnetStatusMultiError is an error wrapping multiple validation errors
// returned by SubnetStatus.ValidateAll() if the designated constraints aren't met.
type SubnetStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SubnetStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SubnetStatusMultiError) AllErrors() []error { return m }

// SubnetStatusValidationError is the validation error returned by
// SubnetStatus.Validate if the designated constraints aren't met.
type SubnetStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SubnetStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SubnetStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SubnetStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SubnetStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SubnetStatusValidationError) ErrorName() string { return "SubnetStatusValidationError" }

// Error satisfies the builtin error interface
func (e SubnetStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSubnetStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SubnetStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SubnetStatusValidationError{}

// Validate checks the field values on DGWGatewayStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DGWGatewayStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DGWGatewayStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DGWGatewayStatusMultiError, or nil if none found.
func (m *DGWGatewayStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *DGWGatewayStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for AdminState

	// no validation rules for Ip

	// no validation rules for Policy

	// no validation rules for Id

	// no validation rules for State

	if len(errors) > 0 {
		return DGWGatewayStatusMultiError(errors)
	}

	return nil
}

// DGWGatewayStatusMultiError is an error wrapping multiple validation errors
// returned by DGWGatewayStatus.ValidateAll() if the designated constraints
// aren't met.
type DGWGatewayStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DGWGatewayStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DGWGatewayStatusMultiError) AllErrors() []error { return m }

// DGWGatewayStatusValidationError is the validation error returned by
// DGWGatewayStatus.Validate if the designated constraints aren't met.
type DGWGatewayStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DGWGatewayStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DGWGatewayStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DGWGatewayStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DGWGatewayStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DGWGatewayStatusValidationError) ErrorName() string { return "DGWGatewayStatusValidationError" }

// Error satisfies the builtin error interface
func (e DGWGatewayStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDGWGatewayStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DGWGatewayStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DGWGatewayStatusValidationError{}

// Validate checks the field values on NATGatewayStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *NATGatewayStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NATGatewayStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NATGatewayStatusMultiError, or nil if none found.
func (m *NATGatewayStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *NATGatewayStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Ip

	// no validation rules for Id

	if len(errors) > 0 {
		return NATGatewayStatusMultiError(errors)
	}

	return nil
}

// NATGatewayStatusMultiError is an error wrapping multiple validation errors
// returned by NATGatewayStatus.ValidateAll() if the designated constraints
// aren't met.
type NATGatewayStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NATGatewayStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NATGatewayStatusMultiError) AllErrors() []error { return m }

// NATGatewayStatusValidationError is the validation error returned by
// NATGatewayStatus.Validate if the designated constraints aren't met.
type NATGatewayStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NATGatewayStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NATGatewayStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NATGatewayStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NATGatewayStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NATGatewayStatusValidationError) ErrorName() string { return "NATGatewayStatusValidationError" }

// Error satisfies the builtin error interface
func (e NATGatewayStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNATGatewayStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NATGatewayStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NATGatewayStatusValidationError{}

// Validate checks the field values on EIPStatus with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EIPStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EIPStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EIPStatusMultiError, or nil
// if none found.
func (m *EIPStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *EIPStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Ip

	// no validation rules for Id

	// no validation rules for DefaultSnat

	// no validation rules for AssociationType

	// no validation rules for AssociationId

	if len(errors) > 0 {
		return EIPStatusMultiError(errors)
	}

	return nil
}

// EIPStatusMultiError is an error wrapping multiple validation errors returned
// by EIPStatus.ValidateAll() if the designated constraints aren't met.
type EIPStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EIPStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EIPStatusMultiError) AllErrors() []error { return m }

// EIPStatusValidationError is the validation error returned by
// EIPStatus.Validate if the designated constraints aren't met.
type EIPStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EIPStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EIPStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EIPStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EIPStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EIPStatusValidationError) ErrorName() string { return "EIPStatusValidationError" }

// Error satisfies the builtin error interface
func (e EIPStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEIPStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EIPStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EIPStatusValidationError{}

// Validate checks the field values on GetVPCMetricsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetVPCMetricsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVPCMetricsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVPCMetricsRequestMultiError, or nil if none found.
func (m *GetVPCMetricsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVPCMetricsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetVPCMetricsRequestMultiError(errors)
	}

	return nil
}

// GetVPCMetricsRequestMultiError is an error wrapping multiple validation
// errors returned by GetVPCMetricsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetVPCMetricsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVPCMetricsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVPCMetricsRequestMultiError) AllErrors() []error { return m }

// GetVPCMetricsRequestValidationError is the validation error returned by
// GetVPCMetricsRequest.Validate if the designated constraints aren't met.
type GetVPCMetricsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVPCMetricsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVPCMetricsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVPCMetricsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVPCMetricsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVPCMetricsRequestValidationError) ErrorName() string {
	return "GetVPCMetricsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetVPCMetricsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVPCMetricsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVPCMetricsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVPCMetricsRequestValidationError{}

// Validate checks the field values on VPC with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *VPC) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VPC with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in VPCMultiError, or nil if none found.
func (m *VPC) ValidateAll() error {
	return m.validate(true)
}

func (m *VPC) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
		err := VPCValidationError{
			field:  "DisplayName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_VPC_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
		err := VPCValidationError{
			field:  "DisplayName",
			reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for Uid

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VPCValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VPCValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VPCValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VPCValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VPCValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VPCValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VPCValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VPCValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VPCValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VPCValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VPCValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VPCValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VPCMultiError(errors)
	}

	return nil
}

// VPCMultiError is an error wrapping multiple validation errors returned by
// VPC.ValidateAll() if the designated constraints aren't met.
type VPCMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VPCMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VPCMultiError) AllErrors() []error { return m }

// VPCValidationError is the validation error returned by VPC.Validate if the
// designated constraints aren't met.
type VPCValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VPCValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VPCValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VPCValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VPCValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VPCValidationError) ErrorName() string { return "VPCValidationError" }

// Error satisfies the builtin error interface
func (e VPCValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVPC.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VPCValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VPCValidationError{}

var _VPC_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on VPCProperties with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VPCProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VPCProperties with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VPCPropertiesMultiError, or
// nil if none found.
func (m *VPCProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *VPCProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Cidr

	// no validation rules for IsDefault

	// no validation rules for SubnetType

	if len(errors) > 0 {
		return VPCPropertiesMultiError(errors)
	}

	return nil
}

// VPCPropertiesMultiError is an error wrapping multiple validation errors
// returned by VPCProperties.ValidateAll() if the designated constraints
// aren't met.
type VPCPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VPCPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VPCPropertiesMultiError) AllErrors() []error { return m }

// VPCPropertiesValidationError is the validation error returned by
// VPCProperties.Validate if the designated constraints aren't met.
type VPCPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VPCPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VPCPropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VPCPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VPCPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VPCPropertiesValidationError) ErrorName() string { return "VPCPropertiesValidationError" }

// Error satisfies the builtin error interface
func (e VPCPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVPCProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VPCPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VPCPropertiesValidationError{}

// Validate checks the field values on GetVPCRDMARequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetVPCRDMARequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVPCRDMARequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetVPCRDMARequestMultiError, or nil if none found.
func (m *GetVPCRDMARequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVPCRDMARequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for VpcName

	if len(errors) > 0 {
		return GetVPCRDMARequestMultiError(errors)
	}

	return nil
}

// GetVPCRDMARequestMultiError is an error wrapping multiple validation errors
// returned by GetVPCRDMARequest.ValidateAll() if the designated constraints
// aren't met.
type GetVPCRDMARequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVPCRDMARequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVPCRDMARequestMultiError) AllErrors() []error { return m }

// GetVPCRDMARequestValidationError is the validation error returned by
// GetVPCRDMARequest.Validate if the designated constraints aren't met.
type GetVPCRDMARequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVPCRDMARequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVPCRDMARequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVPCRDMARequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVPCRDMARequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVPCRDMARequestValidationError) ErrorName() string {
	return "GetVPCRDMARequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetVPCRDMARequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVPCRDMARequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVPCRDMARequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVPCRDMARequestValidationError{}

// Validate checks the field values on VPCRDMAClustersStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *VPCRDMAClustersStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VPCRDMAClustersStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// VPCRDMAClustersStatusMultiError, or nil if none found.
func (m *VPCRDMAClustersStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *VPCRDMAClustersStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetClustersStatus() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, VPCRDMAClustersStatusValidationError{
						field:  fmt.Sprintf("ClustersStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, VPCRDMAClustersStatusValidationError{
						field:  fmt.Sprintf("ClustersStatus[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return VPCRDMAClustersStatusValidationError{
					field:  fmt.Sprintf("ClustersStatus[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return VPCRDMAClustersStatusMultiError(errors)
	}

	return nil
}

// VPCRDMAClustersStatusMultiError is an error wrapping multiple validation
// errors returned by VPCRDMAClustersStatus.ValidateAll() if the designated
// constraints aren't met.
type VPCRDMAClustersStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VPCRDMAClustersStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VPCRDMAClustersStatusMultiError) AllErrors() []error { return m }

// VPCRDMAClustersStatusValidationError is the validation error returned by
// VPCRDMAClustersStatus.Validate if the designated constraints aren't met.
type VPCRDMAClustersStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VPCRDMAClustersStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VPCRDMAClustersStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VPCRDMAClustersStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VPCRDMAClustersStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VPCRDMAClustersStatusValidationError) ErrorName() string {
	return "VPCRDMAClustersStatusValidationError"
}

// Error satisfies the builtin error interface
func (e VPCRDMAClustersStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVPCRDMAClustersStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VPCRDMAClustersStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VPCRDMAClustersStatusValidationError{}

// Validate checks the field values on ClusterStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ClusterStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ClusterStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ClusterStatusMultiError, or
// nil if none found.
func (m *ClusterStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *ClusterStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Protocol

	for idx, item := range m.GetNetworkInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ClusterStatusValidationError{
						field:  fmt.Sprintf("NetworkInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ClusterStatusValidationError{
						field:  fmt.Sprintf("NetworkInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ClusterStatusValidationError{
					field:  fmt.Sprintf("NetworkInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for Properties

	if len(errors) > 0 {
		return ClusterStatusMultiError(errors)
	}

	return nil
}

// ClusterStatusMultiError is an error wrapping multiple validation errors
// returned by ClusterStatus.ValidateAll() if the designated constraints
// aren't met.
type ClusterStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ClusterStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ClusterStatusMultiError) AllErrors() []error { return m }

// ClusterStatusValidationError is the validation error returned by
// ClusterStatus.Validate if the designated constraints aren't met.
type ClusterStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ClusterStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ClusterStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ClusterStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ClusterStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ClusterStatusValidationError) ErrorName() string { return "ClusterStatusValidationError" }

// Error satisfies the builtin error interface
func (e ClusterStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sClusterStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ClusterStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ClusterStatusValidationError{}

// Validate checks the field values on NetworkInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NetworkInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NetworkInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NetworkInfoMultiError, or
// nil if none found.
func (m *NetworkInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *NetworkInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NicCount

	// no validation rules for Bandwidth

	// no validation rules for NodeCount

	if len(errors) > 0 {
		return NetworkInfoMultiError(errors)
	}

	return nil
}

// NetworkInfoMultiError is an error wrapping multiple validation errors
// returned by NetworkInfo.ValidateAll() if the designated constraints aren't met.
type NetworkInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NetworkInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NetworkInfoMultiError) AllErrors() []error { return m }

// NetworkInfoValidationError is the validation error returned by
// NetworkInfo.Validate if the designated constraints aren't met.
type NetworkInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NetworkInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NetworkInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NetworkInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NetworkInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NetworkInfoValidationError) ErrorName() string { return "NetworkInfoValidationError" }

// Error satisfies the builtin error interface
func (e NetworkInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNetworkInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NetworkInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NetworkInfoValidationError{}
