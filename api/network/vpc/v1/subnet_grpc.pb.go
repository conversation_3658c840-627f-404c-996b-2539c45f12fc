// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/vpc/v1/subnet.proto

package vpc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SubnetsClient is the client API for Subnets service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SubnetsClient interface {
	// 列举符合请求的所有 Subnets.
	// [EN] List requested Subnets.
	ListSubnets(ctx context.Context, in *ListSubnetsRequest, opts ...grpc.CallOption) (*ListSubnetsResponse, error)
	// 获取符合请求的一个 Subnet.
	// [EN] Get a requested Subnet.
	GetSubnet(ctx context.Context, in *GetSubnetRequest, opts ...grpc.CallOption) (*Subnet, error)
	// 创建一个 Subnet.
	// [EN] Create a Subnet.
	CreateSubnet(ctx context.Context, in *CreateSubnetRequest, opts ...grpc.CallOption) (*Subnet, error)
	// 更新 Subnet 可编辑字段.
	// [EN] Update Subnet editable properties.
	UpdateSubnet(ctx context.Context, in *UpdateSubnetRequest, opts ...grpc.CallOption) (*Subnet, error)
	// 删除一个 Subnet.
	// [EN] Delete a Subnet.
	DeleteSubnet(ctx context.Context, in *DeleteSubnetRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type subnetsClient struct {
	cc grpc.ClientConnInterface
}

func NewSubnetsClient(cc grpc.ClientConnInterface) SubnetsClient {
	return &subnetsClient{cc}
}

func (c *subnetsClient) ListSubnets(ctx context.Context, in *ListSubnetsRequest, opts ...grpc.CallOption) (*ListSubnetsResponse, error) {
	out := new(ListSubnetsResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.Subnets/ListSubnets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subnetsClient) GetSubnet(ctx context.Context, in *GetSubnetRequest, opts ...grpc.CallOption) (*Subnet, error) {
	out := new(Subnet)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.Subnets/GetSubnet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subnetsClient) CreateSubnet(ctx context.Context, in *CreateSubnetRequest, opts ...grpc.CallOption) (*Subnet, error) {
	out := new(Subnet)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.Subnets/CreateSubnet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subnetsClient) UpdateSubnet(ctx context.Context, in *UpdateSubnetRequest, opts ...grpc.CallOption) (*Subnet, error) {
	out := new(Subnet)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.Subnets/UpdateSubnet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *subnetsClient) DeleteSubnet(ctx context.Context, in *DeleteSubnetRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vpc.v1.Subnets/DeleteSubnet", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SubnetsServer is the server API for Subnets service.
// All implementations must embed UnimplementedSubnetsServer
// for forward compatibility
type SubnetsServer interface {
	// 列举符合请求的所有 Subnets.
	// [EN] List requested Subnets.
	ListSubnets(context.Context, *ListSubnetsRequest) (*ListSubnetsResponse, error)
	// 获取符合请求的一个 Subnet.
	// [EN] Get a requested Subnet.
	GetSubnet(context.Context, *GetSubnetRequest) (*Subnet, error)
	// 创建一个 Subnet.
	// [EN] Create a Subnet.
	CreateSubnet(context.Context, *CreateSubnetRequest) (*Subnet, error)
	// 更新 Subnet 可编辑字段.
	// [EN] Update Subnet editable properties.
	UpdateSubnet(context.Context, *UpdateSubnetRequest) (*Subnet, error)
	// 删除一个 Subnet.
	// [EN] Delete a Subnet.
	DeleteSubnet(context.Context, *DeleteSubnetRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedSubnetsServer()
}

// UnimplementedSubnetsServer must be embedded to have forward compatible implementations.
type UnimplementedSubnetsServer struct {
}

func (UnimplementedSubnetsServer) ListSubnets(context.Context, *ListSubnetsRequest) (*ListSubnetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSubnets not implemented")
}
func (UnimplementedSubnetsServer) GetSubnet(context.Context, *GetSubnetRequest) (*Subnet, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSubnet not implemented")
}
func (UnimplementedSubnetsServer) CreateSubnet(context.Context, *CreateSubnetRequest) (*Subnet, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSubnet not implemented")
}
func (UnimplementedSubnetsServer) UpdateSubnet(context.Context, *UpdateSubnetRequest) (*Subnet, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSubnet not implemented")
}
func (UnimplementedSubnetsServer) DeleteSubnet(context.Context, *DeleteSubnetRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSubnet not implemented")
}
func (UnimplementedSubnetsServer) mustEmbedUnimplementedSubnetsServer() {}

// UnsafeSubnetsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SubnetsServer will
// result in compilation errors.
type UnsafeSubnetsServer interface {
	mustEmbedUnimplementedSubnetsServer()
}

func RegisterSubnetsServer(s grpc.ServiceRegistrar, srv SubnetsServer) {
	s.RegisterService(&Subnets_ServiceDesc, srv)
}

func _Subnets_ListSubnets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSubnetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubnetsServer).ListSubnets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.Subnets/ListSubnets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubnetsServer).ListSubnets(ctx, req.(*ListSubnetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subnets_GetSubnet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSubnetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubnetsServer).GetSubnet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.Subnets/GetSubnet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubnetsServer).GetSubnet(ctx, req.(*GetSubnetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subnets_CreateSubnet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSubnetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubnetsServer).CreateSubnet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.Subnets/CreateSubnet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubnetsServer).CreateSubnet(ctx, req.(*CreateSubnetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subnets_UpdateSubnet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSubnetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubnetsServer).UpdateSubnet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.Subnets/UpdateSubnet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubnetsServer).UpdateSubnet(ctx, req.(*UpdateSubnetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _Subnets_DeleteSubnet_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSubnetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SubnetsServer).DeleteSubnet(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vpc.v1.Subnets/DeleteSubnet",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SubnetsServer).DeleteSubnet(ctx, req.(*DeleteSubnetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// Subnets_ServiceDesc is the grpc.ServiceDesc for Subnets service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var Subnets_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.vpc.v1.Subnets",
	HandlerType: (*SubnetsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListSubnets",
			Handler:    _Subnets_ListSubnets_Handler,
		},
		{
			MethodName: "GetSubnet",
			Handler:    _Subnets_GetSubnet_Handler,
		},
		{
			MethodName: "CreateSubnet",
			Handler:    _Subnets_CreateSubnet_Handler,
		},
		{
			MethodName: "UpdateSubnet",
			Handler:    _Subnets_UpdateSubnet_Handler,
		},
		{
			MethodName: "DeleteSubnet",
			Handler:    _Subnets_DeleteSubnet_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/vpc/v1/subnet.proto",
}
