// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/eip/v1/dnatRule.proto

package eip

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListDNATRulesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListDNATRulesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListDNATRulesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListDNATRulesRequestMultiError, or nil if none found.
func (m *ListDNATRulesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListDNATRulesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	// no validation rules for EipName

	if len(errors) > 0 {
		return ListDNATRulesRequestMultiError(errors)
	}

	return nil
}

// ListDNATRulesRequestMultiError is an error wrapping multiple validation
// errors returned by ListDNATRulesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListDNATRulesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListDNATRulesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListDNATRulesRequestMultiError) AllErrors() []error { return m }

// ListDNATRulesRequestValidationError is the validation error returned by
// ListDNATRulesRequest.Validate if the designated constraints aren't met.
type ListDNATRulesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListDNATRulesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListDNATRulesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListDNATRulesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListDNATRulesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListDNATRulesRequestValidationError) ErrorName() string {
	return "ListDNATRulesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListDNATRulesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListDNATRulesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListDNATRulesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListDNATRulesRequestValidationError{}

// Validate checks the field values on ListMDNATRulesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListMDNATRulesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListMDNATRulesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListMDNATRulesRequestMultiError, or nil if none found.
func (m *ListMDNATRulesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListMDNATRulesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	if len(errors) > 0 {
		return ListMDNATRulesRequestMultiError(errors)
	}

	return nil
}

// ListMDNATRulesRequestMultiError is an error wrapping multiple validation
// errors returned by ListMDNATRulesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListMDNATRulesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListMDNATRulesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListMDNATRulesRequestMultiError) AllErrors() []error { return m }

// ListMDNATRulesRequestValidationError is the validation error returned by
// ListMDNATRulesRequest.Validate if the designated constraints aren't met.
type ListMDNATRulesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListMDNATRulesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListMDNATRulesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListMDNATRulesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListMDNATRulesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListMDNATRulesRequestValidationError) ErrorName() string {
	return "ListMDNATRulesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListMDNATRulesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListMDNATRulesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListMDNATRulesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListMDNATRulesRequestValidationError{}

// Validate checks the field values on GetDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDNATRuleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDNATRuleRequestMultiError, or nil if none found.
func (m *GetDNATRuleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDNATRuleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for DnatRuleName

	// no validation rules for EipName

	if len(errors) > 0 {
		return GetDNATRuleRequestMultiError(errors)
	}

	return nil
}

// GetDNATRuleRequestMultiError is an error wrapping multiple validation errors
// returned by GetDNATRuleRequest.ValidateAll() if the designated constraints
// aren't met.
type GetDNATRuleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDNATRuleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDNATRuleRequestMultiError) AllErrors() []error { return m }

// GetDNATRuleRequestValidationError is the validation error returned by
// GetDNATRuleRequest.Validate if the designated constraints aren't met.
type GetDNATRuleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDNATRuleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDNATRuleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDNATRuleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDNATRuleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDNATRuleRequestValidationError) ErrorName() string {
	return "GetDNATRuleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDNATRuleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDNATRuleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDNATRuleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDNATRuleRequestValidationError{}

// Validate checks the field values on CreateDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateDNATRuleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDNATRuleRequestMultiError, or nil if none found.
func (m *CreateDNATRuleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDNATRuleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetDnatRuleName()) > 63 {
		err := CreateDNATRuleRequestValidationError{
			field:  "DnatRuleName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateDNATRuleRequest_DnatRuleName_Pattern.MatchString(m.GetDnatRuleName()) {
		err := CreateDNATRuleRequestValidationError{
			field:  "DnatRuleName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDnatRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateDNATRuleRequestValidationError{
					field:  "DnatRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateDNATRuleRequestValidationError{
					field:  "DnatRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDnatRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDNATRuleRequestValidationError{
				field:  "DnatRule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EipName

	if len(errors) > 0 {
		return CreateDNATRuleRequestMultiError(errors)
	}

	return nil
}

// CreateDNATRuleRequestMultiError is an error wrapping multiple validation
// errors returned by CreateDNATRuleRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateDNATRuleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDNATRuleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDNATRuleRequestMultiError) AllErrors() []error { return m }

// CreateDNATRuleRequestValidationError is the validation error returned by
// CreateDNATRuleRequest.Validate if the designated constraints aren't met.
type CreateDNATRuleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDNATRuleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDNATRuleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDNATRuleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDNATRuleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDNATRuleRequestValidationError) ErrorName() string {
	return "CreateDNATRuleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateDNATRuleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDNATRuleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDNATRuleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDNATRuleRequestValidationError{}

var _CreateDNATRuleRequest_DnatRuleName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on BindDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *BindDNATRuleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BindDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BindDNATRuleRequestMultiError, or nil if none found.
func (m *BindDNATRuleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BindDNATRuleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetDnatRuleName()) > 63 {
		err := BindDNATRuleRequestValidationError{
			field:  "DnatRuleName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_BindDNATRuleRequest_DnatRuleName_Pattern.MatchString(m.GetDnatRuleName()) {
		err := BindDNATRuleRequestValidationError{
			field:  "DnatRuleName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDnatRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, BindDNATRuleRequestValidationError{
					field:  "DnatRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, BindDNATRuleRequestValidationError{
					field:  "DnatRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDnatRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return BindDNATRuleRequestValidationError{
				field:  "DnatRule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EipName

	if len(errors) > 0 {
		return BindDNATRuleRequestMultiError(errors)
	}

	return nil
}

// BindDNATRuleRequestMultiError is an error wrapping multiple validation
// errors returned by BindDNATRuleRequest.ValidateAll() if the designated
// constraints aren't met.
type BindDNATRuleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BindDNATRuleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BindDNATRuleRequestMultiError) AllErrors() []error { return m }

// BindDNATRuleRequestValidationError is the validation error returned by
// BindDNATRuleRequest.Validate if the designated constraints aren't met.
type BindDNATRuleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BindDNATRuleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BindDNATRuleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BindDNATRuleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BindDNATRuleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BindDNATRuleRequestValidationError) ErrorName() string {
	return "BindDNATRuleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e BindDNATRuleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBindDNATRuleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BindDNATRuleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BindDNATRuleRequestValidationError{}

var _BindDNATRuleRequest_DnatRuleName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on UnbindDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnbindDNATRuleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnbindDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnbindDNATRuleRequestMultiError, or nil if none found.
func (m *UnbindDNATRuleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UnbindDNATRuleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetDnatRuleName()) > 63 {
		err := UnbindDNATRuleRequestValidationError{
			field:  "DnatRuleName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UnbindDNATRuleRequest_DnatRuleName_Pattern.MatchString(m.GetDnatRuleName()) {
		err := UnbindDNATRuleRequestValidationError{
			field:  "DnatRuleName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for EipName

	if len(errors) > 0 {
		return UnbindDNATRuleRequestMultiError(errors)
	}

	return nil
}

// UnbindDNATRuleRequestMultiError is an error wrapping multiple validation
// errors returned by UnbindDNATRuleRequest.ValidateAll() if the designated
// constraints aren't met.
type UnbindDNATRuleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnbindDNATRuleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnbindDNATRuleRequestMultiError) AllErrors() []error { return m }

// UnbindDNATRuleRequestValidationError is the validation error returned by
// UnbindDNATRuleRequest.Validate if the designated constraints aren't met.
type UnbindDNATRuleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnbindDNATRuleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnbindDNATRuleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnbindDNATRuleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnbindDNATRuleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnbindDNATRuleRequestValidationError) ErrorName() string {
	return "UnbindDNATRuleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UnbindDNATRuleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnbindDNATRuleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnbindDNATRuleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnbindDNATRuleRequestValidationError{}

var _UnbindDNATRuleRequest_DnatRuleName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on DnatRules with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DnatRules) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DnatRules with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DnatRulesMultiError, or nil
// if none found.
func (m *DnatRules) ValidateAll() error {
	return m.validate(true)
}

func (m *DnatRules) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for FilterType

	if len(errors) > 0 {
		return DnatRulesMultiError(errors)
	}

	return nil
}

// DnatRulesMultiError is an error wrapping multiple validation errors returned
// by DnatRules.ValidateAll() if the designated constraints aren't met.
type DnatRulesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DnatRulesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DnatRulesMultiError) AllErrors() []error { return m }

// DnatRulesValidationError is the validation error returned by
// DnatRules.Validate if the designated constraints aren't met.
type DnatRulesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DnatRulesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DnatRulesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DnatRulesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DnatRulesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DnatRulesValidationError) ErrorName() string { return "DnatRulesValidationError" }

// Error satisfies the builtin error interface
func (e DnatRulesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDnatRules.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DnatRulesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DnatRulesValidationError{}

// Validate checks the field values on UnbindDNATRulesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UnbindDNATRulesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UnbindDNATRulesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UnbindDNATRulesRequestMultiError, or nil if none found.
func (m *UnbindDNATRulesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UnbindDNATRulesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for EipName

	if all {
		switch v := interface{}(m.GetDnatRules()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UnbindDNATRulesRequestValidationError{
					field:  "DnatRules",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UnbindDNATRulesRequestValidationError{
					field:  "DnatRules",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDnatRules()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UnbindDNATRulesRequestValidationError{
				field:  "DnatRules",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UnbindDNATRulesRequestMultiError(errors)
	}

	return nil
}

// UnbindDNATRulesRequestMultiError is an error wrapping multiple validation
// errors returned by UnbindDNATRulesRequest.ValidateAll() if the designated
// constraints aren't met.
type UnbindDNATRulesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UnbindDNATRulesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UnbindDNATRulesRequestMultiError) AllErrors() []error { return m }

// UnbindDNATRulesRequestValidationError is the validation error returned by
// UnbindDNATRulesRequest.Validate if the designated constraints aren't met.
type UnbindDNATRulesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UnbindDNATRulesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UnbindDNATRulesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UnbindDNATRulesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UnbindDNATRulesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UnbindDNATRulesRequestValidationError) ErrorName() string {
	return "UnbindDNATRulesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UnbindDNATRulesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUnbindDNATRulesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UnbindDNATRulesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UnbindDNATRulesRequestValidationError{}

// Validate checks the field values on UpdateDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateDNATRuleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateDNATRuleRequestMultiError, or nil if none found.
func (m *UpdateDNATRuleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateDNATRuleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetDnatRuleName()) > 63 {
		err := UpdateDNATRuleRequestValidationError{
			field:  "DnatRuleName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UpdateDNATRuleRequest_DnatRuleName_Pattern.MatchString(m.GetDnatRuleName()) {
		err := UpdateDNATRuleRequestValidationError{
			field:  "DnatRuleName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDnatRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateDNATRuleRequestValidationError{
					field:  "DnatRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateDNATRuleRequestValidationError{
					field:  "DnatRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDnatRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateDNATRuleRequestValidationError{
				field:  "DnatRule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EipName

	if len(errors) > 0 {
		return UpdateDNATRuleRequestMultiError(errors)
	}

	return nil
}

// UpdateDNATRuleRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateDNATRuleRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateDNATRuleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateDNATRuleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateDNATRuleRequestMultiError) AllErrors() []error { return m }

// UpdateDNATRuleRequestValidationError is the validation error returned by
// UpdateDNATRuleRequest.Validate if the designated constraints aren't met.
type UpdateDNATRuleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateDNATRuleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateDNATRuleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateDNATRuleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateDNATRuleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateDNATRuleRequestValidationError) ErrorName() string {
	return "UpdateDNATRuleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateDNATRuleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateDNATRuleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateDNATRuleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateDNATRuleRequestValidationError{}

var _UpdateDNATRuleRequest_DnatRuleName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on DeleteDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteDNATRuleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDNATRuleRequestMultiError, or nil if none found.
func (m *DeleteDNATRuleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDNATRuleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for DnatRuleName

	// no validation rules for EipName

	if len(errors) > 0 {
		return DeleteDNATRuleRequestMultiError(errors)
	}

	return nil
}

// DeleteDNATRuleRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteDNATRuleRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteDNATRuleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDNATRuleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDNATRuleRequestMultiError) AllErrors() []error { return m }

// DeleteDNATRuleRequestValidationError is the validation error returned by
// DeleteDNATRuleRequest.Validate if the designated constraints aren't met.
type DeleteDNATRuleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDNATRuleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDNATRuleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDNATRuleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDNATRuleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDNATRuleRequestValidationError) ErrorName() string {
	return "DeleteDNATRuleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDNATRuleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDNATRuleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDNATRuleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDNATRuleRequestValidationError{}

// Validate checks the field values on ListDNATRulesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListDNATRulesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListDNATRulesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListDNATRulesResponseMultiError, or nil if none found.
func (m *ListDNATRulesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListDNATRulesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDnatRules() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListDNATRulesResponseValidationError{
						field:  fmt.Sprintf("DnatRules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListDNATRulesResponseValidationError{
						field:  fmt.Sprintf("DnatRules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListDNATRulesResponseValidationError{
					field:  fmt.Sprintf("DnatRules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListDNATRulesResponseMultiError(errors)
	}

	return nil
}

// ListDNATRulesResponseMultiError is an error wrapping multiple validation
// errors returned by ListDNATRulesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListDNATRulesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListDNATRulesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListDNATRulesResponseMultiError) AllErrors() []error { return m }

// ListDNATRulesResponseValidationError is the validation error returned by
// ListDNATRulesResponse.Validate if the designated constraints aren't met.
type ListDNATRulesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListDNATRulesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListDNATRulesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListDNATRulesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListDNATRulesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListDNATRulesResponseValidationError) ErrorName() string {
	return "ListDNATRulesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListDNATRulesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListDNATRulesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListDNATRulesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListDNATRulesResponseValidationError{}

// Validate checks the field values on DNATRule with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DNATRule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DNATRule with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DNATRuleMultiError, or nil
// if none found.
func (m *DNATRule) ValidateAll() error {
	return m.validate(true)
}

func (m *DNATRule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if m.GetDisplayName() != "" {

		if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
			err := DNATRuleValidationError{
				field:  "DisplayName",
				reason: "value length must be at most 63 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_DNATRule_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
			err := DNATRuleValidationError{
				field:  "DisplayName",
				reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Description

	// no validation rules for Uid

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DNATRuleValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DNATRuleValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DNATRuleValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DNATRuleValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DNATRuleMultiError(errors)
	}

	return nil
}

// DNATRuleMultiError is an error wrapping multiple validation errors returned
// by DNATRule.ValidateAll() if the designated constraints aren't met.
type DNATRuleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DNATRuleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DNATRuleMultiError) AllErrors() []error { return m }

// DNATRuleValidationError is the validation error returned by
// DNATRule.Validate if the designated constraints aren't met.
type DNATRuleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DNATRuleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DNATRuleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DNATRuleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DNATRuleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DNATRuleValidationError) ErrorName() string { return "DNATRuleValidationError" }

// Error satisfies the builtin error interface
func (e DNATRuleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDNATRule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DNATRuleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DNATRuleValidationError{}

var _DNATRule_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on DNATRuleProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DNATRuleProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DNATRuleProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DNATRulePropertiesMultiError, or nil if none found.
func (m *DNATRuleProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *DNATRuleProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NatGatewayId

	// no validation rules for EipId

	// no validation rules for ExternalIp

	// no validation rules for ExternalPort

	// no validation rules for Protocol

	// no validation rules for InternalIp

	// no validation rules for InternalPort

	// no validation rules for Priority

	// no validation rules for InternalInstanceType

	// no validation rules for InternalInstanceName

	if len(errors) > 0 {
		return DNATRulePropertiesMultiError(errors)
	}

	return nil
}

// DNATRulePropertiesMultiError is an error wrapping multiple validation errors
// returned by DNATRuleProperties.ValidateAll() if the designated constraints
// aren't met.
type DNATRulePropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DNATRulePropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DNATRulePropertiesMultiError) AllErrors() []error { return m }

// DNATRulePropertiesValidationError is the validation error returned by
// DNATRuleProperties.Validate if the designated constraints aren't met.
type DNATRulePropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DNATRulePropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DNATRulePropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DNATRulePropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DNATRulePropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DNATRulePropertiesValidationError) ErrorName() string {
	return "DNATRulePropertiesValidationError"
}

// Error satisfies the builtin error interface
func (e DNATRulePropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDNATRuleProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DNATRulePropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DNATRulePropertiesValidationError{}
