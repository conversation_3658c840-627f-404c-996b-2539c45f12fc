// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/eip/v1/eip_data.proto

package eip

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// EIPDataServiceClient is the client API for EIPDataService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EIPDataServiceClient interface {
	// 列举符合请求的所有 SLB 类型的 EIPs.
	// [EN] List requested slb-typed EIPs.
	ListSlbTypedAvailableEIPs(ctx context.Context, in *ListSlbTypedAvailableEIPsRequest, opts ...grpc.CallOption) (*ListSlbTypedAvailableEIPsResponse, error)
	// [zh] 企业内网EIP更新dst cidr
	// [en] Internal EIP update dst cidr
	UpdateEIPDstCIDR(ctx context.Context, in *UpdateEIPDstCIDRRequest, opts ...grpc.CallOption) (*EIPDstCIDR, error)
}

type eIPDataServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewEIPDataServiceClient(cc grpc.ClientConnInterface) EIPDataServiceClient {
	return &eIPDataServiceClient{cc}
}

func (c *eIPDataServiceClient) ListSlbTypedAvailableEIPs(ctx context.Context, in *ListSlbTypedAvailableEIPsRequest, opts ...grpc.CallOption) (*ListSlbTypedAvailableEIPsResponse, error) {
	out := new(ListSlbTypedAvailableEIPsResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.EIPDataService/ListSlbTypedAvailableEIPs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eIPDataServiceClient) UpdateEIPDstCIDR(ctx context.Context, in *UpdateEIPDstCIDRRequest, opts ...grpc.CallOption) (*EIPDstCIDR, error) {
	out := new(EIPDstCIDR)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.EIPDataService/UpdateEIPDstCIDR", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EIPDataServiceServer is the server API for EIPDataService service.
// All implementations must embed UnimplementedEIPDataServiceServer
// for forward compatibility
type EIPDataServiceServer interface {
	// 列举符合请求的所有 SLB 类型的 EIPs.
	// [EN] List requested slb-typed EIPs.
	ListSlbTypedAvailableEIPs(context.Context, *ListSlbTypedAvailableEIPsRequest) (*ListSlbTypedAvailableEIPsResponse, error)
	// [zh] 企业内网EIP更新dst cidr
	// [en] Internal EIP update dst cidr
	UpdateEIPDstCIDR(context.Context, *UpdateEIPDstCIDRRequest) (*EIPDstCIDR, error)
	mustEmbedUnimplementedEIPDataServiceServer()
}

// UnimplementedEIPDataServiceServer must be embedded to have forward compatible implementations.
type UnimplementedEIPDataServiceServer struct {
}

func (UnimplementedEIPDataServiceServer) ListSlbTypedAvailableEIPs(context.Context, *ListSlbTypedAvailableEIPsRequest) (*ListSlbTypedAvailableEIPsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSlbTypedAvailableEIPs not implemented")
}
func (UnimplementedEIPDataServiceServer) UpdateEIPDstCIDR(context.Context, *UpdateEIPDstCIDRRequest) (*EIPDstCIDR, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEIPDstCIDR not implemented")
}
func (UnimplementedEIPDataServiceServer) mustEmbedUnimplementedEIPDataServiceServer() {}

// UnsafeEIPDataServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EIPDataServiceServer will
// result in compilation errors.
type UnsafeEIPDataServiceServer interface {
	mustEmbedUnimplementedEIPDataServiceServer()
}

func RegisterEIPDataServiceServer(s grpc.ServiceRegistrar, srv EIPDataServiceServer) {
	s.RegisterService(&EIPDataService_ServiceDesc, srv)
}

func _EIPDataService_ListSlbTypedAvailableEIPs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSlbTypedAvailableEIPsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EIPDataServiceServer).ListSlbTypedAvailableEIPs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.EIPDataService/ListSlbTypedAvailableEIPs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EIPDataServiceServer).ListSlbTypedAvailableEIPs(ctx, req.(*ListSlbTypedAvailableEIPsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EIPDataService_UpdateEIPDstCIDR_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEIPDstCIDRRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EIPDataServiceServer).UpdateEIPDstCIDR(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.EIPDataService/UpdateEIPDstCIDR",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EIPDataServiceServer).UpdateEIPDstCIDR(ctx, req.(*UpdateEIPDstCIDRRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// EIPDataService_ServiceDesc is the grpc.ServiceDesc for EIPDataService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EIPDataService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.eip.v1.EIPDataService",
	HandlerType: (*EIPDataServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListSlbTypedAvailableEIPs",
			Handler:    _EIPDataService_ListSlbTypedAvailableEIPs_Handler,
		},
		{
			MethodName: "UpdateEIPDstCIDR",
			Handler:    _EIPDataService_UpdateEIPDstCIDR_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/eip/v1/eip_data.proto",
}
