// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/eip/v1/eip.proto

package eip

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// EIPsClient is the client API for EIPs service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type EIPsClient interface {
	// [zh] 获取 EIP 实例列表.
	// [en] List requested EIPs.
	ListEIPs(ctx context.Context, in *ListEIPsRequest, opts ...grpc.CallOption) (*ListEIPsResponse, error)
	// [zh] 查看 EIP 实例详情.
	// [en] Get a requested EIP.
	GetEIP(ctx context.Context, in *GetEIPRequest, opts ...grpc.CallOption) (*EIP, error)
	// [zh] 查看 EIP 状态信息.
	// [en] Get a requested EIP status.
	GetEIPStatus(ctx context.Context, in *GetEIPStatusRequest, opts ...grpc.CallOption) (*EIPStatus, error)
	// [zh] 创建一个 EIP.
	// [en] Create a EIP.
	CreateEIP(ctx context.Context, in *CreateEIPRequest, opts ...grpc.CallOption) (*EIP, error)
	// [zh] 更新 EIP 可编辑字段.
	// [en] Update EIP editable properties.
	UpdateEIP(ctx context.Context, in *UpdateEIPRequest, opts ...grpc.CallOption) (*EIP, error)
	// [zh] 过期停服一个 EIP.
	// [en] ExpireStop a EIP.
	ExpireStopEIP(ctx context.Context, in *ExpireStopEIPRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// [zh] 恢复一个 EIP.
	// [en] Resume a EIP.
	RenewStartEIP(ctx context.Context, in *RenewStartEIPRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// [zh] 在保留期释放一个 EIP.
	// [en] release a EIP.
	ReleaseEIP(ctx context.Context, in *ReleaseEIPRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// [zh] 强制删除一个 EIP.
	// [en] Force Delete a EIP.
	ForceDeleteEIP(ctx context.Context, in *ForceDeleteEIPRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// [zh] 删除一个 EIP.
	// [en] Delete a EIP.
	DeleteEIP(ctx context.Context, in *DeleteEIPRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// [zh] EIP 扩缩容.
	// [en] EIP resize.
	ResizeEIP(ctx context.Context, in *ResizeEIPRequest, opts ...grpc.CallOption) (*EIP, error)
	// [zh] EIP metrics.
	// [en] EIP metrics.
	GetEIPMetrics(ctx context.Context, in *GetEIPMetricsRequest, opts ...grpc.CallOption) (*EIPMetrics, error)
}

type eIPsClient struct {
	cc grpc.ClientConnInterface
}

func NewEIPsClient(cc grpc.ClientConnInterface) EIPsClient {
	return &eIPsClient{cc}
}

func (c *eIPsClient) ListEIPs(ctx context.Context, in *ListEIPsRequest, opts ...grpc.CallOption) (*ListEIPsResponse, error) {
	out := new(ListEIPsResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.EIPs/ListEIPs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eIPsClient) GetEIP(ctx context.Context, in *GetEIPRequest, opts ...grpc.CallOption) (*EIP, error) {
	out := new(EIP)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.EIPs/GetEIP", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eIPsClient) GetEIPStatus(ctx context.Context, in *GetEIPStatusRequest, opts ...grpc.CallOption) (*EIPStatus, error) {
	out := new(EIPStatus)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.EIPs/GetEIPStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eIPsClient) CreateEIP(ctx context.Context, in *CreateEIPRequest, opts ...grpc.CallOption) (*EIP, error) {
	out := new(EIP)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.EIPs/CreateEIP", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eIPsClient) UpdateEIP(ctx context.Context, in *UpdateEIPRequest, opts ...grpc.CallOption) (*EIP, error) {
	out := new(EIP)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.EIPs/UpdateEIP", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eIPsClient) ExpireStopEIP(ctx context.Context, in *ExpireStopEIPRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.EIPs/ExpireStopEIP", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eIPsClient) RenewStartEIP(ctx context.Context, in *RenewStartEIPRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.EIPs/RenewStartEIP", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eIPsClient) ReleaseEIP(ctx context.Context, in *ReleaseEIPRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.EIPs/ReleaseEIP", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eIPsClient) ForceDeleteEIP(ctx context.Context, in *ForceDeleteEIPRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.EIPs/ForceDeleteEIP", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eIPsClient) DeleteEIP(ctx context.Context, in *DeleteEIPRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.EIPs/DeleteEIP", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eIPsClient) ResizeEIP(ctx context.Context, in *ResizeEIPRequest, opts ...grpc.CallOption) (*EIP, error) {
	out := new(EIP)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.EIPs/ResizeEIP", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *eIPsClient) GetEIPMetrics(ctx context.Context, in *GetEIPMetricsRequest, opts ...grpc.CallOption) (*EIPMetrics, error) {
	out := new(EIPMetrics)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.EIPs/GetEIPMetrics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// EIPsServer is the server API for EIPs service.
// All implementations must embed UnimplementedEIPsServer
// for forward compatibility
type EIPsServer interface {
	// [zh] 获取 EIP 实例列表.
	// [en] List requested EIPs.
	ListEIPs(context.Context, *ListEIPsRequest) (*ListEIPsResponse, error)
	// [zh] 查看 EIP 实例详情.
	// [en] Get a requested EIP.
	GetEIP(context.Context, *GetEIPRequest) (*EIP, error)
	// [zh] 查看 EIP 状态信息.
	// [en] Get a requested EIP status.
	GetEIPStatus(context.Context, *GetEIPStatusRequest) (*EIPStatus, error)
	// [zh] 创建一个 EIP.
	// [en] Create a EIP.
	CreateEIP(context.Context, *CreateEIPRequest) (*EIP, error)
	// [zh] 更新 EIP 可编辑字段.
	// [en] Update EIP editable properties.
	UpdateEIP(context.Context, *UpdateEIPRequest) (*EIP, error)
	// [zh] 过期停服一个 EIP.
	// [en] ExpireStop a EIP.
	ExpireStopEIP(context.Context, *ExpireStopEIPRequest) (*emptypb.Empty, error)
	// [zh] 恢复一个 EIP.
	// [en] Resume a EIP.
	RenewStartEIP(context.Context, *RenewStartEIPRequest) (*emptypb.Empty, error)
	// [zh] 在保留期释放一个 EIP.
	// [en] release a EIP.
	ReleaseEIP(context.Context, *ReleaseEIPRequest) (*emptypb.Empty, error)
	// [zh] 强制删除一个 EIP.
	// [en] Force Delete a EIP.
	ForceDeleteEIP(context.Context, *ForceDeleteEIPRequest) (*emptypb.Empty, error)
	// [zh] 删除一个 EIP.
	// [en] Delete a EIP.
	DeleteEIP(context.Context, *DeleteEIPRequest) (*emptypb.Empty, error)
	// [zh] EIP 扩缩容.
	// [en] EIP resize.
	ResizeEIP(context.Context, *ResizeEIPRequest) (*EIP, error)
	// [zh] EIP metrics.
	// [en] EIP metrics.
	GetEIPMetrics(context.Context, *GetEIPMetricsRequest) (*EIPMetrics, error)
	mustEmbedUnimplementedEIPsServer()
}

// UnimplementedEIPsServer must be embedded to have forward compatible implementations.
type UnimplementedEIPsServer struct {
}

func (UnimplementedEIPsServer) ListEIPs(context.Context, *ListEIPsRequest) (*ListEIPsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListEIPs not implemented")
}
func (UnimplementedEIPsServer) GetEIP(context.Context, *GetEIPRequest) (*EIP, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEIP not implemented")
}
func (UnimplementedEIPsServer) GetEIPStatus(context.Context, *GetEIPStatusRequest) (*EIPStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEIPStatus not implemented")
}
func (UnimplementedEIPsServer) CreateEIP(context.Context, *CreateEIPRequest) (*EIP, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateEIP not implemented")
}
func (UnimplementedEIPsServer) UpdateEIP(context.Context, *UpdateEIPRequest) (*EIP, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateEIP not implemented")
}
func (UnimplementedEIPsServer) ExpireStopEIP(context.Context, *ExpireStopEIPRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ExpireStopEIP not implemented")
}
func (UnimplementedEIPsServer) RenewStartEIP(context.Context, *RenewStartEIPRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method RenewStartEIP not implemented")
}
func (UnimplementedEIPsServer) ReleaseEIP(context.Context, *ReleaseEIPRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseEIP not implemented")
}
func (UnimplementedEIPsServer) ForceDeleteEIP(context.Context, *ForceDeleteEIPRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ForceDeleteEIP not implemented")
}
func (UnimplementedEIPsServer) DeleteEIP(context.Context, *DeleteEIPRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteEIP not implemented")
}
func (UnimplementedEIPsServer) ResizeEIP(context.Context, *ResizeEIPRequest) (*EIP, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResizeEIP not implemented")
}
func (UnimplementedEIPsServer) GetEIPMetrics(context.Context, *GetEIPMetricsRequest) (*EIPMetrics, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetEIPMetrics not implemented")
}
func (UnimplementedEIPsServer) mustEmbedUnimplementedEIPsServer() {}

// UnsafeEIPsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to EIPsServer will
// result in compilation errors.
type UnsafeEIPsServer interface {
	mustEmbedUnimplementedEIPsServer()
}

func RegisterEIPsServer(s grpc.ServiceRegistrar, srv EIPsServer) {
	s.RegisterService(&EIPs_ServiceDesc, srv)
}

func _EIPs_ListEIPs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListEIPsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EIPsServer).ListEIPs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.EIPs/ListEIPs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EIPsServer).ListEIPs(ctx, req.(*ListEIPsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EIPs_GetEIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EIPsServer).GetEIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.EIPs/GetEIP",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EIPsServer).GetEIP(ctx, req.(*GetEIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EIPs_GetEIPStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEIPStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EIPsServer).GetEIPStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.EIPs/GetEIPStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EIPsServer).GetEIPStatus(ctx, req.(*GetEIPStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EIPs_CreateEIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateEIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EIPsServer).CreateEIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.EIPs/CreateEIP",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EIPsServer).CreateEIP(ctx, req.(*CreateEIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EIPs_UpdateEIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateEIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EIPsServer).UpdateEIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.EIPs/UpdateEIP",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EIPsServer).UpdateEIP(ctx, req.(*UpdateEIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EIPs_ExpireStopEIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ExpireStopEIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EIPsServer).ExpireStopEIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.EIPs/ExpireStopEIP",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EIPsServer).ExpireStopEIP(ctx, req.(*ExpireStopEIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EIPs_RenewStartEIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(RenewStartEIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EIPsServer).RenewStartEIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.EIPs/RenewStartEIP",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EIPsServer).RenewStartEIP(ctx, req.(*RenewStartEIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EIPs_ReleaseEIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseEIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EIPsServer).ReleaseEIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.EIPs/ReleaseEIP",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EIPsServer).ReleaseEIP(ctx, req.(*ReleaseEIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EIPs_ForceDeleteEIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ForceDeleteEIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EIPsServer).ForceDeleteEIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.EIPs/ForceDeleteEIP",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EIPsServer).ForceDeleteEIP(ctx, req.(*ForceDeleteEIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EIPs_DeleteEIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteEIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EIPsServer).DeleteEIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.EIPs/DeleteEIP",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EIPsServer).DeleteEIP(ctx, req.(*DeleteEIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EIPs_ResizeEIP_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResizeEIPRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EIPsServer).ResizeEIP(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.EIPs/ResizeEIP",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EIPsServer).ResizeEIP(ctx, req.(*ResizeEIPRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _EIPs_GetEIPMetrics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetEIPMetricsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(EIPsServer).GetEIPMetrics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.EIPs/GetEIPMetrics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(EIPsServer).GetEIPMetrics(ctx, req.(*GetEIPMetricsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// EIPs_ServiceDesc is the grpc.ServiceDesc for EIPs service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var EIPs_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.eip.v1.EIPs",
	HandlerType: (*EIPsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListEIPs",
			Handler:    _EIPs_ListEIPs_Handler,
		},
		{
			MethodName: "GetEIP",
			Handler:    _EIPs_GetEIP_Handler,
		},
		{
			MethodName: "GetEIPStatus",
			Handler:    _EIPs_GetEIPStatus_Handler,
		},
		{
			MethodName: "CreateEIP",
			Handler:    _EIPs_CreateEIP_Handler,
		},
		{
			MethodName: "UpdateEIP",
			Handler:    _EIPs_UpdateEIP_Handler,
		},
		{
			MethodName: "ExpireStopEIP",
			Handler:    _EIPs_ExpireStopEIP_Handler,
		},
		{
			MethodName: "RenewStartEIP",
			Handler:    _EIPs_RenewStartEIP_Handler,
		},
		{
			MethodName: "ReleaseEIP",
			Handler:    _EIPs_ReleaseEIP_Handler,
		},
		{
			MethodName: "ForceDeleteEIP",
			Handler:    _EIPs_ForceDeleteEIP_Handler,
		},
		{
			MethodName: "DeleteEIP",
			Handler:    _EIPs_DeleteEIP_Handler,
		},
		{
			MethodName: "ResizeEIP",
			Handler:    _EIPs_ResizeEIP_Handler,
		},
		{
			MethodName: "GetEIPMetrics",
			Handler:    _EIPs_GetEIPMetrics_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/eip/v1/eip.proto",
}
