// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/eip/v1/snatRule.proto

package eip

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SNATRulesClient is the client API for SNATRules service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SNATRulesClient interface {
	// 列举符合请求的所有 SNATRules.
	// [EN] List requested SNATRules.
	ListSNATRules(ctx context.Context, in *ListSNATRulesRequest, opts ...grpc.CallOption) (*ListSNATRulesResponse, error)
	// 获取符合请求的一个 SNATRule.
	// [EN] Get a requested SNATRule.
	GetSNATRule(ctx context.Context, in *GetSNATRuleRequest, opts ...grpc.CallOption) (*SNATRule, error)
	// 创建一个 SNATRule.
	// [EN] Create a SNATRule.
	CreateSNATRule(ctx context.Context, in *CreateSNATRuleRequest, opts ...grpc.CallOption) (*SNATRule, error)
	// 删除一个 SNATRule.
	// [EN] Delete a SNATRule.
	DeleteSNATRule(ctx context.Context, in *DeleteSNATRuleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type sNATRulesClient struct {
	cc grpc.ClientConnInterface
}

func NewSNATRulesClient(cc grpc.ClientConnInterface) SNATRulesClient {
	return &sNATRulesClient{cc}
}

func (c *sNATRulesClient) ListSNATRules(ctx context.Context, in *ListSNATRulesRequest, opts ...grpc.CallOption) (*ListSNATRulesResponse, error) {
	out := new(ListSNATRulesResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.SNATRules/ListSNATRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sNATRulesClient) GetSNATRule(ctx context.Context, in *GetSNATRuleRequest, opts ...grpc.CallOption) (*SNATRule, error) {
	out := new(SNATRule)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.SNATRules/GetSNATRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sNATRulesClient) CreateSNATRule(ctx context.Context, in *CreateSNATRuleRequest, opts ...grpc.CallOption) (*SNATRule, error) {
	out := new(SNATRule)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.SNATRules/CreateSNATRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sNATRulesClient) DeleteSNATRule(ctx context.Context, in *DeleteSNATRuleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.SNATRules/DeleteSNATRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SNATRulesServer is the server API for SNATRules service.
// All implementations must embed UnimplementedSNATRulesServer
// for forward compatibility
type SNATRulesServer interface {
	// 列举符合请求的所有 SNATRules.
	// [EN] List requested SNATRules.
	ListSNATRules(context.Context, *ListSNATRulesRequest) (*ListSNATRulesResponse, error)
	// 获取符合请求的一个 SNATRule.
	// [EN] Get a requested SNATRule.
	GetSNATRule(context.Context, *GetSNATRuleRequest) (*SNATRule, error)
	// 创建一个 SNATRule.
	// [EN] Create a SNATRule.
	CreateSNATRule(context.Context, *CreateSNATRuleRequest) (*SNATRule, error)
	// 删除一个 SNATRule.
	// [EN] Delete a SNATRule.
	DeleteSNATRule(context.Context, *DeleteSNATRuleRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedSNATRulesServer()
}

// UnimplementedSNATRulesServer must be embedded to have forward compatible implementations.
type UnimplementedSNATRulesServer struct {
}

func (UnimplementedSNATRulesServer) ListSNATRules(context.Context, *ListSNATRulesRequest) (*ListSNATRulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSNATRules not implemented")
}
func (UnimplementedSNATRulesServer) GetSNATRule(context.Context, *GetSNATRuleRequest) (*SNATRule, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSNATRule not implemented")
}
func (UnimplementedSNATRulesServer) CreateSNATRule(context.Context, *CreateSNATRuleRequest) (*SNATRule, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSNATRule not implemented")
}
func (UnimplementedSNATRulesServer) DeleteSNATRule(context.Context, *DeleteSNATRuleRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSNATRule not implemented")
}
func (UnimplementedSNATRulesServer) mustEmbedUnimplementedSNATRulesServer() {}

// UnsafeSNATRulesServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SNATRulesServer will
// result in compilation errors.
type UnsafeSNATRulesServer interface {
	mustEmbedUnimplementedSNATRulesServer()
}

func RegisterSNATRulesServer(s grpc.ServiceRegistrar, srv SNATRulesServer) {
	s.RegisterService(&SNATRules_ServiceDesc, srv)
}

func _SNATRules_ListSNATRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSNATRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SNATRulesServer).ListSNATRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.SNATRules/ListSNATRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SNATRulesServer).ListSNATRules(ctx, req.(*ListSNATRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SNATRules_GetSNATRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSNATRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SNATRulesServer).GetSNATRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.SNATRules/GetSNATRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SNATRulesServer).GetSNATRule(ctx, req.(*GetSNATRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SNATRules_CreateSNATRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSNATRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SNATRulesServer).CreateSNATRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.SNATRules/CreateSNATRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SNATRulesServer).CreateSNATRule(ctx, req.(*CreateSNATRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SNATRules_DeleteSNATRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSNATRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SNATRulesServer).DeleteSNATRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.SNATRules/DeleteSNATRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SNATRulesServer).DeleteSNATRule(ctx, req.(*DeleteSNATRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SNATRules_ServiceDesc is the grpc.ServiceDesc for SNATRules service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SNATRules_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.eip.v1.SNATRules",
	HandlerType: (*SNATRulesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListSNATRules",
			Handler:    _SNATRules_ListSNATRules_Handler,
		},
		{
			MethodName: "GetSNATRule",
			Handler:    _SNATRules_GetSNATRule_Handler,
		},
		{
			MethodName: "CreateSNATRule",
			Handler:    _SNATRules_CreateSNATRule_Handler,
		},
		{
			MethodName: "DeleteSNATRule",
			Handler:    _SNATRules_DeleteSNATRule_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/eip/v1/snatRule.proto",
}
