// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/eip/v1/snatRule.proto

package eip

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/api/annotations"
	v1 "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents the different states of a SNATRule.
type SNATRule_State int32

const (
	// The SNATRule resource is being created.
	SNATRule_CREATING SNATRule_State = 0
	// The SNATRule resource has been created.
	SNATRule_CREATED SNATRule_State = 1
	// The SNATRule resource is being bound.
	SNATRule_BINDING SNATRule_State = 2
	// The SNATRule resource is being unbound.
	SNATRule_UNBINDING SNATRule_State = 3
	// The SNATRule resource is being updated.
	SNATRule_UPDATING SNATRule_State = 4
	// The SNATRule resource has been active.
	SNATRule_ACTIVE SNATRule_State = 5
	// The SNATRule resource is being deleted.
	SNATRule_DELETING SNATRule_State = 6
	// The SNATRule resource has been deleted.
	SNATRule_DELETED SNATRule_State = 7
	// The SNATRule resource has been failed.
	SNATRule_FAILED SNATRule_State = 8
)

// Enum value maps for SNATRule_State.
var (
	SNATRule_State_name = map[int32]string{
		0: "CREATING",
		1: "CREATED",
		2: "BINDING",
		3: "UNBINDING",
		4: "UPDATING",
		5: "ACTIVE",
		6: "DELETING",
		7: "DELETED",
		8: "FAILED",
	}
	SNATRule_State_value = map[string]int32{
		"CREATING":  0,
		"CREATED":   1,
		"BINDING":   2,
		"UNBINDING": 3,
		"UPDATING":  4,
		"ACTIVE":    5,
		"DELETING":  6,
		"DELETED":   7,
		"FAILED":    8,
	}
)

func (x SNATRule_State) Enum() *SNATRule_State {
	p := new(SNATRule_State)
	*p = x
	return p
}

func (x SNATRule_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SNATRule_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_eip_v1_snatRule_proto_enumTypes[0].Descriptor()
}

func (SNATRule_State) Type() protoreflect.EnumType {
	return &file_network_eip_v1_snatRule_proto_enumTypes[0]
}

func (x SNATRule_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SNATRule_State.Descriptor instead.
func (SNATRule_State) EnumDescriptor() ([]byte, []int) {
	return file_network_eip_v1_snatRule_proto_rawDescGZIP(), []int{5, 0}
}

// 列举 SNATRules 的请求.
// [EN] Request to list SNATRules.
type ListSNATRulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// List filter.
	Filter string `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// Sort resoults.
	OrderBy string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// The maximum number of items to return.
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// The next_page_token value returned from a previous List request, if any.
	PageToken string `protobuf:"bytes,7,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// eip_name
	EipName string `protobuf:"bytes,8,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *ListSNATRulesRequest) Reset() {
	*x = ListSNATRulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_snatRule_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSNATRulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSNATRulesRequest) ProtoMessage() {}

func (x *ListSNATRulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_snatRule_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSNATRulesRequest.ProtoReflect.Descriptor instead.
func (*ListSNATRulesRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_snatRule_proto_rawDescGZIP(), []int{0}
}

func (x *ListSNATRulesRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListSNATRulesRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListSNATRulesRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListSNATRulesRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListSNATRulesRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListSNATRulesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListSNATRulesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListSNATRulesRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// 获取某一个 SNATRule 的请求.
// [EN] Request to get a SNATRule.
type GetSNATRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The SNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SnatRuleName string `protobuf:"bytes,4,opt,name=snat_rule_name,json=snatRuleName,proto3" json:"snat_rule_name,omitempty"`
	// eip_name
	EipName string `protobuf:"bytes,5,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *GetSNATRuleRequest) Reset() {
	*x = GetSNATRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_snatRule_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSNATRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSNATRuleRequest) ProtoMessage() {}

func (x *GetSNATRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_snatRule_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSNATRuleRequest.ProtoReflect.Descriptor instead.
func (*GetSNATRuleRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_snatRule_proto_rawDescGZIP(), []int{1}
}

func (x *GetSNATRuleRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetSNATRuleRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetSNATRuleRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetSNATRuleRequest) GetSnatRuleName() string {
	if x != nil {
		return x.SnatRuleName
	}
	return ""
}

func (x *GetSNATRuleRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// 创建一个 SNATRule 的请求.
// [EN] Request to create a SNATRule.
type CreateSNATRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone, todo: add validation
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The SNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SnatRuleName string `protobuf:"bytes,4,opt,name=snat_rule_name,json=snatRuleName,proto3" json:"snat_rule_name,omitempty"`
	// The SNATRule resource to create.
	SnatRule *SNATRule `protobuf:"bytes,5,opt,name=snat_rule,json=snatRule,proto3" json:"snat_rule,omitempty"`
	// eip_name
	EipName string `protobuf:"bytes,6,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *CreateSNATRuleRequest) Reset() {
	*x = CreateSNATRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_snatRule_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSNATRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSNATRuleRequest) ProtoMessage() {}

func (x *CreateSNATRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_snatRule_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSNATRuleRequest.ProtoReflect.Descriptor instead.
func (*CreateSNATRuleRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_snatRule_proto_rawDescGZIP(), []int{2}
}

func (x *CreateSNATRuleRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *CreateSNATRuleRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *CreateSNATRuleRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateSNATRuleRequest) GetSnatRuleName() string {
	if x != nil {
		return x.SnatRuleName
	}
	return ""
}

func (x *CreateSNATRuleRequest) GetSnatRule() *SNATRule {
	if x != nil {
		return x.SnatRule
	}
	return nil
}

func (x *CreateSNATRuleRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// 删除某一个 SNATRule 的请求.
// [EN] Request to delete a SNATRule.
type DeleteSNATRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The SNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SnatRuleName string `protobuf:"bytes,4,opt,name=snat_rule_name,json=snatRuleName,proto3" json:"snat_rule_name,omitempty"`
	// eip_name
	EipName string `protobuf:"bytes,5,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *DeleteSNATRuleRequest) Reset() {
	*x = DeleteSNATRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_snatRule_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSNATRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSNATRuleRequest) ProtoMessage() {}

func (x *DeleteSNATRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_snatRule_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSNATRuleRequest.ProtoReflect.Descriptor instead.
func (*DeleteSNATRuleRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_snatRule_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteSNATRuleRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DeleteSNATRuleRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *DeleteSNATRuleRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteSNATRuleRequest) GetSnatRuleName() string {
	if x != nil {
		return x.SnatRuleName
	}
	return ""
}

func (x *DeleteSNATRuleRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// 列举 SNATRules 的响应.
// [EN] Response to list SNATRules.
type ListSNATRulesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// SNATRule 列表.
	// [EN] SNATRule list.
	SnatRules []*SNATRule `protobuf:"bytes,1,rep,name=snat_rules,json=snatRules,proto3" json:"snat_rules,omitempty"`
	// 下一个页面的 token，如果没有更多数据则为空.
	// [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListSNATRulesResponse) Reset() {
	*x = ListSNATRulesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_snatRule_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSNATRulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSNATRulesResponse) ProtoMessage() {}

func (x *ListSNATRulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_snatRule_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSNATRulesResponse.ProtoReflect.Descriptor instead.
func (*ListSNATRulesResponse) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_snatRule_proto_rawDescGZIP(), []int{4}
}

func (x *ListSNATRulesResponse) GetSnatRules() []*SNATRule {
	if x != nil {
		return x.SnatRules
	}
	return nil
}

func (x *ListSNATRulesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListSNATRulesResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// SNATRule 实例结构体.
// [EN] SNATRule entity.
type SNATRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The SNATRule resource id using the form:
	//     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/snatRules/{snat_rule_name}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The SNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// ContainerInstance resource display name
	DisplayName string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// ContainerInstance resource description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// The SNATRule resource uuid.
	Uid string `protobuf:"bytes,5,opt,name=uid,proto3" json:"uid,omitempty"`
	// The SNATRule resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// The id of the user who created the SNATRule resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// The id of the user who owns the SNATRule resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// Available zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// The current state of the SNATRule resource.
	State SNATRule_State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.eip.v1.SNATRule_State" json:"state,omitempty"`
	// Sku id.
	SkuId string `protobuf:"bytes,12,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// Tags attached to the SNATRule resource.
	Tags map[string]string `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Properties of the SNATRule resource.
	Properties *SNATRuleProperties `protobuf:"bytes,14,opt,name=properties,proto3" json:"properties,omitempty"`
	// Payment information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,15,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	// Indicates whether the SNATRule resource is deleted or not.
	Deleted bool `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// The time when the SNATRule resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The time when the SNATRule resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *SNATRule) Reset() {
	*x = SNATRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_snatRule_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SNATRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SNATRule) ProtoMessage() {}

func (x *SNATRule) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_snatRule_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SNATRule.ProtoReflect.Descriptor instead.
func (*SNATRule) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_snatRule_proto_rawDescGZIP(), []int{5}
}

func (x *SNATRule) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SNATRule) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SNATRule) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *SNATRule) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SNATRule) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SNATRule) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *SNATRule) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *SNATRule) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *SNATRule) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *SNATRule) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *SNATRule) GetState() SNATRule_State {
	if x != nil {
		return x.State
	}
	return SNATRule_CREATING
}

func (x *SNATRule) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *SNATRule) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SNATRule) GetProperties() *SNATRuleProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *SNATRule) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

func (x *SNATRule) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *SNATRule) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *SNATRule) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// 资源实际属性.
// [EN] Real resource properties.
type SNATRuleProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 所属的 VPC uid.
	// [EN] VPC uid.
	VpcId string `protobuf:"bytes,1,opt,name=vpc_id,json=vpcId,proto3" json:"vpc_id,omitempty"`
	// 所属的 EIP uid.
	// [EN] EIP uid.
	EipId string `protobuf:"bytes,2,opt,name=eip_id,json=eipId,proto3" json:"eip_id,omitempty"`
}

func (x *SNATRuleProperties) Reset() {
	*x = SNATRuleProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_snatRule_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SNATRuleProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SNATRuleProperties) ProtoMessage() {}

func (x *SNATRuleProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_snatRule_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SNATRuleProperties.ProtoReflect.Descriptor instead.
func (*SNATRuleProperties) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_snatRule_proto_rawDescGZIP(), []int{6}
}

func (x *SNATRuleProperties) GetVpcId() string {
	if x != nil {
		return x.VpcId
	}
	return ""
}

func (x *SNATRuleProperties) GetEipId() string {
	if x != nil {
		return x.EipId
	}
	return ""
}

var File_network_eip_v1_snatRule_proto protoreflect.FileDescriptor

var file_network_eip_v1_snatRule_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1d, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64,
	0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61,
	0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76,
	0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x22, 0x91, 0x02, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x69,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x69,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xc6, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74, 0x53, 0x4e, 0x41,
	0x54, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x24, 0x0a,
	0x0e, 0x73, 0x6e, 0x61, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x73, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xbb,
	0x02, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x50, 0x0a, 0x0e, 0x73, 0x6e, 0x61,
	0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x2a, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d,
	0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36,
	0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0c, 0x73,
	0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x44, 0x0a, 0x09, 0x73,
	0x6e, 0x61, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x27,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53,
	0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x73, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xc9, 0x01, 0x0a,
	0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x73, 0x6e, 0x61, 0x74, 0x5f,
	0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x73, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x65, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa6, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73,
	0x74, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x12, 0x46, 0x0a, 0x0a, 0x73, 0x6e, 0x61, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73,
	0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x27, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x09, 0x73, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65,
	0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a,
	0x65, 0x22, 0xf5, 0x07, 0x0a, 0x08, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x73, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x50, 0xfa, 0x42, 0x4d, 0x72, 0x4b, 0x18,
	0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78,
	0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d,
	0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65,
	0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b,
	0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x43, 0x0a, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4e, 0x41, 0x54, 0x52,
	0x75, 0x6c, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x15, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x45, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65,
	0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x54,
	0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x51,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x31, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2e, 0x63, 0x6f, 0x6d,
	0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65,
	0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f,
	0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d,
	0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69,
	0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a,
	0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03,
	0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14,
	0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7f, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12,
	0x0b, 0x0a, 0x07, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0b, 0x0a, 0x07,
	0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x55, 0x4e, 0x42,
	0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45,
	0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x06,
	0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0a, 0x0a,
	0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x08, 0x22, 0x42, 0x0a, 0x12, 0x53, 0x4e, 0x41,
	0x54, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12,
	0x15, 0x0a, 0x06, 0x76, 0x70, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x70, 0x63, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x69, 0x70, 0x5f, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x69, 0x70, 0x49, 0x64, 0x32, 0xc7, 0x0c,
	0x0a, 0x09, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x87, 0x03, 0x0a, 0x0d,
	0x4c, 0x69, 0x73, 0x74, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x33, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x34, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8a, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x85, 0x01, 0x12, 0x82, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69,
	0x70, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69,
	0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x73, 0x6e,
	0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x76, 0x0a,
	0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65,
	0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65,
	0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0b, 0x65, 0x69, 0x70, 0x2e, 0x65, 0x69,
	0x70, 0x2e, 0x67, 0x65, 0x74, 0x12, 0x87, 0x03, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x53, 0x4e, 0x41,
	0x54, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x31, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65,
	0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c,
	0x65, 0x22, 0x9b, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x96, 0x01, 0x12, 0x93, 0x01, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f,
	0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x73, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x2f, 0x7b, 0x73, 0x6e, 0x61, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x76, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f,
	0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e,
	0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x12, 0x0b, 0x65, 0x69, 0x70, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x67, 0x65, 0x74, 0x12,
	0x9f, 0x03, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75,
	0x6c, 0x65, 0x12, 0x34, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c,
	0x65, 0x22, 0xad, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xa1, 0x01, 0x22, 0x93, 0x01, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f,
	0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x73, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x2f, 0x7b, 0x73, 0x6e, 0x61, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x3a, 0x09, 0x73, 0x6e, 0x61, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x88, 0xb5, 0x18, 0x01,
	0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x12, 0x0e, 0x65, 0x69, 0x70, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x83, 0x03, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x4e, 0x41, 0x54,
	0x52, 0x75, 0x6c, 0x65, 0x12, 0x34, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x4e, 0x41, 0x54, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0xa2, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x96, 0x01, 0x2a, 0x93, 0x01, 0x2f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x64, 0x61, 0x74, 0x61,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73,
	0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x73, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x2f, 0x7b, 0x73, 0x6e, 0x61, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67,
	0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73,
	0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x65, 0x69, 0x70, 0x2e, 0x65, 0x69, 0x70,
	0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x50, 0x5a, 0x4e, 0x67, 0x69, 0x74, 0x6c, 0x61,
	0x62, 0x2e, 0x62, 0x6a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x6d, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x72, 0x79, 0x2f, 0x62, 0x6f,
	0x73, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64,
	0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65,
	0x69, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x69, 0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x33,
}

var (
	file_network_eip_v1_snatRule_proto_rawDescOnce sync.Once
	file_network_eip_v1_snatRule_proto_rawDescData = file_network_eip_v1_snatRule_proto_rawDesc
)

func file_network_eip_v1_snatRule_proto_rawDescGZIP() []byte {
	file_network_eip_v1_snatRule_proto_rawDescOnce.Do(func() {
		file_network_eip_v1_snatRule_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_eip_v1_snatRule_proto_rawDescData)
	})
	return file_network_eip_v1_snatRule_proto_rawDescData
}

var file_network_eip_v1_snatRule_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_network_eip_v1_snatRule_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_network_eip_v1_snatRule_proto_goTypes = []interface{}{
	(SNATRule_State)(0),           // 0: sensetime.core.network.eip.v1.SNATRule.State
	(*ListSNATRulesRequest)(nil),  // 1: sensetime.core.network.eip.v1.ListSNATRulesRequest
	(*GetSNATRuleRequest)(nil),    // 2: sensetime.core.network.eip.v1.GetSNATRuleRequest
	(*CreateSNATRuleRequest)(nil), // 3: sensetime.core.network.eip.v1.CreateSNATRuleRequest
	(*DeleteSNATRuleRequest)(nil), // 4: sensetime.core.network.eip.v1.DeleteSNATRuleRequest
	(*ListSNATRulesResponse)(nil), // 5: sensetime.core.network.eip.v1.ListSNATRulesResponse
	(*SNATRule)(nil),              // 6: sensetime.core.network.eip.v1.SNATRule
	(*SNATRuleProperties)(nil),    // 7: sensetime.core.network.eip.v1.SNATRuleProperties
	nil,                           // 8: sensetime.core.network.eip.v1.SNATRule.TagsEntry
	(*v1.OrderInfo)(nil),          // 9: sensetime.core.higgs.common.v1.OrderInfo
	(*timestamppb.Timestamp)(nil), // 10: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),         // 11: google.protobuf.Empty
}
var file_network_eip_v1_snatRule_proto_depIdxs = []int32{
	6,  // 0: sensetime.core.network.eip.v1.CreateSNATRuleRequest.snat_rule:type_name -> sensetime.core.network.eip.v1.SNATRule
	6,  // 1: sensetime.core.network.eip.v1.ListSNATRulesResponse.snat_rules:type_name -> sensetime.core.network.eip.v1.SNATRule
	0,  // 2: sensetime.core.network.eip.v1.SNATRule.state:type_name -> sensetime.core.network.eip.v1.SNATRule.State
	8,  // 3: sensetime.core.network.eip.v1.SNATRule.tags:type_name -> sensetime.core.network.eip.v1.SNATRule.TagsEntry
	7,  // 4: sensetime.core.network.eip.v1.SNATRule.properties:type_name -> sensetime.core.network.eip.v1.SNATRuleProperties
	9,  // 5: sensetime.core.network.eip.v1.SNATRule.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	10, // 6: sensetime.core.network.eip.v1.SNATRule.create_time:type_name -> google.protobuf.Timestamp
	10, // 7: sensetime.core.network.eip.v1.SNATRule.update_time:type_name -> google.protobuf.Timestamp
	1,  // 8: sensetime.core.network.eip.v1.SNATRules.ListSNATRules:input_type -> sensetime.core.network.eip.v1.ListSNATRulesRequest
	2,  // 9: sensetime.core.network.eip.v1.SNATRules.GetSNATRule:input_type -> sensetime.core.network.eip.v1.GetSNATRuleRequest
	3,  // 10: sensetime.core.network.eip.v1.SNATRules.CreateSNATRule:input_type -> sensetime.core.network.eip.v1.CreateSNATRuleRequest
	4,  // 11: sensetime.core.network.eip.v1.SNATRules.DeleteSNATRule:input_type -> sensetime.core.network.eip.v1.DeleteSNATRuleRequest
	5,  // 12: sensetime.core.network.eip.v1.SNATRules.ListSNATRules:output_type -> sensetime.core.network.eip.v1.ListSNATRulesResponse
	6,  // 13: sensetime.core.network.eip.v1.SNATRules.GetSNATRule:output_type -> sensetime.core.network.eip.v1.SNATRule
	6,  // 14: sensetime.core.network.eip.v1.SNATRules.CreateSNATRule:output_type -> sensetime.core.network.eip.v1.SNATRule
	11, // 15: sensetime.core.network.eip.v1.SNATRules.DeleteSNATRule:output_type -> google.protobuf.Empty
	12, // [12:16] is the sub-list for method output_type
	8,  // [8:12] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_network_eip_v1_snatRule_proto_init() }
func file_network_eip_v1_snatRule_proto_init() {
	if File_network_eip_v1_snatRule_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_network_eip_v1_snatRule_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSNATRulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_snatRule_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSNATRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_snatRule_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSNATRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_snatRule_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSNATRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_snatRule_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSNATRulesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_snatRule_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SNATRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_snatRule_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SNATRuleProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_eip_v1_snatRule_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_network_eip_v1_snatRule_proto_goTypes,
		DependencyIndexes: file_network_eip_v1_snatRule_proto_depIdxs,
		EnumInfos:         file_network_eip_v1_snatRule_proto_enumTypes,
		MessageInfos:      file_network_eip_v1_snatRule_proto_msgTypes,
	}.Build()
	File_network_eip_v1_snatRule_proto = out.File
	file_network_eip_v1_snatRule_proto_rawDesc = nil
	file_network_eip_v1_snatRule_proto_goTypes = nil
	file_network_eip_v1_snatRule_proto_depIdxs = nil
}
