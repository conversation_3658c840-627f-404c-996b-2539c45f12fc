// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/eip/v1/acl.proto

package eip

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/api/annotations"
	v1 "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// [zh] ACL状态类型
// [en] Represents the different states of an ACL.
type ACL_State int32

const (
	// [zh] 创建中
	// [en] The ACL resource is being created.
	ACL_CREATING ACL_State = 0
	// [zh] 更新中
	// [en] The ACL resource is being updated.
	ACL_UPDATING ACL_State = 1
	// [zh] 已激活
	// [en] The ACL resource has been active.
	ACL_ACTIVE ACL_State = 2
	// [zh] 删除中
	// [en] The ACL resource is being deleted.
	ACL_DELETING ACL_State = 3
	// [zh] 已删除
	// [en] The ACL resource has been deleted.
	ACL_DELETED ACL_State = 4
	// [zh] 失败
	// [en] The ACL resource has been failed.
	ACL_FAILED ACL_State = 5
)

// Enum value maps for ACL_State.
var (
	ACL_State_name = map[int32]string{
		0: "CREATING",
		1: "UPDATING",
		2: "ACTIVE",
		3: "DELETING",
		4: "DELETED",
		5: "FAILED",
	}
	ACL_State_value = map[string]int32{
		"CREATING": 0,
		"UPDATING": 1,
		"ACTIVE":   2,
		"DELETING": 3,
		"DELETED":  4,
		"FAILED":   5,
	}
)

func (x ACL_State) Enum() *ACL_State {
	p := new(ACL_State)
	*p = x
	return p
}

func (x ACL_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ACL_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_eip_v1_acl_proto_enumTypes[0].Descriptor()
}

func (ACL_State) Type() protoreflect.EnumType {
	return &file_network_eip_v1_acl_proto_enumTypes[0]
}

func (x ACL_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ACL_State.Descriptor instead.
func (ACL_State) EnumDescriptor() ([]byte, []int) {
	return file_network_eip_v1_acl_proto_rawDescGZIP(), []int{6, 0}
}

// [zh] ACL动作类型.
// [en] ACL action type.
type ACLProperties_ACLAction int32

const (
	// [zh] 拒绝规则
	// [en] Deny acl rule
	ACLProperties_DENY ACLProperties_ACLAction = 0
	// [zh] 允许规则
	// [en] Allow acl rule
	ACLProperties_ALLOW ACLProperties_ACLAction = 1
)

// Enum value maps for ACLProperties_ACLAction.
var (
	ACLProperties_ACLAction_name = map[int32]string{
		0: "DENY",
		1: "ALLOW",
	}
	ACLProperties_ACLAction_value = map[string]int32{
		"DENY":  0,
		"ALLOW": 1,
	}
)

func (x ACLProperties_ACLAction) Enum() *ACLProperties_ACLAction {
	p := new(ACLProperties_ACLAction)
	*p = x
	return p
}

func (x ACLProperties_ACLAction) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ACLProperties_ACLAction) Descriptor() protoreflect.EnumDescriptor {
	return file_network_eip_v1_acl_proto_enumTypes[1].Descriptor()
}

func (ACLProperties_ACLAction) Type() protoreflect.EnumType {
	return &file_network_eip_v1_acl_proto_enumTypes[1]
}

func (x ACLProperties_ACLAction) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ACLProperties_ACLAction.Descriptor instead.
func (ACLProperties_ACLAction) EnumDescriptor() ([]byte, []int) {
	return file_network_eip_v1_acl_proto_rawDescGZIP(), []int{7, 0}
}

// [zh] 列举 ACLs 的请求.
// [en] Request to list ACLs.
type ListACLsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订购
	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区
	// [en] Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 过滤条件
	// [en] List filter.
	Filter string `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// [zh] 排序字段
	// [en] Sort results.
	OrderBy string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// [zh] 分页大小
	// [en] The maximum number of items to return.
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// [zh] 分页标记
	// [en] The next_page_token value returned from a previous List request, if any.
	PageToken string `protobuf:"bytes,7,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// [zh] eip名称
	// [en] eip name
	EipName string `protobuf:"bytes,8,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *ListACLsRequest) Reset() {
	*x = ListACLsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_acl_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListACLsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListACLsRequest) ProtoMessage() {}

func (x *ListACLsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_acl_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListACLsRequest.ProtoReflect.Descriptor instead.
func (*ListACLsRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_acl_proto_rawDescGZIP(), []int{0}
}

func (x *ListACLsRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListACLsRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListACLsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListACLsRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListACLsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListACLsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListACLsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListACLsRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// [zh] 获取某一个 ACL 的请求.
// [en] Request to get an ACL.
type GetACLRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订购
	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区
	// [en] Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] ACL资源名称
	// [en] The ACL resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	AclName string `protobuf:"bytes,4,opt,name=acl_name,json=aclName,proto3" json:"acl_name,omitempty"`
	// [zh] eip名称
	// [en] eip name
	EipName string `protobuf:"bytes,5,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *GetACLRequest) Reset() {
	*x = GetACLRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_acl_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetACLRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetACLRequest) ProtoMessage() {}

func (x *GetACLRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_acl_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetACLRequest.ProtoReflect.Descriptor instead.
func (*GetACLRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_acl_proto_rawDescGZIP(), []int{1}
}

func (x *GetACLRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetACLRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetACLRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetACLRequest) GetAclName() string {
	if x != nil {
		return x.AclName
	}
	return ""
}

func (x *GetACLRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// [zh] 创建一个 ACL 的请求.
// [en] Request to create a ACL.
type CreateACLRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订购
	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区
	// [en] Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] ACL资源名称
	// [en] The ACL resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	AclName string `protobuf:"bytes,4,opt,name=acl_name,json=aclName,proto3" json:"acl_name,omitempty"`
	// [zh] ACL资源
	// [en] The ACL resource to create.
	Acl *ACL `protobuf:"bytes,5,opt,name=acl,proto3" json:"acl,omitempty"`
	// [zh] eip名称
	// [en] eip name
	EipName string `protobuf:"bytes,6,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *CreateACLRequest) Reset() {
	*x = CreateACLRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_acl_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateACLRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateACLRequest) ProtoMessage() {}

func (x *CreateACLRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_acl_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateACLRequest.ProtoReflect.Descriptor instead.
func (*CreateACLRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_acl_proto_rawDescGZIP(), []int{2}
}

func (x *CreateACLRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *CreateACLRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *CreateACLRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateACLRequest) GetAclName() string {
	if x != nil {
		return x.AclName
	}
	return ""
}

func (x *CreateACLRequest) GetAcl() *ACL {
	if x != nil {
		return x.Acl
	}
	return nil
}

func (x *CreateACLRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// 更新一个 ACL 的请求.
// [EN] Request to update an ACL.
type UpdateACLRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订购
	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区
	// [en] Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] ACL资源名称
	// [en] The ACL resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	AclName string `protobuf:"bytes,4,opt,name=acl_name,json=aclName,proto3" json:"acl_name,omitempty"`
	// [zh] ACL资源
	// [en] The ACL resource to create.
	Acl *ACL `protobuf:"bytes,5,opt,name=acl,proto3" json:"acl,omitempty"`
	// [zh] eip名称
	// [en] eip name
	EipName string `protobuf:"bytes,6,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *UpdateACLRequest) Reset() {
	*x = UpdateACLRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_acl_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateACLRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateACLRequest) ProtoMessage() {}

func (x *UpdateACLRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_acl_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateACLRequest.ProtoReflect.Descriptor instead.
func (*UpdateACLRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_acl_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateACLRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *UpdateACLRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *UpdateACLRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateACLRequest) GetAclName() string {
	if x != nil {
		return x.AclName
	}
	return ""
}

func (x *UpdateACLRequest) GetAcl() *ACL {
	if x != nil {
		return x.Acl
	}
	return nil
}

func (x *UpdateACLRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// 删除某一个 ACL 的请求.
// [EN] Request to delete an ACL.
type DeleteACLRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订购
	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区
	// [en] Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] ACL资源名称
	// [en] The ACL resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	AclName string `protobuf:"bytes,4,opt,name=acl_name,json=aclName,proto3" json:"acl_name,omitempty"`
	// [zh] eip名称
	// [en] eip name
	EipName string `protobuf:"bytes,6,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *DeleteACLRequest) Reset() {
	*x = DeleteACLRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_acl_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteACLRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteACLRequest) ProtoMessage() {}

func (x *DeleteACLRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_acl_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteACLRequest.ProtoReflect.Descriptor instead.
func (*DeleteACLRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_acl_proto_rawDescGZIP(), []int{4}
}

func (x *DeleteACLRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DeleteACLRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *DeleteACLRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteACLRequest) GetAclName() string {
	if x != nil {
		return x.AclName
	}
	return ""
}

func (x *DeleteACLRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// [zh] 列举 ACLs 的响应.
// [en] Response to list ACLs.
type ListACLsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] ACL 列表.
	// [en] ACL list.
	Acls []*ACL `protobuf:"bytes,1,rep,name=acls,proto3" json:"acls,omitempty"`
	// [zh] 下一个页面的 token，如果没有更多数据则为空.
	// [en] Token to retrieve the next page of results, or empty if there are no more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// [zh] 分页大小
	// [en] total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListACLsResponse) Reset() {
	*x = ListACLsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_acl_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListACLsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListACLsResponse) ProtoMessage() {}

func (x *ListACLsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_acl_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListACLsResponse.ProtoReflect.Descriptor instead.
func (*ListACLsResponse) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_acl_proto_rawDescGZIP(), []int{5}
}

func (x *ListACLsResponse) GetAcls() []*ACL {
	if x != nil {
		return x.Acls
	}
	return nil
}

func (x *ListACLsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListACLsResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// [zh] ACL 实例结构体.
// [en] ACL entity.
type ACL struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] ACL资源ID
	// [en] The ACL resource id using the form: `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/acls/{acl_name}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// [zh] ACL资源名称
	// [en] The ACL resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] ACL资源显示名称
	// [en] ContainerInstance resource display name
	DisplayName *string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3,oneof" json:"display_name,omitempty"`
	// [zh] 资源描述信息
	// [en] ContainerInstance resource description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// [zh] ACL资源唯一标识
	// [en] The ACL resource uuid.
	Uid string `protobuf:"bytes,5,opt,name=uid,proto3" json:"uid,omitempty"`
	// [zh] ACL资源类型
	// [en] The ACL resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// [zh] 创建者ID
	// [en] The id of the user who created the ACL resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// [zh] 拥有者ID
	// [en] The id of the user who owns the ACL resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// [zh] 租户ID
	// [en] Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// [zh] 可用区
	// [en] Available zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] ACL资源当前状态
	// [en] The current state of the ACL resource.
	State ACL_State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.eip.v1.ACL_State" json:"state,omitempty"`
	// [zh] sku标识
	// [en] Sku id.
	SkuId string `protobuf:"bytes,12,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// [zh] 标签
	// [en] Tags attached to the ACL resource.
	Tags map[string]string `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// [zh] ACL资源属性
	// [en] Properties of the ACL resource.
	Properties *ACLProperties `protobuf:"bytes,14,opt,name=properties,proto3" json:"properties,omitempty"`
	// [zh] 订单信息
	// [en] Payment information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,15,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	// [zh] ACL资源是否已删除
	// [en] Indicates whether the ACL resource is deleted or not.
	Deleted bool `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// [zh] 创建时间
	// [en] The time when the ACL resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// [zh] 更新时间
	// [en] The time when the ACL resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *ACL) Reset() {
	*x = ACL{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_acl_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ACL) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ACL) ProtoMessage() {}

func (x *ACL) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_acl_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ACL.ProtoReflect.Descriptor instead.
func (*ACL) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_acl_proto_rawDescGZIP(), []int{6}
}

func (x *ACL) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ACL) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ACL) GetDisplayName() string {
	if x != nil && x.DisplayName != nil {
		return *x.DisplayName
	}
	return ""
}

func (x *ACL) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ACL) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ACL) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *ACL) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *ACL) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *ACL) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *ACL) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ACL) GetState() ACL_State {
	if x != nil {
		return x.State
	}
	return ACL_CREATING
}

func (x *ACL) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *ACL) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *ACL) GetProperties() *ACLProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *ACL) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

func (x *ACL) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *ACL) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *ACL) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// [zh] 资源实际属性.
// [en] Real resource properties.
type ACLProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] ACL动作.
	// [en] ACL action.
	Action ACLProperties_ACLAction `protobuf:"varint,1,opt,name=action,proto3,enum=sensetime.core.network.eip.v1.ACLProperties_ACLAction" json:"action,omitempty"`
	// [zh] ACL源地址.
	// [en] ACL source addresses, support multi IPs,IP range or Subnet: ********,********,*********-*********,********/27.
	Src string `protobuf:"bytes,2,opt,name=src,proto3" json:"src,omitempty"`
	// [zh] ACL目的地址.
	// [en] ACL destination addresses, reserved now.
	Dest string `protobuf:"bytes,3,opt,name=dest,proto3" json:"dest,omitempty"`
	// [zh] ACL目的端口，目前保留.
	// [en] ACL destination port, reserved now.
	DestPort string `protobuf:"bytes,4,opt,name=dest_port,json=destPort,proto3" json:"dest_port,omitempty"`
	// [zh] 对外暴露的协议，目前保留.
	// [en] External protocol, reserved now.
	Protocol string `protobuf:"bytes,5,opt,name=protocol,proto3" json:"protocol,omitempty"`
	// [zh] ACL规则优先级，目前保留.
	// [en] ACL rule priority.
	Priority int32 `protobuf:"varint,6,opt,name=priority,proto3" json:"priority,omitempty"`
}

func (x *ACLProperties) Reset() {
	*x = ACLProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_acl_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ACLProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ACLProperties) ProtoMessage() {}

func (x *ACLProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_acl_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ACLProperties.ProtoReflect.Descriptor instead.
func (*ACLProperties) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_acl_proto_rawDescGZIP(), []int{7}
}

func (x *ACLProperties) GetAction() ACLProperties_ACLAction {
	if x != nil {
		return x.Action
	}
	return ACLProperties_DENY
}

func (x *ACLProperties) GetSrc() string {
	if x != nil {
		return x.Src
	}
	return ""
}

func (x *ACLProperties) GetDest() string {
	if x != nil {
		return x.Dest
	}
	return ""
}

func (x *ACLProperties) GetDestPort() string {
	if x != nil {
		return x.DestPort
	}
	return ""
}

func (x *ACLProperties) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *ACLProperties) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

var File_network_eip_v1_acl_proto protoreflect.FileDescriptor

var file_network_eip_v1_acl_proto_rawDesc = []byte{
	0x0a, 0x18, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x76, 0x31,
	0x2f, 0x61, 0x63, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b,
	0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x68, 0x69, 0x67,
	0x67, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8c, 0x02, 0x0a,
	0x0f, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x43, 0x4c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a,
	0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x42, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x12, 0x19, 0x0a, 0x08, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x65, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xb6, 0x01, 0x0a, 0x0d,
	0x47, 0x65, 0x74, 0x41, 0x43, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a,
	0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x61, 0x63, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x61, 0x63, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x69, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x69, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x22, 0x9b, 0x02, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41,
	0x43, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x61, 0x63,
	0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42,
	0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x61, 0x63, 0x6c, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x34, 0x0a, 0x03, 0x61, 0x63, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x41,
	0x43, 0x4c, 0x52, 0x03, 0x61, 0x63, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x69, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x69, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0x9b, 0x02, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x43, 0x4c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x61, 0x63, 0x6c, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42, 0x27, 0x72,
	0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x61, 0x63, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x34, 0x0a, 0x03, 0x61, 0x63, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x43, 0x4c,
	0x52, 0x03, 0x61, 0x63, 0x6c, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0xe5, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x43, 0x4c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x61, 0x63, 0x6c, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18,
	0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x61, 0x63, 0x6c, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x65, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x91, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73,
	0x74, 0x41, 0x43, 0x4c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a,
	0x04, 0x61, 0x63, 0x6c, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x43, 0x4c, 0x52,
	0x04, 0x61, 0x63, 0x6c, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d,
	0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a,
	0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xcb, 0x07, 0x0a,
	0x03, 0x41, 0x43, 0x4c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x75, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x4d,
	0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b,
	0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d,
	0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61,
	0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24, 0x48, 0x00, 0x52,
	0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12,
	0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03,
	0x75, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12,
	0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x41, 0x43, 0x4c, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x43, 0x4c, 0x2e, 0x54, 0x61, 0x67,
	0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x4c, 0x0a, 0x0a,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x41, 0x43, 0x4c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a,
	0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x68, 0x69, 0x67, 0x67, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e,
	0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18,
	0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x3b,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x56, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45,
	0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x03,
	0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0a, 0x0a,
	0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x69,
	0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0xfc, 0x01, 0x0a, 0x0d, 0x41,
	0x43, 0x4c, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x4e, 0x0a, 0x06,
	0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x36, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x43, 0x4c,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x41, 0x43, 0x4c, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x52, 0x06, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03,
	0x73, 0x72, 0x63, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x73, 0x72, 0x63, 0x12, 0x12,
	0x0a, 0x04, 0x64, 0x65, 0x73, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x64, 0x65,
	0x73, 0x74, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x65, 0x73, 0x74, 0x5f, 0x70, 0x6f, 0x72, 0x74, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x65, 0x73, 0x74, 0x50, 0x6f, 0x72, 0x74, 0x12,
	0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x1a, 0x0a, 0x08, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70,
	0x72, 0x69, 0x6f, 0x72, 0x69, 0x74, 0x79, 0x22, 0x20, 0x0a, 0x09, 0x41, 0x43, 0x4c, 0x41, 0x63,
	0x74, 0x69, 0x6f, 0x6e, 0x12, 0x08, 0x0a, 0x04, 0x44, 0x45, 0x4e, 0x59, 0x10, 0x00, 0x12, 0x09,
	0x0a, 0x05, 0x41, 0x4c, 0x4c, 0x4f, 0x57, 0x10, 0x01, 0x32, 0xdf, 0x0e, 0x0a, 0x04, 0x41, 0x43,
	0x4c, 0x73, 0x12, 0xf1, 0x02, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x41, 0x43, 0x4c, 0x73, 0x12,
	0x2e, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x43, 0x4c, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x41, 0x43, 0x4c, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x22, 0x83, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7f, 0x12, 0x7d, 0x2f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f,
	0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x61, 0x63, 0x6c, 0x73, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x76,
	0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e,
	0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b,
	0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0b, 0x65, 0x69, 0x70, 0x2e, 0x65,
	0x69, 0x70, 0x2e, 0x67, 0x65, 0x74, 0x12, 0xed, 0x02, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x41, 0x43,
	0x4c, 0x12, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x41, 0x43, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x41, 0x43, 0x4c, 0x22, 0x90, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x8b, 0x01, 0x12, 0x88, 0x01,
	0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x64, 0x61, 0x74,
	0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65,
	0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65,
	0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x61, 0x63, 0x6c, 0x73, 0x2f, 0x7b, 0x61,
	0x63, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x76,
	0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e,
	0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b,
	0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0b, 0x65, 0x69, 0x70, 0x2e, 0x65,
	0x69, 0x70, 0x2e, 0x67, 0x65, 0x74, 0x12, 0xff, 0x02, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x41, 0x43, 0x4c, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x41, 0x43, 0x4c, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65,
	0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x43, 0x4c, 0x22, 0x9c, 0x02, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x90, 0x01, 0x22, 0x88, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65,
	0x69, 0x70, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65,
	0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x61,
	0x63, 0x6c, 0x73, 0x2f, 0x7b, 0x61, 0x63, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x03,
	0x61, 0x63, 0x6c, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a,
	0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65,
	0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65,
	0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x65, 0x69, 0x70, 0x2e, 0x65, 0x69,
	0x70, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xff, 0x02, 0x0a, 0x09, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x41, 0x43, 0x4c, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x41, 0x43, 0x4c,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x43, 0x4c, 0x22, 0x9c, 0x02, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x90, 0x01, 0x32, 0x88, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2f, 0x65, 0x69, 0x70, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d,
	0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x61, 0x63, 0x6c, 0x73, 0x2f, 0x7b, 0x61, 0x63, 0x6c, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x3a, 0x03, 0x61, 0x63, 0x6c, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18,
	0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f,
	0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f,
	0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x65, 0x69, 0x70, 0x2e,
	0x65, 0x69, 0x70, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xee, 0x02, 0x0a, 0x09, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x41, 0x43, 0x4c, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x41,
	0x43, 0x4c, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x97, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x8b, 0x01, 0x2a, 0x88, 0x01, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f,
	0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x61, 0x63, 0x6c, 0x73, 0x2f, 0x7b, 0x61, 0x63, 0x6c,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5,
	0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a,
	0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73,
	0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x65, 0x69, 0x70,
	0x2e, 0x65, 0x69, 0x70, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x50, 0x5a, 0x4e, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x62, 0x6a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x72,
	0x79, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2d, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x69, 0x70, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_network_eip_v1_acl_proto_rawDescOnce sync.Once
	file_network_eip_v1_acl_proto_rawDescData = file_network_eip_v1_acl_proto_rawDesc
)

func file_network_eip_v1_acl_proto_rawDescGZIP() []byte {
	file_network_eip_v1_acl_proto_rawDescOnce.Do(func() {
		file_network_eip_v1_acl_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_eip_v1_acl_proto_rawDescData)
	})
	return file_network_eip_v1_acl_proto_rawDescData
}

var file_network_eip_v1_acl_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_network_eip_v1_acl_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_network_eip_v1_acl_proto_goTypes = []interface{}{
	(ACL_State)(0),                // 0: sensetime.core.network.eip.v1.ACL.State
	(ACLProperties_ACLAction)(0),  // 1: sensetime.core.network.eip.v1.ACLProperties.ACLAction
	(*ListACLsRequest)(nil),       // 2: sensetime.core.network.eip.v1.ListACLsRequest
	(*GetACLRequest)(nil),         // 3: sensetime.core.network.eip.v1.GetACLRequest
	(*CreateACLRequest)(nil),      // 4: sensetime.core.network.eip.v1.CreateACLRequest
	(*UpdateACLRequest)(nil),      // 5: sensetime.core.network.eip.v1.UpdateACLRequest
	(*DeleteACLRequest)(nil),      // 6: sensetime.core.network.eip.v1.DeleteACLRequest
	(*ListACLsResponse)(nil),      // 7: sensetime.core.network.eip.v1.ListACLsResponse
	(*ACL)(nil),                   // 8: sensetime.core.network.eip.v1.ACL
	(*ACLProperties)(nil),         // 9: sensetime.core.network.eip.v1.ACLProperties
	nil,                           // 10: sensetime.core.network.eip.v1.ACL.TagsEntry
	(*v1.OrderInfo)(nil),          // 11: sensetime.core.higgs.common.v1.OrderInfo
	(*timestamppb.Timestamp)(nil), // 12: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),         // 13: google.protobuf.Empty
}
var file_network_eip_v1_acl_proto_depIdxs = []int32{
	8,  // 0: sensetime.core.network.eip.v1.CreateACLRequest.acl:type_name -> sensetime.core.network.eip.v1.ACL
	8,  // 1: sensetime.core.network.eip.v1.UpdateACLRequest.acl:type_name -> sensetime.core.network.eip.v1.ACL
	8,  // 2: sensetime.core.network.eip.v1.ListACLsResponse.acls:type_name -> sensetime.core.network.eip.v1.ACL
	0,  // 3: sensetime.core.network.eip.v1.ACL.state:type_name -> sensetime.core.network.eip.v1.ACL.State
	10, // 4: sensetime.core.network.eip.v1.ACL.tags:type_name -> sensetime.core.network.eip.v1.ACL.TagsEntry
	9,  // 5: sensetime.core.network.eip.v1.ACL.properties:type_name -> sensetime.core.network.eip.v1.ACLProperties
	11, // 6: sensetime.core.network.eip.v1.ACL.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	12, // 7: sensetime.core.network.eip.v1.ACL.create_time:type_name -> google.protobuf.Timestamp
	12, // 8: sensetime.core.network.eip.v1.ACL.update_time:type_name -> google.protobuf.Timestamp
	1,  // 9: sensetime.core.network.eip.v1.ACLProperties.action:type_name -> sensetime.core.network.eip.v1.ACLProperties.ACLAction
	2,  // 10: sensetime.core.network.eip.v1.ACLs.ListACLs:input_type -> sensetime.core.network.eip.v1.ListACLsRequest
	3,  // 11: sensetime.core.network.eip.v1.ACLs.GetACL:input_type -> sensetime.core.network.eip.v1.GetACLRequest
	4,  // 12: sensetime.core.network.eip.v1.ACLs.CreateACL:input_type -> sensetime.core.network.eip.v1.CreateACLRequest
	5,  // 13: sensetime.core.network.eip.v1.ACLs.UpdateACL:input_type -> sensetime.core.network.eip.v1.UpdateACLRequest
	6,  // 14: sensetime.core.network.eip.v1.ACLs.DeleteACL:input_type -> sensetime.core.network.eip.v1.DeleteACLRequest
	7,  // 15: sensetime.core.network.eip.v1.ACLs.ListACLs:output_type -> sensetime.core.network.eip.v1.ListACLsResponse
	8,  // 16: sensetime.core.network.eip.v1.ACLs.GetACL:output_type -> sensetime.core.network.eip.v1.ACL
	8,  // 17: sensetime.core.network.eip.v1.ACLs.CreateACL:output_type -> sensetime.core.network.eip.v1.ACL
	8,  // 18: sensetime.core.network.eip.v1.ACLs.UpdateACL:output_type -> sensetime.core.network.eip.v1.ACL
	13, // 19: sensetime.core.network.eip.v1.ACLs.DeleteACL:output_type -> google.protobuf.Empty
	15, // [15:20] is the sub-list for method output_type
	10, // [10:15] is the sub-list for method input_type
	10, // [10:10] is the sub-list for extension type_name
	10, // [10:10] is the sub-list for extension extendee
	0,  // [0:10] is the sub-list for field type_name
}

func init() { file_network_eip_v1_acl_proto_init() }
func file_network_eip_v1_acl_proto_init() {
	if File_network_eip_v1_acl_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_network_eip_v1_acl_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListACLsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_acl_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetACLRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_acl_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateACLRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_acl_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateACLRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_acl_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteACLRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_acl_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListACLsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_acl_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ACL); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_acl_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ACLProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_network_eip_v1_acl_proto_msgTypes[6].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_eip_v1_acl_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_network_eip_v1_acl_proto_goTypes,
		DependencyIndexes: file_network_eip_v1_acl_proto_depIdxs,
		EnumInfos:         file_network_eip_v1_acl_proto_enumTypes,
		MessageInfos:      file_network_eip_v1_acl_proto_msgTypes,
	}.Build()
	File_network_eip_v1_acl_proto = out.File
	file_network_eip_v1_acl_proto_rawDesc = nil
	file_network_eip_v1_acl_proto_goTypes = nil
	file_network_eip_v1_acl_proto_depIdxs = nil
}
