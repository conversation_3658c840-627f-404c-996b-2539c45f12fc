// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/eip/v1/eip.proto

package eip

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/api/annotations"
	v1 "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// [zh] EIP状态枚举
// [en] Represents the different states of a EIP.
type EIP_State int32

const (
	// [zh] EIP创建中
	// [en] The EIP resource is being created.
	EIP_CREATING EIP_State = 0
	// [zh] EIP更新中
	// [en] The EIP resource is being updated.
	EIP_UPDATING EIP_State = 1
	// [zh] EIP active
	// [en] The EIP resource has been active.
	EIP_ACTIVE EIP_State = 2
	// [zh] EIP删除中
	// [en] The EIP resource is being deleting.
	EIP_DELETING EIP_State = 3
	// [zh] EIP已删除
	// [en] The EIP resource has been deleted.
	EIP_DELETED EIP_State = 4
	// [zh] EIP相关操作失败
	// [en] The EIP resource has been failed.
	EIP_FAILED EIP_State = 5
	// [zh] EIP挂起中
	// [en] The EIP resource is being suspending.
	EIP_SUSPENDING EIP_State = 6
	// [zh] EIP已挂起
	// [en] The EIP resource has been suspended.
	EIP_SUSPENDED EIP_State = 7
	// [zh] EIP恢复中
	// [en] The EIP resource is being resuming.
	EIP_RESUMING EIP_State = 8
	// [zh] EIP已过期，停止中
	// [en] The EIP resource is being expireStopping.
	EIP_EXPIRESTOPPING EIP_State = 9
	// [zh] EIP已过期，停止服务
	// [en] The EIP resource has been expireStopped.
	EIP_EXPIRESTOPPED EIP_State = 10
	// [zh] EIP重新恢复服务
	// [en] The EIP resource is being renewStarting.
	EIP_RENEWSTARTING EIP_State = 11
)

// Enum value maps for EIP_State.
var (
	EIP_State_name = map[int32]string{
		0:  "CREATING",
		1:  "UPDATING",
		2:  "ACTIVE",
		3:  "DELETING",
		4:  "DELETED",
		5:  "FAILED",
		6:  "SUSPENDING",
		7:  "SUSPENDED",
		8:  "RESUMING",
		9:  "EXPIRESTOPPING",
		10: "EXPIRESTOPPED",
		11: "RENEWSTARTING",
	}
	EIP_State_value = map[string]int32{
		"CREATING":       0,
		"UPDATING":       1,
		"ACTIVE":         2,
		"DELETING":       3,
		"DELETED":        4,
		"FAILED":         5,
		"SUSPENDING":     6,
		"SUSPENDED":      7,
		"RESUMING":       8,
		"EXPIRESTOPPING": 9,
		"EXPIRESTOPPED":  10,
		"RENEWSTARTING":  11,
	}
)

func (x EIP_State) Enum() *EIP_State {
	p := new(EIP_State)
	*p = x
	return p
}

func (x EIP_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EIP_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_eip_v1_eip_proto_enumTypes[0].Descriptor()
}

func (EIP_State) Type() protoreflect.EnumType {
	return &file_network_eip_v1_eip_proto_enumTypes[0]
}

func (x EIP_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EIP_State.Descriptor instead.
func (EIP_State) EnumDescriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{19, 0}
}

// [zh] EIP关联类型
// [en] EIP association type.
type EIPProperties_AType int32

const (
	// [zh] EIP支持网关
	// [en] The EIP resource associate NAT GateWay
	EIPProperties_NATGW EIPProperties_AType = 0
	// [zh] EIP支持POD
	// [en] The EIP resource associate POD.
	EIPProperties_POD EIPProperties_AType = 1
	// [zh] EIP支持SLB
	// [en] The EIP resource associate SLB.
	EIPProperties_SLB EIPProperties_AType = 2
	// [zh] EIP支持裸金属
	// [en] The EIP resource associate SLB.
	EIPProperties_BM EIPProperties_AType = 3
	// [zh] EIP支持网关和裸金属
	// [en] The EIP support NATGW and BM at same time, 1030 default.
	EIPProperties_NATGW_AND_BM EIPProperties_AType = 4
)

// Enum value maps for EIPProperties_AType.
var (
	EIPProperties_AType_name = map[int32]string{
		0: "NATGW",
		1: "POD",
		2: "SLB",
		3: "BM",
		4: "NATGW_AND_BM",
	}
	EIPProperties_AType_value = map[string]int32{
		"NATGW":        0,
		"POD":          1,
		"SLB":          2,
		"BM":           3,
		"NATGW_AND_BM": 4,
	}
)

func (x EIPProperties_AType) Enum() *EIPProperties_AType {
	p := new(EIPProperties_AType)
	*p = x
	return p
}

func (x EIPProperties_AType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (EIPProperties_AType) Descriptor() protoreflect.EnumDescriptor {
	return file_network_eip_v1_eip_proto_enumTypes[1].Descriptor()
}

func (EIPProperties_AType) Type() protoreflect.EnumType {
	return &file_network_eip_v1_eip_proto_enumTypes[1]
}

func (x EIPProperties_AType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use EIPProperties_AType.Descriptor instead.
func (EIPProperties_AType) EnumDescriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{20, 0}
}

// [zh] 列举 EIPs 的请求.
// [en] Request to list EIPs.
type ListEIPsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅
	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区
	// [en] Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] eip过滤条件
	// [en] List filter.
	Filter string `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// [zh] eip资源排序规则
	// [en] Sort resoults.
	OrderBy string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// [zh] 分页大小
	// [en] The maximum number of items to return.
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// [zh] 从上一个List请求返回的next_page_token值(如果有的话)
	// [en] The next_page_token value returned from a previous List request, if any.
	PageToken string `protobuf:"bytes,7,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// [zh] 租户id
	// [en] tenant_id.
	TenantId string `protobuf:"bytes,8,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
}

func (x *ListEIPsRequest) Reset() {
	*x = ListEIPsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEIPsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEIPsRequest) ProtoMessage() {}

func (x *ListEIPsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEIPsRequest.ProtoReflect.Descriptor instead.
func (*ListEIPsRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{0}
}

func (x *ListEIPsRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListEIPsRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListEIPsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListEIPsRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListEIPsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListEIPsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListEIPsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListEIPsRequest) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

// [zh] 获取某一个 EIP 的请求.
// [en] Request to get a EIP.
type GetEIPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅
	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区
	// [en] Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] eip名
	// [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	EipName string `protobuf:"bytes,4,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *GetEIPRequest) Reset() {
	*x = GetEIPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEIPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEIPRequest) ProtoMessage() {}

func (x *GetEIPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEIPRequest.ProtoReflect.Descriptor instead.
func (*GetEIPRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{1}
}

func (x *GetEIPRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetEIPRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetEIPRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetEIPRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// [zh] 获取 EIP 状态信息.
// [en] Request to get a EIP status.
type GetEIPStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅
	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区
	// [en] Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] eip名
	// [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	EipName string `protobuf:"bytes,4,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *GetEIPStatusRequest) Reset() {
	*x = GetEIPStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEIPStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEIPStatusRequest) ProtoMessage() {}

func (x *GetEIPStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEIPStatusRequest.ProtoReflect.Descriptor instead.
func (*GetEIPStatusRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{2}
}

func (x *GetEIPStatusRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetEIPStatusRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetEIPStatusRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetEIPStatusRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// [en] CreateEIPRequest.
type CreateEIPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅
	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区
	// [en] Available zone, todo: add validation
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] eip名
	// [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	EipName string `protobuf:"bytes,4,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
	// [zh] eip实例
	// [en] The EIP resource to create.
	Eip *EIP `protobuf:"bytes,5,opt,name=eip,proto3" json:"eip,omitempty"`
}

func (x *CreateEIPRequest) Reset() {
	*x = CreateEIPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateEIPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateEIPRequest) ProtoMessage() {}

func (x *CreateEIPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateEIPRequest.ProtoReflect.Descriptor instead.
func (*CreateEIPRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{3}
}

func (x *CreateEIPRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *CreateEIPRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *CreateEIPRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateEIPRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

func (x *CreateEIPRequest) GetEip() *EIP {
	if x != nil {
		return x.Eip
	}
	return nil
}

// [zh] EIP 可编辑字段.
// [en] EIP editable properties.
type UpdateEIPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅
	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区
	// [en] Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] eip名
	// [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	EipName string `protobuf:"bytes,4,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
	// [zh] eip实例
	// [en] The EIP resource to update.
	Eip *EIP `protobuf:"bytes,5,opt,name=eip,proto3" json:"eip,omitempty"`
	// [zh] 要更新的eip字段掩码
	// [en] update_mask
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,6,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateEIPRequest) Reset() {
	*x = UpdateEIPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEIPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEIPRequest) ProtoMessage() {}

func (x *UpdateEIPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEIPRequest.ProtoReflect.Descriptor instead.
func (*UpdateEIPRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateEIPRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *UpdateEIPRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *UpdateEIPRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateEIPRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

func (x *UpdateEIPRequest) GetEip() *EIP {
	if x != nil {
		return x.Eip
	}
	return nil
}

func (x *UpdateEIPRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// [zh] 过期停服EIP请求
// [en] ExpireStopEIPRequest
type ExpireStopEIPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅
	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区
	// [en] Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] eip名
	// [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	EipName string `protobuf:"bytes,4,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *ExpireStopEIPRequest) Reset() {
	*x = ExpireStopEIPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ExpireStopEIPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ExpireStopEIPRequest) ProtoMessage() {}

func (x *ExpireStopEIPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ExpireStopEIPRequest.ProtoReflect.Descriptor instead.
func (*ExpireStopEIPRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{5}
}

func (x *ExpireStopEIPRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ExpireStopEIPRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ExpireStopEIPRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ExpireStopEIPRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// [en] RenewStartEIPRequest
type RenewStartEIPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅
	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区
	// [en] Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] eip名
	// [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	EipName string `protobuf:"bytes,4,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *RenewStartEIPRequest) Reset() {
	*x = RenewStartEIPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *RenewStartEIPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*RenewStartEIPRequest) ProtoMessage() {}

func (x *RenewStartEIPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use RenewStartEIPRequest.ProtoReflect.Descriptor instead.
func (*RenewStartEIPRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{6}
}

func (x *RenewStartEIPRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *RenewStartEIPRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *RenewStartEIPRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *RenewStartEIPRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// [zh] 释放EIP请求
// [en] ReleaseEIPRequest
type ReleaseEIPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅
	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区
	// [en] Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] eip名
	// [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	EipName string `protobuf:"bytes,4,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *ReleaseEIPRequest) Reset() {
	*x = ReleaseEIPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleaseEIPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseEIPRequest) ProtoMessage() {}

func (x *ReleaseEIPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseEIPRequest.ProtoReflect.Descriptor instead.
func (*ReleaseEIPRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{7}
}

func (x *ReleaseEIPRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ReleaseEIPRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ReleaseEIPRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ReleaseEIPRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// [en] ForceDeleteEIPRequest
type ForceDeleteEIPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [en] Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	EipName string `protobuf:"bytes,4,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *ForceDeleteEIPRequest) Reset() {
	*x = ForceDeleteEIPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForceDeleteEIPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForceDeleteEIPRequest) ProtoMessage() {}

func (x *ForceDeleteEIPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForceDeleteEIPRequest.ProtoReflect.Descriptor instead.
func (*ForceDeleteEIPRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{8}
}

func (x *ForceDeleteEIPRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ForceDeleteEIPRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ForceDeleteEIPRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ForceDeleteEIPRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// [zh] 删除某一个 EIP 的请求.
// [en] Request to delete a EIP.
type DeleteEIPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅
	// [en] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组
	// [en] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区
	// [en] Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] eip名
	// [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	EipName string `protobuf:"bytes,4,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
}

func (x *DeleteEIPRequest) Reset() {
	*x = DeleteEIPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteEIPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteEIPRequest) ProtoMessage() {}

func (x *DeleteEIPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteEIPRequest.ProtoReflect.Descriptor instead.
func (*DeleteEIPRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{9}
}

func (x *DeleteEIPRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DeleteEIPRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *DeleteEIPRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteEIPRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

// [zh] EIP 扩容请求.
// [en] Resize EIP request.
type ResizeEIPRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] EIP 名称，需要严格满足正则 `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`
	// [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	EipName string `protobuf:"bytes,4,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
	// [zh] 变更 EIP 资源.
	// [en] Resize EIP resource.
	EipResize *EIPResize `protobuf:"bytes,5,opt,name=eip_resize,json=eipResize,proto3" json:"eip_resize,omitempty"`
}

func (x *ResizeEIPRequest) Reset() {
	*x = ResizeEIPRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResizeEIPRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResizeEIPRequest) ProtoMessage() {}

func (x *ResizeEIPRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResizeEIPRequest.ProtoReflect.Descriptor instead.
func (*ResizeEIPRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{10}
}

func (x *ResizeEIPRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ResizeEIPRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ResizeEIPRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ResizeEIPRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

func (x *ResizeEIPRequest) GetEipResize() *EIPResize {
	if x != nil {
		return x.EipResize
	}
	return nil
}

// [zh] 变更 EIP 资源.
// [en] Resize EIP body.
type EIPResize struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] EIP 资源uuid.
	// [en] EIP resource uuid.
	ResourceId string `protobuf:"bytes,1,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`
	// [zh] 最小库存单元id.
	// [en] EIP resource sku_id.
	SkuId string `protobuf:"bytes,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// [zh] 变更操作者id.
	// [en] Operator id.
	OperatorId string `protobuf:"bytes,3,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// [zh] EIP 属性.
	// [en] EIP properties.
	Properties *EIPProperties `protobuf:"bytes,4,opt,name=properties,proto3" json:"properties,omitempty"` // [(validate.rules).message.required = true];
	// [zh] 订单信息.
	// [en] Order information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,5,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"` // [(validate.rules).message.required = true];
}

func (x *EIPResize) Reset() {
	*x = EIPResize{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EIPResize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EIPResize) ProtoMessage() {}

func (x *EIPResize) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EIPResize.ProtoReflect.Descriptor instead.
func (*EIPResize) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{11}
}

func (x *EIPResize) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *EIPResize) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *EIPResize) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

func (x *EIPResize) GetProperties() *EIPProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *EIPResize) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

// [zh] snat status 信息
// [en] snat status info
type SnatStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] snat 名字
	// [en] snat name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] snat 源地址cidr
	// [en] snat inner cidr
	InnerIp string `protobuf:"bytes,2,opt,name=inner_ip,json=innerIp,proto3" json:"inner_ip,omitempty"`
	// [zh] snat 目的cidr
	// [en] snat outer cidr
	OuterIp string `protobuf:"bytes,3,opt,name=outer_ip,json=outerIp,proto3" json:"outer_ip,omitempty"`
	// [zh] snat policy策略, src/dst/all
	// [en] snat policy
	Policy string `protobuf:"bytes,4,opt,name=policy,proto3" json:"policy,omitempty"`
	// [zh] snat policy值
	// [en] snat policy value
	PolicyValue string `protobuf:"bytes,5,opt,name=policy_value,json=policyValue,proto3" json:"policy_value,omitempty"`
}

func (x *SnatStatus) Reset() {
	*x = SnatStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SnatStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SnatStatus) ProtoMessage() {}

func (x *SnatStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SnatStatus.ProtoReflect.Descriptor instead.
func (*SnatStatus) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{12}
}

func (x *SnatStatus) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SnatStatus) GetInnerIp() string {
	if x != nil {
		return x.InnerIp
	}
	return ""
}

func (x *SnatStatus) GetOuterIp() string {
	if x != nil {
		return x.OuterIp
	}
	return ""
}

func (x *SnatStatus) GetPolicy() string {
	if x != nil {
		return x.Policy
	}
	return ""
}

func (x *SnatStatus) GetPolicyValue() string {
	if x != nil {
		return x.PolicyValue
	}
	return ""
}

// 获取 EIP 属性的响应.
// [en] Response to get EIP status.
type EIPStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// EIP 信息.
	// [en] EIP info.
	EipInfo *EIP `protobuf:"bytes,1,opt,name=eip_info,json=eipInfo,proto3" json:"eip_info,omitempty"`
	// Resource ip
	EipIp string `protobuf:"bytes,2,opt,name=eip_ip,json=eipIp,proto3" json:"eip_ip,omitempty"`
	// 加载的vpc信息.
	// [en] vpc info.
	VpcInfo *VPCStatus `protobuf:"bytes,3,opt,name=vpc_info,json=vpcInfo,proto3" json:"vpc_info,omitempty"`
	// snat 规则数量
	// [en] snat count.
	SnatCount int32 `protobuf:"varint,4,opt,name=snat_count,json=snatCount,proto3" json:"snat_count,omitempty"`
	// dnat 规则数量
	// [en] dnat count.
	DnatCount int32 `protobuf:"varint,5,opt,name=dnat_count,json=dnatCount,proto3" json:"dnat_count,omitempty"`
	// [zh] snat规则信息
	// [en] snat status info
	SnatInfo []*SnatStatus `protobuf:"bytes,6,rep,name=snat_info,json=snatInfo,proto3" json:"snat_info,omitempty"`
}

func (x *EIPStatus) Reset() {
	*x = EIPStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EIPStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EIPStatus) ProtoMessage() {}

func (x *EIPStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EIPStatus.ProtoReflect.Descriptor instead.
func (*EIPStatus) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{13}
}

func (x *EIPStatus) GetEipInfo() *EIP {
	if x != nil {
		return x.EipInfo
	}
	return nil
}

func (x *EIPStatus) GetEipIp() string {
	if x != nil {
		return x.EipIp
	}
	return ""
}

func (x *EIPStatus) GetVpcInfo() *VPCStatus {
	if x != nil {
		return x.VpcInfo
	}
	return nil
}

func (x *EIPStatus) GetSnatCount() int32 {
	if x != nil {
		return x.SnatCount
	}
	return 0
}

func (x *EIPStatus) GetDnatCount() int32 {
	if x != nil {
		return x.DnatCount
	}
	return 0
}

func (x *EIPStatus) GetSnatInfo() []*SnatStatus {
	if x != nil {
		return x.SnatInfo
	}
	return nil
}

// EIP 相关 metrics.
// [en] EIP metrics.
type EIPMetrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// resource_metrics.
	ResourceMetrics []*ResourceMetrics `protobuf:"bytes,1,rep,name=resource_metrics,json=resourceMetrics,proto3" json:"resource_metrics,omitempty"`
}

func (x *EIPMetrics) Reset() {
	*x = EIPMetrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EIPMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EIPMetrics) ProtoMessage() {}

func (x *EIPMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EIPMetrics.ProtoReflect.Descriptor instead.
func (*EIPMetrics) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{14}
}

func (x *EIPMetrics) GetResourceMetrics() []*ResourceMetrics {
	if x != nil {
		return x.ResourceMetrics
	}
	return nil
}

// ResourceMetrics
type ResourceMetrics struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// value.
	Value int32 `protobuf:"varint,2,opt,name=value,proto3" json:"value,omitempty"`
	// unit.
	Unit string `protobuf:"bytes,3,opt,name=unit,proto3" json:"unit,omitempty"`
	// color.
	Color string `protobuf:"bytes,4,opt,name=color,proto3" json:"color,omitempty"`
}

func (x *ResourceMetrics) Reset() {
	*x = ResourceMetrics{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResourceMetrics) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResourceMetrics) ProtoMessage() {}

func (x *ResourceMetrics) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResourceMetrics.ProtoReflect.Descriptor instead.
func (*ResourceMetrics) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{15}
}

func (x *ResourceMetrics) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ResourceMetrics) GetValue() int32 {
	if x != nil {
		return x.Value
	}
	return 0
}

func (x *ResourceMetrics) GetUnit() string {
	if x != nil {
		return x.Unit
	}
	return ""
}

func (x *ResourceMetrics) GetColor() string {
	if x != nil {
		return x.Color
	}
	return ""
}

// vpc info.
type VPCStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// vpc name.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// vpc display name
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// vpc uid.
	Id string `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *VPCStatus) Reset() {
	*x = VPCStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VPCStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VPCStatus) ProtoMessage() {}

func (x *VPCStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VPCStatus.ProtoReflect.Descriptor instead.
func (*VPCStatus) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{16}
}

func (x *VPCStatus) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VPCStatus) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *VPCStatus) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// GetEIPMetricsRequest
type GetEIPMetricsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *GetEIPMetricsRequest) Reset() {
	*x = GetEIPMetricsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetEIPMetricsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetEIPMetricsRequest) ProtoMessage() {}

func (x *GetEIPMetricsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetEIPMetricsRequest.ProtoReflect.Descriptor instead.
func (*GetEIPMetricsRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{17}
}

// [zh] 列举 EIPs 的响应.
// [en] Response to list EIPs.
type ListEIPsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] EIP 列表.
	// [en] EIP list.
	Eips []*EIP `protobuf:"bytes,1,rep,name=eips,proto3" json:"eips,omitempty"`
	// [zh] 下一个页面的 token，如果没有更多数据则为空.
	// [en] Token to retrieve the next page of results, or empty if there are no more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// [zh] 返回的eip实例总数
	// [en] total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListEIPsResponse) Reset() {
	*x = ListEIPsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListEIPsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListEIPsResponse) ProtoMessage() {}

func (x *ListEIPsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListEIPsResponse.ProtoReflect.Descriptor instead.
func (*ListEIPsResponse) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{18}
}

func (x *ListEIPsResponse) GetEips() []*EIP {
	if x != nil {
		return x.Eips
	}
	return nil
}

func (x *ListEIPsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListEIPsResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// [zh] EIP 实例结构体.
// [en] EIP entity.
type EIP struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] EIP id
	// [en] The EIP resource id using the form:
	//     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/eips/{eip_name}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// [zh] EIP名
	// [en] The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] EIP前端展示名
	// [en] eip resource display name
	DisplayName string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// [zh] EIP说明
	// [en] eip resource description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// [zh] EIP uuid
	// [en] The EIP resource uuid.
	Uid string `protobuf:"bytes,5,opt,name=uid,proto3" json:"uid,omitempty"`
	// [zh] EIP资源类型
	// [en] The EIP resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// [zh] 创建该EIP的用户id
	// [en] The id of the user who created the EIP resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// [zh] 拥有该EIP资源的用户id
	// [en] The id of the user who owns the EIP resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// [zh] 租户id
	// [en] Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// [zh] 可用区
	// [en] Available zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 当前EIP资源的状态
	// [en] The current state of the EIP resource.
	State EIP_State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.eip.v1.EIP_State" json:"state,omitempty"`
	// [zh] 最小库存单元id
	// [en] Sku id.
	SkuId string `protobuf:"bytes,12,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// [zh] EIP资源标签
	// [en] Tags attached to the EIP resource.
	Tags map[string]string `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// [zh] EIP资源属性
	// [en] Properties of the EIP resource.
	Properties *EIPProperties `protobuf:"bytes,14,opt,name=properties,proto3" json:"properties,omitempty"`
	// [zh] EIP订单信息
	// [en] Payment information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,15,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	// [zh] EIP是否已删除
	// [en] Indicates whether the EIP resource is deleted or not.
	Deleted bool `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// [zh] EIP资源的创建时间
	// [en] The time when the EIP resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// [zh] EIP资源的更新时间
	// [en] The time when the EIP resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *EIP) Reset() {
	*x = EIP{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EIP) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EIP) ProtoMessage() {}

func (x *EIP) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EIP.ProtoReflect.Descriptor instead.
func (*EIP) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{19}
}

func (x *EIP) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EIP) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EIP) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *EIP) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *EIP) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *EIP) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *EIP) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *EIP) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *EIP) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *EIP) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *EIP) GetState() EIP_State {
	if x != nil {
		return x.State
	}
	return EIP_CREATING
}

func (x *EIP) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *EIP) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *EIP) GetProperties() *EIPProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *EIP) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

func (x *EIP) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *EIP) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *EIP) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// [zh] 资源实际属性.
// [en] Real resource properties.
type EIPProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] VPC的uuid.
	// [en] VPC uid.
	VpcId string `protobuf:"bytes,1,opt,name=vpc_id,json=vpcId,proto3" json:"vpc_id,omitempty"`
	// [zh] 关联的设备或组件 name, like vpc nat gateway id.
	// [en] Related device or item id, like vpc nat gateway id.
	AssociationId string `protobuf:"bytes,2,opt,name=association_id,json=associationId,proto3" json:"association_id,omitempty"`
	// [zh] 关联的设备或组件类型，NATGW,POD,SLB,BM.
	// [en] Related device type，NATGW,POD,SLB,BM.
	AssociationType EIPProperties_AType `protobuf:"varint,3,opt,name=association_type,json=associationType,proto3,enum=sensetime.core.network.eip.v1.EIPProperties_AType" json:"association_type,omitempty"`
	// [zh] EIP资源规格属性
	// [en] Resources
	Resources *Resources `protobuf:"bytes,4,opt,name=resources,proto3" json:"resources,omitempty"`
	// [zh] EIP 类型.
	// [en] EIP sku.
	Sku string `protobuf:"bytes,5,opt,name=sku,proto3" json:"sku,omitempty"`
	// [zh] 是否作为默认 vpc 流量出口.
	// [en] The vpc default snat.
	DefaultSnat bool `protobuf:"varint,6,opt,name=default_snat,json=defaultSnat,proto3" json:"default_snat,omitempty"`
	// [zh] 是否启用黑白名单.
	// [en] EIP acl enabled.
	AclEnabled bool `protobuf:"varint,7,opt,name=acl_enabled,json=aclEnabled,proto3" json:"acl_enabled,omitempty"`
	// [zh] EIP黑白名单规则列表.
	// [en] EIP acl rule list.
	Acls []*EIPACL `protobuf:"bytes,8,rep,name=acls,proto3" json:"acls,omitempty"`
	// [zh] 是否是企业网EIP.
	// [en] EIP internal flag.
	InternalEip bool `protobuf:"varint,9,opt,name=internal_eip,json=internalEip,proto3" json:"internal_eip,omitempty"`
	// [zh] 是否启用基于目的网段的snat规则.
	// [en] EIP snat enable while access dst cidr.
	SnatDstCidrEnabled bool `protobuf:"varint,10,opt,name=snat_dst_cidr_enabled,json=snatDstCidrEnabled,proto3" json:"snat_dst_cidr_enabled,omitempty"`
}

func (x *EIPProperties) Reset() {
	*x = EIPProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EIPProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EIPProperties) ProtoMessage() {}

func (x *EIPProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EIPProperties.ProtoReflect.Descriptor instead.
func (*EIPProperties) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{20}
}

func (x *EIPProperties) GetVpcId() string {
	if x != nil {
		return x.VpcId
	}
	return ""
}

func (x *EIPProperties) GetAssociationId() string {
	if x != nil {
		return x.AssociationId
	}
	return ""
}

func (x *EIPProperties) GetAssociationType() EIPProperties_AType {
	if x != nil {
		return x.AssociationType
	}
	return EIPProperties_NATGW
}

func (x *EIPProperties) GetResources() *Resources {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *EIPProperties) GetSku() string {
	if x != nil {
		return x.Sku
	}
	return ""
}

func (x *EIPProperties) GetDefaultSnat() bool {
	if x != nil {
		return x.DefaultSnat
	}
	return false
}

func (x *EIPProperties) GetAclEnabled() bool {
	if x != nil {
		return x.AclEnabled
	}
	return false
}

func (x *EIPProperties) GetAcls() []*EIPACL {
	if x != nil {
		return x.Acls
	}
	return nil
}

func (x *EIPProperties) GetInternalEip() bool {
	if x != nil {
		return x.InternalEip
	}
	return false
}

func (x *EIPProperties) GetSnatDstCidrEnabled() bool {
	if x != nil {
		return x.SnatDstCidrEnabled
	}
	return false
}

// [zh] EIP ACL规则
// [en] EIP ACL
type EIPACL struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] ACL名称.
	// [en] The name of the ACL.
	AclName string `protobuf:"bytes,1,opt,name=acl_name,json=aclName,proto3" json:"acl_name,omitempty"`
	// [zh] ACL属性.
	// [en] The properties of the ACL.
	AclProperties *ACLProperties `protobuf:"bytes,2,opt,name=acl_properties,json=aclProperties,proto3" json:"acl_properties,omitempty"`
	// [zh] ACL状态.
	// [en] The state of the ACL.
	AclState string `protobuf:"bytes,3,opt,name=acl_state,json=aclState,proto3" json:"acl_state,omitempty"`
}

func (x *EIPACL) Reset() {
	*x = EIPACL{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EIPACL) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EIPACL) ProtoMessage() {}

func (x *EIPACL) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EIPACL.ProtoReflect.Descriptor instead.
func (*EIPACL) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{21}
}

func (x *EIPACL) GetAclName() string {
	if x != nil {
		return x.AclName
	}
	return ""
}

func (x *EIPACL) GetAclProperties() *ACLProperties {
	if x != nil {
		return x.AclProperties
	}
	return nil
}

func (x *EIPACL) GetAclState() string {
	if x != nil {
		return x.AclState
	}
	return ""
}

// [zh] 资源规格属性
// [en] EIP Resources
type Resources struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 单独购买的计费项
	// [en] eip billing itmes
	BillingItems *BillingItems `protobuf:"bytes,1,opt,name=billing_items,json=billingItems,proto3" json:"billing_items,omitempty"`
	// [zh] 限速项
	// [en] eip limit rate items
	LimitRateItems *LimitRateItems `protobuf:"bytes,2,opt,name=limit_rate_items,json=limitRateItems,proto3" json:"limit_rate_items,omitempty"`
}

func (x *Resources) Reset() {
	*x = Resources{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Resources) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Resources) ProtoMessage() {}

func (x *Resources) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Resources.ProtoReflect.Descriptor instead.
func (*Resources) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{22}
}

func (x *Resources) GetBillingItems() *BillingItems {
	if x != nil {
		return x.BillingItems
	}
	return nil
}

func (x *Resources) GetLimitRateItems() *LimitRateItems {
	if x != nil {
		return x.LimitRateItems
	}
	return nil
}

// [zh] 计费项
// [en] billing items
type BillingItems struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] EIP 带宽限制，单位: M/s.
	// [en] eip bandwidth, unit: M/s
	Bw int32 `protobuf:"varint,1,opt,name=bw,proto3" json:"bw,omitempty"`
}

func (x *BillingItems) Reset() {
	*x = BillingItems{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BillingItems) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingItems) ProtoMessage() {}

func (x *BillingItems) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingItems.ProtoReflect.Descriptor instead.
func (*BillingItems) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{23}
}

func (x *BillingItems) GetBw() int32 {
	if x != nil {
		return x.Bw
	}
	return 0
}

// [zh] 限速项
// [en] rate limit items
type LimitRateItems struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] EIP 上行带宽限制，单位: M/s.
	// [en] eip upstream bandwidth limit, unit: M/s
	UpStreamBw int32 `protobuf:"varint,1,opt,name=up_stream_bw,json=upStreamBw,proto3" json:"up_stream_bw,omitempty"`
	// [zh] EIP 下行带宽限制，单位: M/s
	// [en] EIP downstream bandwidth limit, unit: M/s
	DownStreamBw int32 `protobuf:"varint,2,opt,name=down_stream_bw,json=downStreamBw,proto3" json:"down_stream_bw,omitempty"`
}

func (x *LimitRateItems) Reset() {
	*x = LimitRateItems{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitRateItems) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitRateItems) ProtoMessage() {}

func (x *LimitRateItems) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitRateItems.ProtoReflect.Descriptor instead.
func (*LimitRateItems) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_proto_rawDescGZIP(), []int{24}
}

func (x *LimitRateItems) GetUpStreamBw() int32 {
	if x != nil {
		return x.UpStreamBw
	}
	return 0
}

func (x *LimitRateItems) GetDownStreamBw() int32 {
	if x != nil {
		return x.DownStreamBw
	}
	return 0
}

var File_network_eip_v1_eip_proto protoreflect.FileDescriptor

var file_network_eip_v1_eip_proto_rawDesc = []byte{
	0x0a, 0x18, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x76, 0x31,
	0x2f, 0x65, 0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65,
	0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1b, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f,
	0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x68,
	0x69, 0x67, 0x67, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x18, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f,
	0x61, 0x63, 0x6c, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x8e, 0x02, 0x0a, 0x0f, 0x4c, 0x69,
	0x73, 0x74, 0x45, 0x49, 0x50, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a,
	0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16,
	0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f,
	0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42,
	0x79, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a,
	0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x9b, 0x01, 0x0a, 0x0d, 0x47,
	0x65, 0x74, 0x45, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x65, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa1, 0x01, 0x0a, 0x13, 0x47, 0x65, 0x74,
	0x45, 0x49, 0x50, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a,
	0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x80, 0x02, 0x0a,
	0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e,
	0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e,
	0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b,
	0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24,
	0x52, 0x07, 0x65, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x03, 0x65, 0x69, 0x70,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x52, 0x03, 0x65, 0x69, 0x70, 0x22,
	0x91, 0x02, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x49, 0x50, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x34, 0x0a, 0x03, 0x65, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49,
	0x50, 0x52, 0x03, 0x65, 0x69, 0x70, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d,
	0x61, 0x73, 0x6b, 0x22, 0xa2, 0x01, 0x0a, 0x14, 0x45, 0x78, 0x70, 0x69, 0x72, 0x65, 0x53, 0x74,
	0x6f, 0x70, 0x45, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x65, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa2, 0x01, 0x0a, 0x14, 0x52, 0x65, 0x6e,
	0x65, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x45, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e,
	0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x9f, 0x01,
	0x0a, 0x11, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x45, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0xa3, 0x01, 0x0a, 0x15, 0x46, 0x6f, 0x72, 0x63, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45,
	0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x69,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x69,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x9e, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x45, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65,
	0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65,
	0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xaa, 0x02, 0x0a, 0x10, 0x52, 0x65, 0x73, 0x69, 0x7a,
	0x65, 0x45, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a,
	0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x65,
	0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0,
	0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a,
	0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31,
	0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x65, 0x69,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4c, 0x0a, 0x0a, 0x65, 0x69, 0x70, 0x5f, 0x72, 0x65, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x52, 0x65, 0x73,
	0x69, 0x7a, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x09, 0x65, 0x69, 0x70, 0x52, 0x65, 0x73,
	0x69, 0x7a, 0x65, 0x22, 0x8b, 0x02, 0x0a, 0x09, 0x45, 0x49, 0x50, 0x52, 0x65, 0x73, 0x69, 0x7a,
	0x65, 0x12, 0x24, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x05, 0x73, 0x6b,
	0x75, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49,
	0x50, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x68, 0x69, 0x67,
	0x67, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x22, 0x91, 0x01, 0x0a, 0x0a, 0x53, 0x6e, 0x61, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x70,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x69, 0x6e, 0x6e, 0x65, 0x72, 0x49, 0x70, 0x12,
	0x19, 0x0a, 0x08, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x5f, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x07, 0x6f, 0x75, 0x74, 0x65, 0x72, 0x49, 0x70, 0x12, 0x16, 0x0a, 0x06, 0x70, 0x6f,
	0x6c, 0x69, 0x63, 0x79, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x70, 0x6f, 0x6c, 0x69,
	0x63, 0x79, 0x12, 0x21, 0x0a, 0x0c, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79, 0x5f, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x70, 0x6f, 0x6c, 0x69, 0x63, 0x79,
	0x56, 0x61, 0x6c, 0x75, 0x65, 0x22, 0xac, 0x02, 0x0a, 0x09, 0x45, 0x49, 0x50, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x12, 0x3d, 0x0a, 0x08, 0x65, 0x69, 0x70, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65,
	0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x52, 0x07, 0x65, 0x69, 0x70, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x69, 0x70, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x65, 0x69, 0x70, 0x49, 0x70, 0x12, 0x43, 0x0a, 0x08, 0x76, 0x70, 0x63,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x50, 0x43, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x07, 0x76, 0x70, 0x63, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d,
	0x0a, 0x0a, 0x73, 0x6e, 0x61, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x09, 0x73, 0x6e, 0x61, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x1d, 0x0a,
	0x0a, 0x64, 0x6e, 0x61, 0x74, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x09, 0x64, 0x6e, 0x61, 0x74, 0x43, 0x6f, 0x75, 0x6e, 0x74, 0x12, 0x46, 0x0a, 0x09,
	0x73, 0x6e, 0x61, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x6e, 0x61, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x08, 0x73, 0x6e, 0x61, 0x74,
	0x49, 0x6e, 0x66, 0x6f, 0x22, 0x67, 0x0a, 0x0a, 0x45, 0x49, 0x50, 0x4d, 0x65, 0x74, 0x72, 0x69,
	0x63, 0x73, 0x12, 0x59, 0x0a, 0x10, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x6d,
	0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x0f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x22, 0x65, 0x0a,
	0x0f, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x75, 0x6e,
	0x69, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x75, 0x6e, 0x69, 0x74, 0x12, 0x14,
	0x0a, 0x05, 0x63, 0x6f, 0x6c, 0x6f, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x63,
	0x6f, 0x6c, 0x6f, 0x72, 0x22, 0x52, 0x0a, 0x09, 0x56, 0x50, 0x43, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0x16, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x45,
	0x49, 0x50, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x22, 0x91, 0x01, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x49, 0x50, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x04, 0x65, 0x69, 0x70, 0x73, 0x18, 0x01, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x52, 0x04, 0x65, 0x69, 0x70, 0x73, 0x12, 0x26, 0x0a,
	0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65,
	0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73,
	0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x53, 0x69, 0x7a, 0x65, 0x22, 0xd7, 0x07, 0x0a, 0x03, 0x45, 0x49, 0x50, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x2a, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x07, 0xfa, 0x42, 0x04, 0x72, 0x02, 0x18, 0x19, 0x52,
	0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12,
	0x1b, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e,
	0x45, 0x49, 0x50, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65,
	0x12, 0x15, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18,
	0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65,
	0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x4c, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49,
	0x50, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72,
	0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x68, 0x69, 0x67,
	0x67, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64,
	0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66,
	0x6f, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01,
	0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xbd,
	0x01, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x44, 0x41, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02,
	0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0b,
	0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0e, 0x0a, 0x0a, 0x53, 0x55, 0x53, 0x50, 0x45,
	0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x53, 0x55, 0x53, 0x50, 0x45,
	0x4e, 0x44, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x53, 0x55, 0x4d, 0x49,
	0x4e, 0x47, 0x10, 0x08, 0x12, 0x12, 0x0a, 0x0e, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x53, 0x54,
	0x4f, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x09, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x58, 0x50, 0x49,
	0x52, 0x45, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x45, 0x44, 0x10, 0x0a, 0x12, 0x11, 0x0a, 0x0d, 0x52,
	0x45, 0x4e, 0x45, 0x57, 0x53, 0x54, 0x41, 0x52, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x0b, 0x22, 0x9b,
	0x04, 0x0a, 0x0d, 0x45, 0x49, 0x50, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x12, 0x15, 0x0a, 0x06, 0x76, 0x70, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x05, 0x76, 0x70, 0x63, 0x49, 0x64, 0x12, 0x25, 0x0a, 0x0e, 0x61, 0x73, 0x73, 0x6f, 0x63,
	0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0d, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x49, 0x64, 0x12, 0x5d,
	0x0a, 0x10, 0x61, 0x73, 0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x32, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x41, 0x54, 0x79, 0x70, 0x65, 0x52, 0x0f, 0x61, 0x73,
	0x73, 0x6f, 0x63, 0x69, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70, 0x65, 0x12, 0x46, 0x0a,
	0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31,
	0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x10, 0x0a, 0x03, 0x73, 0x6b, 0x75, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x73, 0x6b, 0x75, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x65, 0x66, 0x61, 0x75,
	0x6c, 0x74, 0x5f, 0x73, 0x6e, 0x61, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x64,
	0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x53, 0x6e, 0x61, 0x74, 0x12, 0x1f, 0x0a, 0x0b, 0x61, 0x63,
	0x6c, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x0a, 0x61, 0x63, 0x6c, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x12, 0x39, 0x0a, 0x04, 0x61,
	0x63, 0x6c, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x41, 0x43, 0x4c,
	0x52, 0x04, 0x61, 0x63, 0x6c, 0x73, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x5f, 0x65, 0x69, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28, 0x08, 0x52, 0x0b, 0x69, 0x6e,
	0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x45, 0x69, 0x70, 0x12, 0x31, 0x0a, 0x15, 0x73, 0x6e, 0x61,
	0x74, 0x5f, 0x64, 0x73, 0x74, 0x5f, 0x63, 0x69, 0x64, 0x72, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c,
	0x65, 0x64, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x08, 0x52, 0x12, 0x73, 0x6e, 0x61, 0x74, 0x44, 0x73,
	0x74, 0x43, 0x69, 0x64, 0x72, 0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x64, 0x22, 0x3e, 0x0a, 0x05,
	0x41, 0x54, 0x79, 0x70, 0x65, 0x12, 0x09, 0x0a, 0x05, 0x4e, 0x41, 0x54, 0x47, 0x57, 0x10, 0x00,
	0x12, 0x07, 0x0a, 0x03, 0x50, 0x4f, 0x44, 0x10, 0x01, 0x12, 0x07, 0x0a, 0x03, 0x53, 0x4c, 0x42,
	0x10, 0x02, 0x12, 0x06, 0x0a, 0x02, 0x42, 0x4d, 0x10, 0x03, 0x12, 0x10, 0x0a, 0x0c, 0x4e, 0x41,
	0x54, 0x47, 0x57, 0x5f, 0x41, 0x4e, 0x44, 0x5f, 0x42, 0x4d, 0x10, 0x04, 0x22, 0x95, 0x01, 0x0a,
	0x06, 0x45, 0x49, 0x50, 0x41, 0x43, 0x4c, 0x12, 0x19, 0x0a, 0x08, 0x61, 0x63, 0x6c, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x61, 0x63, 0x6c, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x53, 0x0a, 0x0e, 0x61, 0x63, 0x6c, 0x5f, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x41, 0x43, 0x4c, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0d, 0x61, 0x63, 0x6c, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x61, 0x63, 0x6c, 0x5f, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x61, 0x63, 0x6c, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x22, 0xb6, 0x01, 0x0a, 0x09, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x73, 0x12, 0x50, 0x0a, 0x0d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x69, 0x74,
	0x65, 0x6d, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x49,
	0x74, 0x65, 0x6d, 0x73, 0x12, 0x57, 0x0a, 0x10, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x5f, 0x72, 0x61,
	0x74, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2d,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x6d, 0x69, 0x74, 0x52, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x0e, 0x6c,
	0x69, 0x6d, 0x69, 0x74, 0x52, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x1e, 0x0a,
	0x0c, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x0e, 0x0a,
	0x02, 0x62, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x02, 0x62, 0x77, 0x22, 0x58, 0x0a,
	0x0e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12,
	0x20, 0x0a, 0x0c, 0x75, 0x70, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f, 0x62, 0x77, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a, 0x75, 0x70, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x42,
	0x77, 0x12, 0x24, 0x0a, 0x0e, 0x64, 0x6f, 0x77, 0x6e, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d,
	0x5f, 0x62, 0x77, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x53,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x42, 0x77, 0x32, 0xf5, 0x20, 0x0a, 0x04, 0x45, 0x49, 0x50, 0x73,
	0x12, 0xc0, 0x02, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x45, 0x49, 0x50, 0x73, 0x12, 0x2e, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x45, 0x49, 0x50, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x45, 0x49, 0x50, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xd2,
	0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x6a, 0x12, 0x68, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70,
	0x73, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x5a, 0x0a, 0x4a, 0x2f, 0x72, 0x6d, 0x2f, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f,
	0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0c, 0x65, 0x69, 0x70, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x6c,
	0x69, 0x73, 0x74, 0x12, 0xd6, 0x02, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x45, 0x49, 0x50, 0x12, 0x2c,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x45, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50,
	0x22, 0xf9, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x75, 0x12, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65,
	0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x80, 0xb5,
	0x18, 0x01, 0x9a, 0xb5, 0x18, 0x76, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f,
	0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12,
	0x0b, 0x65, 0x69, 0x70, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x67, 0x65, 0x74, 0x12, 0xf5, 0x02, 0x0a,
	0x0c, 0x47, 0x65, 0x74, 0x45, 0x49, 0x50, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65,
	0x74, 0x45, 0x49, 0x50, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x45, 0x49, 0x50, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x86, 0x02, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x81, 0x01, 0x12, 0x7f, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f,
	0x65, 0x69, 0x70, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f,
	0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x76, 0x0a, 0x67,
	0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73,
	0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0b, 0x65, 0x69, 0x70, 0x2e, 0x65, 0x69, 0x70,
	0x2e, 0x67, 0x65, 0x74, 0x12, 0xe8, 0x02, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45,
	0x49, 0x50, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x45, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x22, 0x85, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7a,
	0x22, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b,
	0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x03, 0x65, 0x69, 0x70, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5,
	0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f,
	0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12,
	0x0e, 0x65, 0x69, 0x70, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12,
	0xe8, 0x02, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x49, 0x50, 0x12, 0x2f, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x45, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45,
	0x49, 0x50, 0x22, 0x85, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7a, 0x32, 0x73, 0x2f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d,
	0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x3a, 0x03, 0x65, 0x69, 0x70, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18,
	0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f,
	0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f,
	0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x65, 0x69, 0x70, 0x2e,
	0x65, 0x69, 0x70, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xeb, 0x02, 0x0a, 0x0d, 0x45,
	0x78, 0x70, 0x69, 0x72, 0x65, 0x53, 0x74, 0x6f, 0x70, 0x45, 0x49, 0x50, 0x12, 0x33, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x78, 0x70,
	0x69, 0x72, 0x65, 0x53, 0x74, 0x6f, 0x70, 0x45, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x8c, 0x02, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x80, 0x01, 0x22, 0x7e, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69,
	0x70, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65,
	0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65,
	0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x65, 0x78, 0x70, 0x69, 0x72, 0x65, 0x53,
	0x74, 0x6f, 0x70, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a,
	0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65,
	0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65,
	0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x65, 0x69, 0x70, 0x2e, 0x65, 0x69,
	0x70, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xeb, 0x02, 0x0a, 0x0d, 0x52, 0x65, 0x6e,
	0x65, 0x77, 0x53, 0x74, 0x61, 0x72, 0x74, 0x45, 0x49, 0x50, 0x12, 0x33, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6e, 0x65, 0x77,
	0x53, 0x74, 0x61, 0x72, 0x74, 0x45, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x8c, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x80,
	0x01, 0x22, 0x7e, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f,
	0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x72, 0x65, 0x6e, 0x65, 0x77, 0x53, 0x74, 0x61, 0x72,
	0x74, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67, 0x2f,
	0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f,
	0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x65, 0x69, 0x70, 0x2e, 0x65, 0x69, 0x70, 0x2e,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xe1, 0x02, 0x0a, 0x0a, 0x52, 0x65, 0x6c, 0x65, 0x61,
	0x73, 0x65, 0x45, 0x49, 0x50, 0x12, 0x30, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65,
	0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x45, 0x49, 0x50,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x88, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7d, 0x22, 0x7b, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69,
	0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x72, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18,
	0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f,
	0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f,
	0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x65, 0x69, 0x70, 0x2e,
	0x65, 0x69, 0x70, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0xee, 0x02, 0x0a, 0x0e, 0x46,
	0x6f, 0x72, 0x63, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x49, 0x50, 0x12, 0x34, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f,
	0x72, 0x63, 0x65, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x8d, 0x02, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x81, 0x01, 0x22, 0x7f, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f,
	0x65, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f,
	0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f,
	0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x66, 0x6f, 0x72, 0x63, 0x65,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5,
	0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a,
	0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73,
	0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x65, 0x69, 0x70,
	0x2e, 0x65, 0x69, 0x70, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0xd7, 0x02, 0x0a, 0x09,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x45, 0x49, 0x50, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x45, 0x49, 0x50, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x80, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x75, 0x2a, 0x73, 0x2f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d,
	0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72,
	0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b,
	0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x65, 0x69, 0x70, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0xf7, 0x02, 0x0a, 0x09, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65,
	0x45, 0x49, 0x50, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x45, 0x49, 0x50, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x22, 0x94, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x88, 0x01, 0x22, 0x7a, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73,
	0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x72, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x3a, 0x0a,
	0x65, 0x69, 0x70, 0x5f, 0x72, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5,
	0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f,
	0x65, 0x69, 0x70, 0x73, 0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12,
	0x0e, 0x65, 0x69, 0x70, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x72, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x12,
	0xfd, 0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x45, 0x49, 0x50, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x12, 0x33, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76,
	0x31, 0x2e, 0x47, 0x65, 0x74, 0x45, 0x49, 0x50, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x4d, 0x65, 0x74, 0x72, 0x69, 0x63,
	0x73, 0x22, 0x8b, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x23, 0x12, 0x21, 0x2f, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31,
	0x2f, 0x65, 0x69, 0x70, 0x73, 0x2f, 0x6d, 0x65, 0x74, 0x72, 0x69, 0x63, 0x73, 0x80, 0xb5, 0x18,
	0x01, 0x9a, 0xb5, 0x18, 0x5a, 0x0a, 0x4a, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x12, 0x0c, 0x65, 0x69, 0x70, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x42,
	0x50, 0x5a, 0x4e, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x62, 0x6a, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x61, 0x72, 0x79, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x73, 0x6f,
	0x6e, 0x2d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x69,
	0x70, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_network_eip_v1_eip_proto_rawDescOnce sync.Once
	file_network_eip_v1_eip_proto_rawDescData = file_network_eip_v1_eip_proto_rawDesc
)

func file_network_eip_v1_eip_proto_rawDescGZIP() []byte {
	file_network_eip_v1_eip_proto_rawDescOnce.Do(func() {
		file_network_eip_v1_eip_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_eip_v1_eip_proto_rawDescData)
	})
	return file_network_eip_v1_eip_proto_rawDescData
}

var file_network_eip_v1_eip_proto_enumTypes = make([]protoimpl.EnumInfo, 2)
var file_network_eip_v1_eip_proto_msgTypes = make([]protoimpl.MessageInfo, 26)
var file_network_eip_v1_eip_proto_goTypes = []interface{}{
	(EIP_State)(0),                // 0: sensetime.core.network.eip.v1.EIP.State
	(EIPProperties_AType)(0),      // 1: sensetime.core.network.eip.v1.EIPProperties.AType
	(*ListEIPsRequest)(nil),       // 2: sensetime.core.network.eip.v1.ListEIPsRequest
	(*GetEIPRequest)(nil),         // 3: sensetime.core.network.eip.v1.GetEIPRequest
	(*GetEIPStatusRequest)(nil),   // 4: sensetime.core.network.eip.v1.GetEIPStatusRequest
	(*CreateEIPRequest)(nil),      // 5: sensetime.core.network.eip.v1.CreateEIPRequest
	(*UpdateEIPRequest)(nil),      // 6: sensetime.core.network.eip.v1.UpdateEIPRequest
	(*ExpireStopEIPRequest)(nil),  // 7: sensetime.core.network.eip.v1.ExpireStopEIPRequest
	(*RenewStartEIPRequest)(nil),  // 8: sensetime.core.network.eip.v1.RenewStartEIPRequest
	(*ReleaseEIPRequest)(nil),     // 9: sensetime.core.network.eip.v1.ReleaseEIPRequest
	(*ForceDeleteEIPRequest)(nil), // 10: sensetime.core.network.eip.v1.ForceDeleteEIPRequest
	(*DeleteEIPRequest)(nil),      // 11: sensetime.core.network.eip.v1.DeleteEIPRequest
	(*ResizeEIPRequest)(nil),      // 12: sensetime.core.network.eip.v1.ResizeEIPRequest
	(*EIPResize)(nil),             // 13: sensetime.core.network.eip.v1.EIPResize
	(*SnatStatus)(nil),            // 14: sensetime.core.network.eip.v1.SnatStatus
	(*EIPStatus)(nil),             // 15: sensetime.core.network.eip.v1.EIPStatus
	(*EIPMetrics)(nil),            // 16: sensetime.core.network.eip.v1.EIPMetrics
	(*ResourceMetrics)(nil),       // 17: sensetime.core.network.eip.v1.ResourceMetrics
	(*VPCStatus)(nil),             // 18: sensetime.core.network.eip.v1.VPCStatus
	(*GetEIPMetricsRequest)(nil),  // 19: sensetime.core.network.eip.v1.GetEIPMetricsRequest
	(*ListEIPsResponse)(nil),      // 20: sensetime.core.network.eip.v1.ListEIPsResponse
	(*EIP)(nil),                   // 21: sensetime.core.network.eip.v1.EIP
	(*EIPProperties)(nil),         // 22: sensetime.core.network.eip.v1.EIPProperties
	(*EIPACL)(nil),                // 23: sensetime.core.network.eip.v1.EIPACL
	(*Resources)(nil),             // 24: sensetime.core.network.eip.v1.Resources
	(*BillingItems)(nil),          // 25: sensetime.core.network.eip.v1.BillingItems
	(*LimitRateItems)(nil),        // 26: sensetime.core.network.eip.v1.LimitRateItems
	nil,                           // 27: sensetime.core.network.eip.v1.EIP.TagsEntry
	(*fieldmaskpb.FieldMask)(nil), // 28: google.protobuf.FieldMask
	(*v1.OrderInfo)(nil),          // 29: sensetime.core.higgs.common.v1.OrderInfo
	(*timestamppb.Timestamp)(nil), // 30: google.protobuf.Timestamp
	(*ACLProperties)(nil),         // 31: sensetime.core.network.eip.v1.ACLProperties
	(*emptypb.Empty)(nil),         // 32: google.protobuf.Empty
}
var file_network_eip_v1_eip_proto_depIdxs = []int32{
	21, // 0: sensetime.core.network.eip.v1.CreateEIPRequest.eip:type_name -> sensetime.core.network.eip.v1.EIP
	21, // 1: sensetime.core.network.eip.v1.UpdateEIPRequest.eip:type_name -> sensetime.core.network.eip.v1.EIP
	28, // 2: sensetime.core.network.eip.v1.UpdateEIPRequest.update_mask:type_name -> google.protobuf.FieldMask
	13, // 3: sensetime.core.network.eip.v1.ResizeEIPRequest.eip_resize:type_name -> sensetime.core.network.eip.v1.EIPResize
	22, // 4: sensetime.core.network.eip.v1.EIPResize.properties:type_name -> sensetime.core.network.eip.v1.EIPProperties
	29, // 5: sensetime.core.network.eip.v1.EIPResize.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	21, // 6: sensetime.core.network.eip.v1.EIPStatus.eip_info:type_name -> sensetime.core.network.eip.v1.EIP
	18, // 7: sensetime.core.network.eip.v1.EIPStatus.vpc_info:type_name -> sensetime.core.network.eip.v1.VPCStatus
	14, // 8: sensetime.core.network.eip.v1.EIPStatus.snat_info:type_name -> sensetime.core.network.eip.v1.SnatStatus
	17, // 9: sensetime.core.network.eip.v1.EIPMetrics.resource_metrics:type_name -> sensetime.core.network.eip.v1.ResourceMetrics
	21, // 10: sensetime.core.network.eip.v1.ListEIPsResponse.eips:type_name -> sensetime.core.network.eip.v1.EIP
	0,  // 11: sensetime.core.network.eip.v1.EIP.state:type_name -> sensetime.core.network.eip.v1.EIP.State
	27, // 12: sensetime.core.network.eip.v1.EIP.tags:type_name -> sensetime.core.network.eip.v1.EIP.TagsEntry
	22, // 13: sensetime.core.network.eip.v1.EIP.properties:type_name -> sensetime.core.network.eip.v1.EIPProperties
	29, // 14: sensetime.core.network.eip.v1.EIP.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	30, // 15: sensetime.core.network.eip.v1.EIP.create_time:type_name -> google.protobuf.Timestamp
	30, // 16: sensetime.core.network.eip.v1.EIP.update_time:type_name -> google.protobuf.Timestamp
	1,  // 17: sensetime.core.network.eip.v1.EIPProperties.association_type:type_name -> sensetime.core.network.eip.v1.EIPProperties.AType
	24, // 18: sensetime.core.network.eip.v1.EIPProperties.resources:type_name -> sensetime.core.network.eip.v1.Resources
	23, // 19: sensetime.core.network.eip.v1.EIPProperties.acls:type_name -> sensetime.core.network.eip.v1.EIPACL
	31, // 20: sensetime.core.network.eip.v1.EIPACL.acl_properties:type_name -> sensetime.core.network.eip.v1.ACLProperties
	25, // 21: sensetime.core.network.eip.v1.Resources.billing_items:type_name -> sensetime.core.network.eip.v1.BillingItems
	26, // 22: sensetime.core.network.eip.v1.Resources.limit_rate_items:type_name -> sensetime.core.network.eip.v1.LimitRateItems
	2,  // 23: sensetime.core.network.eip.v1.EIPs.ListEIPs:input_type -> sensetime.core.network.eip.v1.ListEIPsRequest
	3,  // 24: sensetime.core.network.eip.v1.EIPs.GetEIP:input_type -> sensetime.core.network.eip.v1.GetEIPRequest
	4,  // 25: sensetime.core.network.eip.v1.EIPs.GetEIPStatus:input_type -> sensetime.core.network.eip.v1.GetEIPStatusRequest
	5,  // 26: sensetime.core.network.eip.v1.EIPs.CreateEIP:input_type -> sensetime.core.network.eip.v1.CreateEIPRequest
	6,  // 27: sensetime.core.network.eip.v1.EIPs.UpdateEIP:input_type -> sensetime.core.network.eip.v1.UpdateEIPRequest
	7,  // 28: sensetime.core.network.eip.v1.EIPs.ExpireStopEIP:input_type -> sensetime.core.network.eip.v1.ExpireStopEIPRequest
	8,  // 29: sensetime.core.network.eip.v1.EIPs.RenewStartEIP:input_type -> sensetime.core.network.eip.v1.RenewStartEIPRequest
	9,  // 30: sensetime.core.network.eip.v1.EIPs.ReleaseEIP:input_type -> sensetime.core.network.eip.v1.ReleaseEIPRequest
	10, // 31: sensetime.core.network.eip.v1.EIPs.ForceDeleteEIP:input_type -> sensetime.core.network.eip.v1.ForceDeleteEIPRequest
	11, // 32: sensetime.core.network.eip.v1.EIPs.DeleteEIP:input_type -> sensetime.core.network.eip.v1.DeleteEIPRequest
	12, // 33: sensetime.core.network.eip.v1.EIPs.ResizeEIP:input_type -> sensetime.core.network.eip.v1.ResizeEIPRequest
	19, // 34: sensetime.core.network.eip.v1.EIPs.GetEIPMetrics:input_type -> sensetime.core.network.eip.v1.GetEIPMetricsRequest
	20, // 35: sensetime.core.network.eip.v1.EIPs.ListEIPs:output_type -> sensetime.core.network.eip.v1.ListEIPsResponse
	21, // 36: sensetime.core.network.eip.v1.EIPs.GetEIP:output_type -> sensetime.core.network.eip.v1.EIP
	15, // 37: sensetime.core.network.eip.v1.EIPs.GetEIPStatus:output_type -> sensetime.core.network.eip.v1.EIPStatus
	21, // 38: sensetime.core.network.eip.v1.EIPs.CreateEIP:output_type -> sensetime.core.network.eip.v1.EIP
	21, // 39: sensetime.core.network.eip.v1.EIPs.UpdateEIP:output_type -> sensetime.core.network.eip.v1.EIP
	32, // 40: sensetime.core.network.eip.v1.EIPs.ExpireStopEIP:output_type -> google.protobuf.Empty
	32, // 41: sensetime.core.network.eip.v1.EIPs.RenewStartEIP:output_type -> google.protobuf.Empty
	32, // 42: sensetime.core.network.eip.v1.EIPs.ReleaseEIP:output_type -> google.protobuf.Empty
	32, // 43: sensetime.core.network.eip.v1.EIPs.ForceDeleteEIP:output_type -> google.protobuf.Empty
	32, // 44: sensetime.core.network.eip.v1.EIPs.DeleteEIP:output_type -> google.protobuf.Empty
	21, // 45: sensetime.core.network.eip.v1.EIPs.ResizeEIP:output_type -> sensetime.core.network.eip.v1.EIP
	16, // 46: sensetime.core.network.eip.v1.EIPs.GetEIPMetrics:output_type -> sensetime.core.network.eip.v1.EIPMetrics
	35, // [35:47] is the sub-list for method output_type
	23, // [23:35] is the sub-list for method input_type
	23, // [23:23] is the sub-list for extension type_name
	23, // [23:23] is the sub-list for extension extendee
	0,  // [0:23] is the sub-list for field type_name
}

func init() { file_network_eip_v1_eip_proto_init() }
func file_network_eip_v1_eip_proto_init() {
	if File_network_eip_v1_eip_proto != nil {
		return
	}
	file_network_eip_v1_acl_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_network_eip_v1_eip_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEIPsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEIPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEIPStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateEIPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEIPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ExpireStopEIPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*RenewStartEIPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleaseEIPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForceDeleteEIPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteEIPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResizeEIPRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EIPResize); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SnatStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EIPStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EIPMetrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResourceMetrics); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VPCStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetEIPMetricsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListEIPsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EIP); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EIPProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EIPACL); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Resources); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BillingItems); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitRateItems); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_eip_v1_eip_proto_rawDesc,
			NumEnums:      2,
			NumMessages:   26,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_network_eip_v1_eip_proto_goTypes,
		DependencyIndexes: file_network_eip_v1_eip_proto_depIdxs,
		EnumInfos:         file_network_eip_v1_eip_proto_enumTypes,
		MessageInfos:      file_network_eip_v1_eip_proto_msgTypes,
	}.Build()
	File_network_eip_v1_eip_proto = out.File
	file_network_eip_v1_eip_proto_rawDesc = nil
	file_network_eip_v1_eip_proto_goTypes = nil
	file_network_eip_v1_eip_proto_depIdxs = nil
}
