// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/eip/v1/acl.proto

package eip

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// ACLsClient is the client API for ACLs service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type ACLsClient interface {
	// [zh]列举EIP下所有 ACLs.
	// [en] List EIP ACLs.
	ListACLs(ctx context.Context, in *ListACLsRequest, opts ...grpc.CallOption) (*ListACLsResponse, error)
	// [zh]获取符合请求的一个 ACL.
	// [en] Get an ACL.
	GetACL(ctx context.Context, in *GetACLRequest, opts ...grpc.CallOption) (*ACL, error)
	// [zh]创建一个 ACL.
	// [en] Create an ACL.
	CreateACL(ctx context.Context, in *CreateACLRequest, opts ...grpc.CallOption) (*ACL, error)
	// [zh]更新一个 ACL.
	// [en] update the ACL.
	UpdateACL(ctx context.Context, in *UpdateACLRequest, opts ...grpc.CallOption) (*ACL, error)
	// [zh]删除一个 ACL.
	// [en] Delete a ACL.
	DeleteACL(ctx context.Context, in *DeleteACLRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type aCLsClient struct {
	cc grpc.ClientConnInterface
}

func NewACLsClient(cc grpc.ClientConnInterface) ACLsClient {
	return &aCLsClient{cc}
}

func (c *aCLsClient) ListACLs(ctx context.Context, in *ListACLsRequest, opts ...grpc.CallOption) (*ListACLsResponse, error) {
	out := new(ListACLsResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.ACLs/ListACLs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCLsClient) GetACL(ctx context.Context, in *GetACLRequest, opts ...grpc.CallOption) (*ACL, error) {
	out := new(ACL)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.ACLs/GetACL", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCLsClient) CreateACL(ctx context.Context, in *CreateACLRequest, opts ...grpc.CallOption) (*ACL, error) {
	out := new(ACL)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.ACLs/CreateACL", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCLsClient) UpdateACL(ctx context.Context, in *UpdateACLRequest, opts ...grpc.CallOption) (*ACL, error) {
	out := new(ACL)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.ACLs/UpdateACL", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *aCLsClient) DeleteACL(ctx context.Context, in *DeleteACLRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.ACLs/DeleteACL", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// ACLsServer is the server API for ACLs service.
// All implementations must embed UnimplementedACLsServer
// for forward compatibility
type ACLsServer interface {
	// [zh]列举EIP下所有 ACLs.
	// [en] List EIP ACLs.
	ListACLs(context.Context, *ListACLsRequest) (*ListACLsResponse, error)
	// [zh]获取符合请求的一个 ACL.
	// [en] Get an ACL.
	GetACL(context.Context, *GetACLRequest) (*ACL, error)
	// [zh]创建一个 ACL.
	// [en] Create an ACL.
	CreateACL(context.Context, *CreateACLRequest) (*ACL, error)
	// [zh]更新一个 ACL.
	// [en] update the ACL.
	UpdateACL(context.Context, *UpdateACLRequest) (*ACL, error)
	// [zh]删除一个 ACL.
	// [en] Delete a ACL.
	DeleteACL(context.Context, *DeleteACLRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedACLsServer()
}

// UnimplementedACLsServer must be embedded to have forward compatible implementations.
type UnimplementedACLsServer struct {
}

func (UnimplementedACLsServer) ListACLs(context.Context, *ListACLsRequest) (*ListACLsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListACLs not implemented")
}
func (UnimplementedACLsServer) GetACL(context.Context, *GetACLRequest) (*ACL, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetACL not implemented")
}
func (UnimplementedACLsServer) CreateACL(context.Context, *CreateACLRequest) (*ACL, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateACL not implemented")
}
func (UnimplementedACLsServer) UpdateACL(context.Context, *UpdateACLRequest) (*ACL, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateACL not implemented")
}
func (UnimplementedACLsServer) DeleteACL(context.Context, *DeleteACLRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteACL not implemented")
}
func (UnimplementedACLsServer) mustEmbedUnimplementedACLsServer() {}

// UnsafeACLsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to ACLsServer will
// result in compilation errors.
type UnsafeACLsServer interface {
	mustEmbedUnimplementedACLsServer()
}

func RegisterACLsServer(s grpc.ServiceRegistrar, srv ACLsServer) {
	s.RegisterService(&ACLs_ServiceDesc, srv)
}

func _ACLs_ListACLs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListACLsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACLsServer).ListACLs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.ACLs/ListACLs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACLsServer).ListACLs(ctx, req.(*ListACLsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACLs_GetACL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetACLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACLsServer).GetACL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.ACLs/GetACL",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACLsServer).GetACL(ctx, req.(*GetACLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACLs_CreateACL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateACLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACLsServer).CreateACL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.ACLs/CreateACL",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACLsServer).CreateACL(ctx, req.(*CreateACLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACLs_UpdateACL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateACLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACLsServer).UpdateACL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.ACLs/UpdateACL",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACLsServer).UpdateACL(ctx, req.(*UpdateACLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _ACLs_DeleteACL_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteACLRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(ACLsServer).DeleteACL(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.ACLs/DeleteACL",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(ACLsServer).DeleteACL(ctx, req.(*DeleteACLRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// ACLs_ServiceDesc is the grpc.ServiceDesc for ACLs service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var ACLs_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.eip.v1.ACLs",
	HandlerType: (*ACLsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListACLs",
			Handler:    _ACLs_ListACLs_Handler,
		},
		{
			MethodName: "GetACL",
			Handler:    _ACLs_GetACL_Handler,
		},
		{
			MethodName: "CreateACL",
			Handler:    _ACLs_CreateACL_Handler,
		},
		{
			MethodName: "UpdateACL",
			Handler:    _ACLs_UpdateACL_Handler,
		},
		{
			MethodName: "DeleteACL",
			Handler:    _ACLs_DeleteACL_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/eip/v1/acl.proto",
}
