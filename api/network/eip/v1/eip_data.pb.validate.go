// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/eip/v1/eip_data.proto

package eip

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListSlbTypedAvailableEIPsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListSlbTypedAvailableEIPsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSlbTypedAvailableEIPsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListSlbTypedAvailableEIPsRequestMultiError, or nil if none found.
func (m *ListSlbTypedAvailableEIPsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSlbTypedAvailableEIPsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for PageSize

	// no validation rules for PageToken

	if len(errors) > 0 {
		return ListSlbTypedAvailableEIPsRequestMultiError(errors)
	}

	return nil
}

// ListSlbTypedAvailableEIPsRequestMultiError is an error wrapping multiple
// validation errors returned by
// ListSlbTypedAvailableEIPsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListSlbTypedAvailableEIPsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSlbTypedAvailableEIPsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSlbTypedAvailableEIPsRequestMultiError) AllErrors() []error { return m }

// ListSlbTypedAvailableEIPsRequestValidationError is the validation error
// returned by ListSlbTypedAvailableEIPsRequest.Validate if the designated
// constraints aren't met.
type ListSlbTypedAvailableEIPsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSlbTypedAvailableEIPsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSlbTypedAvailableEIPsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSlbTypedAvailableEIPsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSlbTypedAvailableEIPsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSlbTypedAvailableEIPsRequestValidationError) ErrorName() string {
	return "ListSlbTypedAvailableEIPsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListSlbTypedAvailableEIPsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSlbTypedAvailableEIPsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSlbTypedAvailableEIPsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSlbTypedAvailableEIPsRequestValidationError{}

// Validate checks the field values on ListSlbTypedAvailableEIPsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *ListSlbTypedAvailableEIPsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSlbTypedAvailableEIPsResponse
// with the rules defined in the proto definition for this message. If any
// rules are violated, the result is a list of violation errors wrapped in
// ListSlbTypedAvailableEIPsResponseMultiError, or nil if none found.
func (m *ListSlbTypedAvailableEIPsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSlbTypedAvailableEIPsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetEips() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListSlbTypedAvailableEIPsResponseValidationError{
						field:  fmt.Sprintf("Eips[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListSlbTypedAvailableEIPsResponseValidationError{
						field:  fmt.Sprintf("Eips[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListSlbTypedAvailableEIPsResponseValidationError{
					field:  fmt.Sprintf("Eips[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListSlbTypedAvailableEIPsResponseMultiError(errors)
	}

	return nil
}

// ListSlbTypedAvailableEIPsResponseMultiError is an error wrapping multiple
// validation errors returned by
// ListSlbTypedAvailableEIPsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListSlbTypedAvailableEIPsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSlbTypedAvailableEIPsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSlbTypedAvailableEIPsResponseMultiError) AllErrors() []error { return m }

// ListSlbTypedAvailableEIPsResponseValidationError is the validation error
// returned by ListSlbTypedAvailableEIPsResponse.Validate if the designated
// constraints aren't met.
type ListSlbTypedAvailableEIPsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSlbTypedAvailableEIPsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSlbTypedAvailableEIPsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSlbTypedAvailableEIPsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSlbTypedAvailableEIPsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSlbTypedAvailableEIPsResponseValidationError) ErrorName() string {
	return "ListSlbTypedAvailableEIPsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListSlbTypedAvailableEIPsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSlbTypedAvailableEIPsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSlbTypedAvailableEIPsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSlbTypedAvailableEIPsResponseValidationError{}

// Validate checks the field values on UpdateEIPDstCIDRRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateEIPDstCIDRRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateEIPDstCIDRRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateEIPDstCIDRRequestMultiError, or nil if none found.
func (m *UpdateEIPDstCIDRRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateEIPDstCIDRRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for EipName

	if all {
		switch v := interface{}(m.GetEipDstCidr()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateEIPDstCIDRRequestValidationError{
					field:  "EipDstCidr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateEIPDstCIDRRequestValidationError{
					field:  "EipDstCidr",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEipDstCidr()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateEIPDstCIDRRequestValidationError{
				field:  "EipDstCidr",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateEIPDstCIDRRequestMultiError(errors)
	}

	return nil
}

// UpdateEIPDstCIDRRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateEIPDstCIDRRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateEIPDstCIDRRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateEIPDstCIDRRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateEIPDstCIDRRequestMultiError) AllErrors() []error { return m }

// UpdateEIPDstCIDRRequestValidationError is the validation error returned by
// UpdateEIPDstCIDRRequest.Validate if the designated constraints aren't met.
type UpdateEIPDstCIDRRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateEIPDstCIDRRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateEIPDstCIDRRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateEIPDstCIDRRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateEIPDstCIDRRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateEIPDstCIDRRequestValidationError) ErrorName() string {
	return "UpdateEIPDstCIDRRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateEIPDstCIDRRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateEIPDstCIDRRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateEIPDstCIDRRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateEIPDstCIDRRequestValidationError{}

// Validate checks the field values on EIPDstCidrInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EIPDstCidrInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EIPDstCidrInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EIPDstCidrInfoMultiError,
// or nil if none found.
func (m *EIPDstCidrInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EIPDstCidrInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if utf8.RuneCountInString(m.GetDstCidrs()) > 1024 {
		err := EIPDstCidrInfoValidationError{
			field:  "DstCidrs",
			reason: "value length must be at most 1024 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return EIPDstCidrInfoMultiError(errors)
	}

	return nil
}

// EIPDstCidrInfoMultiError is an error wrapping multiple validation errors
// returned by EIPDstCidrInfo.ValidateAll() if the designated constraints
// aren't met.
type EIPDstCidrInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EIPDstCidrInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EIPDstCidrInfoMultiError) AllErrors() []error { return m }

// EIPDstCidrInfoValidationError is the validation error returned by
// EIPDstCidrInfo.Validate if the designated constraints aren't met.
type EIPDstCidrInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EIPDstCidrInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EIPDstCidrInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EIPDstCidrInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EIPDstCidrInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EIPDstCidrInfoValidationError) ErrorName() string { return "EIPDstCidrInfoValidationError" }

// Error satisfies the builtin error interface
func (e EIPDstCidrInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEIPDstCidrInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EIPDstCidrInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EIPDstCidrInfoValidationError{}

// Validate checks the field values on EIPDstCIDR with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EIPDstCIDR) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EIPDstCIDR with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EIPDstCIDRMultiError, or
// nil if none found.
func (m *EIPDstCIDR) ValidateAll() error {
	return m.validate(true)
}

func (m *EIPDstCIDR) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for EipName

	// no validation rules for DstCidrs

	if len(errors) > 0 {
		return EIPDstCIDRMultiError(errors)
	}

	return nil
}

// EIPDstCIDRMultiError is an error wrapping multiple validation errors
// returned by EIPDstCIDR.ValidateAll() if the designated constraints aren't met.
type EIPDstCIDRMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EIPDstCIDRMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EIPDstCIDRMultiError) AllErrors() []error { return m }

// EIPDstCIDRValidationError is the validation error returned by
// EIPDstCIDR.Validate if the designated constraints aren't met.
type EIPDstCIDRValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EIPDstCIDRValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EIPDstCIDRValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EIPDstCIDRValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EIPDstCIDRValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EIPDstCIDRValidationError) ErrorName() string { return "EIPDstCIDRValidationError" }

// Error satisfies the builtin error interface
func (e EIPDstCIDRValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEIPDstCIDR.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EIPDstCIDRValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EIPDstCIDRValidationError{}
