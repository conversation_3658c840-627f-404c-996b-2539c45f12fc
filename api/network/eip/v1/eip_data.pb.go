// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/eip/v1/eip_data.proto

package eip

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/api/annotations"
	_ "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	_ "google.golang.org/protobuf/types/known/emptypb"
	_ "google.golang.org/protobuf/types/known/fieldmaskpb"
	_ "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// 列举符合请求的所有 SLB 类型的 EIPs.
// [EN] List requested slb-typed EIPs.
type ListSlbTypedAvailableEIPsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The maximum number of items to return.
	PageSize int32 `protobuf:"varint,4,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// The next_page_token value returned from a previous List request, if any.
	PageToken string `protobuf:"bytes,5,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
}

func (x *ListSlbTypedAvailableEIPsRequest) Reset() {
	*x = ListSlbTypedAvailableEIPsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSlbTypedAvailableEIPsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSlbTypedAvailableEIPsRequest) ProtoMessage() {}

func (x *ListSlbTypedAvailableEIPsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSlbTypedAvailableEIPsRequest.ProtoReflect.Descriptor instead.
func (*ListSlbTypedAvailableEIPsRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_data_proto_rawDescGZIP(), []int{0}
}

func (x *ListSlbTypedAvailableEIPsRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListSlbTypedAvailableEIPsRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListSlbTypedAvailableEIPsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListSlbTypedAvailableEIPsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListSlbTypedAvailableEIPsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// 列举符合请求的所有 SLB 类型的 EIPs.
// [EN] List requested slb-typed EIPs.
type ListSlbTypedAvailableEIPsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// EIP 列表.
	// [EN] EIP list.
	Eips []*EIP `protobuf:"bytes,1,rep,name=eips,proto3" json:"eips,omitempty"`
	// 下一个页面的 token，如果没有更多数据则为空.
	// [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListSlbTypedAvailableEIPsResponse) Reset() {
	*x = ListSlbTypedAvailableEIPsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_data_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSlbTypedAvailableEIPsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSlbTypedAvailableEIPsResponse) ProtoMessage() {}

func (x *ListSlbTypedAvailableEIPsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_data_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSlbTypedAvailableEIPsResponse.ProtoReflect.Descriptor instead.
func (*ListSlbTypedAvailableEIPsResponse) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_data_proto_rawDescGZIP(), []int{1}
}

func (x *ListSlbTypedAvailableEIPsResponse) GetEips() []*EIP {
	if x != nil {
		return x.Eips
	}
	return nil
}

func (x *ListSlbTypedAvailableEIPsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListSlbTypedAvailableEIPsResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// [zh] 更新内网EIP dst cidr 请求
// [en] update internal EIP dst cidr request
type UpdateEIPDstCIDRRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The EIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	EipName string `protobuf:"bytes,4,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
	// [zh] 企业内网 EIP snat dst cidr
	// [en] Internal EIP snat dst cidr
	EipDstCidr *EIPDstCidrInfo `protobuf:"bytes,5,opt,name=eip_dst_cidr,json=eipDstCidr,proto3" json:"eip_dst_cidr,omitempty"`
}

func (x *UpdateEIPDstCIDRRequest) Reset() {
	*x = UpdateEIPDstCIDRRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_data_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateEIPDstCIDRRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateEIPDstCIDRRequest) ProtoMessage() {}

func (x *UpdateEIPDstCIDRRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_data_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateEIPDstCIDRRequest.ProtoReflect.Descriptor instead.
func (*UpdateEIPDstCIDRRequest) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_data_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateEIPDstCIDRRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *UpdateEIPDstCIDRRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *UpdateEIPDstCIDRRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateEIPDstCIDRRequest) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

func (x *UpdateEIPDstCIDRRequest) GetEipDstCidr() *EIPDstCidrInfo {
	if x != nil {
		return x.EipDstCidr
	}
	return nil
}

type EIPDstCidrInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] EIP snat dst cidr
	// [en] EIP snat dst cidr
	DstCidrs string `protobuf:"bytes,1,opt,name=dst_cidrs,json=dstCidrs,proto3" json:"dst_cidrs,omitempty"`
}

func (x *EIPDstCidrInfo) Reset() {
	*x = EIPDstCidrInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_data_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EIPDstCidrInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EIPDstCidrInfo) ProtoMessage() {}

func (x *EIPDstCidrInfo) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_data_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EIPDstCidrInfo.ProtoReflect.Descriptor instead.
func (*EIPDstCidrInfo) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_data_proto_rawDescGZIP(), []int{3}
}

func (x *EIPDstCidrInfo) GetDstCidrs() string {
	if x != nil {
		return x.DstCidrs
	}
	return ""
}

// [zh] 更新内网EIP dst cidr响应
// [en] update internal EIP dst cidr response
type EIPDstCIDR struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] EIP 名
	// [en] EIP name
	EipName string `protobuf:"bytes,1,opt,name=eip_name,json=eipName,proto3" json:"eip_name,omitempty"`
	// [zh] 企业内网 EIP snat dst cidr
	// [en] Internal EIP snat dst cidr
	DstCidrs string `protobuf:"bytes,2,opt,name=dst_cidrs,json=dstCidrs,proto3" json:"dst_cidrs,omitempty"`
}

func (x *EIPDstCIDR) Reset() {
	*x = EIPDstCIDR{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_eip_v1_eip_data_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EIPDstCIDR) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EIPDstCIDR) ProtoMessage() {}

func (x *EIPDstCIDR) ProtoReflect() protoreflect.Message {
	mi := &file_network_eip_v1_eip_data_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EIPDstCIDR.ProtoReflect.Descriptor instead.
func (*EIPDstCIDR) Descriptor() ([]byte, []int) {
	return file_network_eip_v1_eip_data_proto_rawDescGZIP(), []int{4}
}

func (x *EIPDstCIDR) GetEipName() string {
	if x != nil {
		return x.EipName
	}
	return ""
}

func (x *EIPDstCIDR) GetDstCidrs() string {
	if x != nil {
		return x.DstCidrs
	}
	return ""
}

var File_network_eip_v1_eip_data_proto protoreflect.FileDescriptor

var file_network_eip_v1_eip_data_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x76, 0x31,
	0x2f, 0x65, 0x69, 0x70, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1d, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d,
	0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64,
	0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c,
	0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1f, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69,
	0x70, 0x2f, 0x76, 0x31, 0x2f, 0x65, 0x69, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xcf,
	0x01, 0x0a, 0x20, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x62, 0x54, 0x79, 0x70, 0x65, 0x64, 0x41,
	0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x49, 0x50, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e,
	0x22, 0xa2, 0x01, 0x0a, 0x21, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x62, 0x54, 0x79, 0x70, 0x65,
	0x64, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x49, 0x50, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x36, 0x0a, 0x04, 0x65, 0x69, 0x70, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x52, 0x04, 0x65, 0x69, 0x70, 0x73, 0x12, 0x26,
	0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65,
	0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67,
	0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xfb, 0x01, 0x0a, 0x17, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x45, 0x49, 0x50, 0x44, 0x73, 0x74, 0x43, 0x49, 0x44, 0x52, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e,
	0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12,
	0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x69, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x54, 0x0a,
	0x0c, 0x65, 0x69, 0x70, 0x5f, 0x64, 0x73, 0x74, 0x5f, 0x63, 0x69, 0x64, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x44, 0x73, 0x74, 0x43, 0x69, 0x64, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x65, 0x69, 0x70, 0x44, 0x73, 0x74, 0x43,
	0x69, 0x64, 0x72, 0x22, 0x37, 0x0a, 0x0e, 0x45, 0x49, 0x50, 0x44, 0x73, 0x74, 0x43, 0x69, 0x64,
	0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x25, 0x0a, 0x09, 0x64, 0x73, 0x74, 0x5f, 0x63, 0x69, 0x64,
	0x72, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x08, 0xfa, 0x42, 0x05, 0x72, 0x03, 0x18,
	0x80, 0x08, 0x52, 0x08, 0x64, 0x73, 0x74, 0x43, 0x69, 0x64, 0x72, 0x73, 0x22, 0x44, 0x0a, 0x0a,
	0x45, 0x49, 0x50, 0x44, 0x73, 0x74, 0x43, 0x49, 0x44, 0x52, 0x12, 0x19, 0x0a, 0x08, 0x65, 0x69,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x65, 0x69,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x73, 0x74, 0x5f, 0x63, 0x69, 0x64,
	0x72, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x73, 0x74, 0x43, 0x69, 0x64,
	0x72, 0x73, 0x32, 0xd6, 0x05, 0x0a, 0x0e, 0x45, 0x49, 0x50, 0x44, 0x61, 0x74, 0x61, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa6, 0x02, 0x0a, 0x19, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6c,
	0x62, 0x54, 0x79, 0x70, 0x65, 0x64, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x45,
	0x49, 0x50, 0x73, 0x12, 0x3f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x62, 0x54, 0x79, 0x70, 0x65, 0x64,
	0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x49, 0x50, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x40, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69,
	0x70, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x6c, 0x62, 0x54, 0x79, 0x70, 0x65,
	0x64, 0x41, 0x76, 0x61, 0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x49, 0x50, 0x73, 0x52, 0x65,
	0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x85, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7b, 0x12,
	0x79, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x64, 0x61,
	0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e,
	0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x41, 0x76, 0x61,
	0x69, 0x6c, 0x61, 0x62, 0x6c, 0x65, 0x45, 0x69, 0x70, 0x73, 0x80, 0xb5, 0x18, 0x01, 0x12, 0x9a,
	0x03, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x49, 0x50, 0x44, 0x73, 0x74, 0x43,
	0x49, 0x44, 0x52, 0x12, 0x36, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x45, 0x49, 0x50, 0x44, 0x73, 0x74,
	0x43, 0x49, 0x44, 0x52, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x29, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x65, 0x69, 0x70, 0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x44,
	0x73, 0x74, 0x43, 0x49, 0x44, 0x52, 0x22, 0xa2, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x9a, 0x01,
	0x32, 0x89, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f,
	0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a,
	0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73,
	0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x45, 0x69, 0x70, 0x44, 0x73, 0x74, 0x43, 0x69, 0x64, 0x72, 0x3a, 0x0c, 0x65, 0x69,
	0x70, 0x5f, 0x64, 0x73, 0x74, 0x5f, 0x63, 0x69, 0x64, 0x72, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5,
	0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a,
	0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x65, 0x69, 0x70, 0x73,
	0x2f, 0x7b, 0x65, 0x69, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x65, 0x69, 0x70,
	0x2e, 0x65, 0x69, 0x70, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x42, 0x50, 0x5a, 0x4e, 0x67,
	0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x62, 0x6a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x72,
	0x79, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2d, 0x70, 0x72,
	0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2f, 0x65, 0x69, 0x70, 0x2f, 0x76, 0x31, 0x3b, 0x65, 0x69, 0x70, 0x62, 0x06, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_network_eip_v1_eip_data_proto_rawDescOnce sync.Once
	file_network_eip_v1_eip_data_proto_rawDescData = file_network_eip_v1_eip_data_proto_rawDesc
)

func file_network_eip_v1_eip_data_proto_rawDescGZIP() []byte {
	file_network_eip_v1_eip_data_proto_rawDescOnce.Do(func() {
		file_network_eip_v1_eip_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_eip_v1_eip_data_proto_rawDescData)
	})
	return file_network_eip_v1_eip_data_proto_rawDescData
}

var file_network_eip_v1_eip_data_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_network_eip_v1_eip_data_proto_goTypes = []interface{}{
	(*ListSlbTypedAvailableEIPsRequest)(nil),  // 0: sensetime.core.network.eip.v1.ListSlbTypedAvailableEIPsRequest
	(*ListSlbTypedAvailableEIPsResponse)(nil), // 1: sensetime.core.network.eip.v1.ListSlbTypedAvailableEIPsResponse
	(*UpdateEIPDstCIDRRequest)(nil),           // 2: sensetime.core.network.eip.v1.UpdateEIPDstCIDRRequest
	(*EIPDstCidrInfo)(nil),                    // 3: sensetime.core.network.eip.v1.EIPDstCidrInfo
	(*EIPDstCIDR)(nil),                        // 4: sensetime.core.network.eip.v1.EIPDstCIDR
	(*EIP)(nil),                               // 5: sensetime.core.network.eip.v1.EIP
}
var file_network_eip_v1_eip_data_proto_depIdxs = []int32{
	5, // 0: sensetime.core.network.eip.v1.ListSlbTypedAvailableEIPsResponse.eips:type_name -> sensetime.core.network.eip.v1.EIP
	3, // 1: sensetime.core.network.eip.v1.UpdateEIPDstCIDRRequest.eip_dst_cidr:type_name -> sensetime.core.network.eip.v1.EIPDstCidrInfo
	0, // 2: sensetime.core.network.eip.v1.EIPDataService.ListSlbTypedAvailableEIPs:input_type -> sensetime.core.network.eip.v1.ListSlbTypedAvailableEIPsRequest
	2, // 3: sensetime.core.network.eip.v1.EIPDataService.UpdateEIPDstCIDR:input_type -> sensetime.core.network.eip.v1.UpdateEIPDstCIDRRequest
	1, // 4: sensetime.core.network.eip.v1.EIPDataService.ListSlbTypedAvailableEIPs:output_type -> sensetime.core.network.eip.v1.ListSlbTypedAvailableEIPsResponse
	4, // 5: sensetime.core.network.eip.v1.EIPDataService.UpdateEIPDstCIDR:output_type -> sensetime.core.network.eip.v1.EIPDstCIDR
	4, // [4:6] is the sub-list for method output_type
	2, // [2:4] is the sub-list for method input_type
	2, // [2:2] is the sub-list for extension type_name
	2, // [2:2] is the sub-list for extension extendee
	0, // [0:2] is the sub-list for field type_name
}

func init() { file_network_eip_v1_eip_data_proto_init() }
func file_network_eip_v1_eip_data_proto_init() {
	if File_network_eip_v1_eip_data_proto != nil {
		return
	}
	file_network_eip_v1_eip_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_network_eip_v1_eip_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSlbTypedAvailableEIPsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_data_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSlbTypedAvailableEIPsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_data_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateEIPDstCIDRRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_data_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EIPDstCidrInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_eip_v1_eip_data_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EIPDstCIDR); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_eip_v1_eip_data_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_network_eip_v1_eip_data_proto_goTypes,
		DependencyIndexes: file_network_eip_v1_eip_data_proto_depIdxs,
		MessageInfos:      file_network_eip_v1_eip_data_proto_msgTypes,
	}.Build()
	File_network_eip_v1_eip_data_proto = out.File
	file_network_eip_v1_eip_data_proto_rawDesc = nil
	file_network_eip_v1_eip_data_proto_goTypes = nil
	file_network_eip_v1_eip_data_proto_depIdxs = nil
}
