// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/eip/v1/eip.proto

package eip

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListEIPsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListEIPsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListEIPsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListEIPsRequestMultiError, or nil if none found.
func (m *ListEIPsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListEIPsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	// no validation rules for TenantId

	if len(errors) > 0 {
		return ListEIPsRequestMultiError(errors)
	}

	return nil
}

// ListEIPsRequestMultiError is an error wrapping multiple validation errors
// returned by ListEIPsRequest.ValidateAll() if the designated constraints
// aren't met.
type ListEIPsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListEIPsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListEIPsRequestMultiError) AllErrors() []error { return m }

// ListEIPsRequestValidationError is the validation error returned by
// ListEIPsRequest.Validate if the designated constraints aren't met.
type ListEIPsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListEIPsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListEIPsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListEIPsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListEIPsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListEIPsRequestValidationError) ErrorName() string { return "ListEIPsRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListEIPsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListEIPsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListEIPsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListEIPsRequestValidationError{}

// Validate checks the field values on GetEIPRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetEIPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEIPRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetEIPRequestMultiError, or
// nil if none found.
func (m *GetEIPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEIPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for EipName

	if len(errors) > 0 {
		return GetEIPRequestMultiError(errors)
	}

	return nil
}

// GetEIPRequestMultiError is an error wrapping multiple validation errors
// returned by GetEIPRequest.ValidateAll() if the designated constraints
// aren't met.
type GetEIPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEIPRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEIPRequestMultiError) AllErrors() []error { return m }

// GetEIPRequestValidationError is the validation error returned by
// GetEIPRequest.Validate if the designated constraints aren't met.
type GetEIPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEIPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEIPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEIPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEIPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEIPRequestValidationError) ErrorName() string { return "GetEIPRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetEIPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEIPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEIPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEIPRequestValidationError{}

// Validate checks the field values on GetEIPStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEIPStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEIPStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEIPStatusRequestMultiError, or nil if none found.
func (m *GetEIPStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEIPStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for EipName

	if len(errors) > 0 {
		return GetEIPStatusRequestMultiError(errors)
	}

	return nil
}

// GetEIPStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetEIPStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetEIPStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEIPStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEIPStatusRequestMultiError) AllErrors() []error { return m }

// GetEIPStatusRequestValidationError is the validation error returned by
// GetEIPStatusRequest.Validate if the designated constraints aren't met.
type GetEIPStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEIPStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEIPStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEIPStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEIPStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEIPStatusRequestValidationError) ErrorName() string {
	return "GetEIPStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEIPStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEIPStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEIPStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEIPStatusRequestValidationError{}

// Validate checks the field values on CreateEIPRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateEIPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateEIPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateEIPRequestMultiError, or nil if none found.
func (m *CreateEIPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateEIPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetEipName()) > 63 {
		err := CreateEIPRequestValidationError{
			field:  "EipName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateEIPRequest_EipName_Pattern.MatchString(m.GetEipName()) {
		err := CreateEIPRequestValidationError{
			field:  "EipName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetEip()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateEIPRequestValidationError{
					field:  "Eip",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateEIPRequestValidationError{
					field:  "Eip",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEip()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateEIPRequestValidationError{
				field:  "Eip",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateEIPRequestMultiError(errors)
	}

	return nil
}

// CreateEIPRequestMultiError is an error wrapping multiple validation errors
// returned by CreateEIPRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateEIPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateEIPRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateEIPRequestMultiError) AllErrors() []error { return m }

// CreateEIPRequestValidationError is the validation error returned by
// CreateEIPRequest.Validate if the designated constraints aren't met.
type CreateEIPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateEIPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateEIPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateEIPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateEIPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateEIPRequestValidationError) ErrorName() string { return "CreateEIPRequestValidationError" }

// Error satisfies the builtin error interface
func (e CreateEIPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateEIPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateEIPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateEIPRequestValidationError{}

var _CreateEIPRequest_EipName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on UpdateEIPRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateEIPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateEIPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateEIPRequestMultiError, or nil if none found.
func (m *UpdateEIPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateEIPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for EipName

	if all {
		switch v := interface{}(m.GetEip()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateEIPRequestValidationError{
					field:  "Eip",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateEIPRequestValidationError{
					field:  "Eip",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEip()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateEIPRequestValidationError{
				field:  "Eip",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateEIPRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateEIPRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateEIPRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateEIPRequestMultiError(errors)
	}

	return nil
}

// UpdateEIPRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateEIPRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateEIPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateEIPRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateEIPRequestMultiError) AllErrors() []error { return m }

// UpdateEIPRequestValidationError is the validation error returned by
// UpdateEIPRequest.Validate if the designated constraints aren't met.
type UpdateEIPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateEIPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateEIPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateEIPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateEIPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateEIPRequestValidationError) ErrorName() string { return "UpdateEIPRequestValidationError" }

// Error satisfies the builtin error interface
func (e UpdateEIPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateEIPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateEIPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateEIPRequestValidationError{}

// Validate checks the field values on ExpireStopEIPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ExpireStopEIPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ExpireStopEIPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ExpireStopEIPRequestMultiError, or nil if none found.
func (m *ExpireStopEIPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ExpireStopEIPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for EipName

	if len(errors) > 0 {
		return ExpireStopEIPRequestMultiError(errors)
	}

	return nil
}

// ExpireStopEIPRequestMultiError is an error wrapping multiple validation
// errors returned by ExpireStopEIPRequest.ValidateAll() if the designated
// constraints aren't met.
type ExpireStopEIPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ExpireStopEIPRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ExpireStopEIPRequestMultiError) AllErrors() []error { return m }

// ExpireStopEIPRequestValidationError is the validation error returned by
// ExpireStopEIPRequest.Validate if the designated constraints aren't met.
type ExpireStopEIPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ExpireStopEIPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ExpireStopEIPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ExpireStopEIPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ExpireStopEIPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ExpireStopEIPRequestValidationError) ErrorName() string {
	return "ExpireStopEIPRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ExpireStopEIPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sExpireStopEIPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ExpireStopEIPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ExpireStopEIPRequestValidationError{}

// Validate checks the field values on RenewStartEIPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *RenewStartEIPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on RenewStartEIPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// RenewStartEIPRequestMultiError, or nil if none found.
func (m *RenewStartEIPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *RenewStartEIPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for EipName

	if len(errors) > 0 {
		return RenewStartEIPRequestMultiError(errors)
	}

	return nil
}

// RenewStartEIPRequestMultiError is an error wrapping multiple validation
// errors returned by RenewStartEIPRequest.ValidateAll() if the designated
// constraints aren't met.
type RenewStartEIPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m RenewStartEIPRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m RenewStartEIPRequestMultiError) AllErrors() []error { return m }

// RenewStartEIPRequestValidationError is the validation error returned by
// RenewStartEIPRequest.Validate if the designated constraints aren't met.
type RenewStartEIPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e RenewStartEIPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e RenewStartEIPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e RenewStartEIPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e RenewStartEIPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e RenewStartEIPRequestValidationError) ErrorName() string {
	return "RenewStartEIPRequestValidationError"
}

// Error satisfies the builtin error interface
func (e RenewStartEIPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sRenewStartEIPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = RenewStartEIPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = RenewStartEIPRequestValidationError{}

// Validate checks the field values on ReleaseEIPRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReleaseEIPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReleaseEIPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReleaseEIPRequestMultiError, or nil if none found.
func (m *ReleaseEIPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReleaseEIPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for EipName

	if len(errors) > 0 {
		return ReleaseEIPRequestMultiError(errors)
	}

	return nil
}

// ReleaseEIPRequestMultiError is an error wrapping multiple validation errors
// returned by ReleaseEIPRequest.ValidateAll() if the designated constraints
// aren't met.
type ReleaseEIPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReleaseEIPRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReleaseEIPRequestMultiError) AllErrors() []error { return m }

// ReleaseEIPRequestValidationError is the validation error returned by
// ReleaseEIPRequest.Validate if the designated constraints aren't met.
type ReleaseEIPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReleaseEIPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReleaseEIPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReleaseEIPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReleaseEIPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReleaseEIPRequestValidationError) ErrorName() string {
	return "ReleaseEIPRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReleaseEIPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReleaseEIPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReleaseEIPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReleaseEIPRequestValidationError{}

// Validate checks the field values on ForceDeleteEIPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForceDeleteEIPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForceDeleteEIPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ForceDeleteEIPRequestMultiError, or nil if none found.
func (m *ForceDeleteEIPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ForceDeleteEIPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for EipName

	if len(errors) > 0 {
		return ForceDeleteEIPRequestMultiError(errors)
	}

	return nil
}

// ForceDeleteEIPRequestMultiError is an error wrapping multiple validation
// errors returned by ForceDeleteEIPRequest.ValidateAll() if the designated
// constraints aren't met.
type ForceDeleteEIPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForceDeleteEIPRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForceDeleteEIPRequestMultiError) AllErrors() []error { return m }

// ForceDeleteEIPRequestValidationError is the validation error returned by
// ForceDeleteEIPRequest.Validate if the designated constraints aren't met.
type ForceDeleteEIPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForceDeleteEIPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForceDeleteEIPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForceDeleteEIPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForceDeleteEIPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForceDeleteEIPRequestValidationError) ErrorName() string {
	return "ForceDeleteEIPRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ForceDeleteEIPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForceDeleteEIPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForceDeleteEIPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForceDeleteEIPRequestValidationError{}

// Validate checks the field values on DeleteEIPRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteEIPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteEIPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteEIPRequestMultiError, or nil if none found.
func (m *DeleteEIPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteEIPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for EipName

	if len(errors) > 0 {
		return DeleteEIPRequestMultiError(errors)
	}

	return nil
}

// DeleteEIPRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteEIPRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteEIPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteEIPRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteEIPRequestMultiError) AllErrors() []error { return m }

// DeleteEIPRequestValidationError is the validation error returned by
// DeleteEIPRequest.Validate if the designated constraints aren't met.
type DeleteEIPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteEIPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteEIPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteEIPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteEIPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteEIPRequestValidationError) ErrorName() string { return "DeleteEIPRequestValidationError" }

// Error satisfies the builtin error interface
func (e DeleteEIPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteEIPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteEIPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteEIPRequestValidationError{}

// Validate checks the field values on ResizeEIPRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ResizeEIPRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResizeEIPRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResizeEIPRequestMultiError, or nil if none found.
func (m *ResizeEIPRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResizeEIPRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetEipName()) > 63 {
		err := ResizeEIPRequestValidationError{
			field:  "EipName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ResizeEIPRequest_EipName_Pattern.MatchString(m.GetEipName()) {
		err := ResizeEIPRequestValidationError{
			field:  "EipName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetEipResize()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResizeEIPRequestValidationError{
					field:  "EipResize",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResizeEIPRequestValidationError{
					field:  "EipResize",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEipResize()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResizeEIPRequestValidationError{
				field:  "EipResize",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResizeEIPRequestMultiError(errors)
	}

	return nil
}

// ResizeEIPRequestMultiError is an error wrapping multiple validation errors
// returned by ResizeEIPRequest.ValidateAll() if the designated constraints
// aren't met.
type ResizeEIPRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResizeEIPRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResizeEIPRequestMultiError) AllErrors() []error { return m }

// ResizeEIPRequestValidationError is the validation error returned by
// ResizeEIPRequest.Validate if the designated constraints aren't met.
type ResizeEIPRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResizeEIPRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResizeEIPRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResizeEIPRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResizeEIPRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResizeEIPRequestValidationError) ErrorName() string { return "ResizeEIPRequestValidationError" }

// Error satisfies the builtin error interface
func (e ResizeEIPRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResizeEIPRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResizeEIPRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResizeEIPRequestValidationError{}

var _ResizeEIPRequest_EipName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on EIPResize with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EIPResize) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EIPResize with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EIPResizeMultiError, or nil
// if none found.
func (m *EIPResize) ValidateAll() error {
	return m.validate(true)
}

func (m *EIPResize) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ResourceId

	// no validation rules for SkuId

	// no validation rules for OperatorId

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EIPResizeValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EIPResizeValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EIPResizeValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EIPResizeValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EIPResizeValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EIPResizeValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EIPResizeMultiError(errors)
	}

	return nil
}

// EIPResizeMultiError is an error wrapping multiple validation errors returned
// by EIPResize.ValidateAll() if the designated constraints aren't met.
type EIPResizeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EIPResizeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EIPResizeMultiError) AllErrors() []error { return m }

// EIPResizeValidationError is the validation error returned by
// EIPResize.Validate if the designated constraints aren't met.
type EIPResizeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EIPResizeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EIPResizeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EIPResizeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EIPResizeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EIPResizeValidationError) ErrorName() string { return "EIPResizeValidationError" }

// Error satisfies the builtin error interface
func (e EIPResizeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEIPResize.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EIPResizeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EIPResizeValidationError{}

// Validate checks the field values on SnatStatus with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SnatStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SnatStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SnatStatusMultiError, or
// nil if none found.
func (m *SnatStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *SnatStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for InnerIp

	// no validation rules for OuterIp

	// no validation rules for Policy

	// no validation rules for PolicyValue

	if len(errors) > 0 {
		return SnatStatusMultiError(errors)
	}

	return nil
}

// SnatStatusMultiError is an error wrapping multiple validation errors
// returned by SnatStatus.ValidateAll() if the designated constraints aren't met.
type SnatStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SnatStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SnatStatusMultiError) AllErrors() []error { return m }

// SnatStatusValidationError is the validation error returned by
// SnatStatus.Validate if the designated constraints aren't met.
type SnatStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SnatStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SnatStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SnatStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SnatStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SnatStatusValidationError) ErrorName() string { return "SnatStatusValidationError" }

// Error satisfies the builtin error interface
func (e SnatStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSnatStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SnatStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SnatStatusValidationError{}

// Validate checks the field values on EIPStatus with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EIPStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EIPStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EIPStatusMultiError, or nil
// if none found.
func (m *EIPStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *EIPStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetEipInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EIPStatusValidationError{
					field:  "EipInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EIPStatusValidationError{
					field:  "EipInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEipInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EIPStatusValidationError{
				field:  "EipInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for EipIp

	if all {
		switch v := interface{}(m.GetVpcInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EIPStatusValidationError{
					field:  "VpcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EIPStatusValidationError{
					field:  "VpcInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVpcInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EIPStatusValidationError{
				field:  "VpcInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for SnatCount

	// no validation rules for DnatCount

	for idx, item := range m.GetSnatInfo() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EIPStatusValidationError{
						field:  fmt.Sprintf("SnatInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EIPStatusValidationError{
						field:  fmt.Sprintf("SnatInfo[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EIPStatusValidationError{
					field:  fmt.Sprintf("SnatInfo[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EIPStatusMultiError(errors)
	}

	return nil
}

// EIPStatusMultiError is an error wrapping multiple validation errors returned
// by EIPStatus.ValidateAll() if the designated constraints aren't met.
type EIPStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EIPStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EIPStatusMultiError) AllErrors() []error { return m }

// EIPStatusValidationError is the validation error returned by
// EIPStatus.Validate if the designated constraints aren't met.
type EIPStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EIPStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EIPStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EIPStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EIPStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EIPStatusValidationError) ErrorName() string { return "EIPStatusValidationError" }

// Error satisfies the builtin error interface
func (e EIPStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEIPStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EIPStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EIPStatusValidationError{}

// Validate checks the field values on EIPMetrics with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EIPMetrics) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EIPMetrics with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EIPMetricsMultiError, or
// nil if none found.
func (m *EIPMetrics) ValidateAll() error {
	return m.validate(true)
}

func (m *EIPMetrics) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetResourceMetrics() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EIPMetricsValidationError{
						field:  fmt.Sprintf("ResourceMetrics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EIPMetricsValidationError{
						field:  fmt.Sprintf("ResourceMetrics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EIPMetricsValidationError{
					field:  fmt.Sprintf("ResourceMetrics[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return EIPMetricsMultiError(errors)
	}

	return nil
}

// EIPMetricsMultiError is an error wrapping multiple validation errors
// returned by EIPMetrics.ValidateAll() if the designated constraints aren't met.
type EIPMetricsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EIPMetricsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EIPMetricsMultiError) AllErrors() []error { return m }

// EIPMetricsValidationError is the validation error returned by
// EIPMetrics.Validate if the designated constraints aren't met.
type EIPMetricsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EIPMetricsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EIPMetricsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EIPMetricsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EIPMetricsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EIPMetricsValidationError) ErrorName() string { return "EIPMetricsValidationError" }

// Error satisfies the builtin error interface
func (e EIPMetricsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEIPMetrics.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EIPMetricsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EIPMetricsValidationError{}

// Validate checks the field values on ResourceMetrics with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ResourceMetrics) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResourceMetrics with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResourceMetricsMultiError, or nil if none found.
func (m *ResourceMetrics) ValidateAll() error {
	return m.validate(true)
}

func (m *ResourceMetrics) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for Value

	// no validation rules for Unit

	// no validation rules for Color

	if len(errors) > 0 {
		return ResourceMetricsMultiError(errors)
	}

	return nil
}

// ResourceMetricsMultiError is an error wrapping multiple validation errors
// returned by ResourceMetrics.ValidateAll() if the designated constraints
// aren't met.
type ResourceMetricsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourceMetricsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourceMetricsMultiError) AllErrors() []error { return m }

// ResourceMetricsValidationError is the validation error returned by
// ResourceMetrics.Validate if the designated constraints aren't met.
type ResourceMetricsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourceMetricsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourceMetricsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourceMetricsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourceMetricsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourceMetricsValidationError) ErrorName() string { return "ResourceMetricsValidationError" }

// Error satisfies the builtin error interface
func (e ResourceMetricsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResourceMetrics.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourceMetricsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourceMetricsValidationError{}

// Validate checks the field values on VPCStatus with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VPCStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VPCStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VPCStatusMultiError, or nil
// if none found.
func (m *VPCStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *VPCStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for DisplayName

	// no validation rules for Id

	if len(errors) > 0 {
		return VPCStatusMultiError(errors)
	}

	return nil
}

// VPCStatusMultiError is an error wrapping multiple validation errors returned
// by VPCStatus.ValidateAll() if the designated constraints aren't met.
type VPCStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VPCStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VPCStatusMultiError) AllErrors() []error { return m }

// VPCStatusValidationError is the validation error returned by
// VPCStatus.Validate if the designated constraints aren't met.
type VPCStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VPCStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VPCStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VPCStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VPCStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VPCStatusValidationError) ErrorName() string { return "VPCStatusValidationError" }

// Error satisfies the builtin error interface
func (e VPCStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVPCStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VPCStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VPCStatusValidationError{}

// Validate checks the field values on GetEIPMetricsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetEIPMetricsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetEIPMetricsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetEIPMetricsRequestMultiError, or nil if none found.
func (m *GetEIPMetricsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetEIPMetricsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return GetEIPMetricsRequestMultiError(errors)
	}

	return nil
}

// GetEIPMetricsRequestMultiError is an error wrapping multiple validation
// errors returned by GetEIPMetricsRequest.ValidateAll() if the designated
// constraints aren't met.
type GetEIPMetricsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetEIPMetricsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetEIPMetricsRequestMultiError) AllErrors() []error { return m }

// GetEIPMetricsRequestValidationError is the validation error returned by
// GetEIPMetricsRequest.Validate if the designated constraints aren't met.
type GetEIPMetricsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetEIPMetricsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetEIPMetricsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetEIPMetricsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetEIPMetricsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetEIPMetricsRequestValidationError) ErrorName() string {
	return "GetEIPMetricsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetEIPMetricsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetEIPMetricsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetEIPMetricsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetEIPMetricsRequestValidationError{}

// Validate checks the field values on ListEIPsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListEIPsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListEIPsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListEIPsResponseMultiError, or nil if none found.
func (m *ListEIPsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListEIPsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetEips() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListEIPsResponseValidationError{
						field:  fmt.Sprintf("Eips[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListEIPsResponseValidationError{
						field:  fmt.Sprintf("Eips[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListEIPsResponseValidationError{
					field:  fmt.Sprintf("Eips[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListEIPsResponseMultiError(errors)
	}

	return nil
}

// ListEIPsResponseMultiError is an error wrapping multiple validation errors
// returned by ListEIPsResponse.ValidateAll() if the designated constraints
// aren't met.
type ListEIPsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListEIPsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListEIPsResponseMultiError) AllErrors() []error { return m }

// ListEIPsResponseValidationError is the validation error returned by
// ListEIPsResponse.Validate if the designated constraints aren't met.
type ListEIPsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListEIPsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListEIPsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListEIPsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListEIPsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListEIPsResponseValidationError) ErrorName() string { return "ListEIPsResponseValidationError" }

// Error satisfies the builtin error interface
func (e ListEIPsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListEIPsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListEIPsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListEIPsResponseValidationError{}

// Validate checks the field values on EIP with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *EIP) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EIP with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in EIPMultiError, or nil if none found.
func (m *EIP) ValidateAll() error {
	return m.validate(true)
}

func (m *EIP) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if utf8.RuneCountInString(m.GetDisplayName()) > 25 {
		err := EIPValidationError{
			field:  "DisplayName",
			reason: "value length must be at most 25 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for Uid

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EIPValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EIPValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EIPValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EIPValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EIPValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EIPValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EIPValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EIPValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EIPValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EIPValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EIPValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EIPValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return EIPMultiError(errors)
	}

	return nil
}

// EIPMultiError is an error wrapping multiple validation errors returned by
// EIP.ValidateAll() if the designated constraints aren't met.
type EIPMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EIPMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EIPMultiError) AllErrors() []error { return m }

// EIPValidationError is the validation error returned by EIP.Validate if the
// designated constraints aren't met.
type EIPValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EIPValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EIPValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EIPValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EIPValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EIPValidationError) ErrorName() string { return "EIPValidationError" }

// Error satisfies the builtin error interface
func (e EIPValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEIP.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EIPValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EIPValidationError{}

// Validate checks the field values on EIPProperties with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EIPProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EIPProperties with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in EIPPropertiesMultiError, or
// nil if none found.
func (m *EIPProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *EIPProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VpcId

	// no validation rules for AssociationId

	// no validation rules for AssociationType

	if all {
		switch v := interface{}(m.GetResources()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EIPPropertiesValidationError{
					field:  "Resources",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EIPPropertiesValidationError{
					field:  "Resources",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResources()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EIPPropertiesValidationError{
				field:  "Resources",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Sku

	// no validation rules for DefaultSnat

	// no validation rules for AclEnabled

	for idx, item := range m.GetAcls() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, EIPPropertiesValidationError{
						field:  fmt.Sprintf("Acls[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, EIPPropertiesValidationError{
						field:  fmt.Sprintf("Acls[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return EIPPropertiesValidationError{
					field:  fmt.Sprintf("Acls[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for InternalEip

	// no validation rules for SnatDstCidrEnabled

	if len(errors) > 0 {
		return EIPPropertiesMultiError(errors)
	}

	return nil
}

// EIPPropertiesMultiError is an error wrapping multiple validation errors
// returned by EIPProperties.ValidateAll() if the designated constraints
// aren't met.
type EIPPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EIPPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EIPPropertiesMultiError) AllErrors() []error { return m }

// EIPPropertiesValidationError is the validation error returned by
// EIPProperties.Validate if the designated constraints aren't met.
type EIPPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EIPPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EIPPropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EIPPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EIPPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EIPPropertiesValidationError) ErrorName() string { return "EIPPropertiesValidationError" }

// Error satisfies the builtin error interface
func (e EIPPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEIPProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EIPPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EIPPropertiesValidationError{}

// Validate checks the field values on EIPACL with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EIPACL) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EIPACL with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in EIPACLMultiError, or nil if none found.
func (m *EIPACL) ValidateAll() error {
	return m.validate(true)
}

func (m *EIPACL) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for AclName

	if all {
		switch v := interface{}(m.GetAclProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, EIPACLValidationError{
					field:  "AclProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, EIPACLValidationError{
					field:  "AclProperties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetAclProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return EIPACLValidationError{
				field:  "AclProperties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for AclState

	if len(errors) > 0 {
		return EIPACLMultiError(errors)
	}

	return nil
}

// EIPACLMultiError is an error wrapping multiple validation errors returned by
// EIPACL.ValidateAll() if the designated constraints aren't met.
type EIPACLMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EIPACLMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EIPACLMultiError) AllErrors() []error { return m }

// EIPACLValidationError is the validation error returned by EIPACL.Validate if
// the designated constraints aren't met.
type EIPACLValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EIPACLValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EIPACLValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EIPACLValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EIPACLValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EIPACLValidationError) ErrorName() string { return "EIPACLValidationError" }

// Error satisfies the builtin error interface
func (e EIPACLValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEIPACL.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EIPACLValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EIPACLValidationError{}

// Validate checks the field values on Resources with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Resources) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Resources with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ResourcesMultiError, or nil
// if none found.
func (m *Resources) ValidateAll() error {
	return m.validate(true)
}

func (m *Resources) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBillingItems()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResourcesValidationError{
					field:  "BillingItems",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResourcesValidationError{
					field:  "BillingItems",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBillingItems()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResourcesValidationError{
				field:  "BillingItems",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetLimitRateItems()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResourcesValidationError{
					field:  "LimitRateItems",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResourcesValidationError{
					field:  "LimitRateItems",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLimitRateItems()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResourcesValidationError{
				field:  "LimitRateItems",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResourcesMultiError(errors)
	}

	return nil
}

// ResourcesMultiError is an error wrapping multiple validation errors returned
// by Resources.ValidateAll() if the designated constraints aren't met.
type ResourcesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourcesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourcesMultiError) AllErrors() []error { return m }

// ResourcesValidationError is the validation error returned by
// Resources.Validate if the designated constraints aren't met.
type ResourcesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourcesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourcesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourcesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourcesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourcesValidationError) ErrorName() string { return "ResourcesValidationError" }

// Error satisfies the builtin error interface
func (e ResourcesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResources.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourcesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourcesValidationError{}

// Validate checks the field values on BillingItems with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BillingItems) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BillingItems with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BillingItemsMultiError, or
// nil if none found.
func (m *BillingItems) ValidateAll() error {
	return m.validate(true)
}

func (m *BillingItems) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Bw

	if len(errors) > 0 {
		return BillingItemsMultiError(errors)
	}

	return nil
}

// BillingItemsMultiError is an error wrapping multiple validation errors
// returned by BillingItems.ValidateAll() if the designated constraints aren't met.
type BillingItemsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BillingItemsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BillingItemsMultiError) AllErrors() []error { return m }

// BillingItemsValidationError is the validation error returned by
// BillingItems.Validate if the designated constraints aren't met.
type BillingItemsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BillingItemsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BillingItemsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BillingItemsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BillingItemsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BillingItemsValidationError) ErrorName() string { return "BillingItemsValidationError" }

// Error satisfies the builtin error interface
func (e BillingItemsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBillingItems.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BillingItemsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BillingItemsValidationError{}

// Validate checks the field values on LimitRateItems with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LimitRateItems) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LimitRateItems with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LimitRateItemsMultiError,
// or nil if none found.
func (m *LimitRateItems) ValidateAll() error {
	return m.validate(true)
}

func (m *LimitRateItems) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UpStreamBw

	// no validation rules for DownStreamBw

	if len(errors) > 0 {
		return LimitRateItemsMultiError(errors)
	}

	return nil
}

// LimitRateItemsMultiError is an error wrapping multiple validation errors
// returned by LimitRateItems.ValidateAll() if the designated constraints
// aren't met.
type LimitRateItemsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LimitRateItemsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LimitRateItemsMultiError) AllErrors() []error { return m }

// LimitRateItemsValidationError is the validation error returned by
// LimitRateItems.Validate if the designated constraints aren't met.
type LimitRateItemsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LimitRateItemsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LimitRateItemsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LimitRateItemsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LimitRateItemsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LimitRateItemsValidationError) ErrorName() string { return "LimitRateItemsValidationError" }

// Error satisfies the builtin error interface
func (e LimitRateItemsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLimitRateItems.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LimitRateItemsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LimitRateItemsValidationError{}
