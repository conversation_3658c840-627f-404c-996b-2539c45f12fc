// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/eip/v1/dnatRule.proto

package eip

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DNATRulesClient is the client API for DNATRules service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DNATRulesClient interface {
	// 列举符合请求的所有 DNATRules.
	// [EN] List requested DNATRules.
	ListDNATRules(ctx context.Context, in *ListDNATRulesRequest, opts ...grpc.CallOption) (*ListDNATRulesResponse, error)
	// 列举符合请求的所有EIP的 DNATRules.
	// [EN] List requested multi-eip DNATRules.
	ListMDNATRules(ctx context.Context, in *ListMDNATRulesRequest, opts ...grpc.CallOption) (*ListDNATRulesResponse, error)
	// 获取符合请求的一个 DNATRule.
	// [EN] Get a requested DNATRule.
	GetDNATRule(ctx context.Context, in *GetDNATRuleRequest, opts ...grpc.CallOption) (*DNATRule, error)
	// 创建一个 DNATRule.
	// [EN] Create a DNATRule.
	CreateDNATRule(ctx context.Context, in *CreateDNATRuleRequest, opts ...grpc.CallOption) (*DNATRule, error)
	// 绑定一个 DNATRule和计算实例.
	// [EN] bind a DNATRule with a instance.
	BindDNATRule(ctx context.Context, in *BindDNATRuleRequest, opts ...grpc.CallOption) (*DNATRule, error)
	// 解绑一个 DNATRule.
	// [EN] unbind the DNATRule.
	UnbindDNATRule(ctx context.Context, in *UnbindDNATRuleRequest, opts ...grpc.CallOption) (*DNATRule, error)
	// 批量解绑DNATRule.
	// [EN] unbind DNATRules.
	UnbindDNATRules(ctx context.Context, in *UnbindDNATRulesRequest, opts ...grpc.CallOption) (*ListDNATRulesResponse, error)
	// 更新一个 DNATRule.
	// [EN] update the DNATRule.
	UpdateDNATRule(ctx context.Context, in *UpdateDNATRuleRequest, opts ...grpc.CallOption) (*DNATRule, error)
	// 删除一个 DNATRule.
	// [EN] Delete a DNATRule.
	DeleteDNATRule(ctx context.Context, in *DeleteDNATRuleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type dNATRulesClient struct {
	cc grpc.ClientConnInterface
}

func NewDNATRulesClient(cc grpc.ClientConnInterface) DNATRulesClient {
	return &dNATRulesClient{cc}
}

func (c *dNATRulesClient) ListDNATRules(ctx context.Context, in *ListDNATRulesRequest, opts ...grpc.CallOption) (*ListDNATRulesResponse, error) {
	out := new(ListDNATRulesResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.DNATRules/ListDNATRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dNATRulesClient) ListMDNATRules(ctx context.Context, in *ListMDNATRulesRequest, opts ...grpc.CallOption) (*ListDNATRulesResponse, error) {
	out := new(ListDNATRulesResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.DNATRules/ListMDNATRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dNATRulesClient) GetDNATRule(ctx context.Context, in *GetDNATRuleRequest, opts ...grpc.CallOption) (*DNATRule, error) {
	out := new(DNATRule)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.DNATRules/GetDNATRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dNATRulesClient) CreateDNATRule(ctx context.Context, in *CreateDNATRuleRequest, opts ...grpc.CallOption) (*DNATRule, error) {
	out := new(DNATRule)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.DNATRules/CreateDNATRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dNATRulesClient) BindDNATRule(ctx context.Context, in *BindDNATRuleRequest, opts ...grpc.CallOption) (*DNATRule, error) {
	out := new(DNATRule)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.DNATRules/BindDNATRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dNATRulesClient) UnbindDNATRule(ctx context.Context, in *UnbindDNATRuleRequest, opts ...grpc.CallOption) (*DNATRule, error) {
	out := new(DNATRule)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.DNATRules/UnbindDNATRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dNATRulesClient) UnbindDNATRules(ctx context.Context, in *UnbindDNATRulesRequest, opts ...grpc.CallOption) (*ListDNATRulesResponse, error) {
	out := new(ListDNATRulesResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.DNATRules/UnbindDNATRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dNATRulesClient) UpdateDNATRule(ctx context.Context, in *UpdateDNATRuleRequest, opts ...grpc.CallOption) (*DNATRule, error) {
	out := new(DNATRule)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.DNATRules/UpdateDNATRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dNATRulesClient) DeleteDNATRule(ctx context.Context, in *DeleteDNATRuleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.eip.v1.DNATRules/DeleteDNATRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DNATRulesServer is the server API for DNATRules service.
// All implementations must embed UnimplementedDNATRulesServer
// for forward compatibility
type DNATRulesServer interface {
	// 列举符合请求的所有 DNATRules.
	// [EN] List requested DNATRules.
	ListDNATRules(context.Context, *ListDNATRulesRequest) (*ListDNATRulesResponse, error)
	// 列举符合请求的所有EIP的 DNATRules.
	// [EN] List requested multi-eip DNATRules.
	ListMDNATRules(context.Context, *ListMDNATRulesRequest) (*ListDNATRulesResponse, error)
	// 获取符合请求的一个 DNATRule.
	// [EN] Get a requested DNATRule.
	GetDNATRule(context.Context, *GetDNATRuleRequest) (*DNATRule, error)
	// 创建一个 DNATRule.
	// [EN] Create a DNATRule.
	CreateDNATRule(context.Context, *CreateDNATRuleRequest) (*DNATRule, error)
	// 绑定一个 DNATRule和计算实例.
	// [EN] bind a DNATRule with a instance.
	BindDNATRule(context.Context, *BindDNATRuleRequest) (*DNATRule, error)
	// 解绑一个 DNATRule.
	// [EN] unbind the DNATRule.
	UnbindDNATRule(context.Context, *UnbindDNATRuleRequest) (*DNATRule, error)
	// 批量解绑DNATRule.
	// [EN] unbind DNATRules.
	UnbindDNATRules(context.Context, *UnbindDNATRulesRequest) (*ListDNATRulesResponse, error)
	// 更新一个 DNATRule.
	// [EN] update the DNATRule.
	UpdateDNATRule(context.Context, *UpdateDNATRuleRequest) (*DNATRule, error)
	// 删除一个 DNATRule.
	// [EN] Delete a DNATRule.
	DeleteDNATRule(context.Context, *DeleteDNATRuleRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedDNATRulesServer()
}

// UnimplementedDNATRulesServer must be embedded to have forward compatible implementations.
type UnimplementedDNATRulesServer struct {
}

func (UnimplementedDNATRulesServer) ListDNATRules(context.Context, *ListDNATRulesRequest) (*ListDNATRulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDNATRules not implemented")
}
func (UnimplementedDNATRulesServer) ListMDNATRules(context.Context, *ListMDNATRulesRequest) (*ListDNATRulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListMDNATRules not implemented")
}
func (UnimplementedDNATRulesServer) GetDNATRule(context.Context, *GetDNATRuleRequest) (*DNATRule, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDNATRule not implemented")
}
func (UnimplementedDNATRulesServer) CreateDNATRule(context.Context, *CreateDNATRuleRequest) (*DNATRule, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDNATRule not implemented")
}
func (UnimplementedDNATRulesServer) BindDNATRule(context.Context, *BindDNATRuleRequest) (*DNATRule, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindDNATRule not implemented")
}
func (UnimplementedDNATRulesServer) UnbindDNATRule(context.Context, *UnbindDNATRuleRequest) (*DNATRule, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnbindDNATRule not implemented")
}
func (UnimplementedDNATRulesServer) UnbindDNATRules(context.Context, *UnbindDNATRulesRequest) (*ListDNATRulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnbindDNATRules not implemented")
}
func (UnimplementedDNATRulesServer) UpdateDNATRule(context.Context, *UpdateDNATRuleRequest) (*DNATRule, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDNATRule not implemented")
}
func (UnimplementedDNATRulesServer) DeleteDNATRule(context.Context, *DeleteDNATRuleRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDNATRule not implemented")
}
func (UnimplementedDNATRulesServer) mustEmbedUnimplementedDNATRulesServer() {}

// UnsafeDNATRulesServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DNATRulesServer will
// result in compilation errors.
type UnsafeDNATRulesServer interface {
	mustEmbedUnimplementedDNATRulesServer()
}

func RegisterDNATRulesServer(s grpc.ServiceRegistrar, srv DNATRulesServer) {
	s.RegisterService(&DNATRules_ServiceDesc, srv)
}

func _DNATRules_ListDNATRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDNATRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DNATRulesServer).ListDNATRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.DNATRules/ListDNATRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DNATRulesServer).ListDNATRules(ctx, req.(*ListDNATRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DNATRules_ListMDNATRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListMDNATRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DNATRulesServer).ListMDNATRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.DNATRules/ListMDNATRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DNATRulesServer).ListMDNATRules(ctx, req.(*ListMDNATRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DNATRules_GetDNATRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDNATRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DNATRulesServer).GetDNATRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.DNATRules/GetDNATRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DNATRulesServer).GetDNATRule(ctx, req.(*GetDNATRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DNATRules_CreateDNATRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDNATRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DNATRulesServer).CreateDNATRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.DNATRules/CreateDNATRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DNATRulesServer).CreateDNATRule(ctx, req.(*CreateDNATRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DNATRules_BindDNATRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindDNATRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DNATRulesServer).BindDNATRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.DNATRules/BindDNATRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DNATRulesServer).BindDNATRule(ctx, req.(*BindDNATRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DNATRules_UnbindDNATRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnbindDNATRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DNATRulesServer).UnbindDNATRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.DNATRules/UnbindDNATRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DNATRulesServer).UnbindDNATRule(ctx, req.(*UnbindDNATRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DNATRules_UnbindDNATRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UnbindDNATRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DNATRulesServer).UnbindDNATRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.DNATRules/UnbindDNATRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DNATRulesServer).UnbindDNATRules(ctx, req.(*UnbindDNATRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DNATRules_UpdateDNATRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDNATRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DNATRulesServer).UpdateDNATRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.DNATRules/UpdateDNATRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DNATRulesServer).UpdateDNATRule(ctx, req.(*UpdateDNATRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DNATRules_DeleteDNATRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDNATRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DNATRulesServer).DeleteDNATRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.eip.v1.DNATRules/DeleteDNATRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DNATRulesServer).DeleteDNATRule(ctx, req.(*DeleteDNATRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DNATRules_ServiceDesc is the grpc.ServiceDesc for DNATRules service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DNATRules_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.eip.v1.DNATRules",
	HandlerType: (*DNATRulesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListDNATRules",
			Handler:    _DNATRules_ListDNATRules_Handler,
		},
		{
			MethodName: "ListMDNATRules",
			Handler:    _DNATRules_ListMDNATRules_Handler,
		},
		{
			MethodName: "GetDNATRule",
			Handler:    _DNATRules_GetDNATRule_Handler,
		},
		{
			MethodName: "CreateDNATRule",
			Handler:    _DNATRules_CreateDNATRule_Handler,
		},
		{
			MethodName: "BindDNATRule",
			Handler:    _DNATRules_BindDNATRule_Handler,
		},
		{
			MethodName: "UnbindDNATRule",
			Handler:    _DNATRules_UnbindDNATRule_Handler,
		},
		{
			MethodName: "UnbindDNATRules",
			Handler:    _DNATRules_UnbindDNATRules_Handler,
		},
		{
			MethodName: "UpdateDNATRule",
			Handler:    _DNATRules_UpdateDNATRule_Handler,
		},
		{
			MethodName: "DeleteDNATRule",
			Handler:    _DNATRules_DeleteDNATRule_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/eip/v1/dnatRule.proto",
}
