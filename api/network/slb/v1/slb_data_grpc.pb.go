// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/slb/v1/slb_data.proto

package slb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SLBDataServiceClient is the client API for SLBDataService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SLBDataServiceClient interface {
	// [zh] 查看负载均衡资源后端状态.
	// [en] Gets details of a SLB resources status.
	GetSLBStatus(ctx context.Context, in *GetSLBStatusRequest, opts ...grpc.CallOption) (*SLBStatus, error)
	// [zh] 列举符合条件的负载均衡状态列表
	// [en] List  details of SLBs resources status.
	ListSLBsStatus(ctx context.Context, in *ListSLBsStatusRequest, opts ...grpc.CallOption) (*ListSLBsStatusResponse, error)
	// [zh] 创建一个监听器.
	// [en] Create a Listener.
	CreateListener(ctx context.Context, in *CreateListenerRequest, opts ...grpc.CallOption) (*Listener, error)
	// [zh] 更新监听器的可编辑字段.
	// [en] Update listener editable properties.
	UpdateListener(ctx context.Context, in *UpdateListenerRequest, opts ...grpc.CallOption) (*Listener, error)
	// [zh] 删除一个监听器
	// [en] Delete a listener.
	DeleteListener(ctx context.Context, in *DeleteListenerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// [zh] 获取符合请求的所有监听器.
	// [en] List requested listener.
	ListListeners(ctx context.Context, in *ListListenersRequest, opts ...grpc.CallOption) (*ListListenersResponse, error)
	// [zh] 获取符合请求的一个监听器.
	// [en] Get a requested listener.
	GetListener(ctx context.Context, in *GetListenerRequest, opts ...grpc.CallOption) (*Listener, error)
	// [zh] 获取符合请求的一个监听器的相关属性.
	// [en] Get a requested listener status.
	GetListenerStatus(ctx context.Context, in *GetListenerStatusRequest, opts ...grpc.CallOption) (*ListenerStatus, error)
	// [zh] 启动一个监听器.
	// [en] Start a Listener.
	StartListener(ctx context.Context, in *StartListenerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// [zh] 停止一个监听器.
	// [en] Stop a Listener.
	StopListener(ctx context.Context, in *StopListenerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// [zh] 创建一个后端组
	// [en] Create one TargetGroup
	CreateTargetGroup(ctx context.Context, in *CreateTargetGroupRequest, opts ...grpc.CallOption) (*TargetGroup, error)
	// [zh] 更新后端组的可编辑字段
	// [en] Update TargetGroup editable properties.
	UpdateTargetGroup(ctx context.Context, in *UpdateTargetGroupRequest, opts ...grpc.CallOption) (*TargetGroup, error)
	// [zh] 删除一个后端组
	// [en] Delete one TargetGroup
	DeleteTargetGroup(ctx context.Context, in *DeleteTargetGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// [zh] 列举符合请求的后端组
	// [en] List requested TargetGroup
	ListTargetGroups(ctx context.Context, in *ListTargetGroupsRequest, opts ...grpc.CallOption) (*ListTargetGroupsResponse, error)
	// [zh] 获取符合请求的后端组
	// [en] Get requested TargetGroup
	GetTargetGroup(ctx context.Context, in *GetTargetGroupRequest, opts ...grpc.CallOption) (*TargetGroup, error)
	// [zh] 获取符合请求的后端组的状态
	GetTargetGroupStatus(ctx context.Context, in *GetTargetGroupStatusRequest, opts ...grpc.CallOption) (*TargetGroupStatus, error)
	// [zh] 添加一个或多个后端到某个后端组.
	// [en] Add one or more backend targets to a TargetGroup.
	TargetGroupAddTargets(ctx context.Context, in *TargetGroupAddTargetsRequest, opts ...grpc.CallOption) (*TargetGroupAddTargetsResponse, error)
	// [zh] 从某个后端组删除一个或多个后端.
	// [en] Remove one or more backend targets from a TargetGroup.
	TargetGroupRemoveTargets(ctx context.Context, in *TargetGroupRemoveTargetsRequest, opts ...grpc.CallOption) (*TargetGroupRemoveTargetsResponse, error)
	// [zh] 修改某个后端组的一台或者多台后端的属性.
	// [en] Update the editable properties of one or more backends of a TargetGroup.
	TargetsUpdate(ctx context.Context, in *TargetsUpdateRequest, opts ...grpc.CallOption) (*TargetGroupTargets, error)
	// [zh] 查询某个后端组绑定的后端列表
	// [en] Get requested TargetGroup targets
	ListTargets(ctx context.Context, in *ListTargetsRequest, opts ...grpc.CallOption) (*ListTargetsResponse, error)
	// [zh] 查询某个后端组绑定的某个后端
	// [en] Get requested TargetGroup targets
	GetTarget(ctx context.Context, in *GetTargetRequest, opts ...grpc.CallOption) (*Target, error)
}

type sLBDataServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewSLBDataServiceClient(cc grpc.ClientConnInterface) SLBDataServiceClient {
	return &sLBDataServiceClient{cc}
}

func (c *sLBDataServiceClient) GetSLBStatus(ctx context.Context, in *GetSLBStatusRequest, opts ...grpc.CallOption) (*SLBStatus, error) {
	out := new(SLBStatus)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/GetSLBStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) ListSLBsStatus(ctx context.Context, in *ListSLBsStatusRequest, opts ...grpc.CallOption) (*ListSLBsStatusResponse, error) {
	out := new(ListSLBsStatusResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/ListSLBsStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) CreateListener(ctx context.Context, in *CreateListenerRequest, opts ...grpc.CallOption) (*Listener, error) {
	out := new(Listener)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/CreateListener", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) UpdateListener(ctx context.Context, in *UpdateListenerRequest, opts ...grpc.CallOption) (*Listener, error) {
	out := new(Listener)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/UpdateListener", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) DeleteListener(ctx context.Context, in *DeleteListenerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/DeleteListener", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) ListListeners(ctx context.Context, in *ListListenersRequest, opts ...grpc.CallOption) (*ListListenersResponse, error) {
	out := new(ListListenersResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/ListListeners", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) GetListener(ctx context.Context, in *GetListenerRequest, opts ...grpc.CallOption) (*Listener, error) {
	out := new(Listener)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/GetListener", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) GetListenerStatus(ctx context.Context, in *GetListenerStatusRequest, opts ...grpc.CallOption) (*ListenerStatus, error) {
	out := new(ListenerStatus)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/GetListenerStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) StartListener(ctx context.Context, in *StartListenerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/StartListener", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) StopListener(ctx context.Context, in *StopListenerRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/StopListener", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) CreateTargetGroup(ctx context.Context, in *CreateTargetGroupRequest, opts ...grpc.CallOption) (*TargetGroup, error) {
	out := new(TargetGroup)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/CreateTargetGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) UpdateTargetGroup(ctx context.Context, in *UpdateTargetGroupRequest, opts ...grpc.CallOption) (*TargetGroup, error) {
	out := new(TargetGroup)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/UpdateTargetGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) DeleteTargetGroup(ctx context.Context, in *DeleteTargetGroupRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/DeleteTargetGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) ListTargetGroups(ctx context.Context, in *ListTargetGroupsRequest, opts ...grpc.CallOption) (*ListTargetGroupsResponse, error) {
	out := new(ListTargetGroupsResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/ListTargetGroups", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) GetTargetGroup(ctx context.Context, in *GetTargetGroupRequest, opts ...grpc.CallOption) (*TargetGroup, error) {
	out := new(TargetGroup)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/GetTargetGroup", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) GetTargetGroupStatus(ctx context.Context, in *GetTargetGroupStatusRequest, opts ...grpc.CallOption) (*TargetGroupStatus, error) {
	out := new(TargetGroupStatus)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/GetTargetGroupStatus", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) TargetGroupAddTargets(ctx context.Context, in *TargetGroupAddTargetsRequest, opts ...grpc.CallOption) (*TargetGroupAddTargetsResponse, error) {
	out := new(TargetGroupAddTargetsResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/TargetGroupAddTargets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) TargetGroupRemoveTargets(ctx context.Context, in *TargetGroupRemoveTargetsRequest, opts ...grpc.CallOption) (*TargetGroupRemoveTargetsResponse, error) {
	out := new(TargetGroupRemoveTargetsResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/TargetGroupRemoveTargets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) TargetsUpdate(ctx context.Context, in *TargetsUpdateRequest, opts ...grpc.CallOption) (*TargetGroupTargets, error) {
	out := new(TargetGroupTargets)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/TargetsUpdate", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) ListTargets(ctx context.Context, in *ListTargetsRequest, opts ...grpc.CallOption) (*ListTargetsResponse, error) {
	out := new(ListTargetsResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/ListTargets", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBDataServiceClient) GetTarget(ctx context.Context, in *GetTargetRequest, opts ...grpc.CallOption) (*Target, error) {
	out := new(Target)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBDataService/GetTarget", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SLBDataServiceServer is the server API for SLBDataService service.
// All implementations must embed UnimplementedSLBDataServiceServer
// for forward compatibility
type SLBDataServiceServer interface {
	// [zh] 查看负载均衡资源后端状态.
	// [en] Gets details of a SLB resources status.
	GetSLBStatus(context.Context, *GetSLBStatusRequest) (*SLBStatus, error)
	// [zh] 列举符合条件的负载均衡状态列表
	// [en] List  details of SLBs resources status.
	ListSLBsStatus(context.Context, *ListSLBsStatusRequest) (*ListSLBsStatusResponse, error)
	// [zh] 创建一个监听器.
	// [en] Create a Listener.
	CreateListener(context.Context, *CreateListenerRequest) (*Listener, error)
	// [zh] 更新监听器的可编辑字段.
	// [en] Update listener editable properties.
	UpdateListener(context.Context, *UpdateListenerRequest) (*Listener, error)
	// [zh] 删除一个监听器
	// [en] Delete a listener.
	DeleteListener(context.Context, *DeleteListenerRequest) (*emptypb.Empty, error)
	// [zh] 获取符合请求的所有监听器.
	// [en] List requested listener.
	ListListeners(context.Context, *ListListenersRequest) (*ListListenersResponse, error)
	// [zh] 获取符合请求的一个监听器.
	// [en] Get a requested listener.
	GetListener(context.Context, *GetListenerRequest) (*Listener, error)
	// [zh] 获取符合请求的一个监听器的相关属性.
	// [en] Get a requested listener status.
	GetListenerStatus(context.Context, *GetListenerStatusRequest) (*ListenerStatus, error)
	// [zh] 启动一个监听器.
	// [en] Start a Listener.
	StartListener(context.Context, *StartListenerRequest) (*emptypb.Empty, error)
	// [zh] 停止一个监听器.
	// [en] Stop a Listener.
	StopListener(context.Context, *StopListenerRequest) (*emptypb.Empty, error)
	// [zh] 创建一个后端组
	// [en] Create one TargetGroup
	CreateTargetGroup(context.Context, *CreateTargetGroupRequest) (*TargetGroup, error)
	// [zh] 更新后端组的可编辑字段
	// [en] Update TargetGroup editable properties.
	UpdateTargetGroup(context.Context, *UpdateTargetGroupRequest) (*TargetGroup, error)
	// [zh] 删除一个后端组
	// [en] Delete one TargetGroup
	DeleteTargetGroup(context.Context, *DeleteTargetGroupRequest) (*emptypb.Empty, error)
	// [zh] 列举符合请求的后端组
	// [en] List requested TargetGroup
	ListTargetGroups(context.Context, *ListTargetGroupsRequest) (*ListTargetGroupsResponse, error)
	// [zh] 获取符合请求的后端组
	// [en] Get requested TargetGroup
	GetTargetGroup(context.Context, *GetTargetGroupRequest) (*TargetGroup, error)
	// [zh] 获取符合请求的后端组的状态
	GetTargetGroupStatus(context.Context, *GetTargetGroupStatusRequest) (*TargetGroupStatus, error)
	// [zh] 添加一个或多个后端到某个后端组.
	// [en] Add one or more backend targets to a TargetGroup.
	TargetGroupAddTargets(context.Context, *TargetGroupAddTargetsRequest) (*TargetGroupAddTargetsResponse, error)
	// [zh] 从某个后端组删除一个或多个后端.
	// [en] Remove one or more backend targets from a TargetGroup.
	TargetGroupRemoveTargets(context.Context, *TargetGroupRemoveTargetsRequest) (*TargetGroupRemoveTargetsResponse, error)
	// [zh] 修改某个后端组的一台或者多台后端的属性.
	// [en] Update the editable properties of one or more backends of a TargetGroup.
	TargetsUpdate(context.Context, *TargetsUpdateRequest) (*TargetGroupTargets, error)
	// [zh] 查询某个后端组绑定的后端列表
	// [en] Get requested TargetGroup targets
	ListTargets(context.Context, *ListTargetsRequest) (*ListTargetsResponse, error)
	// [zh] 查询某个后端组绑定的某个后端
	// [en] Get requested TargetGroup targets
	GetTarget(context.Context, *GetTargetRequest) (*Target, error)
	mustEmbedUnimplementedSLBDataServiceServer()
}

// UnimplementedSLBDataServiceServer must be embedded to have forward compatible implementations.
type UnimplementedSLBDataServiceServer struct {
}

func (UnimplementedSLBDataServiceServer) GetSLBStatus(context.Context, *GetSLBStatusRequest) (*SLBStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSLBStatus not implemented")
}
func (UnimplementedSLBDataServiceServer) ListSLBsStatus(context.Context, *ListSLBsStatusRequest) (*ListSLBsStatusResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListSLBsStatus not implemented")
}
func (UnimplementedSLBDataServiceServer) CreateListener(context.Context, *CreateListenerRequest) (*Listener, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateListener not implemented")
}
func (UnimplementedSLBDataServiceServer) UpdateListener(context.Context, *UpdateListenerRequest) (*Listener, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateListener not implemented")
}
func (UnimplementedSLBDataServiceServer) DeleteListener(context.Context, *DeleteListenerRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteListener not implemented")
}
func (UnimplementedSLBDataServiceServer) ListListeners(context.Context, *ListListenersRequest) (*ListListenersResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListListeners not implemented")
}
func (UnimplementedSLBDataServiceServer) GetListener(context.Context, *GetListenerRequest) (*Listener, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetListener not implemented")
}
func (UnimplementedSLBDataServiceServer) GetListenerStatus(context.Context, *GetListenerStatusRequest) (*ListenerStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetListenerStatus not implemented")
}
func (UnimplementedSLBDataServiceServer) StartListener(context.Context, *StartListenerRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StartListener not implemented")
}
func (UnimplementedSLBDataServiceServer) StopListener(context.Context, *StopListenerRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method StopListener not implemented")
}
func (UnimplementedSLBDataServiceServer) CreateTargetGroup(context.Context, *CreateTargetGroupRequest) (*TargetGroup, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateTargetGroup not implemented")
}
func (UnimplementedSLBDataServiceServer) UpdateTargetGroup(context.Context, *UpdateTargetGroupRequest) (*TargetGroup, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateTargetGroup not implemented")
}
func (UnimplementedSLBDataServiceServer) DeleteTargetGroup(context.Context, *DeleteTargetGroupRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteTargetGroup not implemented")
}
func (UnimplementedSLBDataServiceServer) ListTargetGroups(context.Context, *ListTargetGroupsRequest) (*ListTargetGroupsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTargetGroups not implemented")
}
func (UnimplementedSLBDataServiceServer) GetTargetGroup(context.Context, *GetTargetGroupRequest) (*TargetGroup, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTargetGroup not implemented")
}
func (UnimplementedSLBDataServiceServer) GetTargetGroupStatus(context.Context, *GetTargetGroupStatusRequest) (*TargetGroupStatus, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTargetGroupStatus not implemented")
}
func (UnimplementedSLBDataServiceServer) TargetGroupAddTargets(context.Context, *TargetGroupAddTargetsRequest) (*TargetGroupAddTargetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TargetGroupAddTargets not implemented")
}
func (UnimplementedSLBDataServiceServer) TargetGroupRemoveTargets(context.Context, *TargetGroupRemoveTargetsRequest) (*TargetGroupRemoveTargetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TargetGroupRemoveTargets not implemented")
}
func (UnimplementedSLBDataServiceServer) TargetsUpdate(context.Context, *TargetsUpdateRequest) (*TargetGroupTargets, error) {
	return nil, status.Errorf(codes.Unimplemented, "method TargetsUpdate not implemented")
}
func (UnimplementedSLBDataServiceServer) ListTargets(context.Context, *ListTargetsRequest) (*ListTargetsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListTargets not implemented")
}
func (UnimplementedSLBDataServiceServer) GetTarget(context.Context, *GetTargetRequest) (*Target, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetTarget not implemented")
}
func (UnimplementedSLBDataServiceServer) mustEmbedUnimplementedSLBDataServiceServer() {}

// UnsafeSLBDataServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SLBDataServiceServer will
// result in compilation errors.
type UnsafeSLBDataServiceServer interface {
	mustEmbedUnimplementedSLBDataServiceServer()
}

func RegisterSLBDataServiceServer(s grpc.ServiceRegistrar, srv SLBDataServiceServer) {
	s.RegisterService(&SLBDataService_ServiceDesc, srv)
}

func _SLBDataService_GetSLBStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSLBStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).GetSLBStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/GetSLBStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).GetSLBStatus(ctx, req.(*GetSLBStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_ListSLBsStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListSLBsStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).ListSLBsStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/ListSLBsStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).ListSLBsStatus(ctx, req.(*ListSLBsStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_CreateListener_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateListenerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).CreateListener(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/CreateListener",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).CreateListener(ctx, req.(*CreateListenerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_UpdateListener_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateListenerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).UpdateListener(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/UpdateListener",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).UpdateListener(ctx, req.(*UpdateListenerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_DeleteListener_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteListenerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).DeleteListener(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/DeleteListener",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).DeleteListener(ctx, req.(*DeleteListenerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_ListListeners_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListListenersRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).ListListeners(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/ListListeners",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).ListListeners(ctx, req.(*ListListenersRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_GetListener_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetListenerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).GetListener(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/GetListener",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).GetListener(ctx, req.(*GetListenerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_GetListenerStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetListenerStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).GetListenerStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/GetListenerStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).GetListenerStatus(ctx, req.(*GetListenerStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_StartListener_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StartListenerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).StartListener(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/StartListener",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).StartListener(ctx, req.(*StartListenerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_StopListener_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(StopListenerRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).StopListener(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/StopListener",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).StopListener(ctx, req.(*StopListenerRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_CreateTargetGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateTargetGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).CreateTargetGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/CreateTargetGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).CreateTargetGroup(ctx, req.(*CreateTargetGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_UpdateTargetGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateTargetGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).UpdateTargetGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/UpdateTargetGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).UpdateTargetGroup(ctx, req.(*UpdateTargetGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_DeleteTargetGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteTargetGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).DeleteTargetGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/DeleteTargetGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).DeleteTargetGroup(ctx, req.(*DeleteTargetGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_ListTargetGroups_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTargetGroupsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).ListTargetGroups(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/ListTargetGroups",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).ListTargetGroups(ctx, req.(*ListTargetGroupsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_GetTargetGroup_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTargetGroupRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).GetTargetGroup(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/GetTargetGroup",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).GetTargetGroup(ctx, req.(*GetTargetGroupRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_GetTargetGroupStatus_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTargetGroupStatusRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).GetTargetGroupStatus(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/GetTargetGroupStatus",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).GetTargetGroupStatus(ctx, req.(*GetTargetGroupStatusRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_TargetGroupAddTargets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TargetGroupAddTargetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).TargetGroupAddTargets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/TargetGroupAddTargets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).TargetGroupAddTargets(ctx, req.(*TargetGroupAddTargetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_TargetGroupRemoveTargets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TargetGroupRemoveTargetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).TargetGroupRemoveTargets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/TargetGroupRemoveTargets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).TargetGroupRemoveTargets(ctx, req.(*TargetGroupRemoveTargetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_TargetsUpdate_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(TargetsUpdateRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).TargetsUpdate(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/TargetsUpdate",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).TargetsUpdate(ctx, req.(*TargetsUpdateRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_ListTargets_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListTargetsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).ListTargets(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/ListTargets",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).ListTargets(ctx, req.(*ListTargetsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBDataService_GetTarget_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetTargetRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBDataServiceServer).GetTarget(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBDataService/GetTarget",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBDataServiceServer).GetTarget(ctx, req.(*GetTargetRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SLBDataService_ServiceDesc is the grpc.ServiceDesc for SLBDataService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SLBDataService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.slb.v1.SLBDataService",
	HandlerType: (*SLBDataServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "GetSLBStatus",
			Handler:    _SLBDataService_GetSLBStatus_Handler,
		},
		{
			MethodName: "ListSLBsStatus",
			Handler:    _SLBDataService_ListSLBsStatus_Handler,
		},
		{
			MethodName: "CreateListener",
			Handler:    _SLBDataService_CreateListener_Handler,
		},
		{
			MethodName: "UpdateListener",
			Handler:    _SLBDataService_UpdateListener_Handler,
		},
		{
			MethodName: "DeleteListener",
			Handler:    _SLBDataService_DeleteListener_Handler,
		},
		{
			MethodName: "ListListeners",
			Handler:    _SLBDataService_ListListeners_Handler,
		},
		{
			MethodName: "GetListener",
			Handler:    _SLBDataService_GetListener_Handler,
		},
		{
			MethodName: "GetListenerStatus",
			Handler:    _SLBDataService_GetListenerStatus_Handler,
		},
		{
			MethodName: "StartListener",
			Handler:    _SLBDataService_StartListener_Handler,
		},
		{
			MethodName: "StopListener",
			Handler:    _SLBDataService_StopListener_Handler,
		},
		{
			MethodName: "CreateTargetGroup",
			Handler:    _SLBDataService_CreateTargetGroup_Handler,
		},
		{
			MethodName: "UpdateTargetGroup",
			Handler:    _SLBDataService_UpdateTargetGroup_Handler,
		},
		{
			MethodName: "DeleteTargetGroup",
			Handler:    _SLBDataService_DeleteTargetGroup_Handler,
		},
		{
			MethodName: "ListTargetGroups",
			Handler:    _SLBDataService_ListTargetGroups_Handler,
		},
		{
			MethodName: "GetTargetGroup",
			Handler:    _SLBDataService_GetTargetGroup_Handler,
		},
		{
			MethodName: "GetTargetGroupStatus",
			Handler:    _SLBDataService_GetTargetGroupStatus_Handler,
		},
		{
			MethodName: "TargetGroupAddTargets",
			Handler:    _SLBDataService_TargetGroupAddTargets_Handler,
		},
		{
			MethodName: "TargetGroupRemoveTargets",
			Handler:    _SLBDataService_TargetGroupRemoveTargets_Handler,
		},
		{
			MethodName: "TargetsUpdate",
			Handler:    _SLBDataService_TargetsUpdate_Handler,
		},
		{
			MethodName: "ListTargets",
			Handler:    _SLBDataService_ListTargets_Handler,
		},
		{
			MethodName: "GetTarget",
			Handler:    _SLBDataService_GetTarget_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/slb/v1/slb_data.proto",
}
