// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/slb/v1/slb_message.proto

package slb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateSLBMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateSLBMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSLBMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSLBMessageMultiError, or nil if none found.
func (m *CreateSLBMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSLBMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenantCode

	// no validation rules for OperatorId

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for SlbName

	if all {
		switch v := interface{}(m.GetSlb()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSLBMessageValidationError{
					field:  "Slb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSLBMessageValidationError{
					field:  "Slb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSlb()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSLBMessageValidationError{
				field:  "Slb",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDedicatedResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSLBMessageValidationError{
					field:  "DedicatedResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSLBMessageValidationError{
					field:  "DedicatedResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDedicatedResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSLBMessageValidationError{
				field:  "DedicatedResource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCallbackData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSLBMessageValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSLBMessageValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallbackData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSLBMessageValidationError{
				field:  "CallbackData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateSLBMessageMultiError(errors)
	}

	return nil
}

// CreateSLBMessageMultiError is an error wrapping multiple validation errors
// returned by CreateSLBMessage.ValidateAll() if the designated constraints
// aren't met.
type CreateSLBMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSLBMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSLBMessageMultiError) AllErrors() []error { return m }

// CreateSLBMessageValidationError is the validation error returned by
// CreateSLBMessage.Validate if the designated constraints aren't met.
type CreateSLBMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSLBMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSLBMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSLBMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSLBMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSLBMessageValidationError) ErrorName() string { return "CreateSLBMessageValidationError" }

// Error satisfies the builtin error interface
func (e CreateSLBMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSLBMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSLBMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSLBMessageValidationError{}

// Validate checks the field values on UpdateSLBMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateSLBMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSLBMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateSLBMessageMultiError, or nil if none found.
func (m *UpdateSLBMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSLBMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenantCode

	// no validation rules for OperatorId

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for SlbName

	if all {
		switch v := interface{}(m.GetSlb()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSLBMessageValidationError{
					field:  "Slb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSLBMessageValidationError{
					field:  "Slb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSlb()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSLBMessageValidationError{
				field:  "Slb",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDedicatedResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSLBMessageValidationError{
					field:  "DedicatedResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSLBMessageValidationError{
					field:  "DedicatedResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDedicatedResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSLBMessageValidationError{
				field:  "DedicatedResource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateSLBMessageMultiError(errors)
	}

	return nil
}

// UpdateSLBMessageMultiError is an error wrapping multiple validation errors
// returned by UpdateSLBMessage.ValidateAll() if the designated constraints
// aren't met.
type UpdateSLBMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSLBMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSLBMessageMultiError) AllErrors() []error { return m }

// UpdateSLBMessageValidationError is the validation error returned by
// UpdateSLBMessage.Validate if the designated constraints aren't met.
type UpdateSLBMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSLBMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSLBMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSLBMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSLBMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSLBMessageValidationError) ErrorName() string { return "UpdateSLBMessageValidationError" }

// Error satisfies the builtin error interface
func (e UpdateSLBMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSLBMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSLBMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSLBMessageValidationError{}

// Validate checks the field values on DeleteSLBMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteSLBMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSLBMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteSLBMessageMultiError, or nil if none found.
func (m *DeleteSLBMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSLBMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenantCode

	// no validation rules for OperatorId

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for SlbName

	if len(errors) > 0 {
		return DeleteSLBMessageMultiError(errors)
	}

	return nil
}

// DeleteSLBMessageMultiError is an error wrapping multiple validation errors
// returned by DeleteSLBMessage.ValidateAll() if the designated constraints
// aren't met.
type DeleteSLBMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSLBMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSLBMessageMultiError) AllErrors() []error { return m }

// DeleteSLBMessageValidationError is the validation error returned by
// DeleteSLBMessage.Validate if the designated constraints aren't met.
type DeleteSLBMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSLBMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSLBMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSLBMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSLBMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSLBMessageValidationError) ErrorName() string { return "DeleteSLBMessageValidationError" }

// Error satisfies the builtin error interface
func (e DeleteSLBMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSLBMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSLBMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSLBMessageValidationError{}

// Validate checks the field values on ResizeSLBMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ResizeSLBMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResizeSLBMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResizeSLBMessageMultiError, or nil if none found.
func (m *ResizeSLBMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *ResizeSLBMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenantCode

	// no validation rules for OperatorId

	// no validation rules for ResourceId

	// no validation rules for SkuId

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResizeSLBMessageValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResizeSLBMessageValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResizeSLBMessageValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResizeSLBMessageValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResizeSLBMessageValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResizeSLBMessageValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDedicatedResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResizeSLBMessageValidationError{
					field:  "DedicatedResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResizeSLBMessageValidationError{
					field:  "DedicatedResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDedicatedResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResizeSLBMessageValidationError{
				field:  "DedicatedResource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCallbackData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResizeSLBMessageValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResizeSLBMessageValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallbackData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResizeSLBMessageValidationError{
				field:  "CallbackData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResizeSLBMessageMultiError(errors)
	}

	return nil
}

// ResizeSLBMessageMultiError is an error wrapping multiple validation errors
// returned by ResizeSLBMessage.ValidateAll() if the designated constraints
// aren't met.
type ResizeSLBMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResizeSLBMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResizeSLBMessageMultiError) AllErrors() []error { return m }

// ResizeSLBMessageValidationError is the validation error returned by
// ResizeSLBMessage.Validate if the designated constraints aren't met.
type ResizeSLBMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResizeSLBMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResizeSLBMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResizeSLBMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResizeSLBMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResizeSLBMessageValidationError) ErrorName() string { return "ResizeSLBMessageValidationError" }

// Error satisfies the builtin error interface
func (e ResizeSLBMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResizeSLBMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResizeSLBMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResizeSLBMessageValidationError{}

// Validate checks the field values on DedicatedResource with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DedicatedResource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DedicatedResource with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DedicatedResourceMultiError, or nil if none found.
func (m *DedicatedResource) ValidateAll() error {
	return m.validate(true)
}

func (m *DedicatedResource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsDedicatedTenant

	// no validation rules for IsDedicatedResource

	// no validation rules for ResourcePackageId

	if len(errors) > 0 {
		return DedicatedResourceMultiError(errors)
	}

	return nil
}

// DedicatedResourceMultiError is an error wrapping multiple validation errors
// returned by DedicatedResource.ValidateAll() if the designated constraints
// aren't met.
type DedicatedResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DedicatedResourceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DedicatedResourceMultiError) AllErrors() []error { return m }

// DedicatedResourceValidationError is the validation error returned by
// DedicatedResource.Validate if the designated constraints aren't met.
type DedicatedResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DedicatedResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DedicatedResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DedicatedResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DedicatedResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DedicatedResourceValidationError) ErrorName() string {
	return "DedicatedResourceValidationError"
}

// Error satisfies the builtin error interface
func (e DedicatedResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDedicatedResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DedicatedResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DedicatedResourceValidationError{}

// Validate checks the field values on CallbackData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CallbackData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CallbackDataMultiError, or
// nil if none found.
func (m *CallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *CallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenantId

	// no validation rules for UserId

	// no validation rules for OrderId

	if len(errors) > 0 {
		return CallbackDataMultiError(errors)
	}

	return nil
}

// CallbackDataMultiError is an error wrapping multiple validation errors
// returned by CallbackData.ValidateAll() if the designated constraints aren't met.
type CallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CallbackDataMultiError) AllErrors() []error { return m }

// CallbackDataValidationError is the validation error returned by
// CallbackData.Validate if the designated constraints aren't met.
type CallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CallbackDataValidationError) ErrorName() string { return "CallbackDataValidationError" }

// Error satisfies the builtin error interface
func (e CallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CallbackDataValidationError{}
