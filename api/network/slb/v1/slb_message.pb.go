// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/slb/v1/slb_message.proto

package slb

import (
	v1 "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// [zh] 创建负载均衡资源消息, 消息类型："sensetime.core.network.slb.create.v1" .
// [en] CreateSLBMessage, message type: "sensetime.core.network.slb.create.v1" .
type CreateSLBMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 租户编码.
	// [en] tenant code.
	TenantCode string `protobuf:"bytes,1,opt,name=tenant_code,json=tenantCode,proto3" json:"tenant_code,omitempty"`
	// [zh] 租户物理资源池信息.
	// [en] tenant prp.
	TenantPrp []string `protobuf:"bytes,2,rep,name=tenant_prp,json=tenantPrp,proto3" json:"tenant_prp,omitempty"`
	// [zh] 操作者ID.
	// [en] operator id.
	OperatorId string `protobuf:"bytes,3,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,4,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,5,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,6,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name.
	SlbName string `protobuf:"bytes,7,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 负载均衡资源.
	// [en] The SLB resource struct.
	Slb *SLB `protobuf:"bytes,8,opt,name=slb,proto3" json:"slb,omitempty"`
	// [zh] 专有云资源.
	// [en] The dedicated resource struct.
	DedicatedResource *DedicatedResource `protobuf:"bytes,9,opt,name=dedicated_resource,json=dedicatedResource,proto3" json:"dedicated_resource,omitempty"`
	// [zh] Boss回传信息.
	// [en] Boss returns information.
	CallbackData *CallbackData `protobuf:"bytes,10,opt,name=callback_data,json=callbackData,proto3" json:"callback_data,omitempty"`
}

func (x *CreateSLBMessage) Reset() {
	*x = CreateSLBMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_message_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSLBMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSLBMessage) ProtoMessage() {}

func (x *CreateSLBMessage) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_message_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSLBMessage.ProtoReflect.Descriptor instead.
func (*CreateSLBMessage) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_message_proto_rawDescGZIP(), []int{0}
}

func (x *CreateSLBMessage) GetTenantCode() string {
	if x != nil {
		return x.TenantCode
	}
	return ""
}

func (x *CreateSLBMessage) GetTenantPrp() []string {
	if x != nil {
		return x.TenantPrp
	}
	return nil
}

func (x *CreateSLBMessage) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

func (x *CreateSLBMessage) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *CreateSLBMessage) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *CreateSLBMessage) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateSLBMessage) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *CreateSLBMessage) GetSlb() *SLB {
	if x != nil {
		return x.Slb
	}
	return nil
}

func (x *CreateSLBMessage) GetDedicatedResource() *DedicatedResource {
	if x != nil {
		return x.DedicatedResource
	}
	return nil
}

func (x *CreateSLBMessage) GetCallbackData() *CallbackData {
	if x != nil {
		return x.CallbackData
	}
	return nil
}

// [zh] 更新负载均衡资源消息, 消息类型："sensetime.core.network.slb.update.v1" .
// [en] UpdateSLBMessage, message type: "sensetime.core.network.slb.update.v1" .
type UpdateSLBMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 租户编码.
	// [en] tenant code.
	TenantCode string `protobuf:"bytes,1,opt,name=tenant_code,json=tenantCode,proto3" json:"tenant_code,omitempty"`
	// [zh] 租户物理资源池信息.
	// [en] tenant prp.
	TenantPrp []string `protobuf:"bytes,2,rep,name=tenant_prp,json=tenantPrp,proto3" json:"tenant_prp,omitempty"`
	// [zh] 操作者ID.
	// [en] operator id.
	OperatorId string `protobuf:"bytes,3,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,4,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,5,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,6,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name.
	SlbName string `protobuf:"bytes,7,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 负载均衡资源.
	// [en] The SLB resource struct.
	Slb *SlbUpdate `protobuf:"bytes,8,opt,name=slb,proto3" json:"slb,omitempty"`
	// [zh] 专有云资源.
	// [en] The dedicated resource struct.
	DedicatedResource *DedicatedResource `protobuf:"bytes,9,opt,name=dedicated_resource,json=dedicatedResource,proto3" json:"dedicated_resource,omitempty"`
}

func (x *UpdateSLBMessage) Reset() {
	*x = UpdateSLBMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_message_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSLBMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSLBMessage) ProtoMessage() {}

func (x *UpdateSLBMessage) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_message_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSLBMessage.ProtoReflect.Descriptor instead.
func (*UpdateSLBMessage) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_message_proto_rawDescGZIP(), []int{1}
}

func (x *UpdateSLBMessage) GetTenantCode() string {
	if x != nil {
		return x.TenantCode
	}
	return ""
}

func (x *UpdateSLBMessage) GetTenantPrp() []string {
	if x != nil {
		return x.TenantPrp
	}
	return nil
}

func (x *UpdateSLBMessage) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

func (x *UpdateSLBMessage) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *UpdateSLBMessage) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *UpdateSLBMessage) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateSLBMessage) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *UpdateSLBMessage) GetSlb() *SlbUpdate {
	if x != nil {
		return x.Slb
	}
	return nil
}

func (x *UpdateSLBMessage) GetDedicatedResource() *DedicatedResource {
	if x != nil {
		return x.DedicatedResource
	}
	return nil
}

// [zh] 退订负载均衡资源消息, 消息类型："sensetime.core.network.slb.delete.v1" .
// [en] CreateSLBMessage, message type: "sensetime.core.network.slb.delete.v1" .
type DeleteSLBMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 租户编码.
	// [en] tenant code.
	TenantCode string `protobuf:"bytes,1,opt,name=tenant_code,json=tenantCode,proto3" json:"tenant_code,omitempty"`
	// [zh] 租户物理资源池信息.
	// [en] tenant prp.
	TenantPrp []string `protobuf:"bytes,2,rep,name=tenant_prp,json=tenantPrp,proto3" json:"tenant_prp,omitempty"`
	// [zh] 操作者ID.
	// [en] operator id.
	OperatorId string `protobuf:"bytes,3,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,4,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,5,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,6,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name.
	SlbName string `protobuf:"bytes,7,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
}

func (x *DeleteSLBMessage) Reset() {
	*x = DeleteSLBMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_message_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSLBMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSLBMessage) ProtoMessage() {}

func (x *DeleteSLBMessage) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_message_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSLBMessage.ProtoReflect.Descriptor instead.
func (*DeleteSLBMessage) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_message_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteSLBMessage) GetTenantCode() string {
	if x != nil {
		return x.TenantCode
	}
	return ""
}

func (x *DeleteSLBMessage) GetTenantPrp() []string {
	if x != nil {
		return x.TenantPrp
	}
	return nil
}

func (x *DeleteSLBMessage) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

func (x *DeleteSLBMessage) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DeleteSLBMessage) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *DeleteSLBMessage) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteSLBMessage) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

// [zh] 【暂不使用】扩缩容负载均衡资源消息, 消息类型："sensetime.core.network.slb.resize.v1" .
// [en] 【Not in use yet】ResizeSLBMessage, message type: "sensetime.core.network.slb.resize.v1" .
type ResizeSLBMessage struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 租户编码.
	// [en] tenant code.
	TenantCode string `protobuf:"bytes,1,opt,name=tenant_code,json=tenantCode,proto3" json:"tenant_code,omitempty"`
	// [zh] 租户物理资源池信息.
	// [en] tenant prp.
	TenantPrp []string `protobuf:"bytes,2,rep,name=tenant_prp,json=tenantPrp,proto3" json:"tenant_prp,omitempty"`
	// [zh] 操作者ID.
	// [en] operator id.
	OperatorId string `protobuf:"bytes,3,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// [zh] 资源ID.
	// [en] resource id.
	ResourceId string `protobuf:"bytes,4,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`
	// [zh] 最小库存单元id.
	// [en] SKU id.
	SkuId string `protobuf:"bytes,5,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// [zh] 资源的属性
	// [en] Properties of the resource.
	Properties *SLBProperties `protobuf:"bytes,6,opt,name=properties,proto3" json:"properties,omitempty"`
	// [zh] 订单信息.
	// [en] Order information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,7,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	// [zh] 专有云资源.
	// [en] The dedicated resource struct.
	DedicatedResource *DedicatedResource `protobuf:"bytes,9,opt,name=dedicated_resource,json=dedicatedResource,proto3" json:"dedicated_resource,omitempty"`
	// [zh] Boss回传信息.
	// [en] Boss returns information.
	CallbackData *CallbackData `protobuf:"bytes,10,opt,name=callback_data,json=callbackData,proto3" json:"callback_data,omitempty"`
}

func (x *ResizeSLBMessage) Reset() {
	*x = ResizeSLBMessage{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_message_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResizeSLBMessage) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResizeSLBMessage) ProtoMessage() {}

func (x *ResizeSLBMessage) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_message_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResizeSLBMessage.ProtoReflect.Descriptor instead.
func (*ResizeSLBMessage) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_message_proto_rawDescGZIP(), []int{3}
}

func (x *ResizeSLBMessage) GetTenantCode() string {
	if x != nil {
		return x.TenantCode
	}
	return ""
}

func (x *ResizeSLBMessage) GetTenantPrp() []string {
	if x != nil {
		return x.TenantPrp
	}
	return nil
}

func (x *ResizeSLBMessage) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

func (x *ResizeSLBMessage) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *ResizeSLBMessage) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *ResizeSLBMessage) GetProperties() *SLBProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *ResizeSLBMessage) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

func (x *ResizeSLBMessage) GetDedicatedResource() *DedicatedResource {
	if x != nil {
		return x.DedicatedResource
	}
	return nil
}

func (x *ResizeSLBMessage) GetCallbackData() *CallbackData {
	if x != nil {
		return x.CallbackData
	}
	return nil
}

// [zh] 专有云资源信息.
// [en] The dedicated resource.
type DedicatedResource struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 是否是专有云租户.
	// [en] The dedicated tenant.
	IsDedicatedTenant bool `protobuf:"varint,1,opt,name=is_dedicated_tenant,json=isDedicatedTenant,proto3" json:"is_dedicated_tenant,omitempty"`
	// [zh] 是否是专有云资源.
	// [en] The dedicated resource.
	IsDedicatedResource bool `protobuf:"varint,2,opt,name=is_dedicated_resource,json=isDedicatedResource,proto3" json:"is_dedicated_resource,omitempty"`
	// [zh] 专有云资源包ID.
	// [en] The dedicated resource package id.
	ResourcePackageId string `protobuf:"bytes,3,opt,name=resource_package_id,json=resourcePackageId,proto3" json:"resource_package_id,omitempty"`
}

func (x *DedicatedResource) Reset() {
	*x = DedicatedResource{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_message_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DedicatedResource) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DedicatedResource) ProtoMessage() {}

func (x *DedicatedResource) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_message_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DedicatedResource.ProtoReflect.Descriptor instead.
func (*DedicatedResource) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_message_proto_rawDescGZIP(), []int{4}
}

func (x *DedicatedResource) GetIsDedicatedTenant() bool {
	if x != nil {
		return x.IsDedicatedTenant
	}
	return false
}

func (x *DedicatedResource) GetIsDedicatedResource() bool {
	if x != nil {
		return x.IsDedicatedResource
	}
	return false
}

func (x *DedicatedResource) GetResourcePackageId() string {
	if x != nil {
		return x.ResourcePackageId
	}
	return ""
}

// [zh] Boss回传订单信息.
// [en] Boss returns order information.
type CallbackData struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 租户ID.
	// [en] The tenant id.
	TenantId string `protobuf:"bytes,1,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// [zh] 用户ID.
	// [en] The user id.
	UserId string `protobuf:"bytes,2,opt,name=user_id,json=userId,proto3" json:"user_id,omitempty"`
	// [zh] 订单ID.
	// [en] The order id.
	OrderId string `protobuf:"bytes,3,opt,name=order_id,json=orderId,proto3" json:"order_id,omitempty"`
}

func (x *CallbackData) Reset() {
	*x = CallbackData{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_message_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CallbackData) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CallbackData) ProtoMessage() {}

func (x *CallbackData) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_message_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CallbackData.ProtoReflect.Descriptor instead.
func (*CallbackData) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_message_proto_rawDescGZIP(), []int{5}
}

func (x *CallbackData) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *CallbackData) GetUserId() string {
	if x != nil {
		return x.UserId
	}
	return ""
}

func (x *CallbackData) GetOrderId() string {
	if x != nil {
		return x.OrderId
	}
	return ""
}

var File_network_slb_v1_slb_message_proto protoreflect.FileDescriptor

var file_network_slb_v1_slb_message_proto_rawDesc = []byte{
	0x0a, 0x20, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x6c, 0x62, 0x5f, 0x6d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x12, 0x1d, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76,
	0x31, 0x1a, 0x18, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x6c, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x68, 0x69, 0x67,
	0x67, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xe8, 0x03, 0x0a,
	0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x4c, 0x42, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67,
	0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x43, 0x6f,
	0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x70,
	0x18, 0x02, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x50, 0x72,
	0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x12, 0x19, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34,
	0x0a, 0x03, 0x73, 0x6c, 0x62, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4c, 0x42, 0x52,
	0x03, 0x73, 0x6c, 0x62, 0x12, 0x5f, 0x0a, 0x12, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x64, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x30, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x52, 0x11, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x50, 0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63,
	0x6b, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c,
	0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x52, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62,
	0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x22, 0x9c, 0x03, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x53, 0x4c, 0x42, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28,
	0x09, 0x52, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x50, 0x72, 0x70, 0x12, 0x1f, 0x0a, 0x0b,
	0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2b, 0x0a,
	0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x19,
	0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3a, 0x0a, 0x03, 0x73, 0x6c, 0x62,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c, 0x62, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x52, 0x03, 0x73, 0x6c, 0x62, 0x12, 0x5f, 0x0a, 0x12, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74,
	0x65, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x30, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x52, 0x11, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x22, 0xff, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x53, 0x4c, 0x42, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x70, 0x18, 0x02, 0x20, 0x03, 0x28, 0x09,
	0x52, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x50, 0x72, 0x70, 0x12, 0x1f, 0x0a, 0x0b, 0x6f,
	0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x2b, 0x0a, 0x11,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x19, 0x0a,
	0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xf6, 0x03, 0x0a, 0x10, 0x52, 0x65, 0x73,
	0x69, 0x7a, 0x65, 0x53, 0x4c, 0x42, 0x4d, 0x65, 0x73, 0x73, 0x61, 0x67, 0x65, 0x12, 0x1f, 0x0a,
	0x0b, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x1d,
	0x0a, 0x0a, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x70, 0x72, 0x70, 0x18, 0x02, 0x20, 0x03,
	0x28, 0x09, 0x52, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x50, 0x72, 0x70, 0x12, 0x1f, 0x0a,
	0x0b, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1f,
	0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12,
	0x15, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4c, 0x42, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x5f,
	0x0a, 0x12, 0x64, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x64, 0x69, 0x63,
	0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x52, 0x11, 0x64, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12,
	0x50, 0x0a, 0x0d, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x5f, 0x64, 0x61, 0x74, 0x61,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44,
	0x61, 0x74, 0x61, 0x52, 0x0c, 0x63, 0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74,
	0x61, 0x22, 0xa7, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x52,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x69, 0x73, 0x5f, 0x64, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x11, 0x69, 0x73, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61, 0x74, 0x65,
	0x64, 0x54, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x12, 0x32, 0x0a, 0x15, 0x69, 0x73, 0x5f, 0x64, 0x65,
	0x64, 0x69, 0x63, 0x61, 0x74, 0x65, 0x64, 0x5f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x08, 0x52, 0x13, 0x69, 0x73, 0x44, 0x65, 0x64, 0x69, 0x63, 0x61,
	0x74, 0x65, 0x64, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x70, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x5f,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x50, 0x61, 0x63, 0x6b, 0x61, 0x67, 0x65, 0x49, 0x64, 0x22, 0x5f, 0x0a, 0x0c, 0x43,
	0x61, 0x6c, 0x6c, 0x62, 0x61, 0x63, 0x6b, 0x44, 0x61, 0x74, 0x61, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x73, 0x65, 0x72,
	0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x75, 0x73, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x64, 0x42, 0x50, 0x5a, 0x4e,
	0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x62, 0x6a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61,
	0x72, 0x79, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2d, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x76, 0x31, 0x3b, 0x73, 0x6c, 0x62, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_network_slb_v1_slb_message_proto_rawDescOnce sync.Once
	file_network_slb_v1_slb_message_proto_rawDescData = file_network_slb_v1_slb_message_proto_rawDesc
)

func file_network_slb_v1_slb_message_proto_rawDescGZIP() []byte {
	file_network_slb_v1_slb_message_proto_rawDescOnce.Do(func() {
		file_network_slb_v1_slb_message_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_slb_v1_slb_message_proto_rawDescData)
	})
	return file_network_slb_v1_slb_message_proto_rawDescData
}

var file_network_slb_v1_slb_message_proto_msgTypes = make([]protoimpl.MessageInfo, 6)
var file_network_slb_v1_slb_message_proto_goTypes = []interface{}{
	(*CreateSLBMessage)(nil),  // 0: sensetime.core.network.slb.v1.CreateSLBMessage
	(*UpdateSLBMessage)(nil),  // 1: sensetime.core.network.slb.v1.UpdateSLBMessage
	(*DeleteSLBMessage)(nil),  // 2: sensetime.core.network.slb.v1.DeleteSLBMessage
	(*ResizeSLBMessage)(nil),  // 3: sensetime.core.network.slb.v1.ResizeSLBMessage
	(*DedicatedResource)(nil), // 4: sensetime.core.network.slb.v1.DedicatedResource
	(*CallbackData)(nil),      // 5: sensetime.core.network.slb.v1.CallbackData
	(*SLB)(nil),               // 6: sensetime.core.network.slb.v1.SLB
	(*SlbUpdate)(nil),         // 7: sensetime.core.network.slb.v1.SlbUpdate
	(*SLBProperties)(nil),     // 8: sensetime.core.network.slb.v1.SLBProperties
	(*v1.OrderInfo)(nil),      // 9: sensetime.core.higgs.common.v1.OrderInfo
}
var file_network_slb_v1_slb_message_proto_depIdxs = []int32{
	6, // 0: sensetime.core.network.slb.v1.CreateSLBMessage.slb:type_name -> sensetime.core.network.slb.v1.SLB
	4, // 1: sensetime.core.network.slb.v1.CreateSLBMessage.dedicated_resource:type_name -> sensetime.core.network.slb.v1.DedicatedResource
	5, // 2: sensetime.core.network.slb.v1.CreateSLBMessage.callback_data:type_name -> sensetime.core.network.slb.v1.CallbackData
	7, // 3: sensetime.core.network.slb.v1.UpdateSLBMessage.slb:type_name -> sensetime.core.network.slb.v1.SlbUpdate
	4, // 4: sensetime.core.network.slb.v1.UpdateSLBMessage.dedicated_resource:type_name -> sensetime.core.network.slb.v1.DedicatedResource
	8, // 5: sensetime.core.network.slb.v1.ResizeSLBMessage.properties:type_name -> sensetime.core.network.slb.v1.SLBProperties
	9, // 6: sensetime.core.network.slb.v1.ResizeSLBMessage.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	4, // 7: sensetime.core.network.slb.v1.ResizeSLBMessage.dedicated_resource:type_name -> sensetime.core.network.slb.v1.DedicatedResource
	5, // 8: sensetime.core.network.slb.v1.ResizeSLBMessage.callback_data:type_name -> sensetime.core.network.slb.v1.CallbackData
	9, // [9:9] is the sub-list for method output_type
	9, // [9:9] is the sub-list for method input_type
	9, // [9:9] is the sub-list for extension type_name
	9, // [9:9] is the sub-list for extension extendee
	0, // [0:9] is the sub-list for field type_name
}

func init() { file_network_slb_v1_slb_message_proto_init() }
func file_network_slb_v1_slb_message_proto_init() {
	if File_network_slb_v1_slb_message_proto != nil {
		return
	}
	file_network_slb_v1_slb_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_network_slb_v1_slb_message_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSLBMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_message_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSLBMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_message_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSLBMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_message_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResizeSLBMessage); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_message_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DedicatedResource); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_message_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CallbackData); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_slb_v1_slb_message_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   6,
			NumExtensions: 0,
			NumServices:   0,
		},
		GoTypes:           file_network_slb_v1_slb_message_proto_goTypes,
		DependencyIndexes: file_network_slb_v1_slb_message_proto_depIdxs,
		MessageInfos:      file_network_slb_v1_slb_message_proto_msgTypes,
	}.Build()
	File_network_slb_v1_slb_message_proto = out.File
	file_network_slb_v1_slb_message_proto_rawDesc = nil
	file_network_slb_v1_slb_message_proto_goTypes = nil
	file_network_slb_v1_slb_message_proto_depIdxs = nil
}
