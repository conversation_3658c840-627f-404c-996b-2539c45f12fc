// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/slb/v1/slb_data.proto

package slb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on GetSLBStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSLBStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSLBStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSLBStatusRequestMultiError, or nil if none found.
func (m *GetSLBStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSLBStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := GetSLBStatusRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetSLBStatusRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := GetSLBStatusRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetSLBStatusRequestMultiError(errors)
	}

	return nil
}

// GetSLBStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetSLBStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetSLBStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSLBStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSLBStatusRequestMultiError) AllErrors() []error { return m }

// GetSLBStatusRequestValidationError is the validation error returned by
// GetSLBStatusRequest.Validate if the designated constraints aren't met.
type GetSLBStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSLBStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSLBStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSLBStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSLBStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSLBStatusRequestValidationError) ErrorName() string {
	return "GetSLBStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSLBStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSLBStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSLBStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSLBStatusRequestValidationError{}

var _GetSLBStatusRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on ListSLBsStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSLBsStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSLBsStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSLBsStatusRequestMultiError, or nil if none found.
func (m *ListSLBsStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSLBsStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	if len(errors) > 0 {
		return ListSLBsStatusRequestMultiError(errors)
	}

	return nil
}

// ListSLBsStatusRequestMultiError is an error wrapping multiple validation
// errors returned by ListSLBsStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type ListSLBsStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSLBsStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSLBsStatusRequestMultiError) AllErrors() []error { return m }

// ListSLBsStatusRequestValidationError is the validation error returned by
// ListSLBsStatusRequest.Validate if the designated constraints aren't met.
type ListSLBsStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSLBsStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSLBsStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSLBsStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSLBsStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSLBsStatusRequestValidationError) ErrorName() string {
	return "ListSLBsStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListSLBsStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSLBsStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSLBsStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSLBsStatusRequestValidationError{}

// Validate checks the field values on ListSLBsStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSLBsStatusResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSLBsStatusResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSLBsStatusResponseMultiError, or nil if none found.
func (m *ListSLBsStatusResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSLBsStatusResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetSlbs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListSLBsStatusResponseValidationError{
						field:  fmt.Sprintf("Slbs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListSLBsStatusResponseValidationError{
						field:  fmt.Sprintf("Slbs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListSLBsStatusResponseValidationError{
					field:  fmt.Sprintf("Slbs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListSLBsStatusResponseMultiError(errors)
	}

	return nil
}

// ListSLBsStatusResponseMultiError is an error wrapping multiple validation
// errors returned by ListSLBsStatusResponse.ValidateAll() if the designated
// constraints aren't met.
type ListSLBsStatusResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSLBsStatusResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSLBsStatusResponseMultiError) AllErrors() []error { return m }

// ListSLBsStatusResponseValidationError is the validation error returned by
// ListSLBsStatusResponse.Validate if the designated constraints aren't met.
type ListSLBsStatusResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSLBsStatusResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSLBsStatusResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSLBsStatusResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSLBsStatusResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSLBsStatusResponseValidationError) ErrorName() string {
	return "ListSLBsStatusResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListSLBsStatusResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSLBsStatusResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSLBsStatusResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSLBsStatusResponseValidationError{}

// Validate checks the field values on CreateListenerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateListenerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateListenerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateListenerRequestMultiError, or nil if none found.
func (m *CreateListenerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateListenerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := CreateListenerRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateListenerRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := CreateListenerRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetListenerName()) > 63 {
		err := CreateListenerRequestValidationError{
			field:  "ListenerName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateListenerRequest_ListenerName_Pattern.MatchString(m.GetListenerName()) {
		err := CreateListenerRequestValidationError{
			field:  "ListenerName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListener()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateListenerRequestValidationError{
					field:  "Listener",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateListenerRequestValidationError{
					field:  "Listener",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListener()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateListenerRequestValidationError{
				field:  "Listener",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateListenerRequestMultiError(errors)
	}

	return nil
}

// CreateListenerRequestMultiError is an error wrapping multiple validation
// errors returned by CreateListenerRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateListenerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateListenerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateListenerRequestMultiError) AllErrors() []error { return m }

// CreateListenerRequestValidationError is the validation error returned by
// CreateListenerRequest.Validate if the designated constraints aren't met.
type CreateListenerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateListenerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateListenerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateListenerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateListenerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateListenerRequestValidationError) ErrorName() string {
	return "CreateListenerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateListenerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateListenerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateListenerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateListenerRequestValidationError{}

var _CreateListenerRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _CreateListenerRequest_ListenerName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on UpdateListenerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateListenerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateListenerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateListenerRequestMultiError, or nil if none found.
func (m *UpdateListenerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateListenerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := UpdateListenerRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UpdateListenerRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := UpdateListenerRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetListenerName()) > 63 {
		err := UpdateListenerRequestValidationError{
			field:  "ListenerName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UpdateListenerRequest_ListenerName_Pattern.MatchString(m.GetListenerName()) {
		err := UpdateListenerRequestValidationError{
			field:  "ListenerName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetListener()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateListenerRequestValidationError{
					field:  "Listener",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateListenerRequestValidationError{
					field:  "Listener",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListener()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateListenerRequestValidationError{
				field:  "Listener",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateListenerRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateListenerRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateListenerRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateListenerRequestMultiError(errors)
	}

	return nil
}

// UpdateListenerRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateListenerRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateListenerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateListenerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateListenerRequestMultiError) AllErrors() []error { return m }

// UpdateListenerRequestValidationError is the validation error returned by
// UpdateListenerRequest.Validate if the designated constraints aren't met.
type UpdateListenerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateListenerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateListenerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateListenerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateListenerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateListenerRequestValidationError) ErrorName() string {
	return "UpdateListenerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateListenerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateListenerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateListenerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateListenerRequestValidationError{}

var _UpdateListenerRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _UpdateListenerRequest_ListenerName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on DeleteListenerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteListenerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteListenerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteListenerRequestMultiError, or nil if none found.
func (m *DeleteListenerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteListenerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := DeleteListenerRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_DeleteListenerRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := DeleteListenerRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetListenerName()) > 63 {
		err := DeleteListenerRequestValidationError{
			field:  "ListenerName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_DeleteListenerRequest_ListenerName_Pattern.MatchString(m.GetListenerName()) {
		err := DeleteListenerRequestValidationError{
			field:  "ListenerName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteListenerRequestMultiError(errors)
	}

	return nil
}

// DeleteListenerRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteListenerRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteListenerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteListenerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteListenerRequestMultiError) AllErrors() []error { return m }

// DeleteListenerRequestValidationError is the validation error returned by
// DeleteListenerRequest.Validate if the designated constraints aren't met.
type DeleteListenerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteListenerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteListenerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteListenerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteListenerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteListenerRequestValidationError) ErrorName() string {
	return "DeleteListenerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteListenerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteListenerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteListenerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteListenerRequestValidationError{}

var _DeleteListenerRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _DeleteListenerRequest_ListenerName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on ListListenersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListListenersRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListListenersRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListListenersRequestMultiError, or nil if none found.
func (m *ListListenersRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListListenersRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	// no validation rules for TenantId

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := ListListenersRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ListListenersRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := ListListenersRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListListenersRequestMultiError(errors)
	}

	return nil
}

// ListListenersRequestMultiError is an error wrapping multiple validation
// errors returned by ListListenersRequest.ValidateAll() if the designated
// constraints aren't met.
type ListListenersRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListListenersRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListListenersRequestMultiError) AllErrors() []error { return m }

// ListListenersRequestValidationError is the validation error returned by
// ListListenersRequest.Validate if the designated constraints aren't met.
type ListListenersRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListListenersRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListListenersRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListListenersRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListListenersRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListListenersRequestValidationError) ErrorName() string {
	return "ListListenersRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListListenersRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListListenersRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListListenersRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListListenersRequestValidationError{}

var _ListListenersRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on ListListenersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListListenersResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListListenersResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListListenersResponseMultiError, or nil if none found.
func (m *ListListenersResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListListenersResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetListenerStatusInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListListenersResponseValidationError{
						field:  fmt.Sprintf("ListenerStatusInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListListenersResponseValidationError{
						field:  fmt.Sprintf("ListenerStatusInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListListenersResponseValidationError{
					field:  fmt.Sprintf("ListenerStatusInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListListenersResponseMultiError(errors)
	}

	return nil
}

// ListListenersResponseMultiError is an error wrapping multiple validation
// errors returned by ListListenersResponse.ValidateAll() if the designated
// constraints aren't met.
type ListListenersResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListListenersResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListListenersResponseMultiError) AllErrors() []error { return m }

// ListListenersResponseValidationError is the validation error returned by
// ListListenersResponse.Validate if the designated constraints aren't met.
type ListListenersResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListListenersResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListListenersResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListListenersResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListListenersResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListListenersResponseValidationError) ErrorName() string {
	return "ListListenersResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListListenersResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListListenersResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListListenersResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListListenersResponseValidationError{}

// Validate checks the field values on GetListenerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetListenerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetListenerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetListenerRequestMultiError, or nil if none found.
func (m *GetListenerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetListenerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := GetListenerRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetListenerRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := GetListenerRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetListenerName()) > 63 {
		err := GetListenerRequestValidationError{
			field:  "ListenerName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetListenerRequest_ListenerName_Pattern.MatchString(m.GetListenerName()) {
		err := GetListenerRequestValidationError{
			field:  "ListenerName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetListenerRequestMultiError(errors)
	}

	return nil
}

// GetListenerRequestMultiError is an error wrapping multiple validation errors
// returned by GetListenerRequest.ValidateAll() if the designated constraints
// aren't met.
type GetListenerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetListenerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetListenerRequestMultiError) AllErrors() []error { return m }

// GetListenerRequestValidationError is the validation error returned by
// GetListenerRequest.Validate if the designated constraints aren't met.
type GetListenerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetListenerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetListenerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetListenerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetListenerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetListenerRequestValidationError) ErrorName() string {
	return "GetListenerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetListenerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetListenerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetListenerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetListenerRequestValidationError{}

var _GetListenerRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _GetListenerRequest_ListenerName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on GetListenerStatusRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetListenerStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetListenerStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetListenerStatusRequestMultiError, or nil if none found.
func (m *GetListenerStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetListenerStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := GetListenerStatusRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetListenerStatusRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := GetListenerStatusRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetListenerName()) > 63 {
		err := GetListenerStatusRequestValidationError{
			field:  "ListenerName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetListenerStatusRequest_ListenerName_Pattern.MatchString(m.GetListenerName()) {
		err := GetListenerStatusRequestValidationError{
			field:  "ListenerName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetListenerStatusRequestMultiError(errors)
	}

	return nil
}

// GetListenerStatusRequestMultiError is an error wrapping multiple validation
// errors returned by GetListenerStatusRequest.ValidateAll() if the designated
// constraints aren't met.
type GetListenerStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetListenerStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetListenerStatusRequestMultiError) AllErrors() []error { return m }

// GetListenerStatusRequestValidationError is the validation error returned by
// GetListenerStatusRequest.Validate if the designated constraints aren't met.
type GetListenerStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetListenerStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetListenerStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetListenerStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetListenerStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetListenerStatusRequestValidationError) ErrorName() string {
	return "GetListenerStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetListenerStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetListenerStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetListenerStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetListenerStatusRequestValidationError{}

var _GetListenerStatusRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _GetListenerStatusRequest_ListenerName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on StartListenerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StartListenerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StartListenerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StartListenerRequestMultiError, or nil if none found.
func (m *StartListenerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StartListenerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := StartListenerRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_StartListenerRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := StartListenerRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetListenerName()) > 63 {
		err := StartListenerRequestValidationError{
			field:  "ListenerName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_StartListenerRequest_ListenerName_Pattern.MatchString(m.GetListenerName()) {
		err := StartListenerRequestValidationError{
			field:  "ListenerName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return StartListenerRequestMultiError(errors)
	}

	return nil
}

// StartListenerRequestMultiError is an error wrapping multiple validation
// errors returned by StartListenerRequest.ValidateAll() if the designated
// constraints aren't met.
type StartListenerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StartListenerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StartListenerRequestMultiError) AllErrors() []error { return m }

// StartListenerRequestValidationError is the validation error returned by
// StartListenerRequest.Validate if the designated constraints aren't met.
type StartListenerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StartListenerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StartListenerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StartListenerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StartListenerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StartListenerRequestValidationError) ErrorName() string {
	return "StartListenerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StartListenerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStartListenerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StartListenerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StartListenerRequestValidationError{}

var _StartListenerRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _StartListenerRequest_ListenerName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on StopListenerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *StopListenerRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on StopListenerRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// StopListenerRequestMultiError, or nil if none found.
func (m *StopListenerRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *StopListenerRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := StopListenerRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_StopListenerRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := StopListenerRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetListenerName()) > 63 {
		err := StopListenerRequestValidationError{
			field:  "ListenerName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_StopListenerRequest_ListenerName_Pattern.MatchString(m.GetListenerName()) {
		err := StopListenerRequestValidationError{
			field:  "ListenerName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return StopListenerRequestMultiError(errors)
	}

	return nil
}

// StopListenerRequestMultiError is an error wrapping multiple validation
// errors returned by StopListenerRequest.ValidateAll() if the designated
// constraints aren't met.
type StopListenerRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m StopListenerRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m StopListenerRequestMultiError) AllErrors() []error { return m }

// StopListenerRequestValidationError is the validation error returned by
// StopListenerRequest.Validate if the designated constraints aren't met.
type StopListenerRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e StopListenerRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e StopListenerRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e StopListenerRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e StopListenerRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e StopListenerRequestValidationError) ErrorName() string {
	return "StopListenerRequestValidationError"
}

// Error satisfies the builtin error interface
func (e StopListenerRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sStopListenerRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = StopListenerRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = StopListenerRequestValidationError{}

var _StopListenerRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _StopListenerRequest_ListenerName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on CreateTargetGroupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateTargetGroupRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateTargetGroupRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateTargetGroupRequestMultiError, or nil if none found.
func (m *CreateTargetGroupRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateTargetGroupRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := CreateTargetGroupRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateTargetGroupRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := CreateTargetGroupRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTargetGroupName()) > 63 {
		err := CreateTargetGroupRequestValidationError{
			field:  "TargetGroupName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateTargetGroupRequest_TargetGroupName_Pattern.MatchString(m.GetTargetGroupName()) {
		err := CreateTargetGroupRequestValidationError{
			field:  "TargetGroupName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTargetGroup()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateTargetGroupRequestValidationError{
					field:  "TargetGroup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateTargetGroupRequestValidationError{
					field:  "TargetGroup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTargetGroup()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateTargetGroupRequestValidationError{
				field:  "TargetGroup",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateTargetGroupRequestMultiError(errors)
	}

	return nil
}

// CreateTargetGroupRequestMultiError is an error wrapping multiple validation
// errors returned by CreateTargetGroupRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateTargetGroupRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateTargetGroupRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateTargetGroupRequestMultiError) AllErrors() []error { return m }

// CreateTargetGroupRequestValidationError is the validation error returned by
// CreateTargetGroupRequest.Validate if the designated constraints aren't met.
type CreateTargetGroupRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateTargetGroupRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateTargetGroupRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateTargetGroupRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateTargetGroupRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateTargetGroupRequestValidationError) ErrorName() string {
	return "CreateTargetGroupRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateTargetGroupRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateTargetGroupRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateTargetGroupRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateTargetGroupRequestValidationError{}

var _CreateTargetGroupRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _CreateTargetGroupRequest_TargetGroupName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on UpdateTargetGroupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateTargetGroupRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateTargetGroupRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateTargetGroupRequestMultiError, or nil if none found.
func (m *UpdateTargetGroupRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateTargetGroupRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := UpdateTargetGroupRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UpdateTargetGroupRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := UpdateTargetGroupRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTargetGroupName()) > 63 {
		err := UpdateTargetGroupRequestValidationError{
			field:  "TargetGroupName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UpdateTargetGroupRequest_TargetGroupName_Pattern.MatchString(m.GetTargetGroupName()) {
		err := UpdateTargetGroupRequestValidationError{
			field:  "TargetGroupName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetTargetGroup()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTargetGroupRequestValidationError{
					field:  "TargetGroup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTargetGroupRequestValidationError{
					field:  "TargetGroup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTargetGroup()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTargetGroupRequestValidationError{
				field:  "TargetGroup",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateTargetGroupRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateTargetGroupRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateTargetGroupRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateTargetGroupRequestMultiError(errors)
	}

	return nil
}

// UpdateTargetGroupRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateTargetGroupRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateTargetGroupRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateTargetGroupRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateTargetGroupRequestMultiError) AllErrors() []error { return m }

// UpdateTargetGroupRequestValidationError is the validation error returned by
// UpdateTargetGroupRequest.Validate if the designated constraints aren't met.
type UpdateTargetGroupRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateTargetGroupRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateTargetGroupRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateTargetGroupRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateTargetGroupRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateTargetGroupRequestValidationError) ErrorName() string {
	return "UpdateTargetGroupRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateTargetGroupRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateTargetGroupRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateTargetGroupRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateTargetGroupRequestValidationError{}

var _UpdateTargetGroupRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _UpdateTargetGroupRequest_TargetGroupName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on DeleteTargetGroupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteTargetGroupRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteTargetGroupRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteTargetGroupRequestMultiError, or nil if none found.
func (m *DeleteTargetGroupRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteTargetGroupRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := DeleteTargetGroupRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_DeleteTargetGroupRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := DeleteTargetGroupRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTargetGroupName()) > 63 {
		err := DeleteTargetGroupRequestValidationError{
			field:  "TargetGroupName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_DeleteTargetGroupRequest_TargetGroupName_Pattern.MatchString(m.GetTargetGroupName()) {
		err := DeleteTargetGroupRequestValidationError{
			field:  "TargetGroupName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteTargetGroupRequestMultiError(errors)
	}

	return nil
}

// DeleteTargetGroupRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteTargetGroupRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteTargetGroupRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteTargetGroupRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteTargetGroupRequestMultiError) AllErrors() []error { return m }

// DeleteTargetGroupRequestValidationError is the validation error returned by
// DeleteTargetGroupRequest.Validate if the designated constraints aren't met.
type DeleteTargetGroupRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteTargetGroupRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteTargetGroupRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteTargetGroupRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteTargetGroupRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteTargetGroupRequestValidationError) ErrorName() string {
	return "DeleteTargetGroupRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteTargetGroupRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteTargetGroupRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteTargetGroupRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteTargetGroupRequestValidationError{}

var _DeleteTargetGroupRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _DeleteTargetGroupRequest_TargetGroupName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on ListTargetGroupsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTargetGroupsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTargetGroupsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTargetGroupsRequestMultiError, or nil if none found.
func (m *ListTargetGroupsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTargetGroupsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	// no validation rules for TenantId

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := ListTargetGroupsRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ListTargetGroupsRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := ListTargetGroupsRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListTargetGroupsRequestMultiError(errors)
	}

	return nil
}

// ListTargetGroupsRequestMultiError is an error wrapping multiple validation
// errors returned by ListTargetGroupsRequest.ValidateAll() if the designated
// constraints aren't met.
type ListTargetGroupsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTargetGroupsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTargetGroupsRequestMultiError) AllErrors() []error { return m }

// ListTargetGroupsRequestValidationError is the validation error returned by
// ListTargetGroupsRequest.Validate if the designated constraints aren't met.
type ListTargetGroupsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTargetGroupsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTargetGroupsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTargetGroupsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTargetGroupsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTargetGroupsRequestValidationError) ErrorName() string {
	return "ListTargetGroupsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListTargetGroupsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTargetGroupsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTargetGroupsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTargetGroupsRequestValidationError{}

var _ListTargetGroupsRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on ListTargetGroupsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTargetGroupsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTargetGroupsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTargetGroupsResponseMultiError, or nil if none found.
func (m *ListTargetGroupsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTargetGroupsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTargetGroups() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListTargetGroupsResponseValidationError{
						field:  fmt.Sprintf("TargetGroups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListTargetGroupsResponseValidationError{
						field:  fmt.Sprintf("TargetGroups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListTargetGroupsResponseValidationError{
					field:  fmt.Sprintf("TargetGroups[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListTargetGroupsResponseMultiError(errors)
	}

	return nil
}

// ListTargetGroupsResponseMultiError is an error wrapping multiple validation
// errors returned by ListTargetGroupsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListTargetGroupsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTargetGroupsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTargetGroupsResponseMultiError) AllErrors() []error { return m }

// ListTargetGroupsResponseValidationError is the validation error returned by
// ListTargetGroupsResponse.Validate if the designated constraints aren't met.
type ListTargetGroupsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTargetGroupsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTargetGroupsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTargetGroupsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTargetGroupsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTargetGroupsResponseValidationError) ErrorName() string {
	return "ListTargetGroupsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListTargetGroupsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTargetGroupsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTargetGroupsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTargetGroupsResponseValidationError{}

// Validate checks the field values on GetTargetGroupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTargetGroupRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTargetGroupRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTargetGroupRequestMultiError, or nil if none found.
func (m *GetTargetGroupRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTargetGroupRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := GetTargetGroupRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetTargetGroupRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := GetTargetGroupRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTargetGroupName()) > 63 {
		err := GetTargetGroupRequestValidationError{
			field:  "TargetGroupName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetTargetGroupRequest_TargetGroupName_Pattern.MatchString(m.GetTargetGroupName()) {
		err := GetTargetGroupRequestValidationError{
			field:  "TargetGroupName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetTargetGroupRequestMultiError(errors)
	}

	return nil
}

// GetTargetGroupRequestMultiError is an error wrapping multiple validation
// errors returned by GetTargetGroupRequest.ValidateAll() if the designated
// constraints aren't met.
type GetTargetGroupRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTargetGroupRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTargetGroupRequestMultiError) AllErrors() []error { return m }

// GetTargetGroupRequestValidationError is the validation error returned by
// GetTargetGroupRequest.Validate if the designated constraints aren't met.
type GetTargetGroupRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTargetGroupRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTargetGroupRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTargetGroupRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTargetGroupRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTargetGroupRequestValidationError) ErrorName() string {
	return "GetTargetGroupRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTargetGroupRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTargetGroupRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTargetGroupRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTargetGroupRequestValidationError{}

var _GetTargetGroupRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _GetTargetGroupRequest_TargetGroupName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on GetTargetGroupStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetTargetGroupStatusRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTargetGroupStatusRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTargetGroupStatusRequestMultiError, or nil if none found.
func (m *GetTargetGroupStatusRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTargetGroupStatusRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := GetTargetGroupStatusRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetTargetGroupStatusRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := GetTargetGroupStatusRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTargetGroupName()) > 63 {
		err := GetTargetGroupStatusRequestValidationError{
			field:  "TargetGroupName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetTargetGroupStatusRequest_TargetGroupName_Pattern.MatchString(m.GetTargetGroupName()) {
		err := GetTargetGroupStatusRequestValidationError{
			field:  "TargetGroupName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetTargetGroupStatusRequestMultiError(errors)
	}

	return nil
}

// GetTargetGroupStatusRequestMultiError is an error wrapping multiple
// validation errors returned by GetTargetGroupStatusRequest.ValidateAll() if
// the designated constraints aren't met.
type GetTargetGroupStatusRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTargetGroupStatusRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTargetGroupStatusRequestMultiError) AllErrors() []error { return m }

// GetTargetGroupStatusRequestValidationError is the validation error returned
// by GetTargetGroupStatusRequest.Validate if the designated constraints
// aren't met.
type GetTargetGroupStatusRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTargetGroupStatusRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTargetGroupStatusRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTargetGroupStatusRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTargetGroupStatusRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTargetGroupStatusRequestValidationError) ErrorName() string {
	return "GetTargetGroupStatusRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetTargetGroupStatusRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTargetGroupStatusRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTargetGroupStatusRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTargetGroupStatusRequestValidationError{}

var _GetTargetGroupStatusRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _GetTargetGroupStatusRequest_TargetGroupName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on TargetGroupAddTargetsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TargetGroupAddTargetsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetGroupAddTargetsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TargetGroupAddTargetsRequestMultiError, or nil if none found.
func (m *TargetGroupAddTargetsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetGroupAddTargetsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := TargetGroupAddTargetsRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_TargetGroupAddTargetsRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := TargetGroupAddTargetsRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTargetGroupName()) > 63 {
		err := TargetGroupAddTargetsRequestValidationError{
			field:  "TargetGroupName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_TargetGroupAddTargetsRequest_TargetGroupName_Pattern.MatchString(m.GetTargetGroupName()) {
		err := TargetGroupAddTargetsRequestValidationError{
			field:  "TargetGroupName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetTargets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TargetGroupAddTargetsRequestValidationError{
						field:  fmt.Sprintf("Targets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TargetGroupAddTargetsRequestValidationError{
						field:  fmt.Sprintf("Targets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TargetGroupAddTargetsRequestValidationError{
					field:  fmt.Sprintf("Targets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TargetGroupAddTargetsRequestMultiError(errors)
	}

	return nil
}

// TargetGroupAddTargetsRequestMultiError is an error wrapping multiple
// validation errors returned by TargetGroupAddTargetsRequest.ValidateAll() if
// the designated constraints aren't met.
type TargetGroupAddTargetsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetGroupAddTargetsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetGroupAddTargetsRequestMultiError) AllErrors() []error { return m }

// TargetGroupAddTargetsRequestValidationError is the validation error returned
// by TargetGroupAddTargetsRequest.Validate if the designated constraints
// aren't met.
type TargetGroupAddTargetsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetGroupAddTargetsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetGroupAddTargetsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetGroupAddTargetsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetGroupAddTargetsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetGroupAddTargetsRequestValidationError) ErrorName() string {
	return "TargetGroupAddTargetsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TargetGroupAddTargetsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetGroupAddTargetsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetGroupAddTargetsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetGroupAddTargetsRequestValidationError{}

var _TargetGroupAddTargetsRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _TargetGroupAddTargetsRequest_TargetGroupName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on TargetGroupAddTargetsResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TargetGroupAddTargetsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetGroupAddTargetsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TargetGroupAddTargetsResponseMultiError, or nil if none found.
func (m *TargetGroupAddTargetsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetGroupAddTargetsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCurrTargets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TargetGroupAddTargetsResponseValidationError{
						field:  fmt.Sprintf("CurrTargets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TargetGroupAddTargetsResponseValidationError{
						field:  fmt.Sprintf("CurrTargets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TargetGroupAddTargetsResponseValidationError{
					field:  fmt.Sprintf("CurrTargets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TargetGroupAddTargetsResponseMultiError(errors)
	}

	return nil
}

// TargetGroupAddTargetsResponseMultiError is an error wrapping multiple
// validation errors returned by TargetGroupAddTargetsResponse.ValidateAll()
// if the designated constraints aren't met.
type TargetGroupAddTargetsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetGroupAddTargetsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetGroupAddTargetsResponseMultiError) AllErrors() []error { return m }

// TargetGroupAddTargetsResponseValidationError is the validation error
// returned by TargetGroupAddTargetsResponse.Validate if the designated
// constraints aren't met.
type TargetGroupAddTargetsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetGroupAddTargetsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetGroupAddTargetsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetGroupAddTargetsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetGroupAddTargetsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetGroupAddTargetsResponseValidationError) ErrorName() string {
	return "TargetGroupAddTargetsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TargetGroupAddTargetsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetGroupAddTargetsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetGroupAddTargetsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetGroupAddTargetsResponseValidationError{}

// Validate checks the field values on TargetGroupRemoveTargetsRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TargetGroupRemoveTargetsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetGroupRemoveTargetsRequest with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TargetGroupRemoveTargetsRequestMultiError, or nil if none found.
func (m *TargetGroupRemoveTargetsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetGroupRemoveTargetsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := TargetGroupRemoveTargetsRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_TargetGroupRemoveTargetsRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := TargetGroupRemoveTargetsRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTargetGroupName()) > 63 {
		err := TargetGroupRemoveTargetsRequestValidationError{
			field:  "TargetGroupName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_TargetGroupRemoveTargetsRequest_TargetGroupName_Pattern.MatchString(m.GetTargetGroupName()) {
		err := TargetGroupRemoveTargetsRequestValidationError{
			field:  "TargetGroupName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return TargetGroupRemoveTargetsRequestMultiError(errors)
	}

	return nil
}

// TargetGroupRemoveTargetsRequestMultiError is an error wrapping multiple
// validation errors returned by TargetGroupRemoveTargetsRequest.ValidateAll()
// if the designated constraints aren't met.
type TargetGroupRemoveTargetsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetGroupRemoveTargetsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetGroupRemoveTargetsRequestMultiError) AllErrors() []error { return m }

// TargetGroupRemoveTargetsRequestValidationError is the validation error
// returned by TargetGroupRemoveTargetsRequest.Validate if the designated
// constraints aren't met.
type TargetGroupRemoveTargetsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetGroupRemoveTargetsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetGroupRemoveTargetsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetGroupRemoveTargetsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetGroupRemoveTargetsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetGroupRemoveTargetsRequestValidationError) ErrorName() string {
	return "TargetGroupRemoveTargetsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TargetGroupRemoveTargetsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetGroupRemoveTargetsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetGroupRemoveTargetsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetGroupRemoveTargetsRequestValidationError{}

var _TargetGroupRemoveTargetsRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _TargetGroupRemoveTargetsRequest_TargetGroupName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on TargetGroupRemoveTargetsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the first error encountered is returned, or nil if there are
// no violations.
func (m *TargetGroupRemoveTargetsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetGroupRemoveTargetsResponse with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// TargetGroupRemoveTargetsResponseMultiError, or nil if none found.
func (m *TargetGroupRemoveTargetsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetGroupRemoveTargetsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCurrTargets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TargetGroupRemoveTargetsResponseValidationError{
						field:  fmt.Sprintf("CurrTargets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TargetGroupRemoveTargetsResponseValidationError{
						field:  fmt.Sprintf("CurrTargets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TargetGroupRemoveTargetsResponseValidationError{
					field:  fmt.Sprintf("CurrTargets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TargetGroupRemoveTargetsResponseMultiError(errors)
	}

	return nil
}

// TargetGroupRemoveTargetsResponseMultiError is an error wrapping multiple
// validation errors returned by
// TargetGroupRemoveTargetsResponse.ValidateAll() if the designated
// constraints aren't met.
type TargetGroupRemoveTargetsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetGroupRemoveTargetsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetGroupRemoveTargetsResponseMultiError) AllErrors() []error { return m }

// TargetGroupRemoveTargetsResponseValidationError is the validation error
// returned by TargetGroupRemoveTargetsResponse.Validate if the designated
// constraints aren't met.
type TargetGroupRemoveTargetsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetGroupRemoveTargetsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetGroupRemoveTargetsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetGroupRemoveTargetsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetGroupRemoveTargetsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetGroupRemoveTargetsResponseValidationError) ErrorName() string {
	return "TargetGroupRemoveTargetsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e TargetGroupRemoveTargetsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetGroupRemoveTargetsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetGroupRemoveTargetsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetGroupRemoveTargetsResponseValidationError{}

// Validate checks the field values on TargetsUpdateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TargetsUpdateRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetsUpdateRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TargetsUpdateRequestMultiError, or nil if none found.
func (m *TargetsUpdateRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetsUpdateRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := TargetsUpdateRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_TargetsUpdateRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := TargetsUpdateRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTargetGroupName()) > 63 {
		err := TargetsUpdateRequestValidationError{
			field:  "TargetGroupName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_TargetsUpdateRequest_TargetGroupName_Pattern.MatchString(m.GetTargetGroupName()) {
		err := TargetsUpdateRequestValidationError{
			field:  "TargetGroupName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	for idx, item := range m.GetTargetGroupTargets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TargetsUpdateRequestValidationError{
						field:  fmt.Sprintf("TargetGroupTargets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TargetsUpdateRequestValidationError{
						field:  fmt.Sprintf("TargetGroupTargets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TargetsUpdateRequestValidationError{
					field:  fmt.Sprintf("TargetGroupTargets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetsUpdateRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetsUpdateRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetsUpdateRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TargetsUpdateRequestMultiError(errors)
	}

	return nil
}

// TargetsUpdateRequestMultiError is an error wrapping multiple validation
// errors returned by TargetsUpdateRequest.ValidateAll() if the designated
// constraints aren't met.
type TargetsUpdateRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetsUpdateRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetsUpdateRequestMultiError) AllErrors() []error { return m }

// TargetsUpdateRequestValidationError is the validation error returned by
// TargetsUpdateRequest.Validate if the designated constraints aren't met.
type TargetsUpdateRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetsUpdateRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetsUpdateRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetsUpdateRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetsUpdateRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetsUpdateRequestValidationError) ErrorName() string {
	return "TargetsUpdateRequestValidationError"
}

// Error satisfies the builtin error interface
func (e TargetsUpdateRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetsUpdateRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetsUpdateRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetsUpdateRequestValidationError{}

var _TargetsUpdateRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _TargetsUpdateRequest_TargetGroupName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on TargetGroupTargets with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TargetGroupTargets) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetGroupTargets with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TargetGroupTargetsMultiError, or nil if none found.
func (m *TargetGroupTargets) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetGroupTargets) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetCurrTargets() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TargetGroupTargetsValidationError{
						field:  fmt.Sprintf("CurrTargets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TargetGroupTargetsValidationError{
						field:  fmt.Sprintf("CurrTargets[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TargetGroupTargetsValidationError{
					field:  fmt.Sprintf("CurrTargets[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return TargetGroupTargetsMultiError(errors)
	}

	return nil
}

// TargetGroupTargetsMultiError is an error wrapping multiple validation errors
// returned by TargetGroupTargets.ValidateAll() if the designated constraints
// aren't met.
type TargetGroupTargetsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetGroupTargetsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetGroupTargetsMultiError) AllErrors() []error { return m }

// TargetGroupTargetsValidationError is the validation error returned by
// TargetGroupTargets.Validate if the designated constraints aren't met.
type TargetGroupTargetsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetGroupTargetsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetGroupTargetsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetGroupTargetsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetGroupTargetsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetGroupTargetsValidationError) ErrorName() string {
	return "TargetGroupTargetsValidationError"
}

// Error satisfies the builtin error interface
func (e TargetGroupTargetsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetGroupTargets.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetGroupTargetsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetGroupTargetsValidationError{}

// Validate checks the field values on ListTargetsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTargetsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTargetsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTargetsRequestMultiError, or nil if none found.
func (m *ListTargetsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTargetsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	// no validation rules for TenantId

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := ListTargetsRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ListTargetsRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := ListTargetsRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTargetGroupName()) > 63 {
		err := ListTargetsRequestValidationError{
			field:  "TargetGroupName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ListTargetsRequest_TargetGroupName_Pattern.MatchString(m.GetTargetGroupName()) {
		err := ListTargetsRequestValidationError{
			field:  "TargetGroupName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ListTargetsRequestMultiError(errors)
	}

	return nil
}

// ListTargetsRequestMultiError is an error wrapping multiple validation errors
// returned by ListTargetsRequest.ValidateAll() if the designated constraints
// aren't met.
type ListTargetsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTargetsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTargetsRequestMultiError) AllErrors() []error { return m }

// ListTargetsRequestValidationError is the validation error returned by
// ListTargetsRequest.Validate if the designated constraints aren't met.
type ListTargetsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTargetsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTargetsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTargetsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTargetsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTargetsRequestValidationError) ErrorName() string {
	return "ListTargetsRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListTargetsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTargetsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTargetsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTargetsRequestValidationError{}

var _ListTargetsRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _ListTargetsRequest_TargetGroupName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on ListTargetsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListTargetsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListTargetsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListTargetsResponseMultiError, or nil if none found.
func (m *ListTargetsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListTargetsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTargetInfos() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListTargetsResponseValidationError{
						field:  fmt.Sprintf("TargetInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListTargetsResponseValidationError{
						field:  fmt.Sprintf("TargetInfos[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListTargetsResponseValidationError{
					field:  fmt.Sprintf("TargetInfos[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListTargetsResponseMultiError(errors)
	}

	return nil
}

// ListTargetsResponseMultiError is an error wrapping multiple validation
// errors returned by ListTargetsResponse.ValidateAll() if the designated
// constraints aren't met.
type ListTargetsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListTargetsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListTargetsResponseMultiError) AllErrors() []error { return m }

// ListTargetsResponseValidationError is the validation error returned by
// ListTargetsResponse.Validate if the designated constraints aren't met.
type ListTargetsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListTargetsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListTargetsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListTargetsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListTargetsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListTargetsResponseValidationError) ErrorName() string {
	return "ListTargetsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListTargetsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListTargetsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListTargetsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListTargetsResponseValidationError{}

// Validate checks the field values on GetTargetRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *GetTargetRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetTargetRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetTargetRequestMultiError, or nil if none found.
func (m *GetTargetRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetTargetRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := GetTargetRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetTargetRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := GetTargetRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTargetGroupName()) > 63 {
		err := GetTargetRequestValidationError{
			field:  "TargetGroupName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetTargetRequest_TargetGroupName_Pattern.MatchString(m.GetTargetGroupName()) {
		err := GetTargetRequestValidationError{
			field:  "TargetGroupName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetTargetName()) > 63 {
		err := GetTargetRequestValidationError{
			field:  "TargetName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetTargetRequest_TargetName_Pattern.MatchString(m.GetTargetName()) {
		err := GetTargetRequestValidationError{
			field:  "TargetName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetTargetRequestMultiError(errors)
	}

	return nil
}

// GetTargetRequestMultiError is an error wrapping multiple validation errors
// returned by GetTargetRequest.ValidateAll() if the designated constraints
// aren't met.
type GetTargetRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetTargetRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetTargetRequestMultiError) AllErrors() []error { return m }

// GetTargetRequestValidationError is the validation error returned by
// GetTargetRequest.Validate if the designated constraints aren't met.
type GetTargetRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetTargetRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetTargetRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetTargetRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetTargetRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetTargetRequestValidationError) ErrorName() string { return "GetTargetRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetTargetRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetTargetRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetTargetRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetTargetRequestValidationError{}

var _GetTargetRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _GetTargetRequest_TargetGroupName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _GetTargetRequest_TargetName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on SLBStatus with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SLBStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SLBStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SLBStatusMultiError, or nil
// if none found.
func (m *SLBStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *SLBStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetSlb()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SLBStatusValidationError{
					field:  "Slb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SLBStatusValidationError{
					field:  "Slb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSlb()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SLBStatusValidationError{
				field:  "Slb",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for InternetVip

	// no validation rules for IntranetVip

	// no validation rules for BasicNetworkVip

	// no validation rules for ExposeBasicNetworkVip

	if all {
		switch v := interface{}(m.GetVpc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SLBStatusValidationError{
					field:  "Vpc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SLBStatusValidationError{
					field:  "Vpc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVpc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SLBStatusValidationError{
				field:  "Vpc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetEip()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SLBStatusValidationError{
					field:  "Eip",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SLBStatusValidationError{
					field:  "Eip",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetEip()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SLBStatusValidationError{
				field:  "Eip",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetListeners() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, SLBStatusValidationError{
						field:  fmt.Sprintf("Listeners[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, SLBStatusValidationError{
						field:  fmt.Sprintf("Listeners[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return SLBStatusValidationError{
					field:  fmt.Sprintf("Listeners[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return SLBStatusMultiError(errors)
	}

	return nil
}

// SLBStatusMultiError is an error wrapping multiple validation errors returned
// by SLBStatus.ValidateAll() if the designated constraints aren't met.
type SLBStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SLBStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SLBStatusMultiError) AllErrors() []error { return m }

// SLBStatusValidationError is the validation error returned by
// SLBStatus.Validate if the designated constraints aren't met.
type SLBStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SLBStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SLBStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SLBStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SLBStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SLBStatusValidationError) ErrorName() string { return "SLBStatusValidationError" }

// Error satisfies the builtin error interface
func (e SLBStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSLBStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SLBStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SLBStatusValidationError{}

// Validate checks the field values on Listener with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Listener) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Listener with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListenerMultiError, or nil
// if none found.
func (m *Listener) ValidateAll() error {
	return m.validate(true)
}

func (m *Listener) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Uid

	// no validation rules for Name

	if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
		err := ListenerValidationError{
			field:  "DisplayName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_Listener_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
		err := ListenerValidationError{
			field:  "DisplayName",
			reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListenerValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListenerValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListenerValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListenerValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListenerValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListenerValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListenerValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListenerValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListenerValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListenerMultiError(errors)
	}

	return nil
}

// ListenerMultiError is an error wrapping multiple validation errors returned
// by Listener.ValidateAll() if the designated constraints aren't met.
type ListenerMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListenerMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListenerMultiError) AllErrors() []error { return m }

// ListenerValidationError is the validation error returned by
// Listener.Validate if the designated constraints aren't met.
type ListenerValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListenerValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListenerValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListenerValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListenerValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListenerValidationError) ErrorName() string { return "ListenerValidationError" }

// Error satisfies the builtin error interface
func (e ListenerValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListener.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListenerValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListenerValidationError{}

var _Listener_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on ListenerProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListenerProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListenerProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListenerPropertiesMultiError, or nil if none found.
func (m *ListenerProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *ListenerProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Protocol

	// no validation rules for Port

	if all {
		switch v := interface{}(m.GetDefaultForwardAction()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListenerPropertiesValidationError{
					field:  "DefaultForwardAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListenerPropertiesValidationError{
					field:  "DefaultForwardAction",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDefaultForwardAction()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListenerPropertiesValidationError{
				field:  "DefaultForwardAction",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOriginalClientIpAddress()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListenerPropertiesValidationError{
					field:  "OriginalClientIpAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListenerPropertiesValidationError{
					field:  "OriginalClientIpAddress",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOriginalClientIpAddress()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListenerPropertiesValidationError{
				field:  "OriginalClientIpAddress",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListenerPropertiesMultiError(errors)
	}

	return nil
}

// ListenerPropertiesMultiError is an error wrapping multiple validation errors
// returned by ListenerProperties.ValidateAll() if the designated constraints
// aren't met.
type ListenerPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListenerPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListenerPropertiesMultiError) AllErrors() []error { return m }

// ListenerPropertiesValidationError is the validation error returned by
// ListenerProperties.Validate if the designated constraints aren't met.
type ListenerPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListenerPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListenerPropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListenerPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListenerPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListenerPropertiesValidationError) ErrorName() string {
	return "ListenerPropertiesValidationError"
}

// Error satisfies the builtin error interface
func (e ListenerPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListenerProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListenerPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListenerPropertiesValidationError{}

// Validate checks the field values on ListenerStatusInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListenerStatusInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListenerStatusInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListenerStatusInfoMultiError, or nil if none found.
func (m *ListenerStatusInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ListenerStatusInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetListener()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListenerStatusInfoValidationError{
					field:  "Listener",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListenerStatusInfoValidationError{
					field:  "Listener",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetListener()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListenerStatusInfoValidationError{
				field:  "Listener",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListenerStatusInfoValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListenerStatusInfoValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListenerStatusInfoValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListenerStatusInfoMultiError(errors)
	}

	return nil
}

// ListenerStatusInfoMultiError is an error wrapping multiple validation errors
// returned by ListenerStatusInfo.ValidateAll() if the designated constraints
// aren't met.
type ListenerStatusInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListenerStatusInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListenerStatusInfoMultiError) AllErrors() []error { return m }

// ListenerStatusInfoValidationError is the validation error returned by
// ListenerStatusInfo.Validate if the designated constraints aren't met.
type ListenerStatusInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListenerStatusInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListenerStatusInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListenerStatusInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListenerStatusInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListenerStatusInfoValidationError) ErrorName() string {
	return "ListenerStatusInfoValidationError"
}

// Error satisfies the builtin error interface
func (e ListenerStatusInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListenerStatusInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListenerStatusInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListenerStatusInfoValidationError{}

// Validate checks the field values on ListenerStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListenerStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListenerStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListenerStatusMultiError,
// or nil if none found.
func (m *ListenerStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *ListenerStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHealthCheckStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListenerStatusValidationError{
					field:  "HealthCheckStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListenerStatusValidationError{
					field:  "HealthCheckStatus",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHealthCheckStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListenerStatusValidationError{
				field:  "HealthCheckStatus",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListenerStatusMultiError(errors)
	}

	return nil
}

// ListenerStatusMultiError is an error wrapping multiple validation errors
// returned by ListenerStatus.ValidateAll() if the designated constraints
// aren't met.
type ListenerStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListenerStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListenerStatusMultiError) AllErrors() []error { return m }

// ListenerStatusValidationError is the validation error returned by
// ListenerStatus.Validate if the designated constraints aren't met.
type ListenerStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListenerStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListenerStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListenerStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListenerStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListenerStatusValidationError) ErrorName() string { return "ListenerStatusValidationError" }

// Error satisfies the builtin error interface
func (e ListenerStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListenerStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListenerStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListenerStatusValidationError{}

// Validate checks the field values on TargetGroup with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TargetGroup) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetGroup with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TargetGroupMultiError, or
// nil if none found.
func (m *TargetGroup) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetGroup) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Uid

	if utf8.RuneCountInString(m.GetName()) > 63 {
		err := TargetGroupValidationError{
			field:  "Name",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_TargetGroup_Name_Pattern.MatchString(m.GetName()) {
		err := TargetGroupValidationError{
			field:  "Name",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
		err := TargetGroupValidationError{
			field:  "DisplayName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_TargetGroup_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
		err := TargetGroupValidationError{
			field:  "DisplayName",
			reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetGroupValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetGroupValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetGroupValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetGroupValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetGroupValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetGroupValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetGroupValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetGroupValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetGroupValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TargetGroupMultiError(errors)
	}

	return nil
}

// TargetGroupMultiError is an error wrapping multiple validation errors
// returned by TargetGroup.ValidateAll() if the designated constraints aren't met.
type TargetGroupMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetGroupMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetGroupMultiError) AllErrors() []error { return m }

// TargetGroupValidationError is the validation error returned by
// TargetGroup.Validate if the designated constraints aren't met.
type TargetGroupValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetGroupValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetGroupValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetGroupValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetGroupValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetGroupValidationError) ErrorName() string { return "TargetGroupValidationError" }

// Error satisfies the builtin error interface
func (e TargetGroupValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetGroup.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetGroupValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetGroupValidationError{}

var _TargetGroup_Name_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _TargetGroup_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on TargetGroupProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TargetGroupProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetGroupProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TargetGroupPropertiesMultiError, or nil if none found.
func (m *TargetGroupProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetGroupProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	// no validation rules for Scheduler

	// no validation rules for Protocol

	if all {
		switch v := interface{}(m.GetHealthCheck()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetGroupPropertiesValidationError{
					field:  "HealthCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetGroupPropertiesValidationError{
					field:  "HealthCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHealthCheck()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetGroupPropertiesValidationError{
				field:  "HealthCheck",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Weight

	if all {
		switch v := interface{}(m.GetCciInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetGroupPropertiesValidationError{
					field:  "CciInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetGroupPropertiesValidationError{
					field:  "CciInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCciInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetGroupPropertiesValidationError{
				field:  "CciInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TargetGroupPropertiesMultiError(errors)
	}

	return nil
}

// TargetGroupPropertiesMultiError is an error wrapping multiple validation
// errors returned by TargetGroupProperties.ValidateAll() if the designated
// constraints aren't met.
type TargetGroupPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetGroupPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetGroupPropertiesMultiError) AllErrors() []error { return m }

// TargetGroupPropertiesValidationError is the validation error returned by
// TargetGroupProperties.Validate if the designated constraints aren't met.
type TargetGroupPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetGroupPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetGroupPropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetGroupPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetGroupPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetGroupPropertiesValidationError) ErrorName() string {
	return "TargetGroupPropertiesValidationError"
}

// Error satisfies the builtin error interface
func (e TargetGroupPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetGroupProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetGroupPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetGroupPropertiesValidationError{}

// Validate checks the field values on TargetGroupStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TargetGroupStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetGroupStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TargetGroupStatusMultiError, or nil if none found.
func (m *TargetGroupStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetGroupStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTargetGroup()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetGroupStatusValidationError{
					field:  "TargetGroup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetGroupStatusValidationError{
					field:  "TargetGroup",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTargetGroup()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetGroupStatusValidationError{
				field:  "TargetGroup",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	for idx, item := range m.GetListeners() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, TargetGroupStatusValidationError{
						field:  fmt.Sprintf("Listeners[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, TargetGroupStatusValidationError{
						field:  fmt.Sprintf("Listeners[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return TargetGroupStatusValidationError{
					field:  fmt.Sprintf("Listeners[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if all {
		switch v := interface{}(m.GetVpc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetGroupStatusValidationError{
					field:  "Vpc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetGroupStatusValidationError{
					field:  "Vpc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVpc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetGroupStatusValidationError{
				field:  "Vpc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TargetGroupStatusMultiError(errors)
	}

	return nil
}

// TargetGroupStatusMultiError is an error wrapping multiple validation errors
// returned by TargetGroupStatus.ValidateAll() if the designated constraints
// aren't met.
type TargetGroupStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetGroupStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetGroupStatusMultiError) AllErrors() []error { return m }

// TargetGroupStatusValidationError is the validation error returned by
// TargetGroupStatus.Validate if the designated constraints aren't met.
type TargetGroupStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetGroupStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetGroupStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetGroupStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetGroupStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetGroupStatusValidationError) ErrorName() string {
	return "TargetGroupStatusValidationError"
}

// Error satisfies the builtin error interface
func (e TargetGroupStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetGroupStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetGroupStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetGroupStatusValidationError{}

// Validate checks the field values on Target with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Target) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Target with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in TargetMultiError, or nil if none found.
func (m *Target) ValidateAll() error {
	return m.validate(true)
}

func (m *Target) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Uid

	if utf8.RuneCountInString(m.GetName()) > 63 {
		err := TargetValidationError{
			field:  "Name",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_Target_Name_Pattern.MatchString(m.GetName()) {
		err := TargetValidationError{
			field:  "Name",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
		err := TargetValidationError{
			field:  "DisplayName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_Target_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
		err := TargetValidationError{
			field:  "DisplayName",
			reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TargetMultiError(errors)
	}

	return nil
}

// TargetMultiError is an error wrapping multiple validation errors returned by
// Target.ValidateAll() if the designated constraints aren't met.
type TargetMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetMultiError) AllErrors() []error { return m }

// TargetValidationError is the validation error returned by Target.Validate if
// the designated constraints aren't met.
type TargetValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetValidationError) ErrorName() string { return "TargetValidationError" }

// Error satisfies the builtin error interface
func (e TargetValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTarget.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetValidationError{}

var _Target_Name_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

var _Target_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on TargetProperties with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TargetProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TargetPropertiesMultiError, or nil if none found.
func (m *TargetProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Ipv4Address

	// no validation rules for Port

	// no validation rules for Weight

	// no validation rules for Type

	// no validation rules for InstanceName

	if len(errors) > 0 {
		return TargetPropertiesMultiError(errors)
	}

	return nil
}

// TargetPropertiesMultiError is an error wrapping multiple validation errors
// returned by TargetProperties.ValidateAll() if the designated constraints
// aren't met.
type TargetPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetPropertiesMultiError) AllErrors() []error { return m }

// TargetPropertiesValidationError is the validation error returned by
// TargetProperties.Validate if the designated constraints aren't met.
type TargetPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetPropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetPropertiesValidationError) ErrorName() string { return "TargetPropertiesValidationError" }

// Error satisfies the builtin error interface
func (e TargetPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetPropertiesValidationError{}

// Validate checks the field values on HealthCheck with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HealthCheck) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HealthCheck with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HealthCheckMultiError, or
// nil if none found.
func (m *HealthCheck) ValidateAll() error {
	return m.validate(true)
}

func (m *HealthCheck) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Enable

	// no validation rules for Type

	// no validation rules for Timeout

	// no validation rules for Interval

	// no validation rules for HealthThreshold

	// no validation rules for UnhealthThreshold

	if all {
		switch v := interface{}(m.GetTcpHealthCheckConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HealthCheckValidationError{
					field:  "TcpHealthCheckConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HealthCheckValidationError{
					field:  "TcpHealthCheckConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTcpHealthCheckConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HealthCheckValidationError{
				field:  "TcpHealthCheckConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetHttpHealthCheckConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HealthCheckValidationError{
					field:  "HttpHealthCheckConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HealthCheckValidationError{
					field:  "HttpHealthCheckConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHttpHealthCheckConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HealthCheckValidationError{
				field:  "HttpHealthCheckConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return HealthCheckMultiError(errors)
	}

	return nil
}

// HealthCheckMultiError is an error wrapping multiple validation errors
// returned by HealthCheck.ValidateAll() if the designated constraints aren't met.
type HealthCheckMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HealthCheckMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HealthCheckMultiError) AllErrors() []error { return m }

// HealthCheckValidationError is the validation error returned by
// HealthCheck.Validate if the designated constraints aren't met.
type HealthCheckValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HealthCheckValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HealthCheckValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HealthCheckValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HealthCheckValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HealthCheckValidationError) ErrorName() string { return "HealthCheckValidationError" }

// Error satisfies the builtin error interface
func (e HealthCheckValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHealthCheck.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HealthCheckValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HealthCheckValidationError{}

// Validate checks the field values on ListenerHealthCheckStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListenerHealthCheckStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListenerHealthCheckStatus with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListenerHealthCheckStatusMultiError, or nil if none found.
func (m *ListenerHealthCheckStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *ListenerHealthCheckStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for State

	if len(errors) > 0 {
		return ListenerHealthCheckStatusMultiError(errors)
	}

	return nil
}

// ListenerHealthCheckStatusMultiError is an error wrapping multiple validation
// errors returned by ListenerHealthCheckStatus.ValidateAll() if the
// designated constraints aren't met.
type ListenerHealthCheckStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListenerHealthCheckStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListenerHealthCheckStatusMultiError) AllErrors() []error { return m }

// ListenerHealthCheckStatusValidationError is the validation error returned by
// ListenerHealthCheckStatus.Validate if the designated constraints aren't met.
type ListenerHealthCheckStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListenerHealthCheckStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListenerHealthCheckStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListenerHealthCheckStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListenerHealthCheckStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListenerHealthCheckStatusValidationError) ErrorName() string {
	return "ListenerHealthCheckStatusValidationError"
}

// Error satisfies the builtin error interface
func (e ListenerHealthCheckStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListenerHealthCheckStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListenerHealthCheckStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListenerHealthCheckStatusValidationError{}

// Validate checks the field values on TargetInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TargetInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TargetInfoMultiError, or
// nil if none found.
func (m *TargetInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetTarget()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetInfoValidationError{
					field:  "Target",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetInfoValidationError{
					field:  "Target",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetTarget()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetInfoValidationError{
				field:  "Target",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetStatus()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetInfoValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetInfoValidationError{
					field:  "Status",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetStatus()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetInfoValidationError{
				field:  "Status",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TargetInfoMultiError(errors)
	}

	return nil
}

// TargetInfoMultiError is an error wrapping multiple validation errors
// returned by TargetInfo.ValidateAll() if the designated constraints aren't met.
type TargetInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetInfoMultiError) AllErrors() []error { return m }

// TargetInfoValidationError is the validation error returned by
// TargetInfo.Validate if the designated constraints aren't met.
type TargetInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetInfoValidationError) ErrorName() string { return "TargetInfoValidationError" }

// Error satisfies the builtin error interface
func (e TargetInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetInfoValidationError{}

// Validate checks the field values on TargetStatus with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TargetStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetStatus with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TargetStatusMultiError, or
// nil if none found.
func (m *TargetStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetHealthCheck()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetStatusValidationError{
					field:  "HealthCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetStatusValidationError{
					field:  "HealthCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHealthCheck()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetStatusValidationError{
				field:  "HealthCheck",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return TargetStatusMultiError(errors)
	}

	return nil
}

// TargetStatusMultiError is an error wrapping multiple validation errors
// returned by TargetStatus.ValidateAll() if the designated constraints aren't met.
type TargetStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetStatusMultiError) AllErrors() []error { return m }

// TargetStatusValidationError is the validation error returned by
// TargetStatus.Validate if the designated constraints aren't met.
type TargetStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetStatusValidationError) ErrorName() string { return "TargetStatusValidationError" }

// Error satisfies the builtin error interface
func (e TargetStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetStatusValidationError{}

// Validate checks the field values on TargetHealthCheckStatus with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TargetHealthCheckStatus) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetHealthCheckStatus with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TargetHealthCheckStatusMultiError, or nil if none found.
func (m *TargetHealthCheckStatus) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetHealthCheckStatus) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for State

	if len(errors) > 0 {
		return TargetHealthCheckStatusMultiError(errors)
	}

	return nil
}

// TargetHealthCheckStatusMultiError is an error wrapping multiple validation
// errors returned by TargetHealthCheckStatus.ValidateAll() if the designated
// constraints aren't met.
type TargetHealthCheckStatusMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetHealthCheckStatusMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetHealthCheckStatusMultiError) AllErrors() []error { return m }

// TargetHealthCheckStatusValidationError is the validation error returned by
// TargetHealthCheckStatus.Validate if the designated constraints aren't met.
type TargetHealthCheckStatusValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetHealthCheckStatusValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetHealthCheckStatusValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetHealthCheckStatusValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetHealthCheckStatusValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetHealthCheckStatusValidationError) ErrorName() string {
	return "TargetHealthCheckStatusValidationError"
}

// Error satisfies the builtin error interface
func (e TargetHealthCheckStatusValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetHealthCheckStatus.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetHealthCheckStatusValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetHealthCheckStatusValidationError{}

// Validate checks the field values on ForwardAction with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ForwardAction) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForwardAction with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ForwardActionMultiError, or
// nil if none found.
func (m *ForwardAction) ValidateAll() error {
	return m.validate(true)
}

func (m *ForwardAction) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Type

	if all {
		switch v := interface{}(m.GetForwardTargetGroupConfig()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ForwardActionValidationError{
					field:  "ForwardTargetGroupConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ForwardActionValidationError{
					field:  "ForwardTargetGroupConfig",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetForwardTargetGroupConfig()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ForwardActionValidationError{
				field:  "ForwardTargetGroupConfig",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ForwardActionMultiError(errors)
	}

	return nil
}

// ForwardActionMultiError is an error wrapping multiple validation errors
// returned by ForwardAction.ValidateAll() if the designated constraints
// aren't met.
type ForwardActionMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForwardActionMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForwardActionMultiError) AllErrors() []error { return m }

// ForwardActionValidationError is the validation error returned by
// ForwardAction.Validate if the designated constraints aren't met.
type ForwardActionValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForwardActionValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForwardActionValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForwardActionValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForwardActionValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForwardActionValidationError) ErrorName() string { return "ForwardActionValidationError" }

// Error satisfies the builtin error interface
func (e ForwardActionValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForwardAction.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForwardActionValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForwardActionValidationError{}

// Validate checks the field values on ForwardTargetGroupConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ForwardTargetGroupConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ForwardTargetGroupConfig with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ForwardTargetGroupConfigMultiError, or nil if none found.
func (m *ForwardTargetGroupConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *ForwardTargetGroupConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetTargetGroups() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ForwardTargetGroupConfigValidationError{
						field:  fmt.Sprintf("TargetGroups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ForwardTargetGroupConfigValidationError{
						field:  fmt.Sprintf("TargetGroups[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ForwardTargetGroupConfigValidationError{
					field:  fmt.Sprintf("TargetGroups[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	if len(errors) > 0 {
		return ForwardTargetGroupConfigMultiError(errors)
	}

	return nil
}

// ForwardTargetGroupConfigMultiError is an error wrapping multiple validation
// errors returned by ForwardTargetGroupConfig.ValidateAll() if the designated
// constraints aren't met.
type ForwardTargetGroupConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ForwardTargetGroupConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ForwardTargetGroupConfigMultiError) AllErrors() []error { return m }

// ForwardTargetGroupConfigValidationError is the validation error returned by
// ForwardTargetGroupConfig.Validate if the designated constraints aren't met.
type ForwardTargetGroupConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ForwardTargetGroupConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ForwardTargetGroupConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ForwardTargetGroupConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ForwardTargetGroupConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ForwardTargetGroupConfigValidationError) ErrorName() string {
	return "ForwardTargetGroupConfigValidationError"
}

// Error satisfies the builtin error interface
func (e ForwardTargetGroupConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sForwardTargetGroupConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ForwardTargetGroupConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ForwardTargetGroupConfigValidationError{}

// Validate checks the field values on OriginalClientIPAddress with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *OriginalClientIPAddress) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on OriginalClientIPAddress with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// OriginalClientIPAddressMultiError, or nil if none found.
func (m *OriginalClientIPAddress) ValidateAll() error {
	return m.validate(true)
}

func (m *OriginalClientIPAddress) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for OriginalClientIpAddressEnable

	// no validation rules for ProxyProtocolEnable

	if len(errors) > 0 {
		return OriginalClientIPAddressMultiError(errors)
	}

	return nil
}

// OriginalClientIPAddressMultiError is an error wrapping multiple validation
// errors returned by OriginalClientIPAddress.ValidateAll() if the designated
// constraints aren't met.
type OriginalClientIPAddressMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m OriginalClientIPAddressMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m OriginalClientIPAddressMultiError) AllErrors() []error { return m }

// OriginalClientIPAddressValidationError is the validation error returned by
// OriginalClientIPAddress.Validate if the designated constraints aren't met.
type OriginalClientIPAddressValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e OriginalClientIPAddressValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e OriginalClientIPAddressValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e OriginalClientIPAddressValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e OriginalClientIPAddressValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e OriginalClientIPAddressValidationError) ErrorName() string {
	return "OriginalClientIPAddressValidationError"
}

// Error satisfies the builtin error interface
func (e OriginalClientIPAddressValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sOriginalClientIPAddress.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = OriginalClientIPAddressValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = OriginalClientIPAddressValidationError{}

// Validate checks the field values on TCPHealthCheckConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *TCPHealthCheckConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TCPHealthCheckConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TCPHealthCheckConfigMultiError, or nil if none found.
func (m *TCPHealthCheckConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *TCPHealthCheckConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Port

	if len(errors) > 0 {
		return TCPHealthCheckConfigMultiError(errors)
	}

	return nil
}

// TCPHealthCheckConfigMultiError is an error wrapping multiple validation
// errors returned by TCPHealthCheckConfig.ValidateAll() if the designated
// constraints aren't met.
type TCPHealthCheckConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TCPHealthCheckConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TCPHealthCheckConfigMultiError) AllErrors() []error { return m }

// TCPHealthCheckConfigValidationError is the validation error returned by
// TCPHealthCheckConfig.Validate if the designated constraints aren't met.
type TCPHealthCheckConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TCPHealthCheckConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TCPHealthCheckConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TCPHealthCheckConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TCPHealthCheckConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TCPHealthCheckConfigValidationError) ErrorName() string {
	return "TCPHealthCheckConfigValidationError"
}

// Error satisfies the builtin error interface
func (e TCPHealthCheckConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTCPHealthCheckConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TCPHealthCheckConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TCPHealthCheckConfigValidationError{}

// Validate checks the field values on HTTPHealthCheckConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *HTTPHealthCheckConfig) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HTTPHealthCheckConfig with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// HTTPHealthCheckConfigMultiError, or nil if none found.
func (m *HTTPHealthCheckConfig) ValidateAll() error {
	return m.validate(true)
}

func (m *HTTPHealthCheckConfig) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Host

	// no validation rules for HostType

	// no validation rules for Path

	// no validation rules for Method

	// no validation rules for Version

	if len(errors) > 0 {
		return HTTPHealthCheckConfigMultiError(errors)
	}

	return nil
}

// HTTPHealthCheckConfigMultiError is an error wrapping multiple validation
// errors returned by HTTPHealthCheckConfig.ValidateAll() if the designated
// constraints aren't met.
type HTTPHealthCheckConfigMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HTTPHealthCheckConfigMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HTTPHealthCheckConfigMultiError) AllErrors() []error { return m }

// HTTPHealthCheckConfigValidationError is the validation error returned by
// HTTPHealthCheckConfig.Validate if the designated constraints aren't met.
type HTTPHealthCheckConfigValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HTTPHealthCheckConfigValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HTTPHealthCheckConfigValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HTTPHealthCheckConfigValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HTTPHealthCheckConfigValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HTTPHealthCheckConfigValidationError) ErrorName() string {
	return "HTTPHealthCheckConfigValidationError"
}

// Error satisfies the builtin error interface
func (e HTTPHealthCheckConfigValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHTTPHealthCheckConfig.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HTTPHealthCheckConfigValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HTTPHealthCheckConfigValidationError{}

// Validate checks the field values on VPCInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VPCInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VPCInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in VPCInfoMultiError, or nil if none found.
func (m *VPCInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *VPCInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for DisplayName

	if len(errors) > 0 {
		return VPCInfoMultiError(errors)
	}

	return nil
}

// VPCInfoMultiError is an error wrapping multiple validation errors returned
// by VPCInfo.ValidateAll() if the designated constraints aren't met.
type VPCInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VPCInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VPCInfoMultiError) AllErrors() []error { return m }

// VPCInfoValidationError is the validation error returned by VPCInfo.Validate
// if the designated constraints aren't met.
type VPCInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VPCInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VPCInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VPCInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VPCInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VPCInfoValidationError) ErrorName() string { return "VPCInfoValidationError" }

// Error satisfies the builtin error interface
func (e VPCInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVPCInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VPCInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VPCInfoValidationError{}

// Validate checks the field values on EIPInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *EIPInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on EIPInfo with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in EIPInfoMultiError, or nil if none found.
func (m *EIPInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *EIPInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for DisplayName

	if len(errors) > 0 {
		return EIPInfoMultiError(errors)
	}

	return nil
}

// EIPInfoMultiError is an error wrapping multiple validation errors returned
// by EIPInfo.ValidateAll() if the designated constraints aren't met.
type EIPInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m EIPInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m EIPInfoMultiError) AllErrors() []error { return m }

// EIPInfoValidationError is the validation error returned by EIPInfo.Validate
// if the designated constraints aren't met.
type EIPInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e EIPInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e EIPInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e EIPInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e EIPInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e EIPInfoValidationError) ErrorName() string { return "EIPInfoValidationError" }

// Error satisfies the builtin error interface
func (e EIPInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sEIPInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = EIPInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = EIPInfoValidationError{}

// Validate checks the field values on ListenerInfo with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListenerInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListenerInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListenerInfoMultiError, or
// nil if none found.
func (m *ListenerInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *ListenerInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for DisplayName

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListenerInfoValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListenerInfoValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListenerInfoValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ListenerInfoMultiError(errors)
	}

	return nil
}

// ListenerInfoMultiError is an error wrapping multiple validation errors
// returned by ListenerInfo.ValidateAll() if the designated constraints aren't met.
type ListenerInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListenerInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListenerInfoMultiError) AllErrors() []error { return m }

// ListenerInfoValidationError is the validation error returned by
// ListenerInfo.Validate if the designated constraints aren't met.
type ListenerInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListenerInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListenerInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListenerInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListenerInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListenerInfoValidationError) ErrorName() string { return "ListenerInfoValidationError" }

// Error satisfies the builtin error interface
func (e ListenerInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListenerInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListenerInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListenerInfoValidationError{}

// Validate checks the field values on TargetGroupInfo with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TargetGroupInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetGroupInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TargetGroupInfoMultiError, or nil if none found.
func (m *TargetGroupInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetGroupInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Uid

	// no validation rules for Name

	// no validation rules for DisplayName

	if len(errors) > 0 {
		return TargetGroupInfoMultiError(errors)
	}

	return nil
}

// TargetGroupInfoMultiError is an error wrapping multiple validation errors
// returned by TargetGroupInfo.ValidateAll() if the designated constraints
// aren't met.
type TargetGroupInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetGroupInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetGroupInfoMultiError) AllErrors() []error { return m }

// TargetGroupInfoValidationError is the validation error returned by
// TargetGroupInfo.Validate if the designated constraints aren't met.
type TargetGroupInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetGroupInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetGroupInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetGroupInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetGroupInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetGroupInfoValidationError) ErrorName() string { return "TargetGroupInfoValidationError" }

// Error satisfies the builtin error interface
func (e TargetGroupInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetGroupInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetGroupInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetGroupInfoValidationError{}

// Validate checks the field values on ListenerUpdate with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *ListenerUpdate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListenerUpdate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ListenerUpdateMultiError,
// or nil if none found.
func (m *ListenerUpdate) ValidateAll() error {
	return m.validate(true)
}

func (m *ListenerUpdate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ListenerUpdateValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ListenerUpdateValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ListenerUpdateValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.DisplayName != nil {

		if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
			err := ListenerUpdateValidationError{
				field:  "DisplayName",
				reason: "value length must be at most 63 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_ListenerUpdate_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
			err := ListenerUpdateValidationError{
				field:  "DisplayName",
				reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return ListenerUpdateMultiError(errors)
	}

	return nil
}

// ListenerUpdateMultiError is an error wrapping multiple validation errors
// returned by ListenerUpdate.ValidateAll() if the designated constraints
// aren't met.
type ListenerUpdateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListenerUpdateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListenerUpdateMultiError) AllErrors() []error { return m }

// ListenerUpdateValidationError is the validation error returned by
// ListenerUpdate.Validate if the designated constraints aren't met.
type ListenerUpdateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListenerUpdateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListenerUpdateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListenerUpdateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListenerUpdateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListenerUpdateValidationError) ErrorName() string { return "ListenerUpdateValidationError" }

// Error satisfies the builtin error interface
func (e ListenerUpdateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListenerUpdate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListenerUpdateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListenerUpdateValidationError{}

var _ListenerUpdate_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on TargetGroupUpdate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *TargetGroupUpdate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetGroupUpdate with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// TargetGroupUpdateMultiError, or nil if none found.
func (m *TargetGroupUpdate) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetGroupUpdate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetGroupUpdateValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetGroupUpdateValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetGroupUpdateValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.DisplayName != nil {

		if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
			err := TargetGroupUpdateValidationError{
				field:  "DisplayName",
				reason: "value length must be at most 63 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_TargetGroupUpdate_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
			err := TargetGroupUpdateValidationError{
				field:  "DisplayName",
				reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return TargetGroupUpdateMultiError(errors)
	}

	return nil
}

// TargetGroupUpdateMultiError is an error wrapping multiple validation errors
// returned by TargetGroupUpdate.ValidateAll() if the designated constraints
// aren't met.
type TargetGroupUpdateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetGroupUpdateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetGroupUpdateMultiError) AllErrors() []error { return m }

// TargetGroupUpdateValidationError is the validation error returned by
// TargetGroupUpdate.Validate if the designated constraints aren't met.
type TargetGroupUpdateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetGroupUpdateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetGroupUpdateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetGroupUpdateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetGroupUpdateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetGroupUpdateValidationError) ErrorName() string {
	return "TargetGroupUpdateValidationError"
}

// Error satisfies the builtin error interface
func (e TargetGroupUpdateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetGroupUpdate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetGroupUpdateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetGroupUpdateValidationError{}

var _TargetGroupUpdate_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on TargetUpdate with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *TargetUpdate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on TargetUpdate with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in TargetUpdateMultiError, or
// nil if none found.
func (m *TargetUpdate) ValidateAll() error {
	return m.validate(true)
}

func (m *TargetUpdate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Uid

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, TargetUpdateValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, TargetUpdateValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return TargetUpdateValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.DisplayName != nil {

		if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
			err := TargetUpdateValidationError{
				field:  "DisplayName",
				reason: "value length must be at most 63 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_TargetUpdate_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
			err := TargetUpdateValidationError{
				field:  "DisplayName",
				reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return TargetUpdateMultiError(errors)
	}

	return nil
}

// TargetUpdateMultiError is an error wrapping multiple validation errors
// returned by TargetUpdate.ValidateAll() if the designated constraints aren't met.
type TargetUpdateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m TargetUpdateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m TargetUpdateMultiError) AllErrors() []error { return m }

// TargetUpdateValidationError is the validation error returned by
// TargetUpdate.Validate if the designated constraints aren't met.
type TargetUpdateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e TargetUpdateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e TargetUpdateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e TargetUpdateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e TargetUpdateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e TargetUpdateValidationError) ErrorName() string { return "TargetUpdateValidationError" }

// Error satisfies the builtin error interface
func (e TargetUpdateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sTargetUpdate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = TargetUpdateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = TargetUpdateValidationError{}

var _TargetUpdate_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on CciDeploymentInstanceInfo with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CciDeploymentInstanceInfo) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CciDeploymentInstanceInfo with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CciDeploymentInstanceInfoMultiError, or nil if none found.
func (m *CciDeploymentInstanceInfo) ValidateAll() error {
	return m.validate(true)
}

func (m *CciDeploymentInstanceInfo) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	// no validation rules for DisplayName

	// no validation rules for Id

	// no validation rules for Uid

	// no validation rules for Port

	if len(errors) > 0 {
		return CciDeploymentInstanceInfoMultiError(errors)
	}

	return nil
}

// CciDeploymentInstanceInfoMultiError is an error wrapping multiple validation
// errors returned by CciDeploymentInstanceInfo.ValidateAll() if the
// designated constraints aren't met.
type CciDeploymentInstanceInfoMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CciDeploymentInstanceInfoMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CciDeploymentInstanceInfoMultiError) AllErrors() []error { return m }

// CciDeploymentInstanceInfoValidationError is the validation error returned by
// CciDeploymentInstanceInfo.Validate if the designated constraints aren't met.
type CciDeploymentInstanceInfoValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CciDeploymentInstanceInfoValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CciDeploymentInstanceInfoValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CciDeploymentInstanceInfoValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CciDeploymentInstanceInfoValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CciDeploymentInstanceInfoValidationError) ErrorName() string {
	return "CciDeploymentInstanceInfoValidationError"
}

// Error satisfies the builtin error interface
func (e CciDeploymentInstanceInfoValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCciDeploymentInstanceInfo.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CciDeploymentInstanceInfoValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CciDeploymentInstanceInfoValidationError{}
