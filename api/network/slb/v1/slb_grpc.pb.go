// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/slb/v1/slb.proto

package slb

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// SLBsClient is the client API for SLBs service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type SLBsClient interface {
	// [zh] 创建负载均衡资源.
	// [en] Creates SLB resources.
	CreateSLB(ctx context.Context, in *CreateSLBRequest, opts ...grpc.CallOption) (*SLB, error)
	// [zh] 删除负载均衡资源.
	// [en] Delete the SLB resources.
	DeleteSLB(ctx context.Context, in *DeleteSLBRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// [zh] 更新负载均衡资源.
	// [en] Update the SLB resources.
	UpdateSLB(ctx context.Context, in *UpdateSLBRequest, opts ...grpc.CallOption) (*SLB, error)
	// [zh] 查看负载均衡资源详情.
	// [en] Gets details of a SLB resources.
	GetSLB(ctx context.Context, in *GetSLBRequest, opts ...grpc.CallOption) (*SLB, error)
	// [zh] 释放 (保留期释放).
	// [en] The Release of a SLB resources.
	ReleaseSLB(ctx context.Context, in *ReleaseSLBRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type sLBsClient struct {
	cc grpc.ClientConnInterface
}

func NewSLBsClient(cc grpc.ClientConnInterface) SLBsClient {
	return &sLBsClient{cc}
}

func (c *sLBsClient) CreateSLB(ctx context.Context, in *CreateSLBRequest, opts ...grpc.CallOption) (*SLB, error) {
	out := new(SLB)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBs/CreateSLB", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBsClient) DeleteSLB(ctx context.Context, in *DeleteSLBRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBs/DeleteSLB", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBsClient) UpdateSLB(ctx context.Context, in *UpdateSLBRequest, opts ...grpc.CallOption) (*SLB, error) {
	out := new(SLB)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBs/UpdateSLB", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBsClient) GetSLB(ctx context.Context, in *GetSLBRequest, opts ...grpc.CallOption) (*SLB, error) {
	out := new(SLB)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBs/GetSLB", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *sLBsClient) ReleaseSLB(ctx context.Context, in *ReleaseSLBRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.slb.v1.SLBs/ReleaseSLB", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// SLBsServer is the server API for SLBs service.
// All implementations must embed UnimplementedSLBsServer
// for forward compatibility
type SLBsServer interface {
	// [zh] 创建负载均衡资源.
	// [en] Creates SLB resources.
	CreateSLB(context.Context, *CreateSLBRequest) (*SLB, error)
	// [zh] 删除负载均衡资源.
	// [en] Delete the SLB resources.
	DeleteSLB(context.Context, *DeleteSLBRequest) (*emptypb.Empty, error)
	// [zh] 更新负载均衡资源.
	// [en] Update the SLB resources.
	UpdateSLB(context.Context, *UpdateSLBRequest) (*SLB, error)
	// [zh] 查看负载均衡资源详情.
	// [en] Gets details of a SLB resources.
	GetSLB(context.Context, *GetSLBRequest) (*SLB, error)
	// [zh] 释放 (保留期释放).
	// [en] The Release of a SLB resources.
	ReleaseSLB(context.Context, *ReleaseSLBRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedSLBsServer()
}

// UnimplementedSLBsServer must be embedded to have forward compatible implementations.
type UnimplementedSLBsServer struct {
}

func (UnimplementedSLBsServer) CreateSLB(context.Context, *CreateSLBRequest) (*SLB, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateSLB not implemented")
}
func (UnimplementedSLBsServer) DeleteSLB(context.Context, *DeleteSLBRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteSLB not implemented")
}
func (UnimplementedSLBsServer) UpdateSLB(context.Context, *UpdateSLBRequest) (*SLB, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateSLB not implemented")
}
func (UnimplementedSLBsServer) GetSLB(context.Context, *GetSLBRequest) (*SLB, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetSLB not implemented")
}
func (UnimplementedSLBsServer) ReleaseSLB(context.Context, *ReleaseSLBRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseSLB not implemented")
}
func (UnimplementedSLBsServer) mustEmbedUnimplementedSLBsServer() {}

// UnsafeSLBsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to SLBsServer will
// result in compilation errors.
type UnsafeSLBsServer interface {
	mustEmbedUnimplementedSLBsServer()
}

func RegisterSLBsServer(s grpc.ServiceRegistrar, srv SLBsServer) {
	s.RegisterService(&SLBs_ServiceDesc, srv)
}

func _SLBs_CreateSLB_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateSLBRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBsServer).CreateSLB(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBs/CreateSLB",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBsServer).CreateSLB(ctx, req.(*CreateSLBRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBs_DeleteSLB_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteSLBRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBsServer).DeleteSLB(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBs/DeleteSLB",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBsServer).DeleteSLB(ctx, req.(*DeleteSLBRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBs_UpdateSLB_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateSLBRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBsServer).UpdateSLB(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBs/UpdateSLB",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBsServer).UpdateSLB(ctx, req.(*UpdateSLBRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBs_GetSLB_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetSLBRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBsServer).GetSLB(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBs/GetSLB",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBsServer).GetSLB(ctx, req.(*GetSLBRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _SLBs_ReleaseSLB_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseSLBRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(SLBsServer).ReleaseSLB(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.slb.v1.SLBs/ReleaseSLB",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(SLBsServer).ReleaseSLB(ctx, req.(*ReleaseSLBRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// SLBs_ServiceDesc is the grpc.ServiceDesc for SLBs service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var SLBs_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.slb.v1.SLBs",
	HandlerType: (*SLBsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateSLB",
			Handler:    _SLBs_CreateSLB_Handler,
		},
		{
			MethodName: "DeleteSLB",
			Handler:    _SLBs_DeleteSLB_Handler,
		},
		{
			MethodName: "UpdateSLB",
			Handler:    _SLBs_UpdateSLB_Handler,
		},
		{
			MethodName: "GetSLB",
			Handler:    _SLBs_GetSLB_Handler,
		},
		{
			MethodName: "ReleaseSLB",
			Handler:    _SLBs_ReleaseSLB_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/slb/v1/slb.proto",
}
