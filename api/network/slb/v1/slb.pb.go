// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/slb/v1/slb.proto

package slb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/api/annotations"
	v1 "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// [zh] SLB 类型
// [en] SLB loadbalancer type
type LoadBalancerType int32

const (
	// [zh] 外网，默认值
	// [en] internet, default
	LoadBalancerType_INTERNET LoadBalancerType = 0
	// [zh] 内网
	// [en] intranet
	LoadBalancerType_INTRANET LoadBalancerType = 1
)

// Enum value maps for LoadBalancerType.
var (
	LoadBalancerType_name = map[int32]string{
		0: "INTERNET",
		1: "INTRANET",
	}
	LoadBalancerType_value = map[string]int32{
		"INTERNET": 0,
		"INTRANET": 1,
	}
)

func (x LoadBalancerType) Enum() *LoadBalancerType {
	p := new(LoadBalancerType)
	*p = x
	return p
}

func (x LoadBalancerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (LoadBalancerType) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_proto_enumTypes[0].Descriptor()
}

func (LoadBalancerType) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_proto_enumTypes[0]
}

func (x LoadBalancerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use LoadBalancerType.Descriptor instead.
func (LoadBalancerType) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_proto_rawDescGZIP(), []int{0}
}

// [zh] IP 版本枚举
// [en] Represents the different states of IP version
type SLBIPVersion int32

const (
	// [zh] IPv4, 默认值
	// [en] IPv4, default
	SLBIPVersion_IPV4 SLBIPVersion = 0
	// [zh] 双栈
	// [en] Dual Stack
	SLBIPVersion_DUAL_STACK SLBIPVersion = 1
)

// Enum value maps for SLBIPVersion.
var (
	SLBIPVersion_name = map[int32]string{
		0: "IPV4",
		1: "DUAL_STACK",
	}
	SLBIPVersion_value = map[string]int32{
		"IPV4":       0,
		"DUAL_STACK": 1,
	}
)

func (x SLBIPVersion) Enum() *SLBIPVersion {
	p := new(SLBIPVersion)
	*p = x
	return p
}

func (x SLBIPVersion) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SLBIPVersion) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_proto_enumTypes[1].Descriptor()
}

func (SLBIPVersion) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_proto_enumTypes[1]
}

func (x SLBIPVersion) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SLBIPVersion.Descriptor instead.
func (SLBIPVersion) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_proto_rawDescGZIP(), []int{1}
}

// [zh] 资源状态.
// [en] Represents the different states of a SLB.
type SLB_State int32

const (
	// [zh] 创建中.
	// [en] The SLB resource is being created.
	SLB_CREATING SLB_State = 0
	// [zh] 更新中.
	// [en] The SLB resource is being updated.
	SLB_UPDATING SLB_State = 1
	// [zh] 已激活.
	// [en] The SLB resource has been active.
	SLB_ACTIVE SLB_State = 2
	// [zh] 删除中.
	// [en] TThe SLB resource is being deleted.
	SLB_DELETING SLB_State = 3
	// [zh] 已删除.
	// [en] The SLB resource has been deleted.
	SLB_DELETED SLB_State = 4
	// [zh] 操作失败.
	// [en] The SLB resource has failed.
	SLB_FAILED SLB_State = 5
	// [zh] 到期降级中【未使用】
	// [en]  The SLB resource is being expireDowngrading.
	SLB_EXPIREDOWNGRADING SLB_State = 6
	// [zh] 到期已降级【未使用】
	// [en]   The SLB resource has been expireDowngrade.
	SLB_EXPIREDOWNGRADED SLB_State = 7
	// [zh] 续订升级中【未使用】
	// [en]   The SLB resource is being renewUpgrading.
	SLB_RENEWUPGRADING SLB_State = 8
	// [zh] 到期停服中.
	// [en] The SLB resource is being disabled.
	SLB_EXPIRESTOPPING SLB_State = 9
	// [zh] 到期已停服.
	// [en] The SLB resource has been disabled.
	SLB_EXPIRESTOPPED SLB_State = 10
	// [zh] 续订恢复中.
	// [en] The SLB resource is being enabled.
	SLB_RENEWSTARTING SLB_State = 11
	// [zh] 服务降级中【瞬时】【未使用】.
	// [en] Default, the SLB is being downgrading.
	SLB_DOWNGRADING SLB_State = 12
	// [zh] 服务降级【未使用】.
	// [en] Default, the SLB is being downgraded.
	SLB_DOWNGRADE SLB_State = 13
	// [zh] 服务降级恢复中【瞬时】【未使用】.
	// [en] Default, the SLB is being restoring.
	SLB_RESTORING SLB_State = 14
	// [zh] 欠费停服中【瞬时】【未使用】.
	// [en] Default, the SLB is being arrearStopping.
	SLB_ARREARSTOPPING SLB_State = 15
	// [zh] 欠费停服【未使用】
	// [en] Default, the SLB is being arrearStopped.
	SLB_ARREARSTOPPED SLB_State = 16
	// [zh] 充值恢复中【瞬时】【未使用】.
	// [en] Default, the SLB is being rechargeStarting.
	SLB_RECHARGESTARTING SLB_State = 17
	// [zh] 资源变更中【瞬时】【未使用】.
	// [en] Default, the SLB is being resizing.
	SLB_RESIZING SLB_State = 18
	// [zh] 取消资源变更【瞬时】【未使用】.
	// [en] Default, the aoss pack is being resizeCanceling.
	SLB_RESIZECANCELING SLB_State = 19
)

// Enum value maps for SLB_State.
var (
	SLB_State_name = map[int32]string{
		0:  "CREATING",
		1:  "UPDATING",
		2:  "ACTIVE",
		3:  "DELETING",
		4:  "DELETED",
		5:  "FAILED",
		6:  "EXPIREDOWNGRADING",
		7:  "EXPIREDOWNGRADED",
		8:  "RENEWUPGRADING",
		9:  "EXPIRESTOPPING",
		10: "EXPIRESTOPPED",
		11: "RENEWSTARTING",
		12: "DOWNGRADING",
		13: "DOWNGRADE",
		14: "RESTORING",
		15: "ARREARSTOPPING",
		16: "ARREARSTOPPED",
		17: "RECHARGESTARTING",
		18: "RESIZING",
		19: "RESIZECANCELING",
	}
	SLB_State_value = map[string]int32{
		"CREATING":          0,
		"UPDATING":          1,
		"ACTIVE":            2,
		"DELETING":          3,
		"DELETED":           4,
		"FAILED":            5,
		"EXPIREDOWNGRADING": 6,
		"EXPIREDOWNGRADED":  7,
		"RENEWUPGRADING":    8,
		"EXPIRESTOPPING":    9,
		"EXPIRESTOPPED":     10,
		"RENEWSTARTING":     11,
		"DOWNGRADING":       12,
		"DOWNGRADE":         13,
		"RESTORING":         14,
		"ARREARSTOPPING":    15,
		"ARREARSTOPPED":     16,
		"RECHARGESTARTING":  17,
		"RESIZING":          18,
		"RESIZECANCELING":   19,
	}
)

func (x SLB_State) Enum() *SLB_State {
	p := new(SLB_State)
	*p = x
	return p
}

func (x SLB_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (SLB_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_proto_enumTypes[2].Descriptor()
}

func (SLB_State) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_proto_enumTypes[2]
}

func (x SLB_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use SLB_State.Descriptor instead.
func (SLB_State) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_proto_rawDescGZIP(), []int{5, 0}
}

// [zh] 创建负载均衡资源请求.
// [en] CreateSLBRequest.
type CreateSLBRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 创建负载均衡资源.
	// [en] The SLB resource to create.
	Slb *SLB `protobuf:"bytes,5,opt,name=slb,proto3" json:"slb,omitempty"` // [(validate.rules).message.required = true];
}

func (x *CreateSLBRequest) Reset() {
	*x = CreateSLBRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateSLBRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateSLBRequest) ProtoMessage() {}

func (x *CreateSLBRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateSLBRequest.ProtoReflect.Descriptor instead.
func (*CreateSLBRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_proto_rawDescGZIP(), []int{0}
}

func (x *CreateSLBRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *CreateSLBRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *CreateSLBRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateSLBRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *CreateSLBRequest) GetSlb() *SLB {
	if x != nil {
		return x.Slb
	}
	return nil
}

// [zh] 删除负载均衡资源请求.
// [en] DeleteSLBRequest.
type DeleteSLBRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
}

func (x *DeleteSLBRequest) Reset() {
	*x = DeleteSLBRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteSLBRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteSLBRequest) ProtoMessage() {}

func (x *DeleteSLBRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteSLBRequest.ProtoReflect.Descriptor instead.
func (*DeleteSLBRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_proto_rawDescGZIP(), []int{1}
}

func (x *DeleteSLBRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DeleteSLBRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *DeleteSLBRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteSLBRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

// [zh] 更新负载均衡请求.
// [en] Update SLB request.
type UpdateSLBRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] The resource subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] The resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] The resource available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 资源名称.
	// [en] The resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 更新资源.
	// [en] The resource to update.
	Slb *SlbUpdate `protobuf:"bytes,5,opt,name=slb,proto3" json:"slb,omitempty"`
	// [zh] 更新标记.
	// [en] The resource update mask.
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,6,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateSLBRequest) Reset() {
	*x = UpdateSLBRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateSLBRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateSLBRequest) ProtoMessage() {}

func (x *UpdateSLBRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateSLBRequest.ProtoReflect.Descriptor instead.
func (*UpdateSLBRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateSLBRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *UpdateSLBRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *UpdateSLBRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateSLBRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *UpdateSLBRequest) GetSlb() *SlbUpdate {
	if x != nil {
		return x.Slb
	}
	return nil
}

func (x *UpdateSLBRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// [zh] 查看负载均衡资源详情请求.
// [en] GetSLBRequest.
type GetSLBRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
}

func (x *GetSLBRequest) Reset() {
	*x = GetSLBRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSLBRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSLBRequest) ProtoMessage() {}

func (x *GetSLBRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSLBRequest.ProtoReflect.Descriptor instead.
func (*GetSLBRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_proto_rawDescGZIP(), []int{3}
}

func (x *GetSLBRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetSLBRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetSLBRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetSLBRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

// [zh] 释放负载均衡资源.
// [en] Release SLB resource.
type ReleaseSLBRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅名.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
}

func (x *ReleaseSLBRequest) Reset() {
	*x = ReleaseSLBRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleaseSLBRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseSLBRequest) ProtoMessage() {}

func (x *ReleaseSLBRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseSLBRequest.ProtoReflect.Descriptor instead.
func (*ReleaseSLBRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_proto_rawDescGZIP(), []int{4}
}

func (x *ReleaseSLBRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ReleaseSLBRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ReleaseSLBRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ReleaseSLBRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

// [zh] 负载均衡资源.
// [en] The SLB resource.
type SLB struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源id.
	// [en] The SLB resource id using the form:
	//     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/slbs/{name}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// [zh] 资源的uuid.
	// [en] The SLB resource uuid.
	Uid string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// [zh] 资源标识.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] 资源名称.
	// [en] The SLB resource display name
	DisplayName string `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// [zh] 资源描述.
	// [en] The SLB resource description.
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// [zh] 资源类型.
	// [en] The SLB resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// [zh] 创建者id.
	// [en] The id of the user who created the SLB resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// [zh] 拥有者id.
	// [en] The id of the user who owns the SLB resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// [zh] 租户id
	// [en] Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 资源状态.
	// [en] The current state of the SLB resource.
	State SLB_State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.slb.v1.SLB_State" json:"state,omitempty"`
	// [zh] 最小库存单元id.
	// [en] SKU id.
	SkuId string `protobuf:"bytes,12,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// [zh] 负载均衡资源的标签.
	// [en] Tags attached to the VirtualMachine resource.
	Tags map[string]string `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// [zh] 负载均衡资源的属性
	// [en] Properties of the VirtualMachine resource.
	Properties *SLBProperties `protobuf:"bytes,14,opt,name=properties,proto3" json:"properties,omitempty"` // [(validate.rules).message.required = true];
	// [zh] 订单信息.
	// [en] Order information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,15,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"` // [(validate.rules).message.required = true];
	// [zh] 资源是否已删除.
	// [en] Indicates whether the SLB resource is deleted or not.
	Deleted bool `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// [zh] 资源创建时间.
	// [en] The time when the SLB resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// [zh] 资源更新时间.
	// [en] The time when the SLB resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *SLB) Reset() {
	*x = SLB{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SLB) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SLB) ProtoMessage() {}

func (x *SLB) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SLB.ProtoReflect.Descriptor instead.
func (*SLB) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_proto_rawDescGZIP(), []int{5}
}

func (x *SLB) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *SLB) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *SLB) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *SLB) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *SLB) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SLB) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *SLB) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *SLB) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *SLB) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *SLB) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *SLB) GetState() SLB_State {
	if x != nil {
		return x.State
	}
	return SLB_CREATING
}

func (x *SLB) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *SLB) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *SLB) GetProperties() *SLBProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *SLB) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

func (x *SLB) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *SLB) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *SLB) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// [zh] 资源属性.
// [en] The SLBProperties.
type SLBProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源规格属性.
	// [en] Resource Specification Properties.
	Resources *Resources `protobuf:"bytes,1,opt,name=resources,proto3" json:"resources,omitempty"`
	// [zh] SLB 类型
	// [en] SLB loadbalancer type
	Type LoadBalancerType `protobuf:"varint,2,opt,name=type,proto3,enum=sensetime.core.network.slb.v1.LoadBalancerType" json:"type,omitempty"`
	// [zh] 所属 VPC uid
	// [en] VPC uid
	VpcId string `protobuf:"bytes,3,opt,name=vpc_id,json=vpcId,proto3" json:"vpc_id,omitempty"`
	// [zh] IP 协议栈版本
	// [en] IP protocol version
	IpVersion SLBIPVersion `protobuf:"varint,4,opt,name=ip_version,json=ipVersion,proto3,enum=sensetime.core.network.slb.v1.SLBIPVersion" json:"ip_version,omitempty"`
	// [zh] 所关联的 EIP uid.
	// [en] EIP uid.
	EipId string `protobuf:"bytes,5,opt,name=eip_id,json=eipId,proto3" json:"eip_id,omitempty"`
}

func (x *SLBProperties) Reset() {
	*x = SLBProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SLBProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SLBProperties) ProtoMessage() {}

func (x *SLBProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SLBProperties.ProtoReflect.Descriptor instead.
func (*SLBProperties) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_proto_rawDescGZIP(), []int{6}
}

func (x *SLBProperties) GetResources() *Resources {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *SLBProperties) GetType() LoadBalancerType {
	if x != nil {
		return x.Type
	}
	return LoadBalancerType_INTERNET
}

func (x *SLBProperties) GetVpcId() string {
	if x != nil {
		return x.VpcId
	}
	return ""
}

func (x *SLBProperties) GetIpVersion() SLBIPVersion {
	if x != nil {
		return x.IpVersion
	}
	return SLBIPVersion_IPV4
}

func (x *SLBProperties) GetEipId() string {
	if x != nil {
		return x.EipId
	}
	return ""
}

// [zh] 资源规格属性
// [en] Resource Specification Properties.
type Resources struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源的规格限制，对应运营系统SLB的描述属性
	// [en] Capacity limitations
	CapacityLimitations *CapacityLimitations `protobuf:"bytes,1,opt,name=capacity_limitations,json=capacityLimitations,proto3" json:"capacity_limitations,omitempty"`
}

func (x *Resources) Reset() {
	*x = Resources{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Resources) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Resources) ProtoMessage() {}

func (x *Resources) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Resources.ProtoReflect.Descriptor instead.
func (*Resources) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_proto_rawDescGZIP(), []int{7}
}

func (x *Resources) GetCapacityLimitations() *CapacityLimitations {
	if x != nil {
		return x.CapacityLimitations
	}
	return nil
}

// [zh] 资源的描述属性
// [en] Description attribute
type CapacityLimitations struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] TCP 新建连接数：每秒处理的新建TCP连接的数量，单位:秒
	// [en] CPS: the number of new TCP connections processed per second; Unit:Second;
	TcpCps int32 `protobuf:"varint,1,opt,name=tcp_cps,json=tcpCps,proto3" json:"tcp_cps,omitempty"`
	// [zh] TCP 并发连接数：每分钟内并发TCP连接的数量，单位：分钟
	// [en] CONNS: the number of concurrent TCP connections per minute; Unit:Minute
	TcpConns int32 `protobuf:"varint,2,opt,name=tcp_conns,json=tcpConns,proto3" json:"tcp_conns,omitempty"`
	// [zh] UDP 新建连接数：每秒处理的新建UDP连接的数量，单位：秒
	// [en] The number of new UDP connections processed per second; Unit: Second
	UdpCps int32 `protobuf:"varint,3,opt,name=udp_cps,json=udpCps,proto3" json:"udp_cps,omitempty"`
	// [zh] UDP 并发连接数：每分钟内并发UDP连接的数量，单位：分钟
	// [en] The number of concurrent UDP connections per minute; Unit: Second
	UdpConns int32 `protobuf:"varint,4,opt,name=udp_conns,json=udpConns,proto3" json:"udp_conns,omitempty"`
	// [zh] TCPSSL 新建连接数：每秒处理的新建TCPSSL连接的数量，单位：秒
	// [en] The number of new TCPSSL connections processed per second; Unit: Second
	TcpsslCps int32 `protobuf:"varint,5,opt,name=tcpssl_cps,json=tcpsslCps,proto3" json:"tcpssl_cps,omitempty"`
	// [zh] TCPSSL 并发连接数：每分钟内并发TCPSSL连接的数量，单位：分钟
	// [en] The number of concurrent TCPSSL connections per minute; Unit: Second
	TcpsslConns int32 `protobuf:"varint,6,opt,name=tcpssl_conns,json=tcpsslConns,proto3" json:"tcpssl_conns,omitempty"`
	// [zh] HTTP/ 新建连接数：每秒处理的新建HTTP/连接的数量，单位：秒
	// [en] The number of new HTTP/ connections processed per second; Unit: Second
	HttpCps int32 `protobuf:"varint,7,opt,name=http_cps,json=httpCps,proto3" json:"http_cps,omitempty"`
	// [zh] HTTP/ 并发连接数：每分钟内并发HTTP/连接的数量，单位：分钟
	// [en] The number of concurrent HTTP/ connections per minute; Unit: Second
	HttpConns int32 `protobuf:"varint,8,opt,name=http_conns,json=httpConns,proto3" json:"http_conns,omitempty"`
	// [zh] HTTP/ 每秒查询数：每秒可以完成的HTTP或的查询（请求）的数量，单位：秒
	// [en] QPS: The number of HTTP/ queries (requests) that can be processed per second; Unit: Second
	HttpQps int32 `protobuf:"varint,9,opt,name=http_qps,json=httpQps,proto3" json:"http_qps,omitempty"`
	// [zh] HTTPS 新建连接数：每秒处理的新建HTTPS连接的数量，单位：秒
	// [en] The number of new HTTPS connections processed per second; Unit: Second
	HttpsCps int32 `protobuf:"varint,10,opt,name=https_cps,json=httpsCps,proto3" json:"https_cps,omitempty"`
	// [zh] HTTPS 并发连接数：每分钟内并发HTTPS连接的数量，单位：分钟
	// [en] The number of concurrent HTTPS connections per minute; Unit: Second
	HttpsConns int32 `protobuf:"varint,11,opt,name=https_conns,json=httpsConns,proto3" json:"https_conns,omitempty"`
	// [zh] HTTPS 每秒查询数：每秒可以完成的HTTPS的查询（请求）的数量，单位：秒
	// [en] QPS: The number of HTTPS queries (requests) that can be processed per second; Unit: Second
	HttpsQps int32 `protobuf:"varint,12,opt,name=https_qps,json=httpsQps,proto3" json:"https_qps,omitempty"`
}

func (x *CapacityLimitations) Reset() {
	*x = CapacityLimitations{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CapacityLimitations) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CapacityLimitations) ProtoMessage() {}

func (x *CapacityLimitations) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CapacityLimitations.ProtoReflect.Descriptor instead.
func (*CapacityLimitations) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_proto_rawDescGZIP(), []int{8}
}

func (x *CapacityLimitations) GetTcpCps() int32 {
	if x != nil {
		return x.TcpCps
	}
	return 0
}

func (x *CapacityLimitations) GetTcpConns() int32 {
	if x != nil {
		return x.TcpConns
	}
	return 0
}

func (x *CapacityLimitations) GetUdpCps() int32 {
	if x != nil {
		return x.UdpCps
	}
	return 0
}

func (x *CapacityLimitations) GetUdpConns() int32 {
	if x != nil {
		return x.UdpConns
	}
	return 0
}

func (x *CapacityLimitations) GetTcpsslCps() int32 {
	if x != nil {
		return x.TcpsslCps
	}
	return 0
}

func (x *CapacityLimitations) GetTcpsslConns() int32 {
	if x != nil {
		return x.TcpsslConns
	}
	return 0
}

func (x *CapacityLimitations) GetHttpCps() int32 {
	if x != nil {
		return x.HttpCps
	}
	return 0
}

func (x *CapacityLimitations) GetHttpConns() int32 {
	if x != nil {
		return x.HttpConns
	}
	return 0
}

func (x *CapacityLimitations) GetHttpQps() int32 {
	if x != nil {
		return x.HttpQps
	}
	return 0
}

func (x *CapacityLimitations) GetHttpsCps() int32 {
	if x != nil {
		return x.HttpsCps
	}
	return 0
}

func (x *CapacityLimitations) GetHttpsConns() int32 {
	if x != nil {
		return x.HttpsConns
	}
	return 0
}

func (x *CapacityLimitations) GetHttpsQps() int32 {
	if x != nil {
		return x.HttpsQps
	}
	return 0
}

// [zh] 负载均衡资源.
// [en] The SLB resource.
type SlbUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源名称.
	// [en] The SLB resource display name
	DisplayName *string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3,oneof" json:"display_name,omitempty"`
	// [zh] 资源描述.
	// [en] The SLB resource description.
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// [zh] 资源规格属性.
	// [en] Resource Specification Properties.
	Resources *Resources `protobuf:"bytes,3,opt,name=resources,proto3" json:"resources,omitempty"`
	// [zh] SLB 类型
	// [en] SLB loadbalancer type
	Type LoadBalancerType `protobuf:"varint,4,opt,name=type,proto3,enum=sensetime.core.network.slb.v1.LoadBalancerType" json:"type,omitempty"`
	// [zh] IP 协议栈版本
	// [en] IP protocol version
	IpVersion SLBIPVersion `protobuf:"varint,5,opt,name=ip_version,json=ipVersion,proto3,enum=sensetime.core.network.slb.v1.SLBIPVersion" json:"ip_version,omitempty"`
	// [zh] 所属的 EIP uid.
	// [en] EIP uid.
	EipId string `protobuf:"bytes,6,opt,name=eip_id,json=eipId,proto3" json:"eip_id,omitempty"`
}

func (x *SlbUpdate) Reset() {
	*x = SlbUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SlbUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SlbUpdate) ProtoMessage() {}

func (x *SlbUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SlbUpdate.ProtoReflect.Descriptor instead.
func (*SlbUpdate) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_proto_rawDescGZIP(), []int{9}
}

func (x *SlbUpdate) GetDisplayName() string {
	if x != nil && x.DisplayName != nil {
		return *x.DisplayName
	}
	return ""
}

func (x *SlbUpdate) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *SlbUpdate) GetResources() *Resources {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *SlbUpdate) GetType() LoadBalancerType {
	if x != nil {
		return x.Type
	}
	return LoadBalancerType_INTERNET
}

func (x *SlbUpdate) GetIpVersion() SLBIPVersion {
	if x != nil {
		return x.IpVersion
	}
	return SLBIPVersion_IPV4
}

func (x *SlbUpdate) GetEipId() string {
	if x != nil {
		return x.EipId
	}
	return ""
}

var File_network_slb_v1_slb_proto protoreflect.FileDescriptor

var file_network_slb_v1_slb_proto_rawDesc = []byte{
	0x0a, 0x18, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x6c, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68, 0x61, 0x76, 0x69,
	0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x69,
	0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0x80, 0x02, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x4c, 0x42, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x45, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f,
	0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x29, 0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x34, 0x0a, 0x03,
	0x73, 0x6c, 0x62, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4c, 0x42, 0x52, 0x03, 0x73,
	0x6c, 0x62, 0x22, 0xdc, 0x01, 0x0a, 0x10, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x4c, 0x42,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17,
	0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42,
	0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0xda, 0x02, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x4c, 0x42, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27,
	0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x3a, 0x0a, 0x03, 0x73, 0x6c, 0x62, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x6c,
	0x62, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x03, 0x73, 0x6c, 0x62, 0x12, 0x40, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x42, 0x03, 0xe0,
	0x41, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b, 0x22, 0xd9,
	0x01, 0x0a, 0x0d, 0x47, 0x65, 0x74, 0x53, 0x4c, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d,
	0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f,
	0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xdd, 0x01, 0x0a, 0x11, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x4c, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d,
	0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f,
	0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xfa, 0x09, 0x0a, 0x03, 0x53,
	0x4c, 0x42, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03,
	0xe0, 0x41, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x73, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x50, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x4a,
	0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d,
	0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61,
	0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78,
	0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f,
	0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22,
	0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72,
	0x49, 0x64, 0x12, 0x1e, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72,
	0x49, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18,
	0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x43, 0x0a,
	0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x28, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4c, 0x42,
	0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x1a, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x40,
	0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4c, 0x42,
	0x2e, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x12, 0x4c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4c, 0x42, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x48,
	0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0f, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e,
	0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x07,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x40, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52,
	0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x37, 0x0a, 0x09, 0x54,
	0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x3a, 0x02, 0x38, 0x01, 0x22, 0xe0, 0x02, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0c,
	0x0a, 0x08, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08,
	0x55, 0x50, 0x44, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43,
	0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10,
	0x04, 0x12, 0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x15, 0x0a,
	0x11, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x4f, 0x57, 0x4e, 0x47, 0x52, 0x41, 0x44, 0x49,
	0x4e, 0x47, 0x10, 0x06, 0x12, 0x14, 0x0a, 0x10, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x44, 0x4f,
	0x57, 0x4e, 0x47, 0x52, 0x41, 0x44, 0x45, 0x44, 0x10, 0x07, 0x12, 0x12, 0x0a, 0x0e, 0x52, 0x45,
	0x4e, 0x45, 0x57, 0x55, 0x50, 0x47, 0x52, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x08, 0x12, 0x12,
	0x0a, 0x0e, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x49, 0x4e, 0x47,
	0x10, 0x09, 0x12, 0x11, 0x0a, 0x0d, 0x45, 0x58, 0x50, 0x49, 0x52, 0x45, 0x53, 0x54, 0x4f, 0x50,
	0x50, 0x45, 0x44, 0x10, 0x0a, 0x12, 0x11, 0x0a, 0x0d, 0x52, 0x45, 0x4e, 0x45, 0x57, 0x53, 0x54,
	0x41, 0x52, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x0b, 0x12, 0x0f, 0x0a, 0x0b, 0x44, 0x4f, 0x57, 0x4e,
	0x47, 0x52, 0x41, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x0c, 0x12, 0x0d, 0x0a, 0x09, 0x44, 0x4f, 0x57,
	0x4e, 0x47, 0x52, 0x41, 0x44, 0x45, 0x10, 0x0d, 0x12, 0x0d, 0x0a, 0x09, 0x52, 0x45, 0x53, 0x54,
	0x4f, 0x52, 0x49, 0x4e, 0x47, 0x10, 0x0e, 0x12, 0x12, 0x0a, 0x0e, 0x41, 0x52, 0x52, 0x45, 0x41,
	0x52, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x0f, 0x12, 0x11, 0x0a, 0x0d, 0x41,
	0x52, 0x52, 0x45, 0x41, 0x52, 0x53, 0x54, 0x4f, 0x50, 0x50, 0x45, 0x44, 0x10, 0x10, 0x12, 0x14,
	0x0a, 0x10, 0x52, 0x45, 0x43, 0x48, 0x41, 0x52, 0x47, 0x45, 0x53, 0x54, 0x41, 0x52, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0x11, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x53, 0x49, 0x5a, 0x49, 0x4e, 0x47,
	0x10, 0x12, 0x12, 0x13, 0x0a, 0x0f, 0x52, 0x45, 0x53, 0x49, 0x5a, 0x45, 0x43, 0x41, 0x4e, 0x43,
	0x45, 0x4c, 0x49, 0x4e, 0x47, 0x10, 0x13, 0x22, 0x96, 0x02, 0x0a, 0x0d, 0x53, 0x4c, 0x42, 0x50,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x46, 0x0a, 0x09, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x28, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x73, 0x12, 0x43, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65,
	0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x76, 0x70, 0x63, 0x5f, 0x69, 0x64,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x70, 0x63, 0x49, 0x64, 0x12, 0x4a, 0x0a,
	0x0a, 0x69, 0x70, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x2b, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x53, 0x4c, 0x42, 0x49, 0x50, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x09,
	0x69, 0x70, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x69, 0x70,
	0x5f, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x69, 0x70, 0x49, 0x64,
	0x22, 0x72, 0x0a, 0x09, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x65, 0x0a,
	0x14, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x5f, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x61,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x61, 0x70, 0x61,
	0x63, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x52,
	0x13, 0x63, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74, 0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x22, 0x9b, 0x03, 0x0a, 0x13, 0x43, 0x61, 0x70, 0x61, 0x63, 0x69, 0x74,
	0x79, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x12, 0x17, 0x0a, 0x07,
	0x74, 0x63, 0x70, 0x5f, 0x63, 0x70, 0x73, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x74,
	0x63, 0x70, 0x43, 0x70, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x63, 0x70, 0x5f, 0x63, 0x6f, 0x6e,
	0x6e, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x74, 0x63, 0x70, 0x43, 0x6f, 0x6e,
	0x6e, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x75, 0x64, 0x70, 0x5f, 0x63, 0x70, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x06, 0x75, 0x64, 0x70, 0x43, 0x70, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x75,
	0x64, 0x70, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x75, 0x64, 0x70, 0x43, 0x6f, 0x6e, 0x6e, 0x73, 0x12, 0x22, 0x0a, 0x0a, 0x74, 0x63, 0x70, 0x73,
	0x73, 0x6c, 0x5f, 0x63, 0x70, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41,
	0x03, 0x52, 0x09, 0x74, 0x63, 0x70, 0x73, 0x73, 0x6c, 0x43, 0x70, 0x73, 0x12, 0x26, 0x0a, 0x0c,
	0x74, 0x63, 0x70, 0x73, 0x73, 0x6c, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x73, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0b, 0x74, 0x63, 0x70, 0x73, 0x73, 0x6c, 0x43,
	0x6f, 0x6e, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x08, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x63, 0x70, 0x73,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x07, 0x68, 0x74, 0x74,
	0x70, 0x43, 0x70, 0x73, 0x12, 0x22, 0x0a, 0x0a, 0x68, 0x74, 0x74, 0x70, 0x5f, 0x63, 0x6f, 0x6e,
	0x6e, 0x73, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x09, 0x68,
	0x74, 0x74, 0x70, 0x43, 0x6f, 0x6e, 0x6e, 0x73, 0x12, 0x1e, 0x0a, 0x08, 0x68, 0x74, 0x74, 0x70,
	0x5f, 0x71, 0x70, 0x73, 0x18, 0x09, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52,
	0x07, 0x68, 0x74, 0x74, 0x70, 0x51, 0x70, 0x73, 0x12, 0x20, 0x0a, 0x09, 0x68, 0x74, 0x74, 0x70,
	0x73, 0x5f, 0x63, 0x70, 0x73, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x03,
	0x52, 0x08, 0x68, 0x74, 0x74, 0x70, 0x73, 0x43, 0x70, 0x73, 0x12, 0x24, 0x0a, 0x0b, 0x68, 0x74,
	0x74, 0x70, 0x73, 0x5f, 0x63, 0x6f, 0x6e, 0x6e, 0x73, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x05, 0x42,
	0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x68, 0x74, 0x74, 0x70, 0x73, 0x43, 0x6f, 0x6e, 0x6e, 0x73,
	0x12, 0x20, 0x0a, 0x09, 0x68, 0x74, 0x74, 0x70, 0x73, 0x5f, 0x71, 0x70, 0x73, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x05, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x08, 0x68, 0x74, 0x74, 0x70, 0x73, 0x51,
	0x70, 0x73, 0x22, 0xa8, 0x03, 0x0a, 0x09, 0x53, 0x6c, 0x62, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0x78, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x50, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x4a, 0x72, 0x48,
	0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c,
	0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d,
	0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34,
	0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d,
	0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x46, 0x0a, 0x09,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x52, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x73, 0x12, 0x43, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x54,
	0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x4a, 0x0a, 0x0a, 0x69, 0x70, 0x5f,
	0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4c,
	0x42, 0x49, 0x50, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x52, 0x09, 0x69, 0x70, 0x56, 0x65,
	0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x69, 0x70, 0x5f, 0x69, 0x64, 0x18,
	0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x69, 0x70, 0x49, 0x64, 0x42, 0x0f, 0x0a, 0x0d,
	0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x2a, 0x2e, 0x0a,
	0x10, 0x4c, 0x6f, 0x61, 0x64, 0x42, 0x61, 0x6c, 0x61, 0x6e, 0x63, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x45, 0x52, 0x4e, 0x45, 0x54, 0x10, 0x00, 0x12,
	0x0c, 0x0a, 0x08, 0x49, 0x4e, 0x54, 0x52, 0x41, 0x4e, 0x45, 0x54, 0x10, 0x01, 0x2a, 0x28, 0x0a,
	0x0c, 0x53, 0x4c, 0x42, 0x49, 0x50, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x08, 0x0a,
	0x04, 0x49, 0x50, 0x56, 0x34, 0x10, 0x00, 0x12, 0x0e, 0x0a, 0x0a, 0x44, 0x55, 0x41, 0x4c, 0x5f,
	0x53, 0x54, 0x41, 0x43, 0x4b, 0x10, 0x01, 0x32, 0xf7, 0x0d, 0x0a, 0x04, 0x53, 0x4c, 0x42, 0x73,
	0x12, 0xe8, 0x02, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x4c, 0x42, 0x12, 0x2f,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x53, 0x4c, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x4c, 0x42, 0x22, 0x85, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7a, 0x22, 0x73, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x3a, 0x03, 0x73, 0x6c, 0x62, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5,
	0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a,
	0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73,
	0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x73, 0x6c, 0x62,
	0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0xd7, 0x02, 0x0a, 0x09,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x53, 0x4c, 0x42, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x53, 0x4c, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x80, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x75, 0x2a, 0x73, 0x2f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d,
	0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72,
	0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b,
	0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x12, 0xe8, 0x02, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x53, 0x4c, 0x42, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x53, 0x4c, 0x42, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4c, 0x42, 0x22, 0x85, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x7a, 0x32, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f,
	0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x03, 0x73, 0x6c, 0x62, 0x80, 0xb5, 0x18, 0x01, 0x88,
	0xb5, 0x18, 0x00, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d,
	0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x12, 0x0e, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0xd6, 0x02, 0x0a, 0x06, 0x47, 0x65, 0x74, 0x53, 0x4c, 0x42, 0x12, 0x2c, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53,
	0x4c, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4c, 0x42, 0x22, 0xf9, 0x01,
	0x82, 0xd3, 0xe4, 0x93, 0x02, 0x75, 0x12, 0x73, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a,
	0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73,
	0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x80, 0xb5, 0x18, 0x01, 0x9a,
	0xb5, 0x18, 0x76, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62,
	0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0b, 0x73, 0x6c,
	0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x67, 0x65, 0x74, 0x12, 0xe5, 0x02, 0x0a, 0x0a, 0x52, 0x65,
	0x6c, 0x65, 0x61, 0x73, 0x65, 0x53, 0x4c, 0x42, 0x12, 0x30, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65,
	0x53, 0x4c, 0x42, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x8c, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x80, 0x01, 0x22, 0x7b, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x3a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x3a, 0x01, 0x2a, 0x80, 0xb5, 0x18, 0x01,
	0x88, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x12, 0x0e, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x42, 0x50, 0x5a, 0x4e, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x62, 0x6a, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x6c, 0x65,
	0x6d, 0x65, 0x6e, 0x74, 0x61, 0x72, 0x79, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2f, 0x62, 0x6f,
	0x73, 0x6f, 0x6e, 0x2d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x76, 0x31, 0x3b,
	0x73, 0x6c, 0x62, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_network_slb_v1_slb_proto_rawDescOnce sync.Once
	file_network_slb_v1_slb_proto_rawDescData = file_network_slb_v1_slb_proto_rawDesc
)

func file_network_slb_v1_slb_proto_rawDescGZIP() []byte {
	file_network_slb_v1_slb_proto_rawDescOnce.Do(func() {
		file_network_slb_v1_slb_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_slb_v1_slb_proto_rawDescData)
	})
	return file_network_slb_v1_slb_proto_rawDescData
}

var file_network_slb_v1_slb_proto_enumTypes = make([]protoimpl.EnumInfo, 3)
var file_network_slb_v1_slb_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_network_slb_v1_slb_proto_goTypes = []interface{}{
	(LoadBalancerType)(0),         // 0: sensetime.core.network.slb.v1.LoadBalancerType
	(SLBIPVersion)(0),             // 1: sensetime.core.network.slb.v1.SLBIPVersion
	(SLB_State)(0),                // 2: sensetime.core.network.slb.v1.SLB.State
	(*CreateSLBRequest)(nil),      // 3: sensetime.core.network.slb.v1.CreateSLBRequest
	(*DeleteSLBRequest)(nil),      // 4: sensetime.core.network.slb.v1.DeleteSLBRequest
	(*UpdateSLBRequest)(nil),      // 5: sensetime.core.network.slb.v1.UpdateSLBRequest
	(*GetSLBRequest)(nil),         // 6: sensetime.core.network.slb.v1.GetSLBRequest
	(*ReleaseSLBRequest)(nil),     // 7: sensetime.core.network.slb.v1.ReleaseSLBRequest
	(*SLB)(nil),                   // 8: sensetime.core.network.slb.v1.SLB
	(*SLBProperties)(nil),         // 9: sensetime.core.network.slb.v1.SLBProperties
	(*Resources)(nil),             // 10: sensetime.core.network.slb.v1.Resources
	(*CapacityLimitations)(nil),   // 11: sensetime.core.network.slb.v1.CapacityLimitations
	(*SlbUpdate)(nil),             // 12: sensetime.core.network.slb.v1.SlbUpdate
	nil,                           // 13: sensetime.core.network.slb.v1.SLB.TagsEntry
	(*fieldmaskpb.FieldMask)(nil), // 14: google.protobuf.FieldMask
	(*v1.OrderInfo)(nil),          // 15: sensetime.core.higgs.common.v1.OrderInfo
	(*timestamppb.Timestamp)(nil), // 16: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),         // 17: google.protobuf.Empty
}
var file_network_slb_v1_slb_proto_depIdxs = []int32{
	8,  // 0: sensetime.core.network.slb.v1.CreateSLBRequest.slb:type_name -> sensetime.core.network.slb.v1.SLB
	12, // 1: sensetime.core.network.slb.v1.UpdateSLBRequest.slb:type_name -> sensetime.core.network.slb.v1.SlbUpdate
	14, // 2: sensetime.core.network.slb.v1.UpdateSLBRequest.update_mask:type_name -> google.protobuf.FieldMask
	2,  // 3: sensetime.core.network.slb.v1.SLB.state:type_name -> sensetime.core.network.slb.v1.SLB.State
	13, // 4: sensetime.core.network.slb.v1.SLB.tags:type_name -> sensetime.core.network.slb.v1.SLB.TagsEntry
	9,  // 5: sensetime.core.network.slb.v1.SLB.properties:type_name -> sensetime.core.network.slb.v1.SLBProperties
	15, // 6: sensetime.core.network.slb.v1.SLB.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	16, // 7: sensetime.core.network.slb.v1.SLB.create_time:type_name -> google.protobuf.Timestamp
	16, // 8: sensetime.core.network.slb.v1.SLB.update_time:type_name -> google.protobuf.Timestamp
	10, // 9: sensetime.core.network.slb.v1.SLBProperties.resources:type_name -> sensetime.core.network.slb.v1.Resources
	0,  // 10: sensetime.core.network.slb.v1.SLBProperties.type:type_name -> sensetime.core.network.slb.v1.LoadBalancerType
	1,  // 11: sensetime.core.network.slb.v1.SLBProperties.ip_version:type_name -> sensetime.core.network.slb.v1.SLBIPVersion
	11, // 12: sensetime.core.network.slb.v1.Resources.capacity_limitations:type_name -> sensetime.core.network.slb.v1.CapacityLimitations
	10, // 13: sensetime.core.network.slb.v1.SlbUpdate.resources:type_name -> sensetime.core.network.slb.v1.Resources
	0,  // 14: sensetime.core.network.slb.v1.SlbUpdate.type:type_name -> sensetime.core.network.slb.v1.LoadBalancerType
	1,  // 15: sensetime.core.network.slb.v1.SlbUpdate.ip_version:type_name -> sensetime.core.network.slb.v1.SLBIPVersion
	3,  // 16: sensetime.core.network.slb.v1.SLBs.CreateSLB:input_type -> sensetime.core.network.slb.v1.CreateSLBRequest
	4,  // 17: sensetime.core.network.slb.v1.SLBs.DeleteSLB:input_type -> sensetime.core.network.slb.v1.DeleteSLBRequest
	5,  // 18: sensetime.core.network.slb.v1.SLBs.UpdateSLB:input_type -> sensetime.core.network.slb.v1.UpdateSLBRequest
	6,  // 19: sensetime.core.network.slb.v1.SLBs.GetSLB:input_type -> sensetime.core.network.slb.v1.GetSLBRequest
	7,  // 20: sensetime.core.network.slb.v1.SLBs.ReleaseSLB:input_type -> sensetime.core.network.slb.v1.ReleaseSLBRequest
	8,  // 21: sensetime.core.network.slb.v1.SLBs.CreateSLB:output_type -> sensetime.core.network.slb.v1.SLB
	17, // 22: sensetime.core.network.slb.v1.SLBs.DeleteSLB:output_type -> google.protobuf.Empty
	8,  // 23: sensetime.core.network.slb.v1.SLBs.UpdateSLB:output_type -> sensetime.core.network.slb.v1.SLB
	8,  // 24: sensetime.core.network.slb.v1.SLBs.GetSLB:output_type -> sensetime.core.network.slb.v1.SLB
	17, // 25: sensetime.core.network.slb.v1.SLBs.ReleaseSLB:output_type -> google.protobuf.Empty
	21, // [21:26] is the sub-list for method output_type
	16, // [16:21] is the sub-list for method input_type
	16, // [16:16] is the sub-list for extension type_name
	16, // [16:16] is the sub-list for extension extendee
	0,  // [0:16] is the sub-list for field type_name
}

func init() { file_network_slb_v1_slb_proto_init() }
func file_network_slb_v1_slb_proto_init() {
	if File_network_slb_v1_slb_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_network_slb_v1_slb_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateSLBRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteSLBRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateSLBRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSLBRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleaseSLBRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SLB); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SLBProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Resources); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CapacityLimitations); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SlbUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_network_slb_v1_slb_proto_msgTypes[9].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_slb_v1_slb_proto_rawDesc,
			NumEnums:      3,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_network_slb_v1_slb_proto_goTypes,
		DependencyIndexes: file_network_slb_v1_slb_proto_depIdxs,
		EnumInfos:         file_network_slb_v1_slb_proto_enumTypes,
		MessageInfos:      file_network_slb_v1_slb_proto_msgTypes,
	}.Build()
	File_network_slb_v1_slb_proto = out.File
	file_network_slb_v1_slb_proto_rawDesc = nil
	file_network_slb_v1_slb_proto_goTypes = nil
	file_network_slb_v1_slb_proto_depIdxs = nil
}
