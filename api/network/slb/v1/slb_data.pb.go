// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/slb/v1/slb_data.proto

package slb

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/api/annotations"
	_ "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// [zh] 监听器状态
// [en] Represents the different states of a Listener.
type Listener_State int32

const (
	// [zh] 创建中
	// [en] The Listener resource is being created.
	Listener_CREATING Listener_State = 0
	// [zh] 更新中
	// [en] The Listener resource is being updated.
	Listener_UPDATING Listener_State = 1
	// [zh] 已激活
	// [en] The Listener resource has been active.
	Listener_ACTIVE Listener_State = 2
	// [zh] 删除中
	// [en] The Listener resource is being deleting.
	Listener_DELETING Listener_State = 3
	// [zh] 已删除
	// [en] The Listener resource has been deleted.
	Listener_DELETED Listener_State = 4
	// [zh] 操作失败
	// [en] The Listener resource has been failed.
	Listener_FAILED Listener_State = 5
	// [zh] 停止中
	// [en] The Listener resource has been stopping
	Listener_STOPPING Listener_State = 6
	// [zh] 已停止
	// [en] The Listener resource has been stopped
	Listener_STOPPED Listener_State = 7
	// [zh] 启动中
	// [en] The Listener resource has been starting
	Listener_STARTING Listener_State = 8
)

// Enum value maps for Listener_State.
var (
	Listener_State_name = map[int32]string{
		0: "CREATING",
		1: "UPDATING",
		2: "ACTIVE",
		3: "DELETING",
		4: "DELETED",
		5: "FAILED",
		6: "STOPPING",
		7: "STOPPED",
		8: "STARTING",
	}
	Listener_State_value = map[string]int32{
		"CREATING": 0,
		"UPDATING": 1,
		"ACTIVE":   2,
		"DELETING": 3,
		"DELETED":  4,
		"FAILED":   5,
		"STOPPING": 6,
		"STOPPED":  7,
		"STARTING": 8,
	}
)

func (x Listener_State) Enum() *Listener_State {
	p := new(Listener_State)
	*p = x
	return p
}

func (x Listener_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Listener_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[0].Descriptor()
}

func (Listener_State) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[0]
}

func (x Listener_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Listener_State.Descriptor instead.
func (Listener_State) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{29, 0}
}

// [zh] 监听的协议枚举
// [en] Listener protocol enum
type ListenerProperties_ListenProtocol int32

const (
	// [zh] 无效值
	// [en] Invalid protocol
	ListenerProperties_INVALID ListenerProperties_ListenProtocol = 0
	// [zh] TCP
	// [en] TCP
	ListenerProperties_TCP ListenerProperties_ListenProtocol = 1
	// [zh] UDP
	// [en] UDP
	ListenerProperties_UDP ListenerProperties_ListenProtocol = 2
)

// Enum value maps for ListenerProperties_ListenProtocol.
var (
	ListenerProperties_ListenProtocol_name = map[int32]string{
		0: "INVALID",
		1: "TCP",
		2: "UDP",
	}
	ListenerProperties_ListenProtocol_value = map[string]int32{
		"INVALID": 0,
		"TCP":     1,
		"UDP":     2,
	}
)

func (x ListenerProperties_ListenProtocol) Enum() *ListenerProperties_ListenProtocol {
	p := new(ListenerProperties_ListenProtocol)
	*p = x
	return p
}

func (x ListenerProperties_ListenProtocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListenerProperties_ListenProtocol) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[1].Descriptor()
}

func (ListenerProperties_ListenProtocol) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[1]
}

func (x ListenerProperties_ListenProtocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListenerProperties_ListenProtocol.Descriptor instead.
func (ListenerProperties_ListenProtocol) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{30, 0}
}

// [zh] 后端组状态
// [en] Represents the different states of a TargetGroup.
type TargetGroup_State int32

const (
	// [zh] 创建中
	// [en] The TargetGroup resource is being created.
	TargetGroup_CREATING TargetGroup_State = 0
	// [zh] 更新中
	// [en] The TargetGroup resource is being updated.
	TargetGroup_UPDATING TargetGroup_State = 1
	// [zh] 已激活
	// [en] The TargetGroup resource has been active.
	TargetGroup_ACTIVE TargetGroup_State = 2
	// [zh] 删除中
	// [en] The TargetGroup resource is being deleting.
	TargetGroup_DELETING TargetGroup_State = 3
	// [zh] 已删除
	// [en] The TargetGroup resource has been deleted.
	TargetGroup_DELETED TargetGroup_State = 4
	// [zh] 操作失败
	// [en] The TargetGroup resource has been failed.
	TargetGroup_FAILED TargetGroup_State = 5
)

// Enum value maps for TargetGroup_State.
var (
	TargetGroup_State_name = map[int32]string{
		0: "CREATING",
		1: "UPDATING",
		2: "ACTIVE",
		3: "DELETING",
		4: "DELETED",
		5: "FAILED",
	}
	TargetGroup_State_value = map[string]int32{
		"CREATING": 0,
		"UPDATING": 1,
		"ACTIVE":   2,
		"DELETING": 3,
		"DELETED":  4,
		"FAILED":   5,
	}
)

func (x TargetGroup_State) Enum() *TargetGroup_State {
	p := new(TargetGroup_State)
	*p = x
	return p
}

func (x TargetGroup_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TargetGroup_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[2].Descriptor()
}

func (TargetGroup_State) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[2]
}

func (x TargetGroup_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TargetGroup_State.Descriptor instead.
func (TargetGroup_State) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{33, 0}
}

// [zh] SLB 后端支持的类型
// [en] SLB target type
type TargetGroupProperties_SLBTargetType int32

const (
	// [zh] 无效值
	// [en] Invalid type
	TargetGroupProperties_INVALID TargetGroupProperties_SLBTargetType = 0
	// [zh] CCI部署创建的实例
	// [en] CCI instance of CCI Deployment
	TargetGroupProperties_CCI_DEPLOYMENT_INSTANCE TargetGroupProperties_SLBTargetType = 1
	// [zh] 服务器实例，包含 ECS、BMS 等
	// [en] Server instance
	TargetGroupProperties_SERVER_INSTANCE TargetGroupProperties_SLBTargetType = 2
)

// Enum value maps for TargetGroupProperties_SLBTargetType.
var (
	TargetGroupProperties_SLBTargetType_name = map[int32]string{
		0: "INVALID",
		1: "CCI_DEPLOYMENT_INSTANCE",
		2: "SERVER_INSTANCE",
	}
	TargetGroupProperties_SLBTargetType_value = map[string]int32{
		"INVALID":                 0,
		"CCI_DEPLOYMENT_INSTANCE": 1,
		"SERVER_INSTANCE":         2,
	}
)

func (x TargetGroupProperties_SLBTargetType) Enum() *TargetGroupProperties_SLBTargetType {
	p := new(TargetGroupProperties_SLBTargetType)
	*p = x
	return p
}

func (x TargetGroupProperties_SLBTargetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TargetGroupProperties_SLBTargetType) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[3].Descriptor()
}

func (TargetGroupProperties_SLBTargetType) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[3]
}

func (x TargetGroupProperties_SLBTargetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TargetGroupProperties_SLBTargetType.Descriptor instead.
func (TargetGroupProperties_SLBTargetType) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{34, 0}
}

// [zh] 调度算法取值
// [en] Scheduler enum
type TargetGroupProperties_TargetGroupSchedulerType int32

const (
	// [zh] 轮询, 默认值
	// [en] RR, round-robin, default
	TargetGroupProperties_RR TargetGroupProperties_TargetGroupSchedulerType = 0
	// [zh] 加权轮询
	// [en] WRR, wight round-robin
	TargetGroupProperties_WRR TargetGroupProperties_TargetGroupSchedulerType = 1
)

// Enum value maps for TargetGroupProperties_TargetGroupSchedulerType.
var (
	TargetGroupProperties_TargetGroupSchedulerType_name = map[int32]string{
		0: "RR",
		1: "WRR",
	}
	TargetGroupProperties_TargetGroupSchedulerType_value = map[string]int32{
		"RR":  0,
		"WRR": 1,
	}
)

func (x TargetGroupProperties_TargetGroupSchedulerType) Enum() *TargetGroupProperties_TargetGroupSchedulerType {
	p := new(TargetGroupProperties_TargetGroupSchedulerType)
	*p = x
	return p
}

func (x TargetGroupProperties_TargetGroupSchedulerType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TargetGroupProperties_TargetGroupSchedulerType) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[4].Descriptor()
}

func (TargetGroupProperties_TargetGroupSchedulerType) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[4]
}

func (x TargetGroupProperties_TargetGroupSchedulerType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TargetGroupProperties_TargetGroupSchedulerType.Descriptor instead.
func (TargetGroupProperties_TargetGroupSchedulerType) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{34, 1}
}

// [zh] 后端使用的协议类型枚举
// [en] The protocol used by the Target
type TargetGroupProperties_TargetProtocol int32

const (
	// [zh] 默认值，默认与监听器监听协议保持一致
	// [en] Default value, which is consistent with the listener listening protocol.
	TargetGroupProperties_DEFAULT TargetGroupProperties_TargetProtocol = 0
	// [zh] HTTP, 支持 HTTP、HTTPS QUIC 监听器与之建立链接
	// [en] HTTP, supports HTTP and HTTPS QUIC listeners to establish links with it
	TargetGroupProperties_HTTP TargetGroupProperties_TargetProtocol = 1
	// [zh] HTTPS, 支持 HTTPS 监听器与之建立链接
	// [en] HTTPS, supports HTTPS listeners to establish connections with it
	TargetGroupProperties_HTTPS TargetGroupProperties_TargetProtocol = 2
	// [zh] TCP, 支持 TCP 监听器与之建立链接
	// [en] TCP, supports TCP listener to establish a connection with it
	TargetGroupProperties_TCP TargetGroupProperties_TargetProtocol = 3
	// [zh] UDP, 支持 UDP 监听器与之建立链接
	// [en] UDP, supports UDP listener to establish a connection with it
	TargetGroupProperties_UDP TargetGroupProperties_TargetProtocol = 4
	// [zh] TCPSSL, 支持 TCPSSL 监听器与之建立链接
	// [en] TCPSSL, supports TCPSSL listener to establish a connection with it
	TargetGroupProperties_TCPSSL TargetGroupProperties_TargetProtocol = 5
)

// Enum value maps for TargetGroupProperties_TargetProtocol.
var (
	TargetGroupProperties_TargetProtocol_name = map[int32]string{
		0: "DEFAULT",
		1: "HTTP",
		2: "HTTPS",
		3: "TCP",
		4: "UDP",
		5: "TCPSSL",
	}
	TargetGroupProperties_TargetProtocol_value = map[string]int32{
		"DEFAULT": 0,
		"HTTP":    1,
		"HTTPS":   2,
		"TCP":     3,
		"UDP":     4,
		"TCPSSL":  5,
	}
)

func (x TargetGroupProperties_TargetProtocol) Enum() *TargetGroupProperties_TargetProtocol {
	p := new(TargetGroupProperties_TargetProtocol)
	*p = x
	return p
}

func (x TargetGroupProperties_TargetProtocol) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TargetGroupProperties_TargetProtocol) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[5].Descriptor()
}

func (TargetGroupProperties_TargetProtocol) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[5]
}

func (x TargetGroupProperties_TargetProtocol) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TargetGroupProperties_TargetProtocol.Descriptor instead.
func (TargetGroupProperties_TargetProtocol) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{34, 2}
}

// [zh] 后端资源状态
// [en] Represents the different states of a Target.
type Target_State int32

const (
	// [zh] 增加中
	// [en] The Target resource is being adding.
	Target_ADDING Target_State = 0
	// [zh] 更新中
	// [en] The Target resource is being updated.
	Target_UPDATING Target_State = 1
	// [zh] 已激活
	// [en] The Target resource has been active.
	Target_ACTIVE Target_State = 2
	// [zh] 移除中
	// [en] The Target resource is being removing.
	Target_REMOVING Target_State = 3
	// [zh] 已移除
	// [en] The Target resource has been removed.
	Target_REMOVED Target_State = 4
	// [zh] 操作失败
	// [en] The Target resource has been failed.
	Target_FAILED Target_State = 5
)

// Enum value maps for Target_State.
var (
	Target_State_name = map[int32]string{
		0: "ADDING",
		1: "UPDATING",
		2: "ACTIVE",
		3: "REMOVING",
		4: "REMOVED",
		5: "FAILED",
	}
	Target_State_value = map[string]int32{
		"ADDING":   0,
		"UPDATING": 1,
		"ACTIVE":   2,
		"REMOVING": 3,
		"REMOVED":  4,
		"FAILED":   5,
	}
)

func (x Target_State) Enum() *Target_State {
	p := new(Target_State)
	*p = x
	return p
}

func (x Target_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (Target_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[6].Descriptor()
}

func (Target_State) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[6]
}

func (x Target_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use Target_State.Descriptor instead.
func (Target_State) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{36, 0}
}

// [zh] SLB 后端支持的类型
// [en] SLB target type
type TargetProperties_SLBTargetType int32

const (
	// [zh] 无效值
	// [en] Invalid type
	TargetProperties_INVALID TargetProperties_SLBTargetType = 0
	// [zh] CCI部署创建的实例
	// [en] CCI instance of CCI Deployment
	TargetProperties_CCI_DEPLOYMENT_INSTANCE TargetProperties_SLBTargetType = 1
	// [zh] ECS云服务器实例
	// [en] ECS instance_name
	TargetProperties_ECS_INSTANCE TargetProperties_SLBTargetType = 2
	// [zh] 裸金属实例
	// [en] BMS instance
	TargetProperties_BMS_INSTANCE TargetProperties_SLBTargetType = 3
)

// Enum value maps for TargetProperties_SLBTargetType.
var (
	TargetProperties_SLBTargetType_name = map[int32]string{
		0: "INVALID",
		1: "CCI_DEPLOYMENT_INSTANCE",
		2: "ECS_INSTANCE",
		3: "BMS_INSTANCE",
	}
	TargetProperties_SLBTargetType_value = map[string]int32{
		"INVALID":                 0,
		"CCI_DEPLOYMENT_INSTANCE": 1,
		"ECS_INSTANCE":            2,
		"BMS_INSTANCE":            3,
	}
)

func (x TargetProperties_SLBTargetType) Enum() *TargetProperties_SLBTargetType {
	p := new(TargetProperties_SLBTargetType)
	*p = x
	return p
}

func (x TargetProperties_SLBTargetType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TargetProperties_SLBTargetType) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[7].Descriptor()
}

func (TargetProperties_SLBTargetType) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[7]
}

func (x TargetProperties_SLBTargetType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TargetProperties_SLBTargetType.Descriptor instead.
func (TargetProperties_SLBTargetType) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{37, 0}
}

// [zh] 健康检查类型枚举
// [en] Health check type enum
type HealthCheck_HealthCheckType int32

const (
	// [zh] 无效值
	// [en] Invalid value
	HealthCheck_INVALID HealthCheck_HealthCheckType = 0
	// [zh] TCP
	// [en] TCP
	HealthCheck_TCP HealthCheck_HealthCheckType = 1
	// [zh] HTTP
	// [en] HTTP
	HealthCheck_HTTP HealthCheck_HealthCheckType = 2
)

// Enum value maps for HealthCheck_HealthCheckType.
var (
	HealthCheck_HealthCheckType_name = map[int32]string{
		0: "INVALID",
		1: "TCP",
		2: "HTTP",
	}
	HealthCheck_HealthCheckType_value = map[string]int32{
		"INVALID": 0,
		"TCP":     1,
		"HTTP":    2,
	}
)

func (x HealthCheck_HealthCheckType) Enum() *HealthCheck_HealthCheckType {
	p := new(HealthCheck_HealthCheckType)
	*p = x
	return p
}

func (x HealthCheck_HealthCheckType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HealthCheck_HealthCheckType) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[8].Descriptor()
}

func (HealthCheck_HealthCheckType) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[8]
}

func (x HealthCheck_HealthCheckType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HealthCheck_HealthCheckType.Descriptor instead.
func (HealthCheck_HealthCheckType) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{38, 0}
}

// [zh] 后端健康检查状态类型
// [en] health check status enum
type ListenerHealthCheckStatus_HealthCheckState int32

const (
	// [zh] 初始化
	// [en] The Target health check status is initial
	ListenerHealthCheckStatus_INITIAL ListenerHealthCheckStatus_HealthCheckState = 0
	// [zh] 健康
	// [en] The Target health check status is health
	ListenerHealthCheckStatus_HEALTH ListenerHealthCheckStatus_HealthCheckState = 1
	// [zh] 部分健康
	// [en] The Target health check status is health partly
	ListenerHealthCheckStatus_PARTLY_HEALTH ListenerHealthCheckStatus_HealthCheckState = 2
	// [zh] 异常
	// [en] The Target health check status is unhealth
	ListenerHealthCheckStatus_UNHEALTH ListenerHealthCheckStatus_HealthCheckState = 3
	// [zh] 未开启
	// [en] The Target health check status is unhealth
	ListenerHealthCheckStatus_DISABLE ListenerHealthCheckStatus_HealthCheckState = 4
)

// Enum value maps for ListenerHealthCheckStatus_HealthCheckState.
var (
	ListenerHealthCheckStatus_HealthCheckState_name = map[int32]string{
		0: "INITIAL",
		1: "HEALTH",
		2: "PARTLY_HEALTH",
		3: "UNHEALTH",
		4: "DISABLE",
	}
	ListenerHealthCheckStatus_HealthCheckState_value = map[string]int32{
		"INITIAL":       0,
		"HEALTH":        1,
		"PARTLY_HEALTH": 2,
		"UNHEALTH":      3,
		"DISABLE":       4,
	}
)

func (x ListenerHealthCheckStatus_HealthCheckState) Enum() *ListenerHealthCheckStatus_HealthCheckState {
	p := new(ListenerHealthCheckStatus_HealthCheckState)
	*p = x
	return p
}

func (x ListenerHealthCheckStatus_HealthCheckState) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ListenerHealthCheckStatus_HealthCheckState) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[9].Descriptor()
}

func (ListenerHealthCheckStatus_HealthCheckState) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[9]
}

func (x ListenerHealthCheckStatus_HealthCheckState) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ListenerHealthCheckStatus_HealthCheckState.Descriptor instead.
func (ListenerHealthCheckStatus_HealthCheckState) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{39, 0}
}

// [zh] 后端健康检查状态类型
// [en] health check status enum
type TargetHealthCheckStatus_State int32

const (
	// [zh] 初始化
	// [en] The Target health check status is initial
	TargetHealthCheckStatus_INITIAL TargetHealthCheckStatus_State = 0
	// [zh] 健康
	// [en] The Target health check status is health
	TargetHealthCheckStatus_HEALTH TargetHealthCheckStatus_State = 1
	// [zh] 不健康
	// [en] The Target health check status is unhealth
	TargetHealthCheckStatus_UNHEALTH TargetHealthCheckStatus_State = 2
	// [zh] 未开启
	// [en] The Target health check status is disable
	TargetHealthCheckStatus_DISABLE TargetHealthCheckStatus_State = 3
)

// Enum value maps for TargetHealthCheckStatus_State.
var (
	TargetHealthCheckStatus_State_name = map[int32]string{
		0: "INITIAL",
		1: "HEALTH",
		2: "UNHEALTH",
		3: "DISABLE",
	}
	TargetHealthCheckStatus_State_value = map[string]int32{
		"INITIAL":  0,
		"HEALTH":   1,
		"UNHEALTH": 2,
		"DISABLE":  3,
	}
)

func (x TargetHealthCheckStatus_State) Enum() *TargetHealthCheckStatus_State {
	p := new(TargetHealthCheckStatus_State)
	*p = x
	return p
}

func (x TargetHealthCheckStatus_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (TargetHealthCheckStatus_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[10].Descriptor()
}

func (TargetHealthCheckStatus_State) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[10]
}

func (x TargetHealthCheckStatus_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use TargetHealthCheckStatus_State.Descriptor instead.
func (TargetHealthCheckStatus_State) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{42, 0}
}

// [zh] 转发动作类型枚举
// [en] forward action type
type ForwardAction_ForwardActionType int32

const (
	// [zh] 无效值
	// [en] Invalid value
	ForwardAction_INVALID ForwardAction_ForwardActionType = 0
	// [zh] 转发到后端组
	// [en] Forward TargetGroup
	ForwardAction_FORWARD_TARGET_GROUP ForwardAction_ForwardActionType = 1
)

// Enum value maps for ForwardAction_ForwardActionType.
var (
	ForwardAction_ForwardActionType_name = map[int32]string{
		0: "INVALID",
		1: "FORWARD_TARGET_GROUP",
	}
	ForwardAction_ForwardActionType_value = map[string]int32{
		"INVALID":              0,
		"FORWARD_TARGET_GROUP": 1,
	}
)

func (x ForwardAction_ForwardActionType) Enum() *ForwardAction_ForwardActionType {
	p := new(ForwardAction_ForwardActionType)
	*p = x
	return p
}

func (x ForwardAction_ForwardActionType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (ForwardAction_ForwardActionType) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[11].Descriptor()
}

func (ForwardAction_ForwardActionType) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[11]
}

func (x ForwardAction_ForwardActionType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use ForwardAction_ForwardActionType.Descriptor instead.
func (ForwardAction_ForwardActionType) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{43, 0}
}

// [zh] HTTP 检查的host的类型
// [en] HTTP health check host type
type HTTPHealthCheckConfig_HttpHostType int32

const (
	// [zh] ip
	// [en] ip
	HTTPHealthCheckConfig_IP HTTPHealthCheckConfig_HttpHostType = 0
	// [zh] name
	// [en] name
	HTTPHealthCheckConfig_NAME HTTPHealthCheckConfig_HttpHostType = 1
)

// Enum value maps for HTTPHealthCheckConfig_HttpHostType.
var (
	HTTPHealthCheckConfig_HttpHostType_name = map[int32]string{
		0: "IP",
		1: "NAME",
	}
	HTTPHealthCheckConfig_HttpHostType_value = map[string]int32{
		"IP":   0,
		"NAME": 1,
	}
)

func (x HTTPHealthCheckConfig_HttpHostType) Enum() *HTTPHealthCheckConfig_HttpHostType {
	p := new(HTTPHealthCheckConfig_HttpHostType)
	*p = x
	return p
}

func (x HTTPHealthCheckConfig_HttpHostType) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HTTPHealthCheckConfig_HttpHostType) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[12].Descriptor()
}

func (HTTPHealthCheckConfig_HttpHostType) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[12]
}

func (x HTTPHealthCheckConfig_HttpHostType) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HTTPHealthCheckConfig_HttpHostType.Descriptor instead.
func (HTTPHealthCheckConfig_HttpHostType) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{47, 0}
}

// [zh] http 方式
// [en] http method
type HTTPHealthCheckConfig_HTTPMethod int32

const (
	// [zh] HEAD
	// [en] HEAD
	HTTPHealthCheckConfig_HEAD HTTPHealthCheckConfig_HTTPMethod = 0
	// [zh] GET
	// [en] GET
	HTTPHealthCheckConfig_GET HTTPHealthCheckConfig_HTTPMethod = 1
)

// Enum value maps for HTTPHealthCheckConfig_HTTPMethod.
var (
	HTTPHealthCheckConfig_HTTPMethod_name = map[int32]string{
		0: "HEAD",
		1: "GET",
	}
	HTTPHealthCheckConfig_HTTPMethod_value = map[string]int32{
		"HEAD": 0,
		"GET":  1,
	}
)

func (x HTTPHealthCheckConfig_HTTPMethod) Enum() *HTTPHealthCheckConfig_HTTPMethod {
	p := new(HTTPHealthCheckConfig_HTTPMethod)
	*p = x
	return p
}

func (x HTTPHealthCheckConfig_HTTPMethod) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HTTPHealthCheckConfig_HTTPMethod) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[13].Descriptor()
}

func (HTTPHealthCheckConfig_HTTPMethod) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[13]
}

func (x HTTPHealthCheckConfig_HTTPMethod) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HTTPHealthCheckConfig_HTTPMethod.Descriptor instead.
func (HTTPHealthCheckConfig_HTTPMethod) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{47, 1}
}

// [zh] HTTP 版本
// [en] HTTP version
type HTTPHealthCheckConfig_HTTPVersion int32

const (
	// [zh] HTTP1
	// [en] HTTP1
	HTTPHealthCheckConfig_HTTP1 HTTPHealthCheckConfig_HTTPVersion = 0
	// [zh] HTTP2
	// [en] HTTP2
	HTTPHealthCheckConfig_HTTP2 HTTPHealthCheckConfig_HTTPVersion = 1
)

// Enum value maps for HTTPHealthCheckConfig_HTTPVersion.
var (
	HTTPHealthCheckConfig_HTTPVersion_name = map[int32]string{
		0: "HTTP1",
		1: "HTTP2",
	}
	HTTPHealthCheckConfig_HTTPVersion_value = map[string]int32{
		"HTTP1": 0,
		"HTTP2": 1,
	}
)

func (x HTTPHealthCheckConfig_HTTPVersion) Enum() *HTTPHealthCheckConfig_HTTPVersion {
	p := new(HTTPHealthCheckConfig_HTTPVersion)
	*p = x
	return p
}

func (x HTTPHealthCheckConfig_HTTPVersion) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HTTPHealthCheckConfig_HTTPVersion) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[14].Descriptor()
}

func (HTTPHealthCheckConfig_HTTPVersion) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[14]
}

func (x HTTPHealthCheckConfig_HTTPVersion) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HTTPHealthCheckConfig_HTTPVersion.Descriptor instead.
func (HTTPHealthCheckConfig_HTTPVersion) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{47, 2}
}

type HTTPHealthCheckConfig_HTTPReturnCode int32

const (
	// [zh] 1XX
	HTTPHealthCheckConfig_code_1XX HTTPHealthCheckConfig_HTTPReturnCode = 0
	// [zh] 2XX
	HTTPHealthCheckConfig_code_2XX HTTPHealthCheckConfig_HTTPReturnCode = 1
	// [zh] 3XX
	HTTPHealthCheckConfig_code_3XX HTTPHealthCheckConfig_HTTPReturnCode = 2
	// [zh] 4XX
	HTTPHealthCheckConfig_code_4XX HTTPHealthCheckConfig_HTTPReturnCode = 3
	// [zh] 5XX
	HTTPHealthCheckConfig_code_5XX HTTPHealthCheckConfig_HTTPReturnCode = 4
)

// Enum value maps for HTTPHealthCheckConfig_HTTPReturnCode.
var (
	HTTPHealthCheckConfig_HTTPReturnCode_name = map[int32]string{
		0: "code_1XX",
		1: "code_2XX",
		2: "code_3XX",
		3: "code_4XX",
		4: "code_5XX",
	}
	HTTPHealthCheckConfig_HTTPReturnCode_value = map[string]int32{
		"code_1XX": 0,
		"code_2XX": 1,
		"code_3XX": 2,
		"code_4XX": 3,
		"code_5XX": 4,
	}
)

func (x HTTPHealthCheckConfig_HTTPReturnCode) Enum() *HTTPHealthCheckConfig_HTTPReturnCode {
	p := new(HTTPHealthCheckConfig_HTTPReturnCode)
	*p = x
	return p
}

func (x HTTPHealthCheckConfig_HTTPReturnCode) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (HTTPHealthCheckConfig_HTTPReturnCode) Descriptor() protoreflect.EnumDescriptor {
	return file_network_slb_v1_slb_data_proto_enumTypes[15].Descriptor()
}

func (HTTPHealthCheckConfig_HTTPReturnCode) Type() protoreflect.EnumType {
	return &file_network_slb_v1_slb_data_proto_enumTypes[15]
}

func (x HTTPHealthCheckConfig_HTTPReturnCode) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use HTTPHealthCheckConfig_HTTPReturnCode.Descriptor instead.
func (HTTPHealthCheckConfig_HTTPReturnCode) EnumDescriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{47, 3}
}

// [zh] 获取某一个 SLB 状态的请求
// [en] Request to get a SLB status
type GetSLBStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
}

func (x *GetSLBStatusRequest) Reset() {
	*x = GetSLBStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetSLBStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetSLBStatusRequest) ProtoMessage() {}

func (x *GetSLBStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetSLBStatusRequest.ProtoReflect.Descriptor instead.
func (*GetSLBStatusRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{0}
}

func (x *GetSLBStatusRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetSLBStatusRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetSLBStatusRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetSLBStatusRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

// [zh] 列举符合条件的负载均衡状态列表
// [en] List  details of SLBs resources status.
type ListSLBsStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] slb过滤条件【暂不支持】
	// [en] List filter.
	Filter string `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// [zh] slb资源排序规则【暂不支持】
	// [en] Sort resoults.
	OrderBy string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// [zh] 分页大小【暂不支持】
	// [en] The maximum number of items to return.
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// [zh] 从上一个List请求返回的next_page_token值(如果有的话)【暂不支持】
	// [en] The next_page_token value returned from a previous List request, if any.
	PageToken string `protobuf:"bytes,7,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// [zh] slb的name列表
	// [en] The slb name list
	SlbNames []string `protobuf:"bytes,8,rep,name=slb_names,json=slbNames,proto3" json:"slb_names,omitempty"`
}

func (x *ListSLBsStatusRequest) Reset() {
	*x = ListSLBsStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSLBsStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSLBsStatusRequest) ProtoMessage() {}

func (x *ListSLBsStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSLBsStatusRequest.ProtoReflect.Descriptor instead.
func (*ListSLBsStatusRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{1}
}

func (x *ListSLBsStatusRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListSLBsStatusRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListSLBsStatusRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListSLBsStatusRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListSLBsStatusRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListSLBsStatusRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListSLBsStatusRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListSLBsStatusRequest) GetSlbNames() []string {
	if x != nil {
		return x.SlbNames
	}
	return nil
}

// [zh] 列举符合条件的负载均衡状态列表
// [en] List  details of SLBs resources status.
type ListSLBsStatusResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] SLB状态列表
	// [en] SLB status list
	Slbs []*SLBStatus `protobuf:"bytes,1,rep,name=slbs,proto3" json:"slbs,omitempty"`
	// [zh] 下一个页面的 token，如果没有更多数据则为空.
	// [en] Token to retrieve the next page of results, or empty if there are no more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// [zh] 返回的slb实例总数
	// [en] total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListSLBsStatusResponse) Reset() {
	*x = ListSLBsStatusResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListSLBsStatusResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListSLBsStatusResponse) ProtoMessage() {}

func (x *ListSLBsStatusResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListSLBsStatusResponse.ProtoReflect.Descriptor instead.
func (*ListSLBsStatusResponse) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{2}
}

func (x *ListSLBsStatusResponse) GetSlbs() []*SLBStatus {
	if x != nil {
		return x.Slbs
	}
	return nil
}

func (x *ListSLBsStatusResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListSLBsStatusResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// [zh] Listener 创建字段
// [en] Listener creation properties
type CreateListenerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 监听器名称
	// [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	ListenerName string `protobuf:"bytes,5,opt,name=listener_name,json=listenerName,proto3" json:"listener_name,omitempty"`
	// [zh] 监听器资源
	// [en] The Listener resource to create
	Listener *Listener `protobuf:"bytes,6,opt,name=listener,proto3" json:"listener,omitempty"`
}

func (x *CreateListenerRequest) Reset() {
	*x = CreateListenerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateListenerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateListenerRequest) ProtoMessage() {}

func (x *CreateListenerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateListenerRequest.ProtoReflect.Descriptor instead.
func (*CreateListenerRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{3}
}

func (x *CreateListenerRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *CreateListenerRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *CreateListenerRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateListenerRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *CreateListenerRequest) GetListenerName() string {
	if x != nil {
		return x.ListenerName
	}
	return ""
}

func (x *CreateListenerRequest) GetListener() *Listener {
	if x != nil {
		return x.Listener
	}
	return nil
}

// [zh] Listener 可编辑字段
// [en] Listener editable properties
type UpdateListenerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 监听器名称
	// [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	ListenerName string `protobuf:"bytes,5,opt,name=listener_name,json=listenerName,proto3" json:"listener_name,omitempty"`
	// [zh] 监听器资源
	// [en] The Listener resource to update
	Listener *ListenerUpdate `protobuf:"bytes,6,opt,name=listener,proto3" json:"listener,omitempty"`
	// [zh] 更新标记.
	// [en] The resource update mask.
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,7,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateListenerRequest) Reset() {
	*x = UpdateListenerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateListenerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateListenerRequest) ProtoMessage() {}

func (x *UpdateListenerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateListenerRequest.ProtoReflect.Descriptor instead.
func (*UpdateListenerRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateListenerRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *UpdateListenerRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *UpdateListenerRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateListenerRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *UpdateListenerRequest) GetListenerName() string {
	if x != nil {
		return x.ListenerName
	}
	return ""
}

func (x *UpdateListenerRequest) GetListener() *ListenerUpdate {
	if x != nil {
		return x.Listener
	}
	return nil
}

func (x *UpdateListenerRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// [zh] 删除某一个 Listener 的请求
// [en] Request to delete a Listener
type DeleteListenerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 监听器名称
	// [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	ListenerName string `protobuf:"bytes,5,opt,name=listener_name,json=listenerName,proto3" json:"listener_name,omitempty"`
}

func (x *DeleteListenerRequest) Reset() {
	*x = DeleteListenerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteListenerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteListenerRequest) ProtoMessage() {}

func (x *DeleteListenerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteListenerRequest.ProtoReflect.Descriptor instead.
func (*DeleteListenerRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{5}
}

func (x *DeleteListenerRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DeleteListenerRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *DeleteListenerRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteListenerRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *DeleteListenerRequest) GetListenerName() string {
	if x != nil {
		return x.ListenerName
	}
	return ""
}

// [zh] 列举 Listeners 的请求
// [en] Request to list Listener
type ListListenersRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 过滤字段
	// [en] List filter.
	Filter string `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// [zh] 排序字段
	// [en] Sort resoults.
	OrderBy string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// [zh] 分页大小
	// [en] The maximum number of items to return.
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// [zh] 分页token
	// [en] The next_page_token value returned from a previous List request, if any.
	PageToken string `protobuf:"bytes,7,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// [zh] 租户ID
	// [en] tenant_id.
	TenantId string `protobuf:"bytes,8,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// [zh] 负载均衡名称
	// [en] SLB name
	SlbName string `protobuf:"bytes,9,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
}

func (x *ListListenersRequest) Reset() {
	*x = ListListenersRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListListenersRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListListenersRequest) ProtoMessage() {}

func (x *ListListenersRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListListenersRequest.ProtoReflect.Descriptor instead.
func (*ListListenersRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{6}
}

func (x *ListListenersRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListListenersRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListListenersRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListListenersRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListListenersRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListListenersRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListListenersRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListListenersRequest) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *ListListenersRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

// [zh] 列举 Listeners 的响应
// [en] Response to list Listeners
type ListListenersResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] Listener 列表
	// [en] Listener list
	ListenerStatusInfos []*ListenerStatusInfo `protobuf:"bytes,1,rep,name=listener_status_infos,json=listenerStatusInfos,proto3" json:"listener_status_infos,omitempty"`
	// [zh] 下一个页面的 token，如果没有更多数据则为空.
	// [en] Token to retrieve the next page of results, or empty if there are no more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// [zh] 总数量
	// [en] total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListListenersResponse) Reset() {
	*x = ListListenersResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListListenersResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListListenersResponse) ProtoMessage() {}

func (x *ListListenersResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListListenersResponse.ProtoReflect.Descriptor instead.
func (*ListListenersResponse) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{7}
}

func (x *ListListenersResponse) GetListenerStatusInfos() []*ListenerStatusInfo {
	if x != nil {
		return x.ListenerStatusInfos
	}
	return nil
}

func (x *ListListenersResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListListenersResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// [zh] 获取某一个 Listener 的请求
// [en] Request to get a Listener
type GetListenerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 监听器名称
	// [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	ListenerName string `protobuf:"bytes,5,opt,name=listener_name,json=listenerName,proto3" json:"listener_name,omitempty"`
}

func (x *GetListenerRequest) Reset() {
	*x = GetListenerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetListenerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetListenerRequest) ProtoMessage() {}

func (x *GetListenerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetListenerRequest.ProtoReflect.Descriptor instead.
func (*GetListenerRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{8}
}

func (x *GetListenerRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetListenerRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetListenerRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetListenerRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *GetListenerRequest) GetListenerName() string {
	if x != nil {
		return x.ListenerName
	}
	return ""
}

// [zh] 获取某一个 Listener 状态的请求
// [en] Request to get a Listener status
type GetListenerStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 监听器名称
	// [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	ListenerName string `protobuf:"bytes,5,opt,name=listener_name,json=listenerName,proto3" json:"listener_name,omitempty"`
}

func (x *GetListenerStatusRequest) Reset() {
	*x = GetListenerStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetListenerStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetListenerStatusRequest) ProtoMessage() {}

func (x *GetListenerStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetListenerStatusRequest.ProtoReflect.Descriptor instead.
func (*GetListenerStatusRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{9}
}

func (x *GetListenerStatusRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetListenerStatusRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetListenerStatusRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetListenerStatusRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *GetListenerStatusRequest) GetListenerName() string {
	if x != nil {
		return x.ListenerName
	}
	return ""
}

// [zh] 启动一个监听器的请求
// [en] Request to start a Listener
type StartListenerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 监听器名称
	// [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	ListenerName string `protobuf:"bytes,5,opt,name=listener_name,json=listenerName,proto3" json:"listener_name,omitempty"`
}

func (x *StartListenerRequest) Reset() {
	*x = StartListenerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StartListenerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StartListenerRequest) ProtoMessage() {}

func (x *StartListenerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StartListenerRequest.ProtoReflect.Descriptor instead.
func (*StartListenerRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{10}
}

func (x *StartListenerRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *StartListenerRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *StartListenerRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *StartListenerRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *StartListenerRequest) GetListenerName() string {
	if x != nil {
		return x.ListenerName
	}
	return ""
}

// [zh] 停止一个监听器的请求
// [en] Request to stop a Listener
type StopListenerRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 监听器名称
	// [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	ListenerName string `protobuf:"bytes,5,opt,name=listener_name,json=listenerName,proto3" json:"listener_name,omitempty"`
}

func (x *StopListenerRequest) Reset() {
	*x = StopListenerRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *StopListenerRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*StopListenerRequest) ProtoMessage() {}

func (x *StopListenerRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use StopListenerRequest.ProtoReflect.Descriptor instead.
func (*StopListenerRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{11}
}

func (x *StopListenerRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *StopListenerRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *StopListenerRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *StopListenerRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *StopListenerRequest) GetListenerName() string {
	if x != nil {
		return x.ListenerName
	}
	return ""
}

// [zh] 后端组创建字段
// [en] TargetGroup creation properties
type CreateTargetGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 后端组名称
	// [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	TargetGroupName string `protobuf:"bytes,5,opt,name=target_group_name,json=targetGroupName,proto3" json:"target_group_name,omitempty"`
	// [zh] 创建的后端组
	// [en] The Target resource to create
	TargetGroup *TargetGroup `protobuf:"bytes,6,opt,name=target_group,json=targetGroup,proto3" json:"target_group,omitempty"`
}

func (x *CreateTargetGroupRequest) Reset() {
	*x = CreateTargetGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateTargetGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateTargetGroupRequest) ProtoMessage() {}

func (x *CreateTargetGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateTargetGroupRequest.ProtoReflect.Descriptor instead.
func (*CreateTargetGroupRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{12}
}

func (x *CreateTargetGroupRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *CreateTargetGroupRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *CreateTargetGroupRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateTargetGroupRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *CreateTargetGroupRequest) GetTargetGroupName() string {
	if x != nil {
		return x.TargetGroupName
	}
	return ""
}

func (x *CreateTargetGroupRequest) GetTargetGroup() *TargetGroup {
	if x != nil {
		return x.TargetGroup
	}
	return nil
}

// [zh] 更新后端组的可编辑字段
// [en] Update TargetGroup editable properties.
type UpdateTargetGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 后端组名称
	// [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	TargetGroupName string `protobuf:"bytes,5,opt,name=target_group_name,json=targetGroupName,proto3" json:"target_group_name,omitempty"`
	// [zh] 更新的后端组
	// [en] The Target resource to create
	TargetGroup *TargetGroupUpdate `protobuf:"bytes,6,opt,name=target_group,json=targetGroup,proto3" json:"target_group,omitempty"`
	// [zh] 更新标记.
	// [en] The resource update mask.
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,7,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateTargetGroupRequest) Reset() {
	*x = UpdateTargetGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateTargetGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateTargetGroupRequest) ProtoMessage() {}

func (x *UpdateTargetGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateTargetGroupRequest.ProtoReflect.Descriptor instead.
func (*UpdateTargetGroupRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{13}
}

func (x *UpdateTargetGroupRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *UpdateTargetGroupRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *UpdateTargetGroupRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateTargetGroupRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *UpdateTargetGroupRequest) GetTargetGroupName() string {
	if x != nil {
		return x.TargetGroupName
	}
	return ""
}

func (x *UpdateTargetGroupRequest) GetTargetGroup() *TargetGroupUpdate {
	if x != nil {
		return x.TargetGroup
	}
	return nil
}

func (x *UpdateTargetGroupRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// [zh] 删除一个后端组
// [en] Delete one TargetGroup
type DeleteTargetGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 后端组名称
	// [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	TargetGroupName string `protobuf:"bytes,5,opt,name=target_group_name,json=targetGroupName,proto3" json:"target_group_name,omitempty"`
}

func (x *DeleteTargetGroupRequest) Reset() {
	*x = DeleteTargetGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteTargetGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteTargetGroupRequest) ProtoMessage() {}

func (x *DeleteTargetGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteTargetGroupRequest.ProtoReflect.Descriptor instead.
func (*DeleteTargetGroupRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{14}
}

func (x *DeleteTargetGroupRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DeleteTargetGroupRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *DeleteTargetGroupRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteTargetGroupRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *DeleteTargetGroupRequest) GetTargetGroupName() string {
	if x != nil {
		return x.TargetGroupName
	}
	return ""
}

// [zh] 列举符合请求的后端组
// [en] List requested TargetGroup
type ListTargetGroupsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 过滤字段
	// [en] List filter.
	Filter string `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// [zh] 排序字段
	// [en] Sort resoults.
	OrderBy string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// [zh] 分页大小
	// [en] The maximum number of items to return.
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// [zh] 分页token
	// [en] The next_page_token value returned from a previous List request, if any.
	PageToken string `protobuf:"bytes,7,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// [zh] 租户ID
	// [en] tenant_id.
	TenantId string `protobuf:"bytes,8,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// [zh] 负载均衡名称
	// [en] SLB name
	SlbName string `protobuf:"bytes,9,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
}

func (x *ListTargetGroupsRequest) Reset() {
	*x = ListTargetGroupsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[15]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTargetGroupsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTargetGroupsRequest) ProtoMessage() {}

func (x *ListTargetGroupsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[15]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTargetGroupsRequest.ProtoReflect.Descriptor instead.
func (*ListTargetGroupsRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{15}
}

func (x *ListTargetGroupsRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListTargetGroupsRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListTargetGroupsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListTargetGroupsRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListTargetGroupsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListTargetGroupsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListTargetGroupsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListTargetGroupsRequest) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *ListTargetGroupsRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

// [zh] 列举符合请求的后端组
// [en] Response to list TargetGroup
type ListTargetGroupsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] TargetGroup 列表
	// [en] TargetGroup list
	TargetGroups []*TargetGroupStatus `protobuf:"bytes,1,rep,name=target_groups,json=targetGroups,proto3" json:"target_groups,omitempty"`
	// [zh] 下一个页面的 token，如果没有更多数据则为空.
	// [en] Token to retrieve the next page of results, or empty if there are no more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// [zh] 总数量
	// [en] total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListTargetGroupsResponse) Reset() {
	*x = ListTargetGroupsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[16]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTargetGroupsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTargetGroupsResponse) ProtoMessage() {}

func (x *ListTargetGroupsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[16]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTargetGroupsResponse.ProtoReflect.Descriptor instead.
func (*ListTargetGroupsResponse) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{16}
}

func (x *ListTargetGroupsResponse) GetTargetGroups() []*TargetGroupStatus {
	if x != nil {
		return x.TargetGroups
	}
	return nil
}

func (x *ListTargetGroupsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListTargetGroupsResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// [zh] 获取符合请求的后端组
// [en] Get requested TargetGroup
type GetTargetGroupRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 后端组名称
	// [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	TargetGroupName string `protobuf:"bytes,5,opt,name=target_group_name,json=targetGroupName,proto3" json:"target_group_name,omitempty"`
}

func (x *GetTargetGroupRequest) Reset() {
	*x = GetTargetGroupRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[17]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTargetGroupRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTargetGroupRequest) ProtoMessage() {}

func (x *GetTargetGroupRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[17]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTargetGroupRequest.ProtoReflect.Descriptor instead.
func (*GetTargetGroupRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{17}
}

func (x *GetTargetGroupRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetTargetGroupRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetTargetGroupRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetTargetGroupRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *GetTargetGroupRequest) GetTargetGroupName() string {
	if x != nil {
		return x.TargetGroupName
	}
	return ""
}

// [zh] 获取符合请求的后端组
// [en] Get requested TargetGroup
type GetTargetGroupStatusRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 后端组名称
	// [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	TargetGroupName string `protobuf:"bytes,5,opt,name=target_group_name,json=targetGroupName,proto3" json:"target_group_name,omitempty"`
}

func (x *GetTargetGroupStatusRequest) Reset() {
	*x = GetTargetGroupStatusRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[18]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTargetGroupStatusRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTargetGroupStatusRequest) ProtoMessage() {}

func (x *GetTargetGroupStatusRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[18]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTargetGroupStatusRequest.ProtoReflect.Descriptor instead.
func (*GetTargetGroupStatusRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{18}
}

func (x *GetTargetGroupStatusRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetTargetGroupStatusRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetTargetGroupStatusRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetTargetGroupStatusRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *GetTargetGroupStatusRequest) GetTargetGroupName() string {
	if x != nil {
		return x.TargetGroupName
	}
	return ""
}

// [zh] 添加一个或多个后端到某个后端组.
// [en] Add one or more backend targets to a TargetGroup.
type TargetGroupAddTargetsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 后端组名称
	// [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	TargetGroupName string `protobuf:"bytes,5,opt,name=target_group_name,json=targetGroupName,proto3" json:"target_group_name,omitempty"`
	// [zh] 后端
	// [en] The TargetGroup resource to create
	Targets []*Target `protobuf:"bytes,6,rep,name=targets,proto3" json:"targets,omitempty"`
}

func (x *TargetGroupAddTargetsRequest) Reset() {
	*x = TargetGroupAddTargetsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[19]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetGroupAddTargetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetGroupAddTargetsRequest) ProtoMessage() {}

func (x *TargetGroupAddTargetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[19]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetGroupAddTargetsRequest.ProtoReflect.Descriptor instead.
func (*TargetGroupAddTargetsRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{19}
}

func (x *TargetGroupAddTargetsRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *TargetGroupAddTargetsRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *TargetGroupAddTargetsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *TargetGroupAddTargetsRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *TargetGroupAddTargetsRequest) GetTargetGroupName() string {
	if x != nil {
		return x.TargetGroupName
	}
	return ""
}

func (x *TargetGroupAddTargetsRequest) GetTargets() []*Target {
	if x != nil {
		return x.Targets
	}
	return nil
}

// [zh] 添加一个或多个后端到某个后端组
// [en] Response to add targets
type TargetGroupAddTargetsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 当前后端组包含的后端列表
	// [en] List of backends contained in the current backend group
	CurrTargets []*Target `protobuf:"bytes,1,rep,name=curr_targets,json=currTargets,proto3" json:"curr_targets,omitempty"`
}

func (x *TargetGroupAddTargetsResponse) Reset() {
	*x = TargetGroupAddTargetsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[20]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetGroupAddTargetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetGroupAddTargetsResponse) ProtoMessage() {}

func (x *TargetGroupAddTargetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[20]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetGroupAddTargetsResponse.ProtoReflect.Descriptor instead.
func (*TargetGroupAddTargetsResponse) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{20}
}

func (x *TargetGroupAddTargetsResponse) GetCurrTargets() []*Target {
	if x != nil {
		return x.CurrTargets
	}
	return nil
}

// [zh] 从某个后端组删除一个或多个后端.
// [en] Remove one or more backend targets from a TargetGroup.
type TargetGroupRemoveTargetsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 后端组名称
	// [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	TargetGroupName string `protobuf:"bytes,5,opt,name=target_group_name,json=targetGroupName,proto3" json:"target_group_name,omitempty"`
	// [zh] 后端ID列表
	// [en] The Target uids
	TargetIds []string `protobuf:"bytes,6,rep,name=target_ids,json=targetIds,proto3" json:"target_ids,omitempty"`
}

func (x *TargetGroupRemoveTargetsRequest) Reset() {
	*x = TargetGroupRemoveTargetsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[21]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetGroupRemoveTargetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetGroupRemoveTargetsRequest) ProtoMessage() {}

func (x *TargetGroupRemoveTargetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[21]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetGroupRemoveTargetsRequest.ProtoReflect.Descriptor instead.
func (*TargetGroupRemoveTargetsRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{21}
}

func (x *TargetGroupRemoveTargetsRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *TargetGroupRemoveTargetsRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *TargetGroupRemoveTargetsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *TargetGroupRemoveTargetsRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *TargetGroupRemoveTargetsRequest) GetTargetGroupName() string {
	if x != nil {
		return x.TargetGroupName
	}
	return ""
}

func (x *TargetGroupRemoveTargetsRequest) GetTargetIds() []string {
	if x != nil {
		return x.TargetIds
	}
	return nil
}

// [zh] 从某个后端组删除一个或多个后端.
// [en] Response to remove targets
type TargetGroupRemoveTargetsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 后端组包含的后端列表
	// [en] List of backends contained in the current backend group
	CurrTargets []*Target `protobuf:"bytes,1,rep,name=curr_targets,json=currTargets,proto3" json:"curr_targets,omitempty"`
}

func (x *TargetGroupRemoveTargetsResponse) Reset() {
	*x = TargetGroupRemoveTargetsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[22]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetGroupRemoveTargetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetGroupRemoveTargetsResponse) ProtoMessage() {}

func (x *TargetGroupRemoveTargetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[22]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetGroupRemoveTargetsResponse.ProtoReflect.Descriptor instead.
func (*TargetGroupRemoveTargetsResponse) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{22}
}

func (x *TargetGroupRemoveTargetsResponse) GetCurrTargets() []*Target {
	if x != nil {
		return x.CurrTargets
	}
	return nil
}

// [zh] 修改某个后端组的一台或者多台后端的属性.
// [en] Update the editable properties of one or more backends of a TargetGroup.
type TargetsUpdateRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 后端组名称
	// [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	TargetGroupName string `protobuf:"bytes,5,opt,name=target_group_name,json=targetGroupName,proto3" json:"target_group_name,omitempty"`
	// [en] The TargetGroup resource to update
	TargetGroupTargets []*TargetUpdate `protobuf:"bytes,6,rep,name=target_group_targets,json=targetGroupTargets,proto3" json:"target_group_targets,omitempty"`
	// [zh] 更新标记.
	// [en] update_mask
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,7,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *TargetsUpdateRequest) Reset() {
	*x = TargetsUpdateRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[23]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetsUpdateRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetsUpdateRequest) ProtoMessage() {}

func (x *TargetsUpdateRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[23]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetsUpdateRequest.ProtoReflect.Descriptor instead.
func (*TargetsUpdateRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{23}
}

func (x *TargetsUpdateRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *TargetsUpdateRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *TargetsUpdateRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *TargetsUpdateRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *TargetsUpdateRequest) GetTargetGroupName() string {
	if x != nil {
		return x.TargetGroupName
	}
	return ""
}

func (x *TargetsUpdateRequest) GetTargetGroupTargets() []*TargetUpdate {
	if x != nil {
		return x.TargetGroupTargets
	}
	return nil
}

func (x *TargetsUpdateRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// [zh] 修改某个后端组的后端属性的响应
// [en] Response to modifying the backend properties of a backend group
type TargetGroupTargets struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 后端组包含的后端列表
	// [en] List of backends contained in the current backend group
	CurrTargets []*Target `protobuf:"bytes,1,rep,name=curr_targets,json=currTargets,proto3" json:"curr_targets,omitempty"`
}

func (x *TargetGroupTargets) Reset() {
	*x = TargetGroupTargets{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[24]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetGroupTargets) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetGroupTargets) ProtoMessage() {}

func (x *TargetGroupTargets) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[24]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetGroupTargets.ProtoReflect.Descriptor instead.
func (*TargetGroupTargets) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{24}
}

func (x *TargetGroupTargets) GetCurrTargets() []*Target {
	if x != nil {
		return x.CurrTargets
	}
	return nil
}

// [zh] 查询某个后端组绑定的后端列表
// [en] List requested TargetGroup targets
type ListTargetsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 过滤字段
	// [en] List filter.
	Filter string `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// [zh] 排序字段
	// [en] Sort resoults.
	OrderBy string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// [zh] 分页大小
	// [en] The maximum number of items to return.
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// [zh] 分页token
	// [en] The next_page_token value returned from a previous List request, if any.
	PageToken string `protobuf:"bytes,7,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// [zh] 租户ID
	// [en] tenant_id.
	TenantId string `protobuf:"bytes,8,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// [zh] 负载均衡名称
	// [en] SLB name
	SlbName string `protobuf:"bytes,9,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 后端组名称
	// [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	TargetGroupName string `protobuf:"bytes,10,opt,name=target_group_name,json=targetGroupName,proto3" json:"target_group_name,omitempty"`
}

func (x *ListTargetsRequest) Reset() {
	*x = ListTargetsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[25]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTargetsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTargetsRequest) ProtoMessage() {}

func (x *ListTargetsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[25]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTargetsRequest.ProtoReflect.Descriptor instead.
func (*ListTargetsRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{25}
}

func (x *ListTargetsRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListTargetsRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListTargetsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListTargetsRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListTargetsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListTargetsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListTargetsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListTargetsRequest) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *ListTargetsRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *ListTargetsRequest) GetTargetGroupName() string {
	if x != nil {
		return x.TargetGroupName
	}
	return ""
}

// [zh] 查询某个后端组绑定的后端列表
// [en] Response to list TargetGroup targets
type ListTargetsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] Target 列表
	// [en] Target list
	TargetInfos []*TargetInfo `protobuf:"bytes,1,rep,name=target_infos,json=targetInfos,proto3" json:"target_infos,omitempty"`
	// [zh] 下一个页面的 token，如果没有更多数据则为空.
	// [en] Token to retrieve the next page of results, or empty if there are no more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// [zh] 总数量
	// [en] total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListTargetsResponse) Reset() {
	*x = ListTargetsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[26]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListTargetsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListTargetsResponse) ProtoMessage() {}

func (x *ListTargetsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[26]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListTargetsResponse.ProtoReflect.Descriptor instead.
func (*ListTargetsResponse) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{26}
}

func (x *ListTargetsResponse) GetTargetInfos() []*TargetInfo {
	if x != nil {
		return x.TargetInfos
	}
	return nil
}

func (x *ListTargetsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListTargetsResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// [zh] 查询某个后端组绑定的后端
// [en] Get requested TargetGroup target
type GetTargetRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 负载均衡资源名称.
	// [en] The SLB resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	SlbName string `protobuf:"bytes,4,opt,name=slb_name,json=slbName,proto3" json:"slb_name,omitempty"`
	// [zh] 后端组名称
	// [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	TargetGroupName string `protobuf:"bytes,5,opt,name=target_group_name,json=targetGroupName,proto3" json:"target_group_name,omitempty"`
	// [zh] 后端名称
	// [en] The Target resource name
	TargetName string `protobuf:"bytes,6,opt,name=target_name,json=targetName,proto3" json:"target_name,omitempty"`
}

func (x *GetTargetRequest) Reset() {
	*x = GetTargetRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[27]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetTargetRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetTargetRequest) ProtoMessage() {}

func (x *GetTargetRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[27]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetTargetRequest.ProtoReflect.Descriptor instead.
func (*GetTargetRequest) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{27}
}

func (x *GetTargetRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetTargetRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetTargetRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetTargetRequest) GetSlbName() string {
	if x != nil {
		return x.SlbName
	}
	return ""
}

func (x *GetTargetRequest) GetTargetGroupName() string {
	if x != nil {
		return x.TargetGroupName
	}
	return ""
}

func (x *GetTargetRequest) GetTargetName() string {
	if x != nil {
		return x.TargetName
	}
	return ""
}

// [zh] SLB 的后端状态
// [en] SLB status
type SLBStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] SLB 配置
	// [en] SLB info
	Slb *SLB `protobuf:"bytes,1,opt,name=slb,proto3" json:"slb,omitempty"`
	// [zh] 公网VIP
	// [en] internet vip
	InternetVip string `protobuf:"bytes,2,opt,name=internet_vip,json=internetVip,proto3" json:"internet_vip,omitempty"`
	// [zh] 内网VIP
	// [en] intranet vip
	IntranetVip string `protobuf:"bytes,3,opt,name=intranet_vip,json=intranetVip,proto3" json:"intranet_vip,omitempty"`
	// [zh] 基础网络VIP
	// [en] basic network vip
	BasicNetworkVip string `protobuf:"bytes,4,opt,name=basic_network_vip,json=basicNetworkVip,proto3" json:"basic_network_vip,omitempty"`
	// [zh] 是否暴露基础网络VIP
	// [en] expose basic network vip
	ExposeBasicNetworkVip bool `protobuf:"varint,5,opt,name=expose_basic_network_vip,json=exposeBasicNetworkVip,proto3" json:"expose_basic_network_vip,omitempty"`
	// [zh] vpc 信息
	// [en] vpc info
	Vpc *VPCInfo `protobuf:"bytes,6,opt,name=vpc,proto3" json:"vpc,omitempty"`
	// [zh] eip 信息
	// [en] eip info
	Eip *EIPInfo `protobuf:"bytes,7,opt,name=eip,proto3" json:"eip,omitempty"`
	// [zh] 监听器信息
	// [en] listener info
	Listeners []*ListenerInfo `protobuf:"bytes,8,rep,name=listeners,proto3" json:"listeners,omitempty"`
}

func (x *SLBStatus) Reset() {
	*x = SLBStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[28]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *SLBStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*SLBStatus) ProtoMessage() {}

func (x *SLBStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[28]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use SLBStatus.ProtoReflect.Descriptor instead.
func (*SLBStatus) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{28}
}

func (x *SLBStatus) GetSlb() *SLB {
	if x != nil {
		return x.Slb
	}
	return nil
}

func (x *SLBStatus) GetInternetVip() string {
	if x != nil {
		return x.InternetVip
	}
	return ""
}

func (x *SLBStatus) GetIntranetVip() string {
	if x != nil {
		return x.IntranetVip
	}
	return ""
}

func (x *SLBStatus) GetBasicNetworkVip() string {
	if x != nil {
		return x.BasicNetworkVip
	}
	return ""
}

func (x *SLBStatus) GetExposeBasicNetworkVip() bool {
	if x != nil {
		return x.ExposeBasicNetworkVip
	}
	return false
}

func (x *SLBStatus) GetVpc() *VPCInfo {
	if x != nil {
		return x.Vpc
	}
	return nil
}

func (x *SLBStatus) GetEip() *EIPInfo {
	if x != nil {
		return x.Eip
	}
	return nil
}

func (x *SLBStatus) GetListeners() []*ListenerInfo {
	if x != nil {
		return x.Listeners
	}
	return nil
}

// [zh] 监听器资源.
// [en] The Listener resource.
type Listener struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源id.
	// [en] The Listener resource id using the form:
	//     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/slbs/{slb_name}/listeners/{name}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// [zh] 资源的uuid.
	// [en] The Listener resource uuid.
	Uid string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// [zh] 资源标识.
	// [en] The Listener resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] 资源名称.
	// [en] The Listener resource display name
	DisplayName string `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// [zh] 资源描述.
	// [en] The Listener resource description.
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// [zh] 资源类型.
	// [en] The Listener resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// [zh] 创建者id.
	// [en] The id of the user who created the Listener resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// [zh] 拥有者id.
	// [en] The id of the user who owns the Listener resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// [zh] 租户id
	// [en] Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 资源状态.
	// [en] The current state of the Listener resource.
	State Listener_State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.slb.v1.Listener_State" json:"state,omitempty"`
	// [zh] 监听器资源的标签.
	// [en] Tags attached to the VirtualMachine resource.
	Tags map[string]string `protobuf:"bytes,12,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// [zh] 监听器资源的属性
	// [en] Properties of the VirtualMachine resource.
	Properties *ListenerProperties `protobuf:"bytes,13,opt,name=properties,proto3" json:"properties,omitempty"` // [(validate.rules).message.required = true];
	// [zh] 资源是否已删除.
	// [en] Indicates whether the Listener resource is deleted or not.
	Deleted bool `protobuf:"varint,14,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// [zh] 资源创建时间.
	// [en] The time when the Listener resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// [zh] 资源更新时间.
	// [en] The time when the Listener resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *Listener) Reset() {
	*x = Listener{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[29]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Listener) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Listener) ProtoMessage() {}

func (x *Listener) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[29]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Listener.ProtoReflect.Descriptor instead.
func (*Listener) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{29}
}

func (x *Listener) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Listener) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Listener) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Listener) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Listener) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Listener) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *Listener) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *Listener) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *Listener) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *Listener) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *Listener) GetState() Listener_State {
	if x != nil {
		return x.State
	}
	return Listener_CREATING
}

func (x *Listener) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Listener) GetProperties() *ListenerProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *Listener) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *Listener) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Listener) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// [zh] Listener 资源实际属性
// [en] Real resource properties of Listener
type ListenerProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 监听的协议
	// [en] Listener protocol
	Protocol ListenerProperties_ListenProtocol `protobuf:"varint,1,opt,name=protocol,proto3,enum=sensetime.core.network.slb.v1.ListenerProperties_ListenProtocol" json:"protocol,omitempty"`
	// [zh] 监听的端口
	// [en] Listener port
	Port int32 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	// [zh] 默认转发动作
	// [en] Default forward action
	DefaultForwardAction *ForwardAction `protobuf:"bytes,3,opt,name=default_forward_action,json=defaultForwardAction,proto3" json:"default_forward_action,omitempty"`
	// [zh] 源地址保持配置
	// [en] Config of retaine original client IP address
	OriginalClientIpAddress *OriginalClientIPAddress `protobuf:"bytes,4,opt,name=original_client_ip_address,json=originalClientIpAddress,proto3" json:"original_client_ip_address,omitempty"`
}

func (x *ListenerProperties) Reset() {
	*x = ListenerProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[30]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListenerProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListenerProperties) ProtoMessage() {}

func (x *ListenerProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[30]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListenerProperties.ProtoReflect.Descriptor instead.
func (*ListenerProperties) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{30}
}

func (x *ListenerProperties) GetProtocol() ListenerProperties_ListenProtocol {
	if x != nil {
		return x.Protocol
	}
	return ListenerProperties_INVALID
}

func (x *ListenerProperties) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *ListenerProperties) GetDefaultForwardAction() *ForwardAction {
	if x != nil {
		return x.DefaultForwardAction
	}
	return nil
}

func (x *ListenerProperties) GetOriginalClientIpAddress() *OriginalClientIPAddress {
	if x != nil {
		return x.OriginalClientIpAddress
	}
	return nil
}

// [zh] Listener 的信息
// [en] Listener info
type ListenerStatusInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 监听器资源配置
	// [en] Listener info.
	Listener *Listener `protobuf:"bytes,1,opt,name=listener,proto3" json:"listener,omitempty"`
	// [zh] 监听器资源状态
	// [en] Listener status.
	Status *ListenerStatus `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *ListenerStatusInfo) Reset() {
	*x = ListenerStatusInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[31]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListenerStatusInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListenerStatusInfo) ProtoMessage() {}

func (x *ListenerStatusInfo) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[31]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListenerStatusInfo.ProtoReflect.Descriptor instead.
func (*ListenerStatusInfo) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{31}
}

func (x *ListenerStatusInfo) GetListener() *Listener {
	if x != nil {
		return x.Listener
	}
	return nil
}

func (x *ListenerStatusInfo) GetStatus() *ListenerStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

// [zh] Listener 的状态
// [en] Listener status
type ListenerStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 健康检查结果
	// [en] Health check results
	HealthCheckStatus *ListenerHealthCheckStatus `protobuf:"bytes,2,opt,name=health_check_status,json=healthCheckStatus,proto3" json:"health_check_status,omitempty"`
}

func (x *ListenerStatus) Reset() {
	*x = ListenerStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[32]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListenerStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListenerStatus) ProtoMessage() {}

func (x *ListenerStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[32]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListenerStatus.ProtoReflect.Descriptor instead.
func (*ListenerStatus) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{32}
}

func (x *ListenerStatus) GetHealthCheckStatus() *ListenerHealthCheckStatus {
	if x != nil {
		return x.HealthCheckStatus
	}
	return nil
}

// [zh] 后端组
// [en] TargetGroup
type TargetGroup struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源ID
	// [en] The Listener resouce id using the form:
	//     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{name}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// [zh] 资源UUID
	// [en] The TargetGroup uuid
	Uid string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// [zh] 资源标识.
	// [en] The TargetGroup resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] 资源名称.
	// [en] The TargetGroup resource display name
	DisplayName string `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// [zh] 资源描述.
	// [en] The TargetGroup resource description.
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// [zh] 资源类型.
	// [en] The TargetGroup resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// [zh] 创建者id.
	// [en] The id of the user who created the TargetGroup resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// [zh] 拥有者id.
	// [en] The id of the user who owns the TargetGroup resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// [zh] 租户id
	// [en] Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 资源状态.
	// [en] The current state of the TargetGroup resource.
	State TargetGroup_State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.slb.v1.TargetGroup_State" json:"state,omitempty"`
	// [zh] 资源的标签.
	// [en] Tags attached to the TargetGroup resource.
	Tags map[string]string `protobuf:"bytes,12,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// [zh] 后端组属性
	// [en] Properties of the TargetGroup resource.
	Properties *TargetGroupProperties `protobuf:"bytes,13,opt,name=properties,proto3" json:"properties,omitempty"`
	// [zh] 是否删除
	// [en] Indicates whether the TargetGroup resource is deleted or not.
	Deleted bool `protobuf:"varint,14,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// [zh] 创建时间
	// [en] The time when the TargetGroup resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// [zh] 更新时间
	// [en] The time when the TargetGroup resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *TargetGroup) Reset() {
	*x = TargetGroup{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[33]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetGroup) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetGroup) ProtoMessage() {}

func (x *TargetGroup) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[33]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetGroup.ProtoReflect.Descriptor instead.
func (*TargetGroup) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{33}
}

func (x *TargetGroup) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TargetGroup) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *TargetGroup) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TargetGroup) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *TargetGroup) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TargetGroup) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *TargetGroup) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *TargetGroup) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *TargetGroup) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *TargetGroup) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *TargetGroup) GetState() TargetGroup_State {
	if x != nil {
		return x.State
	}
	return TargetGroup_CREATING
}

func (x *TargetGroup) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *TargetGroup) GetProperties() *TargetGroupProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *TargetGroup) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *TargetGroup) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *TargetGroup) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// [zh] 后端组资源实际属性
// [en] Real resource properties of TargetGroup.
type TargetGroupProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 后端组的类型
	// [en] TargetGroup type
	Type TargetGroupProperties_SLBTargetType `protobuf:"varint,1,opt,name=type,proto3,enum=sensetime.core.network.slb.v1.TargetGroupProperties_SLBTargetType" json:"type,omitempty"`
	// [zh] 负载算法
	// [en] The scheduling algorithm
	Scheduler TargetGroupProperties_TargetGroupSchedulerType `protobuf:"varint,2,opt,name=scheduler,proto3,enum=sensetime.core.network.slb.v1.TargetGroupProperties_TargetGroupSchedulerType" json:"scheduler,omitempty"`
	// [zh] 后端使用的协议，默认与监听协议保持一致
	// [en] The protocol used by the Target
	Protocol TargetGroupProperties_TargetProtocol `protobuf:"varint,3,opt,name=protocol,proto3,enum=sensetime.core.network.slb.v1.TargetGroupProperties_TargetProtocol" json:"protocol,omitempty"`
	// [zh] 健康检查配置
	// [en] Health check config
	HealthCheck *HealthCheck `protobuf:"bytes,4,opt,name=health_check,json=healthCheck,proto3" json:"health_check,omitempty"`
	// [zh] 后端组权重，范围 0~100，默认100，如果为0，则不转发请求到这个后端组
	// [en] The weight of the TargetGroup. Valid values: 0 to 100. Default value: 100. If the weight of a TargetGroup is set to 0, no requests are forwarded to the TargetGroup.
	Weight int32 `protobuf:"varint,5,opt,name=weight,proto3" json:"weight,omitempty"`
	// [zh] CCI应用信息，类型为： CCI_DEPLOYMENT_INSTANCE 生效
	// [en] The CCI application info takes effect when the type is CCI_DEPLOYMENT_INSTANCE.
	CciInfo *CciDeploymentInstanceInfo `protobuf:"bytes,6,opt,name=cci_info,json=cciInfo,proto3" json:"cci_info,omitempty"`
}

func (x *TargetGroupProperties) Reset() {
	*x = TargetGroupProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[34]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetGroupProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetGroupProperties) ProtoMessage() {}

func (x *TargetGroupProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[34]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetGroupProperties.ProtoReflect.Descriptor instead.
func (*TargetGroupProperties) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{34}
}

func (x *TargetGroupProperties) GetType() TargetGroupProperties_SLBTargetType {
	if x != nil {
		return x.Type
	}
	return TargetGroupProperties_INVALID
}

func (x *TargetGroupProperties) GetScheduler() TargetGroupProperties_TargetGroupSchedulerType {
	if x != nil {
		return x.Scheduler
	}
	return TargetGroupProperties_RR
}

func (x *TargetGroupProperties) GetProtocol() TargetGroupProperties_TargetProtocol {
	if x != nil {
		return x.Protocol
	}
	return TargetGroupProperties_DEFAULT
}

func (x *TargetGroupProperties) GetHealthCheck() *HealthCheck {
	if x != nil {
		return x.HealthCheck
	}
	return nil
}

func (x *TargetGroupProperties) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *TargetGroupProperties) GetCciInfo() *CciDeploymentInstanceInfo {
	if x != nil {
		return x.CciInfo
	}
	return nil
}

// [zh] TargetGroup 的状态
// [en] TargetGroup status`
type TargetGroupStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 后端组配置
	// [en] Target group info
	TargetGroup *TargetGroup `protobuf:"bytes,1,opt,name=target_group,json=targetGroup,proto3" json:"target_group,omitempty"`
	// [zh] 关联的监听器信息
	// [en] Associated Listener Information
	Listeners []*ListenerInfo `protobuf:"bytes,2,rep,name=listeners,proto3" json:"listeners,omitempty"`
	// [zh] 所属VPC信息
	// [en] VPC info
	Vpc *VPCInfo `protobuf:"bytes,3,opt,name=vpc,proto3" json:"vpc,omitempty"`
}

func (x *TargetGroupStatus) Reset() {
	*x = TargetGroupStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[35]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetGroupStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetGroupStatus) ProtoMessage() {}

func (x *TargetGroupStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[35]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetGroupStatus.ProtoReflect.Descriptor instead.
func (*TargetGroupStatus) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{35}
}

func (x *TargetGroupStatus) GetTargetGroup() *TargetGroup {
	if x != nil {
		return x.TargetGroup
	}
	return nil
}

func (x *TargetGroupStatus) GetListeners() []*ListenerInfo {
	if x != nil {
		return x.Listeners
	}
	return nil
}

func (x *TargetGroupStatus) GetVpc() *VPCInfo {
	if x != nil {
		return x.Vpc
	}
	return nil
}

// [zh] Target 实体结构
// [en] Target entity
type Target struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源id
	// [en] The Listener resouce id using the form:
	//     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/slbs/{slb_name}/targetGroups/{target_group_name}/targets/{name}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// [zh] 资源uuid
	// [en] The Target uuid
	Uid string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// [zh] 资源标识
	// [en] The Target resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] 资源名称
	// [en] The Target resource display name
	DisplayName string `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// [zh] 资源描述
	// [en] The Target resource description.
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// [zh] 资源类型
	// [en] The Target resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// [zh] 创建者id
	// [en] The id of the user who created the Target resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// [zh] 拥有者id
	// [en] The id of the user who owns the Target resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// [zh] 租户id
	// [en] Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// [zh] 可用区
	// [en] Available zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 资源状态
	// [en] The current state of the Target resource.
	State Target_State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.slb.v1.Target_State" json:"state,omitempty"`
	// [zh] 资源标签
	// [en] Tags attached to the Target resource.
	Tags map[string]string `protobuf:"bytes,12,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// [zh] 资源属性
	// [en] Properties of the Target resource.
	Properties *TargetProperties `protobuf:"bytes,13,opt,name=properties,proto3" json:"properties,omitempty"`
	// [zh] 是否删除
	// [en] Indicates whether the Target resource is deleted or not.
	Deleted bool `protobuf:"varint,14,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// [zh] 创建时间
	// [en] The time when the Target resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,15,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// [zh] 更新时间
	// [en] The time when the Target resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,16,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *Target) Reset() {
	*x = Target{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[36]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Target) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Target) ProtoMessage() {}

func (x *Target) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[36]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Target.ProtoReflect.Descriptor instead.
func (*Target) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{36}
}

func (x *Target) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *Target) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *Target) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *Target) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *Target) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *Target) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *Target) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *Target) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *Target) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *Target) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *Target) GetState() Target_State {
	if x != nil {
		return x.State
	}
	return Target_ADDING
}

func (x *Target) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *Target) GetProperties() *TargetProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *Target) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *Target) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *Target) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// [zh] Target 资源实际属性
// [en] Real resource properties of Target
type TargetProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] IPv4地址
	// [en] ipv4 address
	Ipv4Address string `protobuf:"bytes,1,opt,name=ipv4_address,json=ipv4Address,proto3" json:"ipv4_address,omitempty"`
	// [zh] 后端使用的端口
	// [en] The port used by the Target.
	Port int32 `protobuf:"varint,2,opt,name=port,proto3" json:"port,omitempty"`
	// [zh] 后端权重，范围 0~100，默认100，如果为0，则不转发请求到这个后端
	// [en] The weight of the Target. Valid values: 0 to 100. Default value: 100. If the weight of a target is set to 0, no requests are forwarded to the target.
	Weight int32 `protobuf:"varint,3,opt,name=weight,proto3" json:"weight,omitempty"`
	// [zh] 后端类型
	// [en] Target type
	Type TargetProperties_SLBTargetType `protobuf:"varint,4,opt,name=type,proto3,enum=sensetime.core.network.slb.v1.TargetProperties_SLBTargetType" json:"type,omitempty"`
	// [zh] 实例名称
	// [en] instanceName
	InstanceName string `protobuf:"bytes,5,opt,name=instance_name,json=instanceName,proto3" json:"instance_name,omitempty"`
}

func (x *TargetProperties) Reset() {
	*x = TargetProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[37]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetProperties) ProtoMessage() {}

func (x *TargetProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[37]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetProperties.ProtoReflect.Descriptor instead.
func (*TargetProperties) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{37}
}

func (x *TargetProperties) GetIpv4Address() string {
	if x != nil {
		return x.Ipv4Address
	}
	return ""
}

func (x *TargetProperties) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

func (x *TargetProperties) GetWeight() int32 {
	if x != nil {
		return x.Weight
	}
	return 0
}

func (x *TargetProperties) GetType() TargetProperties_SLBTargetType {
	if x != nil {
		return x.Type
	}
	return TargetProperties_INVALID
}

func (x *TargetProperties) GetInstanceName() string {
	if x != nil {
		return x.InstanceName
	}
	return ""
}

// [zh] 健康检查配置
// [en] Health check config
type HealthCheck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 是否开启健康检查
	// [en] Health check switch
	Enable bool `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	// [zh] 健康检查类型
	// [en] Healcheck type
	Type HealthCheck_HealthCheckType `protobuf:"varint,2,opt,name=type,proto3,enum=sensetime.core.network.slb.v1.HealthCheck_HealthCheckType" json:"type,omitempty"`
	// [zh] 等待健康检查的超时时间，单位: 毫秒
	// [en] The time to wait for a health check response, unit: ms.
	Timeout int32 `protobuf:"varint,3,opt,name=timeout,proto3" json:"timeout,omitempty"`
	// [zh] 检查间隔，单位: 秒
	// [en] The interval between health checks.
	Interval int32 `protobuf:"varint,4,opt,name=interval,proto3" json:"interval,omitempty"`
	// [zh] 健康状态阈值，主机被标记为健康之前所需的健康健康检查次数。启动的时候，只需要一次，即可立即进入健康状态。
	// [en] The number of healthy health checks required before a host is marked healthy
	HealthThreshold int32 `protobuf:"varint,5,opt,name=health_threshold,json=healthThreshold,proto3" json:"health_threshold,omitempty"`
	// [zh] 不健康状态阈值，主机被标记为不健康之前所需的不健康健康检查次数。
	// [en] The number of unhealthy health checks required before a host is marked unhealthy
	UnhealthThreshold int32 `protobuf:"varint,6,opt,name=unhealth_threshold,json=unhealthThreshold,proto3" json:"unhealth_threshold,omitempty"`
	// [zh] TCP 主动健康检查配置，当健康检查类型为 TCP 的时候，配置生效
	// [en] TCP health check config
	TcpHealthCheckConfig *TCPHealthCheckConfig `protobuf:"bytes,7,opt,name=tcp_health_check_config,json=tcpHealthCheckConfig,proto3" json:"tcp_health_check_config,omitempty"`
	// [zh] HTTP 主动健康检查配置，当健康检查类型为 HTTP 的时候，配置生效
	// [en] HTTP health check config
	HttpHealthCheckConfig *HTTPHealthCheckConfig `protobuf:"bytes,8,opt,name=http_health_check_config,json=httpHealthCheckConfig,proto3" json:"http_health_check_config,omitempty"`
}

func (x *HealthCheck) Reset() {
	*x = HealthCheck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[38]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealthCheck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealthCheck) ProtoMessage() {}

func (x *HealthCheck) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[38]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealthCheck.ProtoReflect.Descriptor instead.
func (*HealthCheck) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{38}
}

func (x *HealthCheck) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *HealthCheck) GetType() HealthCheck_HealthCheckType {
	if x != nil {
		return x.Type
	}
	return HealthCheck_INVALID
}

func (x *HealthCheck) GetTimeout() int32 {
	if x != nil {
		return x.Timeout
	}
	return 0
}

func (x *HealthCheck) GetInterval() int32 {
	if x != nil {
		return x.Interval
	}
	return 0
}

func (x *HealthCheck) GetHealthThreshold() int32 {
	if x != nil {
		return x.HealthThreshold
	}
	return 0
}

func (x *HealthCheck) GetUnhealthThreshold() int32 {
	if x != nil {
		return x.UnhealthThreshold
	}
	return 0
}

func (x *HealthCheck) GetTcpHealthCheckConfig() *TCPHealthCheckConfig {
	if x != nil {
		return x.TcpHealthCheckConfig
	}
	return nil
}

func (x *HealthCheck) GetHttpHealthCheckConfig() *HTTPHealthCheckConfig {
	if x != nil {
		return x.HttpHealthCheckConfig
	}
	return nil
}

// [zh] 健康检查结果
// [en] Health check status
type ListenerHealthCheckStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 后端健康检查状态
	// [en] health check status
	State ListenerHealthCheckStatus_HealthCheckState `protobuf:"varint,1,opt,name=state,proto3,enum=sensetime.core.network.slb.v1.ListenerHealthCheckStatus_HealthCheckState" json:"state,omitempty"`
}

func (x *ListenerHealthCheckStatus) Reset() {
	*x = ListenerHealthCheckStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[39]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListenerHealthCheckStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListenerHealthCheckStatus) ProtoMessage() {}

func (x *ListenerHealthCheckStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[39]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListenerHealthCheckStatus.ProtoReflect.Descriptor instead.
func (*ListenerHealthCheckStatus) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{39}
}

func (x *ListenerHealthCheckStatus) GetState() ListenerHealthCheckStatus_HealthCheckState {
	if x != nil {
		return x.State
	}
	return ListenerHealthCheckStatus_INITIAL
}

// [zh] Target 实体结构
// [en] Target entity
type TargetInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] Target 实体结构
	// [en] Target entity
	Target *Target `protobuf:"bytes,1,opt,name=target,proto3" json:"target,omitempty"`
	// [zh] Target 状态
	// [en] Target status
	Status *TargetStatus `protobuf:"bytes,2,opt,name=status,proto3" json:"status,omitempty"`
}

func (x *TargetInfo) Reset() {
	*x = TargetInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[40]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetInfo) ProtoMessage() {}

func (x *TargetInfo) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[40]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetInfo.ProtoReflect.Descriptor instead.
func (*TargetInfo) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{40}
}

func (x *TargetInfo) GetTarget() *Target {
	if x != nil {
		return x.Target
	}
	return nil
}

func (x *TargetInfo) GetStatus() *TargetStatus {
	if x != nil {
		return x.Status
	}
	return nil
}

// [zh] Target 实体结构
// [en] Target entity
type TargetStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] Target 健康检查状态
	// [en] Target health check status
	HealthCheck *TargetHealthCheckStatus `protobuf:"bytes,1,opt,name=health_check,json=healthCheck,proto3" json:"health_check,omitempty"`
}

func (x *TargetStatus) Reset() {
	*x = TargetStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[41]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetStatus) ProtoMessage() {}

func (x *TargetStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[41]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetStatus.ProtoReflect.Descriptor instead.
func (*TargetStatus) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{41}
}

func (x *TargetStatus) GetHealthCheck() *TargetHealthCheckStatus {
	if x != nil {
		return x.HealthCheck
	}
	return nil
}

// [zh] 后端的健康检查结果
// [en] Target health check status
type TargetHealthCheckStatus struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 健康检查状态
	// [en] Health check status
	State TargetHealthCheckStatus_State `protobuf:"varint,1,opt,name=state,proto3,enum=sensetime.core.network.slb.v1.TargetHealthCheckStatus_State" json:"state,omitempty"`
}

func (x *TargetHealthCheckStatus) Reset() {
	*x = TargetHealthCheckStatus{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[42]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetHealthCheckStatus) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetHealthCheckStatus) ProtoMessage() {}

func (x *TargetHealthCheckStatus) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[42]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetHealthCheckStatus.ProtoReflect.Descriptor instead.
func (*TargetHealthCheckStatus) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{42}
}

func (x *TargetHealthCheckStatus) GetState() TargetHealthCheckStatus_State {
	if x != nil {
		return x.State
	}
	return TargetHealthCheckStatus_INITIAL
}

// [zh] 转发动作
// [en] Forward action
type ForwardAction struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 转发动作类型
	// [en] Forward action type
	Type ForwardAction_ForwardActionType `protobuf:"varint,1,opt,name=type,proto3,enum=sensetime.core.network.slb.v1.ForwardAction_ForwardActionType" json:"type,omitempty"`
	// [zh] 转发动作类型为 ForwardTarget 的配置, 当 type 为 ForwardTargetGroup，此字段生效
	// [en] ForwardTarget config
	ForwardTargetGroupConfig *ForwardTargetGroupConfig `protobuf:"bytes,2,opt,name=forward_target_group_config,json=forwardTargetGroupConfig,proto3" json:"forward_target_group_config,omitempty"`
}

func (x *ForwardAction) Reset() {
	*x = ForwardAction{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[43]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForwardAction) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardAction) ProtoMessage() {}

func (x *ForwardAction) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[43]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardAction.ProtoReflect.Descriptor instead.
func (*ForwardAction) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{43}
}

func (x *ForwardAction) GetType() ForwardAction_ForwardActionType {
	if x != nil {
		return x.Type
	}
	return ForwardAction_INVALID
}

func (x *ForwardAction) GetForwardTargetGroupConfig() *ForwardTargetGroupConfig {
	if x != nil {
		return x.ForwardTargetGroupConfig
	}
	return nil
}

// [zh] 转发到目的后端的配置
// [en] ForwardTarget config
type ForwardTargetGroupConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 后端组列表
	// [en] TargetGroup list
	TargetGroups []*TargetGroupInfo `protobuf:"bytes,1,rep,name=target_groups,json=targetGroups,proto3" json:"target_groups,omitempty"`
}

func (x *ForwardTargetGroupConfig) Reset() {
	*x = ForwardTargetGroupConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[44]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ForwardTargetGroupConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ForwardTargetGroupConfig) ProtoMessage() {}

func (x *ForwardTargetGroupConfig) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[44]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ForwardTargetGroupConfig.ProtoReflect.Descriptor instead.
func (*ForwardTargetGroupConfig) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{44}
}

func (x *ForwardTargetGroupConfig) GetTargetGroups() []*TargetGroupInfo {
	if x != nil {
		return x.TargetGroups
	}
	return nil
}

// [zh] 源地址保持配置
// [en] Config of retaine original client IP address
type OriginalClientIPAddress struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 是否开启源地址保持，默认关闭
	// [en] Original client IP address retaine enable
	OriginalClientIpAddressEnable bool `protobuf:"varint,1,opt,name=original_client_ip_address_enable,json=originalClientIpAddressEnable,proto3" json:"original_client_ip_address_enable,omitempty"`
	// [zh] 四层 Proxy Protocol 配置，当 源地址保持 开启，且监听协议为四层协议的时候，此字段生效
	// [en] The configuration of Proxy Protocol
	ProxyProtocolEnable bool `protobuf:"varint,2,opt,name=proxy_protocol_enable,json=proxyProtocolEnable,proto3" json:"proxy_protocol_enable,omitempty"`
}

func (x *OriginalClientIPAddress) Reset() {
	*x = OriginalClientIPAddress{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[45]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *OriginalClientIPAddress) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*OriginalClientIPAddress) ProtoMessage() {}

func (x *OriginalClientIPAddress) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[45]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use OriginalClientIPAddress.ProtoReflect.Descriptor instead.
func (*OriginalClientIPAddress) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{45}
}

func (x *OriginalClientIPAddress) GetOriginalClientIpAddressEnable() bool {
	if x != nil {
		return x.OriginalClientIpAddressEnable
	}
	return false
}

func (x *OriginalClientIPAddress) GetProxyProtocolEnable() bool {
	if x != nil {
		return x.ProxyProtocolEnable
	}
	return false
}

// [zh] TCP 检查检查配置
// [en] TCP health check config
type TCPHealthCheckConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 健康检查端口，发送SYN握手报文来检测服务器端口是否存活
	// [en] Health check port, send SYN handshake packets to detect if the server port is alive.
	Port int32 `protobuf:"varint,1,opt,name=port,proto3" json:"port,omitempty"`
}

func (x *TCPHealthCheckConfig) Reset() {
	*x = TCPHealthCheckConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[46]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TCPHealthCheckConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TCPHealthCheckConfig) ProtoMessage() {}

func (x *TCPHealthCheckConfig) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[46]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TCPHealthCheckConfig.ProtoReflect.Descriptor instead.
func (*TCPHealthCheckConfig) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{46}
}

func (x *TCPHealthCheckConfig) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

// [zh] HTTP 检查检查配置
// [en] HTTP health check config
type HTTPHealthCheckConfig struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] HTTP 检查的host
	// [en] HTTP health check host
	Host string `protobuf:"bytes,1,opt,name=host,proto3" json:"host,omitempty"`
	// [zh] host类型
	// [en] host type
	HostType HTTPHealthCheckConfig_HttpHostType `protobuf:"varint,2,opt,name=host_type,json=hostType,proto3,enum=sensetime.core.network.slb.v1.HTTPHealthCheckConfig_HttpHostType" json:"host_type,omitempty"`
	// [zh] HTTP 检查的path
	// [en] HTTP health check path
	Path string `protobuf:"bytes,3,opt,name=path,proto3" json:"path,omitempty"`
	// [zh] HTTP 检查的方式
	// [en] HTTP health check host
	Method HTTPHealthCheckConfig_HTTPMethod `protobuf:"varint,4,opt,name=method,proto3,enum=sensetime.core.network.slb.v1.HTTPHealthCheckConfig_HTTPMethod" json:"method,omitempty"`
	// [zh] HTTP 检查的版本
	// [en] HTTP health check version
	Version HTTPHealthCheckConfig_HTTPVersion `protobuf:"varint,5,opt,name=version,proto3,enum=sensetime.core.network.slb.v1.HTTPHealthCheckConfig_HTTPVersion" json:"version,omitempty"`
	// [zh] HTTP返回码
	// [ch] HTTP return code
	ReturnCode []HTTPHealthCheckConfig_HTTPReturnCode `protobuf:"varint,6,rep,packed,name=return_code,json=returnCode,proto3,enum=sensetime.core.network.slb.v1.HTTPHealthCheckConfig_HTTPReturnCode" json:"return_code,omitempty"`
}

func (x *HTTPHealthCheckConfig) Reset() {
	*x = HTTPHealthCheckConfig{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[47]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HTTPHealthCheckConfig) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HTTPHealthCheckConfig) ProtoMessage() {}

func (x *HTTPHealthCheckConfig) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[47]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HTTPHealthCheckConfig.ProtoReflect.Descriptor instead.
func (*HTTPHealthCheckConfig) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{47}
}

func (x *HTTPHealthCheckConfig) GetHost() string {
	if x != nil {
		return x.Host
	}
	return ""
}

func (x *HTTPHealthCheckConfig) GetHostType() HTTPHealthCheckConfig_HttpHostType {
	if x != nil {
		return x.HostType
	}
	return HTTPHealthCheckConfig_IP
}

func (x *HTTPHealthCheckConfig) GetPath() string {
	if x != nil {
		return x.Path
	}
	return ""
}

func (x *HTTPHealthCheckConfig) GetMethod() HTTPHealthCheckConfig_HTTPMethod {
	if x != nil {
		return x.Method
	}
	return HTTPHealthCheckConfig_HEAD
}

func (x *HTTPHealthCheckConfig) GetVersion() HTTPHealthCheckConfig_HTTPVersion {
	if x != nil {
		return x.Version
	}
	return HTTPHealthCheckConfig_HTTP1
}

func (x *HTTPHealthCheckConfig) GetReturnCode() []HTTPHealthCheckConfig_HTTPReturnCode {
	if x != nil {
		return x.ReturnCode
	}
	return nil
}

// [zh] VPC 信息
// [en] VPC info
type VPCInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] ID
	// [en] ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// [zh] UID
	// [en] UID
	Uid string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// [zh] name
	// [en] name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] display_name
	// [en] display_name
	DisplayName string `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
}

func (x *VPCInfo) Reset() {
	*x = VPCInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[48]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VPCInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VPCInfo) ProtoMessage() {}

func (x *VPCInfo) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[48]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VPCInfo.ProtoReflect.Descriptor instead.
func (*VPCInfo) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{48}
}

func (x *VPCInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VPCInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *VPCInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VPCInfo) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

// [zh] EIP 信息
// [en] EIP info
type EIPInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] ID
	// [en] ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// [zh] UID
	// [en] UID
	Uid string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// [zh] name
	// [en] name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] display_name
	// [en] display_name
	DisplayName string `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
}

func (x *EIPInfo) Reset() {
	*x = EIPInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[49]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *EIPInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*EIPInfo) ProtoMessage() {}

func (x *EIPInfo) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[49]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use EIPInfo.ProtoReflect.Descriptor instead.
func (*EIPInfo) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{49}
}

func (x *EIPInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *EIPInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *EIPInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *EIPInfo) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

// [zh] 监听器信息
// [en] Listener info
type ListenerInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] ID
	// [en] EIP ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// [zh] UID
	// [en] UID
	Uid string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// [zh] name
	// [en] name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] display_name
	// [en] display_name
	DisplayName string `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// [zh] Listener 资源实际属性
	// [en] Real resource properties of Listener
	Properties *ListenerProperties `protobuf:"bytes,5,opt,name=properties,proto3" json:"properties,omitempty"`
}

func (x *ListenerInfo) Reset() {
	*x = ListenerInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[50]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListenerInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListenerInfo) ProtoMessage() {}

func (x *ListenerInfo) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[50]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListenerInfo.ProtoReflect.Descriptor instead.
func (*ListenerInfo) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{50}
}

func (x *ListenerInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *ListenerInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *ListenerInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *ListenerInfo) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *ListenerInfo) GetProperties() *ListenerProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

// [zh] 后端组信息
// [en] target group info
type TargetGroupInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] ID
	// [en] EIP ID
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// [zh] UID
	// [en] UID
	Uid string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// [zh] name
	// [en] name
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] display_name
	// [en] display_name
	DisplayName string `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
}

func (x *TargetGroupInfo) Reset() {
	*x = TargetGroupInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[51]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetGroupInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetGroupInfo) ProtoMessage() {}

func (x *TargetGroupInfo) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[51]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetGroupInfo.ProtoReflect.Descriptor instead.
func (*TargetGroupInfo) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{51}
}

func (x *TargetGroupInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *TargetGroupInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *TargetGroupInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *TargetGroupInfo) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

// [zh] 监听器可更新资源.
// [en] The Listener update-resource.
type ListenerUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源名称.
	// [en] The Listener resource display name
	DisplayName *string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3,oneof" json:"display_name,omitempty"`
	// [zh] 资源描述.
	// [en] The Listener resource description.
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// [zh] 监听器资源的属性
	// [en] Properties of the VirtualMachine resource.
	Properties *ListenerProperties `protobuf:"bytes,3,opt,name=properties,proto3" json:"properties,omitempty"`
}

func (x *ListenerUpdate) Reset() {
	*x = ListenerUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[52]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListenerUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListenerUpdate) ProtoMessage() {}

func (x *ListenerUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[52]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListenerUpdate.ProtoReflect.Descriptor instead.
func (*ListenerUpdate) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{52}
}

func (x *ListenerUpdate) GetDisplayName() string {
	if x != nil && x.DisplayName != nil {
		return *x.DisplayName
	}
	return ""
}

func (x *ListenerUpdate) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *ListenerUpdate) GetProperties() *ListenerProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

// [zh] 后端组可更新资源.
// [en] The TargetGroup update-resource.
type TargetGroupUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源名称.
	// [en] The TargetGroup resource display name
	DisplayName *string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3,oneof" json:"display_name,omitempty"`
	// [zh] 资源描述.
	// [en] The TargetGroup resource description.
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// [zh] 后端组属性
	// [en] Properties of the TargetGroup resource.
	Properties *TargetGroupProperties `protobuf:"bytes,3,opt,name=properties,proto3" json:"properties,omitempty"`
}

func (x *TargetGroupUpdate) Reset() {
	*x = TargetGroupUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[53]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetGroupUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetGroupUpdate) ProtoMessage() {}

func (x *TargetGroupUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[53]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetGroupUpdate.ProtoReflect.Descriptor instead.
func (*TargetGroupUpdate) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{53}
}

func (x *TargetGroupUpdate) GetDisplayName() string {
	if x != nil && x.DisplayName != nil {
		return *x.DisplayName
	}
	return ""
}

func (x *TargetGroupUpdate) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TargetGroupUpdate) GetProperties() *TargetGroupProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

// [zh] 后端可更新资源.
// [en] The Target update-resource.
type TargetUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源uuid
	// [en] The Target uuid
	Uid string `protobuf:"bytes,1,opt,name=uid,proto3" json:"uid,omitempty"`
	// [zh] 资源名称
	// [en] The Target resource display name
	DisplayName *string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3,oneof" json:"display_name,omitempty"`
	// [zh] 资源描述
	// [en] The Target resource description.
	Description string `protobuf:"bytes,3,opt,name=description,proto3" json:"description,omitempty"`
	// [zh] 资源属性
	// [en] Properties of the Target resource.
	Properties *TargetProperties `protobuf:"bytes,4,opt,name=properties,proto3" json:"properties,omitempty"`
}

func (x *TargetUpdate) Reset() {
	*x = TargetUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[54]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *TargetUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*TargetUpdate) ProtoMessage() {}

func (x *TargetUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[54]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use TargetUpdate.ProtoReflect.Descriptor instead.
func (*TargetUpdate) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{54}
}

func (x *TargetUpdate) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *TargetUpdate) GetDisplayName() string {
	if x != nil && x.DisplayName != nil {
		return *x.DisplayName
	}
	return ""
}

func (x *TargetUpdate) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *TargetUpdate) GetProperties() *TargetProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

// [zh] 容器应用部署信息
// [en] CCI deployment instance info
type CciDeploymentInstanceInfo struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] instance name
	// instance name
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] instance display name
	// [en] instance display name
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// [zh] instance id
	// [en] instance id
	Id string `protobuf:"bytes,3,opt,name=id,proto3" json:"id,omitempty"`
	// [zh] instance uid
	// [en] instance uid
	Uid string `protobuf:"bytes,4,opt,name=uid,proto3" json:"uid,omitempty"`
	// [zh] instance port
	// [en] instance port
	Port int32 `protobuf:"varint,5,opt,name=port,proto3" json:"port,omitempty"`
}

func (x *CciDeploymentInstanceInfo) Reset() {
	*x = CciDeploymentInstanceInfo{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_slb_v1_slb_data_proto_msgTypes[55]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CciDeploymentInstanceInfo) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CciDeploymentInstanceInfo) ProtoMessage() {}

func (x *CciDeploymentInstanceInfo) ProtoReflect() protoreflect.Message {
	mi := &file_network_slb_v1_slb_data_proto_msgTypes[55]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CciDeploymentInstanceInfo.ProtoReflect.Descriptor instead.
func (*CciDeploymentInstanceInfo) Descriptor() ([]byte, []int) {
	return file_network_slb_v1_slb_data_proto_rawDescGZIP(), []int{55}
}

func (x *CciDeploymentInstanceInfo) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *CciDeploymentInstanceInfo) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *CciDeploymentInstanceInfo) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *CciDeploymentInstanceInfo) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *CciDeploymentInstanceInfo) GetPort() int32 {
	if x != nil {
		return x.Port
	}
	return 0
}

var File_network_slb_v1_slb_data_proto protoreflect.FileDescriptor

var file_network_slb_v1_slb_data_proto_rawDesc = []byte{
	0x0a, 0x1d, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x6c, 0x62, 0x5f, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12,
	0x1d, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x1a, 0x1c,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62,
	0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65,
	0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69,
	0x64, 0x61, 0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f,
	0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x18, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c,
	0x62, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x6c, 0x62, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0xdf,
	0x01, 0x0a, 0x13, 0x47, 0x65, 0x74, 0x53, 0x4c, 0x42, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27,
	0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0xa3, 0x02, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x4c, 0x42, 0x73, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1b, 0x0a,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05,
	0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61,
	0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x6c, 0x62,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x73, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x08, 0x73, 0x6c,
	0x62, 0x4e, 0x61, 0x6d, 0x65, 0x73, 0x22, 0x9d, 0x01, 0x0a, 0x16, 0x4c, 0x69, 0x73, 0x74, 0x53,
	0x4c, 0x42, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x3c, 0x0a, 0x04, 0x73, 0x6c, 0x62, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x4c, 0x42, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x04, 0x73, 0x6c, 0x62, 0x73, 0x12,
	0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61,
	0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74,
	0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xeb, 0x02, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a,
	0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32,
	0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d,
	0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29,
	0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x52, 0x0a, 0x0d, 0x6c,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d,
	0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f,
	0x24, 0x52, 0x0c, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x43, 0x0a, 0x08, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x27, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x52, 0x08, 0x6c, 0x69, 0x73, 0x74,
	0x65, 0x6e, 0x65, 0x72, 0x22, 0xb3, 0x03, 0x0a, 0x15, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4c,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b,
	0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12,
	0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e,
	0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b,
	0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24,
	0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x52, 0x0a, 0x0d, 0x6c, 0x69, 0x73,
	0x74, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b,
	0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30,
	0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52,
	0x0c, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x49, 0x0a,
	0x08, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2d, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x08,
	0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b, 0x22, 0xa6, 0x02, 0x0a, 0x15, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72,
	0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x52, 0x0a, 0x0d, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25,
	0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0c, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0xec, 0x02, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x65, 0x6e, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33,
	0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06,
	0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69,
	0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12,
	0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa,
	0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0xc5, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x65, 0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x65, 0x0a, 0x15,
	0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x5f,
	0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x65, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x13,
	0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e,
	0x66, 0x6f, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65,
	0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74,
	0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xb2, 0x02, 0x0a, 0x12, 0x47,
	0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32,
	0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d,
	0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29,
	0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x52, 0x0a, 0x0d, 0x6c,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d,
	0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f,
	0x24, 0x52, 0x0c, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22,
	0xb8, 0x02, 0x0a, 0x18, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33,
	0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a, 0x08,
	0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d,
	0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d,
	0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36,
	0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x73,
	0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x52, 0x0a, 0x0d, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0,
	0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a,
	0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31,
	0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0c, 0x6c, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xb4, 0x02, 0x0a, 0x14, 0x53,
	0x74, 0x61, 0x72, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03,
	0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18,
	0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x52, 0x0a,
	0x0d, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f,
	0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x29, 0x3f, 0x24, 0x52, 0x0c, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0xb3, 0x02, 0x0a, 0x13, 0x53, 0x74, 0x6f, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03,
	0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02,
	0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28,
	0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x52, 0x0a, 0x0d, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa,
	0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0c, 0x6c, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x65, 0x72, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xff, 0x02, 0x0a, 0x18, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72,
	0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x59, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa,
	0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4d, 0x0a, 0x0c, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0b, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0xc7, 0x03, 0x0a, 0x18, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42,
	0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x59, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41,
	0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d,
	0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d,
	0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0f, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x53, 0x0a, 0x0c,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x30, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x55, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x52, 0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61,
	0x73, 0x6b, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d,
	0x61, 0x73, 0x6b, 0x22, 0xb0, 0x02, 0x0a, 0x18, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a,
	0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32,
	0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d,
	0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29,
	0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x59, 0x0a, 0x11, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25,
	0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xef, 0x02, 0x0a, 0x17, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69,
	0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x48,
	0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b,
	0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30,
	0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52,
	0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xb8, 0x01, 0x0a, 0x18, 0x4c, 0x69, 0x73,
	0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x55, 0x0a, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x30, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0c,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x26, 0x0a, 0x0f,
	0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53,
	0x69, 0x7a, 0x65, 0x22, 0xbc, 0x02, 0x0a, 0x15, 0x47, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a,
	0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a,
	0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c,
	0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07,
	0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x59, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d,
	0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f,
	0x24, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x22, 0xc2, 0x02, 0x0a, 0x1b, 0x47, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f,
	0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x29, 0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x59, 0x0a, 0x11,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72,
	0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a,
	0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x84, 0x03, 0x0a, 0x1c, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x41, 0x64, 0x64, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa,
	0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x59, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0,
	0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a,
	0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31,
	0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0f, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3f, 0x0a,
	0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x07, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22, 0x69,
	0x0a, 0x1d, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x41, 0x64, 0x64,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12,
	0x48, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x18,
	0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73,
	0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x0b, 0x63, 0x75,
	0x72, 0x72, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22, 0xe5, 0x02, 0x0a, 0x1f, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a,
	0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a,
	0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c,
	0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07,
	0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x59, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d,
	0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f,
	0x24, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x73,
	0x18, 0x06, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x64,
	0x73, 0x22, 0x6c, 0x0a, 0x20, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x48, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x5f, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22,
	0xdc, 0x03, 0x0a, 0x14, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa,
	0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x59, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0,
	0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a,
	0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31,
	0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0f, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x5d, 0x0a,
	0x14, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x73, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x12, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x12, 0x40, 0x0a, 0x0b,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x42, 0x03, 0xe0,
	0x41, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b, 0x22, 0x5e,
	0x0a, 0x12, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x73, 0x12, 0x48, 0x0a, 0x0c, 0x63, 0x75, 0x72, 0x72, 0x5f, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x52, 0x0b, 0x63, 0x75, 0x72, 0x72, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x22, 0xc5,
	0x03, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a,
	0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67,
	0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f,
	0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49,
	0x64, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32,
	0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d,
	0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29,
	0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x59, 0x0a, 0x11, 0x74,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25,
	0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xaa, 0x01, 0x0a, 0x13, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4c,
	0x0a, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x73, 0x18, 0x01,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x49, 0x6e, 0x66, 0x6f, 0x73, 0x12, 0x26, 0x0a, 0x0f,
	0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53,
	0x69, 0x7a, 0x65, 0x22, 0x87, 0x03, 0x0a, 0x10, 0x47, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a, 0x08, 0x73, 0x6c, 0x62, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa,
	0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b,
	0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x07, 0x73, 0x6c, 0x62, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x59, 0x0a, 0x11, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0,
	0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a,
	0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31,
	0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0f, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4e, 0x0a,
	0x0b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21,
	0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d,
	0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f,
	0x24, 0x52, 0x0a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xab, 0x03,
	0x0a, 0x09, 0x53, 0x4c, 0x42, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x34, 0x0a, 0x03, 0x73,
	0x6c, 0x62, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4c, 0x42, 0x52, 0x03, 0x73, 0x6c,
	0x62, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65, 0x74, 0x5f, 0x76, 0x69,
	0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x65,
	0x74, 0x56, 0x69, 0x70, 0x12, 0x21, 0x0a, 0x0c, 0x69, 0x6e, 0x74, 0x72, 0x61, 0x6e, 0x65, 0x74,
	0x5f, 0x76, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x6e, 0x74, 0x72,
	0x61, 0x6e, 0x65, 0x74, 0x56, 0x69, 0x70, 0x12, 0x2a, 0x0a, 0x11, 0x62, 0x61, 0x73, 0x69, 0x63,
	0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x76, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0f, 0x62, 0x61, 0x73, 0x69, 0x63, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x56, 0x69, 0x70, 0x12, 0x37, 0x0a, 0x18, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x5f, 0x62, 0x61,
	0x73, 0x69, 0x63, 0x5f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x5f, 0x76, 0x69, 0x70, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x08, 0x52, 0x15, 0x65, 0x78, 0x70, 0x6f, 0x73, 0x65, 0x42, 0x61, 0x73,
	0x69, 0x63, 0x4e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x56, 0x69, 0x70, 0x12, 0x38, 0x0a, 0x03,
	0x76, 0x70, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x50, 0x43, 0x49, 0x6e, 0x66,
	0x6f, 0x52, 0x03, 0x76, 0x70, 0x63, 0x12, 0x38, 0x0a, 0x03, 0x65, 0x69, 0x70, 0x18, 0x07, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x26, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x45, 0x49, 0x50, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x03, 0x65, 0x69, 0x70,
	0x12, 0x49, 0x0a, 0x09, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x08, 0x20,
	0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f,
	0x52, 0x09, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x22, 0xc6, 0x07, 0x0a, 0x08,
	0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a,
	0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52,
	0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x73, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x50,
	0xe0, 0x41, 0x02, 0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d,
	0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b,
	0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24,
	0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x09, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52,
	0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03,
	0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x12, 0x48, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01,
	0x28, 0x0e, 0x32, 0x2d, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x2e, 0x53, 0x74, 0x61, 0x74,
	0x65, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x45, 0x0a,
	0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74,
	0x65, 0x6e, 0x65, 0x72, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x12, 0x51, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x07, 0x64,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x40, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x63, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a,
	0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61,
	0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a,
	0x02, 0x38, 0x01, 0x22, 0x7f, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0c, 0x0a, 0x08,
	0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x45, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4e, 0x47,
	0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12,
	0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x53,
	0x54, 0x4f, 0x50, 0x50, 0x49, 0x4e, 0x47, 0x10, 0x06, 0x12, 0x0b, 0x0a, 0x07, 0x53, 0x54, 0x4f,
	0x50, 0x50, 0x45, 0x44, 0x10, 0x07, 0x12, 0x0c, 0x0a, 0x08, 0x53, 0x54, 0x41, 0x52, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0x08, 0x22, 0x90, 0x03, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65,
	0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x5c, 0x0a, 0x08, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x02, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x62, 0x0a,
	0x16, 0x64, 0x65, 0x66, 0x61, 0x75, 0x6c, 0x74, 0x5f, 0x66, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64,
	0x5f, 0x61, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f,
	0x72, 0x77, 0x61, 0x72, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x52, 0x14, 0x64, 0x65, 0x66,
	0x61, 0x75, 0x6c, 0x74, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f,
	0x6e, 0x12, 0x73, 0x0a, 0x1a, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x36, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73,
	0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x6c,
	0x69, 0x65, 0x6e, 0x74, 0x49, 0x50, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x52, 0x17, 0x6f,
	0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49, 0x70, 0x41,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x22, 0x2f, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x43, 0x50, 0x10, 0x01, 0x12, 0x07,
	0x0a, 0x03, 0x55, 0x44, 0x50, 0x10, 0x02, 0x22, 0xa0, 0x01, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74,
	0x65, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x43,
	0x0a, 0x08, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x27, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x52, 0x08, 0x6c, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x65, 0x72, 0x12, 0x45, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2d, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74,
	0x75, 0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x7a, 0x0a, 0x0e, 0x4c, 0x69,
	0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x68, 0x0a, 0x13,
	0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f, 0x73, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x65, 0x72, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x11, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xa0, 0x07, 0x0a, 0x0b, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x3e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32,
	0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d,
	0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29,
	0x3f, 0x24, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x70, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x4d,
	0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b,
	0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d,
	0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61,
	0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24, 0x52, 0x0b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08,
	0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x46, 0x0a, 0x05,
	0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x30, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x48, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0c, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x34, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x2e, 0x54,
	0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x54,
	0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x34, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18,
	0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x3b,
	0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x0f, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38,
	0x01, 0x22, 0x56, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52,
	0x45, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x44, 0x41,
	0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45,
	0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x03,
	0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0a, 0x0a,
	0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x22, 0xc8, 0x05, 0x0a, 0x15, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x56, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x42, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x53, 0x4c, 0x42, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x6b, 0x0a, 0x09, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x4d,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x54, 0x79, 0x70, 0x65, 0x52, 0x09, 0x73,
	0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x12, 0x5f, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x43, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x52,
	0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x4d, 0x0a, 0x0c, 0x68, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x0b, 0x68, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67,
	0x68, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74,
	0x12, 0x53, 0x0a, 0x08, 0x63, 0x63, 0x69, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x06, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x38, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e,
	0x76, 0x31, 0x2e, 0x43, 0x63, 0x69, 0x44, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74,
	0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x07, 0x63, 0x63,
	0x69, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0x4e, 0x0a, 0x0d, 0x53, 0x4c, 0x42, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49,
	0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x43, 0x49, 0x5f, 0x44, 0x45, 0x50, 0x4c, 0x4f,
	0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45, 0x10, 0x01,
	0x12, 0x13, 0x0a, 0x0f, 0x53, 0x45, 0x52, 0x56, 0x45, 0x52, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41,
	0x4e, 0x43, 0x45, 0x10, 0x02, 0x22, 0x2b, 0x0a, 0x18, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x53, 0x63, 0x68, 0x65, 0x64, 0x75, 0x6c, 0x65, 0x72, 0x54, 0x79, 0x70,
	0x65, 0x12, 0x06, 0x0a, 0x02, 0x52, 0x52, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x57, 0x52, 0x52,
	0x10, 0x01, 0x22, 0x50, 0x0a, 0x0e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x46, 0x41, 0x55, 0x4c, 0x54, 0x10,
	0x00, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x54, 0x54, 0x50, 0x10, 0x01, 0x12, 0x09, 0x0a, 0x05, 0x48,
	0x54, 0x54, 0x50, 0x53, 0x10, 0x02, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x43, 0x50, 0x10, 0x03, 0x12,
	0x07, 0x0a, 0x03, 0x55, 0x44, 0x50, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x54, 0x43, 0x50, 0x53,
	0x53, 0x4c, 0x10, 0x05, 0x22, 0xe7, 0x01, 0x0a, 0x11, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x4d, 0x0a, 0x0c, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x0b, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x49, 0x0a, 0x09, 0x6c, 0x69, 0x73,
	0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x18, 0x02, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73,
	0x74, 0x65, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6c, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x65, 0x72, 0x73, 0x12, 0x38, 0x0a, 0x03, 0x76, 0x70, 0x63, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x26, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x56, 0x50, 0x43, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x03, 0x76, 0x70, 0x63, 0x22, 0x8a,
	0x07, 0x0a, 0x06, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x3e, 0x0a, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42, 0x27, 0x72, 0x25,
	0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x70, 0x0a, 0x0c, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x4d, 0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d,
	0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b,
	0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24,
	0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18,
	0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b,
	0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a,
	0x6f, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12,
	0x41, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x2b,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x43, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0c, 0x20, 0x03, 0x28, 0x0b,
	0x32, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72,
	0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x4f, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65,
	0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x64, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d,
	0x65, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74,
	0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12,
	0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70,
	0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x37, 0x0a, 0x09,
	0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76,
	0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75,
	0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x54, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0a,
	0x0a, 0x06, 0x41, 0x44, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x45, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x4d, 0x4f, 0x56, 0x49, 0x4e, 0x47,
	0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x52, 0x45, 0x4d, 0x4f, 0x56, 0x45, 0x44, 0x10, 0x04, 0x12,
	0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x22, 0xb8, 0x02, 0x0a, 0x10,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x12, 0x21, 0x0a, 0x0c, 0x69, 0x70, 0x76, 0x34, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x69, 0x70, 0x76, 0x34, 0x41, 0x64, 0x64, 0x72,
	0x65, 0x73, 0x73, 0x12, 0x12, 0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68,
	0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x06, 0x77, 0x65, 0x69, 0x67, 0x68, 0x74, 0x12,
	0x51, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3d, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x2e, 0x53,
	0x4c, 0x42, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79,
	0x70, 0x65, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x5d, 0x0a, 0x0d, 0x53, 0x4c, 0x42, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x4e, 0x56, 0x41,
	0x4c, 0x49, 0x44, 0x10, 0x00, 0x12, 0x1b, 0x0a, 0x17, 0x43, 0x43, 0x49, 0x5f, 0x44, 0x45, 0x50,
	0x4c, 0x4f, 0x59, 0x4d, 0x45, 0x4e, 0x54, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e, 0x43, 0x45,
	0x10, 0x01, 0x12, 0x10, 0x0a, 0x0c, 0x45, 0x43, 0x53, 0x5f, 0x49, 0x4e, 0x53, 0x54, 0x41, 0x4e,
	0x43, 0x45, 0x10, 0x02, 0x12, 0x10, 0x0a, 0x0c, 0x42, 0x4d, 0x53, 0x5f, 0x49, 0x4e, 0x53, 0x54,
	0x41, 0x4e, 0x43, 0x45, 0x10, 0x03, 0x22, 0x93, 0x04, 0x0a, 0x0b, 0x48, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12, 0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x4e,
	0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x3a, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x18,
	0x0a, 0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x07, 0x74, 0x69, 0x6d, 0x65, 0x6f, 0x75, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x18, 0x04, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65,
	0x72, 0x76, 0x61, 0x6c, 0x12, 0x29, 0x0a, 0x10, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x74,
	0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0f,
	0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12,
	0x2d, 0x0a, 0x12, 0x75, 0x6e, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x74, 0x68, 0x72, 0x65,
	0x73, 0x68, 0x6f, 0x6c, 0x64, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x11, 0x75, 0x6e, 0x68,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x54, 0x68, 0x72, 0x65, 0x73, 0x68, 0x6f, 0x6c, 0x64, 0x12, 0x6a,
	0x0a, 0x17, 0x74, 0x63, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x63, 0x68, 0x65,
	0x63, 0x6b, 0x5f, 0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x07, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x33, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x43, 0x50, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x52, 0x14, 0x74, 0x63, 0x70, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x6d, 0x0a, 0x18, 0x68, 0x74,
	0x74, 0x70, 0x5f, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x08, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x54, 0x54,
	0x50, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66,
	0x69, 0x67, 0x52, 0x15, 0x68, 0x74, 0x74, 0x70, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22, 0x31, 0x0a, 0x0f, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07,
	0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x54, 0x43, 0x50,
	0x10, 0x01, 0x12, 0x08, 0x0a, 0x04, 0x48, 0x54, 0x54, 0x50, 0x10, 0x02, 0x22, 0xd7, 0x01, 0x0a,
	0x19, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x5f, 0x0a, 0x05, 0x73, 0x74,
	0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x49, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x65, 0x72, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x2e, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53,
	0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x22, 0x59, 0x0a, 0x10, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x0b, 0x0a, 0x07, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06,
	0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x10, 0x01, 0x12, 0x11, 0x0a, 0x0d, 0x50, 0x41, 0x52, 0x54,
	0x4c, 0x59, 0x5f, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x55,
	0x4e, 0x48, 0x45, 0x41, 0x4c, 0x54, 0x48, 0x10, 0x03, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x49, 0x53,
	0x41, 0x42, 0x4c, 0x45, 0x10, 0x04, 0x22, 0x90, 0x01, 0x0a, 0x0a, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x3d, 0x0a, 0x06, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x25, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73,
	0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x06, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x12, 0x43, 0x0a, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2b, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75,
	0x73, 0x52, 0x06, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0x69, 0x0a, 0x0c, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x59, 0x0a, 0x0c, 0x68, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x36, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x0b, 0x68, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x22, 0xaa, 0x01, 0x0a, 0x17, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x52, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x0e, 0x32,
	0x3c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63,
	0x6b, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x22, 0x3b, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0b, 0x0a,
	0x07, 0x49, 0x4e, 0x49, 0x54, 0x49, 0x41, 0x4c, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06, 0x48, 0x45,
	0x41, 0x4c, 0x54, 0x48, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x4e, 0x48, 0x45, 0x41, 0x4c,
	0x54, 0x48, 0x10, 0x02, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x49, 0x53, 0x41, 0x42, 0x4c, 0x45, 0x10,
	0x03, 0x22, 0x97, 0x02, 0x0a, 0x0d, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x41, 0x63, 0x74,
	0x69, 0x6f, 0x6e, 0x12, 0x52, 0x0a, 0x04, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x3e, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x2e,
	0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e, 0x54, 0x79, 0x70,
	0x65, 0x52, 0x04, 0x74, 0x79, 0x70, 0x65, 0x12, 0x76, 0x0a, 0x1b, 0x66, 0x6f, 0x72, 0x77, 0x61,
	0x72, 0x64, 0x5f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x63, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x37, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x46, 0x6f, 0x72,
	0x77, 0x61, 0x72, 0x64, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43,
	0x6f, 0x6e, 0x66, 0x69, 0x67, 0x52, 0x18, 0x66, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x22,
	0x3a, 0x0a, 0x11, 0x46, 0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x41, 0x63, 0x74, 0x69, 0x6f, 0x6e,
	0x54, 0x79, 0x70, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x49, 0x4e, 0x56, 0x41, 0x4c, 0x49, 0x44, 0x10,
	0x00, 0x12, 0x18, 0x0a, 0x14, 0x46, 0x4f, 0x52, 0x57, 0x41, 0x52, 0x44, 0x5f, 0x54, 0x41, 0x52,
	0x47, 0x45, 0x54, 0x5f, 0x47, 0x52, 0x4f, 0x55, 0x50, 0x10, 0x01, 0x22, 0x6f, 0x0a, 0x18, 0x46,
	0x6f, 0x72, 0x77, 0x61, 0x72, 0x64, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x53, 0x0a, 0x0d, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2e,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x0c,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x22, 0x97, 0x01, 0x0a,
	0x17, 0x4f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x49,
	0x50, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x12, 0x48, 0x0a, 0x21, 0x6f, 0x72, 0x69, 0x67,
	0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x63, 0x6c, 0x69, 0x65, 0x6e, 0x74, 0x5f, 0x69, 0x70, 0x5f, 0x61,
	0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x1d, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x43, 0x6c, 0x69,
	0x65, 0x6e, 0x74, 0x49, 0x70, 0x41, 0x64, 0x64, 0x72, 0x65, 0x73, 0x73, 0x45, 0x6e, 0x61, 0x62,
	0x6c, 0x65, 0x12, 0x32, 0x0a, 0x15, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x5f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x63, 0x6f, 0x6c, 0x5f, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x08, 0x52, 0x13, 0x70, 0x72, 0x6f, 0x78, 0x79, 0x50, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c,
	0x45, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x22, 0x2a, 0x0a, 0x14, 0x54, 0x43, 0x50, 0x48, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x22, 0xfa, 0x04, 0x0a, 0x15, 0x48, 0x54, 0x54, 0x50, 0x48, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x12, 0x12, 0x0a, 0x04,
	0x68, 0x6f, 0x73, 0x74, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x68, 0x6f, 0x73, 0x74,
	0x12, 0x5e, 0x0a, 0x09, 0x68, 0x6f, 0x73, 0x74, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0e, 0x32, 0x41, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68,
	0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x48, 0x74, 0x74, 0x70, 0x48, 0x6f,
	0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x52, 0x08, 0x68, 0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x70, 0x61, 0x74, 0x68, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x70, 0x61, 0x74, 0x68, 0x12, 0x57, 0x0a, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x0e, 0x32, 0x3f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43,
	0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f, 0x6e, 0x66, 0x69, 0x67, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x4d,
	0x65, 0x74, 0x68, 0x6f, 0x64, 0x52, 0x06, 0x6d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12, 0x5a, 0x0a,
	0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x40,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x48,
	0x54, 0x54, 0x50, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e,
	0x52, 0x07, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x12, 0x64, 0x0a, 0x0b, 0x72, 0x65, 0x74,
	0x75, 0x72, 0x6e, 0x5f, 0x63, 0x6f, 0x64, 0x65, 0x18, 0x06, 0x20, 0x03, 0x28, 0x0e, 0x32, 0x43,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x48,
	0x54, 0x54, 0x50, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x43, 0x6f,
	0x6e, 0x66, 0x69, 0x67, 0x2e, 0x48, 0x54, 0x54, 0x50, 0x52, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43,
	0x6f, 0x64, 0x65, 0x52, 0x0a, 0x72, 0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x22,
	0x20, 0x0a, 0x0c, 0x48, 0x74, 0x74, 0x70, 0x48, 0x6f, 0x73, 0x74, 0x54, 0x79, 0x70, 0x65, 0x12,
	0x06, 0x0a, 0x02, 0x49, 0x50, 0x10, 0x00, 0x12, 0x08, 0x0a, 0x04, 0x4e, 0x41, 0x4d, 0x45, 0x10,
	0x01, 0x22, 0x1f, 0x0a, 0x0a, 0x48, 0x54, 0x54, 0x50, 0x4d, 0x65, 0x74, 0x68, 0x6f, 0x64, 0x12,
	0x08, 0x0a, 0x04, 0x48, 0x45, 0x41, 0x44, 0x10, 0x00, 0x12, 0x07, 0x0a, 0x03, 0x47, 0x45, 0x54,
	0x10, 0x01, 0x22, 0x23, 0x0a, 0x0b, 0x48, 0x54, 0x54, 0x50, 0x56, 0x65, 0x72, 0x73, 0x69, 0x6f,
	0x6e, 0x12, 0x09, 0x0a, 0x05, 0x48, 0x54, 0x54, 0x50, 0x31, 0x10, 0x00, 0x12, 0x09, 0x0a, 0x05,
	0x48, 0x54, 0x54, 0x50, 0x32, 0x10, 0x01, 0x22, 0x56, 0x0a, 0x0e, 0x48, 0x54, 0x54, 0x50, 0x52,
	0x65, 0x74, 0x75, 0x72, 0x6e, 0x43, 0x6f, 0x64, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x63, 0x6f, 0x64,
	0x65, 0x5f, 0x31, 0x58, 0x58, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x63, 0x6f, 0x64, 0x65, 0x5f,
	0x32, 0x58, 0x58, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x33, 0x58,
	0x58, 0x10, 0x02, 0x12, 0x0c, 0x0a, 0x08, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x34, 0x58, 0x58, 0x10,
	0x03, 0x12, 0x0c, 0x0a, 0x08, 0x63, 0x6f, 0x64, 0x65, 0x5f, 0x35, 0x58, 0x58, 0x10, 0x04, 0x22,
	0x62, 0x0a, 0x07, 0x56, 0x50, 0x43, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e,
	0x61, 0x6d, 0x65, 0x22, 0x62, 0x0a, 0x07, 0x45, 0x49, 0x50, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10,
	0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64,
	0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xba, 0x01, 0x0a, 0x0c, 0x4c, 0x69, 0x73, 0x74,
	0x65, 0x6e, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21,
	0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x51, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73,
	0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x22, 0x6a, 0x0a, 0x0f, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a,
	0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x22, 0x90, 0x02, 0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x12, 0x78, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x50, 0xe0, 0x41, 0x02, 0xfa, 0x42,
	0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41,
	0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66,
	0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c,
	0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d,
	0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24, 0x48, 0x00, 0x52, 0x0b, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x51, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x31, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x22, 0x93, 0x02, 0x0a, 0x11, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x75, 0x0a, 0x0c, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x4d, 0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78,
	0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41,
	0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66,
	0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24, 0x48, 0x00,
	0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x88, 0x01, 0x01,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x54, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x9b, 0x02, 0x0a, 0x0c, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69,
	0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x75, 0x0a, 0x0c,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x4d, 0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d,
	0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78,
	0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29,
	0x24, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4f, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x22, 0x88, 0x01, 0x0a, 0x19, 0x43, 0x63, 0x69, 0x44,
	0x65, 0x70, 0x6c, 0x6f, 0x79, 0x6d, 0x65, 0x6e, 0x74, 0x49, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63,
	0x65, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x64, 0x69, 0x73,
	0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03,
	0x75, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x70, 0x6f, 0x72, 0x74, 0x18, 0x05, 0x20, 0x01, 0x28, 0x05, 0x52, 0x04, 0x70, 0x6f,
	0x72, 0x74, 0x32, 0x88, 0x43, 0x0a, 0x0e, 0x53, 0x4c, 0x42, 0x44, 0x61, 0x74, 0x61, 0x53, 0x65,
	0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xf5, 0x02, 0x0a, 0x0c, 0x47, 0x65, 0x74, 0x53, 0x4c, 0x42,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x32, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x53, 0x4c, 0x42, 0x53, 0x74, 0x61,
	0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x28, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x4c, 0x42, 0x53, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x22, 0x86, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x81, 0x01, 0x12, 0x7f,
	0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x64, 0x61, 0x74,
	0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65,
	0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73,
	0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x80,
	0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x76, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d,
	0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x12, 0x0b, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x67, 0x65, 0x74, 0x12, 0xf8, 0x01,
	0x0a, 0x0e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x4c, 0x42, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73,
	0x12, 0x34, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x4c, 0x42, 0x73, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x35, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x53, 0x4c, 0x42, 0x73, 0x53,
	0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x79, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x6f, 0x12, 0x6d, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f,
	0x73, 0x6c, 0x62, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f,
	0x73, 0x6c, 0x62, 0x73, 0x80, 0xb5, 0x18, 0x01, 0x12, 0x9d, 0x03, 0x0a, 0x0e, 0x43, 0x72, 0x65,
	0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x12, 0x34, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x27, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x22, 0xab, 0x02, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x9f, 0x01, 0x22, 0x92, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f,
	0x73, 0x6c, 0x62, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f,
	0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x6c, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x08, 0x6c, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x65, 0x72, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a,
	0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65,
	0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73,
	0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c,
	0x62, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x9d, 0x03, 0x0a, 0x0e, 0x55, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x12, 0x34, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x27, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x22, 0xab, 0x02, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x9f, 0x01, 0x32, 0x92, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f,
	0x73, 0x6c, 0x62, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f,
	0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x6c, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x08, 0x6c, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x65, 0x72, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a,
	0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65,
	0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73,
	0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c,
	0x62, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x82, 0x03, 0x0a, 0x0e, 0x44, 0x65, 0x6c,
	0x65, 0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x12, 0x34, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xa1, 0x02, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x95, 0x01, 0x2a, 0x92, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73,
	0x6c, 0x62, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73,
	0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6c,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01,
	0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c,
	0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x73,
	0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x87, 0x03,
	0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x12,
	0x33, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x34, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65,
	0x72, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8a, 0x02, 0x82, 0xd3, 0xe4,
	0x93, 0x02, 0x85, 0x01, 0x12, 0x82, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f,
	0x73, 0x6c, 0x62, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f,
	0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18,
	0x76, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f,
	0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f,
	0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0b, 0x73, 0x6c, 0x62, 0x2e,
	0x73, 0x6c, 0x62, 0x2e, 0x67, 0x65, 0x74, 0x12, 0x86, 0x03, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x4c,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x12, 0x31, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x27, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x65,
	0x6e, 0x65, 0x72, 0x22, 0x9a, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x95, 0x01, 0x12, 0x92, 0x01,
	0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x64, 0x61, 0x74,
	0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65,
	0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73,
	0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65,
	0x72, 0x73, 0x2f, 0x7b, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x76, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f,
	0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x12, 0x0b, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x67, 0x65, 0x74,
	0x12, 0x9f, 0x03, 0x0a, 0x11, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72,
	0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x37, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2d, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xa1,
	0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x9c, 0x01, 0x12, 0x99, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f,
	0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x6c,
	0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x76, 0x0a, 0x67, 0x2f, 0x72,
	0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b,
	0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0b, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x67,
	0x65, 0x74, 0x12, 0x89, 0x03, 0x0a, 0x0d, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x69, 0x73, 0x74,
	0x65, 0x6e, 0x65, 0x72, 0x12, 0x33, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c,
	0x62, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x72, 0x74, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e,
	0x65, 0x72, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0xaa, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x9e, 0x01, 0x22, 0x98, 0x01, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f,
	0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73,
	0x2f, 0x7b, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x3a, 0x73, 0x74, 0x61, 0x72, 0x74, 0x3a, 0x01, 0x2a, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18,
	0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73,
	0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e,
	0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x86,
	0x03, 0x0a, 0x0c, 0x53, 0x74, 0x6f, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x12,
	0x32, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e,
	0x53, 0x74, 0x6f, 0x70, 0x4c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xa9, 0x02, 0x82, 0xd3,
	0xe4, 0x93, 0x02, 0x9d, 0x01, 0x22, 0x97, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d,
	0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x6c, 0x69, 0x73, 0x74, 0x65, 0x6e, 0x65, 0x72, 0x73, 0x2f, 0x7b, 0x6c, 0x69, 0x73, 0x74,
	0x65, 0x6e, 0x65, 0x72, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x73, 0x74, 0x6f, 0x70, 0x3a,
	0x01, 0x2a, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67,
	0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73,
	0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c,
	0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62,
	0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xb1, 0x03, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61,
	0x74, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x37, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52,
	0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x22, 0xb6, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xaa, 0x01, 0x22, 0x99, 0x01, 0x2f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x64, 0x61, 0x74, 0x61,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73,
	0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c,
	0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x0c, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5,
	0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a,
	0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73,
	0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x73, 0x6c, 0x62,
	0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xb1, 0x03, 0x0a, 0x11,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x12, 0x37, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2a, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0xb6, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xaa, 0x01,
	0x32, 0x99, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f,
	0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a,
	0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73,
	0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x0c, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5,
	0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65,
	0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f,
	0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12,
	0x0e, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12,
	0x8f, 0x03, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x37, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73,
	0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x54, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0xa8, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x9c, 0x01,
	0x2a, 0x99, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f,
	0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a,
	0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73,
	0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x88, 0xb5, 0x18, 0x01,
	0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x12, 0x0e, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x12, 0x93, 0x03, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x12, 0x36, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x37,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c,
	0x69, 0x73, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x8d, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x88,
	0x01, 0x12, 0x85, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62,
	0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62,
	0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18,
	0x76, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f,
	0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f,
	0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0b, 0x73, 0x6c, 0x62, 0x2e,
	0x73, 0x6c, 0x62, 0x2e, 0x67, 0x65, 0x74, 0x12, 0x96, 0x03, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x12, 0x34, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x2a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31,
	0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x22, 0xa1, 0x02, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x9c, 0x01, 0x12, 0x99, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x76, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f,
	0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x12, 0x0b, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x67, 0x65, 0x74,
	0x12, 0xaf, 0x03, 0x0a, 0x14, 0x47, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x3a, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x30, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73,
	0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x22, 0xa8, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xa3,
	0x01, 0x12, 0xa0, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62,
	0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62,
	0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x73, 0x74,
	0x61, 0x74, 0x75, 0x73, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x76, 0x0a, 0x67, 0x2f, 0x72,
	0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b,
	0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0b, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x67,
	0x65, 0x74, 0x12, 0xcb, 0x03, 0x0a, 0x15, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x41, 0x64, 0x64, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x12, 0x3b, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x41, 0x64, 0x64, 0x54, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3c, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x41, 0x64, 0x64, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x52,
	0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb6, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xaa,
	0x01, 0x22, 0xa4, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62,
	0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62,
	0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x74, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x73, 0x41, 0x64, 0x64, 0x3a, 0x01, 0x2a, 0x88, 0xb5, 0x18, 0x01, 0x80,
	0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d,
	0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x12, 0x0e, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0xd7, 0x03, 0x0a, 0x18, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x12, 0x3e, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x3f, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61,
	0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x52, 0x65, 0x6d, 0x6f, 0x76, 0x65, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xb9,
	0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xad, 0x01, 0x22, 0xa7, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f,
	0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x2f, 0x7b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x52, 0x65, 0x6d, 0x6f,
	0x76, 0x65, 0x3a, 0x01, 0x2a, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18,
	0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f,
	0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f,
	0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x73, 0x6c, 0x62, 0x2e,
	0x73, 0x6c, 0x62, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xad, 0x03, 0x0a, 0x0d, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0x33, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x73, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x31, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76,
	0x31, 0x2e, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x73, 0x22, 0xb3, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xa7, 0x01, 0x22, 0xa1,
	0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x64, 0x61,
	0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e,
	0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b,
	0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x73, 0x3a, 0x01, 0x2a, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18,
	0x79, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f,
	0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f,
	0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x73, 0x6c, 0x62, 0x2e,
	0x73, 0x6c, 0x62, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xa0, 0x03, 0x0a, 0x0b, 0x4c,
	0x69, 0x73, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x12, 0x31, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x32, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x22, 0xa9, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xa4, 0x01, 0x12, 0xa1, 0x01, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f,
	0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x2f, 0x7b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x73, 0x80,
	0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x76, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d,
	0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x12, 0x0b, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x67, 0x65, 0x74, 0x12, 0x9d, 0x03,
	0x0a, 0x09, 0x47, 0x65, 0x74, 0x54, 0x61, 0x72, 0x67, 0x65, 0x74, 0x12, 0x2f, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x54,
	0x61, 0x72, 0x67, 0x65, 0x74, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x25, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x76, 0x31, 0x2e, 0x54, 0x61, 0x72,
	0x67, 0x65, 0x74, 0x22, 0xb7, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0xb2, 0x01, 0x12, 0xaf, 0x01,
	0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x64, 0x61, 0x74,
	0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65,
	0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73,
	0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74,
	0x73, 0x2f, 0x7b, 0x74, 0x61, 0x72, 0x67, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x80,
	0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x76, 0x0a, 0x67, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d,
	0x2f, 0x73, 0x6c, 0x62, 0x73, 0x2f, 0x7b, 0x73, 0x6c, 0x62, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x12, 0x0b, 0x73, 0x6c, 0x62, 0x2e, 0x73, 0x6c, 0x62, 0x2e, 0x67, 0x65, 0x74, 0x42, 0x50, 0x5a,
	0x4e, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x62, 0x6a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74,
	0x61, 0x72, 0x79, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2d,
	0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x73, 0x6c, 0x62, 0x2f, 0x76, 0x31, 0x3b, 0x73, 0x6c, 0x62, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_network_slb_v1_slb_data_proto_rawDescOnce sync.Once
	file_network_slb_v1_slb_data_proto_rawDescData = file_network_slb_v1_slb_data_proto_rawDesc
)

func file_network_slb_v1_slb_data_proto_rawDescGZIP() []byte {
	file_network_slb_v1_slb_data_proto_rawDescOnce.Do(func() {
		file_network_slb_v1_slb_data_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_slb_v1_slb_data_proto_rawDescData)
	})
	return file_network_slb_v1_slb_data_proto_rawDescData
}

var file_network_slb_v1_slb_data_proto_enumTypes = make([]protoimpl.EnumInfo, 16)
var file_network_slb_v1_slb_data_proto_msgTypes = make([]protoimpl.MessageInfo, 59)
var file_network_slb_v1_slb_data_proto_goTypes = []interface{}{
	(Listener_State)(0),                                 // 0: sensetime.core.network.slb.v1.Listener.State
	(ListenerProperties_ListenProtocol)(0),              // 1: sensetime.core.network.slb.v1.ListenerProperties.ListenProtocol
	(TargetGroup_State)(0),                              // 2: sensetime.core.network.slb.v1.TargetGroup.State
	(TargetGroupProperties_SLBTargetType)(0),            // 3: sensetime.core.network.slb.v1.TargetGroupProperties.SLBTargetType
	(TargetGroupProperties_TargetGroupSchedulerType)(0), // 4: sensetime.core.network.slb.v1.TargetGroupProperties.TargetGroupSchedulerType
	(TargetGroupProperties_TargetProtocol)(0),           // 5: sensetime.core.network.slb.v1.TargetGroupProperties.TargetProtocol
	(Target_State)(0),                                   // 6: sensetime.core.network.slb.v1.Target.State
	(TargetProperties_SLBTargetType)(0),                 // 7: sensetime.core.network.slb.v1.TargetProperties.SLBTargetType
	(HealthCheck_HealthCheckType)(0),                    // 8: sensetime.core.network.slb.v1.HealthCheck.HealthCheckType
	(ListenerHealthCheckStatus_HealthCheckState)(0),     // 9: sensetime.core.network.slb.v1.ListenerHealthCheckStatus.HealthCheckState
	(TargetHealthCheckStatus_State)(0),                  // 10: sensetime.core.network.slb.v1.TargetHealthCheckStatus.State
	(ForwardAction_ForwardActionType)(0),                // 11: sensetime.core.network.slb.v1.ForwardAction.ForwardActionType
	(HTTPHealthCheckConfig_HttpHostType)(0),             // 12: sensetime.core.network.slb.v1.HTTPHealthCheckConfig.HttpHostType
	(HTTPHealthCheckConfig_HTTPMethod)(0),               // 13: sensetime.core.network.slb.v1.HTTPHealthCheckConfig.HTTPMethod
	(HTTPHealthCheckConfig_HTTPVersion)(0),              // 14: sensetime.core.network.slb.v1.HTTPHealthCheckConfig.HTTPVersion
	(HTTPHealthCheckConfig_HTTPReturnCode)(0),           // 15: sensetime.core.network.slb.v1.HTTPHealthCheckConfig.HTTPReturnCode
	(*GetSLBStatusRequest)(nil),                         // 16: sensetime.core.network.slb.v1.GetSLBStatusRequest
	(*ListSLBsStatusRequest)(nil),                       // 17: sensetime.core.network.slb.v1.ListSLBsStatusRequest
	(*ListSLBsStatusResponse)(nil),                      // 18: sensetime.core.network.slb.v1.ListSLBsStatusResponse
	(*CreateListenerRequest)(nil),                       // 19: sensetime.core.network.slb.v1.CreateListenerRequest
	(*UpdateListenerRequest)(nil),                       // 20: sensetime.core.network.slb.v1.UpdateListenerRequest
	(*DeleteListenerRequest)(nil),                       // 21: sensetime.core.network.slb.v1.DeleteListenerRequest
	(*ListListenersRequest)(nil),                        // 22: sensetime.core.network.slb.v1.ListListenersRequest
	(*ListListenersResponse)(nil),                       // 23: sensetime.core.network.slb.v1.ListListenersResponse
	(*GetListenerRequest)(nil),                          // 24: sensetime.core.network.slb.v1.GetListenerRequest
	(*GetListenerStatusRequest)(nil),                    // 25: sensetime.core.network.slb.v1.GetListenerStatusRequest
	(*StartListenerRequest)(nil),                        // 26: sensetime.core.network.slb.v1.StartListenerRequest
	(*StopListenerRequest)(nil),                         // 27: sensetime.core.network.slb.v1.StopListenerRequest
	(*CreateTargetGroupRequest)(nil),                    // 28: sensetime.core.network.slb.v1.CreateTargetGroupRequest
	(*UpdateTargetGroupRequest)(nil),                    // 29: sensetime.core.network.slb.v1.UpdateTargetGroupRequest
	(*DeleteTargetGroupRequest)(nil),                    // 30: sensetime.core.network.slb.v1.DeleteTargetGroupRequest
	(*ListTargetGroupsRequest)(nil),                     // 31: sensetime.core.network.slb.v1.ListTargetGroupsRequest
	(*ListTargetGroupsResponse)(nil),                    // 32: sensetime.core.network.slb.v1.ListTargetGroupsResponse
	(*GetTargetGroupRequest)(nil),                       // 33: sensetime.core.network.slb.v1.GetTargetGroupRequest
	(*GetTargetGroupStatusRequest)(nil),                 // 34: sensetime.core.network.slb.v1.GetTargetGroupStatusRequest
	(*TargetGroupAddTargetsRequest)(nil),                // 35: sensetime.core.network.slb.v1.TargetGroupAddTargetsRequest
	(*TargetGroupAddTargetsResponse)(nil),               // 36: sensetime.core.network.slb.v1.TargetGroupAddTargetsResponse
	(*TargetGroupRemoveTargetsRequest)(nil),             // 37: sensetime.core.network.slb.v1.TargetGroupRemoveTargetsRequest
	(*TargetGroupRemoveTargetsResponse)(nil),            // 38: sensetime.core.network.slb.v1.TargetGroupRemoveTargetsResponse
	(*TargetsUpdateRequest)(nil),                        // 39: sensetime.core.network.slb.v1.TargetsUpdateRequest
	(*TargetGroupTargets)(nil),                          // 40: sensetime.core.network.slb.v1.TargetGroupTargets
	(*ListTargetsRequest)(nil),                          // 41: sensetime.core.network.slb.v1.ListTargetsRequest
	(*ListTargetsResponse)(nil),                         // 42: sensetime.core.network.slb.v1.ListTargetsResponse
	(*GetTargetRequest)(nil),                            // 43: sensetime.core.network.slb.v1.GetTargetRequest
	(*SLBStatus)(nil),                                   // 44: sensetime.core.network.slb.v1.SLBStatus
	(*Listener)(nil),                                    // 45: sensetime.core.network.slb.v1.Listener
	(*ListenerProperties)(nil),                          // 46: sensetime.core.network.slb.v1.ListenerProperties
	(*ListenerStatusInfo)(nil),                          // 47: sensetime.core.network.slb.v1.ListenerStatusInfo
	(*ListenerStatus)(nil),                              // 48: sensetime.core.network.slb.v1.ListenerStatus
	(*TargetGroup)(nil),                                 // 49: sensetime.core.network.slb.v1.TargetGroup
	(*TargetGroupProperties)(nil),                       // 50: sensetime.core.network.slb.v1.TargetGroupProperties
	(*TargetGroupStatus)(nil),                           // 51: sensetime.core.network.slb.v1.TargetGroupStatus
	(*Target)(nil),                                      // 52: sensetime.core.network.slb.v1.Target
	(*TargetProperties)(nil),                            // 53: sensetime.core.network.slb.v1.TargetProperties
	(*HealthCheck)(nil),                                 // 54: sensetime.core.network.slb.v1.HealthCheck
	(*ListenerHealthCheckStatus)(nil),                   // 55: sensetime.core.network.slb.v1.ListenerHealthCheckStatus
	(*TargetInfo)(nil),                                  // 56: sensetime.core.network.slb.v1.TargetInfo
	(*TargetStatus)(nil),                                // 57: sensetime.core.network.slb.v1.TargetStatus
	(*TargetHealthCheckStatus)(nil),                     // 58: sensetime.core.network.slb.v1.TargetHealthCheckStatus
	(*ForwardAction)(nil),                               // 59: sensetime.core.network.slb.v1.ForwardAction
	(*ForwardTargetGroupConfig)(nil),                    // 60: sensetime.core.network.slb.v1.ForwardTargetGroupConfig
	(*OriginalClientIPAddress)(nil),                     // 61: sensetime.core.network.slb.v1.OriginalClientIPAddress
	(*TCPHealthCheckConfig)(nil),                        // 62: sensetime.core.network.slb.v1.TCPHealthCheckConfig
	(*HTTPHealthCheckConfig)(nil),                       // 63: sensetime.core.network.slb.v1.HTTPHealthCheckConfig
	(*VPCInfo)(nil),                                     // 64: sensetime.core.network.slb.v1.VPCInfo
	(*EIPInfo)(nil),                                     // 65: sensetime.core.network.slb.v1.EIPInfo
	(*ListenerInfo)(nil),                                // 66: sensetime.core.network.slb.v1.ListenerInfo
	(*TargetGroupInfo)(nil),                             // 67: sensetime.core.network.slb.v1.TargetGroupInfo
	(*ListenerUpdate)(nil),                              // 68: sensetime.core.network.slb.v1.ListenerUpdate
	(*TargetGroupUpdate)(nil),                           // 69: sensetime.core.network.slb.v1.TargetGroupUpdate
	(*TargetUpdate)(nil),                                // 70: sensetime.core.network.slb.v1.TargetUpdate
	(*CciDeploymentInstanceInfo)(nil),                   // 71: sensetime.core.network.slb.v1.CciDeploymentInstanceInfo
	nil,                                                 // 72: sensetime.core.network.slb.v1.Listener.TagsEntry
	nil,                                                 // 73: sensetime.core.network.slb.v1.TargetGroup.TagsEntry
	nil,                                                 // 74: sensetime.core.network.slb.v1.Target.TagsEntry
	(*fieldmaskpb.FieldMask)(nil),                       // 75: google.protobuf.FieldMask
	(*SLB)(nil),                                         // 76: sensetime.core.network.slb.v1.SLB
	(*timestamppb.Timestamp)(nil),                       // 77: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),                               // 78: google.protobuf.Empty
}
var file_network_slb_v1_slb_data_proto_depIdxs = []int32{
	44, // 0: sensetime.core.network.slb.v1.ListSLBsStatusResponse.slbs:type_name -> sensetime.core.network.slb.v1.SLBStatus
	45, // 1: sensetime.core.network.slb.v1.CreateListenerRequest.listener:type_name -> sensetime.core.network.slb.v1.Listener
	68, // 2: sensetime.core.network.slb.v1.UpdateListenerRequest.listener:type_name -> sensetime.core.network.slb.v1.ListenerUpdate
	75, // 3: sensetime.core.network.slb.v1.UpdateListenerRequest.update_mask:type_name -> google.protobuf.FieldMask
	47, // 4: sensetime.core.network.slb.v1.ListListenersResponse.listener_status_infos:type_name -> sensetime.core.network.slb.v1.ListenerStatusInfo
	49, // 5: sensetime.core.network.slb.v1.CreateTargetGroupRequest.target_group:type_name -> sensetime.core.network.slb.v1.TargetGroup
	69, // 6: sensetime.core.network.slb.v1.UpdateTargetGroupRequest.target_group:type_name -> sensetime.core.network.slb.v1.TargetGroupUpdate
	75, // 7: sensetime.core.network.slb.v1.UpdateTargetGroupRequest.update_mask:type_name -> google.protobuf.FieldMask
	51, // 8: sensetime.core.network.slb.v1.ListTargetGroupsResponse.target_groups:type_name -> sensetime.core.network.slb.v1.TargetGroupStatus
	52, // 9: sensetime.core.network.slb.v1.TargetGroupAddTargetsRequest.targets:type_name -> sensetime.core.network.slb.v1.Target
	52, // 10: sensetime.core.network.slb.v1.TargetGroupAddTargetsResponse.curr_targets:type_name -> sensetime.core.network.slb.v1.Target
	52, // 11: sensetime.core.network.slb.v1.TargetGroupRemoveTargetsResponse.curr_targets:type_name -> sensetime.core.network.slb.v1.Target
	70, // 12: sensetime.core.network.slb.v1.TargetsUpdateRequest.target_group_targets:type_name -> sensetime.core.network.slb.v1.TargetUpdate
	75, // 13: sensetime.core.network.slb.v1.TargetsUpdateRequest.update_mask:type_name -> google.protobuf.FieldMask
	52, // 14: sensetime.core.network.slb.v1.TargetGroupTargets.curr_targets:type_name -> sensetime.core.network.slb.v1.Target
	56, // 15: sensetime.core.network.slb.v1.ListTargetsResponse.target_infos:type_name -> sensetime.core.network.slb.v1.TargetInfo
	76, // 16: sensetime.core.network.slb.v1.SLBStatus.slb:type_name -> sensetime.core.network.slb.v1.SLB
	64, // 17: sensetime.core.network.slb.v1.SLBStatus.vpc:type_name -> sensetime.core.network.slb.v1.VPCInfo
	65, // 18: sensetime.core.network.slb.v1.SLBStatus.eip:type_name -> sensetime.core.network.slb.v1.EIPInfo
	66, // 19: sensetime.core.network.slb.v1.SLBStatus.listeners:type_name -> sensetime.core.network.slb.v1.ListenerInfo
	0,  // 20: sensetime.core.network.slb.v1.Listener.state:type_name -> sensetime.core.network.slb.v1.Listener.State
	72, // 21: sensetime.core.network.slb.v1.Listener.tags:type_name -> sensetime.core.network.slb.v1.Listener.TagsEntry
	46, // 22: sensetime.core.network.slb.v1.Listener.properties:type_name -> sensetime.core.network.slb.v1.ListenerProperties
	77, // 23: sensetime.core.network.slb.v1.Listener.create_time:type_name -> google.protobuf.Timestamp
	77, // 24: sensetime.core.network.slb.v1.Listener.update_time:type_name -> google.protobuf.Timestamp
	1,  // 25: sensetime.core.network.slb.v1.ListenerProperties.protocol:type_name -> sensetime.core.network.slb.v1.ListenerProperties.ListenProtocol
	59, // 26: sensetime.core.network.slb.v1.ListenerProperties.default_forward_action:type_name -> sensetime.core.network.slb.v1.ForwardAction
	61, // 27: sensetime.core.network.slb.v1.ListenerProperties.original_client_ip_address:type_name -> sensetime.core.network.slb.v1.OriginalClientIPAddress
	45, // 28: sensetime.core.network.slb.v1.ListenerStatusInfo.listener:type_name -> sensetime.core.network.slb.v1.Listener
	48, // 29: sensetime.core.network.slb.v1.ListenerStatusInfo.status:type_name -> sensetime.core.network.slb.v1.ListenerStatus
	55, // 30: sensetime.core.network.slb.v1.ListenerStatus.health_check_status:type_name -> sensetime.core.network.slb.v1.ListenerHealthCheckStatus
	2,  // 31: sensetime.core.network.slb.v1.TargetGroup.state:type_name -> sensetime.core.network.slb.v1.TargetGroup.State
	73, // 32: sensetime.core.network.slb.v1.TargetGroup.tags:type_name -> sensetime.core.network.slb.v1.TargetGroup.TagsEntry
	50, // 33: sensetime.core.network.slb.v1.TargetGroup.properties:type_name -> sensetime.core.network.slb.v1.TargetGroupProperties
	77, // 34: sensetime.core.network.slb.v1.TargetGroup.create_time:type_name -> google.protobuf.Timestamp
	77, // 35: sensetime.core.network.slb.v1.TargetGroup.update_time:type_name -> google.protobuf.Timestamp
	3,  // 36: sensetime.core.network.slb.v1.TargetGroupProperties.type:type_name -> sensetime.core.network.slb.v1.TargetGroupProperties.SLBTargetType
	4,  // 37: sensetime.core.network.slb.v1.TargetGroupProperties.scheduler:type_name -> sensetime.core.network.slb.v1.TargetGroupProperties.TargetGroupSchedulerType
	5,  // 38: sensetime.core.network.slb.v1.TargetGroupProperties.protocol:type_name -> sensetime.core.network.slb.v1.TargetGroupProperties.TargetProtocol
	54, // 39: sensetime.core.network.slb.v1.TargetGroupProperties.health_check:type_name -> sensetime.core.network.slb.v1.HealthCheck
	71, // 40: sensetime.core.network.slb.v1.TargetGroupProperties.cci_info:type_name -> sensetime.core.network.slb.v1.CciDeploymentInstanceInfo
	49, // 41: sensetime.core.network.slb.v1.TargetGroupStatus.target_group:type_name -> sensetime.core.network.slb.v1.TargetGroup
	66, // 42: sensetime.core.network.slb.v1.TargetGroupStatus.listeners:type_name -> sensetime.core.network.slb.v1.ListenerInfo
	64, // 43: sensetime.core.network.slb.v1.TargetGroupStatus.vpc:type_name -> sensetime.core.network.slb.v1.VPCInfo
	6,  // 44: sensetime.core.network.slb.v1.Target.state:type_name -> sensetime.core.network.slb.v1.Target.State
	74, // 45: sensetime.core.network.slb.v1.Target.tags:type_name -> sensetime.core.network.slb.v1.Target.TagsEntry
	53, // 46: sensetime.core.network.slb.v1.Target.properties:type_name -> sensetime.core.network.slb.v1.TargetProperties
	77, // 47: sensetime.core.network.slb.v1.Target.create_time:type_name -> google.protobuf.Timestamp
	77, // 48: sensetime.core.network.slb.v1.Target.update_time:type_name -> google.protobuf.Timestamp
	7,  // 49: sensetime.core.network.slb.v1.TargetProperties.type:type_name -> sensetime.core.network.slb.v1.TargetProperties.SLBTargetType
	8,  // 50: sensetime.core.network.slb.v1.HealthCheck.type:type_name -> sensetime.core.network.slb.v1.HealthCheck.HealthCheckType
	62, // 51: sensetime.core.network.slb.v1.HealthCheck.tcp_health_check_config:type_name -> sensetime.core.network.slb.v1.TCPHealthCheckConfig
	63, // 52: sensetime.core.network.slb.v1.HealthCheck.http_health_check_config:type_name -> sensetime.core.network.slb.v1.HTTPHealthCheckConfig
	9,  // 53: sensetime.core.network.slb.v1.ListenerHealthCheckStatus.state:type_name -> sensetime.core.network.slb.v1.ListenerHealthCheckStatus.HealthCheckState
	52, // 54: sensetime.core.network.slb.v1.TargetInfo.target:type_name -> sensetime.core.network.slb.v1.Target
	57, // 55: sensetime.core.network.slb.v1.TargetInfo.status:type_name -> sensetime.core.network.slb.v1.TargetStatus
	58, // 56: sensetime.core.network.slb.v1.TargetStatus.health_check:type_name -> sensetime.core.network.slb.v1.TargetHealthCheckStatus
	10, // 57: sensetime.core.network.slb.v1.TargetHealthCheckStatus.state:type_name -> sensetime.core.network.slb.v1.TargetHealthCheckStatus.State
	11, // 58: sensetime.core.network.slb.v1.ForwardAction.type:type_name -> sensetime.core.network.slb.v1.ForwardAction.ForwardActionType
	60, // 59: sensetime.core.network.slb.v1.ForwardAction.forward_target_group_config:type_name -> sensetime.core.network.slb.v1.ForwardTargetGroupConfig
	67, // 60: sensetime.core.network.slb.v1.ForwardTargetGroupConfig.target_groups:type_name -> sensetime.core.network.slb.v1.TargetGroupInfo
	12, // 61: sensetime.core.network.slb.v1.HTTPHealthCheckConfig.host_type:type_name -> sensetime.core.network.slb.v1.HTTPHealthCheckConfig.HttpHostType
	13, // 62: sensetime.core.network.slb.v1.HTTPHealthCheckConfig.method:type_name -> sensetime.core.network.slb.v1.HTTPHealthCheckConfig.HTTPMethod
	14, // 63: sensetime.core.network.slb.v1.HTTPHealthCheckConfig.version:type_name -> sensetime.core.network.slb.v1.HTTPHealthCheckConfig.HTTPVersion
	15, // 64: sensetime.core.network.slb.v1.HTTPHealthCheckConfig.return_code:type_name -> sensetime.core.network.slb.v1.HTTPHealthCheckConfig.HTTPReturnCode
	46, // 65: sensetime.core.network.slb.v1.ListenerInfo.properties:type_name -> sensetime.core.network.slb.v1.ListenerProperties
	46, // 66: sensetime.core.network.slb.v1.ListenerUpdate.properties:type_name -> sensetime.core.network.slb.v1.ListenerProperties
	50, // 67: sensetime.core.network.slb.v1.TargetGroupUpdate.properties:type_name -> sensetime.core.network.slb.v1.TargetGroupProperties
	53, // 68: sensetime.core.network.slb.v1.TargetUpdate.properties:type_name -> sensetime.core.network.slb.v1.TargetProperties
	16, // 69: sensetime.core.network.slb.v1.SLBDataService.GetSLBStatus:input_type -> sensetime.core.network.slb.v1.GetSLBStatusRequest
	17, // 70: sensetime.core.network.slb.v1.SLBDataService.ListSLBsStatus:input_type -> sensetime.core.network.slb.v1.ListSLBsStatusRequest
	19, // 71: sensetime.core.network.slb.v1.SLBDataService.CreateListener:input_type -> sensetime.core.network.slb.v1.CreateListenerRequest
	20, // 72: sensetime.core.network.slb.v1.SLBDataService.UpdateListener:input_type -> sensetime.core.network.slb.v1.UpdateListenerRequest
	21, // 73: sensetime.core.network.slb.v1.SLBDataService.DeleteListener:input_type -> sensetime.core.network.slb.v1.DeleteListenerRequest
	22, // 74: sensetime.core.network.slb.v1.SLBDataService.ListListeners:input_type -> sensetime.core.network.slb.v1.ListListenersRequest
	24, // 75: sensetime.core.network.slb.v1.SLBDataService.GetListener:input_type -> sensetime.core.network.slb.v1.GetListenerRequest
	25, // 76: sensetime.core.network.slb.v1.SLBDataService.GetListenerStatus:input_type -> sensetime.core.network.slb.v1.GetListenerStatusRequest
	26, // 77: sensetime.core.network.slb.v1.SLBDataService.StartListener:input_type -> sensetime.core.network.slb.v1.StartListenerRequest
	27, // 78: sensetime.core.network.slb.v1.SLBDataService.StopListener:input_type -> sensetime.core.network.slb.v1.StopListenerRequest
	28, // 79: sensetime.core.network.slb.v1.SLBDataService.CreateTargetGroup:input_type -> sensetime.core.network.slb.v1.CreateTargetGroupRequest
	29, // 80: sensetime.core.network.slb.v1.SLBDataService.UpdateTargetGroup:input_type -> sensetime.core.network.slb.v1.UpdateTargetGroupRequest
	30, // 81: sensetime.core.network.slb.v1.SLBDataService.DeleteTargetGroup:input_type -> sensetime.core.network.slb.v1.DeleteTargetGroupRequest
	31, // 82: sensetime.core.network.slb.v1.SLBDataService.ListTargetGroups:input_type -> sensetime.core.network.slb.v1.ListTargetGroupsRequest
	33, // 83: sensetime.core.network.slb.v1.SLBDataService.GetTargetGroup:input_type -> sensetime.core.network.slb.v1.GetTargetGroupRequest
	34, // 84: sensetime.core.network.slb.v1.SLBDataService.GetTargetGroupStatus:input_type -> sensetime.core.network.slb.v1.GetTargetGroupStatusRequest
	35, // 85: sensetime.core.network.slb.v1.SLBDataService.TargetGroupAddTargets:input_type -> sensetime.core.network.slb.v1.TargetGroupAddTargetsRequest
	37, // 86: sensetime.core.network.slb.v1.SLBDataService.TargetGroupRemoveTargets:input_type -> sensetime.core.network.slb.v1.TargetGroupRemoveTargetsRequest
	39, // 87: sensetime.core.network.slb.v1.SLBDataService.TargetsUpdate:input_type -> sensetime.core.network.slb.v1.TargetsUpdateRequest
	41, // 88: sensetime.core.network.slb.v1.SLBDataService.ListTargets:input_type -> sensetime.core.network.slb.v1.ListTargetsRequest
	43, // 89: sensetime.core.network.slb.v1.SLBDataService.GetTarget:input_type -> sensetime.core.network.slb.v1.GetTargetRequest
	44, // 90: sensetime.core.network.slb.v1.SLBDataService.GetSLBStatus:output_type -> sensetime.core.network.slb.v1.SLBStatus
	18, // 91: sensetime.core.network.slb.v1.SLBDataService.ListSLBsStatus:output_type -> sensetime.core.network.slb.v1.ListSLBsStatusResponse
	45, // 92: sensetime.core.network.slb.v1.SLBDataService.CreateListener:output_type -> sensetime.core.network.slb.v1.Listener
	45, // 93: sensetime.core.network.slb.v1.SLBDataService.UpdateListener:output_type -> sensetime.core.network.slb.v1.Listener
	78, // 94: sensetime.core.network.slb.v1.SLBDataService.DeleteListener:output_type -> google.protobuf.Empty
	23, // 95: sensetime.core.network.slb.v1.SLBDataService.ListListeners:output_type -> sensetime.core.network.slb.v1.ListListenersResponse
	45, // 96: sensetime.core.network.slb.v1.SLBDataService.GetListener:output_type -> sensetime.core.network.slb.v1.Listener
	48, // 97: sensetime.core.network.slb.v1.SLBDataService.GetListenerStatus:output_type -> sensetime.core.network.slb.v1.ListenerStatus
	78, // 98: sensetime.core.network.slb.v1.SLBDataService.StartListener:output_type -> google.protobuf.Empty
	78, // 99: sensetime.core.network.slb.v1.SLBDataService.StopListener:output_type -> google.protobuf.Empty
	49, // 100: sensetime.core.network.slb.v1.SLBDataService.CreateTargetGroup:output_type -> sensetime.core.network.slb.v1.TargetGroup
	49, // 101: sensetime.core.network.slb.v1.SLBDataService.UpdateTargetGroup:output_type -> sensetime.core.network.slb.v1.TargetGroup
	78, // 102: sensetime.core.network.slb.v1.SLBDataService.DeleteTargetGroup:output_type -> google.protobuf.Empty
	32, // 103: sensetime.core.network.slb.v1.SLBDataService.ListTargetGroups:output_type -> sensetime.core.network.slb.v1.ListTargetGroupsResponse
	49, // 104: sensetime.core.network.slb.v1.SLBDataService.GetTargetGroup:output_type -> sensetime.core.network.slb.v1.TargetGroup
	51, // 105: sensetime.core.network.slb.v1.SLBDataService.GetTargetGroupStatus:output_type -> sensetime.core.network.slb.v1.TargetGroupStatus
	36, // 106: sensetime.core.network.slb.v1.SLBDataService.TargetGroupAddTargets:output_type -> sensetime.core.network.slb.v1.TargetGroupAddTargetsResponse
	38, // 107: sensetime.core.network.slb.v1.SLBDataService.TargetGroupRemoveTargets:output_type -> sensetime.core.network.slb.v1.TargetGroupRemoveTargetsResponse
	40, // 108: sensetime.core.network.slb.v1.SLBDataService.TargetsUpdate:output_type -> sensetime.core.network.slb.v1.TargetGroupTargets
	42, // 109: sensetime.core.network.slb.v1.SLBDataService.ListTargets:output_type -> sensetime.core.network.slb.v1.ListTargetsResponse
	52, // 110: sensetime.core.network.slb.v1.SLBDataService.GetTarget:output_type -> sensetime.core.network.slb.v1.Target
	90, // [90:111] is the sub-list for method output_type
	69, // [69:90] is the sub-list for method input_type
	69, // [69:69] is the sub-list for extension type_name
	69, // [69:69] is the sub-list for extension extendee
	0,  // [0:69] is the sub-list for field type_name
}

func init() { file_network_slb_v1_slb_data_proto_init() }
func file_network_slb_v1_slb_data_proto_init() {
	if File_network_slb_v1_slb_data_proto != nil {
		return
	}
	file_network_slb_v1_slb_proto_init()
	if !protoimpl.UnsafeEnabled {
		file_network_slb_v1_slb_data_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetSLBStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSLBsStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListSLBsStatusResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateListenerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateListenerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteListenerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListListenersRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListListenersResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetListenerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetListenerStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StartListenerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*StopListenerRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateTargetGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateTargetGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteTargetGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[15].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTargetGroupsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[16].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTargetGroupsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[17].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTargetGroupRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[18].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTargetGroupStatusRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[19].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetGroupAddTargetsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[20].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetGroupAddTargetsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[21].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetGroupRemoveTargetsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[22].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetGroupRemoveTargetsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[23].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetsUpdateRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[24].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetGroupTargets); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[25].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTargetsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[26].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListTargetsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[27].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetTargetRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[28].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*SLBStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[29].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Listener); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[30].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListenerProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[31].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListenerStatusInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[32].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListenerStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[33].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetGroup); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[34].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetGroupProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[35].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetGroupStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[36].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Target); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[37].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[38].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealthCheck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[39].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListenerHealthCheckStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[40].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[41].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[42].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetHealthCheckStatus); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[43].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForwardAction); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[44].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ForwardTargetGroupConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[45].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*OriginalClientIPAddress); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[46].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TCPHealthCheckConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[47].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HTTPHealthCheckConfig); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[48].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VPCInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[49].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*EIPInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[50].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListenerInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[51].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetGroupInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[52].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListenerUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[53].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetGroupUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[54].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*TargetUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_slb_v1_slb_data_proto_msgTypes[55].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CciDeploymentInstanceInfo); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_network_slb_v1_slb_data_proto_msgTypes[52].OneofWrappers = []interface{}{}
	file_network_slb_v1_slb_data_proto_msgTypes[53].OneofWrappers = []interface{}{}
	file_network_slb_v1_slb_data_proto_msgTypes[54].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_slb_v1_slb_data_proto_rawDesc,
			NumEnums:      16,
			NumMessages:   59,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_network_slb_v1_slb_data_proto_goTypes,
		DependencyIndexes: file_network_slb_v1_slb_data_proto_depIdxs,
		EnumInfos:         file_network_slb_v1_slb_data_proto_enumTypes,
		MessageInfos:      file_network_slb_v1_slb_data_proto_msgTypes,
	}.Build()
	File_network_slb_v1_slb_data_proto = out.File
	file_network_slb_v1_slb_data_proto_rawDesc = nil
	file_network_slb_v1_slb_data_proto_goTypes = nil
	file_network_slb_v1_slb_data_proto_depIdxs = nil
}
