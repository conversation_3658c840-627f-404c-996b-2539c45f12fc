// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/slb/v1/slb.proto

package slb

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateSLBRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateSLBRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSLBRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSLBRequestMultiError, or nil if none found.
func (m *CreateSLBRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSLBRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := CreateSLBRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateSLBRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := CreateSLBRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSlb()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSLBRequestValidationError{
					field:  "Slb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSLBRequestValidationError{
					field:  "Slb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSlb()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSLBRequestValidationError{
				field:  "Slb",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateSLBRequestMultiError(errors)
	}

	return nil
}

// CreateSLBRequestMultiError is an error wrapping multiple validation errors
// returned by CreateSLBRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateSLBRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSLBRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSLBRequestMultiError) AllErrors() []error { return m }

// CreateSLBRequestValidationError is the validation error returned by
// CreateSLBRequest.Validate if the designated constraints aren't met.
type CreateSLBRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSLBRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSLBRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSLBRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSLBRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSLBRequestValidationError) ErrorName() string { return "CreateSLBRequestValidationError" }

// Error satisfies the builtin error interface
func (e CreateSLBRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSLBRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSLBRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSLBRequestValidationError{}

var _CreateSLBRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on DeleteSLBRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteSLBRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSLBRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteSLBRequestMultiError, or nil if none found.
func (m *DeleteSLBRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSLBRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := DeleteSLBRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_DeleteSLBRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := DeleteSLBRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return DeleteSLBRequestMultiError(errors)
	}

	return nil
}

// DeleteSLBRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteSLBRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteSLBRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSLBRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSLBRequestMultiError) AllErrors() []error { return m }

// DeleteSLBRequestValidationError is the validation error returned by
// DeleteSLBRequest.Validate if the designated constraints aren't met.
type DeleteSLBRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSLBRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSLBRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSLBRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSLBRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSLBRequestValidationError) ErrorName() string { return "DeleteSLBRequestValidationError" }

// Error satisfies the builtin error interface
func (e DeleteSLBRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSLBRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSLBRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSLBRequestValidationError{}

var _DeleteSLBRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on UpdateSLBRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateSLBRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateSLBRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateSLBRequestMultiError, or nil if none found.
func (m *UpdateSLBRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateSLBRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := UpdateSLBRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UpdateSLBRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := UpdateSLBRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSlb()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSLBRequestValidationError{
					field:  "Slb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSLBRequestValidationError{
					field:  "Slb",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSlb()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSLBRequestValidationError{
				field:  "Slb",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateSLBRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateSLBRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateSLBRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateSLBRequestMultiError(errors)
	}

	return nil
}

// UpdateSLBRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateSLBRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateSLBRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateSLBRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateSLBRequestMultiError) AllErrors() []error { return m }

// UpdateSLBRequestValidationError is the validation error returned by
// UpdateSLBRequest.Validate if the designated constraints aren't met.
type UpdateSLBRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateSLBRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateSLBRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateSLBRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateSLBRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateSLBRequestValidationError) ErrorName() string { return "UpdateSLBRequestValidationError" }

// Error satisfies the builtin error interface
func (e UpdateSLBRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateSLBRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateSLBRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateSLBRequestValidationError{}

var _UpdateSLBRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on GetSLBRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetSLBRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSLBRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetSLBRequestMultiError, or
// nil if none found.
func (m *GetSLBRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSLBRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := GetSLBRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_GetSLBRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := GetSLBRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return GetSLBRequestMultiError(errors)
	}

	return nil
}

// GetSLBRequestMultiError is an error wrapping multiple validation errors
// returned by GetSLBRequest.ValidateAll() if the designated constraints
// aren't met.
type GetSLBRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSLBRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSLBRequestMultiError) AllErrors() []error { return m }

// GetSLBRequestValidationError is the validation error returned by
// GetSLBRequest.Validate if the designated constraints aren't met.
type GetSLBRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSLBRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSLBRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSLBRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSLBRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSLBRequestValidationError) ErrorName() string { return "GetSLBRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetSLBRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSLBRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSLBRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSLBRequestValidationError{}

var _GetSLBRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on ReleaseSLBRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ReleaseSLBRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReleaseSLBRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReleaseSLBRequestMultiError, or nil if none found.
func (m *ReleaseSLBRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReleaseSLBRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSlbName()) > 63 {
		err := ReleaseSLBRequestValidationError{
			field:  "SlbName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ReleaseSLBRequest_SlbName_Pattern.MatchString(m.GetSlbName()) {
		err := ReleaseSLBRequestValidationError{
			field:  "SlbName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ReleaseSLBRequestMultiError(errors)
	}

	return nil
}

// ReleaseSLBRequestMultiError is an error wrapping multiple validation errors
// returned by ReleaseSLBRequest.ValidateAll() if the designated constraints
// aren't met.
type ReleaseSLBRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReleaseSLBRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReleaseSLBRequestMultiError) AllErrors() []error { return m }

// ReleaseSLBRequestValidationError is the validation error returned by
// ReleaseSLBRequest.Validate if the designated constraints aren't met.
type ReleaseSLBRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReleaseSLBRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReleaseSLBRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReleaseSLBRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReleaseSLBRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReleaseSLBRequestValidationError) ErrorName() string {
	return "ReleaseSLBRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReleaseSLBRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReleaseSLBRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReleaseSLBRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReleaseSLBRequestValidationError{}

var _ReleaseSLBRequest_SlbName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on SLB with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *SLB) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SLB with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in SLBMultiError, or nil if none found.
func (m *SLB) ValidateAll() error {
	return m.validate(true)
}

func (m *SLB) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Uid

	// no validation rules for Name

	if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
		err := SLBValidationError{
			field:  "DisplayName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SLB_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
		err := SLBValidationError{
			field:  "DisplayName",
			reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SLBValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SLBValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SLBValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SLBValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SLBValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SLBValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SLBValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SLBValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SLBValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SLBValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SLBValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SLBValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SLBMultiError(errors)
	}

	return nil
}

// SLBMultiError is an error wrapping multiple validation errors returned by
// SLB.ValidateAll() if the designated constraints aren't met.
type SLBMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SLBMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SLBMultiError) AllErrors() []error { return m }

// SLBValidationError is the validation error returned by SLB.Validate if the
// designated constraints aren't met.
type SLBValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SLBValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SLBValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SLBValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SLBValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SLBValidationError) ErrorName() string { return "SLBValidationError" }

// Error satisfies the builtin error interface
func (e SLBValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSLB.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SLBValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SLBValidationError{}

var _SLB_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on SLBProperties with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SLBProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SLBProperties with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SLBPropertiesMultiError, or
// nil if none found.
func (m *SLBProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *SLBProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResources()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SLBPropertiesValidationError{
					field:  "Resources",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SLBPropertiesValidationError{
					field:  "Resources",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResources()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SLBPropertiesValidationError{
				field:  "Resources",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	// no validation rules for VpcId

	// no validation rules for IpVersion

	// no validation rules for EipId

	if len(errors) > 0 {
		return SLBPropertiesMultiError(errors)
	}

	return nil
}

// SLBPropertiesMultiError is an error wrapping multiple validation errors
// returned by SLBProperties.ValidateAll() if the designated constraints
// aren't met.
type SLBPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SLBPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SLBPropertiesMultiError) AllErrors() []error { return m }

// SLBPropertiesValidationError is the validation error returned by
// SLBProperties.Validate if the designated constraints aren't met.
type SLBPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SLBPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SLBPropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SLBPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SLBPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SLBPropertiesValidationError) ErrorName() string { return "SLBPropertiesValidationError" }

// Error satisfies the builtin error interface
func (e SLBPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSLBProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SLBPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SLBPropertiesValidationError{}

// Validate checks the field values on Resources with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Resources) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Resources with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ResourcesMultiError, or nil
// if none found.
func (m *Resources) ValidateAll() error {
	return m.validate(true)
}

func (m *Resources) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetCapacityLimitations()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResourcesValidationError{
					field:  "CapacityLimitations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResourcesValidationError{
					field:  "CapacityLimitations",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCapacityLimitations()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResourcesValidationError{
				field:  "CapacityLimitations",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResourcesMultiError(errors)
	}

	return nil
}

// ResourcesMultiError is an error wrapping multiple validation errors returned
// by Resources.ValidateAll() if the designated constraints aren't met.
type ResourcesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourcesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourcesMultiError) AllErrors() []error { return m }

// ResourcesValidationError is the validation error returned by
// Resources.Validate if the designated constraints aren't met.
type ResourcesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourcesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourcesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourcesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourcesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourcesValidationError) ErrorName() string { return "ResourcesValidationError" }

// Error satisfies the builtin error interface
func (e ResourcesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResources.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourcesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourcesValidationError{}

// Validate checks the field values on CapacityLimitations with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CapacityLimitations) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CapacityLimitations with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CapacityLimitationsMultiError, or nil if none found.
func (m *CapacityLimitations) ValidateAll() error {
	return m.validate(true)
}

func (m *CapacityLimitations) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TcpCps

	// no validation rules for TcpConns

	// no validation rules for UdpCps

	// no validation rules for UdpConns

	// no validation rules for TcpsslCps

	// no validation rules for TcpsslConns

	// no validation rules for HttpCps

	// no validation rules for HttpConns

	// no validation rules for HttpQps

	// no validation rules for HttpsCps

	// no validation rules for HttpsConns

	// no validation rules for HttpsQps

	if len(errors) > 0 {
		return CapacityLimitationsMultiError(errors)
	}

	return nil
}

// CapacityLimitationsMultiError is an error wrapping multiple validation
// errors returned by CapacityLimitations.ValidateAll() if the designated
// constraints aren't met.
type CapacityLimitationsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CapacityLimitationsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CapacityLimitationsMultiError) AllErrors() []error { return m }

// CapacityLimitationsValidationError is the validation error returned by
// CapacityLimitations.Validate if the designated constraints aren't met.
type CapacityLimitationsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CapacityLimitationsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CapacityLimitationsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CapacityLimitationsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CapacityLimitationsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CapacityLimitationsValidationError) ErrorName() string {
	return "CapacityLimitationsValidationError"
}

// Error satisfies the builtin error interface
func (e CapacityLimitationsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCapacityLimitations.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CapacityLimitationsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CapacityLimitationsValidationError{}

// Validate checks the field values on SlbUpdate with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SlbUpdate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SlbUpdate with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SlbUpdateMultiError, or nil
// if none found.
func (m *SlbUpdate) ValidateAll() error {
	return m.validate(true)
}

func (m *SlbUpdate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetResources()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SlbUpdateValidationError{
					field:  "Resources",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SlbUpdateValidationError{
					field:  "Resources",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResources()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SlbUpdateValidationError{
				field:  "Resources",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Type

	// no validation rules for IpVersion

	// no validation rules for EipId

	if m.DisplayName != nil {

		if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
			err := SlbUpdateValidationError{
				field:  "DisplayName",
				reason: "value length must be at most 63 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_SlbUpdate_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
			err := SlbUpdateValidationError{
				field:  "DisplayName",
				reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return SlbUpdateMultiError(errors)
	}

	return nil
}

// SlbUpdateMultiError is an error wrapping multiple validation errors returned
// by SlbUpdate.ValidateAll() if the designated constraints aren't met.
type SlbUpdateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SlbUpdateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SlbUpdateMultiError) AllErrors() []error { return m }

// SlbUpdateValidationError is the validation error returned by
// SlbUpdate.Validate if the designated constraints aren't met.
type SlbUpdateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SlbUpdateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SlbUpdateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SlbUpdateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SlbUpdateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SlbUpdateValidationError) ErrorName() string { return "SlbUpdateValidationError" }

// Error satisfies the builtin error interface
func (e SlbUpdateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSlbUpdate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SlbUpdateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SlbUpdateValidationError{}

var _SlbUpdate_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")
