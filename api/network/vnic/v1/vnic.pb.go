// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/vnic/v1/vnic.proto

package vnic

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/api/annotations"
	v1 "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// [zh] 资源状态.
// [en] Represents the different states of a VNic.
type State int32

const (
	// [zh] 未知状态
	// [en] Unknown status
	State_UNKNOWN State = 0
	// [zh] 已激活, 未分配.
	// [en] The VNic resource has been active but not allocated.
	State_ACTIVE State = 1
	// [zh] 已被使用.
	// [en] TThe VNic resource is being deleted.
	State_RESERVED State = 2
)

// Enum value maps for State.
var (
	State_name = map[int32]string{
		0: "UNKNOWN",
		1: "ACTIVE",
		2: "RESERVED",
	}
	State_value = map[string]int32{
		"UNKNOWN":  0,
		"ACTIVE":   1,
		"RESERVED": 2,
	}
)

func (x State) Enum() *State {
	p := new(State)
	*p = x
	return p
}

func (x State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_vnic_v1_vnic_proto_enumTypes[0].Descriptor()
}

func (State) Type() protoreflect.EnumType {
	return &file_network_vnic_v1_vnic_proto_enumTypes[0]
}

func (x State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use State.Descriptor instead.
func (State) EnumDescriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{0}
}

// [zh] 创建虚拟网卡资源请求.
// [en] CreateVNicRequest.
type CreateVNicRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 虚拟网卡资源名称.
	// [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VnicName string `protobuf:"bytes,4,opt,name=vnic_name,json=vnicName,proto3" json:"vnic_name,omitempty"`
	// [zh] 创建虚拟网卡资源.
	// [en] The VNic resource to create.
	Vnic *VNicCreate `protobuf:"bytes,5,opt,name=vnic,proto3" json:"vnic,omitempty"` // [(validate.rules).message.required = true];
}

func (x *CreateVNicRequest) Reset() {
	*x = CreateVNicRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vnic_v1_vnic_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateVNicRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateVNicRequest) ProtoMessage() {}

func (x *CreateVNicRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vnic_v1_vnic_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateVNicRequest.ProtoReflect.Descriptor instead.
func (*CreateVNicRequest) Descriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{0}
}

func (x *CreateVNicRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *CreateVNicRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *CreateVNicRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateVNicRequest) GetVnicName() string {
	if x != nil {
		return x.VnicName
	}
	return ""
}

func (x *CreateVNicRequest) GetVnic() *VNicCreate {
	if x != nil {
		return x.Vnic
	}
	return nil
}

// [zh] 虚拟网卡资源.
// [en] The VNic resource.
type VNicCreate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源id.
	// [en] The VNic resource id using the form:
	//     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/vnics/{name}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// [zh] vnic 的 subnet 名.
	// [en] The name of the subnet.
	Subnet string `protobuf:"bytes,2,opt,name=subnet,proto3" json:"subnet,omitempty"`
	// [zh] 资源标识.
	// [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] 资源描述.
	// [en] The VNic resource description.
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// [zh] 资源类型.
	// [en] The VNic resource type.
	ResourceType string `protobuf:"bytes,5,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// [zh] 最小库存单元id.
	// [en] SKU id.
	SkuId string `protobuf:"bytes,12,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// [zh] 虚拟网卡资源的标签.
	// [en] Tags attached to the VNic resource.
	Tags map[string]string `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// [zh] 虚拟网卡资源的属性
	// [en] Properties of the VNic resource.
	Properties *VNicProperties `protobuf:"bytes,14,opt,name=properties,proto3" json:"properties,omitempty"` // [(validate.rules).message.required = true]
	// [zh] 订单信息.
	// [en] Order information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,15,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"` // [(validate.rules).message.required = true]
}

func (x *VNicCreate) Reset() {
	*x = VNicCreate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vnic_v1_vnic_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VNicCreate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VNicCreate) ProtoMessage() {}

func (x *VNicCreate) ProtoReflect() protoreflect.Message {
	mi := &file_network_vnic_v1_vnic_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VNicCreate.ProtoReflect.Descriptor instead.
func (*VNicCreate) Descriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{1}
}

func (x *VNicCreate) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VNicCreate) GetSubnet() string {
	if x != nil {
		return x.Subnet
	}
	return ""
}

func (x *VNicCreate) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VNicCreate) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *VNicCreate) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *VNicCreate) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *VNicCreate) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *VNicCreate) GetProperties() *VNicProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *VNicCreate) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

// [zh] 删除虚拟网卡资源请求.
// [en] DeleteVNicRequest.
type DeleteVNicRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] vnic 的 subnet 名.
	// [en] The name of the subnet.
	Subnet string `protobuf:"bytes,4,opt,name=subnet,proto3" json:"subnet,omitempty"`
	// [zh] 虚拟网卡资源名称.
	// [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VnicName string `protobuf:"bytes,5,opt,name=vnic_name,json=vnicName,proto3" json:"vnic_name,omitempty"`
}

func (x *DeleteVNicRequest) Reset() {
	*x = DeleteVNicRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vnic_v1_vnic_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteVNicRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteVNicRequest) ProtoMessage() {}

func (x *DeleteVNicRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vnic_v1_vnic_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteVNicRequest.ProtoReflect.Descriptor instead.
func (*DeleteVNicRequest) Descriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{2}
}

func (x *DeleteVNicRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DeleteVNicRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *DeleteVNicRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteVNicRequest) GetSubnet() string {
	if x != nil {
		return x.Subnet
	}
	return ""
}

func (x *DeleteVNicRequest) GetVnicName() string {
	if x != nil {
		return x.VnicName
	}
	return ""
}

// [zh] 更新虚拟网卡请求.
// [en] Update VNic request.
type UpdateVNicRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] The resource subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] The resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] The resource available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 资源名称.
	// [en] The resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VnicName string `protobuf:"bytes,4,opt,name=vnic_name,json=vnicName,proto3" json:"vnic_name,omitempty"`
	// [zh] 更新资源.
	// [en] The resource to update.
	Vnic *VNicUpdate `protobuf:"bytes,5,opt,name=vnic,proto3" json:"vnic,omitempty"`
	// [zh] 更新标记.
	// [en] The resource update mask.
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,6,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateVNicRequest) Reset() {
	*x = UpdateVNicRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vnic_v1_vnic_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateVNicRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateVNicRequest) ProtoMessage() {}

func (x *UpdateVNicRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vnic_v1_vnic_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateVNicRequest.ProtoReflect.Descriptor instead.
func (*UpdateVNicRequest) Descriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{3}
}

func (x *UpdateVNicRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *UpdateVNicRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *UpdateVNicRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateVNicRequest) GetVnicName() string {
	if x != nil {
		return x.VnicName
	}
	return ""
}

func (x *UpdateVNicRequest) GetVnic() *VNicUpdate {
	if x != nil {
		return x.Vnic
	}
	return nil
}

func (x *UpdateVNicRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// [zh] 更新虚拟网卡资源.
// [en] Update VNic body.
type VNicUpdate struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源名称.
	// [en] The resource display name with the restriction: `^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]?([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_]{0,62})$`.
	DisplayName string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// [zh] 资源描述.
	// [en] The resource Description.
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
}

func (x *VNicUpdate) Reset() {
	*x = VNicUpdate{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vnic_v1_vnic_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VNicUpdate) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VNicUpdate) ProtoMessage() {}

func (x *VNicUpdate) ProtoReflect() protoreflect.Message {
	mi := &file_network_vnic_v1_vnic_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VNicUpdate.ProtoReflect.Descriptor instead.
func (*VNicUpdate) Descriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{4}
}

func (x *VNicUpdate) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *VNicUpdate) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

// [zh] 查看虚拟网卡资源详情请求.
// [en] GetVNicRequest.
type GetVNicRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 子网名.
	// [en] The name of the subnet.
	Subnet string `protobuf:"bytes,4,opt,name=subnet,proto3" json:"subnet,omitempty"`
	// [zh] 虚拟网卡资源名称.
	// [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VnicName string `protobuf:"bytes,5,opt,name=vnic_name,json=vnicName,proto3" json:"vnic_name,omitempty"`
}

func (x *GetVNicRequest) Reset() {
	*x = GetVNicRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vnic_v1_vnic_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetVNicRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetVNicRequest) ProtoMessage() {}

func (x *GetVNicRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vnic_v1_vnic_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetVNicRequest.ProtoReflect.Descriptor instead.
func (*GetVNicRequest) Descriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{5}
}

func (x *GetVNicRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetVNicRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetVNicRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetVNicRequest) GetSubnet() string {
	if x != nil {
		return x.Subnet
	}
	return ""
}

func (x *GetVNicRequest) GetVnicName() string {
	if x != nil {
		return x.VnicName
	}
	return ""
}

// 列举 VNIC 的请求.
// [EN] Request to list VNICs.
type ListVNicRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 子网名.
	// [en] The name of the subnet.
	SubnetName string `protobuf:"bytes,4,opt,name=subnet_name,json=subnetName,proto3" json:"subnet_name,omitempty"`
}

func (x *ListVNicRequest) Reset() {
	*x = ListVNicRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vnic_v1_vnic_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListVNicRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVNicRequest) ProtoMessage() {}

func (x *ListVNicRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vnic_v1_vnic_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVNicRequest.ProtoReflect.Descriptor instead.
func (*ListVNicRequest) Descriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{6}
}

func (x *ListVNicRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListVNicRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListVNicRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListVNicRequest) GetSubnetName() string {
	if x != nil {
		return x.SubnetName
	}
	return ""
}

// 列举 VNIC 的响应.
// [EN] Response to list VNICs.
type ListVNicsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// VPC 列表.
	// [EN] VPC list.
	Vnics []*VNic `protobuf:"bytes,1,rep,name=vnics,proto3" json:"vnics,omitempty"`
	// total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListVNicsResponse) Reset() {
	*x = ListVNicsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vnic_v1_vnic_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListVNicsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListVNicsResponse) ProtoMessage() {}

func (x *ListVNicsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_vnic_v1_vnic_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListVNicsResponse.ProtoReflect.Descriptor instead.
func (*ListVNicsResponse) Descriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{7}
}

func (x *ListVNicsResponse) GetVnics() []*VNic {
	if x != nil {
		return x.Vnics
	}
	return nil
}

func (x *ListVNicsResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// [zh] ResizeVNicRequest.
// [en] 虚拟网卡资源变更请求.
type ResizeVNicRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅名.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 虚拟网卡资源名称.
	// [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VnicName string `protobuf:"bytes,4,opt,name=vnic_name,json=vnicName,proto3" json:"vnic_name,omitempty"`
	// [zh] 变更资源包body参数.
	// [en] Resize VNic resource.
	VnicResize *VNicResize `protobuf:"bytes,5,opt,name=vnic_resize,json=vnicResize,proto3" json:"vnic_resize,omitempty"`
}

func (x *ResizeVNicRequest) Reset() {
	*x = ResizeVNicRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vnic_v1_vnic_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ResizeVNicRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ResizeVNicRequest) ProtoMessage() {}

func (x *ResizeVNicRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vnic_v1_vnic_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ResizeVNicRequest.ProtoReflect.Descriptor instead.
func (*ResizeVNicRequest) Descriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{8}
}

func (x *ResizeVNicRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ResizeVNicRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ResizeVNicRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ResizeVNicRequest) GetVnicName() string {
	if x != nil {
		return x.VnicName
	}
	return ""
}

func (x *ResizeVNicRequest) GetVnicResize() *VNicResize {
	if x != nil {
		return x.VnicResize
	}
	return nil
}

// [zh] 变更虚拟网卡资源.
// [en] Resize VNic resource.
type VNicResize struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 虚拟网卡资源uuid.
	// [en] VNic resource uuid.
	ResourceId string `protobuf:"bytes,1,opt,name=resource_id,json=resourceId,proto3" json:"resource_id,omitempty"`
	// [zh] 虚拟网卡资源sku_id.
	// [en] VNic resource sku_id.
	SkuId string `protobuf:"bytes,2,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// [zh] 变更操作者id.
	// [en] operator id.
	OperatorId string `protobuf:"bytes,3,opt,name=operator_id,json=operatorId,proto3" json:"operator_id,omitempty"`
	// [zh] 虚拟网卡资源的属性
	// [en] Properties of the VNic resource.
	Properties *VNicProperties `protobuf:"bytes,14,opt,name=properties,proto3" json:"properties,omitempty"` // [(validate.rules).message.required = true];
	// [zh] 订单信息.
	// [en] Order information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,5,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"` // [(validate.rules).message.required = true];
}

func (x *VNicResize) Reset() {
	*x = VNicResize{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vnic_v1_vnic_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VNicResize) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VNicResize) ProtoMessage() {}

func (x *VNicResize) ProtoReflect() protoreflect.Message {
	mi := &file_network_vnic_v1_vnic_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VNicResize.ProtoReflect.Descriptor instead.
func (*VNicResize) Descriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{9}
}

func (x *VNicResize) GetResourceId() string {
	if x != nil {
		return x.ResourceId
	}
	return ""
}

func (x *VNicResize) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *VNicResize) GetOperatorId() string {
	if x != nil {
		return x.OperatorId
	}
	return ""
}

func (x *VNicResize) GetProperties() *VNicProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *VNicResize) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

// [zh] 释放虚拟网卡资源.
// [en] Release VNic resource.
type ReleaseVNicRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 订阅名.
	// [en] Subscription.
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 资源组.
	// [en] Resource group.
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 虚拟网卡资源名称.
	// [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	VnicName string `protobuf:"bytes,4,opt,name=vnic_name,json=vnicName,proto3" json:"vnic_name,omitempty"`
}

func (x *ReleaseVNicRequest) Reset() {
	*x = ReleaseVNicRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vnic_v1_vnic_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ReleaseVNicRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ReleaseVNicRequest) ProtoMessage() {}

func (x *ReleaseVNicRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_vnic_v1_vnic_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ReleaseVNicRequest.ProtoReflect.Descriptor instead.
func (*ReleaseVNicRequest) Descriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{10}
}

func (x *ReleaseVNicRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ReleaseVNicRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ReleaseVNicRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ReleaseVNicRequest) GetVnicName() string {
	if x != nil {
		return x.VnicName
	}
	return ""
}

// [zh] 虚拟网卡资源.
// [en] The VNic resource.
type VNic struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源id.
	// [en] The VNic resource id using the form:
	//     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/vnics/{name}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// [zh] 资源的uuid.
	// [en] The VNic resource uuid.
	Uid string `protobuf:"bytes,2,opt,name=uid,proto3" json:"uid,omitempty"`
	// [zh] 资源标识.
	// [en] The VNic resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// [zh] 资源名称.
	// [en] The VNic resource display name
	DisplayName string `protobuf:"bytes,4,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// [zh] 资源描述.
	// [en] The VNic resource description.
	Description string `protobuf:"bytes,5,opt,name=description,proto3" json:"description,omitempty"`
	// [zh] 资源类型.
	// [en] The VNic resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// [zh] 创建者id.
	// [en] The id of the user who created the VNic resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// [zh] 拥有者id.
	// [en] The id of the user who owns the VNic resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// [zh] 租户id
	// [en] Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// [zh] 可用区.
	// [en] Available zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// [zh] 资源状态.
	// [en] The current state of the VNic resource.
	State State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.vnic.v1.State" json:"state,omitempty"`
	// [zh] 最小库存单元id.
	// [en] SKU id.
	SkuId string `protobuf:"bytes,12,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// [zh] 虚拟网卡资源的标签.
	// [en] Tags attached to the VirtualMachine resource.
	Tags map[string]string `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// [zh] 虚拟网卡资源的属性
	// [en] Properties of the VirtualMachine resource.
	Properties *VNicProperties `protobuf:"bytes,14,opt,name=properties,proto3" json:"properties,omitempty"` // [(validate.rules).message.required = true];
	// [zh] 订单信息.
	// [en] Order information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,15,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"` // [(validate.rules).message.required = true];
	// [zh] 资源是否已删除.
	// [en] Indicates whether the VNic resource is deleted or not.
	Deleted bool `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// [zh] 资源创建时间.
	// [en] The time when the VNic resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// [zh] 资源更新时间.
	// [en] The time when the VNic resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *VNic) Reset() {
	*x = VNic{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vnic_v1_vnic_proto_msgTypes[11]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VNic) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VNic) ProtoMessage() {}

func (x *VNic) ProtoReflect() protoreflect.Message {
	mi := &file_network_vnic_v1_vnic_proto_msgTypes[11]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VNic.ProtoReflect.Descriptor instead.
func (*VNic) Descriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{11}
}

func (x *VNic) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *VNic) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *VNic) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VNic) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *VNic) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *VNic) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *VNic) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *VNic) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *VNic) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *VNic) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *VNic) GetState() State {
	if x != nil {
		return x.State
	}
	return State_UNKNOWN
}

func (x *VNic) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *VNic) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *VNic) GetProperties() *VNicProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *VNic) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

func (x *VNic) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *VNic) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *VNic) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// [zh] 资源属性.
// [en] The VNicProperties.
type VNicProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 资源规格属性.
	// [en] Resource Specification Properties.
	Resources *Resources `protobuf:"bytes,1,opt,name=resources,proto3" json:"resources,omitempty"`
	// ID of this VNic
	VnicId string `protobuf:"bytes,2,opt,name=vnic_id,json=vnicId,proto3" json:"vnic_id,omitempty"`
	// The VIP resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,3,opt,name=name,proto3" json:"name,omitempty"`
	// The IPv4 address
	IpAddrV4 string `protobuf:"bytes,4,opt,name=ip_addr_v4,json=ipAddrV4,proto3" json:"ip_addr_v4,omitempty"`
	// The Mac address
	MacAddr string `protobuf:"bytes,5,opt,name=mac_addr,json=macAddr,proto3" json:"mac_addr,omitempty"`
	// The name of vnic subnet
	Subnet string `protobuf:"bytes,6,opt,name=subnet,proto3" json:"subnet,omitempty"`
	// The name of vip vpc
	Vpc string `protobuf:"bytes,7,opt,name=vpc,proto3" json:"vpc,omitempty"`
}

func (x *VNicProperties) Reset() {
	*x = VNicProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vnic_v1_vnic_proto_msgTypes[12]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *VNicProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*VNicProperties) ProtoMessage() {}

func (x *VNicProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_vnic_v1_vnic_proto_msgTypes[12]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use VNicProperties.ProtoReflect.Descriptor instead.
func (*VNicProperties) Descriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{12}
}

func (x *VNicProperties) GetResources() *Resources {
	if x != nil {
		return x.Resources
	}
	return nil
}

func (x *VNicProperties) GetVnicId() string {
	if x != nil {
		return x.VnicId
	}
	return ""
}

func (x *VNicProperties) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *VNicProperties) GetIpAddrV4() string {
	if x != nil {
		return x.IpAddrV4
	}
	return ""
}

func (x *VNicProperties) GetMacAddr() string {
	if x != nil {
		return x.MacAddr
	}
	return ""
}

func (x *VNicProperties) GetSubnet() string {
	if x != nil {
		return x.Subnet
	}
	return ""
}

func (x *VNicProperties) GetVpc() string {
	if x != nil {
		return x.Vpc
	}
	return ""
}

// [zh] 资源规格属性
// [en] Resource Specification Properties.
type Resources struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 单独购买的计费项
	// [en] Billing Items Purchased Separately.
	BillingItems *BillingItems `protobuf:"bytes,4,opt,name=billing_items,json=billingItems,proto3" json:"billing_items,omitempty"`
}

func (x *Resources) Reset() {
	*x = Resources{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vnic_v1_vnic_proto_msgTypes[13]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *Resources) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*Resources) ProtoMessage() {}

func (x *Resources) ProtoReflect() protoreflect.Message {
	mi := &file_network_vnic_v1_vnic_proto_msgTypes[13]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use Resources.ProtoReflect.Descriptor instead.
func (*Resources) Descriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{13}
}

func (x *Resources) GetBillingItems() *BillingItems {
	if x != nil {
		return x.BillingItems
	}
	return nil
}

// [zh] 计费项
// [en] The BillingItems in VNicProperties Resources.
type BillingItems struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields
}

func (x *BillingItems) Reset() {
	*x = BillingItems{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_vnic_v1_vnic_proto_msgTypes[14]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BillingItems) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BillingItems) ProtoMessage() {}

func (x *BillingItems) ProtoReflect() protoreflect.Message {
	mi := &file_network_vnic_v1_vnic_proto_msgTypes[14]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BillingItems.ProtoReflect.Descriptor instead.
func (*BillingItems) Descriptor() ([]byte, []int) {
	return file_network_vnic_v1_vnic_proto_rawDescGZIP(), []int{14}
}

var File_network_vnic_v1_vnic_proto protoreflect.FileDescriptor

var file_network_vnic_v1_vnic_proto_rawDesc = []byte{
	0x0a, 0x1a, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x2f, 0x76,
	0x31, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x62, 0x65, 0x68,
	0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70,
	0x74, 0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61,
	0x74, 0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62,
	0x75, 0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x1a, 0x1b, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e,
	0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1f, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f,
	0x76, 0x31, 0x2f, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x8d, 0x02, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x4e, 0x69,
	0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x47, 0x0a, 0x09, 0x76, 0x6e, 0x69,
	0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42,
	0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x08, 0x76, 0x6e, 0x69, 0x63, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x3e, 0x0a, 0x04, 0x76, 0x6e, 0x69, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b,
	0x32, 0x2a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x56, 0x4e, 0x69, 0x63, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x52, 0x04, 0x76, 0x6e,
	0x69, 0x63, 0x22, 0xd2, 0x03, 0x0a, 0x0a, 0x56, 0x4e, 0x69, 0x63, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x1b, 0x0a, 0x06, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x06, 0x73, 0x75, 0x62,
	0x6e, 0x65, 0x74, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b,
	0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x23,
	0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54,
	0x79, 0x70, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x48, 0x0a, 0x04, 0x74, 0x61,
	0x67, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x34, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x4e, 0x69, 0x63, 0x43, 0x72,
	0x65, 0x61, 0x74, 0x65, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04,
	0x74, 0x61, 0x67, 0x73, 0x12, 0x4e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x4e, 0x69, 0x63, 0x50, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x6e,
	0x66, 0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2e,
	0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x1a, 0x37,
	0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b,
	0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61,
	0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0xd2, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x56, 0x4e, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a,
	0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12,
	0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1b, 0x0a,
	0x06, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x06, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x12, 0x20, 0x0a, 0x09, 0x76, 0x6e,
	0x69, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x08, 0x76, 0x6e, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xe1, 0x02, 0x0a,
	0x11, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x4e, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x12, 0x4a, 0x0a, 0x09, 0x76, 0x6e, 0x69, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18,
	0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x5d, 0x29, 0x3f, 0x24, 0x52, 0x08, 0x76, 0x6e, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x3e,
	0x0a, 0x04, 0x76, 0x6e, 0x69, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x4e,
	0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x52, 0x04, 0x76, 0x6e, 0x69, 0x63, 0x12, 0x40,
	0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x06, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69, 0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x42,
	0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d, 0x61, 0x73, 0x6b,
	0x22, 0xa6, 0x01, 0x0a, 0x0a, 0x56, 0x4e, 0x69, 0x63, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12,
	0x76, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x53, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x4d, 0x72, 0x4b, 0x18,
	0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78,
	0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d,
	0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65,
	0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b,
	0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70,
	0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x22, 0xcf, 0x01, 0x0a, 0x0e, 0x47, 0x65,
	0x74, 0x56, 0x4e, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33,
	0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02,
	0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1b, 0x0a, 0x06,
	0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x06, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x12, 0x20, 0x0a, 0x09, 0x76, 0x6e, 0x69,
	0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x08, 0x76, 0x6e, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xb7, 0x01, 0x0a, 0x0f,
	0x4c, 0x69, 0x73, 0x74, 0x56, 0x4e, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03,
	0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12,
	0x24, 0x0a, 0x0b, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x73, 0x75, 0x62, 0x6e, 0x65,
	0x74, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x6e, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x4e, 0x69,
	0x63, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x3a, 0x0a, 0x05, 0x76, 0x6e,
	0x69, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x24, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x4e, 0x69, 0x63, 0x52,
	0x05, 0x76, 0x6e, 0x69, 0x63, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61,
	0x6c, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xb1, 0x02, 0x0a, 0x11, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65,
	0x56, 0x4e, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x30, 0x0a, 0x11, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x10, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a,
	0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52,
	0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x4a, 0x0a, 0x09, 0x76,
	0x6e, 0x69, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2d,
	0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d,
	0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36,
	0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x08, 0x76,
	0x6e, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x50, 0x0a, 0x0b, 0x76, 0x6e, 0x69, 0x63, 0x5f,
	0x72, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2a, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x4e,
	0x69, 0x63, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x76,
	0x6e, 0x69, 0x63, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x22, 0x8e, 0x02, 0x0a, 0x0a, 0x56, 0x4e,
	0x69, 0x63, 0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x12, 0x24, 0x0a, 0x0b, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x02, 0x52, 0x0a, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x49, 0x64, 0x12, 0x1a,
	0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03,
	0xe0, 0x41, 0x02, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x24, 0x0a, 0x0b, 0x6f, 0x70,
	0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42,
	0x03, 0xe0, 0x41, 0x02, 0x52, 0x0a, 0x6f, 0x70, 0x65, 0x72, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64,
	0x12, 0x4e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0e,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e,
	0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x4e, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72,
	0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x05,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d,
	0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52,
	0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x22, 0xe0, 0x01, 0x0a, 0x12, 0x52,
	0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x56, 0x4e, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41,
	0x02, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x12, 0x4a, 0x0a, 0x09, 0x76, 0x6e, 0x69, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x42, 0x2d, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x3f,
	0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d,
	0x29, 0x3f, 0x24, 0x52, 0x08, 0x76, 0x6e, 0x69, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x9e, 0x07,
	0x0a, 0x04, 0x56, 0x4e, 0x69, 0x63, 0x12, 0x13, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x02, 0x69, 0x64, 0x12, 0x15, 0x0a, 0x03, 0x75,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x12, 0x17, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x73, 0x0a, 0x0c, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x50, 0xe0, 0x41, 0x02, 0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e,
	0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30,
	0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d,
	0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32,
	0x7d, 0x29, 0x24, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x22, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03,
	0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x1e, 0x0a, 0x08, 0x6f,
	0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x03, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x20, 0x0a, 0x09, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03,
	0xe0, 0x41, 0x03, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x17, 0x0a,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03,
	0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x40, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18,
	0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x25, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76,
	0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x42, 0x03, 0xe0, 0x41,
	0x03, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x12, 0x1a, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f,
	0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x02, 0x52, 0x05, 0x73,
	0x6b, 0x75, 0x49, 0x64, 0x12, 0x42, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73, 0x18, 0x0d, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x4e, 0x69, 0x63, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x4e, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x4e,
	0x69, 0x63, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x68, 0x69,
	0x67, 0x67, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x1d, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x08, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65,
	0x64, 0x12, 0x40, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65,
	0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61,
	0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54,
	0x69, 0x6d, 0x65, 0x12, 0x40, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69,
	0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73,
	0x74, 0x61, 0x6d, 0x70, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74,
	0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x95,
	0x02, 0x0a, 0x0e, 0x56, 0x4e, 0x69, 0x63, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65,
	0x73, 0x12, 0x47, 0x0a, 0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e,
	0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x52,
	0x09, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x73, 0x12, 0x17, 0x0a, 0x07, 0x76, 0x6e,
	0x69, 0x63, 0x5f, 0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x76, 0x6e, 0x69,
	0x63, 0x49, 0x64, 0x12, 0x3e, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x2a, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18, 0x20, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d,
	0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x33,
	0x30, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x04, 0x6e,
	0x61, 0x6d, 0x65, 0x12, 0x1c, 0x0a, 0x0a, 0x69, 0x70, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x5f, 0x76,
	0x34, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x69, 0x70, 0x41, 0x64, 0x64, 0x72, 0x56,
	0x34, 0x12, 0x19, 0x0a, 0x08, 0x6d, 0x61, 0x63, 0x5f, 0x61, 0x64, 0x64, 0x72, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6d, 0x61, 0x63, 0x41, 0x64, 0x64, 0x72, 0x12, 0x16, 0x0a, 0x06,
	0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x73, 0x75,
	0x62, 0x6e, 0x65, 0x74, 0x12, 0x10, 0x0a, 0x03, 0x76, 0x70, 0x63, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x76, 0x70, 0x63, 0x22, 0x5e, 0x0a, 0x09, 0x52, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x73, 0x12, 0x51, 0x0a, 0x0d, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e, 0x67, 0x5f, 0x69,
	0x74, 0x65, 0x6d, 0x73, 0x18, 0x04, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x6c, 0x6c,
	0x69, 0x6e, 0x67, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x52, 0x0c, 0x62, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x22, 0x0e, 0x0a, 0x0c, 0x42, 0x69, 0x6c, 0x6c, 0x69, 0x6e,
	0x67, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x2a, 0x2e, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12,
	0x0b, 0x0a, 0x07, 0x55, 0x4e, 0x4b, 0x4e, 0x4f, 0x57, 0x4e, 0x10, 0x00, 0x12, 0x0a, 0x0a, 0x06,
	0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x01, 0x12, 0x0c, 0x0a, 0x08, 0x52, 0x45, 0x53, 0x45,
	0x52, 0x56, 0x45, 0x44, 0x10, 0x02, 0x32, 0xd1, 0x14, 0x0a, 0x05, 0x56, 0x4e, 0x69, 0x63, 0x73,
	0x12, 0xf8, 0x02, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x4e, 0x69, 0x63, 0x12,
	0x31, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x56, 0x4e, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x56, 0x4e, 0x69, 0x63, 0x22, 0x90, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x83, 0x01, 0x22, 0x7b, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x6e, 0x69,
	0x63, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x6e,
	0x69, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x6e, 0x69, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a,
	0x04, 0x76, 0x6e, 0x69, 0x63, 0x9a, 0xb5, 0x18, 0x81, 0x01, 0x0a, 0x69, 0x2f, 0x72, 0x6d, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f,
	0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x6e, 0x69, 0x63, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x14, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x69, 0x6e, 0x73, 0x74,
	0x61, 0x6e, 0x63, 0x65, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0xe3, 0x02, 0x0a, 0x0a,
	0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x56, 0x4e, 0x69, 0x63, 0x12, 0x31, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x56, 0x4e, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x89, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7d, 0x2a, 0x7b,
	0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x2f, 0x64, 0x61,
	0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e,
	0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x73, 0x2f,
	0x7b, 0x76, 0x6e, 0x69, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x9a, 0xb5, 0x18, 0x81, 0x01,
	0x0a, 0x69, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e,
	0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x73, 0x2f,
	0x7b, 0x76, 0x6e, 0x69, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x14, 0x76, 0x6e, 0x69,
	0x63, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x12, 0xf8, 0x02, 0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x4e, 0x69, 0x63,
	0x12, 0x31, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x56, 0x4e, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e, 0x69,
	0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x4e, 0x69, 0x63, 0x22, 0x90, 0x02, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x83, 0x01, 0x32, 0x7b, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x6e,
	0x69, 0x63, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76,
	0x6e, 0x69, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x6e, 0x69, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x3a, 0x04, 0x76, 0x6e, 0x69, 0x63, 0x9a, 0xb5, 0x18, 0x81, 0x01, 0x0a, 0x69, 0x2f, 0x72, 0x6d,
	0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a,
	0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x6e, 0x69, 0x63,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x14, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x69, 0x6e, 0x73,
	0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xe7, 0x02, 0x0a,
	0x07, 0x47, 0x65, 0x74, 0x56, 0x4e, 0x69, 0x63, 0x12, 0x2e, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x56, 0x4e, 0x69,
	0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65,
	0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56, 0x4e, 0x69, 0x63, 0x22, 0x85,
	0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7d, 0x12, 0x7b, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f,
	0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e,
	0x65, 0x7d, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x6e, 0x69, 0x63, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x9a, 0xb5, 0x18, 0x7e, 0x0a, 0x69, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x6e, 0x69, 0x63, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x12, 0x11, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e,
	0x63, 0x65, 0x2e, 0x67, 0x65, 0x74, 0x12, 0x82, 0x03, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x56,
	0x4e, 0x69, 0x63, 0x73, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e,
	0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x4e, 0x69, 0x63, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x31, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76,
	0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x56, 0x4e, 0x69, 0x63, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x90, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x81, 0x01, 0x12, 0x7f, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x6e, 0x69,
	0x63, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x73, 0x75,
	0x62, 0x6e, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x9a, 0xb5, 0x18, 0x83, 0x01, 0x0a, 0x6d, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x6e, 0x65,
	0x74, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x12, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x69, 0x6e,
	0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x12, 0x87, 0x03, 0x0a, 0x0a,
	0x52, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x56, 0x4e, 0x69, 0x63, 0x12, 0x31, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x73, 0x69,
	0x7a, 0x65, 0x56, 0x4e, 0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x24, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x56,
	0x4e, 0x69, 0x63, 0x22, 0x9f, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x92, 0x01, 0x22, 0x82, 0x01,
	0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x2f, 0x64, 0x61,
	0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e,
	0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x73, 0x2f,
	0x7b, 0x76, 0x6e, 0x69, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x72, 0x65, 0x73, 0x69,
	0x7a, 0x65, 0x3a, 0x0b, 0x76, 0x6e, 0x69, 0x63, 0x5f, 0x72, 0x65, 0x73, 0x69, 0x7a, 0x65, 0x9a,
	0xb5, 0x18, 0x81, 0x01, 0x0a, 0x69, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x6e,
	0x69, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x6e, 0x69, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12,
	0x14, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61, 0x6e, 0x63, 0x65, 0x2e, 0x75,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xf2, 0x02, 0x0a, 0x0b, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x56, 0x4e, 0x69, 0x63, 0x12, 0x32, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x76,
	0x6e, 0x69, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x52, 0x65, 0x6c, 0x65, 0x61, 0x73, 0x65, 0x56, 0x4e,
	0x69, 0x63, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74,
	0x79, 0x22, 0x96, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x89, 0x01, 0x22, 0x83, 0x01, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x2f, 0x64, 0x61, 0x74, 0x61,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73,
	0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x73, 0x2f, 0x7b, 0x76,
	0x6e, 0x69, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x72, 0x65, 0x6c, 0x65, 0x61, 0x73,
	0x65, 0x3a, 0x01, 0x2a, 0x9a, 0xb5, 0x18, 0x81, 0x01, 0x0a, 0x69, 0x2f, 0x72, 0x6d, 0x2f, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f,
	0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e,
	0x65, 0x7d, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x73, 0x2f, 0x7b, 0x76, 0x6e, 0x69, 0x63, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x12, 0x14, 0x76, 0x6e, 0x69, 0x63, 0x2e, 0x69, 0x6e, 0x73, 0x74, 0x61,
	0x6e, 0x63, 0x65, 0x2e, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x42, 0x52, 0x5a, 0x50, 0x67, 0x69,
	0x74, 0x6c, 0x61, 0x62, 0x2e, 0x62, 0x6a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x72, 0x79,
	0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2d, 0x70, 0x72, 0x6f,
	0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2f, 0x76, 0x6e, 0x69, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x76, 0x6e, 0x69, 0x63, 0x62, 0x06,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_network_vnic_v1_vnic_proto_rawDescOnce sync.Once
	file_network_vnic_v1_vnic_proto_rawDescData = file_network_vnic_v1_vnic_proto_rawDesc
)

func file_network_vnic_v1_vnic_proto_rawDescGZIP() []byte {
	file_network_vnic_v1_vnic_proto_rawDescOnce.Do(func() {
		file_network_vnic_v1_vnic_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_vnic_v1_vnic_proto_rawDescData)
	})
	return file_network_vnic_v1_vnic_proto_rawDescData
}

var file_network_vnic_v1_vnic_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_network_vnic_v1_vnic_proto_msgTypes = make([]protoimpl.MessageInfo, 17)
var file_network_vnic_v1_vnic_proto_goTypes = []interface{}{
	(State)(0),                    // 0: sensetime.core.network.vnic.v1.State
	(*CreateVNicRequest)(nil),     // 1: sensetime.core.network.vnic.v1.CreateVNicRequest
	(*VNicCreate)(nil),            // 2: sensetime.core.network.vnic.v1.VNicCreate
	(*DeleteVNicRequest)(nil),     // 3: sensetime.core.network.vnic.v1.DeleteVNicRequest
	(*UpdateVNicRequest)(nil),     // 4: sensetime.core.network.vnic.v1.UpdateVNicRequest
	(*VNicUpdate)(nil),            // 5: sensetime.core.network.vnic.v1.VNicUpdate
	(*GetVNicRequest)(nil),        // 6: sensetime.core.network.vnic.v1.GetVNicRequest
	(*ListVNicRequest)(nil),       // 7: sensetime.core.network.vnic.v1.ListVNicRequest
	(*ListVNicsResponse)(nil),     // 8: sensetime.core.network.vnic.v1.ListVNicsResponse
	(*ResizeVNicRequest)(nil),     // 9: sensetime.core.network.vnic.v1.ResizeVNicRequest
	(*VNicResize)(nil),            // 10: sensetime.core.network.vnic.v1.VNicResize
	(*ReleaseVNicRequest)(nil),    // 11: sensetime.core.network.vnic.v1.ReleaseVNicRequest
	(*VNic)(nil),                  // 12: sensetime.core.network.vnic.v1.VNic
	(*VNicProperties)(nil),        // 13: sensetime.core.network.vnic.v1.VNicProperties
	(*Resources)(nil),             // 14: sensetime.core.network.vnic.v1.Resources
	(*BillingItems)(nil),          // 15: sensetime.core.network.vnic.v1.BillingItems
	nil,                           // 16: sensetime.core.network.vnic.v1.VNicCreate.TagsEntry
	nil,                           // 17: sensetime.core.network.vnic.v1.VNic.TagsEntry
	(*v1.OrderInfo)(nil),          // 18: sensetime.core.higgs.common.v1.OrderInfo
	(*fieldmaskpb.FieldMask)(nil), // 19: google.protobuf.FieldMask
	(*timestamppb.Timestamp)(nil), // 20: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),         // 21: google.protobuf.Empty
}
var file_network_vnic_v1_vnic_proto_depIdxs = []int32{
	2,  // 0: sensetime.core.network.vnic.v1.CreateVNicRequest.vnic:type_name -> sensetime.core.network.vnic.v1.VNicCreate
	16, // 1: sensetime.core.network.vnic.v1.VNicCreate.tags:type_name -> sensetime.core.network.vnic.v1.VNicCreate.TagsEntry
	13, // 2: sensetime.core.network.vnic.v1.VNicCreate.properties:type_name -> sensetime.core.network.vnic.v1.VNicProperties
	18, // 3: sensetime.core.network.vnic.v1.VNicCreate.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	5,  // 4: sensetime.core.network.vnic.v1.UpdateVNicRequest.vnic:type_name -> sensetime.core.network.vnic.v1.VNicUpdate
	19, // 5: sensetime.core.network.vnic.v1.UpdateVNicRequest.update_mask:type_name -> google.protobuf.FieldMask
	12, // 6: sensetime.core.network.vnic.v1.ListVNicsResponse.vnics:type_name -> sensetime.core.network.vnic.v1.VNic
	10, // 7: sensetime.core.network.vnic.v1.ResizeVNicRequest.vnic_resize:type_name -> sensetime.core.network.vnic.v1.VNicResize
	13, // 8: sensetime.core.network.vnic.v1.VNicResize.properties:type_name -> sensetime.core.network.vnic.v1.VNicProperties
	18, // 9: sensetime.core.network.vnic.v1.VNicResize.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	0,  // 10: sensetime.core.network.vnic.v1.VNic.state:type_name -> sensetime.core.network.vnic.v1.State
	17, // 11: sensetime.core.network.vnic.v1.VNic.tags:type_name -> sensetime.core.network.vnic.v1.VNic.TagsEntry
	13, // 12: sensetime.core.network.vnic.v1.VNic.properties:type_name -> sensetime.core.network.vnic.v1.VNicProperties
	18, // 13: sensetime.core.network.vnic.v1.VNic.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	20, // 14: sensetime.core.network.vnic.v1.VNic.create_time:type_name -> google.protobuf.Timestamp
	20, // 15: sensetime.core.network.vnic.v1.VNic.update_time:type_name -> google.protobuf.Timestamp
	14, // 16: sensetime.core.network.vnic.v1.VNicProperties.resources:type_name -> sensetime.core.network.vnic.v1.Resources
	15, // 17: sensetime.core.network.vnic.v1.Resources.billing_items:type_name -> sensetime.core.network.vnic.v1.BillingItems
	1,  // 18: sensetime.core.network.vnic.v1.VNics.CreateVNic:input_type -> sensetime.core.network.vnic.v1.CreateVNicRequest
	3,  // 19: sensetime.core.network.vnic.v1.VNics.DeleteVNic:input_type -> sensetime.core.network.vnic.v1.DeleteVNicRequest
	4,  // 20: sensetime.core.network.vnic.v1.VNics.UpdateVNic:input_type -> sensetime.core.network.vnic.v1.UpdateVNicRequest
	6,  // 21: sensetime.core.network.vnic.v1.VNics.GetVNic:input_type -> sensetime.core.network.vnic.v1.GetVNicRequest
	7,  // 22: sensetime.core.network.vnic.v1.VNics.ListVNics:input_type -> sensetime.core.network.vnic.v1.ListVNicRequest
	9,  // 23: sensetime.core.network.vnic.v1.VNics.ResizeVNic:input_type -> sensetime.core.network.vnic.v1.ResizeVNicRequest
	11, // 24: sensetime.core.network.vnic.v1.VNics.ReleaseVNic:input_type -> sensetime.core.network.vnic.v1.ReleaseVNicRequest
	12, // 25: sensetime.core.network.vnic.v1.VNics.CreateVNic:output_type -> sensetime.core.network.vnic.v1.VNic
	21, // 26: sensetime.core.network.vnic.v1.VNics.DeleteVNic:output_type -> google.protobuf.Empty
	12, // 27: sensetime.core.network.vnic.v1.VNics.UpdateVNic:output_type -> sensetime.core.network.vnic.v1.VNic
	12, // 28: sensetime.core.network.vnic.v1.VNics.GetVNic:output_type -> sensetime.core.network.vnic.v1.VNic
	8,  // 29: sensetime.core.network.vnic.v1.VNics.ListVNics:output_type -> sensetime.core.network.vnic.v1.ListVNicsResponse
	12, // 30: sensetime.core.network.vnic.v1.VNics.ResizeVNic:output_type -> sensetime.core.network.vnic.v1.VNic
	21, // 31: sensetime.core.network.vnic.v1.VNics.ReleaseVNic:output_type -> google.protobuf.Empty
	25, // [25:32] is the sub-list for method output_type
	18, // [18:25] is the sub-list for method input_type
	18, // [18:18] is the sub-list for extension type_name
	18, // [18:18] is the sub-list for extension extendee
	0,  // [0:18] is the sub-list for field type_name
}

func init() { file_network_vnic_v1_vnic_proto_init() }
func file_network_vnic_v1_vnic_proto_init() {
	if File_network_vnic_v1_vnic_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_network_vnic_v1_vnic_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateVNicRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vnic_v1_vnic_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VNicCreate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vnic_v1_vnic_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteVNicRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vnic_v1_vnic_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateVNicRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vnic_v1_vnic_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VNicUpdate); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vnic_v1_vnic_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetVNicRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vnic_v1_vnic_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListVNicRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vnic_v1_vnic_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListVNicsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vnic_v1_vnic_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ResizeVNicRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vnic_v1_vnic_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VNicResize); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vnic_v1_vnic_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ReleaseVNicRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vnic_v1_vnic_proto_msgTypes[11].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VNic); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vnic_v1_vnic_proto_msgTypes[12].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*VNicProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vnic_v1_vnic_proto_msgTypes[13].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*Resources); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_vnic_v1_vnic_proto_msgTypes[14].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BillingItems); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_vnic_v1_vnic_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   17,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_network_vnic_v1_vnic_proto_goTypes,
		DependencyIndexes: file_network_vnic_v1_vnic_proto_depIdxs,
		EnumInfos:         file_network_vnic_v1_vnic_proto_enumTypes,
		MessageInfos:      file_network_vnic_v1_vnic_proto_msgTypes,
	}.Build()
	File_network_vnic_v1_vnic_proto = out.File
	file_network_vnic_v1_vnic_proto_rawDesc = nil
	file_network_vnic_v1_vnic_proto_goTypes = nil
	file_network_vnic_v1_vnic_proto_depIdxs = nil
}
