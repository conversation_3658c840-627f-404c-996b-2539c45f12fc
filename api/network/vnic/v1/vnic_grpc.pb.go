// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/vnic/v1/vnic.proto

package vnic

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// VNicsClient is the client API for VNics service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type VNicsClient interface {
	// [zh] 创建虚拟网卡资源.
	// [en] Creates VNic resources.
	CreateVNic(ctx context.Context, in *CreateVNicRequest, opts ...grpc.CallOption) (*VNic, error)
	// [zh] 删除虚拟网卡资源.
	// [en] Delete the VNic resources.
	DeleteVNic(ctx context.Context, in *DeleteVNicRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// [zh] 更新虚拟网卡资源.
	// [en] Update the VNic resources.
	UpdateVNic(ctx context.Context, in *UpdateVNicRequest, opts ...grpc.CallOption) (*VNic, error)
	// [zh] 查看虚拟网卡资源详情.
	// [en] Gets details of a VNic resource.
	GetVNic(ctx context.Context, in *GetVNicRequest, opts ...grpc.CallOption) (*VNic, error)
	// [zh] 列举虚拟网卡资源详情.
	// [en] List details of VNic resources.
	ListVNics(ctx context.Context, in *ListVNicRequest, opts ...grpc.CallOption) (*ListVNicsResponse, error)
	// [zh] 扩缩容虚拟网卡资源.
	// [en] The Resize of a VNic resources.
	ResizeVNic(ctx context.Context, in *ResizeVNicRequest, opts ...grpc.CallOption) (*VNic, error)
	// [zh] 释放 (保留期释放).
	// [en] The Release of a VNic resources.
	ReleaseVNic(ctx context.Context, in *ReleaseVNicRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type vNicsClient struct {
	cc grpc.ClientConnInterface
}

func NewVNicsClient(cc grpc.ClientConnInterface) VNicsClient {
	return &vNicsClient{cc}
}

func (c *vNicsClient) CreateVNic(ctx context.Context, in *CreateVNicRequest, opts ...grpc.CallOption) (*VNic, error) {
	out := new(VNic)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vnic.v1.VNics/CreateVNic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vNicsClient) DeleteVNic(ctx context.Context, in *DeleteVNicRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vnic.v1.VNics/DeleteVNic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vNicsClient) UpdateVNic(ctx context.Context, in *UpdateVNicRequest, opts ...grpc.CallOption) (*VNic, error) {
	out := new(VNic)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vnic.v1.VNics/UpdateVNic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vNicsClient) GetVNic(ctx context.Context, in *GetVNicRequest, opts ...grpc.CallOption) (*VNic, error) {
	out := new(VNic)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vnic.v1.VNics/GetVNic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vNicsClient) ListVNics(ctx context.Context, in *ListVNicRequest, opts ...grpc.CallOption) (*ListVNicsResponse, error) {
	out := new(ListVNicsResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vnic.v1.VNics/ListVNics", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vNicsClient) ResizeVNic(ctx context.Context, in *ResizeVNicRequest, opts ...grpc.CallOption) (*VNic, error) {
	out := new(VNic)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vnic.v1.VNics/ResizeVNic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *vNicsClient) ReleaseVNic(ctx context.Context, in *ReleaseVNicRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.vnic.v1.VNics/ReleaseVNic", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// VNicsServer is the server API for VNics service.
// All implementations must embed UnimplementedVNicsServer
// for forward compatibility
type VNicsServer interface {
	// [zh] 创建虚拟网卡资源.
	// [en] Creates VNic resources.
	CreateVNic(context.Context, *CreateVNicRequest) (*VNic, error)
	// [zh] 删除虚拟网卡资源.
	// [en] Delete the VNic resources.
	DeleteVNic(context.Context, *DeleteVNicRequest) (*emptypb.Empty, error)
	// [zh] 更新虚拟网卡资源.
	// [en] Update the VNic resources.
	UpdateVNic(context.Context, *UpdateVNicRequest) (*VNic, error)
	// [zh] 查看虚拟网卡资源详情.
	// [en] Gets details of a VNic resource.
	GetVNic(context.Context, *GetVNicRequest) (*VNic, error)
	// [zh] 列举虚拟网卡资源详情.
	// [en] List details of VNic resources.
	ListVNics(context.Context, *ListVNicRequest) (*ListVNicsResponse, error)
	// [zh] 扩缩容虚拟网卡资源.
	// [en] The Resize of a VNic resources.
	ResizeVNic(context.Context, *ResizeVNicRequest) (*VNic, error)
	// [zh] 释放 (保留期释放).
	// [en] The Release of a VNic resources.
	ReleaseVNic(context.Context, *ReleaseVNicRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedVNicsServer()
}

// UnimplementedVNicsServer must be embedded to have forward compatible implementations.
type UnimplementedVNicsServer struct {
}

func (UnimplementedVNicsServer) CreateVNic(context.Context, *CreateVNicRequest) (*VNic, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateVNic not implemented")
}
func (UnimplementedVNicsServer) DeleteVNic(context.Context, *DeleteVNicRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteVNic not implemented")
}
func (UnimplementedVNicsServer) UpdateVNic(context.Context, *UpdateVNicRequest) (*VNic, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateVNic not implemented")
}
func (UnimplementedVNicsServer) GetVNic(context.Context, *GetVNicRequest) (*VNic, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetVNic not implemented")
}
func (UnimplementedVNicsServer) ListVNics(context.Context, *ListVNicRequest) (*ListVNicsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListVNics not implemented")
}
func (UnimplementedVNicsServer) ResizeVNic(context.Context, *ResizeVNicRequest) (*VNic, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ResizeVNic not implemented")
}
func (UnimplementedVNicsServer) ReleaseVNic(context.Context, *ReleaseVNicRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ReleaseVNic not implemented")
}
func (UnimplementedVNicsServer) mustEmbedUnimplementedVNicsServer() {}

// UnsafeVNicsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to VNicsServer will
// result in compilation errors.
type UnsafeVNicsServer interface {
	mustEmbedUnimplementedVNicsServer()
}

func RegisterVNicsServer(s grpc.ServiceRegistrar, srv VNicsServer) {
	s.RegisterService(&VNics_ServiceDesc, srv)
}

func _VNics_CreateVNic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateVNicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VNicsServer).CreateVNic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vnic.v1.VNics/CreateVNic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VNicsServer).CreateVNic(ctx, req.(*CreateVNicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VNics_DeleteVNic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteVNicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VNicsServer).DeleteVNic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vnic.v1.VNics/DeleteVNic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VNicsServer).DeleteVNic(ctx, req.(*DeleteVNicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VNics_UpdateVNic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateVNicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VNicsServer).UpdateVNic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vnic.v1.VNics/UpdateVNic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VNicsServer).UpdateVNic(ctx, req.(*UpdateVNicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VNics_GetVNic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetVNicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VNicsServer).GetVNic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vnic.v1.VNics/GetVNic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VNicsServer).GetVNic(ctx, req.(*GetVNicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VNics_ListVNics_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListVNicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VNicsServer).ListVNics(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vnic.v1.VNics/ListVNics",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VNicsServer).ListVNics(ctx, req.(*ListVNicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VNics_ResizeVNic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ResizeVNicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VNicsServer).ResizeVNic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vnic.v1.VNics/ResizeVNic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VNicsServer).ResizeVNic(ctx, req.(*ResizeVNicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _VNics_ReleaseVNic_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ReleaseVNicRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(VNicsServer).ReleaseVNic(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.vnic.v1.VNics/ReleaseVNic",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(VNicsServer).ReleaseVNic(ctx, req.(*ReleaseVNicRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// VNics_ServiceDesc is the grpc.ServiceDesc for VNics service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var VNics_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.vnic.v1.VNics",
	HandlerType: (*VNicsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateVNic",
			Handler:    _VNics_CreateVNic_Handler,
		},
		{
			MethodName: "DeleteVNic",
			Handler:    _VNics_DeleteVNic_Handler,
		},
		{
			MethodName: "UpdateVNic",
			Handler:    _VNics_UpdateVNic_Handler,
		},
		{
			MethodName: "GetVNic",
			Handler:    _VNics_GetVNic_Handler,
		},
		{
			MethodName: "ListVNics",
			Handler:    _VNics_ListVNics_Handler,
		},
		{
			MethodName: "ResizeVNic",
			Handler:    _VNics_ResizeVNic_Handler,
		},
		{
			MethodName: "ReleaseVNic",
			Handler:    _VNics_ReleaseVNic_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/vnic/v1/vnic.proto",
}
