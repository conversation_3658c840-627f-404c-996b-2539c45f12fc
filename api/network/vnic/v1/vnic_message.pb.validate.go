// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/vnic/v1/vnic_message.proto

package vnic

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateVNicMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateVNicMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateVNicMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateVNicMessageMultiError, or nil if none found.
func (m *CreateVNicMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVNicMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenantCode

	// no validation rules for OperatorId

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for VnicName

	if all {
		switch v := interface{}(m.GetVnic()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVNicMessageValidationError{
					field:  "Vnic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVNicMessageValidationError{
					field:  "Vnic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVnic()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVNicMessageValidationError{
				field:  "Vnic",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDedicatedResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVNicMessageValidationError{
					field:  "DedicatedResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVNicMessageValidationError{
					field:  "DedicatedResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDedicatedResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVNicMessageValidationError{
				field:  "DedicatedResource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCallbackData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVNicMessageValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVNicMessageValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallbackData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVNicMessageValidationError{
				field:  "CallbackData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateVNicMessageMultiError(errors)
	}

	return nil
}

// CreateVNicMessageMultiError is an error wrapping multiple validation errors
// returned by CreateVNicMessage.ValidateAll() if the designated constraints
// aren't met.
type CreateVNicMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVNicMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVNicMessageMultiError) AllErrors() []error { return m }

// CreateVNicMessageValidationError is the validation error returned by
// CreateVNicMessage.Validate if the designated constraints aren't met.
type CreateVNicMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVNicMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateVNicMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateVNicMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateVNicMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateVNicMessageValidationError) ErrorName() string {
	return "CreateVNicMessageValidationError"
}

// Error satisfies the builtin error interface
func (e CreateVNicMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVNicMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVNicMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVNicMessageValidationError{}

// Validate checks the field values on DeleteVNicMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteVNicMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteVNicMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteVNicMessageMultiError, or nil if none found.
func (m *DeleteVNicMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteVNicMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenantCode

	// no validation rules for OperatorId

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for VnicName

	if len(errors) > 0 {
		return DeleteVNicMessageMultiError(errors)
	}

	return nil
}

// DeleteVNicMessageMultiError is an error wrapping multiple validation errors
// returned by DeleteVNicMessage.ValidateAll() if the designated constraints
// aren't met.
type DeleteVNicMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteVNicMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteVNicMessageMultiError) AllErrors() []error { return m }

// DeleteVNicMessageValidationError is the validation error returned by
// DeleteVNicMessage.Validate if the designated constraints aren't met.
type DeleteVNicMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteVNicMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteVNicMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteVNicMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteVNicMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteVNicMessageValidationError) ErrorName() string {
	return "DeleteVNicMessageValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteVNicMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteVNicMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteVNicMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteVNicMessageValidationError{}

// Validate checks the field values on ResizeVNicMessage with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ResizeVNicMessage) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResizeVNicMessage with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResizeVNicMessageMultiError, or nil if none found.
func (m *ResizeVNicMessage) ValidateAll() error {
	return m.validate(true)
}

func (m *ResizeVNicMessage) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenantCode

	// no validation rules for OperatorId

	// no validation rules for ResourceId

	// no validation rules for SkuId

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResizeVNicMessageValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResizeVNicMessageValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResizeVNicMessageValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResizeVNicMessageValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResizeVNicMessageValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResizeVNicMessageValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetDedicatedResource()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResizeVNicMessageValidationError{
					field:  "DedicatedResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResizeVNicMessageValidationError{
					field:  "DedicatedResource",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDedicatedResource()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResizeVNicMessageValidationError{
				field:  "DedicatedResource",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetCallbackData()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResizeVNicMessageValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResizeVNicMessageValidationError{
					field:  "CallbackData",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCallbackData()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResizeVNicMessageValidationError{
				field:  "CallbackData",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResizeVNicMessageMultiError(errors)
	}

	return nil
}

// ResizeVNicMessageMultiError is an error wrapping multiple validation errors
// returned by ResizeVNicMessage.ValidateAll() if the designated constraints
// aren't met.
type ResizeVNicMessageMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResizeVNicMessageMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResizeVNicMessageMultiError) AllErrors() []error { return m }

// ResizeVNicMessageValidationError is the validation error returned by
// ResizeVNicMessage.Validate if the designated constraints aren't met.
type ResizeVNicMessageValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResizeVNicMessageValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResizeVNicMessageValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResizeVNicMessageValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResizeVNicMessageValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResizeVNicMessageValidationError) ErrorName() string {
	return "ResizeVNicMessageValidationError"
}

// Error satisfies the builtin error interface
func (e ResizeVNicMessageValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResizeVNicMessage.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResizeVNicMessageValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResizeVNicMessageValidationError{}

// Validate checks the field values on ModelSpaceProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ModelSpaceProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ModelSpaceProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ModelSpacePropertiesMultiError, or nil if none found.
func (m *ModelSpaceProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *ModelSpaceProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return ModelSpacePropertiesMultiError(errors)
	}

	return nil
}

// ModelSpacePropertiesMultiError is an error wrapping multiple validation
// errors returned by ModelSpaceProperties.ValidateAll() if the designated
// constraints aren't met.
type ModelSpacePropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ModelSpacePropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ModelSpacePropertiesMultiError) AllErrors() []error { return m }

// ModelSpacePropertiesValidationError is the validation error returned by
// ModelSpaceProperties.Validate if the designated constraints aren't met.
type ModelSpacePropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ModelSpacePropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ModelSpacePropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ModelSpacePropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ModelSpacePropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ModelSpacePropertiesValidationError) ErrorName() string {
	return "ModelSpacePropertiesValidationError"
}

// Error satisfies the builtin error interface
func (e ModelSpacePropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sModelSpaceProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ModelSpacePropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ModelSpacePropertiesValidationError{}

// Validate checks the field values on DedicatedResource with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DedicatedResource) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DedicatedResource with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DedicatedResourceMultiError, or nil if none found.
func (m *DedicatedResource) ValidateAll() error {
	return m.validate(true)
}

func (m *DedicatedResource) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for IsDedicatedTenant

	// no validation rules for IsDedicatedResource

	// no validation rules for ResourcePackageId

	if len(errors) > 0 {
		return DedicatedResourceMultiError(errors)
	}

	return nil
}

// DedicatedResourceMultiError is an error wrapping multiple validation errors
// returned by DedicatedResource.ValidateAll() if the designated constraints
// aren't met.
type DedicatedResourceMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DedicatedResourceMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DedicatedResourceMultiError) AllErrors() []error { return m }

// DedicatedResourceValidationError is the validation error returned by
// DedicatedResource.Validate if the designated constraints aren't met.
type DedicatedResourceValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DedicatedResourceValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DedicatedResourceValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DedicatedResourceValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DedicatedResourceValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DedicatedResourceValidationError) ErrorName() string {
	return "DedicatedResourceValidationError"
}

// Error satisfies the builtin error interface
func (e DedicatedResourceValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDedicatedResource.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DedicatedResourceValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DedicatedResourceValidationError{}

// Validate checks the field values on CallbackData with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *CallbackData) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CallbackData with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in CallbackDataMultiError, or
// nil if none found.
func (m *CallbackData) ValidateAll() error {
	return m.validate(true)
}

func (m *CallbackData) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for TenantId

	// no validation rules for UserId

	// no validation rules for OrderId

	if len(errors) > 0 {
		return CallbackDataMultiError(errors)
	}

	return nil
}

// CallbackDataMultiError is an error wrapping multiple validation errors
// returned by CallbackData.ValidateAll() if the designated constraints aren't met.
type CallbackDataMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CallbackDataMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CallbackDataMultiError) AllErrors() []error { return m }

// CallbackDataValidationError is the validation error returned by
// CallbackData.Validate if the designated constraints aren't met.
type CallbackDataValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CallbackDataValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CallbackDataValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CallbackDataValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CallbackDataValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CallbackDataValidationError) ErrorName() string { return "CallbackDataValidationError" }

// Error satisfies the builtin error interface
func (e CallbackDataValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCallbackData.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CallbackDataValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CallbackDataValidationError{}
