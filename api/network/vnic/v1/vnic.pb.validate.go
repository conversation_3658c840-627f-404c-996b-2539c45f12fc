// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/vnic/v1/vnic.proto

package vnic

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateVNicRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateVNicRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateVNicRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateVNicRequestMultiError, or nil if none found.
func (m *CreateVNicRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateVNicRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetVnicName()) > 63 {
		err := CreateVNicRequestValidationError{
			field:  "VnicName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateVNicRequest_VnicName_Pattern.MatchString(m.GetVnicName()) {
		err := CreateVNicRequestValidationError{
			field:  "VnicName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetVnic()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateVNicRequestValidationError{
					field:  "Vnic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateVNicRequestValidationError{
					field:  "Vnic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVnic()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateVNicRequestValidationError{
				field:  "Vnic",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateVNicRequestMultiError(errors)
	}

	return nil
}

// CreateVNicRequestMultiError is an error wrapping multiple validation errors
// returned by CreateVNicRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateVNicRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateVNicRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateVNicRequestMultiError) AllErrors() []error { return m }

// CreateVNicRequestValidationError is the validation error returned by
// CreateVNicRequest.Validate if the designated constraints aren't met.
type CreateVNicRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateVNicRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateVNicRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateVNicRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateVNicRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateVNicRequestValidationError) ErrorName() string {
	return "CreateVNicRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateVNicRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateVNicRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateVNicRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateVNicRequestValidationError{}

var _CreateVNicRequest_VnicName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on VNicCreate with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VNicCreate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VNicCreate with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VNicCreateMultiError, or
// nil if none found.
func (m *VNicCreate) ValidateAll() error {
	return m.validate(true)
}

func (m *VNicCreate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Subnet

	// no validation rules for Name

	// no validation rules for Description

	// no validation rules for ResourceType

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VNicCreateValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VNicCreateValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VNicCreateValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VNicCreateValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VNicCreateValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VNicCreateValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VNicCreateMultiError(errors)
	}

	return nil
}

// VNicCreateMultiError is an error wrapping multiple validation errors
// returned by VNicCreate.ValidateAll() if the designated constraints aren't met.
type VNicCreateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VNicCreateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VNicCreateMultiError) AllErrors() []error { return m }

// VNicCreateValidationError is the validation error returned by
// VNicCreate.Validate if the designated constraints aren't met.
type VNicCreateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VNicCreateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VNicCreateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VNicCreateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VNicCreateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VNicCreateValidationError) ErrorName() string { return "VNicCreateValidationError" }

// Error satisfies the builtin error interface
func (e VNicCreateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVNicCreate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VNicCreateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VNicCreateValidationError{}

// Validate checks the field values on DeleteVNicRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteVNicRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteVNicRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteVNicRequestMultiError, or nil if none found.
func (m *DeleteVNicRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteVNicRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Subnet

	// no validation rules for VnicName

	if len(errors) > 0 {
		return DeleteVNicRequestMultiError(errors)
	}

	return nil
}

// DeleteVNicRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteVNicRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteVNicRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteVNicRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteVNicRequestMultiError) AllErrors() []error { return m }

// DeleteVNicRequestValidationError is the validation error returned by
// DeleteVNicRequest.Validate if the designated constraints aren't met.
type DeleteVNicRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteVNicRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteVNicRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteVNicRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteVNicRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteVNicRequestValidationError) ErrorName() string {
	return "DeleteVNicRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteVNicRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteVNicRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteVNicRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteVNicRequestValidationError{}

// Validate checks the field values on UpdateVNicRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateVNicRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateVNicRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateVNicRequestMultiError, or nil if none found.
func (m *UpdateVNicRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateVNicRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetVnicName()) > 63 {
		err := UpdateVNicRequestValidationError{
			field:  "VnicName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_UpdateVNicRequest_VnicName_Pattern.MatchString(m.GetVnicName()) {
		err := UpdateVNicRequestValidationError{
			field:  "VnicName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetVnic()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateVNicRequestValidationError{
					field:  "Vnic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateVNicRequestValidationError{
					field:  "Vnic",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVnic()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateVNicRequestValidationError{
				field:  "Vnic",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateVNicRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateVNicRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateVNicRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateVNicRequestMultiError(errors)
	}

	return nil
}

// UpdateVNicRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateVNicRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateVNicRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateVNicRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateVNicRequestMultiError) AllErrors() []error { return m }

// UpdateVNicRequestValidationError is the validation error returned by
// UpdateVNicRequest.Validate if the designated constraints aren't met.
type UpdateVNicRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateVNicRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateVNicRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateVNicRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateVNicRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateVNicRequestValidationError) ErrorName() string {
	return "UpdateVNicRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateVNicRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateVNicRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateVNicRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateVNicRequestValidationError{}

var _UpdateVNicRequest_VnicName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on VNicUpdate with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VNicUpdate) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VNicUpdate with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VNicUpdateMultiError, or
// nil if none found.
func (m *VNicUpdate) ValidateAll() error {
	return m.validate(true)
}

func (m *VNicUpdate) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if m.GetDisplayName() != "" {

		if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
			err := VNicUpdateValidationError{
				field:  "DisplayName",
				reason: "value length must be at most 63 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_VNicUpdate_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
			err := VNicUpdateValidationError{
				field:  "DisplayName",
				reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Description

	if len(errors) > 0 {
		return VNicUpdateMultiError(errors)
	}

	return nil
}

// VNicUpdateMultiError is an error wrapping multiple validation errors
// returned by VNicUpdate.ValidateAll() if the designated constraints aren't met.
type VNicUpdateMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VNicUpdateMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VNicUpdateMultiError) AllErrors() []error { return m }

// VNicUpdateValidationError is the validation error returned by
// VNicUpdate.Validate if the designated constraints aren't met.
type VNicUpdateValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VNicUpdateValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VNicUpdateValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VNicUpdateValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VNicUpdateValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VNicUpdateValidationError) ErrorName() string { return "VNicUpdateValidationError" }

// Error satisfies the builtin error interface
func (e VNicUpdateValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVNicUpdate.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VNicUpdateValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VNicUpdateValidationError{}

var _VNicUpdate_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on GetVNicRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetVNicRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetVNicRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetVNicRequestMultiError,
// or nil if none found.
func (m *GetVNicRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetVNicRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Subnet

	// no validation rules for VnicName

	if len(errors) > 0 {
		return GetVNicRequestMultiError(errors)
	}

	return nil
}

// GetVNicRequestMultiError is an error wrapping multiple validation errors
// returned by GetVNicRequest.ValidateAll() if the designated constraints
// aren't met.
type GetVNicRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetVNicRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetVNicRequestMultiError) AllErrors() []error { return m }

// GetVNicRequestValidationError is the validation error returned by
// GetVNicRequest.Validate if the designated constraints aren't met.
type GetVNicRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetVNicRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetVNicRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetVNicRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetVNicRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetVNicRequestValidationError) ErrorName() string { return "GetVNicRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetVNicRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetVNicRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetVNicRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetVNicRequestValidationError{}

// Validate checks the field values on ListVNicRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListVNicRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListVNicRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListVNicRequestMultiError, or nil if none found.
func (m *ListVNicRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListVNicRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for SubnetName

	if len(errors) > 0 {
		return ListVNicRequestMultiError(errors)
	}

	return nil
}

// ListVNicRequestMultiError is an error wrapping multiple validation errors
// returned by ListVNicRequest.ValidateAll() if the designated constraints
// aren't met.
type ListVNicRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListVNicRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListVNicRequestMultiError) AllErrors() []error { return m }

// ListVNicRequestValidationError is the validation error returned by
// ListVNicRequest.Validate if the designated constraints aren't met.
type ListVNicRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListVNicRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListVNicRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListVNicRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListVNicRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListVNicRequestValidationError) ErrorName() string { return "ListVNicRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListVNicRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListVNicRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListVNicRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListVNicRequestValidationError{}

// Validate checks the field values on ListVNicsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListVNicsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListVNicsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListVNicsResponseMultiError, or nil if none found.
func (m *ListVNicsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListVNicsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetVnics() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListVNicsResponseValidationError{
						field:  fmt.Sprintf("Vnics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListVNicsResponseValidationError{
						field:  fmt.Sprintf("Vnics[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListVNicsResponseValidationError{
					field:  fmt.Sprintf("Vnics[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListVNicsResponseMultiError(errors)
	}

	return nil
}

// ListVNicsResponseMultiError is an error wrapping multiple validation errors
// returned by ListVNicsResponse.ValidateAll() if the designated constraints
// aren't met.
type ListVNicsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListVNicsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListVNicsResponseMultiError) AllErrors() []error { return m }

// ListVNicsResponseValidationError is the validation error returned by
// ListVNicsResponse.Validate if the designated constraints aren't met.
type ListVNicsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListVNicsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListVNicsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListVNicsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListVNicsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListVNicsResponseValidationError) ErrorName() string {
	return "ListVNicsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListVNicsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListVNicsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListVNicsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListVNicsResponseValidationError{}

// Validate checks the field values on ResizeVNicRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ResizeVNicRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ResizeVNicRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ResizeVNicRequestMultiError, or nil if none found.
func (m *ResizeVNicRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ResizeVNicRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetVnicName()) > 63 {
		err := ResizeVNicRequestValidationError{
			field:  "VnicName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ResizeVNicRequest_VnicName_Pattern.MatchString(m.GetVnicName()) {
		err := ResizeVNicRequestValidationError{
			field:  "VnicName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetVnicResize()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResizeVNicRequestValidationError{
					field:  "VnicResize",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResizeVNicRequestValidationError{
					field:  "VnicResize",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetVnicResize()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResizeVNicRequestValidationError{
				field:  "VnicResize",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResizeVNicRequestMultiError(errors)
	}

	return nil
}

// ResizeVNicRequestMultiError is an error wrapping multiple validation errors
// returned by ResizeVNicRequest.ValidateAll() if the designated constraints
// aren't met.
type ResizeVNicRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResizeVNicRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResizeVNicRequestMultiError) AllErrors() []error { return m }

// ResizeVNicRequestValidationError is the validation error returned by
// ResizeVNicRequest.Validate if the designated constraints aren't met.
type ResizeVNicRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResizeVNicRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResizeVNicRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResizeVNicRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResizeVNicRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResizeVNicRequestValidationError) ErrorName() string {
	return "ResizeVNicRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ResizeVNicRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResizeVNicRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResizeVNicRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResizeVNicRequestValidationError{}

var _ResizeVNicRequest_VnicName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on VNicResize with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VNicResize) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VNicResize with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VNicResizeMultiError, or
// nil if none found.
func (m *VNicResize) ValidateAll() error {
	return m.validate(true)
}

func (m *VNicResize) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for ResourceId

	// no validation rules for SkuId

	// no validation rules for OperatorId

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VNicResizeValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VNicResizeValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VNicResizeValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VNicResizeValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VNicResizeValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VNicResizeValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VNicResizeMultiError(errors)
	}

	return nil
}

// VNicResizeMultiError is an error wrapping multiple validation errors
// returned by VNicResize.ValidateAll() if the designated constraints aren't met.
type VNicResizeMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VNicResizeMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VNicResizeMultiError) AllErrors() []error { return m }

// VNicResizeValidationError is the validation error returned by
// VNicResize.Validate if the designated constraints aren't met.
type VNicResizeValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VNicResizeValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VNicResizeValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VNicResizeValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VNicResizeValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VNicResizeValidationError) ErrorName() string { return "VNicResizeValidationError" }

// Error satisfies the builtin error interface
func (e VNicResizeValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVNicResize.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VNicResizeValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VNicResizeValidationError{}

// Validate checks the field values on ReleaseVNicRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ReleaseVNicRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ReleaseVNicRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ReleaseVNicRequestMultiError, or nil if none found.
func (m *ReleaseVNicRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ReleaseVNicRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetVnicName()) > 63 {
		err := ReleaseVNicRequestValidationError{
			field:  "VnicName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_ReleaseVNicRequest_VnicName_Pattern.MatchString(m.GetVnicName()) {
		err := ReleaseVNicRequestValidationError{
			field:  "VnicName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if len(errors) > 0 {
		return ReleaseVNicRequestMultiError(errors)
	}

	return nil
}

// ReleaseVNicRequestMultiError is an error wrapping multiple validation errors
// returned by ReleaseVNicRequest.ValidateAll() if the designated constraints
// aren't met.
type ReleaseVNicRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ReleaseVNicRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ReleaseVNicRequestMultiError) AllErrors() []error { return m }

// ReleaseVNicRequestValidationError is the validation error returned by
// ReleaseVNicRequest.Validate if the designated constraints aren't met.
type ReleaseVNicRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ReleaseVNicRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ReleaseVNicRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ReleaseVNicRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ReleaseVNicRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ReleaseVNicRequestValidationError) ErrorName() string {
	return "ReleaseVNicRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ReleaseVNicRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sReleaseVNicRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ReleaseVNicRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ReleaseVNicRequestValidationError{}

var _ReleaseVNicRequest_VnicName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on VNic with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *VNic) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VNic with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in VNicMultiError, or nil if none found.
func (m *VNic) ValidateAll() error {
	return m.validate(true)
}

func (m *VNic) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Uid

	// no validation rules for Name

	if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
		err := VNicValidationError{
			field:  "DisplayName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_VNic_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
		err := VNicValidationError{
			field:  "DisplayName",
			reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VNicValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VNicValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VNicValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VNicValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VNicValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VNicValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VNicValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VNicValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VNicValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VNicValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VNicValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VNicValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return VNicMultiError(errors)
	}

	return nil
}

// VNicMultiError is an error wrapping multiple validation errors returned by
// VNic.ValidateAll() if the designated constraints aren't met.
type VNicMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VNicMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VNicMultiError) AllErrors() []error { return m }

// VNicValidationError is the validation error returned by VNic.Validate if the
// designated constraints aren't met.
type VNicValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VNicValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VNicValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VNicValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VNicValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VNicValidationError) ErrorName() string { return "VNicValidationError" }

// Error satisfies the builtin error interface
func (e VNicValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVNic.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VNicValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VNicValidationError{}

var _VNic_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on VNicProperties with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *VNicProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on VNicProperties with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in VNicPropertiesMultiError,
// or nil if none found.
func (m *VNicProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *VNicProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetResources()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, VNicPropertiesValidationError{
					field:  "Resources",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, VNicPropertiesValidationError{
					field:  "Resources",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetResources()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return VNicPropertiesValidationError{
				field:  "Resources",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for VnicId

	if utf8.RuneCountInString(m.GetName()) > 32 {
		err := VNicPropertiesValidationError{
			field:  "Name",
			reason: "value length must be at most 32 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_VNicProperties_Name_Pattern.MatchString(m.GetName()) {
		err := VNicPropertiesValidationError{
			field:  "Name",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,30}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for IpAddrV4

	// no validation rules for MacAddr

	// no validation rules for Subnet

	// no validation rules for Vpc

	if len(errors) > 0 {
		return VNicPropertiesMultiError(errors)
	}

	return nil
}

// VNicPropertiesMultiError is an error wrapping multiple validation errors
// returned by VNicProperties.ValidateAll() if the designated constraints
// aren't met.
type VNicPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m VNicPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m VNicPropertiesMultiError) AllErrors() []error { return m }

// VNicPropertiesValidationError is the validation error returned by
// VNicProperties.Validate if the designated constraints aren't met.
type VNicPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e VNicPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e VNicPropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e VNicPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e VNicPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e VNicPropertiesValidationError) ErrorName() string { return "VNicPropertiesValidationError" }

// Error satisfies the builtin error interface
func (e VNicPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sVNicProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = VNicPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = VNicPropertiesValidationError{}

var _VNicProperties_Name_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,30}[a-z0-9])?$")

// Validate checks the field values on Resources with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *Resources) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on Resources with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in ResourcesMultiError, or nil
// if none found.
func (m *Resources) ValidateAll() error {
	return m.validate(true)
}

func (m *Resources) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if all {
		switch v := interface{}(m.GetBillingItems()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, ResourcesValidationError{
					field:  "BillingItems",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, ResourcesValidationError{
					field:  "BillingItems",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetBillingItems()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return ResourcesValidationError{
				field:  "BillingItems",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return ResourcesMultiError(errors)
	}

	return nil
}

// ResourcesMultiError is an error wrapping multiple validation errors returned
// by Resources.ValidateAll() if the designated constraints aren't met.
type ResourcesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ResourcesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ResourcesMultiError) AllErrors() []error { return m }

// ResourcesValidationError is the validation error returned by
// Resources.Validate if the designated constraints aren't met.
type ResourcesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ResourcesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ResourcesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ResourcesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ResourcesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ResourcesValidationError) ErrorName() string { return "ResourcesValidationError" }

// Error satisfies the builtin error interface
func (e ResourcesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sResources.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ResourcesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ResourcesValidationError{}

// Validate checks the field values on BillingItems with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *BillingItems) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BillingItems with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in BillingItemsMultiError, or
// nil if none found.
func (m *BillingItems) ValidateAll() error {
	return m.validate(true)
}

func (m *BillingItems) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	if len(errors) > 0 {
		return BillingItemsMultiError(errors)
	}

	return nil
}

// BillingItemsMultiError is an error wrapping multiple validation errors
// returned by BillingItems.ValidateAll() if the designated constraints aren't met.
type BillingItemsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BillingItemsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BillingItemsMultiError) AllErrors() []error { return m }

// BillingItemsValidationError is the validation error returned by
// BillingItems.Validate if the designated constraints aren't met.
type BillingItemsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BillingItemsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BillingItemsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BillingItemsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BillingItemsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BillingItemsValidationError) ErrorName() string { return "BillingItemsValidationError" }

// Error satisfies the builtin error interface
func (e BillingItemsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBillingItems.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BillingItemsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BillingItemsValidationError{}
