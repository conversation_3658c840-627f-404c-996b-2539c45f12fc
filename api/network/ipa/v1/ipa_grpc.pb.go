// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/ipa/v1/ipa.proto

package ipa

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// IPAsClient is the client API for IPAs service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type IPAsClient interface {
	// 列举符合请求的所有 IPAs.
	// [EN] List requested IPAs.
	ListIPAs(ctx context.Context, in *ListIPAsRequest, opts ...grpc.CallOption) (*ListIPAsResponse, error)
	// 获取符合请求的一个 IPA.
	// [EN] Get a requested IPA.
	GetIPA(ctx context.Context, in *IPAUIDRequest, opts ...grpc.CallOption) (*IPA, error)
	// 分配一个 IPA.
	// [EN] Create a IPA.
	CreateIPA(ctx context.Context, in *CreateIPARequest, opts ...grpc.CallOption) (*IPA, error)
	// 更新一个 IPA.
	// [EN] Update a IPA.
	UpdateIPA(ctx context.Context, in *UpdateIPARequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 释放一个 IPA.
	// [EN] Delete a IPA.
	DeleteIPA(ctx context.Context, in *IPAUIDRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 绑定一个 IPA.
	// [EN] Bind a IPA.
	BindIPA(ctx context.Context, in *BindingIPARequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 解绑一个 IPA.
	// [EN] Unbind a IPA.
	UnbindIPA(ctx context.Context, in *BindingIPARequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type iPAsClient struct {
	cc grpc.ClientConnInterface
}

func NewIPAsClient(cc grpc.ClientConnInterface) IPAsClient {
	return &iPAsClient{cc}
}

func (c *iPAsClient) ListIPAs(ctx context.Context, in *ListIPAsRequest, opts ...grpc.CallOption) (*ListIPAsResponse, error) {
	out := new(ListIPAsResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.ipa.v1.IPAs/ListIPAs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iPAsClient) GetIPA(ctx context.Context, in *IPAUIDRequest, opts ...grpc.CallOption) (*IPA, error) {
	out := new(IPA)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.ipa.v1.IPAs/GetIPA", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iPAsClient) CreateIPA(ctx context.Context, in *CreateIPARequest, opts ...grpc.CallOption) (*IPA, error) {
	out := new(IPA)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.ipa.v1.IPAs/CreateIPA", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iPAsClient) UpdateIPA(ctx context.Context, in *UpdateIPARequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.ipa.v1.IPAs/UpdateIPA", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iPAsClient) DeleteIPA(ctx context.Context, in *IPAUIDRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.ipa.v1.IPAs/DeleteIPA", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iPAsClient) BindIPA(ctx context.Context, in *BindingIPARequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.ipa.v1.IPAs/BindIPA", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *iPAsClient) UnbindIPA(ctx context.Context, in *BindingIPARequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.ipa.v1.IPAs/UnbindIPA", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// IPAsServer is the server API for IPAs service.
// All implementations must embed UnimplementedIPAsServer
// for forward compatibility
type IPAsServer interface {
	// 列举符合请求的所有 IPAs.
	// [EN] List requested IPAs.
	ListIPAs(context.Context, *ListIPAsRequest) (*ListIPAsResponse, error)
	// 获取符合请求的一个 IPA.
	// [EN] Get a requested IPA.
	GetIPA(context.Context, *IPAUIDRequest) (*IPA, error)
	// 分配一个 IPA.
	// [EN] Create a IPA.
	CreateIPA(context.Context, *CreateIPARequest) (*IPA, error)
	// 更新一个 IPA.
	// [EN] Update a IPA.
	UpdateIPA(context.Context, *UpdateIPARequest) (*emptypb.Empty, error)
	// 释放一个 IPA.
	// [EN] Delete a IPA.
	DeleteIPA(context.Context, *IPAUIDRequest) (*emptypb.Empty, error)
	// 绑定一个 IPA.
	// [EN] Bind a IPA.
	BindIPA(context.Context, *BindingIPARequest) (*emptypb.Empty, error)
	// 解绑一个 IPA.
	// [EN] Unbind a IPA.
	UnbindIPA(context.Context, *BindingIPARequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedIPAsServer()
}

// UnimplementedIPAsServer must be embedded to have forward compatible implementations.
type UnimplementedIPAsServer struct {
}

func (UnimplementedIPAsServer) ListIPAs(context.Context, *ListIPAsRequest) (*ListIPAsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListIPAs not implemented")
}
func (UnimplementedIPAsServer) GetIPA(context.Context, *IPAUIDRequest) (*IPA, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetIPA not implemented")
}
func (UnimplementedIPAsServer) CreateIPA(context.Context, *CreateIPARequest) (*IPA, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateIPA not implemented")
}
func (UnimplementedIPAsServer) UpdateIPA(context.Context, *UpdateIPARequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateIPA not implemented")
}
func (UnimplementedIPAsServer) DeleteIPA(context.Context, *IPAUIDRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteIPA not implemented")
}
func (UnimplementedIPAsServer) BindIPA(context.Context, *BindingIPARequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method BindIPA not implemented")
}
func (UnimplementedIPAsServer) UnbindIPA(context.Context, *BindingIPARequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UnbindIPA not implemented")
}
func (UnimplementedIPAsServer) mustEmbedUnimplementedIPAsServer() {}

// UnsafeIPAsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to IPAsServer will
// result in compilation errors.
type UnsafeIPAsServer interface {
	mustEmbedUnimplementedIPAsServer()
}

func RegisterIPAsServer(s grpc.ServiceRegistrar, srv IPAsServer) {
	s.RegisterService(&IPAs_ServiceDesc, srv)
}

func _IPAs_ListIPAs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListIPAsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IPAsServer).ListIPAs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.ipa.v1.IPAs/ListIPAs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IPAsServer).ListIPAs(ctx, req.(*ListIPAsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IPAs_GetIPA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IPAUIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IPAsServer).GetIPA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.ipa.v1.IPAs/GetIPA",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IPAsServer).GetIPA(ctx, req.(*IPAUIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IPAs_CreateIPA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateIPARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IPAsServer).CreateIPA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.ipa.v1.IPAs/CreateIPA",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IPAsServer).CreateIPA(ctx, req.(*CreateIPARequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IPAs_UpdateIPA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateIPARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IPAsServer).UpdateIPA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.ipa.v1.IPAs/UpdateIPA",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IPAsServer).UpdateIPA(ctx, req.(*UpdateIPARequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IPAs_DeleteIPA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(IPAUIDRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IPAsServer).DeleteIPA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.ipa.v1.IPAs/DeleteIPA",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IPAsServer).DeleteIPA(ctx, req.(*IPAUIDRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IPAs_BindIPA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindingIPARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IPAsServer).BindIPA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.ipa.v1.IPAs/BindIPA",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IPAsServer).BindIPA(ctx, req.(*BindingIPARequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _IPAs_UnbindIPA_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(BindingIPARequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(IPAsServer).UnbindIPA(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.ipa.v1.IPAs/UnbindIPA",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(IPAsServer).UnbindIPA(ctx, req.(*BindingIPARequest))
	}
	return interceptor(ctx, in, info, handler)
}

// IPAs_ServiceDesc is the grpc.ServiceDesc for IPAs service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var IPAs_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.ipa.v1.IPAs",
	HandlerType: (*IPAsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListIPAs",
			Handler:    _IPAs_ListIPAs_Handler,
		},
		{
			MethodName: "GetIPA",
			Handler:    _IPAs_GetIPA_Handler,
		},
		{
			MethodName: "CreateIPA",
			Handler:    _IPAs_CreateIPA_Handler,
		},
		{
			MethodName: "UpdateIPA",
			Handler:    _IPAs_UpdateIPA_Handler,
		},
		{
			MethodName: "DeleteIPA",
			Handler:    _IPAs_DeleteIPA_Handler,
		},
		{
			MethodName: "BindIPA",
			Handler:    _IPAs_BindIPA_Handler,
		},
		{
			MethodName: "UnbindIPA",
			Handler:    _IPAs_UnbindIPA_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/ipa/v1/ipa.proto",
}
