// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/ipa/v1/ipa.proto

package ipa

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	v1 "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents the different states of a IPA.
type IPA_State int32

const (
	// The IPA resource is being created.
	IPA_CREATED IPA_State = 0
	// The IPA resource is being binding.
	IPA_BINDING IPA_State = 1
	// The IPA resource has been actived.
	IPA_ACTIVE IPA_State = 2
	// The IPA resource is being unbinding.
	IPA_UNBINDING IPA_State = 3
	// The IPA resource has been deleted.
	IPA_DELETED IPA_State = 4
	// The IPA resource has been failed.
	IPA_FAILED IPA_State = 5
	// The IPA resource is being updating.
	IPA_UPDATING IPA_State = 6
	// The IPA resource is being provision.
	IPA_PROVISION IPA_State = 7
)

// Enum value maps for IPA_State.
var (
	IPA_State_name = map[int32]string{
		0: "CREATED",
		1: "BINDING",
		2: "ACTIVE",
		3: "UNBINDING",
		4: "DELETED",
		5: "FAILED",
		6: "UPDATING",
		7: "PROVISION",
	}
	IPA_State_value = map[string]int32{
		"CREATED":   0,
		"BINDING":   1,
		"ACTIVE":    2,
		"UNBINDING": 3,
		"DELETED":   4,
		"FAILED":    5,
		"UPDATING":  6,
		"PROVISION": 7,
	}
)

func (x IPA_State) Enum() *IPA_State {
	p := new(IPA_State)
	*p = x
	return p
}

func (x IPA_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (IPA_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_ipa_v1_ipa_proto_enumTypes[0].Descriptor()
}

func (IPA_State) Type() protoreflect.EnumType {
	return &file_network_ipa_v1_ipa_proto_enumTypes[0]
}

func (x IPA_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use IPA_State.Descriptor instead.
func (IPA_State) EnumDescriptor() ([]byte, []int) {
	return file_network_ipa_v1_ipa_proto_rawDescGZIP(), []int{6, 0}
}

// 列举 IPAs 的请求.
// [EN] Request to list IPAs.
type ListIPAsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// List filter.
	Filter string `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// Sort resoults.
	OrderBy string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// The maximum number of items to return.
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// The next_page_token value returned from a previous List request, if any.
	PageToken string `protobuf:"bytes,7,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
}

func (x *ListIPAsRequest) Reset() {
	*x = ListIPAsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_ipa_v1_ipa_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListIPAsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListIPAsRequest) ProtoMessage() {}

func (x *ListIPAsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_ipa_v1_ipa_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListIPAsRequest.ProtoReflect.Descriptor instead.
func (*ListIPAsRequest) Descriptor() ([]byte, []int) {
	return file_network_ipa_v1_ipa_proto_rawDescGZIP(), []int{0}
}

func (x *ListIPAsRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListIPAsRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListIPAsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListIPAsRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListIPAsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListIPAsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListIPAsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// IPA UID的请求.
// [EN] IPA UID Request.
type IPAUIDRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The IPA uid.
	Id string `protobuf:"bytes,4,opt,name=id,proto3" json:"id,omitempty"`
}

func (x *IPAUIDRequest) Reset() {
	*x = IPAUIDRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_ipa_v1_ipa_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IPAUIDRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPAUIDRequest) ProtoMessage() {}

func (x *IPAUIDRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_ipa_v1_ipa_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPAUIDRequest.ProtoReflect.Descriptor instead.
func (*IPAUIDRequest) Descriptor() ([]byte, []int) {
	return file_network_ipa_v1_ipa_proto_rawDescGZIP(), []int{1}
}

func (x *IPAUIDRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *IPAUIDRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *IPAUIDRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *IPAUIDRequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

// CreateIPARequest.
type CreateIPARequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 订购名称
	// [EN] Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// 资源组
	// [EN] Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// 可用区
	// [EN] Available zone.
	Zone string `protobuf:"bytes,6,opt,name=zone,proto3" json:"zone,omitempty"`
	// IPA分配的子网ID
	// [EN] The Subnet uid.
	SubnetId string `protobuf:"bytes,7,opt,name=subnet_id,json=subnetId,proto3" json:"subnet_id,omitempty"`
	// IPA协议类型：IPv4，IPv6 或者 Dual
	// [EN] The protocol of the IPA: IPv4, IPv6 or Dual.
	Protocol string `protobuf:"bytes,8,opt,name=protocol,proto3" json:"protocol,omitempty"`
	// 指定IPA分配的IP，如果为空则自动分配
	// [EN] Specifies the IP address assigned by the IPA. If the IP address is empty, the IP address is assigned automatically.
	Ip string `protobuf:"bytes,9,opt,name=ip,proto3" json:"ip,omitempty"`
}

func (x *CreateIPARequest) Reset() {
	*x = CreateIPARequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_ipa_v1_ipa_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateIPARequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateIPARequest) ProtoMessage() {}

func (x *CreateIPARequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_ipa_v1_ipa_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateIPARequest.ProtoReflect.Descriptor instead.
func (*CreateIPARequest) Descriptor() ([]byte, []int) {
	return file_network_ipa_v1_ipa_proto_rawDescGZIP(), []int{2}
}

func (x *CreateIPARequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *CreateIPARequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *CreateIPARequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateIPARequest) GetSubnetId() string {
	if x != nil {
		return x.SubnetId
	}
	return ""
}

func (x *CreateIPARequest) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *CreateIPARequest) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

// 绑定 IPA 的请求.
// [EN] Binding IPA Request.
type BindingIPARequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The IPA uid.
	Id string `protobuf:"bytes,4,opt,name=id,proto3" json:"id,omitempty"`
	// The binding MAC address
	Mac string `protobuf:"bytes,5,opt,name=mac,proto3" json:"mac,omitempty"`
	// The binding IPMI address
	Ipmi string `protobuf:"bytes,6,opt,name=ipmi,proto3" json:"ipmi,omitempty"`
	// The binding mode: disable, enable
	Mode string `protobuf:"bytes,7,opt,name=mode,proto3" json:"mode,omitempty"`
}

func (x *BindingIPARequest) Reset() {
	*x = BindingIPARequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_ipa_v1_ipa_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *BindingIPARequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*BindingIPARequest) ProtoMessage() {}

func (x *BindingIPARequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_ipa_v1_ipa_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use BindingIPARequest.ProtoReflect.Descriptor instead.
func (*BindingIPARequest) Descriptor() ([]byte, []int) {
	return file_network_ipa_v1_ipa_proto_rawDescGZIP(), []int{3}
}

func (x *BindingIPARequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *BindingIPARequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *BindingIPARequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *BindingIPARequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *BindingIPARequest) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *BindingIPARequest) GetIpmi() string {
	if x != nil {
		return x.Ipmi
	}
	return ""
}

func (x *BindingIPARequest) GetMode() string {
	if x != nil {
		return x.Mode
	}
	return ""
}

// 更新 IPA 的请求.
// [EN] Update IPA Request.
type UpdateIPARequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The IPA uid.
	Id string `protobuf:"bytes,4,opt,name=id,proto3" json:"id,omitempty"`
	// Original binding MAC address
	OriginalMac string `protobuf:"bytes,5,opt,name=original_mac,json=originalMac,proto3" json:"original_mac,omitempty"`
	// New binding MAC address
	NewMac string `protobuf:"bytes,6,opt,name=new_mac,json=newMac,proto3" json:"new_mac,omitempty"`
	// The binding IPMI address
	Ipmi string `protobuf:"bytes,7,opt,name=ipmi,proto3" json:"ipmi,omitempty"`
}

func (x *UpdateIPARequest) Reset() {
	*x = UpdateIPARequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_ipa_v1_ipa_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateIPARequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateIPARequest) ProtoMessage() {}

func (x *UpdateIPARequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_ipa_v1_ipa_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateIPARequest.ProtoReflect.Descriptor instead.
func (*UpdateIPARequest) Descriptor() ([]byte, []int) {
	return file_network_ipa_v1_ipa_proto_rawDescGZIP(), []int{4}
}

func (x *UpdateIPARequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *UpdateIPARequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *UpdateIPARequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateIPARequest) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *UpdateIPARequest) GetOriginalMac() string {
	if x != nil {
		return x.OriginalMac
	}
	return ""
}

func (x *UpdateIPARequest) GetNewMac() string {
	if x != nil {
		return x.NewMac
	}
	return ""
}

func (x *UpdateIPARequest) GetIpmi() string {
	if x != nil {
		return x.Ipmi
	}
	return ""
}

// 列举 IPAs 的响应.
// [EN] Response to list IPAs.
type ListIPAsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// IPA 列表.
	// [EN] IPA list.
	Ipas []*IPA `protobuf:"bytes,1,rep,name=ipas,proto3" json:"ipas,omitempty"`
	// 下一个页面的 token，如果没有更多数据则为空.
	// [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListIPAsResponse) Reset() {
	*x = ListIPAsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_ipa_v1_ipa_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListIPAsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListIPAsResponse) ProtoMessage() {}

func (x *ListIPAsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_ipa_v1_ipa_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListIPAsResponse.ProtoReflect.Descriptor instead.
func (*ListIPAsResponse) Descriptor() ([]byte, []int) {
	return file_network_ipa_v1_ipa_proto_rawDescGZIP(), []int{5}
}

func (x *ListIPAsResponse) GetIpas() []*IPA {
	if x != nil {
		return x.Ipas
	}
	return nil
}

func (x *ListIPAsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListIPAsResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// IPA 实例结构体.
// [EN] IPA entity.
type IPA struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The IPA resource id using the form:
	//     `subscriptions/{subscription_name}/resourceGroups/{resource_group_name}/zones/{zone}/ipas/{ipa}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The IPA resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// ContainerInstance resource display name.
	DisplayName string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// ContainerInstance resource description.
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// The Subnet resource uuid.
	Uid string `protobuf:"bytes,5,opt,name=uid,proto3" json:"uid,omitempty"`
	// The Subnet resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// The id of the user who created the Subnet resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// The id of the user who owns the Subnet resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// Available zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// The current state of the IPA resource.
	State IPA_State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.ipa.v1.IPA_State" json:"state,omitempty"`
	// Sku id.
	SkuId string `protobuf:"bytes,12,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// Tags attached to the IPA resource.
	Tags map[string]string `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Properties of the IPA resource.
	Properties *IPAProperties `protobuf:"bytes,14,opt,name=properties,proto3" json:"properties,omitempty"`
	// Payment information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,15,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	// Indicates whether the IPA resource is deleted or not.
	Deleted bool `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// The time when the IPA resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The time when the IPA resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *IPA) Reset() {
	*x = IPA{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_ipa_v1_ipa_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IPA) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPA) ProtoMessage() {}

func (x *IPA) ProtoReflect() protoreflect.Message {
	mi := &file_network_ipa_v1_ipa_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPA.ProtoReflect.Descriptor instead.
func (*IPA) Descriptor() ([]byte, []int) {
	return file_network_ipa_v1_ipa_proto_rawDescGZIP(), []int{6}
}

func (x *IPA) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *IPA) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *IPA) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *IPA) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *IPA) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *IPA) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *IPA) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *IPA) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *IPA) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *IPA) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *IPA) GetState() IPA_State {
	if x != nil {
		return x.State
	}
	return IPA_CREATED
}

func (x *IPA) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *IPA) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *IPA) GetProperties() *IPAProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *IPA) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

func (x *IPA) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *IPA) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *IPA) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// 资源实际属性.
// [EN] Real resource properties.
type IPAProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// IPA分配的子网ID
	// [EN] The Subnet uid.
	SubnetId string `protobuf:"bytes,1,opt,name=subnet_id,json=subnetId,proto3" json:"subnet_id,omitempty"`
	// IPA协议类型：IPv4，IPv6 或者 Dual
	// [EN] The protocol of the IPA: IPv4, IPv6 or Dual.
	Protocol string `protobuf:"bytes,2,opt,name=protocol,proto3" json:"protocol,omitempty"`
	// IPA分配的IP地址
	// [EN] The IP address of the IPA.
	Ip string `protobuf:"bytes,3,opt,name=ip,proto3" json:"ip,omitempty"`
	// 绑定IPA的MAC地址
	// [EN] The MAC address of the IPA.
	Mac string `protobuf:"bytes,4,opt,name=mac,proto3" json:"mac,omitempty"`
	// 网关IP
	// [EN] The gateway IP.
	GwIp string `protobuf:"bytes,5,opt,name=gw_ip,json=gwIp,proto3" json:"gw_ip,omitempty"`
	// 子网CIDR
	// [EN] The cidr.
	Cidr string `protobuf:"bytes,6,opt,name=cidr,proto3" json:"cidr,omitempty"`
	// IPA分配的MTU
	// [EN] The IPA MTU.
	Mtu int32 `protobuf:"varint,7,opt,name=mtu,proto3" json:"mtu,omitempty"`
	// 绑定IPA的IPMI地址
	// [EN] The IPMI address
	Ipmi string `protobuf:"bytes,8,opt,name=ipmi,proto3" json:"ipmi,omitempty"`
	// NAT网关IP
	// [EN] The nat gateway IP.
	NatGwIp string `protobuf:"bytes,9,opt,name=nat_gw_ip,json=natGwIp,proto3" json:"nat_gw_ip,omitempty"`
}

func (x *IPAProperties) Reset() {
	*x = IPAProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_ipa_v1_ipa_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *IPAProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*IPAProperties) ProtoMessage() {}

func (x *IPAProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_ipa_v1_ipa_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use IPAProperties.ProtoReflect.Descriptor instead.
func (*IPAProperties) Descriptor() ([]byte, []int) {
	return file_network_ipa_v1_ipa_proto_rawDescGZIP(), []int{7}
}

func (x *IPAProperties) GetSubnetId() string {
	if x != nil {
		return x.SubnetId
	}
	return ""
}

func (x *IPAProperties) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *IPAProperties) GetIp() string {
	if x != nil {
		return x.Ip
	}
	return ""
}

func (x *IPAProperties) GetMac() string {
	if x != nil {
		return x.Mac
	}
	return ""
}

func (x *IPAProperties) GetGwIp() string {
	if x != nil {
		return x.GwIp
	}
	return ""
}

func (x *IPAProperties) GetCidr() string {
	if x != nil {
		return x.Cidr
	}
	return ""
}

func (x *IPAProperties) GetMtu() int32 {
	if x != nil {
		return x.Mtu
	}
	return 0
}

func (x *IPAProperties) GetIpmi() string {
	if x != nil {
		return x.Ipmi
	}
	return ""
}

func (x *IPAProperties) GetNatGwIp() string {
	if x != nil {
		return x.NatGwIp
	}
	return ""
}

var File_network_ipa_v1_ipa_proto protoreflect.FileDescriptor

var file_network_ipa_v1_ipa_proto_rawDesc = []byte{
	0x0a, 0x18, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x69, 0x70, 0x61, 0x2f, 0x76, 0x31,
	0x2f, 0x69, 0x70, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1d, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x69, 0x70, 0x61, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c,
	0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f, 0x76,
	0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x74,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xf1, 0x01, 0x0a, 0x0f, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x50, 0x41, 0x73, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65,
	0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f,
	0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65,
	0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b,
	0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f,
	0x6b, 0x65, 0x6e, 0x22, 0x90, 0x01, 0x0a, 0x0d, 0x49, 0x50, 0x41, 0x55, 0x49, 0x44, 0x52, 0x65,
	0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61,
	0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x22, 0xcc, 0x01, 0x0a, 0x10, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x49, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1b, 0x0a, 0x09,
	0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x08, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x09, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x02, 0x69, 0x70, 0x22, 0xce, 0x01, 0x0a, 0x11, 0x42, 0x69, 0x6e, 0x64, 0x69, 0x6e,
	0x67, 0x49, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x10, 0x0a, 0x03,
	0x6d, 0x61, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61, 0x63, 0x12, 0x12,
	0x0a, 0x04, 0x69, 0x70, 0x6d, 0x69, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70,
	0x6d, 0x69, 0x12, 0x12, 0x0a, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x6d, 0x6f, 0x64, 0x65, 0x22, 0xe3, 0x01, 0x0a, 0x10, 0x55, 0x70, 0x64, 0x61, 0x74,
	0x65, 0x49, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x0e, 0x0a, 0x02,
	0x69, 0x64, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x21, 0x0a, 0x0c,
	0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x5f, 0x6d, 0x61, 0x63, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x6f, 0x72, 0x69, 0x67, 0x69, 0x6e, 0x61, 0x6c, 0x4d, 0x61, 0x63, 0x12,
	0x17, 0x0a, 0x07, 0x6e, 0x65, 0x77, 0x5f, 0x6d, 0x61, 0x63, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x06, 0x6e, 0x65, 0x77, 0x4d, 0x61, 0x63, 0x12, 0x12, 0x0a, 0x04, 0x69, 0x70, 0x6d, 0x69,
	0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x6d, 0x69, 0x22, 0x91, 0x01, 0x0a,
	0x10, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x50, 0x41, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73,
	0x65, 0x12, 0x36, 0x0a, 0x04, 0x69, 0x70, 0x61, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x69, 0x70, 0x61, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x50, 0x41, 0x52, 0x04, 0x69, 0x70, 0x61, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78,
	0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65,
	0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65,
	0x22, 0xd1, 0x07, 0x0a, 0x03, 0x49, 0x50, 0x41, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x70, 0x0a, 0x0c,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x4d, 0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d,
	0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78,
	0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29,
	0x24, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20,
	0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75,
	0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74,
	0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65,
	0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f,
	0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49,
	0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f,
	0x6e, 0x65, 0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28,
	0x0e, 0x32, 0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x69, 0x70, 0x61, 0x2e, 0x76,
	0x31, 0x2e, 0x49, 0x50, 0x41, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61,
	0x74, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x04, 0x74, 0x61, 0x67,
	0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x69, 0x70, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x50, 0x41, 0x2e, 0x54, 0x61, 0x67, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x4c, 0x0a, 0x0a, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x69, 0x70, 0x61, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x50, 0x41, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70,
	0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72, 0x64,
	0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x68,
	0x69, 0x67, 0x67, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f,
	0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49,
	0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x10,
	0x20, 0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x3b, 0x0a,
	0x0b, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01,
	0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70,
	0x64, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01,
	0x22, 0x72, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x52, 0x45,
	0x41, 0x54, 0x45, 0x44, 0x10, 0x00, 0x12, 0x0b, 0x0a, 0x07, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e,
	0x47, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02, 0x12,
	0x0d, 0x0a, 0x09, 0x55, 0x4e, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0b,
	0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x44, 0x41, 0x54,
	0x49, 0x4e, 0x47, 0x10, 0x06, 0x12, 0x0d, 0x0a, 0x09, 0x50, 0x52, 0x4f, 0x56, 0x49, 0x53, 0x49,
	0x4f, 0x4e, 0x10, 0x07, 0x22, 0xd5, 0x01, 0x0a, 0x0d, 0x49, 0x50, 0x41, 0x50, 0x72, 0x6f, 0x70,
	0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x1b, 0x0a, 0x09, 0x73, 0x75, 0x62, 0x6e, 0x65, 0x74,
	0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x73, 0x75, 0x62, 0x6e, 0x65,
	0x74, 0x49, 0x64, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f, 0x6c, 0x12,
	0x0e, 0x0a, 0x02, 0x69, 0x70, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x70, 0x12,
	0x10, 0x0a, 0x03, 0x6d, 0x61, 0x63, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6d, 0x61,
	0x63, 0x12, 0x13, 0x0a, 0x05, 0x67, 0x77, 0x5f, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x04, 0x67, 0x77, 0x49, 0x70, 0x12, 0x12, 0x0a, 0x04, 0x63, 0x69, 0x64, 0x72, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x63, 0x69, 0x64, 0x72, 0x12, 0x10, 0x0a, 0x03, 0x6d, 0x74,
	0x75, 0x18, 0x07, 0x20, 0x01, 0x28, 0x05, 0x52, 0x03, 0x6d, 0x74, 0x75, 0x12, 0x12, 0x0a, 0x04,
	0x69, 0x70, 0x6d, 0x69, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x69, 0x70, 0x6d, 0x69,
	0x12, 0x1a, 0x0a, 0x09, 0x6e, 0x61, 0x74, 0x5f, 0x67, 0x77, 0x5f, 0x69, 0x70, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6e, 0x61, 0x74, 0x47, 0x77, 0x49, 0x70, 0x32, 0x81, 0x0c, 0x0a,
	0x04, 0x49, 0x50, 0x41, 0x73, 0x12, 0xe2, 0x01, 0x0a, 0x08, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x50,
	0x41, 0x73, 0x12, 0x2e, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x69, 0x70, 0x61, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x50, 0x41, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x69, 0x70, 0x61, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x49, 0x50, 0x41, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f,
	0x6e, 0x73, 0x65, 0x22, 0x75, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x6f, 0x12, 0x6d, 0x2f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x69, 0x70, 0x61, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76,
	0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f,
	0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f,
	0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b,
	0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x69, 0x70, 0x61, 0x73, 0x12, 0xd6, 0x01, 0x0a, 0x06, 0x47,
	0x65, 0x74, 0x49, 0x50, 0x41, 0x12, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x69,
	0x70, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x49, 0x50, 0x41, 0x55, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x69, 0x70, 0x61,
	0x2e, 0x76, 0x31, 0x2e, 0x49, 0x50, 0x41, 0x22, 0x7a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x74, 0x12,
	0x72, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x69, 0x70, 0x61, 0x2f, 0x64, 0x61,
	0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e,
	0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x69, 0x70, 0x61, 0x73, 0x2f, 0x7b,
	0x69, 0x64, 0x7d, 0x12, 0xda, 0x01, 0x0a, 0x09, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x50,
	0x41, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x69, 0x70, 0x61, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x49, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65,
	0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x69, 0x70, 0x61, 0x2e,
	0x76, 0x31, 0x2e, 0x49, 0x50, 0x41, 0x22, 0x78, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x72, 0x22, 0x6d,
	0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x69, 0x70, 0x61, 0x2f, 0x64, 0x61, 0x74,
	0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65,
	0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x69, 0x70, 0x61, 0x73, 0x3a, 0x01, 0x2a,
	0x12, 0xce, 0x01, 0x0a, 0x09, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x50, 0x41, 0x12, 0x2f,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x69, 0x70, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x49, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x78, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x72, 0x32,
	0x6d, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x69, 0x70, 0x61, 0x2f, 0x64, 0x61,
	0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e,
	0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x69, 0x70, 0x61, 0x73, 0x3a, 0x01,
	0x2a, 0x12, 0xcd, 0x01, 0x0a, 0x09, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x49, 0x50, 0x41, 0x12,
	0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x69, 0x70, 0x61, 0x2e, 0x76, 0x31, 0x2e,
	0x49, 0x50, 0x41, 0x55, 0x49, 0x44, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e,
	0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x7a, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x74, 0x2a, 0x72, 0x2f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x69, 0x70, 0x61, 0x2f, 0x64, 0x61, 0x74, 0x61,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73,
	0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x69, 0x70, 0x61, 0x73, 0x2f, 0x7b, 0x69, 0x64,
	0x7d, 0x12, 0xda, 0x01, 0x0a, 0x07, 0x42, 0x69, 0x6e, 0x64, 0x49, 0x50, 0x41, 0x12, 0x30, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x69, 0x70, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69,
	0x6e, 0x64, 0x69, 0x6e, 0x67, 0x49, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x84, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7e,
	0x22, 0x79, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x69, 0x70, 0x61, 0x2f, 0x64,
	0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f,
	0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x69, 0x70, 0x61, 0x73, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x3a, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x3a, 0x01, 0x2a, 0x12, 0xdf,
	0x01, 0x0a, 0x09, 0x55, 0x6e, 0x62, 0x69, 0x6e, 0x64, 0x49, 0x50, 0x41, 0x12, 0x30, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x69, 0x70, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x42, 0x69, 0x6e,
	0x64, 0x69, 0x6e, 0x67, 0x49, 0x50, 0x41, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x87, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x80, 0x01,
	0x22, 0x7b, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x69, 0x70, 0x61, 0x2f, 0x64,
	0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f,
	0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x69, 0x70, 0x61, 0x73, 0x2f,
	0x7b, 0x69, 0x64, 0x7d, 0x3a, 0x64, 0x65, 0x61, 0x63, 0x74, 0x69, 0x76, 0x65, 0x3a, 0x01, 0x2a,
	0x42, 0x50, 0x5a, 0x4e, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x62, 0x6a, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x61, 0x72, 0x79, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x73,
	0x6f, 0x6e, 0x2d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x69, 0x70, 0x61, 0x2f, 0x76, 0x31, 0x3b, 0x69,
	0x70, 0x61, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_network_ipa_v1_ipa_proto_rawDescOnce sync.Once
	file_network_ipa_v1_ipa_proto_rawDescData = file_network_ipa_v1_ipa_proto_rawDesc
)

func file_network_ipa_v1_ipa_proto_rawDescGZIP() []byte {
	file_network_ipa_v1_ipa_proto_rawDescOnce.Do(func() {
		file_network_ipa_v1_ipa_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_ipa_v1_ipa_proto_rawDescData)
	})
	return file_network_ipa_v1_ipa_proto_rawDescData
}

var file_network_ipa_v1_ipa_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_network_ipa_v1_ipa_proto_msgTypes = make([]protoimpl.MessageInfo, 9)
var file_network_ipa_v1_ipa_proto_goTypes = []interface{}{
	(IPA_State)(0),                // 0: sensetime.core.network.ipa.v1.IPA.State
	(*ListIPAsRequest)(nil),       // 1: sensetime.core.network.ipa.v1.ListIPAsRequest
	(*IPAUIDRequest)(nil),         // 2: sensetime.core.network.ipa.v1.IPAUIDRequest
	(*CreateIPARequest)(nil),      // 3: sensetime.core.network.ipa.v1.CreateIPARequest
	(*BindingIPARequest)(nil),     // 4: sensetime.core.network.ipa.v1.BindingIPARequest
	(*UpdateIPARequest)(nil),      // 5: sensetime.core.network.ipa.v1.UpdateIPARequest
	(*ListIPAsResponse)(nil),      // 6: sensetime.core.network.ipa.v1.ListIPAsResponse
	(*IPA)(nil),                   // 7: sensetime.core.network.ipa.v1.IPA
	(*IPAProperties)(nil),         // 8: sensetime.core.network.ipa.v1.IPAProperties
	nil,                           // 9: sensetime.core.network.ipa.v1.IPA.TagsEntry
	(*v1.OrderInfo)(nil),          // 10: sensetime.core.higgs.common.v1.OrderInfo
	(*timestamppb.Timestamp)(nil), // 11: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),         // 12: google.protobuf.Empty
}
var file_network_ipa_v1_ipa_proto_depIdxs = []int32{
	7,  // 0: sensetime.core.network.ipa.v1.ListIPAsResponse.ipas:type_name -> sensetime.core.network.ipa.v1.IPA
	0,  // 1: sensetime.core.network.ipa.v1.IPA.state:type_name -> sensetime.core.network.ipa.v1.IPA.State
	9,  // 2: sensetime.core.network.ipa.v1.IPA.tags:type_name -> sensetime.core.network.ipa.v1.IPA.TagsEntry
	8,  // 3: sensetime.core.network.ipa.v1.IPA.properties:type_name -> sensetime.core.network.ipa.v1.IPAProperties
	10, // 4: sensetime.core.network.ipa.v1.IPA.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	11, // 5: sensetime.core.network.ipa.v1.IPA.create_time:type_name -> google.protobuf.Timestamp
	11, // 6: sensetime.core.network.ipa.v1.IPA.update_time:type_name -> google.protobuf.Timestamp
	1,  // 7: sensetime.core.network.ipa.v1.IPAs.ListIPAs:input_type -> sensetime.core.network.ipa.v1.ListIPAsRequest
	2,  // 8: sensetime.core.network.ipa.v1.IPAs.GetIPA:input_type -> sensetime.core.network.ipa.v1.IPAUIDRequest
	3,  // 9: sensetime.core.network.ipa.v1.IPAs.CreateIPA:input_type -> sensetime.core.network.ipa.v1.CreateIPARequest
	5,  // 10: sensetime.core.network.ipa.v1.IPAs.UpdateIPA:input_type -> sensetime.core.network.ipa.v1.UpdateIPARequest
	2,  // 11: sensetime.core.network.ipa.v1.IPAs.DeleteIPA:input_type -> sensetime.core.network.ipa.v1.IPAUIDRequest
	4,  // 12: sensetime.core.network.ipa.v1.IPAs.BindIPA:input_type -> sensetime.core.network.ipa.v1.BindingIPARequest
	4,  // 13: sensetime.core.network.ipa.v1.IPAs.UnbindIPA:input_type -> sensetime.core.network.ipa.v1.BindingIPARequest
	6,  // 14: sensetime.core.network.ipa.v1.IPAs.ListIPAs:output_type -> sensetime.core.network.ipa.v1.ListIPAsResponse
	7,  // 15: sensetime.core.network.ipa.v1.IPAs.GetIPA:output_type -> sensetime.core.network.ipa.v1.IPA
	7,  // 16: sensetime.core.network.ipa.v1.IPAs.CreateIPA:output_type -> sensetime.core.network.ipa.v1.IPA
	12, // 17: sensetime.core.network.ipa.v1.IPAs.UpdateIPA:output_type -> google.protobuf.Empty
	12, // 18: sensetime.core.network.ipa.v1.IPAs.DeleteIPA:output_type -> google.protobuf.Empty
	12, // 19: sensetime.core.network.ipa.v1.IPAs.BindIPA:output_type -> google.protobuf.Empty
	12, // 20: sensetime.core.network.ipa.v1.IPAs.UnbindIPA:output_type -> google.protobuf.Empty
	14, // [14:21] is the sub-list for method output_type
	7,  // [7:14] is the sub-list for method input_type
	7,  // [7:7] is the sub-list for extension type_name
	7,  // [7:7] is the sub-list for extension extendee
	0,  // [0:7] is the sub-list for field type_name
}

func init() { file_network_ipa_v1_ipa_proto_init() }
func file_network_ipa_v1_ipa_proto_init() {
	if File_network_ipa_v1_ipa_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_network_ipa_v1_ipa_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListIPAsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_ipa_v1_ipa_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IPAUIDRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_ipa_v1_ipa_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateIPARequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_ipa_v1_ipa_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*BindingIPARequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_ipa_v1_ipa_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateIPARequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_ipa_v1_ipa_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListIPAsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_ipa_v1_ipa_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IPA); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_ipa_v1_ipa_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*IPAProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_ipa_v1_ipa_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   9,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_network_ipa_v1_ipa_proto_goTypes,
		DependencyIndexes: file_network_ipa_v1_ipa_proto_depIdxs,
		EnumInfos:         file_network_ipa_v1_ipa_proto_enumTypes,
		MessageInfos:      file_network_ipa_v1_ipa_proto_msgTypes,
	}.Build()
	File_network_ipa_v1_ipa_proto = out.File
	file_network_ipa_v1_ipa_proto_rawDesc = nil
	file_network_ipa_v1_ipa_proto_goTypes = nil
	file_network_ipa_v1_ipa_proto_depIdxs = nil
}
