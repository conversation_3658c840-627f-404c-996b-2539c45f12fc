// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/ipa/v1/ipa.proto

package ipa

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListIPAsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListIPAsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListIPAsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListIPAsRequestMultiError, or nil if none found.
func (m *ListIPAsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListIPAsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	if len(errors) > 0 {
		return ListIPAsRequestMultiError(errors)
	}

	return nil
}

// ListIPAsRequestMultiError is an error wrapping multiple validation errors
// returned by ListIPAsRequest.ValidateAll() if the designated constraints
// aren't met.
type ListIPAsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListIPAsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListIPAsRequestMultiError) AllErrors() []error { return m }

// ListIPAsRequestValidationError is the validation error returned by
// ListIPAsRequest.Validate if the designated constraints aren't met.
type ListIPAsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListIPAsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListIPAsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListIPAsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListIPAsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListIPAsRequestValidationError) ErrorName() string { return "ListIPAsRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListIPAsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListIPAsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListIPAsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListIPAsRequestValidationError{}

// Validate checks the field values on IPAUIDRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IPAUIDRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IPAUIDRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IPAUIDRequestMultiError, or
// nil if none found.
func (m *IPAUIDRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *IPAUIDRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Id

	if len(errors) > 0 {
		return IPAUIDRequestMultiError(errors)
	}

	return nil
}

// IPAUIDRequestMultiError is an error wrapping multiple validation errors
// returned by IPAUIDRequest.ValidateAll() if the designated constraints
// aren't met.
type IPAUIDRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IPAUIDRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IPAUIDRequestMultiError) AllErrors() []error { return m }

// IPAUIDRequestValidationError is the validation error returned by
// IPAUIDRequest.Validate if the designated constraints aren't met.
type IPAUIDRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IPAUIDRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IPAUIDRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IPAUIDRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IPAUIDRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IPAUIDRequestValidationError) ErrorName() string { return "IPAUIDRequestValidationError" }

// Error satisfies the builtin error interface
func (e IPAUIDRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIPAUIDRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IPAUIDRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IPAUIDRequestValidationError{}

// Validate checks the field values on CreateIPARequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateIPARequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateIPARequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateIPARequestMultiError, or nil if none found.
func (m *CreateIPARequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateIPARequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for SubnetId

	// no validation rules for Protocol

	// no validation rules for Ip

	if len(errors) > 0 {
		return CreateIPARequestMultiError(errors)
	}

	return nil
}

// CreateIPARequestMultiError is an error wrapping multiple validation errors
// returned by CreateIPARequest.ValidateAll() if the designated constraints
// aren't met.
type CreateIPARequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateIPARequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateIPARequestMultiError) AllErrors() []error { return m }

// CreateIPARequestValidationError is the validation error returned by
// CreateIPARequest.Validate if the designated constraints aren't met.
type CreateIPARequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateIPARequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateIPARequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateIPARequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateIPARequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateIPARequestValidationError) ErrorName() string { return "CreateIPARequestValidationError" }

// Error satisfies the builtin error interface
func (e CreateIPARequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateIPARequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateIPARequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateIPARequestValidationError{}

// Validate checks the field values on BindingIPARequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *BindingIPARequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on BindingIPARequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// BindingIPARequestMultiError, or nil if none found.
func (m *BindingIPARequest) ValidateAll() error {
	return m.validate(true)
}

func (m *BindingIPARequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Id

	// no validation rules for Mac

	// no validation rules for Ipmi

	// no validation rules for Mode

	if len(errors) > 0 {
		return BindingIPARequestMultiError(errors)
	}

	return nil
}

// BindingIPARequestMultiError is an error wrapping multiple validation errors
// returned by BindingIPARequest.ValidateAll() if the designated constraints
// aren't met.
type BindingIPARequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m BindingIPARequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m BindingIPARequestMultiError) AllErrors() []error { return m }

// BindingIPARequestValidationError is the validation error returned by
// BindingIPARequest.Validate if the designated constraints aren't met.
type BindingIPARequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e BindingIPARequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e BindingIPARequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e BindingIPARequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e BindingIPARequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e BindingIPARequestValidationError) ErrorName() string {
	return "BindingIPARequestValidationError"
}

// Error satisfies the builtin error interface
func (e BindingIPARequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sBindingIPARequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = BindingIPARequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = BindingIPARequestValidationError{}

// Validate checks the field values on UpdateIPARequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateIPARequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateIPARequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateIPARequestMultiError, or nil if none found.
func (m *UpdateIPARequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateIPARequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Id

	// no validation rules for OriginalMac

	// no validation rules for NewMac

	// no validation rules for Ipmi

	if len(errors) > 0 {
		return UpdateIPARequestMultiError(errors)
	}

	return nil
}

// UpdateIPARequestMultiError is an error wrapping multiple validation errors
// returned by UpdateIPARequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateIPARequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateIPARequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateIPARequestMultiError) AllErrors() []error { return m }

// UpdateIPARequestValidationError is the validation error returned by
// UpdateIPARequest.Validate if the designated constraints aren't met.
type UpdateIPARequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateIPARequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateIPARequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateIPARequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateIPARequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateIPARequestValidationError) ErrorName() string { return "UpdateIPARequestValidationError" }

// Error satisfies the builtin error interface
func (e UpdateIPARequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateIPARequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateIPARequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateIPARequestValidationError{}

// Validate checks the field values on ListIPAsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListIPAsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListIPAsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListIPAsResponseMultiError, or nil if none found.
func (m *ListIPAsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListIPAsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetIpas() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListIPAsResponseValidationError{
						field:  fmt.Sprintf("Ipas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListIPAsResponseValidationError{
						field:  fmt.Sprintf("Ipas[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListIPAsResponseValidationError{
					field:  fmt.Sprintf("Ipas[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListIPAsResponseMultiError(errors)
	}

	return nil
}

// ListIPAsResponseMultiError is an error wrapping multiple validation errors
// returned by ListIPAsResponse.ValidateAll() if the designated constraints
// aren't met.
type ListIPAsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListIPAsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListIPAsResponseMultiError) AllErrors() []error { return m }

// ListIPAsResponseValidationError is the validation error returned by
// ListIPAsResponse.Validate if the designated constraints aren't met.
type ListIPAsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListIPAsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListIPAsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListIPAsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListIPAsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListIPAsResponseValidationError) ErrorName() string { return "ListIPAsResponseValidationError" }

// Error satisfies the builtin error interface
func (e ListIPAsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListIPAsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListIPAsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListIPAsResponseValidationError{}

// Validate checks the field values on IPA with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *IPA) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IPA with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in IPAMultiError, or nil if none found.
func (m *IPA) ValidateAll() error {
	return m.validate(true)
}

func (m *IPA) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
		err := IPAValidationError{
			field:  "DisplayName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_IPA_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
		err := IPAValidationError{
			field:  "DisplayName",
			reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for Uid

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IPAValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IPAValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IPAValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IPAValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IPAValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IPAValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IPAValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IPAValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IPAValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, IPAValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, IPAValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return IPAValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return IPAMultiError(errors)
	}

	return nil
}

// IPAMultiError is an error wrapping multiple validation errors returned by
// IPA.ValidateAll() if the designated constraints aren't met.
type IPAMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IPAMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IPAMultiError) AllErrors() []error { return m }

// IPAValidationError is the validation error returned by IPA.Validate if the
// designated constraints aren't met.
type IPAValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IPAValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IPAValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IPAValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IPAValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IPAValidationError) ErrorName() string { return "IPAValidationError" }

// Error satisfies the builtin error interface
func (e IPAValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIPA.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IPAValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IPAValidationError{}

var _IPA_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on IPAProperties with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *IPAProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on IPAProperties with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in IPAPropertiesMultiError, or
// nil if none found.
func (m *IPAProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *IPAProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubnetId

	// no validation rules for Protocol

	// no validation rules for Ip

	// no validation rules for Mac

	// no validation rules for GwIp

	// no validation rules for Cidr

	// no validation rules for Mtu

	// no validation rules for Ipmi

	// no validation rules for NatGwIp

	if len(errors) > 0 {
		return IPAPropertiesMultiError(errors)
	}

	return nil
}

// IPAPropertiesMultiError is an error wrapping multiple validation errors
// returned by IPAProperties.ValidateAll() if the designated constraints
// aren't met.
type IPAPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m IPAPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m IPAPropertiesMultiError) AllErrors() []error { return m }

// IPAPropertiesValidationError is the validation error returned by
// IPAProperties.Validate if the designated constraints aren't met.
type IPAPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e IPAPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e IPAPropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e IPAPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e IPAPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e IPAPropertiesValidationError) ErrorName() string { return "IPAPropertiesValidationError" }

// Error satisfies the builtin error interface
func (e IPAPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sIPAProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = IPAPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = IPAPropertiesValidationError{}
