// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/dc/v1/dcvc.proto

package dc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DCVCsClient is the client API for DCVCs service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DCVCsClient interface {
	// 创建一个 DCVC.
	// [EN] Create a DCVC.a
	CreateDCVC(ctx context.Context, in *CreateDCVCRequest, opts ...grpc.CallOption) (*DCVC, error)
	// 删除一个 DCVC.
	// [EN] Delete a DCVC.
	DeleteDCVC(ctx context.Context, in *DeleteDCVCRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新 DCVC 可编辑字段.
	// [EN] Update DCVC editable properties.
	UpdateDCVC(ctx context.Context, in *UpdateDCVCRequest, opts ...grpc.CallOption) (*DCVC, error)
	// 获取符合请求的一个 DCVC.
	// [EN] Get a requested DCVC.
	GetDCVC(ctx context.Context, in *GetDCVCRequest, opts ...grpc.CallOption) (*DCVC, error)
	// 列举符合请求的所有 DCVCs.
	// [EN] List requested DCVCs.
	ListDCVCs(ctx context.Context, in *ListDCVCsRequest, opts ...grpc.CallOption) (*ListDCVCsResponse, error)
}

type dCVCsClient struct {
	cc grpc.ClientConnInterface
}

func NewDCVCsClient(cc grpc.ClientConnInterface) DCVCsClient {
	return &dCVCsClient{cc}
}

func (c *dCVCsClient) CreateDCVC(ctx context.Context, in *CreateDCVCRequest, opts ...grpc.CallOption) (*DCVC, error) {
	out := new(DCVC)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.dc.v1.DCVCs/CreateDCVC", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dCVCsClient) DeleteDCVC(ctx context.Context, in *DeleteDCVCRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.dc.v1.DCVCs/DeleteDCVC", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dCVCsClient) UpdateDCVC(ctx context.Context, in *UpdateDCVCRequest, opts ...grpc.CallOption) (*DCVC, error) {
	out := new(DCVC)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.dc.v1.DCVCs/UpdateDCVC", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dCVCsClient) GetDCVC(ctx context.Context, in *GetDCVCRequest, opts ...grpc.CallOption) (*DCVC, error) {
	out := new(DCVC)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.dc.v1.DCVCs/GetDCVC", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dCVCsClient) ListDCVCs(ctx context.Context, in *ListDCVCsRequest, opts ...grpc.CallOption) (*ListDCVCsResponse, error) {
	out := new(ListDCVCsResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.dc.v1.DCVCs/ListDCVCs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DCVCsServer is the server API for DCVCs service.
// All implementations must embed UnimplementedDCVCsServer
// for forward compatibility
type DCVCsServer interface {
	// 创建一个 DCVC.
	// [EN] Create a DCVC.a
	CreateDCVC(context.Context, *CreateDCVCRequest) (*DCVC, error)
	// 删除一个 DCVC.
	// [EN] Delete a DCVC.
	DeleteDCVC(context.Context, *DeleteDCVCRequest) (*emptypb.Empty, error)
	// 更新 DCVC 可编辑字段.
	// [EN] Update DCVC editable properties.
	UpdateDCVC(context.Context, *UpdateDCVCRequest) (*DCVC, error)
	// 获取符合请求的一个 DCVC.
	// [EN] Get a requested DCVC.
	GetDCVC(context.Context, *GetDCVCRequest) (*DCVC, error)
	// 列举符合请求的所有 DCVCs.
	// [EN] List requested DCVCs.
	ListDCVCs(context.Context, *ListDCVCsRequest) (*ListDCVCsResponse, error)
	mustEmbedUnimplementedDCVCsServer()
}

// UnimplementedDCVCsServer must be embedded to have forward compatible implementations.
type UnimplementedDCVCsServer struct {
}

func (UnimplementedDCVCsServer) CreateDCVC(context.Context, *CreateDCVCRequest) (*DCVC, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDCVC not implemented")
}
func (UnimplementedDCVCsServer) DeleteDCVC(context.Context, *DeleteDCVCRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDCVC not implemented")
}
func (UnimplementedDCVCsServer) UpdateDCVC(context.Context, *UpdateDCVCRequest) (*DCVC, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDCVC not implemented")
}
func (UnimplementedDCVCsServer) GetDCVC(context.Context, *GetDCVCRequest) (*DCVC, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDCVC not implemented")
}
func (UnimplementedDCVCsServer) ListDCVCs(context.Context, *ListDCVCsRequest) (*ListDCVCsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDCVCs not implemented")
}
func (UnimplementedDCVCsServer) mustEmbedUnimplementedDCVCsServer() {}

// UnsafeDCVCsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DCVCsServer will
// result in compilation errors.
type UnsafeDCVCsServer interface {
	mustEmbedUnimplementedDCVCsServer()
}

func RegisterDCVCsServer(s grpc.ServiceRegistrar, srv DCVCsServer) {
	s.RegisterService(&DCVCs_ServiceDesc, srv)
}

func _DCVCs_CreateDCVC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDCVCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DCVCsServer).CreateDCVC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.dc.v1.DCVCs/CreateDCVC",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DCVCsServer).CreateDCVC(ctx, req.(*CreateDCVCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DCVCs_DeleteDCVC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDCVCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DCVCsServer).DeleteDCVC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.dc.v1.DCVCs/DeleteDCVC",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DCVCsServer).DeleteDCVC(ctx, req.(*DeleteDCVCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DCVCs_UpdateDCVC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDCVCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DCVCsServer).UpdateDCVC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.dc.v1.DCVCs/UpdateDCVC",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DCVCsServer).UpdateDCVC(ctx, req.(*UpdateDCVCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DCVCs_GetDCVC_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDCVCRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DCVCsServer).GetDCVC(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.dc.v1.DCVCs/GetDCVC",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DCVCsServer).GetDCVC(ctx, req.(*GetDCVCRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DCVCs_ListDCVCs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDCVCsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DCVCsServer).ListDCVCs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.dc.v1.DCVCs/ListDCVCs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DCVCsServer).ListDCVCs(ctx, req.(*ListDCVCsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DCVCs_ServiceDesc is the grpc.ServiceDesc for DCVCs service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DCVCs_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.dc.v1.DCVCs",
	HandlerType: (*DCVCsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDCVC",
			Handler:    _DCVCs_CreateDCVC_Handler,
		},
		{
			MethodName: "DeleteDCVC",
			Handler:    _DCVCs_DeleteDCVC_Handler,
		},
		{
			MethodName: "UpdateDCVC",
			Handler:    _DCVCs_UpdateDCVC_Handler,
		},
		{
			MethodName: "GetDCVC",
			Handler:    _DCVCs_GetDCVC_Handler,
		},
		{
			MethodName: "ListDCVCs",
			Handler:    _DCVCs_ListDCVCs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/dc/v1/dcvc.proto",
}
