// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/dc/v1/dcvc.proto

package dc

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/api/annotations"
	v1 "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents the different states of a DCVC.
type DCVC_State int32

const (
	// The DCVC resource is being created.
	DCVC_CREATING DCVC_State = 0
	// The DCVC resource is being updated.
	DCVC_UPDATING DCVC_State = 1
	// The DCVC resource has been active.
	DCVC_ACTIVE DCVC_State = 2
	// The DCVC resource is being deleting.
	DCVC_DELETING DCVC_State = 3
	// The DCVC resource has been deleted.
	DCVC_DELETED DCVC_State = 4
	// The DCVC resource has been failed.
	DCVC_FAILED DCVC_State = 5
)

// Enum value maps for DCVC_State.
var (
	DCVC_State_name = map[int32]string{
		0: "CREATING",
		1: "UPDATING",
		2: "ACTIVE",
		3: "DELETING",
		4: "DELETED",
		5: "FAILED",
	}
	DCVC_State_value = map[string]int32{
		"CREATING": 0,
		"UPDATING": 1,
		"ACTIVE":   2,
		"DELETING": 3,
		"DELETED":  4,
		"FAILED":   5,
	}
)

func (x DCVC_State) Enum() *DCVC_State {
	p := new(DCVC_State)
	*p = x
	return p
}

func (x DCVC_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DCVC_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_dc_v1_dcvc_proto_enumTypes[0].Descriptor()
}

func (DCVC_State) Type() protoreflect.EnumType {
	return &file_network_dc_v1_dcvc_proto_enumTypes[0]
}

func (x DCVC_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DCVC_State.Descriptor instead.
func (DCVC_State) EnumDescriptor() ([]byte, []int) {
	return file_network_dc_v1_dcvc_proto_rawDescGZIP(), []int{7, 0}
}

// DCVC 创建字段.
// [EN] DCVC creation properties.
type CreateDCVCRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Zone, todo: add validation
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	DcvcName string `protobuf:"bytes,4,opt,name=dcvc_name,json=dcvcName,proto3" json:"dcvc_name,omitempty"`
	// The DCVC resource to create.
	Dcvc *DCVC `protobuf:"bytes,5,opt,name=dcvc,proto3" json:"dcvc,omitempty"`
}

func (x *CreateDCVCRequest) Reset() {
	*x = CreateDCVCRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcvc_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDCVCRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDCVCRequest) ProtoMessage() {}

func (x *CreateDCVCRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcvc_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDCVCRequest.ProtoReflect.Descriptor instead.
func (*CreateDCVCRequest) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcvc_proto_rawDescGZIP(), []int{0}
}

func (x *CreateDCVCRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *CreateDCVCRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *CreateDCVCRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateDCVCRequest) GetDcvcName() string {
	if x != nil {
		return x.DcvcName
	}
	return ""
}

func (x *CreateDCVCRequest) GetDcvc() *DCVC {
	if x != nil {
		return x.Dcvc
	}
	return nil
}

// 删除某一个 DCVC 的请求.
// [EN] Request to delete a DCVC.
type DeleteDCVCRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	DcvcName string `protobuf:"bytes,4,opt,name=dcvc_name,json=dcvcName,proto3" json:"dcvc_name,omitempty"`
}

func (x *DeleteDCVCRequest) Reset() {
	*x = DeleteDCVCRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcvc_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDCVCRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDCVCRequest) ProtoMessage() {}

func (x *DeleteDCVCRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcvc_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDCVCRequest.ProtoReflect.Descriptor instead.
func (*DeleteDCVCRequest) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcvc_proto_rawDescGZIP(), []int{1}
}

func (x *DeleteDCVCRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DeleteDCVCRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *DeleteDCVCRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteDCVCRequest) GetDcvcName() string {
	if x != nil {
		return x.DcvcName
	}
	return ""
}

// DCVC 可编辑字段.
// [EN] DCVC editable properties.
type UpdateDCVCRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	DcvcName string `protobuf:"bytes,4,opt,name=dcvc_name,json=dcvcName,proto3" json:"dcvc_name,omitempty"`
	// The DCVC resource to update.
	Dcvc *DCVCUpdateProperties `protobuf:"bytes,5,opt,name=dcvc,proto3" json:"dcvc,omitempty"`
	// update_mask
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,6,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateDCVCRequest) Reset() {
	*x = UpdateDCVCRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcvc_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDCVCRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDCVCRequest) ProtoMessage() {}

func (x *UpdateDCVCRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcvc_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDCVCRequest.ProtoReflect.Descriptor instead.
func (*UpdateDCVCRequest) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcvc_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateDCVCRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *UpdateDCVCRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *UpdateDCVCRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateDCVCRequest) GetDcvcName() string {
	if x != nil {
		return x.DcvcName
	}
	return ""
}

func (x *UpdateDCVCRequest) GetDcvc() *DCVCUpdateProperties {
	if x != nil {
		return x.Dcvc
	}
	return nil
}

func (x *UpdateDCVCRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// [zh] 更新DCVC资源.
// [en] Update DCVC properties.
type DCVCUpdateProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// resource display name
	DisplayName *string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3,oneof" json:"display_name,omitempty"`
	// ContainerInstance resource description
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// Properties of the DCVC resource.
	Properties *DCVCProperties `protobuf:"bytes,3,opt,name=properties,proto3" json:"properties,omitempty"`
}

func (x *DCVCUpdateProperties) Reset() {
	*x = DCVCUpdateProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcvc_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DCVCUpdateProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DCVCUpdateProperties) ProtoMessage() {}

func (x *DCVCUpdateProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcvc_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DCVCUpdateProperties.ProtoReflect.Descriptor instead.
func (*DCVCUpdateProperties) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcvc_proto_rawDescGZIP(), []int{3}
}

func (x *DCVCUpdateProperties) GetDisplayName() string {
	if x != nil && x.DisplayName != nil {
		return *x.DisplayName
	}
	return ""
}

func (x *DCVCUpdateProperties) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DCVCUpdateProperties) GetProperties() *DCVCProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

// 获取某一个 DCVC 的请求.
// [EN] Request to get a DCVC.
type GetDCVCRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	DcvcName string `protobuf:"bytes,4,opt,name=dcvc_name,json=dcvcName,proto3" json:"dcvc_name,omitempty"`
}

func (x *GetDCVCRequest) Reset() {
	*x = GetDCVCRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcvc_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDCVCRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDCVCRequest) ProtoMessage() {}

func (x *GetDCVCRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcvc_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDCVCRequest.ProtoReflect.Descriptor instead.
func (*GetDCVCRequest) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcvc_proto_rawDescGZIP(), []int{4}
}

func (x *GetDCVCRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetDCVCRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetDCVCRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetDCVCRequest) GetDcvcName() string {
	if x != nil {
		return x.DcvcName
	}
	return ""
}

// 列举 DCVCs 的请求.
// [EN] Request to list DCVCs.
type ListDCVCsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// List filter.
	Filter string `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// Sort resoults.
	OrderBy string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// The maximum number of items to return.
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// The next_page_token value returned from a previous List request, if any.
	PageToken string `protobuf:"bytes,7,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// tenant_id.
	TenantId string `protobuf:"bytes,8,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
}

func (x *ListDCVCsRequest) Reset() {
	*x = ListDCVCsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcvc_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDCVCsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDCVCsRequest) ProtoMessage() {}

func (x *ListDCVCsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcvc_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDCVCsRequest.ProtoReflect.Descriptor instead.
func (*ListDCVCsRequest) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcvc_proto_rawDescGZIP(), []int{5}
}

func (x *ListDCVCsRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListDCVCsRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListDCVCsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListDCVCsRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListDCVCsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListDCVCsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListDCVCsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListDCVCsRequest) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

// 列举 DCVCs 的响应.
// [EN] Response to list DCVCs.
type ListDCVCsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// DCVC 列表.
	// [EN] DCVC list.
	Dcvcs []*DCVC `protobuf:"bytes,1,rep,name=dcvcs,proto3" json:"dcvcs,omitempty"`
	// 下一个页面的 token，如果没有更多数据则为空.
	// [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListDCVCsResponse) Reset() {
	*x = ListDCVCsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcvc_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDCVCsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDCVCsResponse) ProtoMessage() {}

func (x *ListDCVCsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcvc_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDCVCsResponse.ProtoReflect.Descriptor instead.
func (*ListDCVCsResponse) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcvc_proto_rawDescGZIP(), []int{6}
}

func (x *ListDCVCsResponse) GetDcvcs() []*DCVC {
	if x != nil {
		return x.Dcvcs
	}
	return nil
}

func (x *ListDCVCsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListDCVCsResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// DCVC 实例结构体.
// [EN] DCVC entity.
type DCVC struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The DCVC resource id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// resource display name
	DisplayName string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// ContainerInstance resource description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// The DCVC resource uuid.
	Uid string `protobuf:"bytes,5,opt,name=uid,proto3" json:"uid,omitempty"`
	// The DCVC resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// The id of the user who created the DCVC resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// The id of the user who owns the DCVC resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// Zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// The current state of the DCVC resource.
	State DCVC_State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.dc.v1.DCVC_State" json:"state,omitempty"`
	// Sku id.
	SkuId string `protobuf:"bytes,12,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// Tags attached to the DCVC resource.
	Tags map[string]string `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Properties of the DCVC resource.
	Properties *DCVCProperties `protobuf:"bytes,14,opt,name=properties,proto3" json:"properties,omitempty"`
	// Payment information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,15,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	// Indicates whether the DCVC resource is deleted or not.
	Deleted bool `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// The time when the DCVC resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The time when the DCVC resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *DCVC) Reset() {
	*x = DCVC{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcvc_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DCVC) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DCVC) ProtoMessage() {}

func (x *DCVC) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcvc_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DCVC.ProtoReflect.Descriptor instead.
func (*DCVC) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcvc_proto_rawDescGZIP(), []int{7}
}

func (x *DCVC) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DCVC) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DCVC) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *DCVC) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DCVC) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *DCVC) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *DCVC) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *DCVC) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *DCVC) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *DCVC) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DCVC) GetState() DCVC_State {
	if x != nil {
		return x.State
	}
	return DCVC_CREATING
}

func (x *DCVC) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *DCVC) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *DCVC) GetProperties() *DCVCProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *DCVC) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

func (x *DCVC) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *DCVC) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *DCVC) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// 资源实际属性.
// [EN] Real resource properties.
type DCVCProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 路由类型.
	// [en] route type.
	RouteType string `protobuf:"bytes,1,opt,name=route_type,json=routeType,proto3" json:"route_type,omitempty"`
	// LimitRateItems
	LimitRateItems *LimitRateItems `protobuf:"bytes,2,opt,name=limit_rate_items,json=limitRateItems,proto3" json:"limit_rate_items,omitempty"`
	// [zh] 物理线路 id.
	// [en] dcpl id.
	DcplId string `protobuf:"bytes,3,opt,name=dcpl_id,json=dcplId,proto3" json:"dcpl_id,omitempty"`
	// [zh] 专线网关 name.
	// [en] dcgw name.
	DcgwName string `protobuf:"bytes,4,opt,name=dcgw_name,json=dcgwName,proto3" json:"dcgw_name,omitempty"`
	// [zh] 虚拟通道 vlan id.
	// [en] vlan id
	VlanId string `protobuf:"bytes,5,opt,name=vlan_id,json=vlanId,proto3" json:"vlan_id,omitempty"`
	// [zh]  客户IDC侧互联ip.
	// [en]  connect ip of customer.
	CustomerConnectIp string `protobuf:"bytes,6,opt,name=customer_connect_ip,json=customerConnectIp,proto3" json:"customer_connect_ip,omitempty"`
	// [zh]  大装置侧互联ip.
	// [en]  connect ip of sensecore.
	SensecoreConnectIp string `protobuf:"bytes,7,opt,name=sensecore_connect_ip,json=sensecoreConnectIp,proto3" json:"sensecore_connect_ip,omitempty"`
	// [zh] 专线网关的 vxlan name.
	// [en] vxlan name of dcgw
	VxlanName string `protobuf:"bytes,8,opt,name=vxlan_name,json=vxlanName,proto3" json:"vxlan_name,omitempty"`
	// [zh] 专线网关的 vxlan id.
	// [en] vxlan id of dcgw
	VxlanId string `protobuf:"bytes,9,opt,name=vxlan_id,json=vxlanId,proto3" json:"vxlan_id,omitempty"`
	// [zh] 专线网关本端的 vtep ip.
	// [en] local vtep ip of dcgw.
	LocalVtepIp string `protobuf:"bytes,10,opt,name=local_vtep_ip,json=localVtepIp,proto3" json:"local_vtep_ip,omitempty"`
	// [zh] 专线网关远端的 vtep ip.
	// [en] remote vtep ip of dcgw.
	RemoteVtepIp string `protobuf:"bytes,11,opt,name=remote_vtep_ip,json=remoteVtepIp,proto3" json:"remote_vtep_ip,omitempty"`
	// [zh] 专线网关本端的 vxlan ip.
	// [en] local vxlan ip of dcgw
	LocalVxlanIp string `protobuf:"bytes,12,opt,name=local_vxlan_ip,json=localVxlanIp,proto3" json:"local_vxlan_ip,omitempty"`
	// [zh] 专线网关远端的 vxlan ip.
	// [en] local dc asw vxlan ip.
	RemoteVxlanIp string `protobuf:"bytes,13,opt,name=remote_vxlan_ip,json=remoteVxlanIp,proto3" json:"remote_vxlan_ip,omitempty"`
	// [zh]  专线网关本端的 CIDR.
	// [en] local cidr of dcgw.
	LocalCidr []string `protobuf:"bytes,14,rep,name=local_cidr,json=localCidr,proto3" json:"local_cidr,omitempty"`
	// [zh] 专线网关远端的 CIDR.
	// [en] remote cidr of dcgw.
	RemoteCidr []string `protobuf:"bytes,15,rep,name=remote_cidr,json=remoteCidr,proto3" json:"remote_cidr,omitempty"`
	// [zh] 专线网关的 subscription name.
	// [en] subscription name of dcgw
	SubscriptionName string `protobuf:"bytes,16,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 专线网关的 resource group name.
	// [en] resource group name of dcgw
	ResourceGroupName string `protobuf:"bytes,17,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// HealtCheck.
	HealthCheck *HealtCheck `protobuf:"bytes,18,opt,name=health_check,json=healthCheck,proto3" json:"health_check,omitempty"`
}

func (x *DCVCProperties) Reset() {
	*x = DCVCProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcvc_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DCVCProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DCVCProperties) ProtoMessage() {}

func (x *DCVCProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcvc_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DCVCProperties.ProtoReflect.Descriptor instead.
func (*DCVCProperties) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcvc_proto_rawDescGZIP(), []int{8}
}

func (x *DCVCProperties) GetRouteType() string {
	if x != nil {
		return x.RouteType
	}
	return ""
}

func (x *DCVCProperties) GetLimitRateItems() *LimitRateItems {
	if x != nil {
		return x.LimitRateItems
	}
	return nil
}

func (x *DCVCProperties) GetDcplId() string {
	if x != nil {
		return x.DcplId
	}
	return ""
}

func (x *DCVCProperties) GetDcgwName() string {
	if x != nil {
		return x.DcgwName
	}
	return ""
}

func (x *DCVCProperties) GetVlanId() string {
	if x != nil {
		return x.VlanId
	}
	return ""
}

func (x *DCVCProperties) GetCustomerConnectIp() string {
	if x != nil {
		return x.CustomerConnectIp
	}
	return ""
}

func (x *DCVCProperties) GetSensecoreConnectIp() string {
	if x != nil {
		return x.SensecoreConnectIp
	}
	return ""
}

func (x *DCVCProperties) GetVxlanName() string {
	if x != nil {
		return x.VxlanName
	}
	return ""
}

func (x *DCVCProperties) GetVxlanId() string {
	if x != nil {
		return x.VxlanId
	}
	return ""
}

func (x *DCVCProperties) GetLocalVtepIp() string {
	if x != nil {
		return x.LocalVtepIp
	}
	return ""
}

func (x *DCVCProperties) GetRemoteVtepIp() string {
	if x != nil {
		return x.RemoteVtepIp
	}
	return ""
}

func (x *DCVCProperties) GetLocalVxlanIp() string {
	if x != nil {
		return x.LocalVxlanIp
	}
	return ""
}

func (x *DCVCProperties) GetRemoteVxlanIp() string {
	if x != nil {
		return x.RemoteVxlanIp
	}
	return ""
}

func (x *DCVCProperties) GetLocalCidr() []string {
	if x != nil {
		return x.LocalCidr
	}
	return nil
}

func (x *DCVCProperties) GetRemoteCidr() []string {
	if x != nil {
		return x.RemoteCidr
	}
	return nil
}

func (x *DCVCProperties) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DCVCProperties) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *DCVCProperties) GetHealthCheck() *HealtCheck {
	if x != nil {
		return x.HealthCheck
	}
	return nil
}

// 限速项
type LimitRateItems struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// DCGW 上行带宽限制，单位: M/s.
	UpStreamBw int32 `protobuf:"varint,1,opt,name=up_stream_bw,json=upStreamBw,proto3" json:"up_stream_bw,omitempty"`
	// DCGW 下行带宽限制，单位: M/s
	DownStreamBw int32 `protobuf:"varint,2,opt,name=down_stream_bw,json=downStreamBw,proto3" json:"down_stream_bw,omitempty"`
}

func (x *LimitRateItems) Reset() {
	*x = LimitRateItems{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcvc_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *LimitRateItems) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*LimitRateItems) ProtoMessage() {}

func (x *LimitRateItems) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcvc_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use LimitRateItems.ProtoReflect.Descriptor instead.
func (*LimitRateItems) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcvc_proto_rawDescGZIP(), []int{9}
}

func (x *LimitRateItems) GetUpStreamBw() int32 {
	if x != nil {
		return x.UpStreamBw
	}
	return 0
}

func (x *LimitRateItems) GetDownStreamBw() int32 {
	if x != nil {
		return x.DownStreamBw
	}
	return 0
}

// HealtCheck
type HealtCheck struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 开启/关闭健康检查标志。默认为：关闭。
	// [en] enable, default false
	Enable bool `protobuf:"varint,1,opt,name=enable,proto3" json:"enable,omitempty"`
	// [zh] 健康检查的目标IP地址。默认值为空字符串，空字符串表示使用此虚拟通道的互联IP（IDC侧），即：CustomerIDCConnectIP。
	// [en] target ip, default customer_connect_ip
	TargetIp string `protobuf:"bytes,2,opt,name=target_ip,json=targetIp,proto3" json:"target_ip,omitempty"`
	// [zh] 健康检查的间隔时间，单位：秒。默认值为2秒。支持[2,5]内整数.
	// [en] health check interval, default 2s, effective value 2 ~ 5
	Interval *int32 `protobuf:"varint,3,opt,name=interval,proto3,oneof" json:"interval,omitempty"`
	// [zh] 健康检查成功/失败判定次数。单位：次。默认值为5次。有效值为3 ~ 8次。
	// [en] health check counter, default 5, effective value 3 ~ 8
	Counter *int32 `protobuf:"varint,4,opt,name=counter,proto3,oneof" json:"counter,omitempty"`
	// [zh] 健康检查的结果，"SUCCESS" --成功，"FAIL"--失败，"CHECKING" --- 检测中 （虚拟通道尚未完成完整的1轮检测，无法给出成功/失败结果。默认情况下2x5=10秒）
	// [en] run status
	RunStatus string `protobuf:"bytes,5,opt,name=run_status,json=runStatus,proto3" json:"run_status,omitempty"`
	//[zh]  健康检查结果最后变化时间。表示最近从“成功”到“失败”，或者“失败”到“成功”，或者“检测中”到“成功”/“失败”的切换时刻。从这个时间至今，都是没有状态变化。
	// [en] run time
	RunTime *timestamppb.Timestamp `protobuf:"bytes,6,opt,name=run_time,json=runTime,proto3" json:"run_time,omitempty"`
}

func (x *HealtCheck) Reset() {
	*x = HealtCheck{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcvc_proto_msgTypes[10]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *HealtCheck) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*HealtCheck) ProtoMessage() {}

func (x *HealtCheck) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcvc_proto_msgTypes[10]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use HealtCheck.ProtoReflect.Descriptor instead.
func (*HealtCheck) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcvc_proto_rawDescGZIP(), []int{10}
}

func (x *HealtCheck) GetEnable() bool {
	if x != nil {
		return x.Enable
	}
	return false
}

func (x *HealtCheck) GetTargetIp() string {
	if x != nil {
		return x.TargetIp
	}
	return ""
}

func (x *HealtCheck) GetInterval() int32 {
	if x != nil && x.Interval != nil {
		return *x.Interval
	}
	return 0
}

func (x *HealtCheck) GetCounter() int32 {
	if x != nil && x.Counter != nil {
		return *x.Counter
	}
	return 0
}

func (x *HealtCheck) GetRunStatus() string {
	if x != nil {
		return x.RunStatus
	}
	return ""
}

func (x *HealtCheck) GetRunTime() *timestamppb.Timestamp {
	if x != nil {
		return x.RunTime
	}
	return nil
}

var File_network_dc_v1_dcvc_proto protoreflect.FileDescriptor

var file_network_dc_v1_dcvc_proto_rawDesc = []byte{
	0x0a, 0x18, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x64, 0x63, 0x2f, 0x76, 0x31, 0x2f,
	0x64, 0x63, 0x76, 0x63, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x68, 0x69,
	0x67, 0x67, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x85,
	0x02, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x43, 0x56, 0x43, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x47, 0x0a, 0x09, 0x64, 0x63, 0x76, 0x63, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18,
	0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x5d, 0x29, 0x3f, 0x24, 0x52, 0x08, 0x64, 0x63, 0x76, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x36,
	0x0a, 0x04, 0x64, 0x63, 0x76, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43, 0x56, 0x43,
	0x52, 0x04, 0x64, 0x63, 0x76, 0x63, 0x22, 0xa1, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x44, 0x43, 0x56, 0x43, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x64, 0x63, 0x76, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x63, 0x76, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa6, 0x02, 0x0a, 0x11, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x43, 0x56, 0x43, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a,
	0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x63, 0x76, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x63, 0x76, 0x63, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x46,
	0x0a, 0x04, 0x64, 0x63, 0x76, 0x63, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43, 0x56, 0x43,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x52, 0x04, 0x64, 0x63, 0x76, 0x63, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d,
	0x61, 0x73, 0x6b, 0x22, 0x8e, 0x02, 0x0a, 0x14, 0x44, 0x43, 0x56, 0x43, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x75, 0x0a, 0x0c,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x4d, 0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d,
	0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78,
	0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29,
	0x24, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43, 0x56, 0x43, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0x9e, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x44, 0x43, 0x56, 0x43,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x63, 0x76, 0x63,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x63, 0x76,
	0x63, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x8f, 0x02, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x43,
	0x56, 0x43, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x94, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x43, 0x56, 0x43, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a,
	0x05, 0x64, 0x63, 0x76, 0x63, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43, 0x56, 0x43,
	0x52, 0x05, 0x64, 0x63, 0x76, 0x63, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xb6,
	0x07, 0x0a, 0x04, 0x44, 0x43, 0x56, 0x43, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x70, 0x0a, 0x0c, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x4d, 0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d,
	0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b,
	0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24,
	0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x43, 0x56, 0x43, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43, 0x56, 0x43, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x4c, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43,
	0x56, 0x43, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x68, 0x69,
	0x67, 0x67, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x56, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x44, 0x41, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02,
	0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0b,
	0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x22, 0x9c, 0x06, 0x0a, 0x0e, 0x44, 0x43, 0x56, 0x43,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x6f,
	0x75, 0x74, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x6f, 0x75, 0x74, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x56, 0x0a, 0x10, 0x6c, 0x69, 0x6d,
	0x69, 0x74, 0x5f, 0x72, 0x61, 0x74, 0x65, 0x5f, 0x69, 0x74, 0x65, 0x6d, 0x73, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x52, 0x0e, 0x6c, 0x69, 0x6d, 0x69, 0x74, 0x52, 0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d,
	0x73, 0x12, 0x17, 0x0a, 0x07, 0x64, 0x63, 0x70, 0x6c, 0x5f, 0x69, 0x64, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x64, 0x63, 0x70, 0x6c, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x63,
	0x67, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64,
	0x63, 0x67, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x17, 0x0a, 0x07, 0x76, 0x6c, 0x61, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x76, 0x6c, 0x61, 0x6e, 0x49, 0x64,
	0x12, 0x2e, 0x0a, 0x13, 0x63, 0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x5f, 0x63, 0x6f, 0x6e,
	0x6e, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x63,
	0x75, 0x73, 0x74, 0x6f, 0x6d, 0x65, 0x72, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74, 0x49, 0x70,
	0x12, 0x30, 0x0a, 0x14, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x63, 0x6f, 0x72, 0x65, 0x5f, 0x63, 0x6f,
	0x6e, 0x6e, 0x65, 0x63, 0x74, 0x5f, 0x69, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x12,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x63, 0x6f, 0x72, 0x65, 0x43, 0x6f, 0x6e, 0x6e, 0x65, 0x63, 0x74,
	0x49, 0x70, 0x12, 0x22, 0x0a, 0x0a, 0x76, 0x78, 0x6c, 0x61, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x09, 0x76, 0x78, 0x6c,
	0x61, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x1e, 0x0a, 0x08, 0x76, 0x78, 0x6c, 0x61, 0x6e, 0x5f,
	0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x07, 0x76,
	0x78, 0x6c, 0x61, 0x6e, 0x49, 0x64, 0x12, 0x27, 0x0a, 0x0d, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f,
	0x76, 0x74, 0x65, 0x70, 0x5f, 0x69, 0x70, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x03, 0x52, 0x0b, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x56, 0x74, 0x65, 0x70, 0x49, 0x70, 0x12,
	0x29, 0x0a, 0x0e, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x76, 0x74, 0x65, 0x70, 0x5f, 0x69,
	0x70, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0c, 0x72, 0x65,
	0x6d, 0x6f, 0x74, 0x65, 0x56, 0x74, 0x65, 0x70, 0x49, 0x70, 0x12, 0x29, 0x0a, 0x0e, 0x6c, 0x6f,
	0x63, 0x61, 0x6c, 0x5f, 0x76, 0x78, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x70, 0x18, 0x0c, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x0c, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x56, 0x78,
	0x6c, 0x61, 0x6e, 0x49, 0x70, 0x12, 0x2b, 0x0a, 0x0f, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f,
	0x76, 0x78, 0x6c, 0x61, 0x6e, 0x5f, 0x69, 0x70, 0x18, 0x0d, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03,
	0xe0, 0x41, 0x03, 0x52, 0x0d, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x56, 0x78, 0x6c, 0x61, 0x6e,
	0x49, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x63, 0x69, 0x64, 0x72,
	0x18, 0x0e, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x43, 0x69, 0x64,
	0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x63, 0x69, 0x64, 0x72,
	0x18, 0x0f, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x43, 0x69,
	0x64, 0x72, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x10, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0,
	0x41, 0x03, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x0c, 0x68, 0x65, 0x61,
	0x6c, 0x74, 0x68, 0x5f, 0x63, 0x68, 0x65, 0x63, 0x6b, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32,
	0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x48,
	0x65, 0x61, 0x6c, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x52, 0x0b, 0x68, 0x65, 0x61, 0x6c, 0x74,
	0x68, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x22, 0x58, 0x0a, 0x0e, 0x4c, 0x69, 0x6d, 0x69, 0x74, 0x52,
	0x61, 0x74, 0x65, 0x49, 0x74, 0x65, 0x6d, 0x73, 0x12, 0x20, 0x0a, 0x0c, 0x75, 0x70, 0x5f, 0x73,
	0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f, 0x62, 0x77, 0x18, 0x01, 0x20, 0x01, 0x28, 0x05, 0x52, 0x0a,
	0x75, 0x70, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x42, 0x77, 0x12, 0x24, 0x0a, 0x0e, 0x64, 0x6f,
	0x77, 0x6e, 0x5f, 0x73, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x5f, 0x62, 0x77, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x05, 0x52, 0x0c, 0x64, 0x6f, 0x77, 0x6e, 0x53, 0x74, 0x72, 0x65, 0x61, 0x6d, 0x42, 0x77,
	0x22, 0x86, 0x02, 0x0a, 0x0a, 0x48, 0x65, 0x61, 0x6c, 0x74, 0x43, 0x68, 0x65, 0x63, 0x6b, 0x12,
	0x16, 0x0a, 0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x08, 0x52,
	0x06, 0x65, 0x6e, 0x61, 0x62, 0x6c, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x61, 0x72, 0x67, 0x65,
	0x74, 0x5f, 0x69, 0x70, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x61, 0x72, 0x67,
	0x65, 0x74, 0x49, 0x70, 0x12, 0x2a, 0x0a, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x05, 0x28,
	0x02, 0x48, 0x00, 0x52, 0x08, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x88, 0x01, 0x01,
	0x12, 0x28, 0x0a, 0x07, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x05, 0x42, 0x09, 0xfa, 0x42, 0x06, 0x1a, 0x04, 0x18, 0x08, 0x28, 0x03, 0x48, 0x01, 0x52, 0x07,
	0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x88, 0x01, 0x01, 0x12, 0x1d, 0x0a, 0x0a, 0x72, 0x75,
	0x6e, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x75, 0x73, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09,
	0x72, 0x75, 0x6e, 0x53, 0x74, 0x61, 0x74, 0x75, 0x73, 0x12, 0x35, 0x0a, 0x08, 0x72, 0x75, 0x6e,
	0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69,
	0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x07, 0x72, 0x75, 0x6e, 0x54, 0x69, 0x6d, 0x65,
	0x42, 0x0b, 0x0a, 0x09, 0x5f, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x76, 0x61, 0x6c, 0x42, 0x0a, 0x0a,
	0x08, 0x5f, 0x63, 0x6f, 0x75, 0x6e, 0x74, 0x65, 0x72, 0x32, 0xc3, 0x0d, 0x0a, 0x05, 0x44, 0x43,
	0x56, 0x43, 0x73, 0x12, 0xd4, 0x02, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x43,
	0x56, 0x43, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76,
	0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x43, 0x56, 0x43, 0x52, 0x65, 0x71, 0x75,
	0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e,
	0x76, 0x31, 0x2e, 0x44, 0x43, 0x56, 0x43, 0x22, 0xf0, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x81,
	0x01, 0x22, 0x79, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x64, 0x63, 0x2f, 0x64,
	0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f,
	0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x64, 0x63, 0x76, 0x63, 0x73,
	0x2f, 0x7b, 0x64, 0x63, 0x76, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x04, 0x64, 0x63,
	0x76, 0x63, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x5c, 0x0a, 0x4a,
	0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x64, 0x63, 0x2e, 0x64,
	0x63, 0x70, 0x6c, 0x2e, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x12, 0xe0, 0x02, 0x0a, 0x0a, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x43, 0x56, 0x43, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44,
	0x43, 0x56, 0x43, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f,
	0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70,
	0x74, 0x79, 0x22, 0x88, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7b, 0x2a, 0x79, 0x2f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x64, 0x63, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a,
	0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x64, 0x63, 0x76, 0x63, 0x73, 0x2f, 0x7b, 0x64, 0x63, 0x76, 0x63,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5,
	0x18, 0x7b, 0x0a, 0x69, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a,
	0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x64, 0x63, 0x76, 0x63,
	0x73, 0x2f, 0x7b, 0x64, 0x63, 0x76, 0x63, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x64,
	0x63, 0x2e, 0x64, 0x63, 0x70, 0x6c, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xf3, 0x02,
	0x0a, 0x0a, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x43, 0x56, 0x43, 0x12, 0x2f, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x44, 0x43, 0x56, 0x43, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43, 0x56,
	0x43, 0x22, 0x8f, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x81, 0x01, 0x32, 0x79, 0x2f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x64, 0x63, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a,
	0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x64, 0x63, 0x76, 0x63, 0x73, 0x2f, 0x7b, 0x64, 0x63, 0x76, 0x63,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x04, 0x64, 0x63, 0x76, 0x63, 0x88, 0xb5, 0x18, 0x01,
	0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x7b, 0x0a, 0x69, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x64, 0x63, 0x76, 0x63, 0x73, 0x2f, 0x7b, 0x64, 0x63, 0x76, 0x63, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x64, 0x63, 0x2e, 0x64, 0x63, 0x70, 0x6c, 0x2e, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x12, 0xc0, 0x02, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x44, 0x43, 0x56, 0x43, 0x12,
	0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47,
	0x65, 0x74, 0x44, 0x43, 0x56, 0x43, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43, 0x56,
	0x43, 0x22, 0xe2, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7b, 0x12, 0x79, 0x2f, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x64, 0x63, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f,
	0x6e, 0x65, 0x7d, 0x2f, 0x64, 0x63, 0x76, 0x63, 0x73, 0x2f, 0x7b, 0x64, 0x63, 0x76, 0x63, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x59, 0x0a, 0x4a, 0x2f,
	0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0b, 0x64, 0x63, 0x2e, 0x64, 0x63,
	0x70, 0x6c, 0x2e, 0x67, 0x65, 0x74, 0x12, 0xc6, 0x02, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x43, 0x56, 0x43, 0x73, 0x12, 0x2e, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x43, 0x56, 0x43, 0x73, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x43, 0x56, 0x43, 0x73, 0x52, 0x65, 0x73,
	0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0xd7, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x6f, 0x12, 0x6d,
	0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x64, 0x63, 0x2f, 0x64, 0x61, 0x74, 0x61,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73,
	0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x64, 0x63, 0x76, 0x63, 0x73, 0x80, 0xb5, 0x18,
	0x01, 0x9a, 0xb5, 0x18, 0x5a, 0x0a, 0x4a, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x12, 0x0c, 0x64, 0x63, 0x2e, 0x64, 0x63, 0x70, 0x6c, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x42,
	0x4e, 0x5a, 0x4c, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x62, 0x6a, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x65,
	0x6e, 0x74, 0x61, 0x72, 0x79, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x73, 0x6f,
	0x6e, 0x2d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x64, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x64, 0x63, 0x62,
	0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_network_dc_v1_dcvc_proto_rawDescOnce sync.Once
	file_network_dc_v1_dcvc_proto_rawDescData = file_network_dc_v1_dcvc_proto_rawDesc
)

func file_network_dc_v1_dcvc_proto_rawDescGZIP() []byte {
	file_network_dc_v1_dcvc_proto_rawDescOnce.Do(func() {
		file_network_dc_v1_dcvc_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_dc_v1_dcvc_proto_rawDescData)
	})
	return file_network_dc_v1_dcvc_proto_rawDescData
}

var file_network_dc_v1_dcvc_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_network_dc_v1_dcvc_proto_msgTypes = make([]protoimpl.MessageInfo, 12)
var file_network_dc_v1_dcvc_proto_goTypes = []interface{}{
	(DCVC_State)(0),               // 0: sensetime.core.network.dc.v1.DCVC.State
	(*CreateDCVCRequest)(nil),     // 1: sensetime.core.network.dc.v1.CreateDCVCRequest
	(*DeleteDCVCRequest)(nil),     // 2: sensetime.core.network.dc.v1.DeleteDCVCRequest
	(*UpdateDCVCRequest)(nil),     // 3: sensetime.core.network.dc.v1.UpdateDCVCRequest
	(*DCVCUpdateProperties)(nil),  // 4: sensetime.core.network.dc.v1.DCVCUpdateProperties
	(*GetDCVCRequest)(nil),        // 5: sensetime.core.network.dc.v1.GetDCVCRequest
	(*ListDCVCsRequest)(nil),      // 6: sensetime.core.network.dc.v1.ListDCVCsRequest
	(*ListDCVCsResponse)(nil),     // 7: sensetime.core.network.dc.v1.ListDCVCsResponse
	(*DCVC)(nil),                  // 8: sensetime.core.network.dc.v1.DCVC
	(*DCVCProperties)(nil),        // 9: sensetime.core.network.dc.v1.DCVCProperties
	(*LimitRateItems)(nil),        // 10: sensetime.core.network.dc.v1.LimitRateItems
	(*HealtCheck)(nil),            // 11: sensetime.core.network.dc.v1.HealtCheck
	nil,                           // 12: sensetime.core.network.dc.v1.DCVC.TagsEntry
	(*fieldmaskpb.FieldMask)(nil), // 13: google.protobuf.FieldMask
	(*v1.OrderInfo)(nil),          // 14: sensetime.core.higgs.common.v1.OrderInfo
	(*timestamppb.Timestamp)(nil), // 15: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),         // 16: google.protobuf.Empty
}
var file_network_dc_v1_dcvc_proto_depIdxs = []int32{
	8,  // 0: sensetime.core.network.dc.v1.CreateDCVCRequest.dcvc:type_name -> sensetime.core.network.dc.v1.DCVC
	4,  // 1: sensetime.core.network.dc.v1.UpdateDCVCRequest.dcvc:type_name -> sensetime.core.network.dc.v1.DCVCUpdateProperties
	13, // 2: sensetime.core.network.dc.v1.UpdateDCVCRequest.update_mask:type_name -> google.protobuf.FieldMask
	9,  // 3: sensetime.core.network.dc.v1.DCVCUpdateProperties.properties:type_name -> sensetime.core.network.dc.v1.DCVCProperties
	8,  // 4: sensetime.core.network.dc.v1.ListDCVCsResponse.dcvcs:type_name -> sensetime.core.network.dc.v1.DCVC
	0,  // 5: sensetime.core.network.dc.v1.DCVC.state:type_name -> sensetime.core.network.dc.v1.DCVC.State
	12, // 6: sensetime.core.network.dc.v1.DCVC.tags:type_name -> sensetime.core.network.dc.v1.DCVC.TagsEntry
	9,  // 7: sensetime.core.network.dc.v1.DCVC.properties:type_name -> sensetime.core.network.dc.v1.DCVCProperties
	14, // 8: sensetime.core.network.dc.v1.DCVC.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	15, // 9: sensetime.core.network.dc.v1.DCVC.create_time:type_name -> google.protobuf.Timestamp
	15, // 10: sensetime.core.network.dc.v1.DCVC.update_time:type_name -> google.protobuf.Timestamp
	10, // 11: sensetime.core.network.dc.v1.DCVCProperties.limit_rate_items:type_name -> sensetime.core.network.dc.v1.LimitRateItems
	11, // 12: sensetime.core.network.dc.v1.DCVCProperties.health_check:type_name -> sensetime.core.network.dc.v1.HealtCheck
	15, // 13: sensetime.core.network.dc.v1.HealtCheck.run_time:type_name -> google.protobuf.Timestamp
	1,  // 14: sensetime.core.network.dc.v1.DCVCs.CreateDCVC:input_type -> sensetime.core.network.dc.v1.CreateDCVCRequest
	2,  // 15: sensetime.core.network.dc.v1.DCVCs.DeleteDCVC:input_type -> sensetime.core.network.dc.v1.DeleteDCVCRequest
	3,  // 16: sensetime.core.network.dc.v1.DCVCs.UpdateDCVC:input_type -> sensetime.core.network.dc.v1.UpdateDCVCRequest
	5,  // 17: sensetime.core.network.dc.v1.DCVCs.GetDCVC:input_type -> sensetime.core.network.dc.v1.GetDCVCRequest
	6,  // 18: sensetime.core.network.dc.v1.DCVCs.ListDCVCs:input_type -> sensetime.core.network.dc.v1.ListDCVCsRequest
	8,  // 19: sensetime.core.network.dc.v1.DCVCs.CreateDCVC:output_type -> sensetime.core.network.dc.v1.DCVC
	16, // 20: sensetime.core.network.dc.v1.DCVCs.DeleteDCVC:output_type -> google.protobuf.Empty
	8,  // 21: sensetime.core.network.dc.v1.DCVCs.UpdateDCVC:output_type -> sensetime.core.network.dc.v1.DCVC
	8,  // 22: sensetime.core.network.dc.v1.DCVCs.GetDCVC:output_type -> sensetime.core.network.dc.v1.DCVC
	7,  // 23: sensetime.core.network.dc.v1.DCVCs.ListDCVCs:output_type -> sensetime.core.network.dc.v1.ListDCVCsResponse
	19, // [19:24] is the sub-list for method output_type
	14, // [14:19] is the sub-list for method input_type
	14, // [14:14] is the sub-list for extension type_name
	14, // [14:14] is the sub-list for extension extendee
	0,  // [0:14] is the sub-list for field type_name
}

func init() { file_network_dc_v1_dcvc_proto_init() }
func file_network_dc_v1_dcvc_proto_init() {
	if File_network_dc_v1_dcvc_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_network_dc_v1_dcvc_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDCVCRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcvc_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDCVCRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcvc_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDCVCRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcvc_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DCVCUpdateProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcvc_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDCVCRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcvc_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDCVCsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcvc_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDCVCsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcvc_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DCVC); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcvc_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DCVCProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcvc_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*LimitRateItems); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcvc_proto_msgTypes[10].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*HealtCheck); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_network_dc_v1_dcvc_proto_msgTypes[3].OneofWrappers = []interface{}{}
	file_network_dc_v1_dcvc_proto_msgTypes[10].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_dc_v1_dcvc_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   12,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_network_dc_v1_dcvc_proto_goTypes,
		DependencyIndexes: file_network_dc_v1_dcvc_proto_depIdxs,
		EnumInfos:         file_network_dc_v1_dcvc_proto_enumTypes,
		MessageInfos:      file_network_dc_v1_dcvc_proto_msgTypes,
	}.Build()
	File_network_dc_v1_dcvc_proto = out.File
	file_network_dc_v1_dcvc_proto_rawDesc = nil
	file_network_dc_v1_dcvc_proto_goTypes = nil
	file_network_dc_v1_dcvc_proto_depIdxs = nil
}
