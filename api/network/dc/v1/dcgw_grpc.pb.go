// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/dc/v1/dcgw.proto

package dc

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DCGWsClient is the client API for DCGWs service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DCGWsClient interface {
	// 创建一个 DCGW.
	// [EN] Create a DCGW.a
	CreateDCGW(ctx context.Context, in *CreateDCGWRequest, opts ...grpc.CallOption) (*DCGW, error)
	// 删除一个 DCGW.
	// [EN] Delete a DCGW.
	DeleteDCGW(ctx context.Context, in *DeleteDCGWRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
	// 更新 DCGW 可编辑字段.
	// [EN] Update DCGW editable properties.
	UpdateDCGW(ctx context.Context, in *UpdateDCGWRequest, opts ...grpc.CallOption) (*DCGW, error)
	// 获取符合请求的一个 DCGW.
	// [EN] Get a requested DCGW.
	GetDCGW(ctx context.Context, in *GetDCGWRequest, opts ...grpc.CallOption) (*DCGW, error)
	// 列举符合请求的所有 DCGWs.
	// [EN] List requested DCGWs.
	ListDCGWs(ctx context.Context, in *ListDCGWsRequest, opts ...grpc.CallOption) (*ListDCGWsResponse, error)
}

type dCGWsClient struct {
	cc grpc.ClientConnInterface
}

func NewDCGWsClient(cc grpc.ClientConnInterface) DCGWsClient {
	return &dCGWsClient{cc}
}

func (c *dCGWsClient) CreateDCGW(ctx context.Context, in *CreateDCGWRequest, opts ...grpc.CallOption) (*DCGW, error) {
	out := new(DCGW)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.dc.v1.DCGWs/CreateDCGW", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dCGWsClient) DeleteDCGW(ctx context.Context, in *DeleteDCGWRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.dc.v1.DCGWs/DeleteDCGW", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dCGWsClient) UpdateDCGW(ctx context.Context, in *UpdateDCGWRequest, opts ...grpc.CallOption) (*DCGW, error) {
	out := new(DCGW)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.dc.v1.DCGWs/UpdateDCGW", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dCGWsClient) GetDCGW(ctx context.Context, in *GetDCGWRequest, opts ...grpc.CallOption) (*DCGW, error) {
	out := new(DCGW)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.dc.v1.DCGWs/GetDCGW", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dCGWsClient) ListDCGWs(ctx context.Context, in *ListDCGWsRequest, opts ...grpc.CallOption) (*ListDCGWsResponse, error) {
	out := new(ListDCGWsResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.dc.v1.DCGWs/ListDCGWs", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DCGWsServer is the server API for DCGWs service.
// All implementations must embed UnimplementedDCGWsServer
// for forward compatibility
type DCGWsServer interface {
	// 创建一个 DCGW.
	// [EN] Create a DCGW.a
	CreateDCGW(context.Context, *CreateDCGWRequest) (*DCGW, error)
	// 删除一个 DCGW.
	// [EN] Delete a DCGW.
	DeleteDCGW(context.Context, *DeleteDCGWRequest) (*emptypb.Empty, error)
	// 更新 DCGW 可编辑字段.
	// [EN] Update DCGW editable properties.
	UpdateDCGW(context.Context, *UpdateDCGWRequest) (*DCGW, error)
	// 获取符合请求的一个 DCGW.
	// [EN] Get a requested DCGW.
	GetDCGW(context.Context, *GetDCGWRequest) (*DCGW, error)
	// 列举符合请求的所有 DCGWs.
	// [EN] List requested DCGWs.
	ListDCGWs(context.Context, *ListDCGWsRequest) (*ListDCGWsResponse, error)
	mustEmbedUnimplementedDCGWsServer()
}

// UnimplementedDCGWsServer must be embedded to have forward compatible implementations.
type UnimplementedDCGWsServer struct {
}

func (UnimplementedDCGWsServer) CreateDCGW(context.Context, *CreateDCGWRequest) (*DCGW, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDCGW not implemented")
}
func (UnimplementedDCGWsServer) DeleteDCGW(context.Context, *DeleteDCGWRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDCGW not implemented")
}
func (UnimplementedDCGWsServer) UpdateDCGW(context.Context, *UpdateDCGWRequest) (*DCGW, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateDCGW not implemented")
}
func (UnimplementedDCGWsServer) GetDCGW(context.Context, *GetDCGWRequest) (*DCGW, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDCGW not implemented")
}
func (UnimplementedDCGWsServer) ListDCGWs(context.Context, *ListDCGWsRequest) (*ListDCGWsResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDCGWs not implemented")
}
func (UnimplementedDCGWsServer) mustEmbedUnimplementedDCGWsServer() {}

// UnsafeDCGWsServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DCGWsServer will
// result in compilation errors.
type UnsafeDCGWsServer interface {
	mustEmbedUnimplementedDCGWsServer()
}

func RegisterDCGWsServer(s grpc.ServiceRegistrar, srv DCGWsServer) {
	s.RegisterService(&DCGWs_ServiceDesc, srv)
}

func _DCGWs_CreateDCGW_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDCGWRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DCGWsServer).CreateDCGW(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.dc.v1.DCGWs/CreateDCGW",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DCGWsServer).CreateDCGW(ctx, req.(*CreateDCGWRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DCGWs_DeleteDCGW_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDCGWRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DCGWsServer).DeleteDCGW(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.dc.v1.DCGWs/DeleteDCGW",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DCGWsServer).DeleteDCGW(ctx, req.(*DeleteDCGWRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DCGWs_UpdateDCGW_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateDCGWRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DCGWsServer).UpdateDCGW(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.dc.v1.DCGWs/UpdateDCGW",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DCGWsServer).UpdateDCGW(ctx, req.(*UpdateDCGWRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DCGWs_GetDCGW_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDCGWRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DCGWsServer).GetDCGW(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.dc.v1.DCGWs/GetDCGW",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DCGWsServer).GetDCGW(ctx, req.(*GetDCGWRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DCGWs_ListDCGWs_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDCGWsRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DCGWsServer).ListDCGWs(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.dc.v1.DCGWs/ListDCGWs",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DCGWsServer).ListDCGWs(ctx, req.(*ListDCGWsRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DCGWs_ServiceDesc is the grpc.ServiceDesc for DCGWs service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DCGWs_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.dc.v1.DCGWs",
	HandlerType: (*DCGWsServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "CreateDCGW",
			Handler:    _DCGWs_CreateDCGW_Handler,
		},
		{
			MethodName: "DeleteDCGW",
			Handler:    _DCGWs_DeleteDCGW_Handler,
		},
		{
			MethodName: "UpdateDCGW",
			Handler:    _DCGWs_UpdateDCGW_Handler,
		},
		{
			MethodName: "GetDCGW",
			Handler:    _DCGWs_GetDCGW_Handler,
		},
		{
			MethodName: "ListDCGWs",
			Handler:    _DCGWs_ListDCGWs_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/dc/v1/dcgw.proto",
}
