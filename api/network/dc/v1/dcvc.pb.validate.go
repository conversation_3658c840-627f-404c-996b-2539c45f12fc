// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/dc/v1/dcvc.proto

package dc

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateDCVCRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateDCVCRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDCVCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDCVCRequestMultiError, or nil if none found.
func (m *CreateDCVCRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDCVCRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetDcvcName()) > 63 {
		err := CreateDCVCRequestValidationError{
			field:  "DcvcName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateDCVCRequest_DcvcName_Pattern.MatchString(m.GetDcvcName()) {
		err := CreateDCVCRequestValidationError{
			field:  "DcvcName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDcvc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateDCVCRequestValidationError{
					field:  "Dcvc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateDCVCRequestValidationError{
					field:  "Dcvc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDcvc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDCVCRequestValidationError{
				field:  "Dcvc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateDCVCRequestMultiError(errors)
	}

	return nil
}

// CreateDCVCRequestMultiError is an error wrapping multiple validation errors
// returned by CreateDCVCRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateDCVCRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDCVCRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDCVCRequestMultiError) AllErrors() []error { return m }

// CreateDCVCRequestValidationError is the validation error returned by
// CreateDCVCRequest.Validate if the designated constraints aren't met.
type CreateDCVCRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDCVCRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDCVCRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDCVCRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDCVCRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDCVCRequestValidationError) ErrorName() string {
	return "CreateDCVCRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateDCVCRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDCVCRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDCVCRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDCVCRequestValidationError{}

var _CreateDCVCRequest_DcvcName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on DeleteDCVCRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteDCVCRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDCVCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDCVCRequestMultiError, or nil if none found.
func (m *DeleteDCVCRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDCVCRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for DcvcName

	if len(errors) > 0 {
		return DeleteDCVCRequestMultiError(errors)
	}

	return nil
}

// DeleteDCVCRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteDCVCRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteDCVCRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDCVCRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDCVCRequestMultiError) AllErrors() []error { return m }

// DeleteDCVCRequestValidationError is the validation error returned by
// DeleteDCVCRequest.Validate if the designated constraints aren't met.
type DeleteDCVCRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDCVCRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDCVCRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDCVCRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDCVCRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDCVCRequestValidationError) ErrorName() string {
	return "DeleteDCVCRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDCVCRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDCVCRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDCVCRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDCVCRequestValidationError{}

// Validate checks the field values on UpdateDCVCRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateDCVCRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateDCVCRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateDCVCRequestMultiError, or nil if none found.
func (m *UpdateDCVCRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateDCVCRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for DcvcName

	if all {
		switch v := interface{}(m.GetDcvc()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateDCVCRequestValidationError{
					field:  "Dcvc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateDCVCRequestValidationError{
					field:  "Dcvc",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDcvc()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateDCVCRequestValidationError{
				field:  "Dcvc",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateDCVCRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateDCVCRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateDCVCRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateDCVCRequestMultiError(errors)
	}

	return nil
}

// UpdateDCVCRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateDCVCRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateDCVCRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateDCVCRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateDCVCRequestMultiError) AllErrors() []error { return m }

// UpdateDCVCRequestValidationError is the validation error returned by
// UpdateDCVCRequest.Validate if the designated constraints aren't met.
type UpdateDCVCRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateDCVCRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateDCVCRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateDCVCRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateDCVCRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateDCVCRequestValidationError) ErrorName() string {
	return "UpdateDCVCRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateDCVCRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateDCVCRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateDCVCRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateDCVCRequestValidationError{}

// Validate checks the field values on DCVCUpdateProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DCVCUpdateProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DCVCUpdateProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DCVCUpdatePropertiesMultiError, or nil if none found.
func (m *DCVCUpdateProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *DCVCUpdateProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DCVCUpdatePropertiesValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DCVCUpdatePropertiesValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DCVCUpdatePropertiesValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.DisplayName != nil {

		if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
			err := DCVCUpdatePropertiesValidationError{
				field:  "DisplayName",
				reason: "value length must be at most 63 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_DCVCUpdateProperties_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
			err := DCVCUpdatePropertiesValidationError{
				field:  "DisplayName",
				reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return DCVCUpdatePropertiesMultiError(errors)
	}

	return nil
}

// DCVCUpdatePropertiesMultiError is an error wrapping multiple validation
// errors returned by DCVCUpdateProperties.ValidateAll() if the designated
// constraints aren't met.
type DCVCUpdatePropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DCVCUpdatePropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DCVCUpdatePropertiesMultiError) AllErrors() []error { return m }

// DCVCUpdatePropertiesValidationError is the validation error returned by
// DCVCUpdateProperties.Validate if the designated constraints aren't met.
type DCVCUpdatePropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DCVCUpdatePropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DCVCUpdatePropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DCVCUpdatePropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DCVCUpdatePropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DCVCUpdatePropertiesValidationError) ErrorName() string {
	return "DCVCUpdatePropertiesValidationError"
}

// Error satisfies the builtin error interface
func (e DCVCUpdatePropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDCVCUpdateProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DCVCUpdatePropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DCVCUpdatePropertiesValidationError{}

var _DCVCUpdateProperties_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on GetDCVCRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetDCVCRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDCVCRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetDCVCRequestMultiError,
// or nil if none found.
func (m *GetDCVCRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDCVCRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for DcvcName

	if len(errors) > 0 {
		return GetDCVCRequestMultiError(errors)
	}

	return nil
}

// GetDCVCRequestMultiError is an error wrapping multiple validation errors
// returned by GetDCVCRequest.ValidateAll() if the designated constraints
// aren't met.
type GetDCVCRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDCVCRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDCVCRequestMultiError) AllErrors() []error { return m }

// GetDCVCRequestValidationError is the validation error returned by
// GetDCVCRequest.Validate if the designated constraints aren't met.
type GetDCVCRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDCVCRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDCVCRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDCVCRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDCVCRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDCVCRequestValidationError) ErrorName() string { return "GetDCVCRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetDCVCRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDCVCRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDCVCRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDCVCRequestValidationError{}

// Validate checks the field values on ListDCVCsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListDCVCsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListDCVCsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListDCVCsRequestMultiError, or nil if none found.
func (m *ListDCVCsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListDCVCsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	// no validation rules for TenantId

	if len(errors) > 0 {
		return ListDCVCsRequestMultiError(errors)
	}

	return nil
}

// ListDCVCsRequestMultiError is an error wrapping multiple validation errors
// returned by ListDCVCsRequest.ValidateAll() if the designated constraints
// aren't met.
type ListDCVCsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListDCVCsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListDCVCsRequestMultiError) AllErrors() []error { return m }

// ListDCVCsRequestValidationError is the validation error returned by
// ListDCVCsRequest.Validate if the designated constraints aren't met.
type ListDCVCsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListDCVCsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListDCVCsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListDCVCsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListDCVCsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListDCVCsRequestValidationError) ErrorName() string { return "ListDCVCsRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListDCVCsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListDCVCsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListDCVCsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListDCVCsRequestValidationError{}

// Validate checks the field values on ListDCVCsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListDCVCsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListDCVCsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListDCVCsResponseMultiError, or nil if none found.
func (m *ListDCVCsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListDCVCsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDcvcs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListDCVCsResponseValidationError{
						field:  fmt.Sprintf("Dcvcs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListDCVCsResponseValidationError{
						field:  fmt.Sprintf("Dcvcs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListDCVCsResponseValidationError{
					field:  fmt.Sprintf("Dcvcs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListDCVCsResponseMultiError(errors)
	}

	return nil
}

// ListDCVCsResponseMultiError is an error wrapping multiple validation errors
// returned by ListDCVCsResponse.ValidateAll() if the designated constraints
// aren't met.
type ListDCVCsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListDCVCsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListDCVCsResponseMultiError) AllErrors() []error { return m }

// ListDCVCsResponseValidationError is the validation error returned by
// ListDCVCsResponse.Validate if the designated constraints aren't met.
type ListDCVCsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListDCVCsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListDCVCsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListDCVCsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListDCVCsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListDCVCsResponseValidationError) ErrorName() string {
	return "ListDCVCsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListDCVCsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListDCVCsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListDCVCsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListDCVCsResponseValidationError{}

// Validate checks the field values on DCVC with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *DCVC) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DCVC with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in DCVCMultiError, or nil if none found.
func (m *DCVC) ValidateAll() error {
	return m.validate(true)
}

func (m *DCVC) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
		err := DCVCValidationError{
			field:  "DisplayName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_DCVC_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
		err := DCVCValidationError{
			field:  "DisplayName",
			reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for Uid

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DCVCValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DCVCValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DCVCValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DCVCValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DCVCValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DCVCValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DCVCValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DCVCValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DCVCValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DCVCValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DCVCValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DCVCValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DCVCMultiError(errors)
	}

	return nil
}

// DCVCMultiError is an error wrapping multiple validation errors returned by
// DCVC.ValidateAll() if the designated constraints aren't met.
type DCVCMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DCVCMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DCVCMultiError) AllErrors() []error { return m }

// DCVCValidationError is the validation error returned by DCVC.Validate if the
// designated constraints aren't met.
type DCVCValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DCVCValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DCVCValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DCVCValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DCVCValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DCVCValidationError) ErrorName() string { return "DCVCValidationError" }

// Error satisfies the builtin error interface
func (e DCVCValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDCVC.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DCVCValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DCVCValidationError{}

var _DCVC_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on DCVCProperties with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DCVCProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DCVCProperties with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DCVCPropertiesMultiError,
// or nil if none found.
func (m *DCVCProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *DCVCProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for RouteType

	if all {
		switch v := interface{}(m.GetLimitRateItems()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DCVCPropertiesValidationError{
					field:  "LimitRateItems",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DCVCPropertiesValidationError{
					field:  "LimitRateItems",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetLimitRateItems()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DCVCPropertiesValidationError{
				field:  "LimitRateItems",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for DcplId

	// no validation rules for DcgwName

	// no validation rules for VlanId

	// no validation rules for CustomerConnectIp

	// no validation rules for SensecoreConnectIp

	// no validation rules for VxlanName

	// no validation rules for VxlanId

	// no validation rules for LocalVtepIp

	// no validation rules for RemoteVtepIp

	// no validation rules for LocalVxlanIp

	// no validation rules for RemoteVxlanIp

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	if all {
		switch v := interface{}(m.GetHealthCheck()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DCVCPropertiesValidationError{
					field:  "HealthCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DCVCPropertiesValidationError{
					field:  "HealthCheck",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetHealthCheck()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DCVCPropertiesValidationError{
				field:  "HealthCheck",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DCVCPropertiesMultiError(errors)
	}

	return nil
}

// DCVCPropertiesMultiError is an error wrapping multiple validation errors
// returned by DCVCProperties.ValidateAll() if the designated constraints
// aren't met.
type DCVCPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DCVCPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DCVCPropertiesMultiError) AllErrors() []error { return m }

// DCVCPropertiesValidationError is the validation error returned by
// DCVCProperties.Validate if the designated constraints aren't met.
type DCVCPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DCVCPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DCVCPropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DCVCPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DCVCPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DCVCPropertiesValidationError) ErrorName() string { return "DCVCPropertiesValidationError" }

// Error satisfies the builtin error interface
func (e DCVCPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDCVCProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DCVCPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DCVCPropertiesValidationError{}

// Validate checks the field values on LimitRateItems with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *LimitRateItems) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on LimitRateItems with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in LimitRateItemsMultiError,
// or nil if none found.
func (m *LimitRateItems) ValidateAll() error {
	return m.validate(true)
}

func (m *LimitRateItems) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for UpStreamBw

	// no validation rules for DownStreamBw

	if len(errors) > 0 {
		return LimitRateItemsMultiError(errors)
	}

	return nil
}

// LimitRateItemsMultiError is an error wrapping multiple validation errors
// returned by LimitRateItems.ValidateAll() if the designated constraints
// aren't met.
type LimitRateItemsMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m LimitRateItemsMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m LimitRateItemsMultiError) AllErrors() []error { return m }

// LimitRateItemsValidationError is the validation error returned by
// LimitRateItems.Validate if the designated constraints aren't met.
type LimitRateItemsValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e LimitRateItemsValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e LimitRateItemsValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e LimitRateItemsValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e LimitRateItemsValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e LimitRateItemsValidationError) ErrorName() string { return "LimitRateItemsValidationError" }

// Error satisfies the builtin error interface
func (e LimitRateItemsValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sLimitRateItems.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = LimitRateItemsValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = LimitRateItemsValidationError{}

// Validate checks the field values on HealtCheck with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *HealtCheck) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on HealtCheck with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in HealtCheckMultiError, or
// nil if none found.
func (m *HealtCheck) ValidateAll() error {
	return m.validate(true)
}

func (m *HealtCheck) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Enable

	// no validation rules for TargetIp

	// no validation rules for RunStatus

	if all {
		switch v := interface{}(m.GetRunTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, HealtCheckValidationError{
					field:  "RunTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, HealtCheckValidationError{
					field:  "RunTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetRunTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return HealtCheckValidationError{
				field:  "RunTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.Interval != nil {

		if val := m.GetInterval(); val < 2 || val > 5 {
			err := HealtCheckValidationError{
				field:  "Interval",
				reason: "value must be inside range [2, 5]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if m.Counter != nil {

		if val := m.GetCounter(); val < 3 || val > 8 {
			err := HealtCheckValidationError{
				field:  "Counter",
				reason: "value must be inside range [3, 8]",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return HealtCheckMultiError(errors)
	}

	return nil
}

// HealtCheckMultiError is an error wrapping multiple validation errors
// returned by HealtCheck.ValidateAll() if the designated constraints aren't met.
type HealtCheckMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m HealtCheckMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m HealtCheckMultiError) AllErrors() []error { return m }

// HealtCheckValidationError is the validation error returned by
// HealtCheck.Validate if the designated constraints aren't met.
type HealtCheckValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e HealtCheckValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e HealtCheckValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e HealtCheckValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e HealtCheckValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e HealtCheckValidationError) ErrorName() string { return "HealtCheckValidationError" }

// Error satisfies the builtin error interface
func (e HealtCheckValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sHealtCheck.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = HealtCheckValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = HealtCheckValidationError{}
