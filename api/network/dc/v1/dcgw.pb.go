// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/dc/v1/dcgw.proto

package dc

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/api/annotations"
	v1 "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	fieldmaskpb "google.golang.org/protobuf/types/known/fieldmaskpb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents the different states of a DCGW.
type DCGW_State int32

const (
	// The DCGW resource is being created.
	DCGW_CREATING DCGW_State = 0
	// The DCGW resource is being updated.
	DCGW_UPDATING DCGW_State = 1
	// The DCGW resource has been active.
	DCGW_ACTIVE DCGW_State = 2
	// The DCGW resource is being deleting.
	DCGW_DELETING DCGW_State = 3
	// The DCGW resource has been deleted.
	DCGW_DELETED DCGW_State = 4
	// The DCGW resource has been failed.
	DCGW_FAILED DCGW_State = 5
)

// Enum value maps for DCGW_State.
var (
	DCGW_State_name = map[int32]string{
		0: "CREATING",
		1: "UPDATING",
		2: "ACTIVE",
		3: "DELETING",
		4: "DELETED",
		5: "FAILED",
	}
	DCGW_State_value = map[string]int32{
		"CREATING": 0,
		"UPDATING": 1,
		"ACTIVE":   2,
		"DELETING": 3,
		"DELETED":  4,
		"FAILED":   5,
	}
)

func (x DCGW_State) Enum() *DCGW_State {
	p := new(DCGW_State)
	*p = x
	return p
}

func (x DCGW_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DCGW_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_dc_v1_dcgw_proto_enumTypes[0].Descriptor()
}

func (DCGW_State) Type() protoreflect.EnumType {
	return &file_network_dc_v1_dcgw_proto_enumTypes[0]
}

func (x DCGW_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DCGW_State.Descriptor instead.
func (DCGW_State) EnumDescriptor() ([]byte, []int) {
	return file_network_dc_v1_dcgw_proto_rawDescGZIP(), []int{7, 0}
}

// DCGW 创建字段.
// [EN] DCGW creation properties.
type CreateDCGWRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Zone, todo: add validation
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The DCGW resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	DcgwName string `protobuf:"bytes,4,opt,name=dcgw_name,json=dcgwName,proto3" json:"dcgw_name,omitempty"`
	// The DCGW resource to create.
	Dcgw *DCGW `protobuf:"bytes,5,opt,name=dcgw,proto3" json:"dcgw,omitempty"`
}

func (x *CreateDCGWRequest) Reset() {
	*x = CreateDCGWRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcgw_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDCGWRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDCGWRequest) ProtoMessage() {}

func (x *CreateDCGWRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcgw_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDCGWRequest.ProtoReflect.Descriptor instead.
func (*CreateDCGWRequest) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcgw_proto_rawDescGZIP(), []int{0}
}

func (x *CreateDCGWRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *CreateDCGWRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *CreateDCGWRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateDCGWRequest) GetDcgwName() string {
	if x != nil {
		return x.DcgwName
	}
	return ""
}

func (x *CreateDCGWRequest) GetDcgw() *DCGW {
	if x != nil {
		return x.Dcgw
	}
	return nil
}

// 删除某一个 DCGW 的请求.
// [EN] Request to delete a DCGW.
type DeleteDCGWRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The DCGW resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	DcgwName string `protobuf:"bytes,4,opt,name=dcgw_name,json=dcgwName,proto3" json:"dcgw_name,omitempty"`
}

func (x *DeleteDCGWRequest) Reset() {
	*x = DeleteDCGWRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcgw_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDCGWRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDCGWRequest) ProtoMessage() {}

func (x *DeleteDCGWRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcgw_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDCGWRequest.ProtoReflect.Descriptor instead.
func (*DeleteDCGWRequest) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcgw_proto_rawDescGZIP(), []int{1}
}

func (x *DeleteDCGWRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DeleteDCGWRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *DeleteDCGWRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteDCGWRequest) GetDcgwName() string {
	if x != nil {
		return x.DcgwName
	}
	return ""
}

// DCGW 可编辑字段.
// [EN] DCGW editable properties.
type UpdateDCGWRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The DCGW resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	DcgwName string `protobuf:"bytes,4,opt,name=dcgw_name,json=dcgwName,proto3" json:"dcgw_name,omitempty"`
	// The DCGW resource to update.
	Dcgw *DCGWUpdateProperties `protobuf:"bytes,5,opt,name=dcgw,proto3" json:"dcgw,omitempty"`
	// update_mask
	UpdateMask *fieldmaskpb.FieldMask `protobuf:"bytes,6,opt,name=update_mask,json=updateMask,proto3" json:"update_mask,omitempty"`
}

func (x *UpdateDCGWRequest) Reset() {
	*x = UpdateDCGWRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcgw_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *UpdateDCGWRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*UpdateDCGWRequest) ProtoMessage() {}

func (x *UpdateDCGWRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcgw_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use UpdateDCGWRequest.ProtoReflect.Descriptor instead.
func (*UpdateDCGWRequest) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcgw_proto_rawDescGZIP(), []int{2}
}

func (x *UpdateDCGWRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *UpdateDCGWRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *UpdateDCGWRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *UpdateDCGWRequest) GetDcgwName() string {
	if x != nil {
		return x.DcgwName
	}
	return ""
}

func (x *UpdateDCGWRequest) GetDcgw() *DCGWUpdateProperties {
	if x != nil {
		return x.Dcgw
	}
	return nil
}

func (x *UpdateDCGWRequest) GetUpdateMask() *fieldmaskpb.FieldMask {
	if x != nil {
		return x.UpdateMask
	}
	return nil
}

// [zh] 更新DCGW资源.
// [en] Update DCGW properties.
type DCGWUpdateProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// resource display name
	DisplayName *string `protobuf:"bytes,1,opt,name=display_name,json=displayName,proto3,oneof" json:"display_name,omitempty"`
	// ContainerInstance resource description
	Description string `protobuf:"bytes,2,opt,name=description,proto3" json:"description,omitempty"`
	// Properties of the DCGW resource.
	Properties *DCGWProperties `protobuf:"bytes,3,opt,name=properties,proto3" json:"properties,omitempty"`
}

func (x *DCGWUpdateProperties) Reset() {
	*x = DCGWUpdateProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcgw_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DCGWUpdateProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DCGWUpdateProperties) ProtoMessage() {}

func (x *DCGWUpdateProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcgw_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DCGWUpdateProperties.ProtoReflect.Descriptor instead.
func (*DCGWUpdateProperties) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcgw_proto_rawDescGZIP(), []int{3}
}

func (x *DCGWUpdateProperties) GetDisplayName() string {
	if x != nil && x.DisplayName != nil {
		return *x.DisplayName
	}
	return ""
}

func (x *DCGWUpdateProperties) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DCGWUpdateProperties) GetProperties() *DCGWProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

// 获取某一个 DCGW 的请求.
// [EN] Request to get a DCGW.
type GetDCGWRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The DCGW resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	DcgwName string `protobuf:"bytes,4,opt,name=dcgw_name,json=dcgwName,proto3" json:"dcgw_name,omitempty"`
}

func (x *GetDCGWRequest) Reset() {
	*x = GetDCGWRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcgw_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDCGWRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDCGWRequest) ProtoMessage() {}

func (x *GetDCGWRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcgw_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDCGWRequest.ProtoReflect.Descriptor instead.
func (*GetDCGWRequest) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcgw_proto_rawDescGZIP(), []int{4}
}

func (x *GetDCGWRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetDCGWRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetDCGWRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetDCGWRequest) GetDcgwName() string {
	if x != nil {
		return x.DcgwName
	}
	return ""
}

// 列举 DCGWs 的请求.
// [EN] Request to list DCGWs.
type ListDCGWsRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// List filter.
	Filter string `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// Sort resoults.
	OrderBy string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// The maximum number of items to return.
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// The next_page_token value returned from a previous List request, if any.
	PageToken string `protobuf:"bytes,7,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
	// tenant_id.
	TenantId string `protobuf:"bytes,8,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
}

func (x *ListDCGWsRequest) Reset() {
	*x = ListDCGWsRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcgw_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDCGWsRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDCGWsRequest) ProtoMessage() {}

func (x *ListDCGWsRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcgw_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDCGWsRequest.ProtoReflect.Descriptor instead.
func (*ListDCGWsRequest) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcgw_proto_rawDescGZIP(), []int{5}
}

func (x *ListDCGWsRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListDCGWsRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListDCGWsRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListDCGWsRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListDCGWsRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListDCGWsRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListDCGWsRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

func (x *ListDCGWsRequest) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

// 列举 DCGWs 的响应.
// [EN] Response to list DCGWs.
type ListDCGWsResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// DCGW 列表.
	// [EN] DCGW list.
	Dcgws []*DCGW `protobuf:"bytes,1,rep,name=dcgws,proto3" json:"dcgws,omitempty"`
	// 下一个页面的 token，如果没有更多数据则为空.
	// [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListDCGWsResponse) Reset() {
	*x = ListDCGWsResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcgw_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDCGWsResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDCGWsResponse) ProtoMessage() {}

func (x *ListDCGWsResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcgw_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDCGWsResponse.ProtoReflect.Descriptor instead.
func (*ListDCGWsResponse) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcgw_proto_rawDescGZIP(), []int{6}
}

func (x *ListDCGWsResponse) GetDcgws() []*DCGW {
	if x != nil {
		return x.Dcgws
	}
	return nil
}

func (x *ListDCGWsResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListDCGWsResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// DCGW 实例结构体.
// [EN] DCGW entity.
type DCGW struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The DCGW resource id
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The DCGW resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// resource display name
	DisplayName string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// ContainerInstance resource description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// The DCGW resource uuid.
	Uid string `protobuf:"bytes,5,opt,name=uid,proto3" json:"uid,omitempty"`
	// The DCGW resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// The id of the user who created the DCGW resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// The id of the user who owns the DCGW resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// Zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// The current state of the DCGW resource.
	State DCGW_State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.dc.v1.DCGW_State" json:"state,omitempty"`
	// Sku id.
	SkuId string `protobuf:"bytes,12,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// Tags attached to the DCGW resource.
	Tags map[string]string `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Properties of the DCGW resource.
	Properties *DCGWProperties `protobuf:"bytes,14,opt,name=properties,proto3" json:"properties,omitempty"`
	// Payment information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,15,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	// Indicates whether the DCGW resource is deleted or not.
	Deleted bool `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// The time when the DCGW resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The time when the DCGW resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *DCGW) Reset() {
	*x = DCGW{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcgw_proto_msgTypes[7]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DCGW) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DCGW) ProtoMessage() {}

func (x *DCGW) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcgw_proto_msgTypes[7]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DCGW.ProtoReflect.Descriptor instead.
func (*DCGW) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcgw_proto_rawDescGZIP(), []int{7}
}

func (x *DCGW) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DCGW) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DCGW) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *DCGW) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DCGW) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *DCGW) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *DCGW) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *DCGW) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *DCGW) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *DCGW) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DCGW) GetState() DCGW_State {
	if x != nil {
		return x.State
	}
	return DCGW_CREATING
}

func (x *DCGW) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *DCGW) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *DCGW) GetProperties() *DCGWProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *DCGW) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

func (x *DCGW) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *DCGW) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *DCGW) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// 资源实际属性.
// [EN] Real resource properties.
type DCGWProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// VPC uid.
	// [EN] VPC uid.
	VpcId string `protobuf:"bytes,1,opt,name=vpc_id,json=vpcId,proto3" json:"vpc_id,omitempty"`
	// [zh] 专线网关高可用模式.
	// [en] ha mode of dcgw
	HaMode string `protobuf:"bytes,2,opt,name=ha_mode,json=haMode,proto3" json:"ha_mode,omitempty"`
	// [zh] 专线网关部署模式.
	// [en] deploy mode of dcgw.
	DeployMode string `protobuf:"bytes,3,opt,name=deploy_mode,json=deployMode,proto3" json:"deploy_mode,omitempty"`
	// [zh] 专线网关内部的 vip.
	// [en] internal vip of dcgw.
	InternalVip string `protobuf:"bytes,4,opt,name=internal_vip,json=internalVip,proto3" json:"internal_vip,omitempty"`
	// [zh] monitor 监听地址
	// [en] monitor ip of dcgw.
	MonitorIp string `protobuf:"bytes,5,opt,name=monitor_ip,json=monitorIp,proto3" json:"monitor_ip,omitempty"`
	// [zh] heartbeat 监听在哪个网卡
	// [en] heartbeat nic ip of dcgw.
	HeartbeatNic string `protobuf:"bytes,6,opt,name=heartbeat_nic,json=heartbeatNic,proto3" json:"heartbeat_nic,omitempty"`
	// [zh] 专线网关外部的 vip.
	// [en] external vip of dcgw
	ExternalVip string `protobuf:"bytes,7,opt,name=external_vip,json=externalVip,proto3" json:"external_vip,omitempty"`
	// [zh]  专线网关本端的 CIDR.
	// [en] local cidr of dcgw.
	LocalCidr []string `protobuf:"bytes,8,rep,name=local_cidr,json=localCidr,proto3" json:"local_cidr,omitempty"`
	// [zh] 专线网关远端的 CIDR.
	// [en] remote cidr of dcgw.
	RemoteCidr []string `protobuf:"bytes,9,rep,name=remote_cidr,json=remoteCidr,proto3" json:"remote_cidr,omitempty"`
	// [zh] 专线网关关联的虚拟通道.
	// [en] dcvcs of dcgw.
	Dcvcs []*DCVCElement `protobuf:"bytes,13,rep,name=dcvcs,proto3" json:"dcvcs,omitempty"`
	// [zh] 专线网关的 subscription name.
	// [en] subscription name of dcgw
	SubscriptionName string `protobuf:"bytes,14,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// [zh] 专线网关的 resource group name.
	// [en] resource group name of dcgw
	ResourceGroupName string `protobuf:"bytes,15,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
}

func (x *DCGWProperties) Reset() {
	*x = DCGWProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcgw_proto_msgTypes[8]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DCGWProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DCGWProperties) ProtoMessage() {}

func (x *DCGWProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcgw_proto_msgTypes[8]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DCGWProperties.ProtoReflect.Descriptor instead.
func (*DCGWProperties) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcgw_proto_rawDescGZIP(), []int{8}
}

func (x *DCGWProperties) GetVpcId() string {
	if x != nil {
		return x.VpcId
	}
	return ""
}

func (x *DCGWProperties) GetHaMode() string {
	if x != nil {
		return x.HaMode
	}
	return ""
}

func (x *DCGWProperties) GetDeployMode() string {
	if x != nil {
		return x.DeployMode
	}
	return ""
}

func (x *DCGWProperties) GetInternalVip() string {
	if x != nil {
		return x.InternalVip
	}
	return ""
}

func (x *DCGWProperties) GetMonitorIp() string {
	if x != nil {
		return x.MonitorIp
	}
	return ""
}

func (x *DCGWProperties) GetHeartbeatNic() string {
	if x != nil {
		return x.HeartbeatNic
	}
	return ""
}

func (x *DCGWProperties) GetExternalVip() string {
	if x != nil {
		return x.ExternalVip
	}
	return ""
}

func (x *DCGWProperties) GetLocalCidr() []string {
	if x != nil {
		return x.LocalCidr
	}
	return nil
}

func (x *DCGWProperties) GetRemoteCidr() []string {
	if x != nil {
		return x.RemoteCidr
	}
	return nil
}

func (x *DCGWProperties) GetDcvcs() []*DCVCElement {
	if x != nil {
		return x.Dcvcs
	}
	return nil
}

func (x *DCGWProperties) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DCGWProperties) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

// 虚拟通道.
// [EN] DCVC Element.
type DCVCElement struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The DCVC resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,1,opt,name=name,proto3" json:"name,omitempty"`
	// resource display name
	DisplayName string `protobuf:"bytes,2,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
}

func (x *DCVCElement) Reset() {
	*x = DCVCElement{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_dc_v1_dcgw_proto_msgTypes[9]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DCVCElement) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DCVCElement) ProtoMessage() {}

func (x *DCVCElement) ProtoReflect() protoreflect.Message {
	mi := &file_network_dc_v1_dcgw_proto_msgTypes[9]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DCVCElement.ProtoReflect.Descriptor instead.
func (*DCVCElement) Descriptor() ([]byte, []int) {
	return file_network_dc_v1_dcgw_proto_rawDescGZIP(), []int{9}
}

func (x *DCVCElement) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DCVCElement) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

var File_network_dc_v1_dcgw_proto protoreflect.FileDescriptor

var file_network_dc_v1_dcgw_proto_rawDesc = []byte{
	0x0a, 0x18, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x64, 0x63, 0x2f, 0x76, 0x31, 0x2f,
	0x64, 0x63, 0x67, 0x77, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x1c, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70,
	0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74, 0x79, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x1a, 0x20, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x62, 0x75, 0x66, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x2e,
	0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2f,
	0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f,
	0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a,
	0x1b, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74,
	0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x68, 0x69,
	0x67, 0x67, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f, 0x6f, 0x72,
	0x64, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x66, 0x69, 0x65, 0x6c, 0x64, 0x5f,
	0x62, 0x65, 0x68, 0x61, 0x76, 0x69, 0x6f, 0x72, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22, 0x85,
	0x02, 0x0a, 0x11, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x43, 0x47, 0x57, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d,
	0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x47, 0x0a, 0x09, 0x64, 0x63, 0x67, 0x77, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42, 0x27, 0x72, 0x25, 0x18,
	0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39,
	0x5d, 0x29, 0x3f, 0x24, 0x52, 0x08, 0x64, 0x63, 0x67, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x36,
	0x0a, 0x04, 0x64, 0x63, 0x67, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43, 0x47, 0x57,
	0x52, 0x04, 0x64, 0x63, 0x67, 0x77, 0x22, 0xa1, 0x01, 0x0a, 0x11, 0x44, 0x65, 0x6c, 0x65, 0x74,
	0x65, 0x44, 0x43, 0x47, 0x57, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1b, 0x0a,
	0x09, 0x64, 0x63, 0x67, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x08, 0x64, 0x63, 0x67, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa6, 0x02, 0x0a, 0x11, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x43, 0x47, 0x57, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a,
	0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x63, 0x67, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x63, 0x67, 0x77, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x46,
	0x0a, 0x04, 0x64, 0x63, 0x67, 0x77, 0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x32, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43, 0x47, 0x57,
	0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73,
	0x52, 0x04, 0x64, 0x63, 0x67, 0x77, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x5f, 0x6d, 0x61, 0x73, 0x6b, 0x18, 0x06, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x46, 0x69,
	0x65, 0x6c, 0x64, 0x4d, 0x61, 0x73, 0x6b, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x4d,
	0x61, 0x73, 0x6b, 0x22, 0x8e, 0x02, 0x0a, 0x14, 0x44, 0x43, 0x47, 0x57, 0x55, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x75, 0x0a, 0x0c,
	0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x42, 0x4d, 0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61,
	0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d,
	0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78,
	0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29,
	0x24, 0x48, 0x00, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65,
	0x88, 0x01, 0x01, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69,
	0x6f, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69,
	0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x4c, 0x0a, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x18, 0x03, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43, 0x47, 0x57, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x42, 0x0f, 0x0a, 0x0d, 0x5f, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x22, 0x9e, 0x01, 0x0a, 0x0e, 0x47, 0x65, 0x74, 0x44, 0x43, 0x47, 0x57,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65,
	0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x1b, 0x0a, 0x09, 0x64, 0x63, 0x67, 0x77,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x64, 0x63, 0x67,
	0x77, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x8f, 0x02, 0x0a, 0x10, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x43,
	0x47, 0x57, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66,
	0x69, 0x6c, 0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18,
	0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1b,
	0x0a, 0x09, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28,
	0x05, 0x52, 0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x09, 0x70, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65,
	0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74,
	0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x22, 0x94, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74,
	0x44, 0x43, 0x47, 0x57, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x38, 0x0a,
	0x05, 0x64, 0x63, 0x67, 0x77, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x22, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43, 0x47, 0x57,
	0x52, 0x05, 0x64, 0x63, 0x67, 0x77, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65, 0x78, 0x74, 0x5f,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x12,
	0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a, 0x65, 0x22, 0xb6,
	0x07, 0x0a, 0x04, 0x44, 0x43, 0x47, 0x57, 0x12, 0x0e, 0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x12, 0x70, 0x0a, 0x0c, 0x64,
	0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x4d, 0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18, 0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d,
	0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d,
	0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b,
	0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24,
	0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a,
	0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12,
	0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69,
	0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79,
	0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f,
	0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61,
	0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69,
	0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64,
	0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61, 0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x12, 0x3e, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74, 0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e,
	0x32, 0x28, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x43, 0x47, 0x57, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x0c, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x40, 0x0a, 0x04, 0x74, 0x61, 0x67, 0x73,
	0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x2c, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43, 0x47, 0x57, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x4c, 0x0a, 0x0a, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2c,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43,
	0x47, 0x57, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72,
	0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65,
	0x72, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x68, 0x69,
	0x67, 0x67, 0x73, 0x2e, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72,
	0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x12, 0x18, 0x0a, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x10, 0x20,
	0x01, 0x28, 0x08, 0x52, 0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x0b,
	0x63, 0x72, 0x65, 0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28,
	0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64,
	0x61, 0x74, 0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a,
	0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66,
	0x2e, 0x54, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61,
	0x74, 0x65, 0x54, 0x69, 0x6d, 0x65, 0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09,
	0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22,
	0x56, 0x0a, 0x05, 0x53, 0x74, 0x61, 0x74, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52, 0x45, 0x41,
	0x54, 0x49, 0x4e, 0x47, 0x10, 0x00, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50, 0x44, 0x41, 0x54, 0x49,
	0x4e, 0x47, 0x10, 0x01, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49, 0x56, 0x45, 0x10, 0x02,
	0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0b,
	0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x46,
	0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x05, 0x22, 0xd3, 0x03, 0x0a, 0x0e, 0x44, 0x43, 0x47, 0x57,
	0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x12, 0x15, 0x0a, 0x06, 0x76, 0x70,
	0x63, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x76, 0x70, 0x63, 0x49,
	0x64, 0x12, 0x17, 0x0a, 0x07, 0x68, 0x61, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x06, 0x68, 0x61, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x1f, 0x0a, 0x0b, 0x64, 0x65,
	0x70, 0x6c, 0x6f, 0x79, 0x5f, 0x6d, 0x6f, 0x64, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0a, 0x64, 0x65, 0x70, 0x6c, 0x6f, 0x79, 0x4d, 0x6f, 0x64, 0x65, 0x12, 0x21, 0x0a, 0x0c, 0x69,
	0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x76, 0x69, 0x70, 0x18, 0x04, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x56, 0x69, 0x70, 0x12, 0x1d,
	0x0a, 0x0a, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x70, 0x18, 0x05, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x09, 0x6d, 0x6f, 0x6e, 0x69, 0x74, 0x6f, 0x72, 0x49, 0x70, 0x12, 0x23, 0x0a,
	0x0d, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x5f, 0x6e, 0x69, 0x63, 0x18, 0x06,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x68, 0x65, 0x61, 0x72, 0x74, 0x62, 0x65, 0x61, 0x74, 0x4e,
	0x69, 0x63, 0x12, 0x21, 0x0a, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x76,
	0x69, 0x70, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e,
	0x61, 0x6c, 0x56, 0x69, 0x70, 0x12, 0x1d, 0x0a, 0x0a, 0x6c, 0x6f, 0x63, 0x61, 0x6c, 0x5f, 0x63,
	0x69, 0x64, 0x72, 0x18, 0x08, 0x20, 0x03, 0x28, 0x09, 0x52, 0x09, 0x6c, 0x6f, 0x63, 0x61, 0x6c,
	0x43, 0x69, 0x64, 0x72, 0x12, 0x1f, 0x0a, 0x0b, 0x72, 0x65, 0x6d, 0x6f, 0x74, 0x65, 0x5f, 0x63,
	0x69, 0x64, 0x72, 0x18, 0x09, 0x20, 0x03, 0x28, 0x09, 0x52, 0x0a, 0x72, 0x65, 0x6d, 0x6f, 0x74,
	0x65, 0x43, 0x69, 0x64, 0x72, 0x12, 0x3f, 0x0a, 0x05, 0x64, 0x63, 0x76, 0x63, 0x73, 0x18, 0x0d,
	0x20, 0x03, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65,
	0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63,
	0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43, 0x56, 0x43, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x52,
	0x05, 0x64, 0x63, 0x76, 0x63, 0x73, 0x12, 0x30, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x0e, 0x20, 0x01, 0x28,
	0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x33, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x0f, 0x20, 0x01, 0x28, 0x09, 0x42, 0x03, 0xe0, 0x41, 0x03, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0x96, 0x01,
	0x0a, 0x0b, 0x44, 0x43, 0x56, 0x43, 0x45, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x12, 0x12, 0x0a,
	0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61, 0x6d,
	0x65, 0x12, 0x73, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x42, 0x50, 0xfa, 0x42, 0x4d, 0x72, 0x4b, 0x18, 0x3f,
	0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b,
	0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d, 0x28,
	0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65, 0x30,
	0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b, 0x30,
	0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24, 0xd0, 0x01, 0x01, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c,
	0x61, 0x79, 0x4e, 0x61, 0x6d, 0x65, 0x32, 0xc3, 0x0d, 0x0a, 0x05, 0x44, 0x43, 0x47, 0x57, 0x73,
	0x12, 0xd4, 0x02, 0x0a, 0x0a, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x43, 0x47, 0x57, 0x12,
	0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x43,
	0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x43, 0x47, 0x57, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x1a, 0x22, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72,
	0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e,
	0x44, 0x43, 0x47, 0x57, 0x22, 0xf0, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x81, 0x01, 0x22, 0x79,
	0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x64, 0x63, 0x2f, 0x64, 0x61, 0x74, 0x61,
	0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67,
	0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73,
	0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x64, 0x63, 0x67, 0x77, 0x73, 0x2f, 0x7b, 0x64,
	0x63, 0x67, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x3a, 0x04, 0x64, 0x63, 0x67, 0x77, 0x88,
	0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x5c, 0x0a, 0x4a, 0x2f, 0x72, 0x6d,
	0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x70, 0x63,
	0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xe0, 0x02, 0x0a, 0x0a, 0x44, 0x65, 0x6c, 0x65,
	0x74, 0x65, 0x44, 0x43, 0x47, 0x57, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x43, 0x47, 0x57,
	0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22,
	0x88, 0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7b, 0x2a, 0x79, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2f, 0x64, 0x63, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x64, 0x63, 0x67, 0x77, 0x73, 0x2f, 0x7b, 0x64, 0x63, 0x67, 0x77, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x7b, 0x0a,
	0x69, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47,
	0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f,
	0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65,
	0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x64, 0x63, 0x67, 0x77, 0x73, 0x2f, 0x7b,
	0x64, 0x63, 0x67, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0e, 0x76, 0x70, 0x63, 0x2e,
	0x76, 0x70, 0x63, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x12, 0xf3, 0x02, 0x0a, 0x0a, 0x55,
	0x70, 0x64, 0x61, 0x74, 0x65, 0x44, 0x43, 0x47, 0x57, 0x12, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x55, 0x70, 0x64, 0x61, 0x74, 0x65, 0x44,
	0x43, 0x47, 0x57, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43, 0x47, 0x57, 0x22, 0x8f,
	0x02, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x81, 0x01, 0x32, 0x79, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2f, 0x64, 0x63, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x64, 0x63, 0x67, 0x77, 0x73, 0x2f, 0x7b, 0x64, 0x63, 0x67, 0x77, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x3a, 0x04, 0x64, 0x63, 0x67, 0x77, 0x88, 0xb5, 0x18, 0x01, 0x80, 0xb5, 0x18,
	0x01, 0x9a, 0xb5, 0x18, 0x7b, 0x0a, 0x69, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63,
	0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73,
	0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x64,
	0x63, 0x67, 0x77, 0x73, 0x2f, 0x7b, 0x64, 0x63, 0x67, 0x77, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d,
	0x12, 0x0e, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65,
	0x12, 0xc0, 0x02, 0x0a, 0x07, 0x47, 0x65, 0x74, 0x44, 0x43, 0x47, 0x57, 0x12, 0x2c, 0x2e, 0x73,
	0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44,
	0x43, 0x47, 0x57, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x22, 0x2e, 0x73, 0x65, 0x6e,
	0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x43, 0x47, 0x57, 0x22, 0xe2,
	0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x7b, 0x12, 0x79, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2f, 0x64, 0x63, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d,
	0x2f, 0x64, 0x63, 0x67, 0x77, 0x73, 0x2f, 0x7b, 0x64, 0x63, 0x67, 0x77, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5, 0x18, 0x59, 0x0a, 0x4a, 0x2f, 0x72, 0x6d, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0b, 0x76, 0x70, 0x63, 0x2e, 0x76, 0x70, 0x63, 0x2e,
	0x67, 0x65, 0x74, 0x12, 0xc6, 0x02, 0x0a, 0x09, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x43, 0x47, 0x57,
	0x73, 0x12, 0x2e, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x43, 0x47, 0x57, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73,
	0x74, 0x1a, 0x2f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x64, 0x63, 0x2e, 0x76, 0x31,
	0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x43, 0x47, 0x57, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e,
	0x73, 0x65, 0x22, 0xd7, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x6f, 0x12, 0x6d, 0x2f, 0x6e, 0x65,
	0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x64, 0x63, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31,
	0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d,
	0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70,
	0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75,
	0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a,
	0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x64, 0x63, 0x67, 0x77, 0x73, 0x80, 0xb5, 0x18, 0x01, 0x9a, 0xb5,
	0x18, 0x5a, 0x0a, 0x4a, 0x2f, 0x72, 0x6d, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70,
	0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72,
	0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x12, 0x0c,
	0x76, 0x70, 0x63, 0x2e, 0x76, 0x70, 0x63, 0x2e, 0x6c, 0x69, 0x73, 0x74, 0x42, 0x4e, 0x5a, 0x4c,
	0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x62, 0x6a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61,
	0x72, 0x79, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2d, 0x70,
	0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x65, 0x74, 0x77,
	0x6f, 0x72, 0x6b, 0x2f, 0x64, 0x63, 0x2f, 0x76, 0x31, 0x3b, 0x64, 0x63, 0x62, 0x06, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_network_dc_v1_dcgw_proto_rawDescOnce sync.Once
	file_network_dc_v1_dcgw_proto_rawDescData = file_network_dc_v1_dcgw_proto_rawDesc
)

func file_network_dc_v1_dcgw_proto_rawDescGZIP() []byte {
	file_network_dc_v1_dcgw_proto_rawDescOnce.Do(func() {
		file_network_dc_v1_dcgw_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_dc_v1_dcgw_proto_rawDescData)
	})
	return file_network_dc_v1_dcgw_proto_rawDescData
}

var file_network_dc_v1_dcgw_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_network_dc_v1_dcgw_proto_msgTypes = make([]protoimpl.MessageInfo, 11)
var file_network_dc_v1_dcgw_proto_goTypes = []interface{}{
	(DCGW_State)(0),               // 0: sensetime.core.network.dc.v1.DCGW.State
	(*CreateDCGWRequest)(nil),     // 1: sensetime.core.network.dc.v1.CreateDCGWRequest
	(*DeleteDCGWRequest)(nil),     // 2: sensetime.core.network.dc.v1.DeleteDCGWRequest
	(*UpdateDCGWRequest)(nil),     // 3: sensetime.core.network.dc.v1.UpdateDCGWRequest
	(*DCGWUpdateProperties)(nil),  // 4: sensetime.core.network.dc.v1.DCGWUpdateProperties
	(*GetDCGWRequest)(nil),        // 5: sensetime.core.network.dc.v1.GetDCGWRequest
	(*ListDCGWsRequest)(nil),      // 6: sensetime.core.network.dc.v1.ListDCGWsRequest
	(*ListDCGWsResponse)(nil),     // 7: sensetime.core.network.dc.v1.ListDCGWsResponse
	(*DCGW)(nil),                  // 8: sensetime.core.network.dc.v1.DCGW
	(*DCGWProperties)(nil),        // 9: sensetime.core.network.dc.v1.DCGWProperties
	(*DCVCElement)(nil),           // 10: sensetime.core.network.dc.v1.DCVCElement
	nil,                           // 11: sensetime.core.network.dc.v1.DCGW.TagsEntry
	(*fieldmaskpb.FieldMask)(nil), // 12: google.protobuf.FieldMask
	(*v1.OrderInfo)(nil),          // 13: sensetime.core.higgs.common.v1.OrderInfo
	(*timestamppb.Timestamp)(nil), // 14: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),         // 15: google.protobuf.Empty
}
var file_network_dc_v1_dcgw_proto_depIdxs = []int32{
	8,  // 0: sensetime.core.network.dc.v1.CreateDCGWRequest.dcgw:type_name -> sensetime.core.network.dc.v1.DCGW
	4,  // 1: sensetime.core.network.dc.v1.UpdateDCGWRequest.dcgw:type_name -> sensetime.core.network.dc.v1.DCGWUpdateProperties
	12, // 2: sensetime.core.network.dc.v1.UpdateDCGWRequest.update_mask:type_name -> google.protobuf.FieldMask
	9,  // 3: sensetime.core.network.dc.v1.DCGWUpdateProperties.properties:type_name -> sensetime.core.network.dc.v1.DCGWProperties
	8,  // 4: sensetime.core.network.dc.v1.ListDCGWsResponse.dcgws:type_name -> sensetime.core.network.dc.v1.DCGW
	0,  // 5: sensetime.core.network.dc.v1.DCGW.state:type_name -> sensetime.core.network.dc.v1.DCGW.State
	11, // 6: sensetime.core.network.dc.v1.DCGW.tags:type_name -> sensetime.core.network.dc.v1.DCGW.TagsEntry
	9,  // 7: sensetime.core.network.dc.v1.DCGW.properties:type_name -> sensetime.core.network.dc.v1.DCGWProperties
	13, // 8: sensetime.core.network.dc.v1.DCGW.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	14, // 9: sensetime.core.network.dc.v1.DCGW.create_time:type_name -> google.protobuf.Timestamp
	14, // 10: sensetime.core.network.dc.v1.DCGW.update_time:type_name -> google.protobuf.Timestamp
	10, // 11: sensetime.core.network.dc.v1.DCGWProperties.dcvcs:type_name -> sensetime.core.network.dc.v1.DCVCElement
	1,  // 12: sensetime.core.network.dc.v1.DCGWs.CreateDCGW:input_type -> sensetime.core.network.dc.v1.CreateDCGWRequest
	2,  // 13: sensetime.core.network.dc.v1.DCGWs.DeleteDCGW:input_type -> sensetime.core.network.dc.v1.DeleteDCGWRequest
	3,  // 14: sensetime.core.network.dc.v1.DCGWs.UpdateDCGW:input_type -> sensetime.core.network.dc.v1.UpdateDCGWRequest
	5,  // 15: sensetime.core.network.dc.v1.DCGWs.GetDCGW:input_type -> sensetime.core.network.dc.v1.GetDCGWRequest
	6,  // 16: sensetime.core.network.dc.v1.DCGWs.ListDCGWs:input_type -> sensetime.core.network.dc.v1.ListDCGWsRequest
	8,  // 17: sensetime.core.network.dc.v1.DCGWs.CreateDCGW:output_type -> sensetime.core.network.dc.v1.DCGW
	15, // 18: sensetime.core.network.dc.v1.DCGWs.DeleteDCGW:output_type -> google.protobuf.Empty
	8,  // 19: sensetime.core.network.dc.v1.DCGWs.UpdateDCGW:output_type -> sensetime.core.network.dc.v1.DCGW
	8,  // 20: sensetime.core.network.dc.v1.DCGWs.GetDCGW:output_type -> sensetime.core.network.dc.v1.DCGW
	7,  // 21: sensetime.core.network.dc.v1.DCGWs.ListDCGWs:output_type -> sensetime.core.network.dc.v1.ListDCGWsResponse
	17, // [17:22] is the sub-list for method output_type
	12, // [12:17] is the sub-list for method input_type
	12, // [12:12] is the sub-list for extension type_name
	12, // [12:12] is the sub-list for extension extendee
	0,  // [0:12] is the sub-list for field type_name
}

func init() { file_network_dc_v1_dcgw_proto_init() }
func file_network_dc_v1_dcgw_proto_init() {
	if File_network_dc_v1_dcgw_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_network_dc_v1_dcgw_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDCGWRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcgw_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDCGWRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcgw_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*UpdateDCGWRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcgw_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DCGWUpdateProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcgw_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDCGWRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcgw_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDCGWsRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcgw_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDCGWsResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcgw_proto_msgTypes[7].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DCGW); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcgw_proto_msgTypes[8].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DCGWProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_dc_v1_dcgw_proto_msgTypes[9].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DCVCElement); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	file_network_dc_v1_dcgw_proto_msgTypes[3].OneofWrappers = []interface{}{}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_dc_v1_dcgw_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   11,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_network_dc_v1_dcgw_proto_goTypes,
		DependencyIndexes: file_network_dc_v1_dcgw_proto_depIdxs,
		EnumInfos:         file_network_dc_v1_dcgw_proto_enumTypes,
		MessageInfos:      file_network_dc_v1_dcgw_proto_msgTypes,
	}.Build()
	File_network_dc_v1_dcgw_proto = out.File
	file_network_dc_v1_dcgw_proto_rawDesc = nil
	file_network_dc_v1_dcgw_proto_goTypes = nil
	file_network_dc_v1_dcgw_proto_depIdxs = nil
}
