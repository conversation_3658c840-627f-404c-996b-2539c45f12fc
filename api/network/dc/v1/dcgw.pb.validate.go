// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/dc/v1/dcgw.proto

package dc

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on CreateDCGWRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *CreateDCGWRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDCGWRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDCGWRequestMultiError, or nil if none found.
func (m *CreateDCGWRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDCGWRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetDcgwName()) > 63 {
		err := CreateDCGWRequestValidationError{
			field:  "DcgwName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateDCGWRequest_DcgwName_Pattern.MatchString(m.GetDcgwName()) {
		err := CreateDCGWRequestValidationError{
			field:  "DcgwName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDcgw()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateDCGWRequestValidationError{
					field:  "Dcgw",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateDCGWRequestValidationError{
					field:  "Dcgw",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDcgw()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDCGWRequestValidationError{
				field:  "Dcgw",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateDCGWRequestMultiError(errors)
	}

	return nil
}

// CreateDCGWRequestMultiError is an error wrapping multiple validation errors
// returned by CreateDCGWRequest.ValidateAll() if the designated constraints
// aren't met.
type CreateDCGWRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDCGWRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDCGWRequestMultiError) AllErrors() []error { return m }

// CreateDCGWRequestValidationError is the validation error returned by
// CreateDCGWRequest.Validate if the designated constraints aren't met.
type CreateDCGWRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDCGWRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDCGWRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDCGWRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDCGWRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDCGWRequestValidationError) ErrorName() string {
	return "CreateDCGWRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateDCGWRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDCGWRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDCGWRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDCGWRequestValidationError{}

var _CreateDCGWRequest_DcgwName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on DeleteDCGWRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *DeleteDCGWRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDCGWRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDCGWRequestMultiError, or nil if none found.
func (m *DeleteDCGWRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDCGWRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for DcgwName

	if len(errors) > 0 {
		return DeleteDCGWRequestMultiError(errors)
	}

	return nil
}

// DeleteDCGWRequestMultiError is an error wrapping multiple validation errors
// returned by DeleteDCGWRequest.ValidateAll() if the designated constraints
// aren't met.
type DeleteDCGWRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDCGWRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDCGWRequestMultiError) AllErrors() []error { return m }

// DeleteDCGWRequestValidationError is the validation error returned by
// DeleteDCGWRequest.Validate if the designated constraints aren't met.
type DeleteDCGWRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDCGWRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDCGWRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDCGWRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDCGWRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDCGWRequestValidationError) ErrorName() string {
	return "DeleteDCGWRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDCGWRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDCGWRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDCGWRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDCGWRequestValidationError{}

// Validate checks the field values on UpdateDCGWRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *UpdateDCGWRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateDCGWRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateDCGWRequestMultiError, or nil if none found.
func (m *UpdateDCGWRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateDCGWRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for DcgwName

	if all {
		switch v := interface{}(m.GetDcgw()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateDCGWRequestValidationError{
					field:  "Dcgw",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateDCGWRequestValidationError{
					field:  "Dcgw",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDcgw()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateDCGWRequestValidationError{
				field:  "Dcgw",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateMask()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateDCGWRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateDCGWRequestValidationError{
					field:  "UpdateMask",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateMask()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateDCGWRequestValidationError{
				field:  "UpdateMask",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateDCGWRequestMultiError(errors)
	}

	return nil
}

// UpdateDCGWRequestMultiError is an error wrapping multiple validation errors
// returned by UpdateDCGWRequest.ValidateAll() if the designated constraints
// aren't met.
type UpdateDCGWRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateDCGWRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateDCGWRequestMultiError) AllErrors() []error { return m }

// UpdateDCGWRequestValidationError is the validation error returned by
// UpdateDCGWRequest.Validate if the designated constraints aren't met.
type UpdateDCGWRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateDCGWRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateDCGWRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateDCGWRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateDCGWRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateDCGWRequestValidationError) ErrorName() string {
	return "UpdateDCGWRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateDCGWRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateDCGWRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateDCGWRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateDCGWRequestValidationError{}

// Validate checks the field values on DCGWUpdateProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DCGWUpdateProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DCGWUpdateProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DCGWUpdatePropertiesMultiError, or nil if none found.
func (m *DCGWUpdateProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *DCGWUpdateProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Description

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DCGWUpdatePropertiesValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DCGWUpdatePropertiesValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DCGWUpdatePropertiesValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if m.DisplayName != nil {

		if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
			err := DCGWUpdatePropertiesValidationError{
				field:  "DisplayName",
				reason: "value length must be at most 63 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_DCGWUpdateProperties_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
			err := DCGWUpdatePropertiesValidationError{
				field:  "DisplayName",
				reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return DCGWUpdatePropertiesMultiError(errors)
	}

	return nil
}

// DCGWUpdatePropertiesMultiError is an error wrapping multiple validation
// errors returned by DCGWUpdateProperties.ValidateAll() if the designated
// constraints aren't met.
type DCGWUpdatePropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DCGWUpdatePropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DCGWUpdatePropertiesMultiError) AllErrors() []error { return m }

// DCGWUpdatePropertiesValidationError is the validation error returned by
// DCGWUpdateProperties.Validate if the designated constraints aren't met.
type DCGWUpdatePropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DCGWUpdatePropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DCGWUpdatePropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DCGWUpdatePropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DCGWUpdatePropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DCGWUpdatePropertiesValidationError) ErrorName() string {
	return "DCGWUpdatePropertiesValidationError"
}

// Error satisfies the builtin error interface
func (e DCGWUpdatePropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDCGWUpdateProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DCGWUpdatePropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DCGWUpdatePropertiesValidationError{}

var _DCGWUpdateProperties_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on GetDCGWRequest with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *GetDCGWRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDCGWRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in GetDCGWRequestMultiError,
// or nil if none found.
func (m *GetDCGWRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDCGWRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for DcgwName

	if len(errors) > 0 {
		return GetDCGWRequestMultiError(errors)
	}

	return nil
}

// GetDCGWRequestMultiError is an error wrapping multiple validation errors
// returned by GetDCGWRequest.ValidateAll() if the designated constraints
// aren't met.
type GetDCGWRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDCGWRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDCGWRequestMultiError) AllErrors() []error { return m }

// GetDCGWRequestValidationError is the validation error returned by
// GetDCGWRequest.Validate if the designated constraints aren't met.
type GetDCGWRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDCGWRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDCGWRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDCGWRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDCGWRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDCGWRequestValidationError) ErrorName() string { return "GetDCGWRequestValidationError" }

// Error satisfies the builtin error interface
func (e GetDCGWRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDCGWRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDCGWRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDCGWRequestValidationError{}

// Validate checks the field values on ListDCGWsRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListDCGWsRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListDCGWsRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListDCGWsRequestMultiError, or nil if none found.
func (m *ListDCGWsRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListDCGWsRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	// no validation rules for TenantId

	if len(errors) > 0 {
		return ListDCGWsRequestMultiError(errors)
	}

	return nil
}

// ListDCGWsRequestMultiError is an error wrapping multiple validation errors
// returned by ListDCGWsRequest.ValidateAll() if the designated constraints
// aren't met.
type ListDCGWsRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListDCGWsRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListDCGWsRequestMultiError) AllErrors() []error { return m }

// ListDCGWsRequestValidationError is the validation error returned by
// ListDCGWsRequest.Validate if the designated constraints aren't met.
type ListDCGWsRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListDCGWsRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListDCGWsRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListDCGWsRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListDCGWsRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListDCGWsRequestValidationError) ErrorName() string { return "ListDCGWsRequestValidationError" }

// Error satisfies the builtin error interface
func (e ListDCGWsRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListDCGWsRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListDCGWsRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListDCGWsRequestValidationError{}

// Validate checks the field values on ListDCGWsResponse with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListDCGWsResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListDCGWsResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListDCGWsResponseMultiError, or nil if none found.
func (m *ListDCGWsResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListDCGWsResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDcgws() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListDCGWsResponseValidationError{
						field:  fmt.Sprintf("Dcgws[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListDCGWsResponseValidationError{
						field:  fmt.Sprintf("Dcgws[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListDCGWsResponseValidationError{
					field:  fmt.Sprintf("Dcgws[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListDCGWsResponseMultiError(errors)
	}

	return nil
}

// ListDCGWsResponseMultiError is an error wrapping multiple validation errors
// returned by ListDCGWsResponse.ValidateAll() if the designated constraints
// aren't met.
type ListDCGWsResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListDCGWsResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListDCGWsResponseMultiError) AllErrors() []error { return m }

// ListDCGWsResponseValidationError is the validation error returned by
// ListDCGWsResponse.Validate if the designated constraints aren't met.
type ListDCGWsResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListDCGWsResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListDCGWsResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListDCGWsResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListDCGWsResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListDCGWsResponseValidationError) ErrorName() string {
	return "ListDCGWsResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListDCGWsResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListDCGWsResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListDCGWsResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListDCGWsResponseValidationError{}

// Validate checks the field values on DCGW with the rules defined in the proto
// definition for this message. If any rules are violated, the first error
// encountered is returned, or nil if there are no violations.
func (m *DCGW) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DCGW with the rules defined in the
// proto definition for this message. If any rules are violated, the result is
// a list of violation errors wrapped in DCGWMultiError, or nil if none found.
func (m *DCGW) ValidateAll() error {
	return m.validate(true)
}

func (m *DCGW) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
		err := DCGWValidationError{
			field:  "DisplayName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_DCGW_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
		err := DCGWValidationError{
			field:  "DisplayName",
			reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for Uid

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DCGWValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DCGWValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DCGWValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DCGWValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DCGWValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DCGWValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DCGWValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DCGWValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DCGWValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DCGWValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DCGWValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DCGWValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DCGWMultiError(errors)
	}

	return nil
}

// DCGWMultiError is an error wrapping multiple validation errors returned by
// DCGW.ValidateAll() if the designated constraints aren't met.
type DCGWMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DCGWMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DCGWMultiError) AllErrors() []error { return m }

// DCGWValidationError is the validation error returned by DCGW.Validate if the
// designated constraints aren't met.
type DCGWValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DCGWValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DCGWValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DCGWValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DCGWValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DCGWValidationError) ErrorName() string { return "DCGWValidationError" }

// Error satisfies the builtin error interface
func (e DCGWValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDCGW.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DCGWValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DCGWValidationError{}

var _DCGW_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on DCGWProperties with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DCGWProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DCGWProperties with the rules defined
// in the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DCGWPropertiesMultiError,
// or nil if none found.
func (m *DCGWProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *DCGWProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for VpcId

	// no validation rules for HaMode

	// no validation rules for DeployMode

	// no validation rules for InternalVip

	// no validation rules for MonitorIp

	// no validation rules for HeartbeatNic

	// no validation rules for ExternalVip

	for idx, item := range m.GetDcvcs() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, DCGWPropertiesValidationError{
						field:  fmt.Sprintf("Dcvcs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, DCGWPropertiesValidationError{
						field:  fmt.Sprintf("Dcvcs[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return DCGWPropertiesValidationError{
					field:  fmt.Sprintf("Dcvcs[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	if len(errors) > 0 {
		return DCGWPropertiesMultiError(errors)
	}

	return nil
}

// DCGWPropertiesMultiError is an error wrapping multiple validation errors
// returned by DCGWProperties.ValidateAll() if the designated constraints
// aren't met.
type DCGWPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DCGWPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DCGWPropertiesMultiError) AllErrors() []error { return m }

// DCGWPropertiesValidationError is the validation error returned by
// DCGWProperties.Validate if the designated constraints aren't met.
type DCGWPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DCGWPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DCGWPropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DCGWPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DCGWPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DCGWPropertiesValidationError) ErrorName() string { return "DCGWPropertiesValidationError" }

// Error satisfies the builtin error interface
func (e DCGWPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDCGWProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DCGWPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DCGWPropertiesValidationError{}

// Validate checks the field values on DCVCElement with the rules defined in
// the proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DCVCElement) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DCVCElement with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DCVCElementMultiError, or
// nil if none found.
func (m *DCVCElement) ValidateAll() error {
	return m.validate(true)
}

func (m *DCVCElement) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Name

	if m.GetDisplayName() != "" {

		if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
			err := DCVCElementValidationError{
				field:  "DisplayName",
				reason: "value length must be at most 63 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_DCVCElement_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
			err := DCVCElementValidationError{
				field:  "DisplayName",
				reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	if len(errors) > 0 {
		return DCVCElementMultiError(errors)
	}

	return nil
}

// DCVCElementMultiError is an error wrapping multiple validation errors
// returned by DCVCElement.ValidateAll() if the designated constraints aren't met.
type DCVCElementMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DCVCElementMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DCVCElementMultiError) AllErrors() []error { return m }

// DCVCElementValidationError is the validation error returned by
// DCVCElement.Validate if the designated constraints aren't met.
type DCVCElementValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DCVCElementValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DCVCElementValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DCVCElementValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DCVCElementValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DCVCElementValidationError) ErrorName() string { return "DCVCElementValidationError" }

// Error satisfies the builtin error interface
func (e DCVCElementValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDCVCElement.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DCVCElementValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DCVCElementValidationError{}

var _DCVCElement_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")
