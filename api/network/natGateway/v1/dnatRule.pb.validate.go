// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/natGateway/v1/dnatRule.proto

package natGateway

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListDNATRulesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListDNATRulesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListDNATRulesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListDNATRulesRequestMultiError, or nil if none found.
func (m *ListDNATRulesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListDNATRulesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	if len(errors) > 0 {
		return ListDNATRulesRequestMultiError(errors)
	}

	return nil
}

// ListDNATRulesRequestMultiError is an error wrapping multiple validation
// errors returned by ListDNATRulesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListDNATRulesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListDNATRulesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListDNATRulesRequestMultiError) AllErrors() []error { return m }

// ListDNATRulesRequestValidationError is the validation error returned by
// ListDNATRulesRequest.Validate if the designated constraints aren't met.
type ListDNATRulesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListDNATRulesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListDNATRulesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListDNATRulesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListDNATRulesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListDNATRulesRequestValidationError) ErrorName() string {
	return "ListDNATRulesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListDNATRulesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListDNATRulesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListDNATRulesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListDNATRulesRequestValidationError{}

// Validate checks the field values on GetDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetDNATRuleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetDNATRuleRequestMultiError, or nil if none found.
func (m *GetDNATRuleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetDNATRuleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for DnatRuleName

	if len(errors) > 0 {
		return GetDNATRuleRequestMultiError(errors)
	}

	return nil
}

// GetDNATRuleRequestMultiError is an error wrapping multiple validation errors
// returned by GetDNATRuleRequest.ValidateAll() if the designated constraints
// aren't met.
type GetDNATRuleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetDNATRuleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetDNATRuleRequestMultiError) AllErrors() []error { return m }

// GetDNATRuleRequestValidationError is the validation error returned by
// GetDNATRuleRequest.Validate if the designated constraints aren't met.
type GetDNATRuleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetDNATRuleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetDNATRuleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetDNATRuleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetDNATRuleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetDNATRuleRequestValidationError) ErrorName() string {
	return "GetDNATRuleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetDNATRuleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetDNATRuleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetDNATRuleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetDNATRuleRequestValidationError{}

// Validate checks the field values on CreateDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateDNATRuleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateDNATRuleRequestMultiError, or nil if none found.
func (m *CreateDNATRuleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateDNATRuleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetDnatRuleName()) > 63 {
		err := CreateDNATRuleRequestValidationError{
			field:  "DnatRuleName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateDNATRuleRequest_DnatRuleName_Pattern.MatchString(m.GetDnatRuleName()) {
		err := CreateDNATRuleRequestValidationError{
			field:  "DnatRuleName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetDnatRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateDNATRuleRequestValidationError{
					field:  "DnatRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateDNATRuleRequestValidationError{
					field:  "DnatRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetDnatRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateDNATRuleRequestValidationError{
				field:  "DnatRule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateDNATRuleRequestMultiError(errors)
	}

	return nil
}

// CreateDNATRuleRequestMultiError is an error wrapping multiple validation
// errors returned by CreateDNATRuleRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateDNATRuleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateDNATRuleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateDNATRuleRequestMultiError) AllErrors() []error { return m }

// CreateDNATRuleRequestValidationError is the validation error returned by
// CreateDNATRuleRequest.Validate if the designated constraints aren't met.
type CreateDNATRuleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateDNATRuleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateDNATRuleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateDNATRuleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateDNATRuleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateDNATRuleRequestValidationError) ErrorName() string {
	return "CreateDNATRuleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateDNATRuleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateDNATRuleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateDNATRuleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateDNATRuleRequestValidationError{}

var _CreateDNATRuleRequest_DnatRuleName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on DeleteDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteDNATRuleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteDNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteDNATRuleRequestMultiError, or nil if none found.
func (m *DeleteDNATRuleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteDNATRuleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for DnatRuleName

	if len(errors) > 0 {
		return DeleteDNATRuleRequestMultiError(errors)
	}

	return nil
}

// DeleteDNATRuleRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteDNATRuleRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteDNATRuleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteDNATRuleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteDNATRuleRequestMultiError) AllErrors() []error { return m }

// DeleteDNATRuleRequestValidationError is the validation error returned by
// DeleteDNATRuleRequest.Validate if the designated constraints aren't met.
type DeleteDNATRuleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteDNATRuleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteDNATRuleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteDNATRuleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteDNATRuleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteDNATRuleRequestValidationError) ErrorName() string {
	return "DeleteDNATRuleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteDNATRuleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteDNATRuleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteDNATRuleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteDNATRuleRequestValidationError{}

// Validate checks the field values on ListDNATRulesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListDNATRulesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListDNATRulesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListDNATRulesResponseMultiError, or nil if none found.
func (m *ListDNATRulesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListDNATRulesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetDnatRules() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListDNATRulesResponseValidationError{
						field:  fmt.Sprintf("DnatRules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListDNATRulesResponseValidationError{
						field:  fmt.Sprintf("DnatRules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListDNATRulesResponseValidationError{
					field:  fmt.Sprintf("DnatRules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListDNATRulesResponseMultiError(errors)
	}

	return nil
}

// ListDNATRulesResponseMultiError is an error wrapping multiple validation
// errors returned by ListDNATRulesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListDNATRulesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListDNATRulesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListDNATRulesResponseMultiError) AllErrors() []error { return m }

// ListDNATRulesResponseValidationError is the validation error returned by
// ListDNATRulesResponse.Validate if the designated constraints aren't met.
type ListDNATRulesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListDNATRulesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListDNATRulesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListDNATRulesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListDNATRulesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListDNATRulesResponseValidationError) ErrorName() string {
	return "ListDNATRulesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListDNATRulesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListDNATRulesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListDNATRulesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListDNATRulesResponseValidationError{}

// Validate checks the field values on DNATRule with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *DNATRule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DNATRule with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in DNATRuleMultiError, or nil
// if none found.
func (m *DNATRule) ValidateAll() error {
	return m.validate(true)
}

func (m *DNATRule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
		err := DNATRuleValidationError{
			field:  "DisplayName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_DNATRule_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
		err := DNATRuleValidationError{
			field:  "DisplayName",
			reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for Uid

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DNATRuleValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DNATRuleValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DNATRuleValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, DNATRuleValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return DNATRuleValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return DNATRuleMultiError(errors)
	}

	return nil
}

// DNATRuleMultiError is an error wrapping multiple validation errors returned
// by DNATRule.ValidateAll() if the designated constraints aren't met.
type DNATRuleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DNATRuleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DNATRuleMultiError) AllErrors() []error { return m }

// DNATRuleValidationError is the validation error returned by
// DNATRule.Validate if the designated constraints aren't met.
type DNATRuleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DNATRuleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DNATRuleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DNATRuleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DNATRuleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DNATRuleValidationError) ErrorName() string { return "DNATRuleValidationError" }

// Error satisfies the builtin error interface
func (e DNATRuleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDNATRule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DNATRuleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DNATRuleValidationError{}

var _DNATRule_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on DNATRuleProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DNATRuleProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DNATRuleProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DNATRulePropertiesMultiError, or nil if none found.
func (m *DNATRuleProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *DNATRuleProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NatGatewayId

	// no validation rules for EipId

	// no validation rules for ExternalIp

	// no validation rules for ExternalPort

	// no validation rules for Protocol

	// no validation rules for InternalIp

	// no validation rules for InternalPort

	// no validation rules for Priority

	if len(errors) > 0 {
		return DNATRulePropertiesMultiError(errors)
	}

	return nil
}

// DNATRulePropertiesMultiError is an error wrapping multiple validation errors
// returned by DNATRuleProperties.ValidateAll() if the designated constraints
// aren't met.
type DNATRulePropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DNATRulePropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DNATRulePropertiesMultiError) AllErrors() []error { return m }

// DNATRulePropertiesValidationError is the validation error returned by
// DNATRuleProperties.Validate if the designated constraints aren't met.
type DNATRulePropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DNATRulePropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DNATRulePropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DNATRulePropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DNATRulePropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DNATRulePropertiesValidationError) ErrorName() string {
	return "DNATRulePropertiesValidationError"
}

// Error satisfies the builtin error interface
func (e DNATRulePropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDNATRuleProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DNATRulePropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DNATRulePropertiesValidationError{}
