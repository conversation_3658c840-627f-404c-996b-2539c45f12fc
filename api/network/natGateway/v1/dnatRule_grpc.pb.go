// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/natGateway/v1/dnatRule.proto

package natGateway

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// DNATRulesClient is the client API for DNATRules service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type DNATRulesClient interface {
	// 列举符合请求的所有 DNATRules.
	// [EN] List requested DNATRules.
	ListDNATRules(ctx context.Context, in *ListDNATRulesRequest, opts ...grpc.CallOption) (*ListDNATRulesResponse, error)
	// 获取符合请求的一个 DNATRule.
	// [EN] Get a requested DNATRule.
	GetDNATRule(ctx context.Context, in *GetDNATRuleRequest, opts ...grpc.CallOption) (*DNATRule, error)
	// 创建一个 DNATRule.
	// [EN] Create a DNATRule.
	CreateDNATRule(ctx context.Context, in *CreateDNATRuleRequest, opts ...grpc.CallOption) (*DNATRule, error)
	// 删除一个 DNATRule.
	// [EN] Delete a DNATRule.
	DeleteDNATRule(ctx context.Context, in *DeleteDNATRuleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type dNATRulesClient struct {
	cc grpc.ClientConnInterface
}

func NewDNATRulesClient(cc grpc.ClientConnInterface) DNATRulesClient {
	return &dNATRulesClient{cc}
}

func (c *dNATRulesClient) ListDNATRules(ctx context.Context, in *ListDNATRulesRequest, opts ...grpc.CallOption) (*ListDNATRulesResponse, error) {
	out := new(ListDNATRulesResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.natGateway.v1.DNATRules/ListDNATRules", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dNATRulesClient) GetDNATRule(ctx context.Context, in *GetDNATRuleRequest, opts ...grpc.CallOption) (*DNATRule, error) {
	out := new(DNATRule)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.natGateway.v1.DNATRules/GetDNATRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dNATRulesClient) CreateDNATRule(ctx context.Context, in *CreateDNATRuleRequest, opts ...grpc.CallOption) (*DNATRule, error) {
	out := new(DNATRule)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.natGateway.v1.DNATRules/CreateDNATRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *dNATRulesClient) DeleteDNATRule(ctx context.Context, in *DeleteDNATRuleRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.natGateway.v1.DNATRules/DeleteDNATRule", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// DNATRulesServer is the server API for DNATRules service.
// All implementations must embed UnimplementedDNATRulesServer
// for forward compatibility
type DNATRulesServer interface {
	// 列举符合请求的所有 DNATRules.
	// [EN] List requested DNATRules.
	ListDNATRules(context.Context, *ListDNATRulesRequest) (*ListDNATRulesResponse, error)
	// 获取符合请求的一个 DNATRule.
	// [EN] Get a requested DNATRule.
	GetDNATRule(context.Context, *GetDNATRuleRequest) (*DNATRule, error)
	// 创建一个 DNATRule.
	// [EN] Create a DNATRule.
	CreateDNATRule(context.Context, *CreateDNATRuleRequest) (*DNATRule, error)
	// 删除一个 DNATRule.
	// [EN] Delete a DNATRule.
	DeleteDNATRule(context.Context, *DeleteDNATRuleRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedDNATRulesServer()
}

// UnimplementedDNATRulesServer must be embedded to have forward compatible implementations.
type UnimplementedDNATRulesServer struct {
}

func (UnimplementedDNATRulesServer) ListDNATRules(context.Context, *ListDNATRulesRequest) (*ListDNATRulesResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListDNATRules not implemented")
}
func (UnimplementedDNATRulesServer) GetDNATRule(context.Context, *GetDNATRuleRequest) (*DNATRule, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetDNATRule not implemented")
}
func (UnimplementedDNATRulesServer) CreateDNATRule(context.Context, *CreateDNATRuleRequest) (*DNATRule, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateDNATRule not implemented")
}
func (UnimplementedDNATRulesServer) DeleteDNATRule(context.Context, *DeleteDNATRuleRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteDNATRule not implemented")
}
func (UnimplementedDNATRulesServer) mustEmbedUnimplementedDNATRulesServer() {}

// UnsafeDNATRulesServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to DNATRulesServer will
// result in compilation errors.
type UnsafeDNATRulesServer interface {
	mustEmbedUnimplementedDNATRulesServer()
}

func RegisterDNATRulesServer(s grpc.ServiceRegistrar, srv DNATRulesServer) {
	s.RegisterService(&DNATRules_ServiceDesc, srv)
}

func _DNATRules_ListDNATRules_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListDNATRulesRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DNATRulesServer).ListDNATRules(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.natGateway.v1.DNATRules/ListDNATRules",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DNATRulesServer).ListDNATRules(ctx, req.(*ListDNATRulesRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DNATRules_GetDNATRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetDNATRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DNATRulesServer).GetDNATRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.natGateway.v1.DNATRules/GetDNATRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DNATRulesServer).GetDNATRule(ctx, req.(*GetDNATRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DNATRules_CreateDNATRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateDNATRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DNATRulesServer).CreateDNATRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.natGateway.v1.DNATRules/CreateDNATRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DNATRulesServer).CreateDNATRule(ctx, req.(*CreateDNATRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _DNATRules_DeleteDNATRule_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteDNATRuleRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(DNATRulesServer).DeleteDNATRule(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.natGateway.v1.DNATRules/DeleteDNATRule",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(DNATRulesServer).DeleteDNATRule(ctx, req.(*DeleteDNATRuleRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// DNATRules_ServiceDesc is the grpc.ServiceDesc for DNATRules service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var DNATRules_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.natGateway.v1.DNATRules",
	HandlerType: (*DNATRulesServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListDNATRules",
			Handler:    _DNATRules_ListDNATRules_Handler,
		},
		{
			MethodName: "GetDNATRule",
			Handler:    _DNATRules_GetDNATRule_Handler,
		},
		{
			MethodName: "CreateDNATRule",
			Handler:    _DNATRules_CreateDNATRule_Handler,
		},
		{
			MethodName: "DeleteDNATRule",
			Handler:    _DNATRules_DeleteDNATRule_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/natGateway/v1/dnatRule.proto",
}
