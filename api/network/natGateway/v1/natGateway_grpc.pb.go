// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/natGateway/v1/natGateway.proto

package natGateway

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// NATGatewaysClient is the client API for NATGateways service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type NATGatewaysClient interface {
	// 列举符合请求的所有 NATGateways.
	// [EN] List requested NATGateways.
	ListNATGateways(ctx context.Context, in *ListNATGatewaysRequest, opts ...grpc.CallOption) (*ListNATGatewaysResponse, error)
	// 获取符合请求的一个 NATGateway.
	// [EN] Get a requested NATGateway.
	GetNATGateway(ctx context.Context, in *GetNATGatewayRequest, opts ...grpc.CallOption) (*NATGateway, error)
	// 创建一个 NATGateway.
	// [EN] Create a NATGateway.
	CreateNATGateway(ctx context.Context, in *CreateNATGatewayRequest, opts ...grpc.CallOption) (*NATGateway, error)
	// 更新 NATGateway 可编辑字段.
	// [EN] Update NATGateway editable properties.
	UpdateNATGateway(ctx context.Context, in *UpdateNATGatewayRequest, opts ...grpc.CallOption) (*NATGateway, error)
	// 删除一个 NATGateway.
	// [EN] Delete a NATGateway.
	DeleteNATGateway(ctx context.Context, in *DeleteNATGatewayRequest, opts ...grpc.CallOption) (*emptypb.Empty, error)
}

type nATGatewaysClient struct {
	cc grpc.ClientConnInterface
}

func NewNATGatewaysClient(cc grpc.ClientConnInterface) NATGatewaysClient {
	return &nATGatewaysClient{cc}
}

func (c *nATGatewaysClient) ListNATGateways(ctx context.Context, in *ListNATGatewaysRequest, opts ...grpc.CallOption) (*ListNATGatewaysResponse, error) {
	out := new(ListNATGatewaysResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.natGateway.v1.NATGateways/ListNATGateways", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nATGatewaysClient) GetNATGateway(ctx context.Context, in *GetNATGatewayRequest, opts ...grpc.CallOption) (*NATGateway, error) {
	out := new(NATGateway)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.natGateway.v1.NATGateways/GetNATGateway", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nATGatewaysClient) CreateNATGateway(ctx context.Context, in *CreateNATGatewayRequest, opts ...grpc.CallOption) (*NATGateway, error) {
	out := new(NATGateway)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.natGateway.v1.NATGateways/CreateNATGateway", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nATGatewaysClient) UpdateNATGateway(ctx context.Context, in *UpdateNATGatewayRequest, opts ...grpc.CallOption) (*NATGateway, error) {
	out := new(NATGateway)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.natGateway.v1.NATGateways/UpdateNATGateway", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (c *nATGatewaysClient) DeleteNATGateway(ctx context.Context, in *DeleteNATGatewayRequest, opts ...grpc.CallOption) (*emptypb.Empty, error) {
	out := new(emptypb.Empty)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.natGateway.v1.NATGateways/DeleteNATGateway", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// NATGatewaysServer is the server API for NATGateways service.
// All implementations must embed UnimplementedNATGatewaysServer
// for forward compatibility
type NATGatewaysServer interface {
	// 列举符合请求的所有 NATGateways.
	// [EN] List requested NATGateways.
	ListNATGateways(context.Context, *ListNATGatewaysRequest) (*ListNATGatewaysResponse, error)
	// 获取符合请求的一个 NATGateway.
	// [EN] Get a requested NATGateway.
	GetNATGateway(context.Context, *GetNATGatewayRequest) (*NATGateway, error)
	// 创建一个 NATGateway.
	// [EN] Create a NATGateway.
	CreateNATGateway(context.Context, *CreateNATGatewayRequest) (*NATGateway, error)
	// 更新 NATGateway 可编辑字段.
	// [EN] Update NATGateway editable properties.
	UpdateNATGateway(context.Context, *UpdateNATGatewayRequest) (*NATGateway, error)
	// 删除一个 NATGateway.
	// [EN] Delete a NATGateway.
	DeleteNATGateway(context.Context, *DeleteNATGatewayRequest) (*emptypb.Empty, error)
	mustEmbedUnimplementedNATGatewaysServer()
}

// UnimplementedNATGatewaysServer must be embedded to have forward compatible implementations.
type UnimplementedNATGatewaysServer struct {
}

func (UnimplementedNATGatewaysServer) ListNATGateways(context.Context, *ListNATGatewaysRequest) (*ListNATGatewaysResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListNATGateways not implemented")
}
func (UnimplementedNATGatewaysServer) GetNATGateway(context.Context, *GetNATGatewayRequest) (*NATGateway, error) {
	return nil, status.Errorf(codes.Unimplemented, "method GetNATGateway not implemented")
}
func (UnimplementedNATGatewaysServer) CreateNATGateway(context.Context, *CreateNATGatewayRequest) (*NATGateway, error) {
	return nil, status.Errorf(codes.Unimplemented, "method CreateNATGateway not implemented")
}
func (UnimplementedNATGatewaysServer) UpdateNATGateway(context.Context, *UpdateNATGatewayRequest) (*NATGateway, error) {
	return nil, status.Errorf(codes.Unimplemented, "method UpdateNATGateway not implemented")
}
func (UnimplementedNATGatewaysServer) DeleteNATGateway(context.Context, *DeleteNATGatewayRequest) (*emptypb.Empty, error) {
	return nil, status.Errorf(codes.Unimplemented, "method DeleteNATGateway not implemented")
}
func (UnimplementedNATGatewaysServer) mustEmbedUnimplementedNATGatewaysServer() {}

// UnsafeNATGatewaysServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to NATGatewaysServer will
// result in compilation errors.
type UnsafeNATGatewaysServer interface {
	mustEmbedUnimplementedNATGatewaysServer()
}

func RegisterNATGatewaysServer(s grpc.ServiceRegistrar, srv NATGatewaysServer) {
	s.RegisterService(&NATGateways_ServiceDesc, srv)
}

func _NATGateways_ListNATGateways_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListNATGatewaysRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NATGatewaysServer).ListNATGateways(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.natGateway.v1.NATGateways/ListNATGateways",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NATGatewaysServer).ListNATGateways(ctx, req.(*ListNATGatewaysRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NATGateways_GetNATGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(GetNATGatewayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NATGatewaysServer).GetNATGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.natGateway.v1.NATGateways/GetNATGateway",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NATGatewaysServer).GetNATGateway(ctx, req.(*GetNATGatewayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NATGateways_CreateNATGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(CreateNATGatewayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NATGatewaysServer).CreateNATGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.natGateway.v1.NATGateways/CreateNATGateway",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NATGatewaysServer).CreateNATGateway(ctx, req.(*CreateNATGatewayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NATGateways_UpdateNATGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(UpdateNATGatewayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NATGatewaysServer).UpdateNATGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.natGateway.v1.NATGateways/UpdateNATGateway",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NATGatewaysServer).UpdateNATGateway(ctx, req.(*UpdateNATGatewayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

func _NATGateways_DeleteNATGateway_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(DeleteNATGatewayRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(NATGatewaysServer).DeleteNATGateway(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.natGateway.v1.NATGateways/DeleteNATGateway",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(NATGatewaysServer).DeleteNATGateway(ctx, req.(*DeleteNATGatewayRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// NATGateways_ServiceDesc is the grpc.ServiceDesc for NATGateways service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var NATGateways_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.natGateway.v1.NATGateways",
	HandlerType: (*NATGatewaysServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListNATGateways",
			Handler:    _NATGateways_ListNATGateways_Handler,
		},
		{
			MethodName: "GetNATGateway",
			Handler:    _NATGateways_GetNATGateway_Handler,
		},
		{
			MethodName: "CreateNATGateway",
			Handler:    _NATGateways_CreateNATGateway_Handler,
		},
		{
			MethodName: "UpdateNATGateway",
			Handler:    _NATGateways_UpdateNATGateway_Handler,
		},
		{
			MethodName: "DeleteNATGateway",
			Handler:    _NATGateways_DeleteNATGateway_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/natGateway/v1/natGateway.proto",
}
