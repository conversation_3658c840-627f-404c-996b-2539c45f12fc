// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/natGateway/v1/natGateway.proto

package natGateway

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListNATGatewaysRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListNATGatewaysRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListNATGatewaysRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListNATGatewaysRequestMultiError, or nil if none found.
func (m *ListNATGatewaysRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNATGatewaysRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	if len(errors) > 0 {
		return ListNATGatewaysRequestMultiError(errors)
	}

	return nil
}

// ListNATGatewaysRequestMultiError is an error wrapping multiple validation
// errors returned by ListNATGatewaysRequest.ValidateAll() if the designated
// constraints aren't met.
type ListNATGatewaysRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNATGatewaysRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNATGatewaysRequestMultiError) AllErrors() []error { return m }

// ListNATGatewaysRequestValidationError is the validation error returned by
// ListNATGatewaysRequest.Validate if the designated constraints aren't met.
type ListNATGatewaysRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNATGatewaysRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNATGatewaysRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNATGatewaysRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNATGatewaysRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNATGatewaysRequestValidationError) ErrorName() string {
	return "ListNATGatewaysRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListNATGatewaysRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNATGatewaysRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNATGatewaysRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNATGatewaysRequestValidationError{}

// Validate checks the field values on GetNATGatewayRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetNATGatewayRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetNATGatewayRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetNATGatewayRequestMultiError, or nil if none found.
func (m *GetNATGatewayRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetNATGatewayRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for NatGatewayName

	if len(errors) > 0 {
		return GetNATGatewayRequestMultiError(errors)
	}

	return nil
}

// GetNATGatewayRequestMultiError is an error wrapping multiple validation
// errors returned by GetNATGatewayRequest.ValidateAll() if the designated
// constraints aren't met.
type GetNATGatewayRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetNATGatewayRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetNATGatewayRequestMultiError) AllErrors() []error { return m }

// GetNATGatewayRequestValidationError is the validation error returned by
// GetNATGatewayRequest.Validate if the designated constraints aren't met.
type GetNATGatewayRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetNATGatewayRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetNATGatewayRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetNATGatewayRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetNATGatewayRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetNATGatewayRequestValidationError) ErrorName() string {
	return "GetNATGatewayRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetNATGatewayRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetNATGatewayRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetNATGatewayRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetNATGatewayRequestValidationError{}

// Validate checks the field values on CreateNATGatewayRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateNATGatewayRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateNATGatewayRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateNATGatewayRequestMultiError, or nil if none found.
func (m *CreateNATGatewayRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateNATGatewayRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetNatGatewayName()) > 63 {
		err := CreateNATGatewayRequestValidationError{
			field:  "NatGatewayName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateNATGatewayRequest_NatGatewayName_Pattern.MatchString(m.GetNatGatewayName()) {
		err := CreateNATGatewayRequestValidationError{
			field:  "NatGatewayName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetNatGateway()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateNATGatewayRequestValidationError{
					field:  "NatGateway",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateNATGatewayRequestValidationError{
					field:  "NatGateway",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNatGateway()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateNATGatewayRequestValidationError{
				field:  "NatGateway",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateNATGatewayRequestMultiError(errors)
	}

	return nil
}

// CreateNATGatewayRequestMultiError is an error wrapping multiple validation
// errors returned by CreateNATGatewayRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateNATGatewayRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateNATGatewayRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateNATGatewayRequestMultiError) AllErrors() []error { return m }

// CreateNATGatewayRequestValidationError is the validation error returned by
// CreateNATGatewayRequest.Validate if the designated constraints aren't met.
type CreateNATGatewayRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateNATGatewayRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateNATGatewayRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateNATGatewayRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateNATGatewayRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateNATGatewayRequestValidationError) ErrorName() string {
	return "CreateNATGatewayRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateNATGatewayRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateNATGatewayRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateNATGatewayRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateNATGatewayRequestValidationError{}

var _CreateNATGatewayRequest_NatGatewayName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on UpdateNATGatewayRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *UpdateNATGatewayRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on UpdateNATGatewayRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// UpdateNATGatewayRequestMultiError, or nil if none found.
func (m *UpdateNATGatewayRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *UpdateNATGatewayRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for NatGatewayName

	if all {
		switch v := interface{}(m.GetNatGateway()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, UpdateNATGatewayRequestValidationError{
					field:  "NatGateway",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, UpdateNATGatewayRequestValidationError{
					field:  "NatGateway",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNatGateway()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return UpdateNATGatewayRequestValidationError{
				field:  "NatGateway",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return UpdateNATGatewayRequestMultiError(errors)
	}

	return nil
}

// UpdateNATGatewayRequestMultiError is an error wrapping multiple validation
// errors returned by UpdateNATGatewayRequest.ValidateAll() if the designated
// constraints aren't met.
type UpdateNATGatewayRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m UpdateNATGatewayRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m UpdateNATGatewayRequestMultiError) AllErrors() []error { return m }

// UpdateNATGatewayRequestValidationError is the validation error returned by
// UpdateNATGatewayRequest.Validate if the designated constraints aren't met.
type UpdateNATGatewayRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e UpdateNATGatewayRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e UpdateNATGatewayRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e UpdateNATGatewayRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e UpdateNATGatewayRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e UpdateNATGatewayRequestValidationError) ErrorName() string {
	return "UpdateNATGatewayRequestValidationError"
}

// Error satisfies the builtin error interface
func (e UpdateNATGatewayRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sUpdateNATGatewayRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = UpdateNATGatewayRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = UpdateNATGatewayRequestValidationError{}

// Validate checks the field values on DeleteNATGatewayRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteNATGatewayRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteNATGatewayRequest with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteNATGatewayRequestMultiError, or nil if none found.
func (m *DeleteNATGatewayRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteNATGatewayRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for NatGatewayName

	if len(errors) > 0 {
		return DeleteNATGatewayRequestMultiError(errors)
	}

	return nil
}

// DeleteNATGatewayRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteNATGatewayRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteNATGatewayRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteNATGatewayRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteNATGatewayRequestMultiError) AllErrors() []error { return m }

// DeleteNATGatewayRequestValidationError is the validation error returned by
// DeleteNATGatewayRequest.Validate if the designated constraints aren't met.
type DeleteNATGatewayRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteNATGatewayRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteNATGatewayRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteNATGatewayRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteNATGatewayRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteNATGatewayRequestValidationError) ErrorName() string {
	return "DeleteNATGatewayRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteNATGatewayRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteNATGatewayRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteNATGatewayRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteNATGatewayRequestValidationError{}

// Validate checks the field values on ListNATGatewaysResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListNATGatewaysResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListNATGatewaysResponse with the
// rules defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListNATGatewaysResponseMultiError, or nil if none found.
func (m *ListNATGatewaysResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListNATGatewaysResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetNatGateways() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListNATGatewaysResponseValidationError{
						field:  fmt.Sprintf("NatGateways[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListNATGatewaysResponseValidationError{
						field:  fmt.Sprintf("NatGateways[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListNATGatewaysResponseValidationError{
					field:  fmt.Sprintf("NatGateways[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListNATGatewaysResponseMultiError(errors)
	}

	return nil
}

// ListNATGatewaysResponseMultiError is an error wrapping multiple validation
// errors returned by ListNATGatewaysResponse.ValidateAll() if the designated
// constraints aren't met.
type ListNATGatewaysResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListNATGatewaysResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListNATGatewaysResponseMultiError) AllErrors() []error { return m }

// ListNATGatewaysResponseValidationError is the validation error returned by
// ListNATGatewaysResponse.Validate if the designated constraints aren't met.
type ListNATGatewaysResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListNATGatewaysResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListNATGatewaysResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListNATGatewaysResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListNATGatewaysResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListNATGatewaysResponseValidationError) ErrorName() string {
	return "ListNATGatewaysResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListNATGatewaysResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListNATGatewaysResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListNATGatewaysResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListNATGatewaysResponseValidationError{}

// Validate checks the field values on NATGateway with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *NATGateway) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NATGateway with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in NATGatewayMultiError, or
// nil if none found.
func (m *NATGateway) ValidateAll() error {
	return m.validate(true)
}

func (m *NATGateway) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if m.GetDisplayName() != "" {

		if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
			err := NATGatewayValidationError{
				field:  "DisplayName",
				reason: "value length must be at most 63 runes",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

		if !_NATGateway_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
			err := NATGatewayValidationError{
				field:  "DisplayName",
				reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
			}
			if !all {
				return err
			}
			errors = append(errors, err)
		}

	}

	// no validation rules for Description

	// no validation rules for Uid

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NATGatewayValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NATGatewayValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NATGatewayValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NATGatewayValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NATGatewayValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NATGatewayValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NATGatewayValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NATGatewayValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NATGatewayValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NATGatewayValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NATGatewayValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NATGatewayValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NATGatewayMultiError(errors)
	}

	return nil
}

// NATGatewayMultiError is an error wrapping multiple validation errors
// returned by NATGateway.ValidateAll() if the designated constraints aren't met.
type NATGatewayMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NATGatewayMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NATGatewayMultiError) AllErrors() []error { return m }

// NATGatewayValidationError is the validation error returned by
// NATGateway.Validate if the designated constraints aren't met.
type NATGatewayValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NATGatewayValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NATGatewayValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NATGatewayValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NATGatewayValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NATGatewayValidationError) ErrorName() string { return "NATGatewayValidationError" }

// Error satisfies the builtin error interface
func (e NATGatewayValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNATGateway.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NATGatewayValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NATGatewayValidationError{}

var _NATGateway_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on NATGatewayProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NATGatewayProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NATGatewayProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NATGatewayPropertiesMultiError, or nil if none found.
func (m *NATGatewayProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *NATGatewayProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubnetId

	// no validation rules for VpcId

	// no validation rules for InternalIp

	if all {
		switch v := interface{}(m.GetNatGwExternalIp()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, NATGatewayPropertiesValidationError{
					field:  "NatGwExternalIp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, NATGatewayPropertiesValidationError{
					field:  "NatGwExternalIp",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetNatGwExternalIp()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return NATGatewayPropertiesValidationError{
				field:  "NatGwExternalIp",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return NATGatewayPropertiesMultiError(errors)
	}

	return nil
}

// NATGatewayPropertiesMultiError is an error wrapping multiple validation
// errors returned by NATGatewayProperties.ValidateAll() if the designated
// constraints aren't met.
type NATGatewayPropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NATGatewayPropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NATGatewayPropertiesMultiError) AllErrors() []error { return m }

// NATGatewayPropertiesValidationError is the validation error returned by
// NATGatewayProperties.Validate if the designated constraints aren't met.
type NATGatewayPropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NATGatewayPropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NATGatewayPropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NATGatewayPropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NATGatewayPropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NATGatewayPropertiesValidationError) ErrorName() string {
	return "NATGatewayPropertiesValidationError"
}

// Error satisfies the builtin error interface
func (e NATGatewayPropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNATGatewayProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NATGatewayPropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NATGatewayPropertiesValidationError{}

// Validate checks the field values on NATGatewayExternalIP with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *NATGatewayExternalIP) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on NATGatewayExternalIP with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// NATGatewayExternalIPMultiError, or nil if none found.
func (m *NATGatewayExternalIP) ValidateAll() error {
	return m.validate(true)
}

func (m *NATGatewayExternalIP) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Cidr

	// no validation rules for Gateway

	if len(errors) > 0 {
		return NATGatewayExternalIPMultiError(errors)
	}

	return nil
}

// NATGatewayExternalIPMultiError is an error wrapping multiple validation
// errors returned by NATGatewayExternalIP.ValidateAll() if the designated
// constraints aren't met.
type NATGatewayExternalIPMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m NATGatewayExternalIPMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m NATGatewayExternalIPMultiError) AllErrors() []error { return m }

// NATGatewayExternalIPValidationError is the validation error returned by
// NATGatewayExternalIP.Validate if the designated constraints aren't met.
type NATGatewayExternalIPValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e NATGatewayExternalIPValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e NATGatewayExternalIPValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e NATGatewayExternalIPValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e NATGatewayExternalIPValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e NATGatewayExternalIPValidationError) ErrorName() string {
	return "NATGatewayExternalIPValidationError"
}

// Error satisfies the builtin error interface
func (e NATGatewayExternalIPValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sNATGatewayExternalIP.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = NATGatewayExternalIPValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = NATGatewayExternalIPValidationError{}
