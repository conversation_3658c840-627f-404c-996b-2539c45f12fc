// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/natGateway/v1/snatRule.proto

package natGateway

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListSNATRulesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSNATRulesRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSNATRulesRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSNATRulesRequestMultiError, or nil if none found.
func (m *ListSNATRulesRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSNATRulesRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	if len(errors) > 0 {
		return ListSNATRulesRequestMultiError(errors)
	}

	return nil
}

// ListSNATRulesRequestMultiError is an error wrapping multiple validation
// errors returned by ListSNATRulesRequest.ValidateAll() if the designated
// constraints aren't met.
type ListSNATRulesRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSNATRulesRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSNATRulesRequestMultiError) AllErrors() []error { return m }

// ListSNATRulesRequestValidationError is the validation error returned by
// ListSNATRulesRequest.Validate if the designated constraints aren't met.
type ListSNATRulesRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSNATRulesRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSNATRulesRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSNATRulesRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSNATRulesRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSNATRulesRequestValidationError) ErrorName() string {
	return "ListSNATRulesRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListSNATRulesRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSNATRulesRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSNATRulesRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSNATRulesRequestValidationError{}

// Validate checks the field values on GetSNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *GetSNATRuleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on GetSNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// GetSNATRuleRequestMultiError, or nil if none found.
func (m *GetSNATRuleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *GetSNATRuleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for SnatRuleName

	if len(errors) > 0 {
		return GetSNATRuleRequestMultiError(errors)
	}

	return nil
}

// GetSNATRuleRequestMultiError is an error wrapping multiple validation errors
// returned by GetSNATRuleRequest.ValidateAll() if the designated constraints
// aren't met.
type GetSNATRuleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m GetSNATRuleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m GetSNATRuleRequestMultiError) AllErrors() []error { return m }

// GetSNATRuleRequestValidationError is the validation error returned by
// GetSNATRuleRequest.Validate if the designated constraints aren't met.
type GetSNATRuleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e GetSNATRuleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e GetSNATRuleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e GetSNATRuleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e GetSNATRuleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e GetSNATRuleRequestValidationError) ErrorName() string {
	return "GetSNATRuleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e GetSNATRuleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sGetSNATRuleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = GetSNATRuleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = GetSNATRuleRequestValidationError{}

// Validate checks the field values on CreateSNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *CreateSNATRuleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on CreateSNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// CreateSNATRuleRequestMultiError, or nil if none found.
func (m *CreateSNATRuleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *CreateSNATRuleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	if utf8.RuneCountInString(m.GetSnatRuleName()) > 63 {
		err := CreateSNATRuleRequestValidationError{
			field:  "SnatRuleName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_CreateSNATRuleRequest_SnatRuleName_Pattern.MatchString(m.GetSnatRuleName()) {
		err := CreateSNATRuleRequestValidationError{
			field:  "SnatRuleName",
			reason: "value does not match regex pattern \"^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if all {
		switch v := interface{}(m.GetSnatRule()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, CreateSNATRuleRequestValidationError{
					field:  "SnatRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, CreateSNATRuleRequestValidationError{
					field:  "SnatRule",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetSnatRule()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return CreateSNATRuleRequestValidationError{
				field:  "SnatRule",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return CreateSNATRuleRequestMultiError(errors)
	}

	return nil
}

// CreateSNATRuleRequestMultiError is an error wrapping multiple validation
// errors returned by CreateSNATRuleRequest.ValidateAll() if the designated
// constraints aren't met.
type CreateSNATRuleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m CreateSNATRuleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m CreateSNATRuleRequestMultiError) AllErrors() []error { return m }

// CreateSNATRuleRequestValidationError is the validation error returned by
// CreateSNATRuleRequest.Validate if the designated constraints aren't met.
type CreateSNATRuleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e CreateSNATRuleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e CreateSNATRuleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e CreateSNATRuleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e CreateSNATRuleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e CreateSNATRuleRequestValidationError) ErrorName() string {
	return "CreateSNATRuleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e CreateSNATRuleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sCreateSNATRuleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = CreateSNATRuleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = CreateSNATRuleRequestValidationError{}

var _CreateSNATRuleRequest_SnatRuleName_Pattern = regexp.MustCompile("^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$")

// Validate checks the field values on DeleteSNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *DeleteSNATRuleRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on DeleteSNATRuleRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// DeleteSNATRuleRequestMultiError, or nil if none found.
func (m *DeleteSNATRuleRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *DeleteSNATRuleRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for SubscriptionName

	// no validation rules for ResourceGroupName

	// no validation rules for Zone

	// no validation rules for SnatRuleName

	if len(errors) > 0 {
		return DeleteSNATRuleRequestMultiError(errors)
	}

	return nil
}

// DeleteSNATRuleRequestMultiError is an error wrapping multiple validation
// errors returned by DeleteSNATRuleRequest.ValidateAll() if the designated
// constraints aren't met.
type DeleteSNATRuleRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m DeleteSNATRuleRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m DeleteSNATRuleRequestMultiError) AllErrors() []error { return m }

// DeleteSNATRuleRequestValidationError is the validation error returned by
// DeleteSNATRuleRequest.Validate if the designated constraints aren't met.
type DeleteSNATRuleRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e DeleteSNATRuleRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e DeleteSNATRuleRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e DeleteSNATRuleRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e DeleteSNATRuleRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e DeleteSNATRuleRequestValidationError) ErrorName() string {
	return "DeleteSNATRuleRequestValidationError"
}

// Error satisfies the builtin error interface
func (e DeleteSNATRuleRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sDeleteSNATRuleRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = DeleteSNATRuleRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = DeleteSNATRuleRequestValidationError{}

// Validate checks the field values on ListSNATRulesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListSNATRulesResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListSNATRulesResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListSNATRulesResponseMultiError, or nil if none found.
func (m *ListSNATRulesResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListSNATRulesResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	for idx, item := range m.GetSnatRules() {
		_, _ = idx, item

		if all {
			switch v := interface{}(item).(type) {
			case interface{ ValidateAll() error }:
				if err := v.ValidateAll(); err != nil {
					errors = append(errors, ListSNATRulesResponseValidationError{
						field:  fmt.Sprintf("SnatRules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			case interface{ Validate() error }:
				if err := v.Validate(); err != nil {
					errors = append(errors, ListSNATRulesResponseValidationError{
						field:  fmt.Sprintf("SnatRules[%v]", idx),
						reason: "embedded message failed validation",
						cause:  err,
					})
				}
			}
		} else if v, ok := interface{}(item).(interface{ Validate() error }); ok {
			if err := v.Validate(); err != nil {
				return ListSNATRulesResponseValidationError{
					field:  fmt.Sprintf("SnatRules[%v]", idx),
					reason: "embedded message failed validation",
					cause:  err,
				}
			}
		}

	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListSNATRulesResponseMultiError(errors)
	}

	return nil
}

// ListSNATRulesResponseMultiError is an error wrapping multiple validation
// errors returned by ListSNATRulesResponse.ValidateAll() if the designated
// constraints aren't met.
type ListSNATRulesResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListSNATRulesResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListSNATRulesResponseMultiError) AllErrors() []error { return m }

// ListSNATRulesResponseValidationError is the validation error returned by
// ListSNATRulesResponse.Validate if the designated constraints aren't met.
type ListSNATRulesResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListSNATRulesResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListSNATRulesResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListSNATRulesResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListSNATRulesResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListSNATRulesResponseValidationError) ErrorName() string {
	return "ListSNATRulesResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListSNATRulesResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListSNATRulesResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListSNATRulesResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListSNATRulesResponseValidationError{}

// Validate checks the field values on SNATRule with the rules defined in the
// proto definition for this message. If any rules are violated, the first
// error encountered is returned, or nil if there are no violations.
func (m *SNATRule) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SNATRule with the rules defined in
// the proto definition for this message. If any rules are violated, the
// result is a list of violation errors wrapped in SNATRuleMultiError, or nil
// if none found.
func (m *SNATRule) ValidateAll() error {
	return m.validate(true)
}

func (m *SNATRule) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Id

	// no validation rules for Name

	if utf8.RuneCountInString(m.GetDisplayName()) > 63 {
		err := SNATRuleValidationError{
			field:  "DisplayName",
			reason: "value length must be at most 63 runes",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	if !_SNATRule_DisplayName_Pattern.MatchString(m.GetDisplayName()) {
		err := SNATRuleValidationError{
			field:  "DisplayName",
			reason: "value does not match regex pattern \"^[a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}]([a-z0-9A-Z\\\\x{4e00}-\\\\x{9fa5}_-]{0,62})$\"",
		}
		if !all {
			return err
		}
		errors = append(errors, err)
	}

	// no validation rules for Description

	// no validation rules for Uid

	// no validation rules for ResourceType

	// no validation rules for CreatorId

	// no validation rules for OwnerId

	// no validation rules for TenantId

	// no validation rules for Zone

	// no validation rules for State

	// no validation rules for SkuId

	// no validation rules for Tags

	if all {
		switch v := interface{}(m.GetProperties()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SNATRuleValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SNATRuleValidationError{
					field:  "Properties",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetProperties()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SNATRuleValidationError{
				field:  "Properties",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetOrderInfo()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SNATRuleValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SNATRuleValidationError{
					field:  "OrderInfo",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetOrderInfo()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SNATRuleValidationError{
				field:  "OrderInfo",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	// no validation rules for Deleted

	if all {
		switch v := interface{}(m.GetCreateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SNATRuleValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SNATRuleValidationError{
					field:  "CreateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetCreateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SNATRuleValidationError{
				field:  "CreateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if all {
		switch v := interface{}(m.GetUpdateTime()).(type) {
		case interface{ ValidateAll() error }:
			if err := v.ValidateAll(); err != nil {
				errors = append(errors, SNATRuleValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		case interface{ Validate() error }:
			if err := v.Validate(); err != nil {
				errors = append(errors, SNATRuleValidationError{
					field:  "UpdateTime",
					reason: "embedded message failed validation",
					cause:  err,
				})
			}
		}
	} else if v, ok := interface{}(m.GetUpdateTime()).(interface{ Validate() error }); ok {
		if err := v.Validate(); err != nil {
			return SNATRuleValidationError{
				field:  "UpdateTime",
				reason: "embedded message failed validation",
				cause:  err,
			}
		}
	}

	if len(errors) > 0 {
		return SNATRuleMultiError(errors)
	}

	return nil
}

// SNATRuleMultiError is an error wrapping multiple validation errors returned
// by SNATRule.ValidateAll() if the designated constraints aren't met.
type SNATRuleMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SNATRuleMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SNATRuleMultiError) AllErrors() []error { return m }

// SNATRuleValidationError is the validation error returned by
// SNATRule.Validate if the designated constraints aren't met.
type SNATRuleValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SNATRuleValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SNATRuleValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SNATRuleValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SNATRuleValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SNATRuleValidationError) ErrorName() string { return "SNATRuleValidationError" }

// Error satisfies the builtin error interface
func (e SNATRuleValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSNATRule.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SNATRuleValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SNATRuleValidationError{}

var _SNATRule_DisplayName_Pattern = regexp.MustCompile("^[a-z0-9A-Z\\x{4e00}-\\x{9fa5}]([a-z0-9A-Z\\x{4e00}-\\x{9fa5}_-]{0,62})$")

// Validate checks the field values on SNATRuleProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *SNATRuleProperties) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on SNATRuleProperties with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// SNATRulePropertiesMultiError, or nil if none found.
func (m *SNATRuleProperties) ValidateAll() error {
	return m.validate(true)
}

func (m *SNATRuleProperties) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for NatGatewayId

	// no validation rules for ExternalIp

	// no validation rules for InternalCidr

	// no validation rules for Priority

	if len(errors) > 0 {
		return SNATRulePropertiesMultiError(errors)
	}

	return nil
}

// SNATRulePropertiesMultiError is an error wrapping multiple validation errors
// returned by SNATRuleProperties.ValidateAll() if the designated constraints
// aren't met.
type SNATRulePropertiesMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m SNATRulePropertiesMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m SNATRulePropertiesMultiError) AllErrors() []error { return m }

// SNATRulePropertiesValidationError is the validation error returned by
// SNATRuleProperties.Validate if the designated constraints aren't met.
type SNATRulePropertiesValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e SNATRulePropertiesValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e SNATRulePropertiesValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e SNATRulePropertiesValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e SNATRulePropertiesValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e SNATRulePropertiesValidationError) ErrorName() string {
	return "SNATRulePropertiesValidationError"
}

// Error satisfies the builtin error interface
func (e SNATRulePropertiesValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sSNATRuleProperties.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = SNATRulePropertiesValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = SNATRulePropertiesValidationError{}
