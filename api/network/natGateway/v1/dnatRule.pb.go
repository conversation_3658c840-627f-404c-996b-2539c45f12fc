// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/natGateway/v1/dnatRule.proto

package natGateway

import (
	_ "github.com/envoyproxy/protoc-gen-validate/validate"
	_ "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/api/annotations"
	v1 "gitlab.bj.sensetime.com/elementary/higgs/elementary-apis/api/higgs/common/v1"
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	emptypb "google.golang.org/protobuf/types/known/emptypb"
	timestamppb "google.golang.org/protobuf/types/known/timestamppb"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// Represents the different states of a DNATRule.
type DNATRule_State int32

const (
	// The DNATRule resource is being created.
	DNATRule_CREATING DNATRule_State = 0
	// The VPC resource has been created.
	DNATRule_CREATED DNATRule_State = 1
	// The VPC resource is being bound.
	DNATRule_BINDING DNATRule_State = 2
	// The VPC resource is being unbound.
	DNATRule_UNBINDING DNATRule_State = 3
	// The DNATRule resource is being updated.
	DNATRule_UPDATING DNATRule_State = 4
	// The DNATRule resource has been active.
	DNATRule_ACTIVE DNATRule_State = 5
	// The DNATRule resource is being deleted.
	DNATRule_DELETING DNATRule_State = 6
	// The DNATRule resource has been deleted.
	DNATRule_DELETED DNATRule_State = 7
	// The VPC resource has been failed.
	DNATRule_FAILED DNATRule_State = 8
)

// Enum value maps for DNATRule_State.
var (
	DNATRule_State_name = map[int32]string{
		0: "CREATING",
		1: "CREATED",
		2: "BINDING",
		3: "UNBINDING",
		4: "UPDATING",
		5: "ACTIVE",
		6: "DELETING",
		7: "DELETED",
		8: "FAILED",
	}
	DNATRule_State_value = map[string]int32{
		"CREATING":  0,
		"CREATED":   1,
		"BINDING":   2,
		"UNBINDING": 3,
		"UPDATING":  4,
		"ACTIVE":    5,
		"DELETING":  6,
		"DELETED":   7,
		"FAILED":    8,
	}
)

func (x DNATRule_State) Enum() *DNATRule_State {
	p := new(DNATRule_State)
	*p = x
	return p
}

func (x DNATRule_State) String() string {
	return protoimpl.X.EnumStringOf(x.Descriptor(), protoreflect.EnumNumber(x))
}

func (DNATRule_State) Descriptor() protoreflect.EnumDescriptor {
	return file_network_natGateway_v1_dnatRule_proto_enumTypes[0].Descriptor()
}

func (DNATRule_State) Type() protoreflect.EnumType {
	return &file_network_natGateway_v1_dnatRule_proto_enumTypes[0]
}

func (x DNATRule_State) Number() protoreflect.EnumNumber {
	return protoreflect.EnumNumber(x)
}

// Deprecated: Use DNATRule_State.Descriptor instead.
func (DNATRule_State) EnumDescriptor() ([]byte, []int) {
	return file_network_natGateway_v1_dnatRule_proto_rawDescGZIP(), []int{5, 0}
}

// 列举 DNATRules 的请求.
// [EN] Request to list DNATRules.
type ListDNATRulesRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// List filter.
	Filter string `protobuf:"bytes,4,opt,name=filter,proto3" json:"filter,omitempty"`
	// Sort resoults.
	OrderBy string `protobuf:"bytes,5,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// The maximum number of items to return.
	PageSize int32 `protobuf:"varint,6,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// The next_page_token value returned from a previous List request, if any.
	PageToken string `protobuf:"bytes,7,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
}

func (x *ListDNATRulesRequest) Reset() {
	*x = ListDNATRulesRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_natGateway_v1_dnatRule_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDNATRulesRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDNATRulesRequest) ProtoMessage() {}

func (x *ListDNATRulesRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_natGateway_v1_dnatRule_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDNATRulesRequest.ProtoReflect.Descriptor instead.
func (*ListDNATRulesRequest) Descriptor() ([]byte, []int) {
	return file_network_natGateway_v1_dnatRule_proto_rawDescGZIP(), []int{0}
}

func (x *ListDNATRulesRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *ListDNATRulesRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *ListDNATRulesRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *ListDNATRulesRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListDNATRulesRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListDNATRulesRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListDNATRulesRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// 获取某一个 DNATRule 的请求.
// [EN] Request to get a DNATRule.
type GetDNATRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	DnatRuleName string `protobuf:"bytes,4,opt,name=dnat_rule_name,json=dnatRuleName,proto3" json:"dnat_rule_name,omitempty"`
}

func (x *GetDNATRuleRequest) Reset() {
	*x = GetDNATRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_natGateway_v1_dnatRule_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *GetDNATRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*GetDNATRuleRequest) ProtoMessage() {}

func (x *GetDNATRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_natGateway_v1_dnatRule_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use GetDNATRuleRequest.ProtoReflect.Descriptor instead.
func (*GetDNATRuleRequest) Descriptor() ([]byte, []int) {
	return file_network_natGateway_v1_dnatRule_proto_rawDescGZIP(), []int{1}
}

func (x *GetDNATRuleRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *GetDNATRuleRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *GetDNATRuleRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *GetDNATRuleRequest) GetDnatRuleName() string {
	if x != nil {
		return x.DnatRuleName
	}
	return ""
}

// CreateDNATRuleRequest.
type CreateDNATRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone, todo: add validation
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	DnatRuleName string `protobuf:"bytes,4,opt,name=dnat_rule_name,json=dnatRuleName,proto3" json:"dnat_rule_name,omitempty"`
	// The DNATRule resource to create.
	DnatRule *DNATRule `protobuf:"bytes,5,opt,name=dnat_rule,json=dnatRule,proto3" json:"dnat_rule,omitempty"`
}

func (x *CreateDNATRuleRequest) Reset() {
	*x = CreateDNATRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_natGateway_v1_dnatRule_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *CreateDNATRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*CreateDNATRuleRequest) ProtoMessage() {}

func (x *CreateDNATRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_natGateway_v1_dnatRule_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use CreateDNATRuleRequest.ProtoReflect.Descriptor instead.
func (*CreateDNATRuleRequest) Descriptor() ([]byte, []int) {
	return file_network_natGateway_v1_dnatRule_proto_rawDescGZIP(), []int{2}
}

func (x *CreateDNATRuleRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *CreateDNATRuleRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *CreateDNATRuleRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *CreateDNATRuleRequest) GetDnatRuleName() string {
	if x != nil {
		return x.DnatRuleName
	}
	return ""
}

func (x *CreateDNATRuleRequest) GetDnatRule() *DNATRule {
	if x != nil {
		return x.DnatRule
	}
	return nil
}

// 删除某一个 DNATRule 的请求.
// [EN] Request to delete a DNATRule.
type DeleteDNATRuleRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// Subscription
	SubscriptionName string `protobuf:"bytes,1,opt,name=subscription_name,json=subscriptionName,proto3" json:"subscription_name,omitempty"`
	// Resource group
	ResourceGroupName string `protobuf:"bytes,2,opt,name=resource_group_name,json=resourceGroupName,proto3" json:"resource_group_name,omitempty"`
	// Available zone
	Zone string `protobuf:"bytes,3,opt,name=zone,proto3" json:"zone,omitempty"`
	// The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	DnatRuleName string `protobuf:"bytes,4,opt,name=dnat_rule_name,json=dnatRuleName,proto3" json:"dnat_rule_name,omitempty"`
}

func (x *DeleteDNATRuleRequest) Reset() {
	*x = DeleteDNATRuleRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_natGateway_v1_dnatRule_proto_msgTypes[3]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DeleteDNATRuleRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DeleteDNATRuleRequest) ProtoMessage() {}

func (x *DeleteDNATRuleRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_natGateway_v1_dnatRule_proto_msgTypes[3]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DeleteDNATRuleRequest.ProtoReflect.Descriptor instead.
func (*DeleteDNATRuleRequest) Descriptor() ([]byte, []int) {
	return file_network_natGateway_v1_dnatRule_proto_rawDescGZIP(), []int{3}
}

func (x *DeleteDNATRuleRequest) GetSubscriptionName() string {
	if x != nil {
		return x.SubscriptionName
	}
	return ""
}

func (x *DeleteDNATRuleRequest) GetResourceGroupName() string {
	if x != nil {
		return x.ResourceGroupName
	}
	return ""
}

func (x *DeleteDNATRuleRequest) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DeleteDNATRuleRequest) GetDnatRuleName() string {
	if x != nil {
		return x.DnatRuleName
	}
	return ""
}

// 列举 DNATRules 的响应.
// [EN] Response to list DNATRules.
type ListDNATRulesResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// DNATRule 列表.
	// [EN] DNATRule list.
	DnatRules []*DNATRule `protobuf:"bytes,1,rep,name=dnat_rules,json=dnatRules,proto3" json:"dnat_rules,omitempty"`
	// 下一个页面的 token，如果没有更多数据则为空.
	// [EN] Token to retrieve the next page of results, or empty if there are no more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListDNATRulesResponse) Reset() {
	*x = ListDNATRulesResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_natGateway_v1_dnatRule_proto_msgTypes[4]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListDNATRulesResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListDNATRulesResponse) ProtoMessage() {}

func (x *ListDNATRulesResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_natGateway_v1_dnatRule_proto_msgTypes[4]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListDNATRulesResponse.ProtoReflect.Descriptor instead.
func (*ListDNATRulesResponse) Descriptor() ([]byte, []int) {
	return file_network_natGateway_v1_dnatRule_proto_rawDescGZIP(), []int{4}
}

func (x *ListDNATRulesResponse) GetDnatRules() []*DNATRule {
	if x != nil {
		return x.DnatRules
	}
	return nil
}

func (x *ListDNATRulesResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListDNATRulesResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

// DNATRule 实例结构体.
// [EN] DNATRule entity.
type DNATRule struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// The DNATRule resource id using the form:
	//     `subscriptions/{subscription_id}/resourceGroups/{resource_group_name}/zones/{zone_id}/dnatRules/{dnat_rule_name}`.
	Id string `protobuf:"bytes,1,opt,name=id,proto3" json:"id,omitempty"`
	// The DNATRule resource name with the restriction: `^[a-z]([a-z0-9-]{0,61}[a-z0-9])?$`.
	Name string `protobuf:"bytes,2,opt,name=name,proto3" json:"name,omitempty"`
	// ContainerInstance resource display name
	DisplayName string `protobuf:"bytes,3,opt,name=display_name,json=displayName,proto3" json:"display_name,omitempty"`
	// ContainerInstance resource description
	Description string `protobuf:"bytes,4,opt,name=description,proto3" json:"description,omitempty"`
	// The DNATRule resource uuid.
	Uid string `protobuf:"bytes,5,opt,name=uid,proto3" json:"uid,omitempty"`
	// The DNATRule resource type.
	ResourceType string `protobuf:"bytes,6,opt,name=resource_type,json=resourceType,proto3" json:"resource_type,omitempty"`
	// The id of the user who created the DNATRule resource.
	CreatorId string `protobuf:"bytes,7,opt,name=creator_id,json=creatorId,proto3" json:"creator_id,omitempty"`
	// The id of the user who owns the DNATRule resource.
	OwnerId string `protobuf:"bytes,8,opt,name=owner_id,json=ownerId,proto3" json:"owner_id,omitempty"`
	// Tenant id.
	TenantId string `protobuf:"bytes,9,opt,name=tenant_id,json=tenantId,proto3" json:"tenant_id,omitempty"`
	// Available zone.
	Zone string `protobuf:"bytes,10,opt,name=zone,proto3" json:"zone,omitempty"`
	// The current state of the DNATRule resource.
	State DNATRule_State `protobuf:"varint,11,opt,name=state,proto3,enum=sensetime.core.network.natGateway.v1.DNATRule_State" json:"state,omitempty"`
	// Sku id.
	SkuId string `protobuf:"bytes,12,opt,name=sku_id,json=skuId,proto3" json:"sku_id,omitempty"`
	// Tags attached to the DNATRule resource.
	Tags map[string]string `protobuf:"bytes,13,rep,name=tags,proto3" json:"tags,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// Properties of the DNATRule resource.
	Properties *DNATRuleProperties `protobuf:"bytes,14,opt,name=properties,proto3" json:"properties,omitempty"`
	// Payment information.
	OrderInfo *v1.OrderInfo `protobuf:"bytes,15,opt,name=order_info,json=orderInfo,proto3" json:"order_info,omitempty"`
	// Indicates whether the DNATRule resource is deleted or not.
	Deleted bool `protobuf:"varint,16,opt,name=deleted,proto3" json:"deleted,omitempty"`
	// The time when the DNATRule resource was created.
	CreateTime *timestamppb.Timestamp `protobuf:"bytes,17,opt,name=create_time,json=createTime,proto3" json:"create_time,omitempty"`
	// The time when the DNATRule resource was last updated.
	UpdateTime *timestamppb.Timestamp `protobuf:"bytes,18,opt,name=update_time,json=updateTime,proto3" json:"update_time,omitempty"`
}

func (x *DNATRule) Reset() {
	*x = DNATRule{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_natGateway_v1_dnatRule_proto_msgTypes[5]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DNATRule) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DNATRule) ProtoMessage() {}

func (x *DNATRule) ProtoReflect() protoreflect.Message {
	mi := &file_network_natGateway_v1_dnatRule_proto_msgTypes[5]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DNATRule.ProtoReflect.Descriptor instead.
func (*DNATRule) Descriptor() ([]byte, []int) {
	return file_network_natGateway_v1_dnatRule_proto_rawDescGZIP(), []int{5}
}

func (x *DNATRule) GetId() string {
	if x != nil {
		return x.Id
	}
	return ""
}

func (x *DNATRule) GetName() string {
	if x != nil {
		return x.Name
	}
	return ""
}

func (x *DNATRule) GetDisplayName() string {
	if x != nil {
		return x.DisplayName
	}
	return ""
}

func (x *DNATRule) GetDescription() string {
	if x != nil {
		return x.Description
	}
	return ""
}

func (x *DNATRule) GetUid() string {
	if x != nil {
		return x.Uid
	}
	return ""
}

func (x *DNATRule) GetResourceType() string {
	if x != nil {
		return x.ResourceType
	}
	return ""
}

func (x *DNATRule) GetCreatorId() string {
	if x != nil {
		return x.CreatorId
	}
	return ""
}

func (x *DNATRule) GetOwnerId() string {
	if x != nil {
		return x.OwnerId
	}
	return ""
}

func (x *DNATRule) GetTenantId() string {
	if x != nil {
		return x.TenantId
	}
	return ""
}

func (x *DNATRule) GetZone() string {
	if x != nil {
		return x.Zone
	}
	return ""
}

func (x *DNATRule) GetState() DNATRule_State {
	if x != nil {
		return x.State
	}
	return DNATRule_CREATING
}

func (x *DNATRule) GetSkuId() string {
	if x != nil {
		return x.SkuId
	}
	return ""
}

func (x *DNATRule) GetTags() map[string]string {
	if x != nil {
		return x.Tags
	}
	return nil
}

func (x *DNATRule) GetProperties() *DNATRuleProperties {
	if x != nil {
		return x.Properties
	}
	return nil
}

func (x *DNATRule) GetOrderInfo() *v1.OrderInfo {
	if x != nil {
		return x.OrderInfo
	}
	return nil
}

func (x *DNATRule) GetDeleted() bool {
	if x != nil {
		return x.Deleted
	}
	return false
}

func (x *DNATRule) GetCreateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.CreateTime
	}
	return nil
}

func (x *DNATRule) GetUpdateTime() *timestamppb.Timestamp {
	if x != nil {
		return x.UpdateTime
	}
	return nil
}

// 资源实际属性.
// [EN] Real resource properties.
type DNATRuleProperties struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// 所属的 NATGateway uid.
	// [EN] NATGateway uid.
	NatGatewayId string `protobuf:"bytes,1,opt,name=nat_gateway_id,json=natGatewayId,proto3" json:"nat_gateway_id,omitempty"`
	// 所属的 EIP uid.
	// [EN] EIP uid.
	EipId string `protobuf:"bytes,2,opt,name=eip_id,json=eipId,proto3" json:"eip_id,omitempty"`
	// VPC 对外暴露的 DNATRule external IP.
	// [EN] DNATRule external IP in VPC.
	ExternalIp string `protobuf:"bytes,3,opt,name=external_ip,json=externalIp,proto3" json:"external_ip,omitempty"`
	// 对外暴露的端口,（10000，10000-20000，(10000,10001,20000,20001),*）.
	// [EN] External port,（10000，10000-20000，(10000,10001,20000,20001),*）.
	ExternalPort string `protobuf:"bytes,4,opt,name=external_port,json=externalPort,proto3" json:"external_port,omitempty"`
	// 对外暴露的协议.
	// [EN] External protocol.
	Protocol string `protobuf:"bytes,5,opt,name=protocol,proto3" json:"protocol,omitempty"`
	// 对外暴露的 VPC 内服务的 IP.
	// [EN] Exposed service IP in the VPC.
	InternalIp string `protobuf:"bytes,6,opt,name=internal_ip,json=internalIp,proto3" json:"internal_ip,omitempty"`
	// 对外暴露的 VPC 内服务的端口,（10000，10000-20000，(10000,10001,20000,20001),*）.
	// [EN] Exposed service port in the VPC,（10000，10000-20000，(10000,10001,20000,20001),*）.
	InternalPort string `protobuf:"bytes,7,opt,name=internal_port,json=internalPort,proto3" json:"internal_port,omitempty"`
	// 规则优先级.
	// [EN] NAT rule priority.
	Priority int32 `protobuf:"varint,8,opt,name=priority,proto3" json:"priority,omitempty"`
}

func (x *DNATRuleProperties) Reset() {
	*x = DNATRuleProperties{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_natGateway_v1_dnatRule_proto_msgTypes[6]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *DNATRuleProperties) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*DNATRuleProperties) ProtoMessage() {}

func (x *DNATRuleProperties) ProtoReflect() protoreflect.Message {
	mi := &file_network_natGateway_v1_dnatRule_proto_msgTypes[6]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use DNATRuleProperties.ProtoReflect.Descriptor instead.
func (*DNATRuleProperties) Descriptor() ([]byte, []int) {
	return file_network_natGateway_v1_dnatRule_proto_rawDescGZIP(), []int{6}
}

func (x *DNATRuleProperties) GetNatGatewayId() string {
	if x != nil {
		return x.NatGatewayId
	}
	return ""
}

func (x *DNATRuleProperties) GetEipId() string {
	if x != nil {
		return x.EipId
	}
	return ""
}

func (x *DNATRuleProperties) GetExternalIp() string {
	if x != nil {
		return x.ExternalIp
	}
	return ""
}

func (x *DNATRuleProperties) GetExternalPort() string {
	if x != nil {
		return x.ExternalPort
	}
	return ""
}

func (x *DNATRuleProperties) GetProtocol() string {
	if x != nil {
		return x.Protocol
	}
	return ""
}

func (x *DNATRuleProperties) GetInternalIp() string {
	if x != nil {
		return x.InternalIp
	}
	return ""
}

func (x *DNATRuleProperties) GetInternalPort() string {
	if x != nil {
		return x.InternalPort
	}
	return ""
}

func (x *DNATRuleProperties) GetPriority() int32 {
	if x != nil {
		return x.Priority
	}
	return 0
}

var File_network_natGateway_v1_dnatRule_proto protoreflect.FileDescriptor

var file_network_natGateway_v1_dnatRule_proto_rawDesc = []byte{
	0x0a, 0x24, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x6e, 0x61, 0x74, 0x47, 0x61, 0x74,
	0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x64, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65,
	0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x24, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d,
	0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x6e,
	0x61, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f,
	0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74,
	0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1b, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2f, 0x65, 0x6d, 0x70, 0x74,
	0x79, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x17, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74,
	0x65, 0x2f, 0x76, 0x61, 0x6c, 0x69, 0x64, 0x61, 0x74, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f,
	0x1a, 0x1f, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75,
	0x66, 0x2f, 0x74, 0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x2e, 0x70, 0x72, 0x6f, 0x74,
	0x6f, 0x1a, 0x1b, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x61, 0x6e, 0x6e,
	0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x1a, 0x1f,
	0x68, 0x69, 0x67, 0x67, 0x73, 0x2f, 0x63, 0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2f, 0x76, 0x31, 0x2f,
	0x6f, 0x72, 0x64, 0x65, 0x72, 0x69, 0x6e, 0x66, 0x6f, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x22,
	0xf6, 0x01, 0x0a, 0x14, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f,
	0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63,
	0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75,
	0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c,
	0x74, 0x65, 0x72, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65,
	0x72, 0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1b, 0x0a, 0x09,
	0x70, 0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x05, 0x52,
	0x08, 0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67,
	0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xab, 0x01, 0x0a, 0x12, 0x47, 0x65, 0x74,
	0x44, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12,
	0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04,
	0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65,
	0x12, 0x24, 0x0a, 0x0e, 0x64, 0x6e, 0x61, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6e, 0x61, 0x74, 0x52, 0x75,
	0x6c, 0x65, 0x4e, 0x61, 0x6d, 0x65, 0x22, 0xa7, 0x02, 0x0a, 0x15, 0x43, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x44, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74,
	0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a,
	0x13, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f,
	0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a,
	0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e,
	0x65, 0x12, 0x50, 0x0a, 0x0e, 0x64, 0x6e, 0x61, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x42, 0x2a, 0xfa, 0x42, 0x27, 0x72, 0x25,
	0x18, 0x3f, 0x32, 0x21, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x5d, 0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30,
	0x2d, 0x39, 0x2d, 0x5d, 0x7b, 0x30, 0x2c, 0x36, 0x31, 0x7d, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d,
	0x39, 0x5d, 0x29, 0x3f, 0x24, 0x52, 0x0c, 0x64, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x4e,
	0x61, 0x6d, 0x65, 0x12, 0x4b, 0x0a, 0x09, 0x64, 0x6e, 0x61, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65,
	0x18, 0x05, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x2e, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x6e, 0x61, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x4e,
	0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x08, 0x64, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65,
	0x22, 0xae, 0x01, 0x0a, 0x15, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x4e, 0x41, 0x54, 0x52,
	0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x2b, 0x0a, 0x11, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18,
	0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x10, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x2e, 0x0a, 0x13, 0x72, 0x65, 0x73, 0x6f, 0x75,
	0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x11, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72,
	0x6f, 0x75, 0x70, 0x4e, 0x61, 0x6d, 0x65, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18,
	0x03, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x24, 0x0a, 0x0e, 0x64,
	0x6e, 0x61, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x04, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0c, 0x64, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x4e, 0x61, 0x6d,
	0x65, 0x22, 0xad, 0x01, 0x0a, 0x15, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x4e, 0x41, 0x54, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x4d, 0x0a, 0x0a, 0x64,
	0x6e, 0x61, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32,
	0x2e, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x6e, 0x61, 0x74, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x52,
	0x09, 0x64, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x12, 0x26, 0x0a, 0x0f, 0x6e, 0x65,
	0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x02, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54, 0x6f, 0x6b,
	0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69, 0x7a, 0x65,
	0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53, 0x69, 0x7a,
	0x65, 0x22, 0x87, 0x08, 0x0a, 0x08, 0x44, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x0e,
	0x0a, 0x02, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x02, 0x69, 0x64, 0x12, 0x12,
	0x0a, 0x04, 0x6e, 0x61, 0x6d, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x04, 0x6e, 0x61,
	0x6d, 0x65, 0x12, 0x70, 0x0a, 0x0c, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x09, 0x42, 0x4d, 0xfa, 0x42, 0x4a, 0x72, 0x48, 0x18,
	0x3f, 0x32, 0x44, 0x5e, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78,
	0x7b, 0x34, 0x65, 0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5d,
	0x28, 0x5b, 0x61, 0x2d, 0x7a, 0x30, 0x2d, 0x39, 0x41, 0x2d, 0x5a, 0x5c, 0x78, 0x7b, 0x34, 0x65,
	0x30, 0x30, 0x7d, 0x2d, 0x5c, 0x78, 0x7b, 0x39, 0x66, 0x61, 0x35, 0x7d, 0x5f, 0x2d, 0x5d, 0x7b,
	0x30, 0x2c, 0x36, 0x32, 0x7d, 0x29, 0x24, 0x52, 0x0b, 0x64, 0x69, 0x73, 0x70, 0x6c, 0x61, 0x79,
	0x4e, 0x61, 0x6d, 0x65, 0x12, 0x20, 0x0a, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74,
	0x69, 0x6f, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0b, 0x64, 0x65, 0x73, 0x63, 0x72,
	0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x12, 0x10, 0x0a, 0x03, 0x75, 0x69, 0x64, 0x18, 0x05, 0x20,
	0x01, 0x28, 0x09, 0x52, 0x03, 0x75, 0x69, 0x64, 0x12, 0x23, 0x0a, 0x0d, 0x72, 0x65, 0x73, 0x6f,
	0x75, 0x72, 0x63, 0x65, 0x5f, 0x74, 0x79, 0x70, 0x65, 0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x0c, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x54, 0x79, 0x70, 0x65, 0x12, 0x1d, 0x0a,
	0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x07, 0x20, 0x01, 0x28,
	0x09, 0x52, 0x09, 0x63, 0x72, 0x65, 0x61, 0x74, 0x6f, 0x72, 0x49, 0x64, 0x12, 0x19, 0x0a, 0x08,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x5f, 0x69, 0x64, 0x18, 0x08, 0x20, 0x01, 0x28, 0x09, 0x52, 0x07,
	0x6f, 0x77, 0x6e, 0x65, 0x72, 0x49, 0x64, 0x12, 0x1b, 0x0a, 0x09, 0x74, 0x65, 0x6e, 0x61, 0x6e,
	0x74, 0x5f, 0x69, 0x64, 0x18, 0x09, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x74, 0x65, 0x6e, 0x61,
	0x6e, 0x74, 0x49, 0x64, 0x12, 0x12, 0x0a, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x18, 0x0a, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x04, 0x7a, 0x6f, 0x6e, 0x65, 0x12, 0x4a, 0x0a, 0x05, 0x73, 0x74, 0x61, 0x74,
	0x65, 0x18, 0x0b, 0x20, 0x01, 0x28, 0x0e, 0x32, 0x34, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x6e, 0x61, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x53, 0x74, 0x61, 0x74, 0x65, 0x52, 0x05, 0x73,
	0x74, 0x61, 0x74, 0x65, 0x12, 0x15, 0x0a, 0x06, 0x73, 0x6b, 0x75, 0x5f, 0x69, 0x64, 0x18, 0x0c,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x73, 0x6b, 0x75, 0x49, 0x64, 0x12, 0x4c, 0x0a, 0x04, 0x74,
	0x61, 0x67, 0x73, 0x18, 0x0d, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x38, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x6e, 0x61, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x2e, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x04, 0x74, 0x61, 0x67, 0x73, 0x12, 0x58, 0x0a, 0x0a, 0x70, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x18, 0x0e, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x38, 0x2e,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x6e, 0x61, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f,
	0x70, 0x65, 0x72, 0x74, 0x69, 0x65, 0x73, 0x52, 0x0a, 0x70, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74,
	0x69, 0x65, 0x73, 0x12, 0x48, 0x0a, 0x0a, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x69, 0x6e, 0x66,
	0x6f, 0x18, 0x0f, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x29, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x68, 0x69, 0x67, 0x67, 0x73, 0x2e, 0x63,
	0x6f, 0x6d, 0x6d, 0x6f, 0x6e, 0x2e, 0x76, 0x31, 0x2e, 0x4f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e,
	0x66, 0x6f, 0x52, 0x09, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x49, 0x6e, 0x66, 0x6f, 0x12, 0x18, 0x0a,
	0x07, 0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x18, 0x10, 0x20, 0x01, 0x28, 0x08, 0x52, 0x07,
	0x64, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x64, 0x12, 0x3b, 0x0a, 0x0b, 0x63, 0x72, 0x65, 0x61, 0x74,
	0x65, 0x5f, 0x74, 0x69, 0x6d, 0x65, 0x18, 0x11, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67,
	0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54,
	0x69, 0x6d, 0x65, 0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x63, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x54, 0x69, 0x6d, 0x65, 0x12, 0x3b, 0x0a, 0x0b, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x5f, 0x74,
	0x69, 0x6d, 0x65, 0x18, 0x12, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x1a, 0x2e, 0x67, 0x6f, 0x6f, 0x67,
	0x6c, 0x65, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x54, 0x69, 0x6d, 0x65,
	0x73, 0x74, 0x61, 0x6d, 0x70, 0x52, 0x0a, 0x75, 0x70, 0x64, 0x61, 0x74, 0x65, 0x54, 0x69, 0x6d,
	0x65, 0x1a, 0x37, 0x0a, 0x09, 0x54, 0x61, 0x67, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10,
	0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79,
	0x12, 0x14, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52,
	0x05, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x22, 0x7f, 0x0a, 0x05, 0x53, 0x74,
	0x61, 0x74, 0x65, 0x12, 0x0c, 0x0a, 0x08, 0x43, 0x52, 0x45, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10,
	0x00, 0x12, 0x0b, 0x0a, 0x07, 0x43, 0x52, 0x45, 0x41, 0x54, 0x45, 0x44, 0x10, 0x01, 0x12, 0x0b,
	0x0a, 0x07, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x02, 0x12, 0x0d, 0x0a, 0x09, 0x55,
	0x4e, 0x42, 0x49, 0x4e, 0x44, 0x49, 0x4e, 0x47, 0x10, 0x03, 0x12, 0x0c, 0x0a, 0x08, 0x55, 0x50,
	0x44, 0x41, 0x54, 0x49, 0x4e, 0x47, 0x10, 0x04, 0x12, 0x0a, 0x0a, 0x06, 0x41, 0x43, 0x54, 0x49,
	0x56, 0x45, 0x10, 0x05, 0x12, 0x0c, 0x0a, 0x08, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x49, 0x4e, 0x47,
	0x10, 0x06, 0x12, 0x0b, 0x0a, 0x07, 0x44, 0x45, 0x4c, 0x45, 0x54, 0x45, 0x44, 0x10, 0x07, 0x12,
	0x0a, 0x0a, 0x06, 0x46, 0x41, 0x49, 0x4c, 0x45, 0x44, 0x10, 0x08, 0x22, 0x95, 0x02, 0x0a, 0x12,
	0x44, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x50, 0x72, 0x6f, 0x70, 0x65, 0x72, 0x74, 0x69,
	0x65, 0x73, 0x12, 0x24, 0x0a, 0x0e, 0x6e, 0x61, 0x74, 0x5f, 0x67, 0x61, 0x74, 0x65, 0x77, 0x61,
	0x79, 0x5f, 0x69, 0x64, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x6e, 0x61, 0x74, 0x47,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x49, 0x64, 0x12, 0x15, 0x0a, 0x06, 0x65, 0x69, 0x70, 0x5f,
	0x69, 0x64, 0x18, 0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x05, 0x65, 0x69, 0x70, 0x49, 0x64, 0x12,
	0x1f, 0x0a, 0x0b, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x70, 0x18, 0x03,
	0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x49, 0x70,
	0x12, 0x23, 0x0a, 0x0d, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x70, 0x6f, 0x72,
	0x74, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x65, 0x78, 0x74, 0x65, 0x72, 0x6e, 0x61,
	0x6c, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x18, 0x05, 0x20, 0x01, 0x28, 0x09, 0x52, 0x08, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x63, 0x6f,
	0x6c, 0x12, 0x1f, 0x0a, 0x0b, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x69, 0x70,
	0x18, 0x06, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0a, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c,
	0x49, 0x70, 0x12, 0x23, 0x0a, 0x0d, 0x69, 0x6e, 0x74, 0x65, 0x72, 0x6e, 0x61, 0x6c, 0x5f, 0x70,
	0x6f, 0x72, 0x74, 0x18, 0x07, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0c, 0x69, 0x6e, 0x74, 0x65, 0x72,
	0x6e, 0x61, 0x6c, 0x50, 0x6f, 0x72, 0x74, 0x12, 0x1a, 0x0a, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x18, 0x08, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08, 0x70, 0x72, 0x69, 0x6f, 0x72,
	0x69, 0x74, 0x79, 0x32, 0xd0, 0x08, 0x0a, 0x09, 0x44, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65,
	0x73, 0x12, 0x8b, 0x02, 0x0a, 0x0d, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x4e, 0x41, 0x54, 0x52, 0x75,
	0x6c, 0x65, 0x73, 0x12, 0x3a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e,
	0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x6e, 0x61, 0x74,
	0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44,
	0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x3b, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x6e, 0x61, 0x74, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x44, 0x4e, 0x41, 0x54, 0x52,
	0x75, 0x6c, 0x65, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x80, 0x01, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x76, 0x12, 0x74, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f,
	0x6e, 0x61, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75,
	0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b,
	0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65,
	0x7d, 0x2f, 0x64, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x80, 0xb5, 0x18, 0x01, 0x12,
	0x8d, 0x02, 0x0a, 0x0b, 0x47, 0x65, 0x74, 0x44, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x12,
	0x38, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x6e, 0x61, 0x74, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x47, 0x65, 0x74, 0x44, 0x4e, 0x41, 0x54, 0x52, 0x75,
	0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a, 0x2e, 0x2e, 0x73, 0x65, 0x6e, 0x73,
	0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f,
	0x72, 0x6b, 0x2e, 0x6e, 0x61, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31,
	0x2e, 0x44, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x22, 0x93, 0x01, 0x82, 0xd3, 0xe4, 0x93,
	0x02, 0x88, 0x01, 0x12, 0x85, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x6e,
	0x61, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x31, 0x2f, 0x73, 0x75, 0x62,
	0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73,
	0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72,
	0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61,
	0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d,
	0x2f, 0x64, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x2f, 0x7b, 0x64, 0x6e, 0x61, 0x74,
	0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x80, 0xb5, 0x18, 0x01, 0x12,
	0xa2, 0x02, 0x0a, 0x0e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65, 0x44, 0x4e, 0x41, 0x54, 0x52, 0x75,
	0x6c, 0x65, 0x12, 0x3b, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x6e, 0x61, 0x74, 0x47,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x43, 0x72, 0x65, 0x61, 0x74, 0x65,
	0x44, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x2e, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x6e, 0x61, 0x74, 0x47, 0x61, 0x74, 0x65,
	0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x22,
	0xa2, 0x01, 0x82, 0xd3, 0xe4, 0x93, 0x02, 0x93, 0x01, 0x22, 0x85, 0x01, 0x2f, 0x6e, 0x65, 0x74,
	0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x6e, 0x61, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f,
	0x76, 0x31, 0x2f, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73,
	0x2f, 0x7b, 0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e,
	0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f,
	0x75, 0x70, 0x73, 0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72,
	0x6f, 0x75, 0x70, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f,
	0x7b, 0x7a, 0x6f, 0x6e, 0x65, 0x7d, 0x2f, 0x64, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73,
	0x2f, 0x7b, 0x64, 0x6e, 0x61, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x3a, 0x09, 0x64, 0x6e, 0x61, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x88, 0xb5, 0x18, 0x01,
	0x80, 0xb5, 0x18, 0x01, 0x12, 0xff, 0x01, 0x0a, 0x0e, 0x44, 0x65, 0x6c, 0x65, 0x74, 0x65, 0x44,
	0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x12, 0x3b, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74,
	0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b,
	0x2e, 0x6e, 0x61, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2e, 0x76, 0x31, 0x2e, 0x44,
	0x65, 0x6c, 0x65, 0x74, 0x65, 0x44, 0x4e, 0x41, 0x54, 0x52, 0x75, 0x6c, 0x65, 0x52, 0x65, 0x71,
	0x75, 0x65, 0x73, 0x74, 0x1a, 0x16, 0x2e, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2e, 0x70, 0x72,
	0x6f, 0x74, 0x6f, 0x62, 0x75, 0x66, 0x2e, 0x45, 0x6d, 0x70, 0x74, 0x79, 0x22, 0x97, 0x01, 0x82,
	0xd3, 0xe4, 0x93, 0x02, 0x88, 0x01, 0x2a, 0x85, 0x01, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72,
	0x6b, 0x2f, 0x6e, 0x61, 0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x31, 0x2f,
	0x73, 0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2f, 0x7b, 0x73,
	0x75, 0x62, 0x73, 0x63, 0x72, 0x69, 0x70, 0x74, 0x69, 0x6f, 0x6e, 0x5f, 0x6e, 0x61, 0x6d, 0x65,
	0x7d, 0x2f, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x47, 0x72, 0x6f, 0x75, 0x70, 0x73,
	0x2f, 0x7b, 0x72, 0x65, 0x73, 0x6f, 0x75, 0x72, 0x63, 0x65, 0x5f, 0x67, 0x72, 0x6f, 0x75, 0x70,
	0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x2f, 0x7a, 0x6f, 0x6e, 0x65, 0x73, 0x2f, 0x7b, 0x7a, 0x6f,
	0x6e, 0x65, 0x7d, 0x2f, 0x64, 0x6e, 0x61, 0x74, 0x52, 0x75, 0x6c, 0x65, 0x73, 0x2f, 0x7b, 0x64,
	0x6e, 0x61, 0x74, 0x5f, 0x72, 0x75, 0x6c, 0x65, 0x5f, 0x6e, 0x61, 0x6d, 0x65, 0x7d, 0x88, 0xb5,
	0x18, 0x01, 0x80, 0xb5, 0x18, 0x01, 0x42, 0x5e, 0x5a, 0x5c, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62,
	0x2e, 0x62, 0x6a, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f,
	0x6d, 0x2f, 0x65, 0x6c, 0x65, 0x6d, 0x65, 0x6e, 0x74, 0x61, 0x72, 0x79, 0x2f, 0x62, 0x6f, 0x73,
	0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65,
	0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x6e, 0x61,
	0x74, 0x47, 0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x2f, 0x76, 0x31, 0x3b, 0x6e, 0x61, 0x74, 0x47,
	0x61, 0x74, 0x65, 0x77, 0x61, 0x79, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_network_natGateway_v1_dnatRule_proto_rawDescOnce sync.Once
	file_network_natGateway_v1_dnatRule_proto_rawDescData = file_network_natGateway_v1_dnatRule_proto_rawDesc
)

func file_network_natGateway_v1_dnatRule_proto_rawDescGZIP() []byte {
	file_network_natGateway_v1_dnatRule_proto_rawDescOnce.Do(func() {
		file_network_natGateway_v1_dnatRule_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_natGateway_v1_dnatRule_proto_rawDescData)
	})
	return file_network_natGateway_v1_dnatRule_proto_rawDescData
}

var file_network_natGateway_v1_dnatRule_proto_enumTypes = make([]protoimpl.EnumInfo, 1)
var file_network_natGateway_v1_dnatRule_proto_msgTypes = make([]protoimpl.MessageInfo, 8)
var file_network_natGateway_v1_dnatRule_proto_goTypes = []interface{}{
	(DNATRule_State)(0),           // 0: sensetime.core.network.natGateway.v1.DNATRule.State
	(*ListDNATRulesRequest)(nil),  // 1: sensetime.core.network.natGateway.v1.ListDNATRulesRequest
	(*GetDNATRuleRequest)(nil),    // 2: sensetime.core.network.natGateway.v1.GetDNATRuleRequest
	(*CreateDNATRuleRequest)(nil), // 3: sensetime.core.network.natGateway.v1.CreateDNATRuleRequest
	(*DeleteDNATRuleRequest)(nil), // 4: sensetime.core.network.natGateway.v1.DeleteDNATRuleRequest
	(*ListDNATRulesResponse)(nil), // 5: sensetime.core.network.natGateway.v1.ListDNATRulesResponse
	(*DNATRule)(nil),              // 6: sensetime.core.network.natGateway.v1.DNATRule
	(*DNATRuleProperties)(nil),    // 7: sensetime.core.network.natGateway.v1.DNATRuleProperties
	nil,                           // 8: sensetime.core.network.natGateway.v1.DNATRule.TagsEntry
	(*v1.OrderInfo)(nil),          // 9: sensetime.core.higgs.common.v1.OrderInfo
	(*timestamppb.Timestamp)(nil), // 10: google.protobuf.Timestamp
	(*emptypb.Empty)(nil),         // 11: google.protobuf.Empty
}
var file_network_natGateway_v1_dnatRule_proto_depIdxs = []int32{
	6,  // 0: sensetime.core.network.natGateway.v1.CreateDNATRuleRequest.dnat_rule:type_name -> sensetime.core.network.natGateway.v1.DNATRule
	6,  // 1: sensetime.core.network.natGateway.v1.ListDNATRulesResponse.dnat_rules:type_name -> sensetime.core.network.natGateway.v1.DNATRule
	0,  // 2: sensetime.core.network.natGateway.v1.DNATRule.state:type_name -> sensetime.core.network.natGateway.v1.DNATRule.State
	8,  // 3: sensetime.core.network.natGateway.v1.DNATRule.tags:type_name -> sensetime.core.network.natGateway.v1.DNATRule.TagsEntry
	7,  // 4: sensetime.core.network.natGateway.v1.DNATRule.properties:type_name -> sensetime.core.network.natGateway.v1.DNATRuleProperties
	9,  // 5: sensetime.core.network.natGateway.v1.DNATRule.order_info:type_name -> sensetime.core.higgs.common.v1.OrderInfo
	10, // 6: sensetime.core.network.natGateway.v1.DNATRule.create_time:type_name -> google.protobuf.Timestamp
	10, // 7: sensetime.core.network.natGateway.v1.DNATRule.update_time:type_name -> google.protobuf.Timestamp
	1,  // 8: sensetime.core.network.natGateway.v1.DNATRules.ListDNATRules:input_type -> sensetime.core.network.natGateway.v1.ListDNATRulesRequest
	2,  // 9: sensetime.core.network.natGateway.v1.DNATRules.GetDNATRule:input_type -> sensetime.core.network.natGateway.v1.GetDNATRuleRequest
	3,  // 10: sensetime.core.network.natGateway.v1.DNATRules.CreateDNATRule:input_type -> sensetime.core.network.natGateway.v1.CreateDNATRuleRequest
	4,  // 11: sensetime.core.network.natGateway.v1.DNATRules.DeleteDNATRule:input_type -> sensetime.core.network.natGateway.v1.DeleteDNATRuleRequest
	5,  // 12: sensetime.core.network.natGateway.v1.DNATRules.ListDNATRules:output_type -> sensetime.core.network.natGateway.v1.ListDNATRulesResponse
	6,  // 13: sensetime.core.network.natGateway.v1.DNATRules.GetDNATRule:output_type -> sensetime.core.network.natGateway.v1.DNATRule
	6,  // 14: sensetime.core.network.natGateway.v1.DNATRules.CreateDNATRule:output_type -> sensetime.core.network.natGateway.v1.DNATRule
	11, // 15: sensetime.core.network.natGateway.v1.DNATRules.DeleteDNATRule:output_type -> google.protobuf.Empty
	12, // [12:16] is the sub-list for method output_type
	8,  // [8:12] is the sub-list for method input_type
	8,  // [8:8] is the sub-list for extension type_name
	8,  // [8:8] is the sub-list for extension extendee
	0,  // [0:8] is the sub-list for field type_name
}

func init() { file_network_natGateway_v1_dnatRule_proto_init() }
func file_network_natGateway_v1_dnatRule_proto_init() {
	if File_network_natGateway_v1_dnatRule_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_network_natGateway_v1_dnatRule_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDNATRulesRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_natGateway_v1_dnatRule_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*GetDNATRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_natGateway_v1_dnatRule_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*CreateDNATRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_natGateway_v1_dnatRule_proto_msgTypes[3].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DeleteDNATRuleRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_natGateway_v1_dnatRule_proto_msgTypes[4].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListDNATRulesResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_natGateway_v1_dnatRule_proto_msgTypes[5].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DNATRule); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_natGateway_v1_dnatRule_proto_msgTypes[6].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*DNATRuleProperties); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_natGateway_v1_dnatRule_proto_rawDesc,
			NumEnums:      1,
			NumMessages:   8,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_network_natGateway_v1_dnatRule_proto_goTypes,
		DependencyIndexes: file_network_natGateway_v1_dnatRule_proto_depIdxs,
		EnumInfos:         file_network_natGateway_v1_dnatRule_proto_enumTypes,
		MessageInfos:      file_network_natGateway_v1_dnatRule_proto_msgTypes,
	}.Build()
	File_network_natGateway_v1_dnatRule_proto = out.File
	file_network_natGateway_v1_dnatRule_proto_rawDesc = nil
	file_network_natGateway_v1_dnatRule_proto_goTypes = nil
	file_network_natGateway_v1_dnatRule_proto_depIdxs = nil
}
