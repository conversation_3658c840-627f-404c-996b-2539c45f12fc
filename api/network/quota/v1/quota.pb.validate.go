// Code generated by protoc-gen-validate. DO NOT EDIT.
// source: network/quota/v1/quota.proto

package quota

import (
	"bytes"
	"errors"
	"fmt"
	"net"
	"net/mail"
	"net/url"
	"regexp"
	"sort"
	"strings"
	"time"
	"unicode/utf8"

	"google.golang.org/protobuf/types/known/anypb"
)

// ensure the imports are used
var (
	_ = bytes.MinRead
	_ = errors.New("")
	_ = fmt.Print
	_ = utf8.UTFMax
	_ = (*regexp.Regexp)(nil)
	_ = (*strings.Reader)(nil)
	_ = net.IPv4len
	_ = time.Duration(0)
	_ = (*url.URL)(nil)
	_ = (*mail.Address)(nil)
	_ = anypb.Any{}
	_ = sort.Sort
)

// Validate checks the field values on ListQuotasRequest with the rules defined
// in the proto definition for this message. If any rules are violated, the
// first error encountered is returned, or nil if there are no violations.
func (m *ListQuotasRequest) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListQuotasRequest with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListQuotasRequestMultiError, or nil if none found.
func (m *ListQuotasRequest) ValidateAll() error {
	return m.validate(true)
}

func (m *ListQuotasRequest) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Filter

	// no validation rules for OrderBy

	// no validation rules for PageSize

	// no validation rules for PageToken

	if len(errors) > 0 {
		return ListQuotasRequestMultiError(errors)
	}

	return nil
}

// ListQuotasRequestMultiError is an error wrapping multiple validation errors
// returned by ListQuotasRequest.ValidateAll() if the designated constraints
// aren't met.
type ListQuotasRequestMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListQuotasRequestMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListQuotasRequestMultiError) AllErrors() []error { return m }

// ListQuotasRequestValidationError is the validation error returned by
// ListQuotasRequest.Validate if the designated constraints aren't met.
type ListQuotasRequestValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListQuotasRequestValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListQuotasRequestValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListQuotasRequestValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListQuotasRequestValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListQuotasRequestValidationError) ErrorName() string {
	return "ListQuotasRequestValidationError"
}

// Error satisfies the builtin error interface
func (e ListQuotasRequestValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListQuotasRequest.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListQuotasRequestValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListQuotasRequestValidationError{}

// Validate checks the field values on ListQuotasResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListQuotasResponse) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListQuotasResponse with the rules
// defined in the proto definition for this message. If any rules are
// violated, the result is a list of violation errors wrapped in
// ListQuotasResponseMultiError, or nil if none found.
func (m *ListQuotasResponse) ValidateAll() error {
	return m.validate(true)
}

func (m *ListQuotasResponse) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	{
		sorted_keys := make([]string, len(m.GetQuotas()))
		i := 0
		for key := range m.GetQuotas() {
			sorted_keys[i] = key
			i++
		}
		sort.Slice(sorted_keys, func(i, j int) bool { return sorted_keys[i] < sorted_keys[j] })
		for _, key := range sorted_keys {
			val := m.GetQuotas()[key]
			_ = val

			// no validation rules for Quotas[key]

			if all {
				switch v := interface{}(val).(type) {
				case interface{ ValidateAll() error }:
					if err := v.ValidateAll(); err != nil {
						errors = append(errors, ListQuotasResponseValidationError{
							field:  fmt.Sprintf("Quotas[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				case interface{ Validate() error }:
					if err := v.Validate(); err != nil {
						errors = append(errors, ListQuotasResponseValidationError{
							field:  fmt.Sprintf("Quotas[%v]", key),
							reason: "embedded message failed validation",
							cause:  err,
						})
					}
				}
			} else if v, ok := interface{}(val).(interface{ Validate() error }); ok {
				if err := v.Validate(); err != nil {
					return ListQuotasResponseValidationError{
						field:  fmt.Sprintf("Quotas[%v]", key),
						reason: "embedded message failed validation",
						cause:  err,
					}
				}
			}

		}
	}

	// no validation rules for NextPageToken

	// no validation rules for TotalSize

	if len(errors) > 0 {
		return ListQuotasResponseMultiError(errors)
	}

	return nil
}

// ListQuotasResponseMultiError is an error wrapping multiple validation errors
// returned by ListQuotasResponse.ValidateAll() if the designated constraints
// aren't met.
type ListQuotasResponseMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListQuotasResponseMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListQuotasResponseMultiError) AllErrors() []error { return m }

// ListQuotasResponseValidationError is the validation error returned by
// ListQuotasResponse.Validate if the designated constraints aren't met.
type ListQuotasResponseValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListQuotasResponseValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListQuotasResponseValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListQuotasResponseValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListQuotasResponseValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListQuotasResponseValidationError) ErrorName() string {
	return "ListQuotasResponseValidationError"
}

// Error satisfies the builtin error interface
func (e ListQuotasResponseValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListQuotasResponse.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListQuotasResponseValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListQuotasResponseValidationError{}

// Validate checks the field values on ListQuotasResponse_QuotaValue with the
// rules defined in the proto definition for this message. If any rules are
// violated, the first error encountered is returned, or nil if there are no violations.
func (m *ListQuotasResponse_QuotaValue) Validate() error {
	return m.validate(false)
}

// ValidateAll checks the field values on ListQuotasResponse_QuotaValue with
// the rules defined in the proto definition for this message. If any rules
// are violated, the result is a list of violation errors wrapped in
// ListQuotasResponse_QuotaValueMultiError, or nil if none found.
func (m *ListQuotasResponse_QuotaValue) ValidateAll() error {
	return m.validate(true)
}

func (m *ListQuotasResponse_QuotaValue) validate(all bool) error {
	if m == nil {
		return nil
	}

	var errors []error

	// no validation rules for Values

	if len(errors) > 0 {
		return ListQuotasResponse_QuotaValueMultiError(errors)
	}

	return nil
}

// ListQuotasResponse_QuotaValueMultiError is an error wrapping multiple
// validation errors returned by ListQuotasResponse_QuotaValue.ValidateAll()
// if the designated constraints aren't met.
type ListQuotasResponse_QuotaValueMultiError []error

// Error returns a concatenation of all the error messages it wraps.
func (m ListQuotasResponse_QuotaValueMultiError) Error() string {
	var msgs []string
	for _, err := range m {
		msgs = append(msgs, err.Error())
	}
	return strings.Join(msgs, "; ")
}

// AllErrors returns a list of validation violation errors.
func (m ListQuotasResponse_QuotaValueMultiError) AllErrors() []error { return m }

// ListQuotasResponse_QuotaValueValidationError is the validation error
// returned by ListQuotasResponse_QuotaValue.Validate if the designated
// constraints aren't met.
type ListQuotasResponse_QuotaValueValidationError struct {
	field  string
	reason string
	cause  error
	key    bool
}

// Field function returns field value.
func (e ListQuotasResponse_QuotaValueValidationError) Field() string { return e.field }

// Reason function returns reason value.
func (e ListQuotasResponse_QuotaValueValidationError) Reason() string { return e.reason }

// Cause function returns cause value.
func (e ListQuotasResponse_QuotaValueValidationError) Cause() error { return e.cause }

// Key function returns key value.
func (e ListQuotasResponse_QuotaValueValidationError) Key() bool { return e.key }

// ErrorName returns error name.
func (e ListQuotasResponse_QuotaValueValidationError) ErrorName() string {
	return "ListQuotasResponse_QuotaValueValidationError"
}

// Error satisfies the builtin error interface
func (e ListQuotasResponse_QuotaValueValidationError) Error() string {
	cause := ""
	if e.cause != nil {
		cause = fmt.Sprintf(" | caused by: %v", e.cause)
	}

	key := ""
	if e.key {
		key = "key for "
	}

	return fmt.Sprintf(
		"invalid %sListQuotasResponse_QuotaValue.%s: %s%s",
		key,
		e.field,
		e.reason,
		cause)
}

var _ error = ListQuotasResponse_QuotaValueValidationError{}

var _ interface {
	Field() string
	Reason() string
	Key() bool
	Cause() error
	ErrorName() string
} = ListQuotasResponse_QuotaValueValidationError{}
