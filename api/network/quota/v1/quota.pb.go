// Code generated by protoc-gen-go. DO NOT EDIT.
// versions:
// 	protoc-gen-go v1.28.0
// 	protoc        v3.20.0
// source: network/quota/v1/quota.proto

package quota

import (
	_ "google.golang.org/genproto/googleapis/api/annotations"
	protoreflect "google.golang.org/protobuf/reflect/protoreflect"
	protoimpl "google.golang.org/protobuf/runtime/protoimpl"
	reflect "reflect"
	sync "sync"
)

const (
	// Verify that this generated code is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(20 - protoimpl.MinVersion)
	// Verify that runtime/protoimpl is sufficiently up-to-date.
	_ = protoimpl.EnforceVersion(protoimpl.MaxVersion - 20)
)

// [zh] 列举 Quotas 的请求.
// [en] Request to list Quotas.
type ListQuotasRequest struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] 过滤条件
	// [en] List filter.
	Filter string `protobuf:"bytes,1,opt,name=filter,proto3" json:"filter,omitempty"`
	// [zh] 排序规则
	// [en] Sort rules.
	OrderBy string `protobuf:"bytes,2,opt,name=order_by,json=orderBy,proto3" json:"order_by,omitempty"`
	// [zh] 分页大小
	// [en] The maximum number of items to return.
	PageSize int32 `protobuf:"varint,3,opt,name=page_size,json=pageSize,proto3" json:"page_size,omitempty"`
	// [zh] 从上一个List请求返回的next_page_token值(如果有的话)
	// [en] The next_page_token value returned from a previous List request, if
	// any.
	PageToken string `protobuf:"bytes,4,opt,name=page_token,json=pageToken,proto3" json:"page_token,omitempty"`
}

func (x *ListQuotasRequest) Reset() {
	*x = ListQuotasRequest{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_quota_v1_quota_proto_msgTypes[0]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListQuotasRequest) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListQuotasRequest) ProtoMessage() {}

func (x *ListQuotasRequest) ProtoReflect() protoreflect.Message {
	mi := &file_network_quota_v1_quota_proto_msgTypes[0]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListQuotasRequest.ProtoReflect.Descriptor instead.
func (*ListQuotasRequest) Descriptor() ([]byte, []int) {
	return file_network_quota_v1_quota_proto_rawDescGZIP(), []int{0}
}

func (x *ListQuotasRequest) GetFilter() string {
	if x != nil {
		return x.Filter
	}
	return ""
}

func (x *ListQuotasRequest) GetOrderBy() string {
	if x != nil {
		return x.OrderBy
	}
	return ""
}

func (x *ListQuotasRequest) GetPageSize() int32 {
	if x != nil {
		return x.PageSize
	}
	return 0
}

func (x *ListQuotasRequest) GetPageToken() string {
	if x != nil {
		return x.PageToken
	}
	return ""
}

// [zh] 列举 Quotas 的响应.
// [en] Response to list Quotas.
type ListQuotasResponse struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	// [zh] Quotas 列表.
	// [en] Quotas list.
	Quotas map[string]*ListQuotasResponse_QuotaValue `protobuf:"bytes,1,rep,name=Quotas,proto3" json:"Quotas,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"bytes,2,opt,name=value,proto3"`
	// [zh] 下一个页面的 token，如果没有更多数据则为空.
	// [en] Token to retrieve the next page of results, or empty if there are no
	// more results in the list.
	NextPageToken string `protobuf:"bytes,2,opt,name=next_page_token,json=nextPageToken,proto3" json:"next_page_token,omitempty"`
	// [zh] Quotas 总数
	// [en] total size
	TotalSize int32 `protobuf:"varint,3,opt,name=total_size,json=totalSize,proto3" json:"total_size,omitempty"`
}

func (x *ListQuotasResponse) Reset() {
	*x = ListQuotasResponse{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_quota_v1_quota_proto_msgTypes[1]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListQuotasResponse) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListQuotasResponse) ProtoMessage() {}

func (x *ListQuotasResponse) ProtoReflect() protoreflect.Message {
	mi := &file_network_quota_v1_quota_proto_msgTypes[1]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListQuotasResponse.ProtoReflect.Descriptor instead.
func (*ListQuotasResponse) Descriptor() ([]byte, []int) {
	return file_network_quota_v1_quota_proto_rawDescGZIP(), []int{1}
}

func (x *ListQuotasResponse) GetQuotas() map[string]*ListQuotasResponse_QuotaValue {
	if x != nil {
		return x.Quotas
	}
	return nil
}

func (x *ListQuotasResponse) GetNextPageToken() string {
	if x != nil {
		return x.NextPageToken
	}
	return ""
}

func (x *ListQuotasResponse) GetTotalSize() int32 {
	if x != nil {
		return x.TotalSize
	}
	return 0
}

type ListQuotasResponse_QuotaValue struct {
	state         protoimpl.MessageState
	sizeCache     protoimpl.SizeCache
	unknownFields protoimpl.UnknownFields

	Values map[string]int64 `protobuf:"bytes,1,rep,name=values,proto3" json:"values,omitempty" protobuf_key:"bytes,1,opt,name=key,proto3" protobuf_val:"varint,2,opt,name=value,proto3"`
}

func (x *ListQuotasResponse_QuotaValue) Reset() {
	*x = ListQuotasResponse_QuotaValue{}
	if protoimpl.UnsafeEnabled {
		mi := &file_network_quota_v1_quota_proto_msgTypes[2]
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		ms.StoreMessageInfo(mi)
	}
}

func (x *ListQuotasResponse_QuotaValue) String() string {
	return protoimpl.X.MessageStringOf(x)
}

func (*ListQuotasResponse_QuotaValue) ProtoMessage() {}

func (x *ListQuotasResponse_QuotaValue) ProtoReflect() protoreflect.Message {
	mi := &file_network_quota_v1_quota_proto_msgTypes[2]
	if protoimpl.UnsafeEnabled && x != nil {
		ms := protoimpl.X.MessageStateOf(protoimpl.Pointer(x))
		if ms.LoadMessageInfo() == nil {
			ms.StoreMessageInfo(mi)
		}
		return ms
	}
	return mi.MessageOf(x)
}

// Deprecated: Use ListQuotasResponse_QuotaValue.ProtoReflect.Descriptor instead.
func (*ListQuotasResponse_QuotaValue) Descriptor() ([]byte, []int) {
	return file_network_quota_v1_quota_proto_rawDescGZIP(), []int{1, 0}
}

func (x *ListQuotasResponse_QuotaValue) GetValues() map[string]int64 {
	if x != nil {
		return x.Values
	}
	return nil
}

var File_network_quota_v1_quota_proto protoreflect.FileDescriptor

var file_network_quota_v1_quota_proto_rawDesc = []byte{
	0x0a, 0x1c, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x2f,
	0x76, 0x31, 0x2f, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x2e, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x12, 0x24,
	0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e,
	0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x2e, 0x64, 0x61, 0x74,
	0x61, 0x2e, 0x76, 0x31, 0x1a, 0x1c, 0x67, 0x6f, 0x6f, 0x67, 0x6c, 0x65, 0x2f, 0x61, 0x70, 0x69,
	0x2f, 0x61, 0x6e, 0x6e, 0x6f, 0x74, 0x61, 0x74, 0x69, 0x6f, 0x6e, 0x73, 0x2e, 0x70, 0x72, 0x6f,
	0x74, 0x6f, 0x22, 0x82, 0x01, 0x0a, 0x11, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x61,
	0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x12, 0x16, 0x0a, 0x06, 0x66, 0x69, 0x6c, 0x74,
	0x65, 0x72, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x06, 0x66, 0x69, 0x6c, 0x74, 0x65, 0x72,
	0x12, 0x19, 0x0a, 0x08, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x5f, 0x62, 0x79, 0x18, 0x02, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x07, 0x6f, 0x72, 0x64, 0x65, 0x72, 0x42, 0x79, 0x12, 0x1b, 0x0a, 0x09, 0x70,
	0x61, 0x67, 0x65, 0x5f, 0x73, 0x69, 0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x08,
	0x70, 0x61, 0x67, 0x65, 0x53, 0x69, 0x7a, 0x65, 0x12, 0x1d, 0x0a, 0x0a, 0x70, 0x61, 0x67, 0x65,
	0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18, 0x04, 0x20, 0x01, 0x28, 0x09, 0x52, 0x09, 0x70, 0x61,
	0x67, 0x65, 0x54, 0x6f, 0x6b, 0x65, 0x6e, 0x22, 0xec, 0x03, 0x0a, 0x12, 0x4c, 0x69, 0x73, 0x74,
	0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x12, 0x5c,
	0x0a, 0x06, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x18, 0x01, 0x20, 0x03, 0x28, 0x0b, 0x32, 0x44,
	0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x2e, 0x64, 0x61,
	0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73,
	0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x45,
	0x6e, 0x74, 0x72, 0x79, 0x52, 0x06, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x12, 0x26, 0x0a, 0x0f,
	0x6e, 0x65, 0x78, 0x74, 0x5f, 0x70, 0x61, 0x67, 0x65, 0x5f, 0x74, 0x6f, 0x6b, 0x65, 0x6e, 0x18,
	0x02, 0x20, 0x01, 0x28, 0x09, 0x52, 0x0d, 0x6e, 0x65, 0x78, 0x74, 0x50, 0x61, 0x67, 0x65, 0x54,
	0x6f, 0x6b, 0x65, 0x6e, 0x12, 0x1d, 0x0a, 0x0a, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x5f, 0x73, 0x69,
	0x7a, 0x65, 0x18, 0x03, 0x20, 0x01, 0x28, 0x05, 0x52, 0x09, 0x74, 0x6f, 0x74, 0x61, 0x6c, 0x53,
	0x69, 0x7a, 0x65, 0x1a, 0xb0, 0x01, 0x0a, 0x0a, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x56, 0x61, 0x6c,
	0x75, 0x65, 0x12, 0x67, 0x0a, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x18, 0x01, 0x20, 0x03,
	0x28, 0x0b, 0x32, 0x4f, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63,
	0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x71, 0x75, 0x6f, 0x74,
	0x61, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75,
	0x6f, 0x74, 0x61, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x2e, 0x51, 0x75, 0x6f,
	0x74, 0x61, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x2e, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e,
	0x74, 0x72, 0x79, 0x52, 0x06, 0x76, 0x61, 0x6c, 0x75, 0x65, 0x73, 0x1a, 0x39, 0x0a, 0x0b, 0x56,
	0x61, 0x6c, 0x75, 0x65, 0x73, 0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65,
	0x79, 0x18, 0x01, 0x20, 0x01, 0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x14, 0x0a, 0x05,
	0x76, 0x61, 0x6c, 0x75, 0x65, 0x18, 0x02, 0x20, 0x01, 0x28, 0x03, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x1a, 0x7e, 0x0a, 0x0b, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73,
	0x45, 0x6e, 0x74, 0x72, 0x79, 0x12, 0x10, 0x0a, 0x03, 0x6b, 0x65, 0x79, 0x18, 0x01, 0x20, 0x01,
	0x28, 0x09, 0x52, 0x03, 0x6b, 0x65, 0x79, 0x12, 0x59, 0x0a, 0x05, 0x76, 0x61, 0x6c, 0x75, 0x65,
	0x18, 0x02, 0x20, 0x01, 0x28, 0x0b, 0x32, 0x43, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x71, 0x75, 0x6f, 0x74, 0x61, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65,
	0x2e, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x56, 0x61, 0x6c, 0x75, 0x65, 0x52, 0x05, 0x76, 0x61, 0x6c,
	0x75, 0x65, 0x3a, 0x02, 0x38, 0x01, 0x32, 0xb7, 0x01, 0x0a, 0x0c, 0x51, 0x75, 0x6f, 0x74, 0x61,
	0x53, 0x65, 0x72, 0x76, 0x69, 0x63, 0x65, 0x12, 0xa6, 0x01, 0x0a, 0x0a, 0x4c, 0x69, 0x73, 0x74,
	0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x12, 0x37, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69,
	0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65, 0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e,
	0x71, 0x75, 0x6f, 0x74, 0x61, 0x2e, 0x64, 0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69,
	0x73, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x61, 0x73, 0x52, 0x65, 0x71, 0x75, 0x65, 0x73, 0x74, 0x1a,
	0x38, 0x2e, 0x73, 0x65, 0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x72, 0x65,
	0x2e, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2e, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x2e, 0x64,
	0x61, 0x74, 0x61, 0x2e, 0x76, 0x31, 0x2e, 0x4c, 0x69, 0x73, 0x74, 0x51, 0x75, 0x6f, 0x74, 0x61,
	0x73, 0x52, 0x65, 0x73, 0x70, 0x6f, 0x6e, 0x73, 0x65, 0x22, 0x25, 0x82, 0xd3, 0xe4, 0x93, 0x02,
	0x1f, 0x12, 0x1d, 0x2f, 0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x71, 0x75, 0x6f, 0x74,
	0x61, 0x2f, 0x64, 0x61, 0x74, 0x61, 0x2f, 0x76, 0x31, 0x2f, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x73,
	0x42, 0x54, 0x5a, 0x52, 0x67, 0x69, 0x74, 0x6c, 0x61, 0x62, 0x2e, 0x62, 0x6a, 0x2e, 0x73, 0x65,
	0x6e, 0x73, 0x65, 0x74, 0x69, 0x6d, 0x65, 0x2e, 0x63, 0x6f, 0x6d, 0x2f, 0x65, 0x6c, 0x65, 0x6d,
	0x65, 0x6e, 0x74, 0x61, 0x72, 0x79, 0x2f, 0x62, 0x6f, 0x73, 0x6f, 0x6e, 0x2f, 0x62, 0x6f, 0x73,
	0x6f, 0x6e, 0x2d, 0x70, 0x72, 0x6f, 0x76, 0x69, 0x64, 0x65, 0x72, 0x2f, 0x61, 0x70, 0x69, 0x2f,
	0x6e, 0x65, 0x74, 0x77, 0x6f, 0x72, 0x6b, 0x2f, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x2f, 0x76, 0x31,
	0x3b, 0x71, 0x75, 0x6f, 0x74, 0x61, 0x62, 0x06, 0x70, 0x72, 0x6f, 0x74, 0x6f, 0x33,
}

var (
	file_network_quota_v1_quota_proto_rawDescOnce sync.Once
	file_network_quota_v1_quota_proto_rawDescData = file_network_quota_v1_quota_proto_rawDesc
)

func file_network_quota_v1_quota_proto_rawDescGZIP() []byte {
	file_network_quota_v1_quota_proto_rawDescOnce.Do(func() {
		file_network_quota_v1_quota_proto_rawDescData = protoimpl.X.CompressGZIP(file_network_quota_v1_quota_proto_rawDescData)
	})
	return file_network_quota_v1_quota_proto_rawDescData
}

var file_network_quota_v1_quota_proto_msgTypes = make([]protoimpl.MessageInfo, 5)
var file_network_quota_v1_quota_proto_goTypes = []interface{}{
	(*ListQuotasRequest)(nil),             // 0: sensetime.core.network.quota.data.v1.ListQuotasRequest
	(*ListQuotasResponse)(nil),            // 1: sensetime.core.network.quota.data.v1.ListQuotasResponse
	(*ListQuotasResponse_QuotaValue)(nil), // 2: sensetime.core.network.quota.data.v1.ListQuotasResponse.QuotaValue
	nil,                                   // 3: sensetime.core.network.quota.data.v1.ListQuotasResponse.QuotasEntry
	nil,                                   // 4: sensetime.core.network.quota.data.v1.ListQuotasResponse.QuotaValue.ValuesEntry
}
var file_network_quota_v1_quota_proto_depIdxs = []int32{
	3, // 0: sensetime.core.network.quota.data.v1.ListQuotasResponse.Quotas:type_name -> sensetime.core.network.quota.data.v1.ListQuotasResponse.QuotasEntry
	4, // 1: sensetime.core.network.quota.data.v1.ListQuotasResponse.QuotaValue.values:type_name -> sensetime.core.network.quota.data.v1.ListQuotasResponse.QuotaValue.ValuesEntry
	2, // 2: sensetime.core.network.quota.data.v1.ListQuotasResponse.QuotasEntry.value:type_name -> sensetime.core.network.quota.data.v1.ListQuotasResponse.QuotaValue
	0, // 3: sensetime.core.network.quota.data.v1.QuotaService.ListQuotas:input_type -> sensetime.core.network.quota.data.v1.ListQuotasRequest
	1, // 4: sensetime.core.network.quota.data.v1.QuotaService.ListQuotas:output_type -> sensetime.core.network.quota.data.v1.ListQuotasResponse
	4, // [4:5] is the sub-list for method output_type
	3, // [3:4] is the sub-list for method input_type
	3, // [3:3] is the sub-list for extension type_name
	3, // [3:3] is the sub-list for extension extendee
	0, // [0:3] is the sub-list for field type_name
}

func init() { file_network_quota_v1_quota_proto_init() }
func file_network_quota_v1_quota_proto_init() {
	if File_network_quota_v1_quota_proto != nil {
		return
	}
	if !protoimpl.UnsafeEnabled {
		file_network_quota_v1_quota_proto_msgTypes[0].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListQuotasRequest); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_quota_v1_quota_proto_msgTypes[1].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListQuotasResponse); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
		file_network_quota_v1_quota_proto_msgTypes[2].Exporter = func(v interface{}, i int) interface{} {
			switch v := v.(*ListQuotasResponse_QuotaValue); i {
			case 0:
				return &v.state
			case 1:
				return &v.sizeCache
			case 2:
				return &v.unknownFields
			default:
				return nil
			}
		}
	}
	type x struct{}
	out := protoimpl.TypeBuilder{
		File: protoimpl.DescBuilder{
			GoPackagePath: reflect.TypeOf(x{}).PkgPath(),
			RawDescriptor: file_network_quota_v1_quota_proto_rawDesc,
			NumEnums:      0,
			NumMessages:   5,
			NumExtensions: 0,
			NumServices:   1,
		},
		GoTypes:           file_network_quota_v1_quota_proto_goTypes,
		DependencyIndexes: file_network_quota_v1_quota_proto_depIdxs,
		MessageInfos:      file_network_quota_v1_quota_proto_msgTypes,
	}.Build()
	File_network_quota_v1_quota_proto = out.File
	file_network_quota_v1_quota_proto_rawDesc = nil
	file_network_quota_v1_quota_proto_goTypes = nil
	file_network_quota_v1_quota_proto_depIdxs = nil
}
