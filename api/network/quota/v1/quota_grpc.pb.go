// Code generated by protoc-gen-go-grpc. DO NOT EDIT.
// versions:
// - protoc-gen-go-grpc v1.2.0
// - protoc             v3.20.0
// source: network/quota/v1/quota.proto

package quota

import (
	context "context"
	grpc "google.golang.org/grpc"
	codes "google.golang.org/grpc/codes"
	status "google.golang.org/grpc/status"
)

// This is a compile-time assertion to ensure that this generated file
// is compatible with the grpc package it is being compiled against.
// Requires gRPC-Go v1.32.0 or later.
const _ = grpc.SupportPackageIsVersion7

// QuotaServiceClient is the client API for QuotaService service.
//
// For semantics around ctx use and closing/ending streaming RPCs, please refer to https://pkg.go.dev/google.golang.org/grpc/?tab=doc#ClientConn.NewStream.
type QuotaServiceClient interface {
	// [zh] 列举产品的所有 Quotas.
	// [en] List requested Quotas.
	ListQuotas(ctx context.Context, in *ListQuotasRequest, opts ...grpc.CallOption) (*ListQuotasResponse, error)
}

type quotaServiceClient struct {
	cc grpc.ClientConnInterface
}

func NewQuotaServiceClient(cc grpc.ClientConnInterface) QuotaServiceClient {
	return &quotaServiceClient{cc}
}

func (c *quotaServiceClient) ListQuotas(ctx context.Context, in *ListQuotasRequest, opts ...grpc.CallOption) (*ListQuotasResponse, error) {
	out := new(ListQuotasResponse)
	err := c.cc.Invoke(ctx, "/sensetime.core.network.quota.data.v1.QuotaService/ListQuotas", in, out, opts...)
	if err != nil {
		return nil, err
	}
	return out, nil
}

// QuotaServiceServer is the server API for QuotaService service.
// All implementations must embed UnimplementedQuotaServiceServer
// for forward compatibility
type QuotaServiceServer interface {
	// [zh] 列举产品的所有 Quotas.
	// [en] List requested Quotas.
	ListQuotas(context.Context, *ListQuotasRequest) (*ListQuotasResponse, error)
	mustEmbedUnimplementedQuotaServiceServer()
}

// UnimplementedQuotaServiceServer must be embedded to have forward compatible implementations.
type UnimplementedQuotaServiceServer struct {
}

func (UnimplementedQuotaServiceServer) ListQuotas(context.Context, *ListQuotasRequest) (*ListQuotasResponse, error) {
	return nil, status.Errorf(codes.Unimplemented, "method ListQuotas not implemented")
}
func (UnimplementedQuotaServiceServer) mustEmbedUnimplementedQuotaServiceServer() {}

// UnsafeQuotaServiceServer may be embedded to opt out of forward compatibility for this service.
// Use of this interface is not recommended, as added methods to QuotaServiceServer will
// result in compilation errors.
type UnsafeQuotaServiceServer interface {
	mustEmbedUnimplementedQuotaServiceServer()
}

func RegisterQuotaServiceServer(s grpc.ServiceRegistrar, srv QuotaServiceServer) {
	s.RegisterService(&QuotaService_ServiceDesc, srv)
}

func _QuotaService_ListQuotas_Handler(srv interface{}, ctx context.Context, dec func(interface{}) error, interceptor grpc.UnaryServerInterceptor) (interface{}, error) {
	in := new(ListQuotasRequest)
	if err := dec(in); err != nil {
		return nil, err
	}
	if interceptor == nil {
		return srv.(QuotaServiceServer).ListQuotas(ctx, in)
	}
	info := &grpc.UnaryServerInfo{
		Server:     srv,
		FullMethod: "/sensetime.core.network.quota.data.v1.QuotaService/ListQuotas",
	}
	handler := func(ctx context.Context, req interface{}) (interface{}, error) {
		return srv.(QuotaServiceServer).ListQuotas(ctx, req.(*ListQuotasRequest))
	}
	return interceptor(ctx, in, info, handler)
}

// QuotaService_ServiceDesc is the grpc.ServiceDesc for QuotaService service.
// It's only intended for direct use with grpc.RegisterService,
// and not to be introspected or modified (even as a copy)
var QuotaService_ServiceDesc = grpc.ServiceDesc{
	ServiceName: "sensetime.core.network.quota.data.v1.QuotaService",
	HandlerType: (*QuotaServiceServer)(nil),
	Methods: []grpc.MethodDesc{
		{
			MethodName: "ListQuotas",
			Handler:    _QuotaService_ListQuotas_Handler,
		},
	},
	Streams:  []grpc.StreamDesc{},
	Metadata: "network/quota/v1/quota.proto",
}
