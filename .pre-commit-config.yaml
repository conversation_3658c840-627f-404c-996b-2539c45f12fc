default_language_version:
  python: python3
default_install_hook_types:
  - pre-commit
  - pre-merge-commit
  - pre-push
  - prepare-commit-msg
  - commit-msg
  - post-commit
  - post-checkout
  - post-merge
  - post-rewrite
repos:
  - hooks:
      - id: check-json
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-merge-conflict
      - id: check-yaml
        args:
          - --allow-multiple-documents
      - id: mixed-line-ending
        args: ["--fix=lf"]
        description: Forces to replace line ending by the UNIX 'lf' character.

    repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.2.0
  - hooks:
      - id: go-build
      - id: go-unit-tests
    repo: https://github.com/dnephin/pre-commit-golang
    rev: v0.5.0
  - hooks:
      - id: golangci-lint
    repo: https://github.com/golangci/golangci-lint
    rev: v1.45.0
  - hooks:
      - id: commitizen
        stages:
          - commit-msg
    repo: https://github.com/commitizen-tools/commitizen
    rev: v2.24.0
