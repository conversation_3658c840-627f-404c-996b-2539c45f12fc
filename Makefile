.PHONY: generate_go generate_openapi default build push clean pb_tool_img docker_pb toolbox-image-base

PROTO=vpc

# gen xxx.proto to xxx.go
generate_go:
	$(info ******************** generate_go ********************)
	protoc -I ./pb \
	-I ./pb/third_party \
	--go_out=. --go_opt=module=gitlab.bj.sensetime.com/elementary/boson/boson-provider \
	--go-grpc_out=. --go-grpc_opt=module=gitlab.bj.sensetime.com/elementary/boson/boson-provider \
	--grpc-gateway_out=. --grpc-gateway_opt=module=gitlab.bj.sensetime.com/elementary/boson/boson-provider \
	--validate_out=. --validate_opt=module=gitlab.bj.sensetime.com/elementary/boson/boson-provider --validate_opt=lang=go \
	$(shell find ./pb -iname "*.proto" -not -path "./pb/third_party*")

# gen xxx.proto to openapi
generate_openapi:
	$(info ******************** generate_openapi ********************)
	for i in $$(find ./pb -iname "*.proto" -not -path "./pb/third_party*"); do \
		filename=$$(basename $$i .proto); \
		dirname=$$(dirname $$i); \
		protoc -I ./pb -I ./pb/third_party --openapi_out=$$dirname --openapi_opt=naming=proto $$i; \
		mv $$dirname/openapi.yaml $$dirname/$$filename.openapi.yaml; \
		cp $$dirname/$$filename.openapi.yaml docs/apis/; \
	done

# 使用 make docker_pb，不要直接 make pb，确保生成代码的一致
pb: generate_go generate_openapi

fmt:
	go fmt ./...

lint:
	git config -l
	go fmt ./... && go vet ./... && golangci-lint run pkg/...

lint-docker:
	docker run --rm -v $(PWD):/go/src/gitlab.bj.sensetime.com/elementary/boson/boson-provider registry.sensetime.com/sensecore-boson/golangci-lint:v1.48 sh -c 'cd /go/src/gitlab.bj.sensetime.com/elementary/boson/boson-provider && make lint'

#
# makefile variables
#

BUILD_ENV=-dev
PWD=$(shell pwd -P)
PRJNAME := "boson-provider"
Img := registry.sensetime.com/sensecore-boson/boson-provider
PKG := gitlab.bj.sensetime.com/elementary/boson/boson-provider
GOIMG := "registry.sensetime.com/diamond-dev/golang:1.18"
DESCRIBE := $(shell git describe --tags)
DATE := $(shell date +"%Y%m%d%H%M%S")
TAG := $(DESCRIBE)-$(DATE)
DOCKER ?= docker

# populate version variables within the binary
#VERSION=$(shell cat VERSION.txt)

BUILD_TIME=$(shell date -u +%Y-%m-%d_%H:%M:%S_%Z)
gitCommit=$(shell git rev-parse --short HEAD)
gitDirty=$(shell git status --porcelain --untracked-files=no)
GIT_COMMIT=$(gitCommit)
ifneq ($(gitDirty),)
        GIT_COMMIT=$(gitCommit)-dirty
endif

IMAGE := ${Img}:${TAG}
IMAGE-dev := ${Img}:dev-${TAG}

BUILD_FLAGS=-X $(PKG)/version.buildAt=$(BUILD_TIME) -w -s

OS=$(shell uname -s)
#GOOS=linux
ifeq ($(GOOS),)
        GOOS="linux"
endif
ifeq ($(ARCH),)
        ARCH="amd64"
endif
GOCACHE=$(shell go env GOCACHE)
ifeq ($(GOCACHE),)
        GOCACHE="/tmp/.gocache/go-build"
endif



#
# makefile targets
#

build:
	CGO_ENABLED=0 GOOS=${GOOS} GOARCH=${ARCH} go build -ldflags "${BUILD_FLAGS}" -o build/bin/boson-provider ${PWD}/cmd/${PRJNAME}/main.go

default: binary    ## See `binary`

nolint: binary-nolint ## See `binary-nolint`

#all: integration-test push  ## Build product image -> Run integration test cases locallly -> Push to remote registry

prepare:  ## Prepare necessary directories and ensure local docker get ready
	mkdir -p $(GOCACHE)

golint: golangci-lint ## See `golangci-lint`

golangci-lint:  ## Perform golang code statics linters through golangci-lint
	time $(DOCKER) run --name gocheck-ucp-server --rm \
                -e GOCACHE=/go/cache \
                -v $(PWD):/go/src/${PKG}:delegated \
                -v $(HOME)/.netrc:/root/.netrc \
                -v $(GOCACHE):/go/cache/:cached \
                registry.sensetime.com/diamond-dev/golangci-lint:v1.33.0 \
                golangci-lint run --concurrency=4 --timeout=10m \
                /go/src/${PKG}/...
	echo "  --- Golangci-lint Passed!"

binary: prepare golint binary-nolint  ## Build binary with code statics linters

binary-nolint:          ## Build binary without code statics linters
	@make $(DOCKER)-binary-build

$(DOCKER)-binary-build: clean  ## Build binary through a local docker container
	time $(DOCKER) run --rm \
                --name buildresourceprovider \
                -w /go/src/${PKG} \
                -e GOARCH=amd64 \
                -e CGO_ENABLED=0 \
                -e GOOS=${GOOS} \
                -e GOCACHE=/go/cache \
                -e GONOSUMDB=gitlab.bj.sensetime.com,* \
                -e GONOPROXY=gitlab.bj.sensetime.com \
		-e GOPROXY=https://goproxy.cn,https://goproxy.io,direct \
                -v $(PWD):/go/src/${PKG}:delegated \
                -v $(HOME)/.netrc:/root/.netrc \
                -v $(GOCACHE):/go/cache/:cached \
		-v $(HOME)/.netrc:/root/.netrc \
                $(GOIMG) \
		sh -c 'pwd && ls && go build -ldflags "${BUILD_FLAGS}" -o build/bin/boson-provider cmd/${PRJNAME}/main.go'

build-image: $(DOCKER)-binary-build ## Build docker image for test
	echo Running: $(DOCKER) build ${PRJNAME}
	$(DOCKER) build \
	       --no-cache \
	       --build-arg VERSION=${GIT_COMMIT} \
	       --build-arg ARCH=${ARCH} \
	       --build-arg IMAGE_REPO=${Img} \
	       --build-arg no_proxy=.sensetime.com,localhost \
	       -t ${IMAGE} ./build
	${DOCKER} push ${IMAGE}

build-image-dev: $(DOCKER)-binary-build ## Build docker image for test
	echo Running: $(DOCKER) build ${PRJNAME}
	$(DOCKER) build \
               --no-cache \
               --build-arg VERSION=${GIT_COMMIT} \
               --build-arg ARCH=${ARCH} \
               --build-arg IMAGE_REPO=${Img} \
               --build-arg no_proxy=.sensetime.com,localhost \
               -t ${IMAGE-dev} ./build
	${DOCKER} push ${IMAGE-dev}

push:
	echo Running: pushing ${IMAGE}
	$(DOCKER) push ${IMAGE}

image: build-image push

tool-build: clean
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/consume local-test/consume/main.go \
		    && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/produce local-test/produce/main.go \
		    && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/createTopics local-test/createTopics/main.go \
		    && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/initGwIPs local-test/initGwIPs/main.go \
		    && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/syncTables local-test/syncTables/main.go \
		    && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/truncTables local-test/truncTables/main.go \
		    && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/initPools local-test/initPools/main.go \
		    && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/initTainingRoce local-test/initTainingRoce/main.go \
		    && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/initSnat local-test/initSnat/main.go \
		    && CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/initDGW local-test/initDGW/main.go \
			&& CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/initAcl local-test/initAcl/main.go \
			&& CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/updateEipBw local-test/updateEipBw/main.go \
			&& CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/initBms2Pod local-test/initBms2Pod/main.go \
			&& CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/migrateAcl local-test/migrateAcl/main.go


DGW:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/initDGW local-test/initDGW/main.go

initAcl:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/initAcl local-test/initAcl/main.go

updateEipBw:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/updateEipBw local-test/updateEipBw/main.go

initBms2Pod:
	CGO_ENABLED=0 GOOS=linux GOARCH=amd64 go build -o build/bin/initBms2Pod local-test/initBms2Pod/main.go

toolbox-image: tool-build bugfix
	cp -r local-test/produce/sample_messages ./build/bin/ \
		&& cp docs/toolbox/README.md ./build/bin/ \
		&& cd build \
		&& $(DOCKER) build . -f Dockerfile.toolbox -t registry.sensetime.com/sensecore-boson/boson-toolbox:${TAG} \
		&& $(DOCKER) push registry.sensetime.com/sensecore-boson/boson-toolbox:${TAG}

toolbox-image-dev: tool-build bugfix
	cp -r local-test/produce/sample_messages ./build/bin/ \
		&& cp docs/toolbox/README.md ./build/bin/ \
		&& cd build \
		&& $(DOCKER) build . -f Dockerfile.toolbox -t registry.sensetime.com/sensecore-boson/boson-toolbox:${TAG}-dev \
		&& $(DOCKER) push registry.sensetime.com/sensecore-boson/boson-toolbox:${TAG}-dev

toolbox-image-base:
	cd build \
		&& $(DOCKER) build . -f Dockerfile.toolbox.base -t registry.sensetime.com/sensecore-boson/boson-toolbox:${TAG} \
		&& $(DOCKER) push registry.sensetime.com/sensecore-boson/boson-toolbox:${TAG}

bugfix:
	mkdir -p ./build/bin/bugfix
	chmod +x local-test/bugfix/*
	cp -r local-test/bugfix/* ./build/bin/bugfix/

clean:
	rm -rf ./build/bin/*

pre-commit:
	pre-commit run --all-files

pb_tool_img:
	docker build ./pb/tools/ -f pb/tools/Dockerfile -t registry.sensetime.com/sensecore-boson/pb-tools:latest
	docker push registry.sensetime.com/sensecore-boson/pb-tools:latest

docker_pb:
	docker run --rm -v `pwd`:/src  registry.sensetime.com/sensecore-higgs/higgs-elementary-apis-builder make pb
