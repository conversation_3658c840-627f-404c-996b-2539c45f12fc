# Boson Provider

接收 RM 的资源操作请求，并调用不同服务网络完成操作内容

## 开发指引

- cmd 为程序入口
- api 由 pb 下的 proto 文件生成，勿手动修改
- pb 为 proto 接口定义
- pkg/config 对应 boson-provider.yaml 的配置
- pkg/db 为数据库表对应的 go struct
- pkg/informer watch 相关 k8s 资源
- pkg/parser 消息自动格式化
- pkg/receiver 消息消费者
- pkg/resourceprovider 接收消息并进行处理
- pkg/sender 消息生产者
- pkg/types 消息结构体

## 代码校验及编译

```
# 格式化并校验
make lint

# 编译
make build
```

编译的二进制在 build/bin 下

## 服务运行前提

- bin 同级目录配置 boson-provider.yaml
- 提前创建好 mq 及对应的 topic
- 提前创建好 pg 及对应的 DB 及 table
- 提前创建好 Diamond 基于 kube-ovn 的 k8s 集群

# TODO
1. 数据库升级可以考虑做成版本化、自动化，可以参考 https://flywaydb.org/