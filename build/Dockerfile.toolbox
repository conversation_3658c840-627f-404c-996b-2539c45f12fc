FROM registry.sensetime.com/sensecore-boson/boson-toolbox:v1.13.0-20-gdd7fc07-20240604191203

WORKDIR /boson-toolbox

RUN apt-get update && \
    apt install -y linux-libc-dev libnghttp2-14

RUN mkdir -p /boson-toolbox/crds

COPY ./bin/sample_messages /boson-toolbox/sample_messages
COPY ./boson-provider.yaml.sample /boson-toolbox/boson-provider.yaml
COPY ./bin/README.md /boson-toolbox/README.md
COPY ./bin/consume /boson-toolbox/consume
COPY ./bin/createTopics /boson-toolbox/createTopics
COPY ./bin/initGwIPs /boson-toolbox/initGwIPs
COPY ./bin/produce /boson-toolbox/produce
COPY ./bin/syncTables /boson-toolbox/syncTables
COPY ./bin/truncTables /boson-toolbox/truncTables
COPY ./bin/initPools /boson-toolbox/initPools
COPY ./bin/initTainingRoce /boson-toolbox/initTainingRoce
COPY ./bin/initSnat /boson-toolbox/initSnat
COPY ./bin/bugfix/* /boson-toolbox/
COPY ./bin/migrateAcl /boson-toolbox/migrateAcl
