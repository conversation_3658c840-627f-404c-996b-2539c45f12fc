FROM registry.sensetime.com/sensecore-boson/toolkits/ubuntu-base:20.04-g5dc1e1d-20231017

COPY deps /root/deps

RUN sudo apt-get update && \
    sudo install -o root -g root -m 0755 /root/deps/kubectl_1.25 /usr/local/bin/kubectl && \
    sudo install -o root -g root -m 0755 /root/deps/grpcurl_1.8.7_linux_x86_64 /usr/local/bin/grpcurl && \
    apt --no-install-recommends install -y postgresql-client=12+214ubuntu0.1 python3-psycopg2=2.8.4-2 && \
    pip3 install prometheus-client==0.18.0 pyyaml==6.0.1 rocketmq==0.4.4 sqlparse==0.4.4 /root/deps/pyvesta-*.tar.gz -i https://mirrors.aliyun.com/pypi/simple/ && \
    rm -rf /root/deps && \
    apt clean && \
    rm -rf /var/lib/apt/lists/*
